// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/api/user.go

// Package api is a generated GoMock package.
package api

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockUserRepository is a mock of UserRepository interface.
type MockUserRepository struct {
	ctrl     *gomock.Controller
	recorder *MockUserRepositoryMockRecorder
}

// MockUserRepositoryMockRecorder is the mock recorder for MockUserRepository.
type MockUserRepositoryMockRecorder struct {
	mock *MockUserRepository
}

// NewMockUserRepository creates a new mock instance.
func NewMockUserRepository(ctrl *gomock.Controller) *MockUserRepository {
	mock := &MockUserRepository{ctrl: ctrl}
	mock.recorder = &MockUserRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserRepository) EXPECT() *MockUserRepositoryMockRecorder {
	return m.recorder
}

// LoginHandler mocks base method.
func (m *MockUserRepository) LoginHandler(c *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "LoginHandler", c)
}

// LoginHandler indicates an expected call of LoginHandler.
func (mr *MockUserRepositoryMockRecorder) LoginHandler(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoginHandler", reflect.TypeOf((*MockUserRepository)(nil).LoginHandler), c)
}

// RegisterHandler mocks base method.
func (m *MockUserRepository) RegisterHandler(c *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterHandler", c)
}

// RegisterHandler indicates an expected call of RegisterHandler.
func (mr *MockUserRepositoryMockRecorder) RegisterHandler(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterHandler", reflect.TypeOf((*MockUserRepository)(nil).RegisterHandler), c)
}
