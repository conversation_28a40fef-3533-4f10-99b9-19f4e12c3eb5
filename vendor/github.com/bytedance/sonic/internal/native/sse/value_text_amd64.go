// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_value = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, // QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000010 LCPI0_1
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000010 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000020 LCPI0_2
	0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, //0x00000020 QUAD $0x1f1f1f1f1f1f1f1f; QUAD $0x1f1f1f1f1f1f1f1f  // .space 16, '\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f'
	//0x00000030 LCPI0_3
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000030 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000040 LCPI0_4
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000040 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000050 LCPI0_5
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x00000050 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x00000060 LCPI0_6
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x00000060 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x00000070 LCPI0_7
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000070 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000080 LCPI0_8
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000080 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000090 LCPI0_9
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000090 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x000000a0 LCPI0_10
	0x00, 0x00, 0x30, 0x43, //0x000000a0 .long 1127219200
	0x00, 0x00, 0x30, 0x45, //0x000000a4 .long 1160773632
	0x00, 0x00, 0x00, 0x00, //0x000000a8 .long 0
	0x00, 0x00, 0x00, 0x00, //0x000000ac .long 0
	//0x000000b0 LCPI0_11
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x43, //0x000000b0 .quad 0x4330000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x45, //0x000000b8 .quad 0x4530000000000000
	//0x000000c0 .p2align 3, 0x00
	//0x000000c0 LCPI0_12
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x000000c0 .quad 0x430c6bf526340000
	//0x000000c8 LCPI0_13
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0xc3, //0x000000c8 .quad 0xc30c6bf526340000
	//0x000000d0 .p2align 4, 0x90
	//0x000000d0 _value
	0x55, //0x000000d0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000000d1 movq         %rsp, %rbp
	0x41, 0x57, //0x000000d4 pushq        %r15
	0x41, 0x56, //0x000000d6 pushq        %r14
	0x41, 0x55, //0x000000d8 pushq        %r13
	0x41, 0x54, //0x000000da pushq        %r12
	0x53, //0x000000dc pushq        %rbx
	0x48, 0x83, 0xec, 0x50, //0x000000dd subq         $80, %rsp
	0x49, 0x89, 0xcd, //0x000000e1 movq         %rcx, %r13
	0x49, 0x89, 0xd4, //0x000000e4 movq         %rdx, %r12
	0x49, 0x89, 0xf7, //0x000000e7 movq         %rsi, %r15
	0x48, 0x39, 0xf2, //0x000000ea cmpq         %rsi, %rdx
	0x0f, 0x83, 0x28, 0x00, 0x00, 0x00, //0x000000ed jae          LBB0_5
	0x42, 0x8a, 0x04, 0x27, //0x000000f3 movb         (%rdi,%r12), %al
	0x3c, 0x0d, //0x000000f7 cmpb         $13, %al
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x000000f9 je           LBB0_5
	0x3c, 0x20, //0x000000ff cmpb         $32, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00000101 je           LBB0_5
	0x8d, 0x48, 0xf5, //0x00000107 leal         $-11(%rax), %ecx
	0x80, 0xf9, 0xfe, //0x0000010a cmpb         $-2, %cl
	0x0f, 0x83, 0x08, 0x00, 0x00, 0x00, //0x0000010d jae          LBB0_5
	0x4d, 0x89, 0xe1, //0x00000113 movq         %r12, %r9
	0xe9, 0x00, 0x01, 0x00, 0x00, //0x00000116 jmp          LBB0_28
	//0x0000011b LBB0_5
	0x4d, 0x8d, 0x4c, 0x24, 0x01, //0x0000011b leaq         $1(%r12), %r9
	0x4d, 0x39, 0xf9, //0x00000120 cmpq         %r15, %r9
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x00000123 jae          LBB0_9
	0x42, 0x8a, 0x04, 0x0f, //0x00000129 movb         (%rdi,%r9), %al
	0x3c, 0x0d, //0x0000012d cmpb         $13, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000012f je           LBB0_9
	0x3c, 0x20, //0x00000135 cmpb         $32, %al
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00000137 je           LBB0_9
	0x8d, 0x48, 0xf5, //0x0000013d leal         $-11(%rax), %ecx
	0x80, 0xf9, 0xfe, //0x00000140 cmpb         $-2, %cl
	0x0f, 0x82, 0xd2, 0x00, 0x00, 0x00, //0x00000143 jb           LBB0_28
	//0x00000149 LBB0_9
	0x4d, 0x8d, 0x4c, 0x24, 0x02, //0x00000149 leaq         $2(%r12), %r9
	0x4d, 0x39, 0xf9, //0x0000014e cmpq         %r15, %r9
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x00000151 jae          LBB0_13
	0x42, 0x8a, 0x04, 0x0f, //0x00000157 movb         (%rdi,%r9), %al
	0x3c, 0x0d, //0x0000015b cmpb         $13, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000015d je           LBB0_13
	0x3c, 0x20, //0x00000163 cmpb         $32, %al
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00000165 je           LBB0_13
	0x8d, 0x48, 0xf5, //0x0000016b leal         $-11(%rax), %ecx
	0x80, 0xf9, 0xfe, //0x0000016e cmpb         $-2, %cl
	0x0f, 0x82, 0xa4, 0x00, 0x00, 0x00, //0x00000171 jb           LBB0_28
	//0x00000177 LBB0_13
	0x4d, 0x8d, 0x4c, 0x24, 0x03, //0x00000177 leaq         $3(%r12), %r9
	0x4d, 0x39, 0xf9, //0x0000017c cmpq         %r15, %r9
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x0000017f jae          LBB0_17
	0x42, 0x8a, 0x04, 0x0f, //0x00000185 movb         (%rdi,%r9), %al
	0x3c, 0x0d, //0x00000189 cmpb         $13, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000018b je           LBB0_17
	0x3c, 0x20, //0x00000191 cmpb         $32, %al
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00000193 je           LBB0_17
	0x8d, 0x48, 0xf5, //0x00000199 leal         $-11(%rax), %ecx
	0x80, 0xf9, 0xfe, //0x0000019c cmpb         $-2, %cl
	0x0f, 0x82, 0x76, 0x00, 0x00, 0x00, //0x0000019f jb           LBB0_28
	//0x000001a5 LBB0_17
	0x4d, 0x8d, 0x4c, 0x24, 0x04, //0x000001a5 leaq         $4(%r12), %r9
	0x4d, 0x39, 0xf9, //0x000001aa cmpq         %r15, %r9
	0x0f, 0x83, 0x47, 0x00, 0x00, 0x00, //0x000001ad jae          LBB0_23
	0x0f, 0x84, 0x49, 0x00, 0x00, 0x00, //0x000001b3 je           LBB0_24
	0x4a, 0x8d, 0x04, 0x3f, //0x000001b9 leaq         (%rdi,%r15), %rax
	0x48, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000001bd movabsq      $4294977024, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000001c7 .p2align 4, 0x90
	//0x000001d0 LBB0_20
	0x42, 0x0f, 0xbe, 0x34, 0x0f, //0x000001d0 movsbl       (%rdi,%r9), %esi
	0x83, 0xfe, 0x20, //0x000001d5 cmpl         $32, %esi
	0x0f, 0x87, 0x30, 0x00, 0x00, 0x00, //0x000001d8 ja           LBB0_26
	0x48, 0x0f, 0xa3, 0xf1, //0x000001de btq          %rsi, %rcx
	0x0f, 0x83, 0x26, 0x00, 0x00, 0x00, //0x000001e2 jae          LBB0_26
	0x49, 0x83, 0xc1, 0x01, //0x000001e8 addq         $1, %r9
	0x4d, 0x39, 0xcf, //0x000001ec cmpq         %r9, %r15
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x000001ef jne          LBB0_20
	0xe9, 0x0e, 0x00, 0x00, 0x00, //0x000001f5 jmp          LBB0_25
	//0x000001fa LBB0_23
	0x4d, 0x89, 0xcc, //0x000001fa movq         %r9, %r12
	0xe9, 0xb8, 0x00, 0x00, 0x00, //0x000001fd jmp          LBB0_37
	//0x00000202 LBB0_24
	0x49, 0x01, 0xf9, //0x00000202 addq         %rdi, %r9
	0x4c, 0x89, 0xc8, //0x00000205 movq         %r9, %rax
	//0x00000208 LBB0_25
	0x48, 0x29, 0xf8, //0x00000208 subq         %rdi, %rax
	0x49, 0x89, 0xc1, //0x0000020b movq         %rax, %r9
	//0x0000020e LBB0_26
	0x4d, 0x39, 0xf9, //0x0000020e cmpq         %r15, %r9
	0x0f, 0x83, 0xa3, 0x00, 0x00, 0x00, //0x00000211 jae          LBB0_37
	0x42, 0x8a, 0x04, 0x0f, //0x00000217 movb         (%rdi,%r9), %al
	//0x0000021b LBB0_28
	0x0f, 0xbe, 0xc8, //0x0000021b movsbl       %al, %ecx
	0x83, 0xf9, 0x7d, //0x0000021e cmpl         $125, %ecx
	0x0f, 0x87, 0x04, 0x05, 0x00, 0x00, //0x00000221 ja           LBB0_107
	0x4d, 0x8d, 0x61, 0x01, //0x00000227 leaq         $1(%r9), %r12
	0x4e, 0x8d, 0x14, 0x0f, //0x0000022b leaq         (%rdi,%r9), %r10
	0x48, 0x8d, 0x35, 0x36, 0x2e, 0x00, 0x00, //0x0000022f leaq         $11830(%rip), %rsi  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8e, //0x00000236 movslq       (%rsi,%rcx,4), %rcx
	0x48, 0x01, 0xf1, //0x0000023a addq         %rsi, %rcx
	0xff, 0xe1, //0x0000023d jmpq         *%rcx
	//0x0000023f LBB0_30
	0x41, 0xf6, 0xc0, 0x02, //0x0000023f testb        $2, %r8b
	0x0f, 0x85, 0x8b, 0x00, 0x00, 0x00, //0x00000243 jne          LBB0_39
	0x4d, 0x8b, 0x75, 0x20, //0x00000249 movq         $32(%r13), %r14
	0x49, 0x8b, 0x55, 0x28, //0x0000024d movq         $40(%r13), %rdx
	0x49, 0xc7, 0x45, 0x00, 0x09, 0x00, 0x00, 0x00, //0x00000251 movq         $9, (%r13)
	0x66, 0x0f, 0xef, 0xc0, //0x00000259 pxor         %xmm0, %xmm0
	0xf3, 0x41, 0x0f, 0x7f, 0x45, 0x08, //0x0000025d movdqu       %xmm0, $8(%r13)
	0x4d, 0x89, 0x4d, 0x18, //0x00000263 movq         %r9, $24(%r13)
	0x4d, 0x39, 0xf9, //0x00000267 cmpq         %r15, %r9
	0x0f, 0x83, 0xbe, 0x0c, 0x00, 0x00, //0x0000026a jae          LBB0_215
	0x41, 0x8a, 0x02, //0x00000270 movb         (%r10), %al
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000273 movl         $1, %r8d
	0x89, 0xc3, //0x00000279 movl         %eax, %ebx
	0x4c, 0x89, 0xc9, //0x0000027b movq         %r9, %rcx
	0x3c, 0x2d, //0x0000027e cmpb         $45, %al
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x00000280 jne          LBB0_35
	0x4d, 0x39, 0xfc, //0x00000286 cmpq         %r15, %r12
	0x0f, 0x83, 0x9f, 0x0c, 0x00, 0x00, //0x00000289 jae          LBB0_215
	0x42, 0x8a, 0x1c, 0x27, //0x0000028f movb         (%rdi,%r12), %bl
	0x41, 0xb8, 0xff, 0xff, 0xff, 0xff, //0x00000293 movl         $-1, %r8d
	0x4c, 0x89, 0xe1, //0x00000299 movq         %r12, %rcx
	//0x0000029c LBB0_35
	0x88, 0x45, 0xc8, //0x0000029c movb         %al, $-56(%rbp)
	0x8d, 0x43, 0xc6, //0x0000029f leal         $-58(%rbx), %eax
	0x3c, 0xf5, //0x000002a2 cmpb         $-11, %al
	0x0f, 0x87, 0x44, 0x03, 0x00, 0x00, //0x000002a4 ja           LBB0_87
	//0x000002aa LBB0_36
	0x49, 0xc7, 0x45, 0x00, 0xfe, 0xff, 0xff, 0xff, //0x000002aa movq         $-2, (%r13)
	0x49, 0x89, 0xcc, //0x000002b2 movq         %rcx, %r12
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x000002b5 jmp          LBB0_38
	//0x000002ba LBB0_37
	0x49, 0xc7, 0x45, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000002ba movq         $1, (%r13)
	//0x000002c2 LBB0_38
	0x4c, 0x89, 0xe0, //0x000002c2 movq         %r12, %rax
	0x48, 0x83, 0xc4, 0x50, //0x000002c5 addq         $80, %rsp
	0x5b, //0x000002c9 popq         %rbx
	0x41, 0x5c, //0x000002ca popq         %r12
	0x41, 0x5d, //0x000002cc popq         %r13
	0x41, 0x5e, //0x000002ce popq         %r14
	0x41, 0x5f, //0x000002d0 popq         %r15
	0x5d, //0x000002d2 popq         %rbp
	0xc3, //0x000002d3 retq         
	//0x000002d4 LBB0_39
	0x4d, 0x29, 0xcf, //0x000002d4 subq         %r9, %r15
	0x31, 0xf6, //0x000002d7 xorl         %esi, %esi
	0x3c, 0x2d, //0x000002d9 cmpb         $45, %al
	0x40, 0x0f, 0x94, 0xc6, //0x000002db sete         %sil
	0x4d, 0x8d, 0x24, 0x32, //0x000002df leaq         (%r10,%rsi), %r12
	0x49, 0x29, 0xf7, //0x000002e3 subq         %rsi, %r15
	0x0f, 0x84, 0x5f, 0x24, 0x00, 0x00, //0x000002e6 je           LBB0_569
	0x4c, 0x89, 0x6d, 0xc0, //0x000002ec movq         %r13, $-64(%rbp)
	0x41, 0x8a, 0x04, 0x24, //0x000002f0 movb         (%r12), %al
	0x8d, 0x48, 0xc6, //0x000002f4 leal         $-58(%rax), %ecx
	0x80, 0xf9, 0xf6, //0x000002f7 cmpb         $-10, %cl
	0x0f, 0x82, 0xfc, 0x06, 0x00, 0x00, //0x000002fa jb           LBB0_139
	0x3c, 0x30, //0x00000300 cmpb         $48, %al
	0x0f, 0x85, 0x35, 0x00, 0x00, 0x00, //0x00000302 jne          LBB0_45
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00000308 movl         $1, %edx
	0x49, 0x83, 0xff, 0x01, //0x0000030d cmpq         $1, %r15
	0x0f, 0x84, 0xa1, 0x06, 0x00, 0x00, //0x00000311 je           LBB0_135
	0x41, 0x8a, 0x44, 0x24, 0x01, //0x00000317 movb         $1(%r12), %al
	0x04, 0xd2, //0x0000031c addb         $-46, %al
	0x3c, 0x37, //0x0000031e cmpb         $55, %al
	0x0f, 0x87, 0x92, 0x06, 0x00, 0x00, //0x00000320 ja           LBB0_135
	0x0f, 0xb6, 0xc0, //0x00000326 movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000329 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00000333 btq          %rax, %rcx
	0x0f, 0x83, 0x7b, 0x06, 0x00, 0x00, //0x00000337 jae          LBB0_135
	//0x0000033d LBB0_45
	0x4c, 0x89, 0x55, 0xd0, //0x0000033d movq         %r10, $-48(%rbp)
	0x48, 0x89, 0x7d, 0xb8, //0x00000341 movq         %rdi, $-72(%rbp)
	0x49, 0x83, 0xff, 0x10, //0x00000345 cmpq         $16, %r15
	0x48, 0x89, 0x75, 0xb0, //0x00000349 movq         %rsi, $-80(%rbp)
	0x0f, 0x82, 0xbe, 0x24, 0x00, 0x00, //0x0000034d jb           LBB0_572
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000353 movq         $-1, %r11
	0x31, 0xd2, //0x0000035a xorl         %edx, %edx
	0xf3, 0x44, 0x0f, 0x6f, 0x05, 0xcb, 0xfc, 0xff, 0xff, //0x0000035c movdqu       $-821(%rip), %xmm8  /* LCPI0_3+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x0d, 0xd2, 0xfc, 0xff, 0xff, //0x00000365 movdqu       $-814(%rip), %xmm9  /* LCPI0_4+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xd9, 0xfc, 0xff, 0xff, //0x0000036e movdqu       $-807(%rip), %xmm10  /* LCPI0_5+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x1d, 0xe1, 0xfc, 0xff, 0xff, //0x00000377 movdqu       $-799(%rip), %xmm3  /* LCPI0_6+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0xe9, 0xfc, 0xff, 0xff, //0x0000037f movdqu       $-791(%rip), %xmm4  /* LCPI0_7+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x2d, 0xf1, 0xfc, 0xff, 0xff, //0x00000387 movdqu       $-783(%rip), %xmm5  /* LCPI0_8+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x35, 0xf9, 0xfc, 0xff, 0xff, //0x0000038f movdqu       $-775(%rip), %xmm6  /* LCPI0_9+0(%rip) */
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00000397 movq         $-1, %r13
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000039e movq         $-1, %r10
	0x4c, 0x89, 0xfb, //0x000003a5 movq         %r15, %rbx
	0x4c, 0x89, 0xe7, //0x000003a8 movq         %r12, %rdi
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000003ab .p2align 4, 0x90
	//0x000003b0 LBB0_47
	0xf3, 0x41, 0x0f, 0x6f, 0x3c, 0x14, //0x000003b0 movdqu       (%r12,%rdx), %xmm7
	0x66, 0x0f, 0x6f, 0xc7, //0x000003b6 movdqa       %xmm7, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x000003ba pcmpeqb      %xmm8, %xmm0
	0x66, 0x0f, 0x6f, 0xcf, //0x000003bf movdqa       %xmm7, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xc9, //0x000003c3 pcmpeqb      %xmm9, %xmm1
	0x66, 0x0f, 0xeb, 0xc8, //0x000003c8 por          %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xc7, //0x000003cc movdqa       %xmm7, %xmm0
	0x66, 0x41, 0x0f, 0xfc, 0xc2, //0x000003d0 paddb        %xmm10, %xmm0
	0x66, 0x0f, 0x6f, 0xd0, //0x000003d5 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0xda, 0xd3, //0x000003d9 pminub       %xmm3, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x000003dd pcmpeqb      %xmm0, %xmm2
	0x66, 0x0f, 0x6f, 0xc7, //0x000003e1 movdqa       %xmm7, %xmm0
	0x66, 0x0f, 0xdb, 0xc4, //0x000003e5 pand         %xmm4, %xmm0
	0x66, 0x0f, 0x74, 0xc6, //0x000003e9 pcmpeqb      %xmm6, %xmm0
	0x66, 0x0f, 0x74, 0xfd, //0x000003ed pcmpeqb      %xmm5, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xf0, //0x000003f1 pmovmskb     %xmm0, %r14d
	0x66, 0x0f, 0xeb, 0xc7, //0x000003f6 por          %xmm7, %xmm0
	0x66, 0x0f, 0xeb, 0xc1, //0x000003fa por          %xmm1, %xmm0
	0x66, 0x0f, 0xeb, 0xc2, //0x000003fe por          %xmm2, %xmm0
	0x66, 0x0f, 0xd7, 0xc7, //0x00000402 pmovmskb     %xmm7, %eax
	0x66, 0x44, 0x0f, 0xd7, 0xc1, //0x00000406 pmovmskb     %xmm1, %r8d
	0x66, 0x0f, 0xd7, 0xc8, //0x0000040b pmovmskb     %xmm0, %ecx
	0xf7, 0xd1, //0x0000040f notl         %ecx
	0x0f, 0xbc, 0xc9, //0x00000411 bsfl         %ecx, %ecx
	0x83, 0xf9, 0x10, //0x00000414 cmpl         $16, %ecx
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00000417 je           LBB0_49
	0xbe, 0xff, 0xff, 0xff, 0xff, //0x0000041d movl         $-1, %esi
	0xd3, 0xe6, //0x00000422 shll         %cl, %esi
	0xf7, 0xd6, //0x00000424 notl         %esi
	0x21, 0xf0, //0x00000426 andl         %esi, %eax
	0x41, 0x21, 0xf6, //0x00000428 andl         %esi, %r14d
	0x44, 0x21, 0xc6, //0x0000042b andl         %r8d, %esi
	0x41, 0x89, 0xf0, //0x0000042e movl         %esi, %r8d
	//0x00000431 LBB0_49
	0x44, 0x8d, 0x60, 0xff, //0x00000431 leal         $-1(%rax), %r12d
	0x41, 0x21, 0xc4, //0x00000435 andl         %eax, %r12d
	0x0f, 0x85, 0x91, 0x0d, 0x00, 0x00, //0x00000438 jne          LBB0_248
	0x41, 0x8d, 0x76, 0xff, //0x0000043e leal         $-1(%r14), %esi
	0x44, 0x21, 0xf6, //0x00000442 andl         %r14d, %esi
	0x0f, 0x85, 0x8d, 0x0d, 0x00, 0x00, //0x00000445 jne          LBB0_249
	0x41, 0x8d, 0x70, 0xff, //0x0000044b leal         $-1(%r8), %esi
	0x44, 0x21, 0xc6, //0x0000044f andl         %r8d, %esi
	0x49, 0x89, 0xfc, //0x00000452 movq         %rdi, %r12
	0x0f, 0x85, 0x92, 0x0d, 0x00, 0x00, //0x00000455 jne          LBB0_251
	0x85, 0xc0, //0x0000045b testl        %eax, %eax
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x0000045d je           LBB0_55
	0x0f, 0xbc, 0xc0, //0x00000463 bsfl         %eax, %eax
	0x49, 0x83, 0xfa, 0xff, //0x00000466 cmpq         $-1, %r10
	0x0f, 0x85, 0x0e, 0x0d, 0x00, 0x00, //0x0000046a jne          LBB0_245
	0x48, 0x01, 0xd0, //0x00000470 addq         %rdx, %rax
	0x49, 0x89, 0xc2, //0x00000473 movq         %rax, %r10
	//0x00000476 LBB0_55
	0x45, 0x85, 0xf6, //0x00000476 testl        %r14d, %r14d
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00000479 je           LBB0_58
	0x41, 0x0f, 0xbc, 0xc6, //0x0000047f bsfl         %r14d, %eax
	0x49, 0x83, 0xfd, 0xff, //0x00000483 cmpq         $-1, %r13
	0x0f, 0x85, 0xf1, 0x0c, 0x00, 0x00, //0x00000487 jne          LBB0_245
	0x48, 0x01, 0xd0, //0x0000048d addq         %rdx, %rax
	0x49, 0x89, 0xc5, //0x00000490 movq         %rax, %r13
	//0x00000493 LBB0_58
	0x45, 0x85, 0xc0, //0x00000493 testl        %r8d, %r8d
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00000496 je           LBB0_61
	0x41, 0x0f, 0xbc, 0xc0, //0x0000049c bsfl         %r8d, %eax
	0x49, 0x83, 0xfb, 0xff, //0x000004a0 cmpq         $-1, %r11
	0x0f, 0x85, 0xd4, 0x0c, 0x00, 0x00, //0x000004a4 jne          LBB0_245
	0x48, 0x01, 0xd0, //0x000004aa addq         %rdx, %rax
	0x49, 0x89, 0xc3, //0x000004ad movq         %rax, %r11
	//0x000004b0 LBB0_61
	0x83, 0xf9, 0x10, //0x000004b0 cmpl         $16, %ecx
	0x0f, 0x85, 0xb8, 0x00, 0x00, 0x00, //0x000004b3 jne          LBB0_76
	0x48, 0x83, 0xc3, 0xf0, //0x000004b9 addq         $-16, %rbx
	0x48, 0x83, 0xc2, 0x10, //0x000004bd addq         $16, %rdx
	0x48, 0x83, 0xfb, 0x0f, //0x000004c1 cmpq         $15, %rbx
	0x0f, 0x87, 0xe5, 0xfe, 0xff, 0xff, //0x000004c5 ja           LBB0_47
	0x49, 0x8d, 0x0c, 0x14, //0x000004cb leaq         (%r12,%rdx), %rcx
	0x49, 0x89, 0xc8, //0x000004cf movq         %rcx, %r8
	0x49, 0x39, 0xd7, //0x000004d2 cmpq         %rdx, %r15
	0x48, 0x8b, 0x7d, 0xb8, //0x000004d5 movq         $-72(%rbp), %rdi
	0x0f, 0x84, 0xaa, 0x00, 0x00, 0x00, //0x000004d9 je           LBB0_78
	//0x000004df LBB0_64
	0x4c, 0x8d, 0x04, 0x19, //0x000004df leaq         (%rcx,%rbx), %r8
	0x49, 0x89, 0xce, //0x000004e3 movq         %rcx, %r14
	0x4d, 0x29, 0xe6, //0x000004e6 subq         %r12, %r14
	0x31, 0xd2, //0x000004e9 xorl         %edx, %edx
	0x4c, 0x8d, 0x3d, 0x72, 0x2d, 0x00, 0x00, //0x000004eb leaq         $11634(%rip), %r15  /* LJTI0_1+0(%rip) */
	0xe9, 0x26, 0x00, 0x00, 0x00, //0x000004f2 jmp          LBB0_68
	//0x000004f7 LBB0_65
	0x49, 0x83, 0xfb, 0xff, //0x000004f7 cmpq         $-1, %r11
	0x0f, 0x85, 0x4d, 0x0a, 0x00, 0x00, //0x000004fb jne          LBB0_217
	0x4d, 0x8d, 0x1c, 0x16, //0x00000501 leaq         (%r14,%rdx), %r11
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000505 .p2align 4, 0x90
	//0x00000510 LBB0_67
	0x48, 0x83, 0xc2, 0x01, //0x00000510 addq         $1, %rdx
	0x48, 0x39, 0xd3, //0x00000514 cmpq         %rdx, %rbx
	0x0f, 0x84, 0x6c, 0x00, 0x00, 0x00, //0x00000517 je           LBB0_78
	//0x0000051d LBB0_68
	0x0f, 0xbe, 0x34, 0x11, //0x0000051d movsbl       (%rcx,%rdx), %esi
	0x8d, 0x46, 0xd0, //0x00000521 leal         $-48(%rsi), %eax
	0x83, 0xf8, 0x0a, //0x00000524 cmpl         $10, %eax
	0x0f, 0x82, 0xe3, 0xff, 0xff, 0xff, //0x00000527 jb           LBB0_67
	0x8d, 0x46, 0xd5, //0x0000052d leal         $-43(%rsi), %eax
	0x83, 0xf8, 0x1a, //0x00000530 cmpl         $26, %eax
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x00000533 ja           LBB0_73
	0x49, 0x63, 0x04, 0x87, //0x00000539 movslq       (%r15,%rax,4), %rax
	0x4c, 0x01, 0xf8, //0x0000053d addq         %r15, %rax
	0xff, 0xe0, //0x00000540 jmpq         *%rax
	//0x00000542 LBB0_71
	0x49, 0x83, 0xfa, 0xff, //0x00000542 cmpq         $-1, %r10
	0x0f, 0x85, 0x02, 0x0a, 0x00, 0x00, //0x00000546 jne          LBB0_217
	0x4d, 0x8d, 0x14, 0x16, //0x0000054c leaq         (%r14,%rdx), %r10
	0xe9, 0xbb, 0xff, 0xff, 0xff, //0x00000550 jmp          LBB0_67
	//0x00000555 LBB0_73
	0x83, 0xfe, 0x65, //0x00000555 cmpl         $101, %esi
	0x0f, 0x85, 0x25, 0x00, 0x00, 0x00, //0x00000558 jne          LBB0_77
	//0x0000055e LBB0_74
	0x49, 0x83, 0xfd, 0xff, //0x0000055e cmpq         $-1, %r13
	0x0f, 0x85, 0xe6, 0x09, 0x00, 0x00, //0x00000562 jne          LBB0_217
	0x4d, 0x8d, 0x2c, 0x16, //0x00000568 leaq         (%r14,%rdx), %r13
	0xe9, 0x9f, 0xff, 0xff, 0xff, //0x0000056c jmp          LBB0_67
	//0x00000571 LBB0_76
	0x41, 0x89, 0xc8, //0x00000571 movl         %ecx, %r8d
	0x4d, 0x01, 0xe0, //0x00000574 addq         %r12, %r8
	0x49, 0x01, 0xd0, //0x00000577 addq         %rdx, %r8
	0x48, 0x8b, 0x7d, 0xb8, //0x0000057a movq         $-72(%rbp), %rdi
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x0000057e jmp          LBB0_78
	//0x00000583 LBB0_77
	0x48, 0x01, 0xd1, //0x00000583 addq         %rdx, %rcx
	0x49, 0x89, 0xc8, //0x00000586 movq         %rcx, %r8
	//0x00000589 LBB0_78
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00000589 movq         $-1, %rdx
	0x4d, 0x85, 0xd2, //0x00000590 testq        %r10, %r10
	0x0f, 0x84, 0x5d, 0x04, 0x00, 0x00, //0x00000593 je           LBB0_138
	0x4d, 0x85, 0xdb, //0x00000599 testq        %r11, %r11
	0x0f, 0x84, 0x54, 0x04, 0x00, 0x00, //0x0000059c je           LBB0_138
	0x4d, 0x85, 0xed, //0x000005a2 testq        %r13, %r13
	0x0f, 0x84, 0x4b, 0x04, 0x00, 0x00, //0x000005a5 je           LBB0_138
	0x4d, 0x29, 0xe0, //0x000005ab subq         %r12, %r8
	0x49, 0x8d, 0x40, 0xff, //0x000005ae leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc2, //0x000005b2 cmpq         %rax, %r10
	0x0f, 0x84, 0xee, 0x03, 0x00, 0x00, //0x000005b5 je           LBB0_133
	0x49, 0x39, 0xc3, //0x000005bb cmpq         %rax, %r11
	0x0f, 0x84, 0xe5, 0x03, 0x00, 0x00, //0x000005be je           LBB0_133
	0x49, 0x39, 0xc5, //0x000005c4 cmpq         %rax, %r13
	0x0f, 0x84, 0xdc, 0x03, 0x00, 0x00, //0x000005c7 je           LBB0_133
	0x4d, 0x85, 0xdb, //0x000005cd testq        %r11, %r11
	0x0f, 0x8e, 0x66, 0x06, 0x00, 0x00, //0x000005d0 jle          LBB0_154
	0x49, 0x8d, 0x43, 0xff, //0x000005d6 leaq         $-1(%r11), %rax
	0x49, 0x39, 0xc5, //0x000005da cmpq         %rax, %r13
	0x0f, 0x84, 0x59, 0x06, 0x00, 0x00, //0x000005dd je           LBB0_154
	0x49, 0xf7, 0xd3, //0x000005e3 notq         %r11
	0x4c, 0x89, 0xda, //0x000005e6 movq         %r11, %rdx
	0xe9, 0xc1, 0x03, 0x00, 0x00, //0x000005e9 jmp          LBB0_134
	//0x000005ee LBB0_87
	0x48, 0x89, 0x55, 0xb0, //0x000005ee movq         %rdx, $-80(%rbp)
	0x80, 0xfb, 0x30, //0x000005f2 cmpb         $48, %bl
	0x0f, 0x85, 0x32, 0x00, 0x00, 0x00, //0x000005f5 jne          LBB0_91
	0x4c, 0x8d, 0x61, 0x01, //0x000005fb leaq         $1(%rcx), %r12
	0x4c, 0x39, 0xf9, //0x000005ff cmpq         %r15, %rcx
	0x0f, 0x83, 0xba, 0xfc, 0xff, 0xff, //0x00000602 jae          LBB0_38
	0x42, 0x8a, 0x04, 0x27, //0x00000608 movb         (%rdi,%r12), %al
	0x04, 0xd2, //0x0000060c addb         $-46, %al
	0x3c, 0x37, //0x0000060e cmpb         $55, %al
	0x0f, 0x87, 0xac, 0xfc, 0xff, 0xff, //0x00000610 ja           LBB0_38
	0x0f, 0xb6, 0xc0, //0x00000616 movzbl       %al, %eax
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000619 movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xc2, //0x00000623 btq          %rax, %rdx
	0x0f, 0x83, 0x95, 0xfc, 0xff, 0xff, //0x00000627 jae          LBB0_38
	//0x0000062d LBB0_91
	0x4c, 0x89, 0x55, 0xd0, //0x0000062d movq         %r10, $-48(%rbp)
	0x4c, 0x39, 0xf9, //0x00000631 cmpq         %r15, %rcx
	0x0f, 0x83, 0x25, 0x06, 0x00, 0x00, //0x00000634 jae          LBB0_157
	0x41, 0xbb, 0xd0, 0xff, 0xff, 0xff, //0x0000063a movl         $4294967248, %r11d
	0x48, 0x83, 0xc1, 0x01, //0x00000640 addq         $1, %rcx
	0x31, 0xd2, //0x00000644 xorl         %edx, %edx
	0x31, 0xc0, //0x00000646 xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x00000648 xorl         %r10d, %r10d
	//0x0000064b LBB0_93
	0x83, 0xf8, 0x12, //0x0000064b cmpl         $18, %eax
	0x0f, 0x8f, 0x16, 0x00, 0x00, 0x00, //0x0000064e jg           LBB0_95
	0x4b, 0x8d, 0x34, 0x92, //0x00000654 leaq         (%r10,%r10,4), %rsi
	0x0f, 0xb6, 0xdb, //0x00000658 movzbl       %bl, %ebx
	0x44, 0x01, 0xdb, //0x0000065b addl         %r11d, %ebx
	0x4c, 0x8d, 0x14, 0x73, //0x0000065e leaq         (%rbx,%rsi,2), %r10
	0x83, 0xc0, 0x01, //0x00000662 addl         $1, %eax
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00000665 jmp          LBB0_96
	//0x0000066a LBB0_95
	0x83, 0xc2, 0x01, //0x0000066a addl         $1, %edx
	//0x0000066d LBB0_96
	0x49, 0x39, 0xcf, //0x0000066d cmpq         %rcx, %r15
	0x0f, 0x84, 0x23, 0x07, 0x00, 0x00, //0x00000670 je           LBB0_179
	0x0f, 0xb6, 0x1c, 0x0f, //0x00000676 movzbl       (%rdi,%rcx), %ebx
	0x8d, 0x73, 0xd0, //0x0000067a leal         $-48(%rbx), %esi
	0x48, 0x83, 0xc1, 0x01, //0x0000067d addq         $1, %rcx
	0x40, 0x80, 0xfe, 0x0a, //0x00000681 cmpb         $10, %sil
	0x0f, 0x82, 0xc0, 0xff, 0xff, 0xff, //0x00000685 jb           LBB0_93
	0x80, 0xfb, 0x2e, //0x0000068b cmpb         $46, %bl
	0x0f, 0x85, 0x36, 0x07, 0x00, 0x00, //0x0000068e jne          LBB0_187
	0x49, 0xc7, 0x45, 0x00, 0x08, 0x00, 0x00, 0x00, //0x00000694 movq         $8, (%r13)
	0x4c, 0x39, 0xf9, //0x0000069c cmpq         %r15, %rcx
	0x0f, 0x83, 0x89, 0x08, 0x00, 0x00, //0x0000069f jae          LBB0_215
	0x8a, 0x1c, 0x0f, //0x000006a5 movb         (%rdi,%rcx), %bl
	0x80, 0xc3, 0xc6, //0x000006a8 addb         $-58, %bl
	0x80, 0xfb, 0xf5, //0x000006ab cmpb         $-11, %bl
	0x0f, 0x86, 0xf6, 0xfb, 0xff, 0xff, //0x000006ae jbe          LBB0_36
	0xc7, 0x45, 0x90, 0x00, 0x00, 0x00, 0x00, //0x000006b4 movl         $0, $-112(%rbp)
	0xe9, 0x14, 0x07, 0x00, 0x00, //0x000006bb jmp          LBB0_188
	//0x000006c0 LBB0_102
	0x31, 0xc0, //0x000006c0 xorl         %eax, %eax
	0x41, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x80, //0x000006c2 testl        $-2147483648, %r8d
	0x0f, 0x94, 0xc0, //0x000006c9 sete         %al
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000006cc movq         $-2, %rcx
	0xba, 0x0b, 0x00, 0x00, 0x00, //0x000006d3 movl         $11, %edx
	0xe9, 0x7d, 0x02, 0x00, 0x00, //0x000006d8 jmp          LBB0_128
	//0x000006dd LBB0_103
	0x49, 0x8d, 0x4f, 0xfd, //0x000006dd leaq         $-3(%r15), %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000006e1 movq         $-1, %rax
	0x49, 0x39, 0xc9, //0x000006e8 cmpq         %rcx, %r9
	0x0f, 0x83, 0xcd, 0x06, 0x00, 0x00, //0x000006eb jae          LBB0_186
	0x41, 0x8b, 0x0a, //0x000006f1 movl         (%r10), %ecx
	0x81, 0xf9, 0x6e, 0x75, 0x6c, 0x6c, //0x000006f4 cmpl         $1819047278, %ecx
	0x0f, 0x85, 0x70, 0x05, 0x00, 0x00, //0x000006fa jne          LBB0_158
	0x49, 0x83, 0xc1, 0x04, //0x00000700 addq         $4, %r9
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00000704 movl         $2, %eax
	0xe9, 0xad, 0x06, 0x00, 0x00, //0x00000709 jmp          LBB0_185
	//0x0000070e LBB0_106
	0x31, 0xc0, //0x0000070e xorl         %eax, %eax
	0x41, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x80, //0x00000710 testl        $-2147483648, %r8d
	0x0f, 0x94, 0xc0, //0x00000717 sete         %al
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000071a movq         $-2, %rcx
	0xba, 0x0d, 0x00, 0x00, 0x00, //0x00000721 movl         $13, %edx
	0xe9, 0x2f, 0x02, 0x00, 0x00, //0x00000726 jmp          LBB0_128
	//0x0000072b LBB0_107
	0x49, 0xc7, 0x45, 0x00, 0xfe, 0xff, 0xff, 0xff, //0x0000072b movq         $-2, (%r13)
	0x4d, 0x89, 0xcc, //0x00000733 movq         %r9, %r12
	0xe9, 0x87, 0xfb, 0xff, 0xff, //0x00000736 jmp          LBB0_38
	//0x0000073b LBB0_108
	0x41, 0xf6, 0xc0, 0x20, //0x0000073b testb        $32, %r8b
	0x4c, 0x89, 0x65, 0xa8, //0x0000073f movq         %r12, $-88(%rbp)
	0x4c, 0x89, 0x6d, 0xc0, //0x00000743 movq         %r13, $-64(%rbp)
	0x0f, 0x85, 0xc6, 0x02, 0x00, 0x00, //0x00000747 jne          LBB0_140
	0x4d, 0x39, 0xfc, //0x0000074d cmpq         %r15, %r12
	0x0f, 0x84, 0x98, 0x25, 0x00, 0x00, //0x00000750 je           LBB0_662
	0x4d, 0x89, 0xfb, //0x00000756 movq         %r15, %r11
	0x4d, 0x29, 0xe3, //0x00000759 subq         %r12, %r11
	0x49, 0x83, 0xfb, 0x40, //0x0000075c cmpq         $64, %r11
	0x0f, 0x82, 0x94, 0x25, 0x00, 0x00, //0x00000760 jb           LBB0_663
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00000766 movq         $-2, %rcx
	0x4c, 0x29, 0xc9, //0x0000076d subq         %r9, %rcx
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00000770 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc9, //0x00000778 xorl         %r9d, %r9d
	0xf3, 0x0f, 0x6f, 0x05, 0x7d, 0xf8, 0xff, 0xff, //0x0000077b movdqu       $-1923(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x85, 0xf8, 0xff, 0xff, //0x00000783 movdqu       $-1915(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000078b .p2align 4, 0x90
	//0x00000790 LBB0_112
	0xf3, 0x42, 0x0f, 0x6f, 0x14, 0x27, //0x00000790 movdqu       (%rdi,%r12), %xmm2
	0xf3, 0x42, 0x0f, 0x6f, 0x5c, 0x27, 0x10, //0x00000796 movdqu       $16(%rdi,%r12), %xmm3
	0xf3, 0x42, 0x0f, 0x6f, 0x64, 0x27, 0x20, //0x0000079d movdqu       $32(%rdi,%r12), %xmm4
	0x49, 0x89, 0xfe, //0x000007a4 movq         %rdi, %r14
	0xf3, 0x42, 0x0f, 0x6f, 0x6c, 0x27, 0x30, //0x000007a7 movdqu       $48(%rdi,%r12), %xmm5
	0x66, 0x0f, 0x6f, 0xf2, //0x000007ae movdqa       %xmm2, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000007b2 pcmpeqb      %xmm0, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xee, //0x000007b6 pmovmskb     %xmm6, %r13d
	0x66, 0x0f, 0x6f, 0xf3, //0x000007bb movdqa       %xmm3, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000007bf pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xc6, //0x000007c3 pmovmskb     %xmm6, %eax
	0x66, 0x0f, 0x6f, 0xf4, //0x000007c7 movdqa       %xmm4, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000007cb pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xd6, //0x000007cf pmovmskb     %xmm6, %edx
	0x66, 0x0f, 0x6f, 0xf5, //0x000007d3 movdqa       %xmm5, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000007d7 pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xfe, //0x000007db pmovmskb     %xmm6, %edi
	0x66, 0x0f, 0x74, 0xd1, //0x000007df pcmpeqb      %xmm1, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xc2, //0x000007e3 pmovmskb     %xmm2, %r8d
	0x66, 0x0f, 0x74, 0xd9, //0x000007e8 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x000007ec pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x74, 0xe1, //0x000007f0 pcmpeqb      %xmm1, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xd4, //0x000007f4 pmovmskb     %xmm4, %r10d
	0x66, 0x0f, 0x74, 0xe9, //0x000007f9 pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x000007fd pmovmskb     %xmm5, %ebx
	0x48, 0xc1, 0xe7, 0x30, //0x00000801 shlq         $48, %rdi
	0x48, 0xc1, 0xe2, 0x20, //0x00000805 shlq         $32, %rdx
	0x48, 0x09, 0xfa, //0x00000809 orq          %rdi, %rdx
	0x48, 0xc1, 0xe0, 0x10, //0x0000080c shlq         $16, %rax
	0x48, 0x09, 0xd0, //0x00000810 orq          %rdx, %rax
	0x49, 0x09, 0xc5, //0x00000813 orq          %rax, %r13
	0x48, 0xc1, 0xe3, 0x30, //0x00000816 shlq         $48, %rbx
	0x49, 0xc1, 0xe2, 0x20, //0x0000081a shlq         $32, %r10
	0x49, 0x09, 0xda, //0x0000081e orq          %rbx, %r10
	0x48, 0xc1, 0xe6, 0x10, //0x00000821 shlq         $16, %rsi
	0x4c, 0x09, 0xd6, //0x00000825 orq          %r10, %rsi
	0x49, 0x09, 0xf0, //0x00000828 orq          %rsi, %r8
	0x0f, 0x85, 0x33, 0x00, 0x00, 0x00, //0x0000082b jne          LBB0_116
	0x4d, 0x85, 0xc9, //0x00000831 testq        %r9, %r9
	0x0f, 0x85, 0x48, 0x00, 0x00, 0x00, //0x00000834 jne          LBB0_118
	0x45, 0x31, 0xc9, //0x0000083a xorl         %r9d, %r9d
	0x4c, 0x89, 0xf7, //0x0000083d movq         %r14, %rdi
	0x4d, 0x85, 0xed, //0x00000840 testq        %r13, %r13
	0x0f, 0x85, 0x8e, 0x00, 0x00, 0x00, //0x00000843 jne          LBB0_120
	//0x00000849 LBB0_115
	0x49, 0x83, 0xc3, 0xc0, //0x00000849 addq         $-64, %r11
	0x48, 0x83, 0xc1, 0xc0, //0x0000084d addq         $-64, %rcx
	0x49, 0x83, 0xc4, 0x40, //0x00000851 addq         $64, %r12
	0x49, 0x83, 0xfb, 0x3f, //0x00000855 cmpq         $63, %r11
	0x0f, 0x87, 0x31, 0xff, 0xff, 0xff, //0x00000859 ja           LBB0_112
	0xe9, 0xa4, 0x0c, 0x00, 0x00, //0x0000085f jmp          LBB0_290
	//0x00000864 LBB0_116
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00000864 cmpq         $-1, $-56(%rbp)
	0x4c, 0x89, 0xf7, //0x00000869 movq         %r14, %rdi
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x0000086c jne          LBB0_119
	0x49, 0x0f, 0xbc, 0xc0, //0x00000872 bsfq         %r8, %rax
	0x4c, 0x01, 0xe0, //0x00000876 addq         %r12, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00000879 movq         %rax, $-56(%rbp)
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x0000087d jmp          LBB0_119
	//0x00000882 LBB0_118
	0x4c, 0x89, 0xf7, //0x00000882 movq         %r14, %rdi
	//0x00000885 LBB0_119
	0x4c, 0x89, 0xc8, //0x00000885 movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00000888 notq         %rax
	0x4c, 0x21, 0xc0, //0x0000088b andq         %r8, %rax
	0x48, 0x8d, 0x14, 0x00, //0x0000088e leaq         (%rax,%rax), %rdx
	0x4c, 0x09, 0xca, //0x00000892 orq          %r9, %rdx
	0x48, 0x89, 0xd6, //0x00000895 movq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00000898 notq         %rsi
	0x4c, 0x21, 0xc6, //0x0000089b andq         %r8, %rsi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000089e movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xde, //0x000008a8 andq         %rbx, %rsi
	0x45, 0x31, 0xc9, //0x000008ab xorl         %r9d, %r9d
	0x48, 0x01, 0xc6, //0x000008ae addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc1, //0x000008b1 setb         %r9b
	0x48, 0x01, 0xf6, //0x000008b5 addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000008b8 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x000008c2 xorq         %rax, %rsi
	0x48, 0x21, 0xd6, //0x000008c5 andq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x000008c8 notq         %rsi
	0x49, 0x21, 0xf5, //0x000008cb andq         %rsi, %r13
	0x4d, 0x85, 0xed, //0x000008ce testq        %r13, %r13
	0x0f, 0x84, 0x72, 0xff, 0xff, 0xff, //0x000008d1 je           LBB0_115
	//0x000008d7 LBB0_120
	0x4d, 0x0f, 0xbc, 0xe5, //0x000008d7 bsfq         %r13, %r12
	0x49, 0x29, 0xcc, //0x000008db subq         %rcx, %r12
	//0x000008de LBB0_121
	0x4c, 0x8b, 0x6d, 0xc0, //0x000008de movq         $-64(%rbp), %r13
	0xe9, 0x6f, 0x04, 0x00, 0x00, //0x000008e2 jmp          LBB0_175
	//0x000008e7 LBB0_122
	0x31, 0xc0, //0x000008e7 xorl         %eax, %eax
	0x41, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x80, //0x000008e9 testl        $-2147483648, %r8d
	0x0f, 0x94, 0xc0, //0x000008f0 sete         %al
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000008f3 movq         $-2, %rcx
	0xba, 0x0a, 0x00, 0x00, 0x00, //0x000008fa movl         $10, %edx
	0xe9, 0x56, 0x00, 0x00, 0x00, //0x000008ff jmp          LBB0_128
	//0x00000904 LBB0_123
	0x49, 0xc7, 0x45, 0x00, 0x05, 0x00, 0x00, 0x00, //0x00000904 movq         $5, (%r13)
	0xe9, 0xb1, 0xf9, 0xff, 0xff, //0x0000090c jmp          LBB0_38
	//0x00000911 LBB0_124
	0x49, 0x8d, 0x4f, 0xfd, //0x00000911 leaq         $-3(%r15), %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000915 movq         $-1, %rax
	0x49, 0x39, 0xc9, //0x0000091c cmpq         %rcx, %r9
	0x0f, 0x83, 0x99, 0x04, 0x00, 0x00, //0x0000091f jae          LBB0_186
	0x41, 0x8b, 0x0a, //0x00000925 movl         (%r10), %ecx
	0x81, 0xf9, 0x74, 0x72, 0x75, 0x65, //0x00000928 cmpl         $1702195828, %ecx
	0x0f, 0x85, 0x77, 0x03, 0x00, 0x00, //0x0000092e jne          LBB0_162
	0x49, 0x83, 0xc1, 0x04, //0x00000934 addq         $4, %r9
	0xb8, 0x03, 0x00, 0x00, 0x00, //0x00000938 movl         $3, %eax
	0xe9, 0x79, 0x04, 0x00, 0x00, //0x0000093d jmp          LBB0_185
	//0x00000942 LBB0_127
	0x31, 0xc0, //0x00000942 xorl         %eax, %eax
	0x41, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x80, //0x00000944 testl        $-2147483648, %r8d
	0x0f, 0x94, 0xc0, //0x0000094b sete         %al
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000094e movq         $-2, %rcx
	0xba, 0x0c, 0x00, 0x00, 0x00, //0x00000955 movl         $12, %edx
	//0x0000095a LBB0_128
	0x48, 0x0f, 0x44, 0xd1, //0x0000095a cmoveq       %rcx, %rdx
	0x49, 0x89, 0x55, 0x00, //0x0000095e movq         %rdx, (%r13)
	0x49, 0x29, 0xc4, //0x00000962 subq         %rax, %r12
	0xe9, 0x58, 0xf9, 0xff, 0xff, //0x00000965 jmp          LBB0_38
	//0x0000096a LBB0_129
	0x49, 0x8d, 0x4f, 0xfc, //0x0000096a leaq         $-4(%r15), %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000096e movq         $-1, %rax
	0x49, 0x39, 0xc9, //0x00000975 cmpq         %rcx, %r9
	0x0f, 0x83, 0x40, 0x04, 0x00, 0x00, //0x00000978 jae          LBB0_186
	0x42, 0x8b, 0x0c, 0x27, //0x0000097e movl         (%rdi,%r12), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x00000982 cmpl         $1702063201, %ecx
	0x0f, 0x85, 0x58, 0x03, 0x00, 0x00, //0x00000988 jne          LBB0_166
	0x49, 0x83, 0xc1, 0x05, //0x0000098e addq         $5, %r9
	0xb8, 0x04, 0x00, 0x00, 0x00, //0x00000992 movl         $4, %eax
	0xe9, 0x1f, 0x04, 0x00, 0x00, //0x00000997 jmp          LBB0_185
	//0x0000099c LBB0_132
	0x49, 0xc7, 0x45, 0x00, 0x06, 0x00, 0x00, 0x00, //0x0000099c movq         $6, (%r13)
	0xe9, 0x19, 0xf9, 0xff, 0xff, //0x000009a4 jmp          LBB0_38
	//0x000009a9 LBB0_133
	0x49, 0xf7, 0xd8, //0x000009a9 negq         %r8
	0x4c, 0x89, 0xc2, //0x000009ac movq         %r8, %rdx
	//0x000009af LBB0_134
	0x48, 0x85, 0xd2, //0x000009af testq        %rdx, %rdx
	0x0f, 0x88, 0x3e, 0x00, 0x00, 0x00, //0x000009b2 js           LBB0_138
	//0x000009b8 LBB0_135
	0x49, 0x01, 0xd4, //0x000009b8 addq         %rdx, %r12
	0x49, 0x29, 0xfc, //0x000009bb subq         %rdi, %r12
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x000009be movabsq      $-9223372036854775808, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000009c8 addq         $-2, %rax
	0x49, 0x39, 0xc1, //0x000009cc cmpq         %rax, %r9
	0x0f, 0x86, 0x0d, 0x00, 0x00, 0x00, //0x000009cf jbe          LBB0_137
	0x4c, 0x8b, 0x6d, 0xc0, //0x000009d5 movq         $-64(%rbp), %r13
	0x4d, 0x89, 0x4d, 0x00, //0x000009d9 movq         %r9, (%r13)
	0xe9, 0xe0, 0xf8, 0xff, 0xff, //0x000009dd jmp          LBB0_38
	//0x000009e2 LBB0_137
	0x48, 0x8b, 0x7d, 0xc0, //0x000009e2 movq         $-64(%rbp), %rdi
	0x48, 0xc7, 0x07, 0x08, 0x00, 0x00, 0x00, //0x000009e6 movq         $8, (%rdi)
	0x4c, 0x89, 0x4f, 0x18, //0x000009ed movq         %r9, $24(%rdi)
	0xe9, 0xcc, 0xf8, 0xff, 0xff, //0x000009f1 jmp          LBB0_38
	//0x000009f6 LBB0_138
	0x48, 0xf7, 0xd2, //0x000009f6 notq         %rdx
	0x49, 0x01, 0xd4, //0x000009f9 addq         %rdx, %r12
	//0x000009fc LBB0_139
	0x4c, 0x8b, 0x6d, 0xc0, //0x000009fc movq         $-64(%rbp), %r13
	0x49, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00000a00 movq         $-2, %r9
	0x49, 0x29, 0xfc, //0x00000a07 subq         %rdi, %r12
	0x4d, 0x89, 0x4d, 0x00, //0x00000a0a movq         %r9, (%r13)
	0xe9, 0xaf, 0xf8, 0xff, 0xff, //0x00000a0e jmp          LBB0_38
	//0x00000a13 LBB0_140
	0x4d, 0x39, 0xfc, //0x00000a13 cmpq         %r15, %r12
	0x0f, 0x84, 0xd2, 0x22, 0x00, 0x00, //0x00000a16 je           LBB0_662
	0x4c, 0x89, 0xf9, //0x00000a1c movq         %r15, %rcx
	0x4c, 0x29, 0xe1, //0x00000a1f subq         %r12, %rcx
	0x48, 0x83, 0xf9, 0x40, //0x00000a22 cmpq         $64, %rcx
	0x0f, 0x82, 0xee, 0x22, 0x00, 0x00, //0x00000a26 jb           LBB0_665
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000a2c movq         $-2, %rax
	0x4c, 0x29, 0xc8, //0x00000a33 subq         %r9, %rax
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00000a36 movq         $-1, $-56(%rbp)
	0x31, 0xf6, //0x00000a3e xorl         %esi, %esi
	0xf3, 0x44, 0x0f, 0x6f, 0x05, 0xb7, 0xf5, 0xff, 0xff, //0x00000a40 movdqu       $-2633(%rip), %xmm8  /* LCPI0_0+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x0d, 0xbe, 0xf5, 0xff, 0xff, //0x00000a49 movdqu       $-2626(%rip), %xmm9  /* LCPI0_1+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xc5, 0xf5, 0xff, 0xff, //0x00000a52 movdqu       $-2619(%rip), %xmm10  /* LCPI0_2+0(%rip) */
	0x4c, 0x89, 0xe2, //0x00000a5b movq         %r12, %rdx
	0x48, 0x89, 0x7d, 0xb8, //0x00000a5e movq         %rdi, $-72(%rbp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a62 .p2align 4, 0x90
	//0x00000a70 LBB0_143
	0x48, 0x89, 0x75, 0xb0, //0x00000a70 movq         %rsi, $-80(%rbp)
	0x48, 0x89, 0x4d, 0xd0, //0x00000a74 movq         %rcx, $-48(%rbp)
	0xf3, 0x0f, 0x6f, 0x1c, 0x17, //0x00000a78 movdqu       (%rdi,%rdx), %xmm3
	0xf3, 0x0f, 0x6f, 0x44, 0x17, 0x10, //0x00000a7d movdqu       $16(%rdi,%rdx), %xmm0
	0xf3, 0x0f, 0x6f, 0x4c, 0x17, 0x20, //0x00000a83 movdqu       $32(%rdi,%rdx), %xmm1
	0xf3, 0x0f, 0x6f, 0x54, 0x17, 0x30, //0x00000a89 movdqu       $48(%rdi,%rdx), %xmm2
	0x66, 0x0f, 0x6f, 0xfb, //0x00000a8f movdqa       %xmm3, %xmm7
	0x66, 0x41, 0x0f, 0x74, 0xf8, //0x00000a93 pcmpeqb      %xmm8, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xef, //0x00000a98 pmovmskb     %xmm7, %r13d
	0x66, 0x0f, 0x6f, 0xf8, //0x00000a9d movdqa       %xmm0, %xmm7
	0x66, 0x0f, 0x6f, 0xf0, //0x00000aa1 movdqa       %xmm0, %xmm6
	0x66, 0x41, 0x0f, 0xda, 0xf2, //0x00000aa5 pminub       %xmm10, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x00000aaa pcmpeqb      %xmm0, %xmm6
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x00000aae pcmpeqb      %xmm8, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xc0, //0x00000ab3 pmovmskb     %xmm0, %r8d
	0x66, 0x0f, 0x6f, 0xc1, //0x00000ab8 movdqa       %xmm1, %xmm0
	0x66, 0x0f, 0x6f, 0xe9, //0x00000abc movdqa       %xmm1, %xmm5
	0x66, 0x41, 0x0f, 0xda, 0xea, //0x00000ac0 pminub       %xmm10, %xmm5
	0x66, 0x0f, 0x74, 0xe9, //0x00000ac5 pcmpeqb      %xmm1, %xmm5
	0x66, 0x41, 0x0f, 0x74, 0xc8, //0x00000ac9 pcmpeqb      %xmm8, %xmm1
	0x66, 0x44, 0x0f, 0xd7, 0xc9, //0x00000ace pmovmskb     %xmm1, %r9d
	0x66, 0x0f, 0x6f, 0xca, //0x00000ad3 movdqa       %xmm2, %xmm1
	0x66, 0x0f, 0x6f, 0xe2, //0x00000ad7 movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0xda, 0xe2, //0x00000adb pminub       %xmm10, %xmm4
	0x66, 0x0f, 0x74, 0xe2, //0x00000ae0 pcmpeqb      %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xd0, //0x00000ae4 pcmpeqb      %xmm8, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xe2, //0x00000ae9 pmovmskb     %xmm2, %r12d
	0x66, 0x0f, 0x6f, 0xd3, //0x00000aee movdqa       %xmm3, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd1, //0x00000af2 pcmpeqb      %xmm9, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00000af7 pmovmskb     %xmm2, %esi
	0x66, 0x41, 0x0f, 0x74, 0xf9, //0x00000afb pcmpeqb      %xmm9, %xmm7
	0x66, 0x0f, 0xd7, 0xcf, //0x00000b00 pmovmskb     %xmm7, %ecx
	0x66, 0x41, 0x0f, 0x74, 0xc1, //0x00000b04 pcmpeqb      %xmm9, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00000b09 pmovmskb     %xmm0, %edi
	0x66, 0x41, 0x0f, 0x74, 0xc9, //0x00000b0d pcmpeqb      %xmm9, %xmm1
	0x66, 0x44, 0x0f, 0xd7, 0xd9, //0x00000b12 pmovmskb     %xmm1, %r11d
	0x66, 0x0f, 0xd7, 0xde, //0x00000b17 pmovmskb     %xmm6, %ebx
	0x66, 0x44, 0x0f, 0xd7, 0xd5, //0x00000b1b pmovmskb     %xmm5, %r10d
	0x66, 0x44, 0x0f, 0xd7, 0xf4, //0x00000b20 pmovmskb     %xmm4, %r14d
	0x49, 0xc1, 0xe4, 0x30, //0x00000b25 shlq         $48, %r12
	0x49, 0xc1, 0xe1, 0x20, //0x00000b29 shlq         $32, %r9
	0x4d, 0x09, 0xe1, //0x00000b2d orq          %r12, %r9
	0x49, 0xc1, 0xe0, 0x10, //0x00000b30 shlq         $16, %r8
	0x4d, 0x09, 0xc8, //0x00000b34 orq          %r9, %r8
	0x4d, 0x09, 0xc5, //0x00000b37 orq          %r8, %r13
	0x49, 0xc1, 0xe3, 0x30, //0x00000b3a shlq         $48, %r11
	0x48, 0xc1, 0xe7, 0x20, //0x00000b3e shlq         $32, %rdi
	0x4c, 0x09, 0xdf, //0x00000b42 orq          %r11, %rdi
	0x48, 0xc1, 0xe1, 0x10, //0x00000b45 shlq         $16, %rcx
	0x48, 0x09, 0xf9, //0x00000b49 orq          %rdi, %rcx
	0x49, 0xc1, 0xe6, 0x30, //0x00000b4c shlq         $48, %r14
	0x49, 0xc1, 0xe2, 0x20, //0x00000b50 shlq         $32, %r10
	0x4d, 0x09, 0xf2, //0x00000b54 orq          %r14, %r10
	0x48, 0xc1, 0xe3, 0x10, //0x00000b57 shlq         $16, %rbx
	0x4c, 0x09, 0xd3, //0x00000b5b orq          %r10, %rbx
	0x48, 0x09, 0xce, //0x00000b5e orq          %rcx, %rsi
	0x0f, 0x85, 0x58, 0x00, 0x00, 0x00, //0x00000b61 jne          LBB0_149
	0x48, 0x8b, 0x7d, 0xb0, //0x00000b67 movq         $-80(%rbp), %rdi
	0x48, 0x85, 0xff, //0x00000b6b testq        %rdi, %rdi
	0x0f, 0x85, 0x65, 0x00, 0x00, 0x00, //0x00000b6e jne          LBB0_151
	0x31, 0xf6, //0x00000b74 xorl         %esi, %esi
	//0x00000b76 LBB0_146
	0x66, 0x0f, 0x6f, 0xc3, //0x00000b76 movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc2, //0x00000b7a pminub       %xmm10, %xmm0
	0x66, 0x0f, 0x74, 0xc3, //0x00000b7f pcmpeqb      %xmm3, %xmm0
	0x66, 0x0f, 0xd7, 0xc8, //0x00000b83 pmovmskb     %xmm0, %ecx
	0x48, 0x09, 0xcb, //0x00000b87 orq          %rcx, %rbx
	0x4d, 0x85, 0xed, //0x00000b8a testq        %r13, %r13
	0x48, 0x8b, 0x7d, 0xb8, //0x00000b8d movq         $-72(%rbp), %rdi
	0x0f, 0x85, 0x8f, 0x00, 0x00, 0x00, //0x00000b91 jne          LBB0_152
	0x48, 0x85, 0xdb, //0x00000b97 testq        %rbx, %rbx
	0x0f, 0x85, 0x18, 0x24, 0x00, 0x00, //0x00000b9a jne          LBB0_709
	0x48, 0x8b, 0x4d, 0xd0, //0x00000ba0 movq         $-48(%rbp), %rcx
	0x48, 0x83, 0xc1, 0xc0, //0x00000ba4 addq         $-64, %rcx
	0x48, 0x83, 0xc0, 0xc0, //0x00000ba8 addq         $-64, %rax
	0x48, 0x83, 0xc2, 0x40, //0x00000bac addq         $64, %rdx
	0x48, 0x83, 0xf9, 0x3f, //0x00000bb0 cmpq         $63, %rcx
	0x0f, 0x87, 0xb6, 0xfe, 0xff, 0xff, //0x00000bb4 ja           LBB0_143
	0xe9, 0xd3, 0x09, 0x00, 0x00, //0x00000bba jmp          LBB0_295
	//0x00000bbf LBB0_149
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00000bbf cmpq         $-1, $-56(%rbp)
	0x48, 0x8b, 0x7d, 0xb0, //0x00000bc4 movq         $-80(%rbp), %rdi
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00000bc8 jne          LBB0_151
	0x48, 0x0f, 0xbc, 0xce, //0x00000bce bsfq         %rsi, %rcx
	0x48, 0x01, 0xd1, //0x00000bd2 addq         %rdx, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00000bd5 movq         %rcx, $-56(%rbp)
	//0x00000bd9 LBB0_151
	0x48, 0x89, 0xf9, //0x00000bd9 movq         %rdi, %rcx
	0x48, 0xf7, 0xd1, //0x00000bdc notq         %rcx
	0x48, 0x21, 0xf1, //0x00000bdf andq         %rsi, %rcx
	0x4c, 0x8d, 0x04, 0x09, //0x00000be2 leaq         (%rcx,%rcx), %r8
	0x49, 0x09, 0xf8, //0x00000be6 orq          %rdi, %r8
	0x4c, 0x89, 0xc7, //0x00000be9 movq         %r8, %rdi
	0x48, 0xf7, 0xd7, //0x00000bec notq         %rdi
	0x48, 0x21, 0xf7, //0x00000bef andq         %rsi, %rdi
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000bf2 movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf7, //0x00000bfc andq         %rsi, %rdi
	0x31, 0xf6, //0x00000bff xorl         %esi, %esi
	0x48, 0x01, 0xcf, //0x00000c01 addq         %rcx, %rdi
	0x40, 0x0f, 0x92, 0xc6, //0x00000c04 setb         %sil
	0x48, 0x01, 0xff, //0x00000c08 addq         %rdi, %rdi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000c0b movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xcf, //0x00000c15 xorq         %rcx, %rdi
	0x4c, 0x21, 0xc7, //0x00000c18 andq         %r8, %rdi
	0x48, 0xf7, 0xd7, //0x00000c1b notq         %rdi
	0x49, 0x21, 0xfd, //0x00000c1e andq         %rdi, %r13
	0xe9, 0x50, 0xff, 0xff, 0xff, //0x00000c21 jmp          LBB0_146
	//0x00000c26 LBB0_152
	0x4d, 0x0f, 0xbc, 0xe5, //0x00000c26 bsfq         %r13, %r12
	0x48, 0x85, 0xdb, //0x00000c2a testq        %rbx, %rbx
	0x0f, 0x84, 0x0e, 0x01, 0x00, 0x00, //0x00000c2d je           LBB0_172
	0x48, 0x0f, 0xbc, 0xcb, //0x00000c33 bsfq         %rbx, %rcx
	0xe9, 0x0a, 0x01, 0x00, 0x00, //0x00000c37 jmp          LBB0_173
	//0x00000c3c LBB0_154
	0x4c, 0x89, 0xd0, //0x00000c3c movq         %r10, %rax
	0x4c, 0x09, 0xe8, //0x00000c3f orq          %r13, %rax
	0x0f, 0x99, 0xc0, //0x00000c42 setns        %al
	0x0f, 0x88, 0xda, 0x00, 0x00, 0x00, //0x00000c45 js           LBB0_171
	0x4d, 0x39, 0xea, //0x00000c4b cmpq         %r13, %r10
	0x0f, 0x8c, 0xd1, 0x00, 0x00, 0x00, //0x00000c4e jl           LBB0_171
	0x49, 0xf7, 0xd2, //0x00000c54 notq         %r10
	0x4c, 0x89, 0xd2, //0x00000c57 movq         %r10, %rdx
	0xe9, 0x50, 0xfd, 0xff, 0xff, //0x00000c5a jmp          LBB0_134
	//0x00000c5f LBB0_157
	0xb0, 0x01, //0x00000c5f movb         $1, %al
	0x89, 0x45, 0x90, //0x00000c61 movl         %eax, $-112(%rbp)
	0x31, 0xd2, //0x00000c64 xorl         %edx, %edx
	0x31, 0xc0, //0x00000c66 xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x00000c68 xorl         %r10d, %r10d
	0xe9, 0x64, 0x01, 0x00, 0x00, //0x00000c6b jmp          LBB0_188
	//0x00000c70 LBB0_158
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000c70 movq         $-2, %rax
	0x80, 0xf9, 0x6e, //0x00000c77 cmpb         $110, %cl
	0x0f, 0x85, 0x3b, 0x01, 0x00, 0x00, //0x00000c7a jne          LBB0_185
	0x42, 0x80, 0x7c, 0x0f, 0x01, 0x75, //0x00000c80 cmpb         $117, $1(%rdi,%r9)
	0x0f, 0x85, 0x1a, 0x01, 0x00, 0x00, //0x00000c86 jne          LBB0_182
	0x42, 0x80, 0x7c, 0x0f, 0x02, 0x6c, //0x00000c8c cmpb         $108, $2(%rdi,%r9)
	0x0f, 0x85, 0x1f, 0x01, 0x00, 0x00, //0x00000c92 jne          LBB0_184
	0x4d, 0x8d, 0x79, 0x03, //0x00000c98 leaq         $3(%r9), %r15
	0x49, 0x8d, 0x49, 0x04, //0x00000c9c leaq         $4(%r9), %rcx
	0x42, 0x80, 0x7c, 0x0f, 0x03, 0x6c, //0x00000ca0 cmpb         $108, $3(%rdi,%r9)
	0xe9, 0x71, 0x00, 0x00, 0x00, //0x00000ca6 jmp          LBB0_170
	//0x00000cab LBB0_162
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000cab movq         $-2, %rax
	0x80, 0xf9, 0x74, //0x00000cb2 cmpb         $116, %cl
	0x0f, 0x85, 0x00, 0x01, 0x00, 0x00, //0x00000cb5 jne          LBB0_185
	0x42, 0x80, 0x7c, 0x0f, 0x01, 0x72, //0x00000cbb cmpb         $114, $1(%rdi,%r9)
	0x0f, 0x85, 0xdf, 0x00, 0x00, 0x00, //0x00000cc1 jne          LBB0_182
	0x42, 0x80, 0x7c, 0x0f, 0x02, 0x75, //0x00000cc7 cmpb         $117, $2(%rdi,%r9)
	0x0f, 0x85, 0xe4, 0x00, 0x00, 0x00, //0x00000ccd jne          LBB0_184
	0x4d, 0x8d, 0x79, 0x03, //0x00000cd3 leaq         $3(%r9), %r15
	0x49, 0x8d, 0x49, 0x04, //0x00000cd7 leaq         $4(%r9), %rcx
	0x42, 0x80, 0x7c, 0x0f, 0x03, 0x65, //0x00000cdb cmpb         $101, $3(%rdi,%r9)
	0xe9, 0x36, 0x00, 0x00, 0x00, //0x00000ce1 jmp          LBB0_170
	//0x00000ce6 LBB0_166
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000ce6 movq         $-2, %rax
	0x80, 0xf9, 0x61, //0x00000ced cmpb         $97, %cl
	0x0f, 0x85, 0xb9, 0x00, 0x00, 0x00, //0x00000cf0 jne          LBB0_183
	0x42, 0x80, 0x7c, 0x0f, 0x02, 0x6c, //0x00000cf6 cmpb         $108, $2(%rdi,%r9)
	0x0f, 0x85, 0xb5, 0x00, 0x00, 0x00, //0x00000cfc jne          LBB0_184
	0x42, 0x80, 0x7c, 0x0f, 0x03, 0x73, //0x00000d02 cmpb         $115, $3(%rdi,%r9)
	0x0f, 0x85, 0xb8, 0x04, 0x00, 0x00, //0x00000d08 jne          LBB0_247
	0x4d, 0x8d, 0x79, 0x04, //0x00000d0e leaq         $4(%r9), %r15
	0x49, 0x8d, 0x49, 0x05, //0x00000d12 leaq         $5(%r9), %rcx
	0x42, 0x80, 0x7c, 0x0f, 0x04, 0x65, //0x00000d16 cmpb         $101, $4(%rdi,%r9)
	//0x00000d1c LBB0_170
	0x4c, 0x0f, 0x44, 0xf9, //0x00000d1c cmoveq       %rcx, %r15
	0xe9, 0x99, 0x00, 0x00, 0x00, //0x00000d20 jmp          LBB0_186
	//0x00000d25 LBB0_171
	0x49, 0x8d, 0x4d, 0xff, //0x00000d25 leaq         $-1(%r13), %rcx
	0x49, 0x39, 0xca, //0x00000d29 cmpq         %rcx, %r10
	0x49, 0xf7, 0xd5, //0x00000d2c notq         %r13
	0x4d, 0x0f, 0x45, 0xe8, //0x00000d2f cmovneq      %r8, %r13
	0x84, 0xc0, //0x00000d33 testb        %al, %al
	0x4d, 0x0f, 0x44, 0xe8, //0x00000d35 cmoveq       %r8, %r13
	0x4c, 0x89, 0xea, //0x00000d39 movq         %r13, %rdx
	0xe9, 0x6e, 0xfc, 0xff, 0xff, //0x00000d3c jmp          LBB0_134
	//0x00000d41 LBB0_172
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000d41 movl         $64, %ecx
	//0x00000d46 LBB0_173
	0x4c, 0x39, 0xe1, //0x00000d46 cmpq         %r12, %rcx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00000d49 movq         $-64(%rbp), %r13
	0x0f, 0x82, 0xed, 0x1f, 0x00, 0x00, //0x00000d4d jb           LBB0_667
	0x49, 0x29, 0xc4, //0x00000d53 subq         %rax, %r12
	//0x00000d56 LBB0_175
	0x4d, 0x85, 0xe4, //0x00000d56 testq        %r12, %r12
	0x0f, 0x88, 0x2b, 0x00, 0x00, 0x00, //0x00000d59 js           LBB0_177
	0x48, 0x8b, 0x45, 0xa8, //0x00000d5f movq         $-88(%rbp), %rax
	0x49, 0x89, 0x45, 0x10, //0x00000d63 movq         %rax, $16(%r13)
	0x49, 0xc7, 0x45, 0x00, 0x07, 0x00, 0x00, 0x00, //0x00000d67 movq         $7, (%r13)
	0x48, 0x8b, 0x4d, 0xc8, //0x00000d6f movq         $-56(%rbp), %rcx
	0x4c, 0x39, 0xe1, //0x00000d73 cmpq         %r12, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000d76 movq         $-1, %rax
	0x48, 0x0f, 0x4c, 0xc1, //0x00000d7d cmovlq       %rcx, %rax
	0x49, 0x89, 0x45, 0x18, //0x00000d81 movq         %rax, $24(%r13)
	0xe9, 0x38, 0xf5, 0xff, 0xff, //0x00000d85 jmp          LBB0_38
	//0x00000d8a LBB0_177
	0x4c, 0x89, 0xe7, //0x00000d8a movq         %r12, %rdi
	//0x00000d8d LBB0_178
	0x49, 0x89, 0x7d, 0x00, //0x00000d8d movq         %rdi, (%r13)
	0x4d, 0x89, 0xfc, //0x00000d91 movq         %r15, %r12
	0xe9, 0x29, 0xf5, 0xff, 0xff, //0x00000d94 jmp          LBB0_38
	//0x00000d99 LBB0_179
	0xb1, 0x01, //0x00000d99 movb         $1, %cl
	0x89, 0x4d, 0x90, //0x00000d9b movl         %ecx, $-112(%rbp)
	0x4c, 0x89, 0xf9, //0x00000d9e movq         %r15, %rcx
	0xe9, 0x2e, 0x00, 0x00, 0x00, //0x00000da1 jmp          LBB0_188
	//0x00000da6 LBB0_182
	0x49, 0x83, 0xc1, 0x01, //0x00000da6 addq         $1, %r9
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x00000daa jmp          LBB0_185
	//0x00000daf LBB0_183
	0x4d, 0x89, 0xe7, //0x00000daf movq         %r12, %r15
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00000db2 jmp          LBB0_186
	//0x00000db7 LBB0_184
	0x49, 0x83, 0xc1, 0x02, //0x00000db7 addq         $2, %r9
	//0x00000dbb LBB0_185
	0x4d, 0x89, 0xcf, //0x00000dbb movq         %r9, %r15
	//0x00000dbe LBB0_186
	0x49, 0x89, 0x45, 0x00, //0x00000dbe movq         %rax, (%r13)
	0x4d, 0x89, 0xfc, //0x00000dc2 movq         %r15, %r12
	0xe9, 0xf8, 0xf4, 0xff, 0xff, //0x00000dc5 jmp          LBB0_38
	//0x00000dca LBB0_187
	0x40, 0xb6, 0x01, //0x00000dca movb         $1, %sil
	0x89, 0x75, 0x90, //0x00000dcd movl         %esi, $-112(%rbp)
	0x48, 0x83, 0xc1, 0xff, //0x00000dd0 addq         $-1, %rcx
	//0x00000dd4 LBB0_188
	0x45, 0x31, 0xe4, //0x00000dd4 xorl         %r12d, %r12d
	0x85, 0xd2, //0x00000dd7 testl        %edx, %edx
	0x41, 0x0f, 0x9f, 0xc4, //0x00000dd9 setg         %r12b
	0x4d, 0x85, 0xd2, //0x00000ddd testq        %r10, %r10
	0x0f, 0x85, 0x54, 0x00, 0x00, 0x00, //0x00000de0 jne          LBB0_197
	0x85, 0xd2, //0x00000de6 testl        %edx, %edx
	0x0f, 0x85, 0x4c, 0x00, 0x00, 0x00, //0x00000de8 jne          LBB0_197
	0x4c, 0x39, 0xf9, //0x00000dee cmpq         %r15, %rcx
	0x0f, 0x83, 0x30, 0x00, 0x00, 0x00, //0x00000df1 jae          LBB0_195
	0x44, 0x89, 0x65, 0x98, //0x00000df7 movl         %r12d, $-104(%rbp)
	0x41, 0x89, 0xcc, //0x00000dfb movl         %ecx, %r12d
	0x45, 0x29, 0xfc, //0x00000dfe subl         %r15d, %r12d
	0x31, 0xc0, //0x00000e01 xorl         %eax, %eax
	0x31, 0xd2, //0x00000e03 xorl         %edx, %edx
	//0x00000e05 LBB0_192
	0x80, 0x3c, 0x0f, 0x30, //0x00000e05 cmpb         $48, (%rdi,%rcx)
	0x0f, 0x85, 0x24, 0x00, 0x00, 0x00, //0x00000e09 jne          LBB0_196
	0x48, 0x83, 0xc1, 0x01, //0x00000e0f addq         $1, %rcx
	0x83, 0xc2, 0xff, //0x00000e13 addl         $-1, %edx
	0x49, 0x39, 0xcf, //0x00000e16 cmpq         %rcx, %r15
	0x0f, 0x85, 0xe6, 0xff, 0xff, 0xff, //0x00000e19 jne          LBB0_192
	0x45, 0x31, 0xd2, //0x00000e1f xorl         %r10d, %r10d
	0xe9, 0x47, 0x01, 0x00, 0x00, //0x00000e22 jmp          LBB0_219
	//0x00000e27 LBB0_195
	0x31, 0xd2, //0x00000e27 xorl         %edx, %edx
	0x31, 0xc0, //0x00000e29 xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x00000e2b xorl         %r10d, %r10d
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00000e2e jmp          LBB0_197
	//0x00000e33 LBB0_196
	0x45, 0x31, 0xd2, //0x00000e33 xorl         %r10d, %r10d
	0x44, 0x8b, 0x65, 0x98, //0x00000e36 movl         $-104(%rbp), %r12d
	//0x00000e3a LBB0_197
	0x4c, 0x39, 0xf9, //0x00000e3a cmpq         %r15, %rcx
	0x0f, 0x83, 0x49, 0x00, 0x00, 0x00, //0x00000e3d jae          LBB0_203
	0x83, 0xf8, 0x12, //0x00000e43 cmpl         $18, %eax
	0x0f, 0x8f, 0x40, 0x00, 0x00, 0x00, //0x00000e46 jg           LBB0_203
	0x41, 0xbb, 0xd0, 0xff, 0xff, 0xff, //0x00000e4c movl         $4294967248, %r11d
	//0x00000e52 LBB0_200
	0x0f, 0xb6, 0x1c, 0x0f, //0x00000e52 movzbl       (%rdi,%rcx), %ebx
	0x8d, 0x73, 0xd0, //0x00000e56 leal         $-48(%rbx), %esi
	0x40, 0x80, 0xfe, 0x09, //0x00000e59 cmpb         $9, %sil
	0x0f, 0x87, 0x29, 0x00, 0x00, 0x00, //0x00000e5d ja           LBB0_203
	0x4b, 0x8d, 0x34, 0x92, //0x00000e63 leaq         (%r10,%r10,4), %rsi
	0x44, 0x01, 0xdb, //0x00000e67 addl         %r11d, %ebx
	0x4c, 0x8d, 0x14, 0x73, //0x00000e6a leaq         (%rbx,%rsi,2), %r10
	0x83, 0xc2, 0xff, //0x00000e6e addl         $-1, %edx
	0x48, 0x83, 0xc1, 0x01, //0x00000e71 addq         $1, %rcx
	0x4c, 0x39, 0xf9, //0x00000e75 cmpq         %r15, %rcx
	0x0f, 0x83, 0x0e, 0x00, 0x00, 0x00, //0x00000e78 jae          LBB0_203
	0x8d, 0x70, 0x01, //0x00000e7e leal         $1(%rax), %esi
	0x83, 0xf8, 0x12, //0x00000e81 cmpl         $18, %eax
	0x89, 0xf0, //0x00000e84 movl         %esi, %eax
	0x0f, 0x8c, 0xc6, 0xff, 0xff, 0xff, //0x00000e86 jl           LBB0_200
	//0x00000e8c LBB0_203
	0x4c, 0x39, 0xf9, //0x00000e8c cmpq         %r15, %rcx
	0x0f, 0x83, 0xa9, 0x00, 0x00, 0x00, //0x00000e8f jae          LBB0_216
	0x8a, 0x04, 0x0f, //0x00000e95 movb         (%rdi,%rcx), %al
	0x8d, 0x70, 0xd0, //0x00000e98 leal         $-48(%rax), %esi
	0x40, 0x80, 0xfe, 0x09, //0x00000e9b cmpb         $9, %sil
	0x0f, 0x87, 0x28, 0x00, 0x00, 0x00, //0x00000e9f ja           LBB0_209
	0x49, 0x8d, 0x77, 0xff, //0x00000ea5 leaq         $-1(%r15), %rsi
	//0x00000ea9 LBB0_206
	0x48, 0x39, 0xce, //0x00000ea9 cmpq         %rcx, %rsi
	0x0f, 0x84, 0xb2, 0x00, 0x00, 0x00, //0x00000eac je           LBB0_218
	0x0f, 0xb6, 0x44, 0x0f, 0x01, //0x00000eb2 movzbl       $1(%rdi,%rcx), %eax
	0x8d, 0x58, 0xd0, //0x00000eb7 leal         $-48(%rax), %ebx
	0x48, 0x83, 0xc1, 0x01, //0x00000eba addq         $1, %rcx
	0x80, 0xfb, 0x09, //0x00000ebe cmpb         $9, %bl
	0x0f, 0x86, 0xe2, 0xff, 0xff, 0xff, //0x00000ec1 jbe          LBB0_206
	0x41, 0xbc, 0x01, 0x00, 0x00, 0x00, //0x00000ec7 movl         $1, %r12d
	//0x00000ecd LBB0_209
	0x0c, 0x20, //0x00000ecd orb          $32, %al
	0x3c, 0x65, //0x00000ecf cmpb         $101, %al
	0x0f, 0x85, 0x67, 0x00, 0x00, 0x00, //0x00000ed1 jne          LBB0_216
	0x48, 0x8d, 0x41, 0x01, //0x00000ed7 leaq         $1(%rcx), %rax
	0x49, 0xc7, 0x45, 0x00, 0x08, 0x00, 0x00, 0x00, //0x00000edb movq         $8, (%r13)
	0x4c, 0x39, 0xf8, //0x00000ee3 cmpq         %r15, %rax
	0x0f, 0x83, 0x42, 0x00, 0x00, 0x00, //0x00000ee6 jae          LBB0_215
	0x40, 0x8a, 0x34, 0x07, //0x00000eec movb         (%rdi,%rax), %sil
	0x40, 0x80, 0xfe, 0x2d, //0x00000ef0 cmpb         $45, %sil
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x00000ef4 je           LBB0_213
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00000efa movl         $1, %ebx
	0x40, 0x80, 0xfe, 0x2b, //0x00000eff cmpb         $43, %sil
	0x0f, 0x85, 0xf6, 0x02, 0x00, 0x00, //0x00000f03 jne          LBB0_253
	//0x00000f09 LBB0_213
	0x48, 0x83, 0xc1, 0x02, //0x00000f09 addq         $2, %rcx
	0x4c, 0x39, 0xf9, //0x00000f0d cmpq         %r15, %rcx
	0x0f, 0x83, 0x18, 0x00, 0x00, 0x00, //0x00000f10 jae          LBB0_215
	0x31, 0xc0, //0x00000f16 xorl         %eax, %eax
	0x40, 0x80, 0xfe, 0x2b, //0x00000f18 cmpb         $43, %sil
	0x0f, 0x94, 0xc0, //0x00000f1c sete         %al
	0x8d, 0x1c, 0x00, //0x00000f1f leal         (%rax,%rax), %ebx
	0x83, 0xc3, 0xff, //0x00000f22 addl         $-1, %ebx
	0x40, 0x8a, 0x34, 0x0f, //0x00000f25 movb         (%rdi,%rcx), %sil
	0xe9, 0xd4, 0x02, 0x00, 0x00, //0x00000f29 jmp          LBB0_254
	//0x00000f2e LBB0_215
	0x49, 0xc7, 0x45, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00000f2e movq         $-1, (%r13)
	0x4d, 0x89, 0xfc, //0x00000f36 movq         %r15, %r12
	0xe9, 0x84, 0xf3, 0xff, 0xff, //0x00000f39 jmp          LBB0_38
	//0x00000f3e LBB0_216
	0x44, 0x89, 0x65, 0x98, //0x00000f3e movl         %r12d, $-104(%rbp)
	0x41, 0x89, 0xd4, //0x00000f42 movl         %edx, %r12d
	0x48, 0x89, 0x4d, 0xa8, //0x00000f45 movq         %rcx, $-88(%rbp)
	0xe9, 0x24, 0x00, 0x00, 0x00, //0x00000f49 jmp          LBB0_220
	//0x00000f4e LBB0_217
	0x48, 0x8b, 0x45, 0xd0, //0x00000f4e movq         $-48(%rbp), %rax
	0x48, 0x03, 0x45, 0xb0, //0x00000f52 addq         $-80(%rbp), %rax
	0x48, 0x29, 0xc8, //0x00000f56 subq         %rcx, %rax
	0x48, 0xf7, 0xd2, //0x00000f59 notq         %rdx
	0x48, 0x01, 0xc2, //0x00000f5c addq         %rax, %rdx
	0xe9, 0x4b, 0xfa, 0xff, 0xff, //0x00000f5f jmp          LBB0_134
	//0x00000f64 LBB0_218
	0xc7, 0x45, 0x98, 0x01, 0x00, 0x00, 0x00, //0x00000f64 movl         $1, $-104(%rbp)
	0x41, 0x89, 0xd4, //0x00000f6b movl         %edx, %r12d
	//0x00000f6e LBB0_219
	0x4c, 0x89, 0x7d, 0xa8, //0x00000f6e movq         %r15, $-88(%rbp)
	//0x00000f72 LBB0_220
	0x80, 0x7d, 0x90, 0x00, //0x00000f72 cmpb         $0, $-112(%rbp)
	0x48, 0x89, 0x7d, 0xb8, //0x00000f76 movq         %rdi, $-72(%rbp)
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00000f7a je           LBB0_225
	0x45, 0x85, 0xe4, //0x00000f80 testl        %r12d, %r12d
	0x0f, 0x85, 0x25, 0x00, 0x00, 0x00, //0x00000f83 jne          LBB0_224
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00000f89 movabsq      $-9223372036854775808, %rax
	0x49, 0x63, 0xc8, //0x00000f93 movslq       %r8d, %rcx
	0x4d, 0x85, 0xd2, //0x00000f96 testq        %r10, %r10
	0x0f, 0x89, 0xe6, 0x01, 0x00, 0x00, //0x00000f99 jns          LBB0_246
	0x4c, 0x89, 0xd2, //0x00000f9f movq         %r10, %rdx
	0x48, 0x21, 0xca, //0x00000fa2 andq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x00000fa5 cmpq         %rax, %rdx
	0x0f, 0x84, 0xd7, 0x01, 0x00, 0x00, //0x00000fa8 je           LBB0_246
	//0x00000fae LBB0_224
	0x49, 0xc7, 0x45, 0x00, 0x08, 0x00, 0x00, 0x00, //0x00000fae movq         $8, (%r13)
	//0x00000fb6 LBB0_225
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00000fb6 movabsq      $-9223372036854775808, %rdi
	0x49, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00000fc0 movabsq      $4503599627370495, %r15
	0x49, 0x8d, 0x4f, 0x01, //0x00000fca leaq         $1(%r15), %rcx
	0x49, 0x39, 0xca, //0x00000fce cmpq         %rcx, %r10
	0x0f, 0x83, 0xcb, 0x00, 0x00, 0x00, //0x00000fd1 jae          LBB0_236
	0x66, 0x49, 0x0f, 0x6e, 0xc2, //0x00000fd7 movq         %r10, %xmm0
	0x66, 0x0f, 0x62, 0x05, 0xbc, 0xf0, 0xff, 0xff, //0x00000fdc punpckldq    $-3908(%rip), %xmm0  /* LCPI0_10+0(%rip) */
	0x66, 0x0f, 0x5c, 0x05, 0xc4, 0xf0, 0xff, 0xff, //0x00000fe4 subpd        $-3900(%rip), %xmm0  /* LCPI0_11+0(%rip) */
	0x66, 0x0f, 0x28, 0xc8, //0x00000fec movapd       %xmm0, %xmm1
	0x66, 0x0f, 0x15, 0xc8, //0x00000ff0 unpckhpd     %xmm0, %xmm1
	0xf2, 0x0f, 0x58, 0xc8, //0x00000ff4 addsd        %xmm0, %xmm1
	0x41, 0xc1, 0xe8, 0x1f, //0x00000ff8 shrl         $31, %r8d
	0x49, 0xc1, 0xe0, 0x3f, //0x00000ffc shlq         $63, %r8
	0x66, 0x49, 0x0f, 0x6e, 0xc0, //0x00001000 movq         %r8, %xmm0
	0x66, 0x0f, 0xeb, 0xc1, //0x00001005 por          %xmm1, %xmm0
	0x4d, 0x85, 0xd2, //0x00001009 testq        %r10, %r10
	0x0f, 0x84, 0x4a, 0x1c, 0x00, 0x00, //0x0000100c je           LBB0_653
	0x45, 0x85, 0xe4, //0x00001012 testl        %r12d, %r12d
	0x0f, 0x84, 0x41, 0x1c, 0x00, 0x00, //0x00001015 je           LBB0_653
	0x41, 0x8d, 0x44, 0x24, 0xff, //0x0000101b leal         $-1(%r12), %eax
	0x83, 0xf8, 0x24, //0x00001020 cmpl         $36, %eax
	0x0f, 0x87, 0x5a, 0x00, 0x00, 0x00, //0x00001023 ja           LBB0_234
	0x48, 0x89, 0x4d, 0x88, //0x00001029 movq         %rcx, $-120(%rbp)
	0x44, 0x89, 0xe0, //0x0000102d movl         %r12d, %eax
	0x41, 0x83, 0xfc, 0x17, //0x00001030 cmpl         $23, %r12d
	0x0f, 0x82, 0x16, 0x00, 0x00, 0x00, //0x00001034 jb           LBB0_231
	0x41, 0x8d, 0x44, 0x24, 0xea, //0x0000103a leal         $-22(%r12), %eax
	0x48, 0x8d, 0x0d, 0x9a, 0x22, 0x00, 0x00, //0x0000103f leaq         $8858(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xf2, 0x0f, 0x59, 0x04, 0xc1, //0x00001046 mulsd        (%rcx,%rax,8), %xmm0
	0xb8, 0x16, 0x00, 0x00, 0x00, //0x0000104b movl         $22, %eax
	//0x00001050 LBB0_231
	0x66, 0x0f, 0x2e, 0x05, 0x68, 0xf0, 0xff, 0xff, //0x00001050 ucomisd      $-3992(%rip), %xmm0  /* LCPI0_12+0(%rip) */
	0x0f, 0x87, 0x5b, 0x00, 0x00, 0x00, //0x00001058 ja           LBB0_237
	0xf2, 0x0f, 0x10, 0x0d, 0x62, 0xf0, 0xff, 0xff, //0x0000105e movsd        $-3998(%rip), %xmm1  /* LCPI0_13+0(%rip) */
	0x66, 0x0f, 0x2e, 0xc8, //0x00001066 ucomisd      %xmm0, %xmm1
	0x0f, 0x87, 0x49, 0x00, 0x00, 0x00, //0x0000106a ja           LBB0_237
	0x89, 0xc0, //0x00001070 movl         %eax, %eax
	0x48, 0x8d, 0x0d, 0x67, 0x22, 0x00, 0x00, //0x00001072 leaq         $8807(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xf2, 0x0f, 0x59, 0x04, 0xc1, //0x00001079 mulsd        (%rcx,%rax,8), %xmm0
	0xe9, 0xd9, 0x1b, 0x00, 0x00, //0x0000107e jmp          LBB0_653
	//0x00001083 LBB0_234
	0x41, 0x83, 0xfc, 0xea, //0x00001083 cmpl         $-22, %r12d
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x00001087 jb           LBB0_236
	0x41, 0xf7, 0xdc, //0x0000108d negl         %r12d
	0x48, 0x8d, 0x05, 0x49, 0x22, 0x00, 0x00, //0x00001090 leaq         $8777(%rip), %rax  /* _P10_TAB+0(%rip) */
	0xf2, 0x42, 0x0f, 0x5e, 0x04, 0xe0, //0x00001097 divsd        (%rax,%r12,8), %xmm0
	0xe9, 0xba, 0x1b, 0x00, 0x00, //0x0000109d jmp          LBB0_653
	//0x000010a2 LBB0_236
	0x48, 0x89, 0x4d, 0x88, //0x000010a2 movq         %rcx, $-120(%rbp)
	0x41, 0x8d, 0x84, 0x24, 0xa4, 0xfe, 0xff, 0xff, //0x000010a6 leal         $-348(%r12), %eax
	0x3d, 0x48, 0xfd, 0xff, 0xff, //0x000010ae cmpl         $-696, %eax
	0x0f, 0x82, 0x27, 0x02, 0x00, 0x00, //0x000010b3 jb           LBB0_265
	//0x000010b9 LBB0_237
	0x4d, 0x85, 0xd2, //0x000010b9 testq        %r10, %r10
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000010bc je           LBB0_239
	0x49, 0x0f, 0xbd, 0xca, //0x000010c2 bsrq         %r10, %rcx
	0x48, 0x83, 0xf1, 0x3f, //0x000010c6 xorq         $63, %rcx
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x000010ca jmp          LBB0_240
	//0x000010cf LBB0_239
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000010cf movl         $64, %ecx
	//0x000010d4 LBB0_240
	0x4d, 0x89, 0xd0, //0x000010d4 movq         %r10, %r8
	0x49, 0x89, 0xcb, //0x000010d7 movq         %rcx, %r11
	0x49, 0xd3, 0xe0, //0x000010da shlq         %cl, %r8
	0x41, 0x8d, 0x84, 0x24, 0x5c, 0x01, 0x00, 0x00, //0x000010dd leal         $348(%r12), %eax
	0x48, 0xc1, 0xe0, 0x04, //0x000010e5 shlq         $4, %rax
	0x48, 0x8d, 0x0d, 0xb0, 0x22, 0x00, 0x00, //0x000010e9 leaq         $8880(%rip), %rcx  /* _POW10_M128_TAB+0(%rip) */
	0x48, 0x89, 0x45, 0x90, //0x000010f0 movq         %rax, $-112(%rbp)
	0x48, 0x8b, 0x44, 0x08, 0x08, //0x000010f4 movq         $8(%rax,%rcx), %rax
	0x48, 0x89, 0x45, 0xa0, //0x000010f9 movq         %rax, $-96(%rbp)
	0x49, 0xf7, 0xe0, //0x000010fd mulq         %r8
	0x48, 0x89, 0xc3, //0x00001100 movq         %rax, %rbx
	0x48, 0x89, 0xd6, //0x00001103 movq         %rdx, %rsi
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x00001106 andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x0000110c cmpq         $511, %rdx
	0x0f, 0x85, 0x54, 0x01, 0x00, 0x00, //0x00001113 jne          LBB0_261
	0x4c, 0x89, 0xc1, //0x00001119 movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x0000111c notq         %rcx
	0x48, 0x39, 0xcb, //0x0000111f cmpq         %rcx, %rbx
	0x0f, 0x86, 0x45, 0x01, 0x00, 0x00, //0x00001122 jbe          LBB0_261
	0x4c, 0x89, 0xc0, //0x00001128 movq         %r8, %rax
	0x48, 0x8b, 0x55, 0x90, //0x0000112b movq         $-112(%rbp), %rdx
	0x48, 0x89, 0xdf, //0x0000112f movq         %rbx, %rdi
	0x48, 0x8d, 0x1d, 0x67, 0x22, 0x00, 0x00, //0x00001132 leaq         $8807(%rip), %rbx  /* _POW10_M128_TAB+0(%rip) */
	0x48, 0xf7, 0x24, 0x1a, //0x00001139 mulq         (%rdx,%rbx)
	0x48, 0x89, 0xfb, //0x0000113d movq         %rdi, %rbx
	0x48, 0x01, 0xd3, //0x00001140 addq         %rdx, %rbx
	0x48, 0x83, 0xd6, 0x00, //0x00001143 adcq         $0, %rsi
	0x89, 0xf2, //0x00001147 movl         %esi, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x00001149 andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x0000114f cmpq         $511, %rdx
	0x0f, 0x85, 0x07, 0x01, 0x00, 0x00, //0x00001156 jne          LBB0_260
	0x48, 0x83, 0xfb, 0xff, //0x0000115c cmpq         $-1, %rbx
	0x0f, 0x85, 0xfd, 0x00, 0x00, 0x00, //0x00001160 jne          LBB0_260
	0x48, 0x39, 0xc8, //0x00001166 cmpq         %rcx, %rax
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001169 movabsq      $-9223372036854775808, %rdi
	0x0f, 0x87, 0x67, 0x01, 0x00, 0x00, //0x00001173 ja           LBB0_265
	0xe9, 0xef, 0x00, 0x00, 0x00, //0x00001179 jmp          LBB0_261
	//0x0000117e LBB0_245
	0x89, 0xc0, //0x0000117e movl         %eax, %eax
	0xe9, 0x6b, 0x00, 0x00, 0x00, //0x00001180 jmp          LBB0_252
	//0x00001185 LBB0_246
	0x66, 0x49, 0x0f, 0x6e, 0xc2, //0x00001185 movq         %r10, %xmm0
	0x4c, 0x0f, 0xaf, 0xd1, //0x0000118a imulq        %rcx, %r10
	0x4d, 0x89, 0x55, 0x10, //0x0000118e movq         %r10, $16(%r13)
	0x66, 0x0f, 0x62, 0x05, 0x06, 0xef, 0xff, 0xff, //0x00001192 punpckldq    $-4346(%rip), %xmm0  /* LCPI0_10+0(%rip) */
	0x66, 0x0f, 0x5c, 0x05, 0x0e, 0xef, 0xff, 0xff, //0x0000119a subpd        $-4338(%rip), %xmm0  /* LCPI0_11+0(%rip) */
	0x66, 0x0f, 0x28, 0xc8, //0x000011a2 movapd       %xmm0, %xmm1
	0x66, 0x0f, 0x15, 0xc8, //0x000011a6 unpckhpd     %xmm0, %xmm1
	0xf2, 0x0f, 0x58, 0xc8, //0x000011aa addsd        %xmm0, %xmm1
	0x48, 0x21, 0xc8, //0x000011ae andq         %rcx, %rax
	0x66, 0x48, 0x0f, 0x7e, 0xc9, //0x000011b1 movq         %xmm1, %rcx
	0x48, 0x09, 0xc1, //0x000011b6 orq          %rax, %rcx
	0x49, 0x89, 0x4d, 0x08, //0x000011b9 movq         %rcx, $8(%r13)
	0x4c, 0x8b, 0x65, 0xa8, //0x000011bd movq         $-88(%rbp), %r12
	0xe9, 0xfc, 0xf0, 0xff, 0xff, //0x000011c1 jmp          LBB0_38
	//0x000011c6 LBB0_247
	0x49, 0x83, 0xc1, 0x03, //0x000011c6 addq         $3, %r9
	0xe9, 0xec, 0xfb, 0xff, 0xff, //0x000011ca jmp          LBB0_185
	//0x000011cf LBB0_248
	0x41, 0x0f, 0xbc, 0xc4, //0x000011cf bsfl         %r12d, %eax
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000011d3 jmp          LBB0_250
	//0x000011d8 LBB0_249
	0x0f, 0xbc, 0xc6, //0x000011d8 bsfl         %esi, %eax
	//0x000011db LBB0_250
	0x48, 0xf7, 0xd2, //0x000011db notq         %rdx
	0x48, 0x29, 0xc2, //0x000011de subq         %rax, %rdx
	0x49, 0x89, 0xfc, //0x000011e1 movq         %rdi, %r12
	0x48, 0x8b, 0x7d, 0xb8, //0x000011e4 movq         $-72(%rbp), %rdi
	0xe9, 0xc2, 0xf7, 0xff, 0xff, //0x000011e8 jmp          LBB0_134
	//0x000011ed LBB0_251
	0x0f, 0xbc, 0xc6, //0x000011ed bsfl         %esi, %eax
	//0x000011f0 LBB0_252
	0x48, 0xf7, 0xd2, //0x000011f0 notq         %rdx
	0x48, 0x29, 0xc2, //0x000011f3 subq         %rax, %rdx
	0x48, 0x8b, 0x7d, 0xb8, //0x000011f6 movq         $-72(%rbp), %rdi
	0xe9, 0xb0, 0xf7, 0xff, 0xff, //0x000011fa jmp          LBB0_134
	//0x000011ff LBB0_253
	0x48, 0x89, 0xc1, //0x000011ff movq         %rax, %rcx
	//0x00001202 LBB0_254
	0x8d, 0x46, 0xc6, //0x00001202 leal         $-58(%rsi), %eax
	0x3c, 0xf6, //0x00001205 cmpb         $-10, %al
	0x0f, 0x82, 0x9d, 0xf0, 0xff, 0xff, //0x00001207 jb           LBB0_36
	0x89, 0x5d, 0xa8, //0x0000120d movl         %ebx, $-88(%rbp)
	0x44, 0x89, 0x65, 0x98, //0x00001210 movl         %r12d, $-104(%rbp)
	0x45, 0x31, 0xe4, //0x00001214 xorl         %r12d, %r12d
	0x4c, 0x39, 0xf9, //0x00001217 cmpq         %r15, %rcx
	0x0f, 0x83, 0x1e, 0x04, 0x00, 0x00, //0x0000121a jae          LBB0_301
	0x4d, 0x8d, 0x5f, 0xff, //0x00001220 leaq         $-1(%r15), %r11
	0x45, 0x31, 0xe4, //0x00001224 xorl         %r12d, %r12d
	//0x00001227 LBB0_257
	0x44, 0x89, 0xe3, //0x00001227 movl         %r12d, %ebx
	0x40, 0x0f, 0xb6, 0xf6, //0x0000122a movzbl       %sil, %esi
	0x41, 0x81, 0xfc, 0x10, 0x27, 0x00, 0x00, //0x0000122e cmpl         $10000, %r12d
	0x8d, 0x04, 0x9b, //0x00001235 leal         (%rbx,%rbx,4), %eax
	0x44, 0x8d, 0x64, 0x46, 0xd0, //0x00001238 leal         $-48(%rsi,%rax,2), %r12d
	0x44, 0x0f, 0x4d, 0xe3, //0x0000123d cmovgel      %ebx, %r12d
	0x49, 0x39, 0xcb, //0x00001241 cmpq         %rcx, %r11
	0x0f, 0x84, 0xf1, 0x03, 0x00, 0x00, //0x00001244 je           LBB0_300
	0x0f, 0xb6, 0x74, 0x0f, 0x01, //0x0000124a movzbl       $1(%rdi,%rcx), %esi
	0x8d, 0x46, 0xd0, //0x0000124f leal         $-48(%rsi), %eax
	0x48, 0x83, 0xc1, 0x01, //0x00001252 addq         $1, %rcx
	0x3c, 0x0a, //0x00001256 cmpb         $10, %al
	0x0f, 0x82, 0xc9, 0xff, 0xff, 0xff, //0x00001258 jb           LBB0_257
	0xe9, 0xdb, 0x03, 0x00, 0x00, //0x0000125e jmp          LBB0_301
	//0x00001263 LBB0_260
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001263 movabsq      $-9223372036854775808, %rdi
	//0x0000126d LBB0_261
	0x48, 0x89, 0xf0, //0x0000126d movq         %rsi, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x00001270 shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x00001274 leal         $9(%rax), %ecx
	0x48, 0xd3, 0xee, //0x00001277 shrq         %cl, %rsi
	0x48, 0x85, 0xdb, //0x0000127a testq        %rbx, %rbx
	0x0f, 0x85, 0x17, 0x00, 0x00, 0x00, //0x0000127d jne          LBB0_264
	0x48, 0x85, 0xd2, //0x00001283 testq        %rdx, %rdx
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00001286 jne          LBB0_264
	0x89, 0xf1, //0x0000128c movl         %esi, %ecx
	0x83, 0xe1, 0x03, //0x0000128e andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x00001291 cmpl         $1, %ecx
	0x0f, 0x84, 0x46, 0x00, 0x00, 0x00, //0x00001294 je           LBB0_265
	//0x0000129a LBB0_264
	0x41, 0x69, 0xcc, 0x6a, 0x52, 0x03, 0x00, //0x0000129a imull        $217706, %r12d, %ecx
	0xc1, 0xf9, 0x10, //0x000012a1 sarl         $16, %ecx
	0x81, 0xc1, 0x3f, 0x04, 0x00, 0x00, //0x000012a4 addl         $1087, %ecx
	0x4c, 0x63, 0xc1, //0x000012aa movslq       %ecx, %r8
	0x4c, 0x89, 0xc3, //0x000012ad movq         %r8, %rbx
	0x4c, 0x29, 0xdb, //0x000012b0 subq         %r11, %rbx
	0x89, 0xf2, //0x000012b3 movl         %esi, %edx
	0x83, 0xe2, 0x01, //0x000012b5 andl         $1, %edx
	0x48, 0x01, 0xf2, //0x000012b8 addq         %rsi, %rdx
	0x48, 0x89, 0xd1, //0x000012bb movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x36, //0x000012be shrq         $54, %rcx
	0x48, 0x01, 0xc3, //0x000012c2 addq         %rax, %rbx
	0x48, 0x83, 0xf9, 0x01, //0x000012c5 cmpq         $1, %rcx
	0x48, 0x83, 0xdb, 0x00, //0x000012c9 sbbq         $0, %rbx
	0x48, 0x8d, 0x83, 0x01, 0xf8, 0xff, 0xff, //0x000012cd leaq         $-2047(%rbx), %rax
	0x48, 0x3d, 0x02, 0xf8, 0xff, 0xff, //0x000012d4 cmpq         $-2046, %rax
	0x0f, 0x83, 0x4e, 0x00, 0x00, 0x00, //0x000012da jae          LBB0_270
	//0x000012e0 LBB0_265
	0x48, 0x8b, 0x5d, 0xa8, //0x000012e0 movq         $-88(%rbp), %rbx
	0x49, 0x89, 0xdb, //0x000012e4 movq         %rbx, %r11
	0x4d, 0x29, 0xcb, //0x000012e7 subq         %r9, %r11
	0x4c, 0x8b, 0x65, 0xb0, //0x000012ea movq         $-80(%rbp), %r12
	0x4d, 0x85, 0xe4, //0x000012ee testq        %r12, %r12
	0x48, 0x8b, 0x4d, 0xd0, //0x000012f1 movq         $-48(%rbp), %rcx
	0x0f, 0x84, 0x71, 0x03, 0x00, 0x00, //0x000012f5 je           LBB0_304
	0x41, 0xc6, 0x06, 0x00, //0x000012fb movb         $0, (%r14)
	0x49, 0x83, 0xfc, 0x01, //0x000012ff cmpq         $1, %r12
	0x0f, 0x84, 0x63, 0x03, 0x00, 0x00, //0x00001303 je           LBB0_304
	0x4d, 0x8d, 0x44, 0x24, 0xff, //0x00001309 leaq         $-1(%r12), %r8
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000130e movl         $1, %eax
	0x49, 0x83, 0xf8, 0x08, //0x00001313 cmpq         $8, %r8
	0x0f, 0x82, 0x3d, 0x03, 0x00, 0x00, //0x00001317 jb           LBB0_303
	0x49, 0x83, 0xf8, 0x20, //0x0000131d cmpq         $32, %r8
	0x0f, 0x83, 0xe1, 0x00, 0x00, 0x00, //0x00001321 jae          LBB0_278
	0x31, 0xd2, //0x00001327 xorl         %edx, %edx
	0xe9, 0xab, 0x01, 0x00, 0x00, //0x00001329 jmp          LBB0_287
	//0x0000132e LBB0_270
	0x48, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, //0x0000132e movabsq      $18014398509481984, %rsi
	0x48, 0x39, 0xf2, //0x00001338 cmpq         %rsi, %rdx
	0xb1, 0x02, //0x0000133b movb         $2, %cl
	0x80, 0xd9, 0x00, //0x0000133d sbbb         $0, %cl
	0x48, 0xd3, 0xea, //0x00001340 shrq         %cl, %rdx
	0x48, 0xc1, 0xe3, 0x34, //0x00001343 shlq         $52, %rbx
	0x4c, 0x21, 0xfa, //0x00001347 andq         %r15, %rdx
	0x48, 0x09, 0xda, //0x0000134a orq          %rbx, %rdx
	0x48, 0x89, 0xd0, //0x0000134d movq         %rdx, %rax
	0x48, 0x09, 0xf8, //0x00001350 orq          %rdi, %rax
	0x80, 0x7d, 0xc8, 0x2d, //0x00001353 cmpb         $45, $-56(%rbp)
	0x48, 0x0f, 0x45, 0xc2, //0x00001357 cmovneq      %rdx, %rax
	0x66, 0x48, 0x0f, 0x6e, 0xc0, //0x0000135b movq         %rax, %xmm0
	0x83, 0x7d, 0x98, 0x00, //0x00001360 cmpl         $0, $-104(%rbp)
	0x0f, 0x84, 0xf2, 0x18, 0x00, 0x00, //0x00001364 je           LBB0_653
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x0000136a movl         $64, %ecx
	0x49, 0xff, 0xc2, //0x0000136f incq         %r10
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00001372 je           LBB0_273
	0x49, 0x0f, 0xbd, 0xca, //0x00001378 bsrq         %r10, %rcx
	0x48, 0x83, 0xf1, 0x3f, //0x0000137c xorq         $63, %rcx
	//0x00001380 LBB0_273
	0x4d, 0x89, 0xfb, //0x00001380 movq         %r15, %r11
	0x49, 0x89, 0xcf, //0x00001383 movq         %rcx, %r15
	0x49, 0xd3, 0xe2, //0x00001386 shlq         %cl, %r10
	0x48, 0x8b, 0x45, 0xa0, //0x00001389 movq         $-96(%rbp), %rax
	0x49, 0xf7, 0xe2, //0x0000138d mulq         %r10
	0x49, 0x89, 0xc4, //0x00001390 movq         %rax, %r12
	0x48, 0x89, 0xd3, //0x00001393 movq         %rdx, %rbx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x00001396 andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x0000139c cmpq         $511, %rdx
	0x0f, 0x85, 0xbf, 0x13, 0x00, 0x00, //0x000013a3 jne          LBB0_576
	0x4c, 0x89, 0xd1, //0x000013a9 movq         %r10, %rcx
	0x48, 0xf7, 0xd1, //0x000013ac notq         %rcx
	0x49, 0x39, 0xcc, //0x000013af cmpq         %rcx, %r12
	0x0f, 0x86, 0xb0, 0x13, 0x00, 0x00, //0x000013b2 jbe          LBB0_576
	0x4c, 0x89, 0xd0, //0x000013b8 movq         %r10, %rax
	0x48, 0x8b, 0x55, 0x90, //0x000013bb movq         $-112(%rbp), %rdx
	0x48, 0x8d, 0x35, 0xda, 0x1f, 0x00, 0x00, //0x000013bf leaq         $8154(%rip), %rsi  /* _POW10_M128_TAB+0(%rip) */
	0x48, 0xf7, 0x24, 0x32, //0x000013c6 mulq         (%rdx,%rsi)
	0x49, 0x01, 0xd4, //0x000013ca addq         %rdx, %r12
	0x48, 0x83, 0xd3, 0x00, //0x000013cd adcq         $0, %rbx
	0x89, 0xda, //0x000013d1 movl         %ebx, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x000013d3 andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x000013d9 cmpq         $511, %rdx
	0x0f, 0x85, 0x78, 0x13, 0x00, 0x00, //0x000013e0 jne          LBB0_575
	0x49, 0x83, 0xfc, 0xff, //0x000013e6 cmpq         $-1, %r12
	0x0f, 0x85, 0x6e, 0x13, 0x00, 0x00, //0x000013ea jne          LBB0_575
	0x48, 0x39, 0xc8, //0x000013f0 cmpq         %rcx, %rax
	0x48, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, //0x000013f3 movabsq      $18014398509481984, %rsi
	0x0f, 0x87, 0xdd, 0xfe, 0xff, 0xff, //0x000013fd ja           LBB0_265
	0xe9, 0x60, 0x13, 0x00, 0x00, //0x00001403 jmp          LBB0_576
	//0x00001408 LBB0_278
	0x49, 0x89, 0xda, //0x00001408 movq         %rbx, %r10
	0x4c, 0x89, 0xc2, //0x0000140b movq         %r8, %rdx
	0x48, 0x83, 0xe2, 0xe0, //0x0000140e andq         $-32, %rdx
	0x48, 0x8d, 0x72, 0xe0, //0x00001412 leaq         $-32(%rdx), %rsi
	0x48, 0x89, 0xf0, //0x00001416 movq         %rsi, %rax
	0x48, 0xc1, 0xe8, 0x05, //0x00001419 shrq         $5, %rax
	0x48, 0x83, 0xc0, 0x01, //0x0000141d addq         $1, %rax
	0x89, 0xc3, //0x00001421 movl         %eax, %ebx
	0x83, 0xe3, 0x03, //0x00001423 andl         $3, %ebx
	0x48, 0x83, 0xfe, 0x60, //0x00001426 cmpq         $96, %rsi
	0x0f, 0x83, 0x07, 0x00, 0x00, 0x00, //0x0000142a jae          LBB0_280
	0x31, 0xf6, //0x00001430 xorl         %esi, %esi
	0xe9, 0x5d, 0x00, 0x00, 0x00, //0x00001432 jmp          LBB0_282
	//0x00001437 LBB0_280
	0x48, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, //0x00001437 movabsq      $1152921504606846976, %rsi
	0x48, 0x83, 0xc6, 0xfc, //0x00001441 addq         $-4, %rsi
	0x48, 0x21, 0xf0, //0x00001445 andq         %rsi, %rax
	0x31, 0xf6, //0x00001448 xorl         %esi, %esi
	0x66, 0x0f, 0xef, 0xc0, //0x0000144a pxor         %xmm0, %xmm0
	//0x0000144e LBB0_281
	0xf3, 0x41, 0x0f, 0x7f, 0x44, 0x36, 0x01, //0x0000144e movdqu       %xmm0, $1(%r14,%rsi)
	0xf3, 0x41, 0x0f, 0x7f, 0x44, 0x36, 0x11, //0x00001455 movdqu       %xmm0, $17(%r14,%rsi)
	0xf3, 0x41, 0x0f, 0x7f, 0x44, 0x36, 0x21, //0x0000145c movdqu       %xmm0, $33(%r14,%rsi)
	0xf3, 0x41, 0x0f, 0x7f, 0x44, 0x36, 0x31, //0x00001463 movdqu       %xmm0, $49(%r14,%rsi)
	0xf3, 0x41, 0x0f, 0x7f, 0x44, 0x36, 0x41, //0x0000146a movdqu       %xmm0, $65(%r14,%rsi)
	0xf3, 0x41, 0x0f, 0x7f, 0x44, 0x36, 0x51, //0x00001471 movdqu       %xmm0, $81(%r14,%rsi)
	0xf3, 0x41, 0x0f, 0x7f, 0x44, 0x36, 0x61, //0x00001478 movdqu       %xmm0, $97(%r14,%rsi)
	0xf3, 0x41, 0x0f, 0x7f, 0x44, 0x36, 0x71, //0x0000147f movdqu       %xmm0, $113(%r14,%rsi)
	0x48, 0x83, 0xee, 0x80, //0x00001486 subq         $-128, %rsi
	0x48, 0x83, 0xc0, 0xfc, //0x0000148a addq         $-4, %rax
	0x0f, 0x85, 0xba, 0xff, 0xff, 0xff, //0x0000148e jne          LBB0_281
	//0x00001494 LBB0_282
	0x48, 0x85, 0xdb, //0x00001494 testq        %rbx, %rbx
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00001497 je           LBB0_285
	0x66, 0x0f, 0xef, 0xc0, //0x0000149d pxor         %xmm0, %xmm0
	//0x000014a1 LBB0_284
	0x48, 0x89, 0xf0, //0x000014a1 movq         %rsi, %rax
	0x48, 0x83, 0xc8, 0x01, //0x000014a4 orq          $1, %rax
	0xf3, 0x41, 0x0f, 0x7f, 0x04, 0x06, //0x000014a8 movdqu       %xmm0, (%r14,%rax)
	0xf3, 0x41, 0x0f, 0x7f, 0x44, 0x06, 0x10, //0x000014ae movdqu       %xmm0, $16(%r14,%rax)
	0x48, 0x83, 0xc6, 0x20, //0x000014b5 addq         $32, %rsi
	0x48, 0x83, 0xc3, 0xff, //0x000014b9 addq         $-1, %rbx
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x000014bd jne          LBB0_284
	//0x000014c3 LBB0_285
	0x49, 0x39, 0xd0, //0x000014c3 cmpq         %rdx, %r8
	0x4c, 0x89, 0xd3, //0x000014c6 movq         %r10, %rbx
	0x0f, 0x84, 0x9d, 0x01, 0x00, 0x00, //0x000014c9 je           LBB0_304
	0x41, 0xf6, 0xc0, 0x18, //0x000014cf testb        $24, %r8b
	0x0f, 0x84, 0x7a, 0x01, 0x00, 0x00, //0x000014d3 je           LBB0_302
	//0x000014d9 LBB0_287
	0x4c, 0x89, 0xc6, //0x000014d9 movq         %r8, %rsi
	0x48, 0x83, 0xe6, 0xf8, //0x000014dc andq         $-8, %rsi
	0x48, 0x8d, 0x46, 0x01, //0x000014e0 leaq         $1(%rsi), %rax
	//0x000014e4 LBB0_288
	0x49, 0xc7, 0x44, 0x16, 0x01, 0x00, 0x00, 0x00, 0x00, //0x000014e4 movq         $0, $1(%r14,%rdx)
	0x48, 0x83, 0xc2, 0x08, //0x000014ed addq         $8, %rdx
	0x48, 0x39, 0xd6, //0x000014f1 cmpq         %rdx, %rsi
	0x0f, 0x85, 0xea, 0xff, 0xff, 0xff, //0x000014f4 jne          LBB0_288
	0x49, 0x39, 0xf0, //0x000014fa cmpq         %rsi, %r8
	0x0f, 0x85, 0x57, 0x01, 0x00, 0x00, //0x000014fd jne          LBB0_303
	0xe9, 0x64, 0x01, 0x00, 0x00, //0x00001503 jmp          LBB0_304
	//0x00001508 LBB0_290
	0x49, 0x01, 0xfc, //0x00001508 addq         %rdi, %r12
	0x49, 0x83, 0xfb, 0x20, //0x0000150b cmpq         $32, %r11
	0x0f, 0x82, 0xfd, 0x17, 0x00, 0x00, //0x0000150f jb           LBB0_664
	//0x00001515 LBB0_291
	0xf3, 0x41, 0x0f, 0x6f, 0x04, 0x24, //0x00001515 movdqu       (%r12), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x4c, 0x24, 0x10, //0x0000151b movdqu       $16(%r12), %xmm1
	0xf3, 0x0f, 0x6f, 0x15, 0xd6, 0xea, 0xff, 0xff, //0x00001522 movdqu       $-5418(%rip), %xmm2  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x1d, 0xde, 0xea, 0xff, 0xff, //0x0000152a movdqu       $-5410(%rip), %xmm3  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0x6f, 0xe0, //0x00001532 movdqa       %xmm0, %xmm4
	0x66, 0x0f, 0x74, 0xe2, //0x00001536 pcmpeqb      %xmm2, %xmm4
	0x66, 0x0f, 0xd7, 0xc4, //0x0000153a pmovmskb     %xmm4, %eax
	0x66, 0x0f, 0x74, 0xd1, //0x0000153e pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00001542 pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x74, 0xc3, //0x00001546 pcmpeqb      %xmm3, %xmm0
	0x66, 0x0f, 0xd7, 0xc8, //0x0000154a pmovmskb     %xmm0, %ecx
	0x66, 0x0f, 0x74, 0xcb, //0x0000154e pcmpeqb      %xmm3, %xmm1
	0x66, 0x0f, 0xd7, 0xf1, //0x00001552 pmovmskb     %xmm1, %esi
	0x48, 0xc1, 0xe2, 0x10, //0x00001556 shlq         $16, %rdx
	0x48, 0x09, 0xd0, //0x0000155a orq          %rdx, %rax
	0x48, 0xc1, 0xe6, 0x10, //0x0000155d shlq         $16, %rsi
	0x48, 0x09, 0xf1, //0x00001561 orq          %rsi, %rcx
	0x0f, 0x85, 0xe2, 0x17, 0x00, 0x00, //0x00001564 jne          LBB0_669
	0x4d, 0x85, 0xc9, //0x0000156a testq        %r9, %r9
	0x0f, 0x85, 0xfd, 0x17, 0x00, 0x00, //0x0000156d jne          LBB0_671
	0x45, 0x31, 0xc9, //0x00001573 xorl         %r9d, %r9d
	0x48, 0x85, 0xc0, //0x00001576 testq        %rax, %rax
	0x0f, 0x84, 0x2f, 0x18, 0x00, 0x00, //0x00001579 je           LBB0_673
	//0x0000157f LBB0_294
	0x48, 0x0f, 0xbc, 0xc0, //0x0000157f bsfq         %rax, %rax
	0x49, 0x29, 0xfc, //0x00001583 subq         %rdi, %r12
	0x49, 0x01, 0xc4, //0x00001586 addq         %rax, %r12
	0x49, 0x83, 0xc4, 0x01, //0x00001589 addq         $1, %r12
	0xe9, 0x4c, 0xf3, 0xff, 0xff, //0x0000158d jmp          LBB0_121
	//0x00001592 LBB0_295
	0x48, 0x01, 0xfa, //0x00001592 addq         %rdi, %rdx
	0x49, 0x89, 0xfa, //0x00001595 movq         %rdi, %r10
	0x48, 0x83, 0xf9, 0x20, //0x00001598 cmpq         $32, %rcx
	0x0f, 0x82, 0x93, 0x17, 0x00, 0x00, //0x0000159c jb           LBB0_666
	//0x000015a2 LBB0_296
	0xf3, 0x0f, 0x6f, 0x02, //0x000015a2 movdqu       (%rdx), %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0x52, 0xea, 0xff, 0xff, //0x000015a6 movdqu       $-5550(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd0, //0x000015ae movdqa       %xmm0, %xmm2
	0xf3, 0x0f, 0x6f, 0x1d, 0x66, 0xea, 0xff, 0xff, //0x000015b2 movdqu       $-5530(%rip), %xmm3  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0x6f, 0xe0, //0x000015ba movdqa       %xmm0, %xmm4
	0x66, 0x0f, 0xda, 0xe3, //0x000015be pminub       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x000015c2 pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0x74, 0xc1, //0x000015c6 pcmpeqb      %xmm1, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xd8, //0x000015ca pmovmskb     %xmm0, %r11d
	0xf3, 0x0f, 0x6f, 0x42, 0x10, //0x000015cf movdqu       $16(%rdx), %xmm0
	0x66, 0x0f, 0x74, 0xc8, //0x000015d4 pcmpeqb      %xmm0, %xmm1
	0x66, 0x0f, 0xd7, 0xf9, //0x000015d8 pmovmskb     %xmm1, %edi
	0xf3, 0x0f, 0x6f, 0x0d, 0x2c, 0xea, 0xff, 0xff, //0x000015dc movdqu       $-5588(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0x74, 0xd1, //0x000015e4 pcmpeqb      %xmm1, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xf2, //0x000015e8 pmovmskb     %xmm2, %r14d
	0x66, 0x0f, 0x74, 0xc8, //0x000015ed pcmpeqb      %xmm0, %xmm1
	0x66, 0x0f, 0xd7, 0xd9, //0x000015f1 pmovmskb     %xmm1, %ebx
	0x66, 0x44, 0x0f, 0xd7, 0xc4, //0x000015f5 pmovmskb     %xmm4, %r8d
	0x66, 0x0f, 0xda, 0xd8, //0x000015fa pminub       %xmm0, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000015fe pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00001602 pmovmskb     %xmm3, %eax
	0x48, 0xc1, 0xe7, 0x10, //0x00001606 shlq         $16, %rdi
	0x49, 0x09, 0xfb, //0x0000160a orq          %rdi, %r11
	0x48, 0xc1, 0xe3, 0x10, //0x0000160d shlq         $16, %rbx
	0x49, 0x09, 0xde, //0x00001611 orq          %rbx, %r14
	0x0f, 0x85, 0x67, 0x18, 0x00, 0x00, //0x00001614 jne          LBB0_687
	0x48, 0x85, 0xf6, //0x0000161a testq        %rsi, %rsi
	0x0f, 0x85, 0x7a, 0x18, 0x00, 0x00, //0x0000161d jne          LBB0_689
	0x31, 0xff, //0x00001623 xorl         %edi, %edi
	0x48, 0xc1, 0xe0, 0x10, //0x00001625 shlq         $16, %rax
	0x4d, 0x85, 0xdb, //0x00001629 testq        %r11, %r11
	0x0f, 0x84, 0xaa, 0x18, 0x00, 0x00, //0x0000162c je           LBB0_690
	//0x00001632 LBB0_299
	0x49, 0x0f, 0xbc, 0xf3, //0x00001632 bsfq         %r11, %rsi
	0xe9, 0xa6, 0x18, 0x00, 0x00, //0x00001636 jmp          LBB0_691
	//0x0000163b LBB0_300
	0x4c, 0x89, 0xf9, //0x0000163b movq         %r15, %rcx
	//0x0000163e LBB0_301
	0x48, 0x89, 0x7d, 0xb8, //0x0000163e movq         %rdi, $-72(%rbp)
	0x44, 0x0f, 0xaf, 0x65, 0xa8, //0x00001642 imull        $-88(%rbp), %r12d
	0x41, 0x01, 0xd4, //0x00001647 addl         %edx, %r12d
	0x48, 0x89, 0x4d, 0xa8, //0x0000164a movq         %rcx, $-88(%rbp)
	0xe9, 0x63, 0xf9, 0xff, 0xff, //0x0000164e jmp          LBB0_225
	//0x00001653 LBB0_302
	0x48, 0x83, 0xca, 0x01, //0x00001653 orq          $1, %rdx
	0x48, 0x89, 0xd0, //0x00001657 movq         %rdx, %rax
	//0x0000165a LBB0_303
	0x41, 0xc6, 0x04, 0x06, 0x00, //0x0000165a movb         $0, (%r14,%rax)
	0x48, 0x83, 0xc0, 0x01, //0x0000165f addq         $1, %rax
	0x49, 0x39, 0xc4, //0x00001663 cmpq         %rax, %r12
	0x0f, 0x85, 0xee, 0xff, 0xff, 0xff, //0x00001666 jne          LBB0_303
	//0x0000166c LBB0_304
	0x8a, 0x11, //0x0000166c movb         (%rcx), %dl
	0x31, 0xc9, //0x0000166e xorl         %ecx, %ecx
	0x80, 0xfa, 0x2d, //0x00001670 cmpb         $45, %dl
	0x0f, 0x94, 0xc1, //0x00001673 sete         %cl
	0x49, 0x39, 0xcb, //0x00001676 cmpq         %rcx, %r11
	0x0f, 0x8e, 0xbf, 0x00, 0x00, 0x00, //0x00001679 jle          LBB0_317
	0x48, 0x89, 0x5d, 0xa8, //0x0000167f movq         %rbx, $-88(%rbp)
	0x88, 0x55, 0xa0, //0x00001683 movb         %dl, $-96(%rbp)
	0x4c, 0x89, 0x6d, 0xc0, //0x00001686 movq         %r13, $-64(%rbp)
	0xb0, 0x01, //0x0000168a movb         $1, %al
	0x45, 0x31, 0xed, //0x0000168c xorl         %r13d, %r13d
	0x45, 0x31, 0xd2, //0x0000168f xorl         %r10d, %r10d
	0x31, 0xdb, //0x00001692 xorl         %ebx, %ebx
	0x31, 0xf6, //0x00001694 xorl         %esi, %esi
	0x31, 0xff, //0x00001696 xorl         %edi, %edi
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x00001698 jmp          LBB0_308
	//0x0000169d LBB0_306
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x0000169d movl         $1, %esi
	0x89, 0xdf, //0x000016a2 movl         %ebx, %edi
	0x41, 0x80, 0xff, 0x2e, //0x000016a4 cmpb         $46, %r15b
	0x0f, 0x85, 0xab, 0x00, 0x00, 0x00, //0x000016a8 jne          LBB0_319
	//0x000016ae LBB0_307
	0x48, 0x83, 0xc1, 0x01, //0x000016ae addq         $1, %rcx
	0x4c, 0x39, 0xd9, //0x000016b2 cmpq         %r11, %rcx
	0x0f, 0x9c, 0xc0, //0x000016b5 setl         %al
	0x0f, 0x84, 0x93, 0x00, 0x00, 0x00, //0x000016b8 je           LBB0_318
	//0x000016be LBB0_308
	0x89, 0xf2, //0x000016be movl         %esi, %edx
	0x41, 0x89, 0xf8, //0x000016c0 movl         %edi, %r8d
	0x48, 0x8b, 0x75, 0xd0, //0x000016c3 movq         $-48(%rbp), %rsi
	0x44, 0x0f, 0xb6, 0x3c, 0x0e, //0x000016c7 movzbl       (%rsi,%rcx), %r15d
	0x41, 0x8d, 0x77, 0xd0, //0x000016cc leal         $-48(%r15), %esi
	0x40, 0x80, 0xfe, 0x09, //0x000016d0 cmpb         $9, %sil
	0x0f, 0x87, 0xc3, 0xff, 0xff, 0xff, //0x000016d4 ja           LBB0_306
	0x41, 0x80, 0xff, 0x30, //0x000016da cmpb         $48, %r15b
	0x0f, 0x85, 0x19, 0x00, 0x00, 0x00, //0x000016de jne          LBB0_312
	0x85, 0xdb, //0x000016e4 testl        %ebx, %ebx
	0x0f, 0x84, 0x42, 0x00, 0x00, 0x00, //0x000016e6 je           LBB0_316
	0x49, 0x63, 0xc5, //0x000016ec movslq       %r13d, %rax
	0x49, 0x39, 0xc4, //0x000016ef cmpq         %rax, %r12
	0x0f, 0x87, 0x11, 0x00, 0x00, 0x00, //0x000016f2 ja           LBB0_313
	0xe9, 0x14, 0x00, 0x00, 0x00, //0x000016f8 jmp          LBB0_314
	//0x000016fd LBB0_312
	0x48, 0x63, 0xc3, //0x000016fd movslq       %ebx, %rax
	0x49, 0x39, 0xc4, //0x00001700 cmpq         %rax, %r12
	0x0f, 0x86, 0x15, 0x00, 0x00, 0x00, //0x00001703 jbe          LBB0_315
	//0x00001709 LBB0_313
	0x45, 0x88, 0x3c, 0x06, //0x00001709 movb         %r15b, (%r14,%rax)
	0x41, 0x83, 0xc5, 0x01, //0x0000170d addl         $1, %r13d
	//0x00001711 LBB0_314
	0x44, 0x89, 0xc7, //0x00001711 movl         %r8d, %edi
	0x44, 0x89, 0xeb, //0x00001714 movl         %r13d, %ebx
	0x89, 0xd6, //0x00001717 movl         %edx, %esi
	0xe9, 0x90, 0xff, 0xff, 0xff, //0x00001719 jmp          LBB0_307
	//0x0000171e LBB0_315
	0x44, 0x89, 0xc7, //0x0000171e movl         %r8d, %edi
	0x41, 0xba, 0x01, 0x00, 0x00, 0x00, //0x00001721 movl         $1, %r10d
	0x89, 0xd6, //0x00001727 movl         %edx, %esi
	0xe9, 0x80, 0xff, 0xff, 0xff, //0x00001729 jmp          LBB0_307
	//0x0000172e LBB0_316
	0x41, 0x83, 0xc0, 0xff, //0x0000172e addl         $-1, %r8d
	0x31, 0xdb, //0x00001732 xorl         %ebx, %ebx
	0x44, 0x89, 0xc7, //0x00001734 movl         %r8d, %edi
	0x89, 0xd6, //0x00001737 movl         %edx, %esi
	0xe9, 0x70, 0xff, 0xff, 0xff, //0x00001739 jmp          LBB0_307
	//0x0000173e LBB0_317
	0x31, 0xdb, //0x0000173e xorl         %ebx, %ebx
	0x48, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001740 movabsq      $4503599627370495, %rsi
	0x31, 0xc9, //0x0000174a xorl         %ecx, %ecx
	0xe9, 0xf3, 0x14, 0x00, 0x00, //0x0000174c jmp          LBB0_652
	//0x00001751 LBB0_318
	0x41, 0x89, 0xf8, //0x00001751 movl         %edi, %r8d
	0x4c, 0x89, 0xd9, //0x00001754 movq         %r11, %rcx
	0x89, 0xf2, //0x00001757 movl         %esi, %edx
	//0x00001759 LBB0_319
	0x85, 0xd2, //0x00001759 testl        %edx, %edx
	0x45, 0x0f, 0x44, 0xc5, //0x0000175b cmovel       %r13d, %r8d
	0xa8, 0x01, //0x0000175f testb        $1, %al
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001761 movabsq      $-9223372036854775808, %rdi
	0x49, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x0000176b movabsq      $9218868437227405312, %r15
	0x0f, 0x84, 0x38, 0x00, 0x00, 0x00, //0x00001775 je           LBB0_325
	0x89, 0xc8, //0x0000177b movl         %ecx, %eax
	0x48, 0x8b, 0x75, 0xd0, //0x0000177d movq         $-48(%rbp), %rsi
	0x8a, 0x04, 0x06, //0x00001781 movb         (%rsi,%rax), %al
	0x0c, 0x20, //0x00001784 orb          $32, %al
	0x3c, 0x65, //0x00001786 cmpb         $101, %al
	0x0f, 0x85, 0x25, 0x00, 0x00, 0x00, //0x00001788 jne          LBB0_325
	0x89, 0xca, //0x0000178e movl         %ecx, %edx
	0x8a, 0x5c, 0x16, 0x01, //0x00001790 movb         $1(%rsi,%rdx), %bl
	0x80, 0xfb, 0x2d, //0x00001794 cmpb         $45, %bl
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00001797 je           LBB0_326
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000179d movl         $1, %eax
	0x80, 0xfb, 0x2b, //0x000017a2 cmpb         $43, %bl
	0x0f, 0x85, 0x26, 0x00, 0x00, 0x00, //0x000017a5 jne          LBB0_328
	0x83, 0xc1, 0x02, //0x000017ab addl         $2, %ecx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x000017ae jmp          LBB0_327
	//0x000017b3 LBB0_325
	0x48, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000017b3 movabsq      $4503599627370495, %rsi
	0xe9, 0x7a, 0x00, 0x00, 0x00, //0x000017bd jmp          LBB0_336
	//0x000017c2 LBB0_326
	0x83, 0xc1, 0x02, //0x000017c2 addl         $2, %ecx
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000017c5 movl         $-1, %eax
	//0x000017ca LBB0_327
	0x89, 0xca, //0x000017ca movl         %ecx, %edx
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x000017cc jmp          LBB0_329
	//0x000017d1 LBB0_328
	0x48, 0x83, 0xc2, 0x01, //0x000017d1 addq         $1, %rdx
	//0x000017d5 LBB0_329
	0x48, 0x8b, 0x5d, 0xa8, //0x000017d5 movq         $-88(%rbp), %rbx
	0x48, 0x63, 0xd2, //0x000017d9 movslq       %edx, %rdx
	0x31, 0xc9, //0x000017dc xorl         %ecx, %ecx
	0x49, 0x39, 0xd3, //0x000017de cmpq         %rdx, %r11
	0x48, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000017e1 movabsq      $4503599627370495, %rsi
	0x0f, 0x8e, 0x42, 0x00, 0x00, 0x00, //0x000017eb jle          LBB0_335
	0x49, 0x01, 0xd1, //0x000017f1 addq         %rdx, %r9
	0x31, 0xc9, //0x000017f4 xorl         %ecx, %ecx
	//0x000017f6 LBB0_331
	0x48, 0x8b, 0x55, 0xb8, //0x000017f6 movq         $-72(%rbp), %rdx
	0x42, 0x0f, 0xbe, 0x14, 0x0a, //0x000017fa movsbl       (%rdx,%r9), %edx
	0x83, 0xfa, 0x30, //0x000017ff cmpl         $48, %edx
	0x0f, 0x8c, 0x2b, 0x00, 0x00, 0x00, //0x00001802 jl           LBB0_335
	0x80, 0xfa, 0x39, //0x00001808 cmpb         $57, %dl
	0x0f, 0x87, 0x22, 0x00, 0x00, 0x00, //0x0000180b ja           LBB0_335
	0x81, 0xf9, 0x0f, 0x27, 0x00, 0x00, //0x00001811 cmpl         $9999, %ecx
	0x0f, 0x8f, 0x16, 0x00, 0x00, 0x00, //0x00001817 jg           LBB0_335
	0x8d, 0x0c, 0x89, //0x0000181d leal         (%rcx,%rcx,4), %ecx
	0x8d, 0x0c, 0x4a, //0x00001820 leal         (%rdx,%rcx,2), %ecx
	0x83, 0xc1, 0xd0, //0x00001823 addl         $-48, %ecx
	0x49, 0x83, 0xc1, 0x01, //0x00001826 addq         $1, %r9
	0x4c, 0x39, 0xcb, //0x0000182a cmpq         %r9, %rbx
	0x0f, 0x85, 0xc3, 0xff, 0xff, 0xff, //0x0000182d jne          LBB0_331
	//0x00001833 LBB0_335
	0x0f, 0xaf, 0xc8, //0x00001833 imull        %eax, %ecx
	0x44, 0x01, 0xc1, //0x00001836 addl         %r8d, %ecx
	0x41, 0x89, 0xc8, //0x00001839 movl         %ecx, %r8d
	//0x0000183c LBB0_336
	0x45, 0x85, 0xed, //0x0000183c testl        %r13d, %r13d
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x0000183f je           LBB0_339
	0x4c, 0x89, 0xc0, //0x00001845 movq         %r8, %rax
	0x31, 0xdb, //0x00001848 xorl         %ebx, %ebx
	0x3d, 0x36, 0x01, 0x00, 0x00, //0x0000184a cmpl         $310, %eax
	0x0f, 0x8e, 0x19, 0x00, 0x00, 0x00, //0x0000184f jle          LBB0_340
	0x4c, 0x89, 0xf9, //0x00001855 movq         %r15, %rcx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00001858 movq         $-64(%rbp), %r13
	0xe9, 0xe0, 0x13, 0x00, 0x00, //0x0000185c jmp          LBB0_651
	//0x00001861 LBB0_339
	0x31, 0xc9, //0x00001861 xorl         %ecx, %ecx
	0x31, 0xdb, //0x00001863 xorl         %ebx, %ebx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00001865 movq         $-64(%rbp), %r13
	0xe9, 0xd3, 0x13, 0x00, 0x00, //0x00001869 jmp          LBB0_651
	//0x0000186e LBB0_340
	0x3d, 0xb6, 0xfe, 0xff, 0xff, //0x0000186e cmpl         $-330, %eax
	0x0f, 0x8d, 0x0b, 0x00, 0x00, 0x00, //0x00001873 jge          LBB0_342
	0x31, 0xc9, //0x00001879 xorl         %ecx, %ecx
	0x4c, 0x8b, 0x6d, 0xc0, //0x0000187b movq         $-64(%rbp), %r13
	0xe9, 0xbd, 0x13, 0x00, 0x00, //0x0000187f jmp          LBB0_651
	//0x00001884 LBB0_342
	0x49, 0xbb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, //0x00001884 movabsq      $1152921504606846976, %r11
	0x85, 0xc0, //0x0000188e testl        %eax, %eax
	0x0f, 0x8e, 0x00, 0x04, 0x00, 0x00, //0x00001890 jle          LBB0_407
	0x45, 0x31, 0xc9, //0x00001896 xorl         %r9d, %r9d
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001899 movl         $1, %r15d
	0x44, 0x89, 0xef, //0x0000189f movl         %r13d, %edi
	0x44, 0x89, 0xee, //0x000018a2 movl         %r13d, %esi
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x000018a5 jmp          LBB0_346
	//0x000018aa LBB0_344
	0x31, 0xf6, //0x000018aa xorl         %esi, %esi
	//0x000018ac LBB0_345
	0x44, 0x01, 0xcb, //0x000018ac addl         %r9d, %ebx
	0x41, 0x89, 0xd9, //0x000018af movl         %ebx, %r9d
	0x4c, 0x8b, 0x45, 0xd0, //0x000018b2 movq         $-48(%rbp), %r8
	0x45, 0x85, 0xc0, //0x000018b6 testl        %r8d, %r8d
	0x0f, 0x8e, 0xda, 0x03, 0x00, 0x00, //0x000018b9 jle          LBB0_408
	//0x000018bf LBB0_346
	0x41, 0x83, 0xf8, 0x08, //0x000018bf cmpl         $8, %r8d
	0x4c, 0x89, 0x45, 0xd0, //0x000018c3 movq         %r8, $-48(%rbp)
	0x0f, 0x86, 0x24, 0x00, 0x00, 0x00, //0x000018c7 jbe          LBB0_349
	0xb9, 0xe5, 0xff, 0xff, 0xff, //0x000018cd movl         $-27, %ecx
	0xbb, 0x1b, 0x00, 0x00, 0x00, //0x000018d2 movl         $27, %ebx
	0x85, 0xf6, //0x000018d7 testl        %esi, %esi
	0x0f, 0x84, 0xcb, 0xff, 0xff, 0xff, //0x000018d9 je           LBB0_344
	0x4c, 0x89, 0x4d, 0xc8, //0x000018df movq         %r9, $-56(%rbp)
	0xb8, 0x1b, 0x00, 0x00, 0x00, //0x000018e3 movl         $27, %eax
	0x48, 0x89, 0x45, 0xb8, //0x000018e8 movq         %rax, $-72(%rbp)
	0xe9, 0xc4, 0x01, 0x00, 0x00, //0x000018ec jmp          LBB0_379
	//0x000018f1 LBB0_349
	0x44, 0x89, 0xc0, //0x000018f1 movl         %r8d, %eax
	0x48, 0x8d, 0x0d, 0x35, 0x46, 0x00, 0x00, //0x000018f4 leaq         $17973(%rip), %rcx  /* _POW_TAB+0(%rip) */
	0x8b, 0x1c, 0x81, //0x000018fb movl         (%rcx,%rax,4), %ebx
	0x85, 0xf6, //0x000018fe testl        %esi, %esi
	0x0f, 0x84, 0xa4, 0xff, 0xff, 0xff, //0x00001900 je           LBB0_344
	0x4c, 0x89, 0x4d, 0xc8, //0x00001906 movq         %r9, $-56(%rbp)
	0x48, 0x89, 0x5d, 0xb8, //0x0000190a movq         %rbx, $-72(%rbp)
	0x89, 0xd9, //0x0000190e movl         %ebx, %ecx
	0xf7, 0xd9, //0x00001910 negl         %ecx
	0x83, 0xf9, 0xc3, //0x00001912 cmpl         $-61, %ecx
	0x0f, 0x87, 0x9a, 0x01, 0x00, 0x00, //0x00001915 ja           LBB0_379
	0x48, 0x8b, 0x7d, 0xd0, //0x0000191b movq         $-48(%rbp), %rdi
	0xe9, 0x16, 0x00, 0x00, 0x00, //0x0000191f jmp          LBB0_354
	//0x00001924 LBB0_376
	0x31, 0xff, //0x00001924 xorl         %edi, %edi
	//0x00001926 LBB0_352
	0x45, 0x31, 0xed, //0x00001926 xorl         %r13d, %r13d
	//0x00001929 LBB0_353
	0x41, 0x8d, 0x49, 0x3c, //0x00001929 leal         $60(%r9), %ecx
	0x44, 0x89, 0xee, //0x0000192d movl         %r13d, %esi
	0x41, 0x83, 0xf9, 0x88, //0x00001930 cmpl         $-120, %r9d
	0x0f, 0x8d, 0x74, 0x01, 0x00, 0x00, //0x00001934 jge          LBB0_378
	//0x0000193a LBB0_354
	0x41, 0x89, 0xc9, //0x0000193a movl         %ecx, %r9d
	0x85, 0xf6, //0x0000193d testl        %esi, %esi
	0xb9, 0x00, 0x00, 0x00, 0x00, //0x0000193f movl         $0, %ecx
	0x0f, 0x4f, 0xce, //0x00001944 cmovgl       %esi, %ecx
	0x31, 0xd2, //0x00001947 xorl         %edx, %edx
	0x31, 0xc0, //0x00001949 xorl         %eax, %eax
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000194b .p2align 4, 0x90
	//0x00001950 LBB0_355
	0x48, 0x39, 0xd1, //0x00001950 cmpq         %rdx, %rcx
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00001953 je           LBB0_358
	0x48, 0x8d, 0x04, 0x80, //0x00001959 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x1c, 0x16, //0x0000195d movsbq       (%r14,%rdx), %rbx
	0x48, 0x8d, 0x04, 0x43, //0x00001962 leaq         (%rbx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00001966 addq         $-48, %rax
	0x48, 0x83, 0xc2, 0x01, //0x0000196a addq         $1, %rdx
	0x4c, 0x39, 0xd8, //0x0000196e cmpq         %r11, %rax
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x00001971 jb           LBB0_355
	0x89, 0xd1, //0x00001977 movl         %edx, %ecx
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00001979 jmp          LBB0_360
	//0x0000197e LBB0_358
	0x48, 0x85, 0xc0, //0x0000197e testq        %rax, %rax
	0x0f, 0x84, 0x9f, 0xff, 0xff, 0xff, //0x00001981 je           LBB0_352
	//0x00001987 LBB0_359
	0x48, 0x01, 0xc0, //0x00001987 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x0000198a leaq         (%rax,%rax,4), %rax
	0x83, 0xc1, 0x01, //0x0000198e addl         $1, %ecx
	0x4c, 0x39, 0xd8, //0x00001991 cmpq         %r11, %rax
	0x0f, 0x82, 0xed, 0xff, 0xff, 0xff, //0x00001994 jb           LBB0_359
	//0x0000199a LBB0_360
	0x29, 0xcf, //0x0000199a subl         %ecx, %edi
	0x31, 0xd2, //0x0000199c xorl         %edx, %edx
	0x39, 0xf1, //0x0000199e cmpl         %esi, %ecx
	0x0f, 0x8d, 0x5c, 0x00, 0x00, 0x00, //0x000019a0 jge          LBB0_365
	0x49, 0x89, 0xff, //0x000019a6 movq         %rdi, %r15
	0x48, 0x63, 0xc9, //0x000019a9 movslq       %ecx, %rcx
	0x49, 0x63, 0xf5, //0x000019ac movslq       %r13d, %rsi
	0x49, 0x8d, 0x14, 0x0e, //0x000019af leaq         (%r14,%rcx), %rdx
	0x45, 0x31, 0xed, //0x000019b3 xorl         %r13d, %r13d
	//0x000019b6 LBB0_362
	0x49, 0x8d, 0x5b, 0xff, //0x000019b6 leaq         $-1(%r11), %rbx
	0x48, 0x21, 0xc3, //0x000019ba andq         %rax, %rbx
	0x48, 0xc1, 0xe8, 0x3c, //0x000019bd shrq         $60, %rax
	0x0c, 0x30, //0x000019c1 orb          $48, %al
	0x43, 0x88, 0x04, 0x2e, //0x000019c3 movb         %al, (%r14,%r13)
	0x4a, 0x0f, 0xbe, 0x04, 0x2a, //0x000019c7 movsbq       (%rdx,%r13), %rax
	0x4a, 0x8d, 0x3c, 0x29, //0x000019cc leaq         (%rcx,%r13), %rdi
	0x48, 0x83, 0xc7, 0x01, //0x000019d0 addq         $1, %rdi
	0x49, 0x83, 0xc5, 0x01, //0x000019d4 addq         $1, %r13
	0x48, 0x8d, 0x1c, 0x9b, //0x000019d8 leaq         (%rbx,%rbx,4), %rbx
	0x48, 0x8d, 0x04, 0x58, //0x000019dc leaq         (%rax,%rbx,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x000019e0 addq         $-48, %rax
	0x48, 0x39, 0xf7, //0x000019e4 cmpq         %rsi, %rdi
	0x0f, 0x8c, 0xc9, 0xff, 0xff, 0xff, //0x000019e7 jl           LBB0_362
	0x48, 0x85, 0xc0, //0x000019ed testq        %rax, %rax
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x000019f0 je           LBB0_369
	0x44, 0x89, 0xea, //0x000019f6 movl         %r13d, %edx
	0x4c, 0x89, 0xff, //0x000019f9 movq         %r15, %rdi
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x000019fc movl         $1, %r15d
	//0x00001a02 LBB0_365
	0x41, 0x89, 0xd5, //0x00001a02 movl         %edx, %r13d
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00001a05 jmp          LBB0_367
	//0x00001a0a LBB0_366
	0x4c, 0x39, 0xd8, //0x00001a0a cmpq         %r11, %rax
	0x45, 0x0f, 0x43, 0xd7, //0x00001a0d cmovael      %r15d, %r10d
	0x48, 0x8d, 0x04, 0x09, //0x00001a11 leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001a15 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc9, //0x00001a19 testq        %rcx, %rcx
	0x0f, 0x84, 0x42, 0x00, 0x00, 0x00, //0x00001a1c je           LBB0_370
	//0x00001a22 LBB0_367
	0x49, 0x8d, 0x4b, 0xff, //0x00001a22 leaq         $-1(%r11), %rcx
	0x48, 0x21, 0xc1, //0x00001a26 andq         %rax, %rcx
	0x49, 0x63, 0xd5, //0x00001a29 movslq       %r13d, %rdx
	0x49, 0x39, 0xd4, //0x00001a2c cmpq         %rdx, %r12
	0x0f, 0x86, 0xd5, 0xff, 0xff, 0xff, //0x00001a2f jbe          LBB0_366
	0x48, 0xc1, 0xe8, 0x3c, //0x00001a35 shrq         $60, %rax
	0x0c, 0x30, //0x00001a39 orb          $48, %al
	0x41, 0x88, 0x04, 0x16, //0x00001a3b movb         %al, (%r14,%rdx)
	0x83, 0xc2, 0x01, //0x00001a3f addl         $1, %edx
	0x41, 0x89, 0xd5, //0x00001a42 movl         %edx, %r13d
	0x48, 0x8d, 0x04, 0x09, //0x00001a45 leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001a49 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc9, //0x00001a4d testq        %rcx, %rcx
	0x0f, 0x85, 0xcc, 0xff, 0xff, 0xff, //0x00001a50 jne          LBB0_367
	0xe9, 0x09, 0x00, 0x00, 0x00, //0x00001a56 jmp          LBB0_370
	//0x00001a5b LBB0_369
	0x4c, 0x89, 0xff, //0x00001a5b movq         %r15, %rdi
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001a5e movl         $1, %r15d
	//0x00001a64 LBB0_370
	0x83, 0xc7, 0x01, //0x00001a64 addl         $1, %edi
	0x45, 0x85, 0xed, //0x00001a67 testl        %r13d, %r13d
	0x0f, 0x8e, 0x33, 0x00, 0x00, 0x00, //0x00001a6a jle          LBB0_375
	0x44, 0x89, 0xe8, //0x00001a70 movl         %r13d, %eax
	0x42, 0x80, 0x7c, 0x30, 0xff, 0x30, //0x00001a73 cmpb         $48, $-1(%rax,%r14)
	0x0f, 0x85, 0xaa, 0xfe, 0xff, 0xff, //0x00001a79 jne          LBB0_353
	//0x00001a7f LBB0_372
	0x48, 0x83, 0xf8, 0x01, //0x00001a7f cmpq         $1, %rax
	0x0f, 0x86, 0x9b, 0xfe, 0xff, 0xff, //0x00001a83 jbe          LBB0_376
	0x8d, 0x48, 0xfe, //0x00001a89 leal         $-2(%rax), %ecx
	0x48, 0x83, 0xc0, 0xff, //0x00001a8c addq         $-1, %rax
	0x41, 0x80, 0x3c, 0x0e, 0x30, //0x00001a90 cmpb         $48, (%r14,%rcx)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00001a95 je           LBB0_372
	0x41, 0x89, 0xc5, //0x00001a9b movl         %eax, %r13d
	0xe9, 0x86, 0xfe, 0xff, 0xff, //0x00001a9e jmp          LBB0_353
	//0x00001aa3 LBB0_375
	0x0f, 0x85, 0x80, 0xfe, 0xff, 0xff, //0x00001aa3 jne          LBB0_353
	0xe9, 0x76, 0xfe, 0xff, 0xff, //0x00001aa9 jmp          LBB0_376
	//0x00001aae LBB0_378
	0x48, 0x89, 0x7d, 0xd0, //0x00001aae movq         %rdi, $-48(%rbp)
	0x44, 0x89, 0xee, //0x00001ab2 movl         %r13d, %esi
	//0x00001ab5 LBB0_379
	0xf7, 0xd9, //0x00001ab5 negl         %ecx
	0x85, 0xf6, //0x00001ab7 testl        %esi, %esi
	0x41, 0xb9, 0x00, 0x00, 0x00, 0x00, //0x00001ab9 movl         $0, %r9d
	0x44, 0x0f, 0x4f, 0xce, //0x00001abf cmovgl       %esi, %r9d
	0x31, 0xd2, //0x00001ac3 xorl         %edx, %edx
	0x31, 0xc0, //0x00001ac5 xorl         %eax, %eax
	//0x00001ac7 LBB0_380
	0x49, 0x39, 0xd1, //0x00001ac7 cmpq         %rdx, %r9
	0x0f, 0x84, 0xab, 0x00, 0x00, 0x00, //0x00001aca je           LBB0_389
	0x48, 0x8d, 0x04, 0x80, //0x00001ad0 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x3c, 0x16, //0x00001ad4 movsbq       (%r14,%rdx), %rdi
	0x48, 0x8d, 0x04, 0x47, //0x00001ad9 leaq         (%rdi,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00001add addq         $-48, %rax
	0x48, 0x83, 0xc2, 0x01, //0x00001ae1 addq         $1, %rdx
	0x48, 0x89, 0xc7, //0x00001ae5 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00001ae8 shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x00001aeb testq        %rdi, %rdi
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00001aee je           LBB0_380
	0x41, 0x89, 0xd1, //0x00001af4 movl         %edx, %r9d
	//0x00001af7 LBB0_383
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001af7 movq         $-1, %r12
	0x49, 0xd3, 0xe4, //0x00001afe shlq         %cl, %r12
	0x49, 0xf7, 0xd4, //0x00001b01 notq         %r12
	0x31, 0xff, //0x00001b04 xorl         %edi, %edi
	0x41, 0x39, 0xf1, //0x00001b06 cmpl         %esi, %r9d
	0x0f, 0x8d, 0x48, 0x00, 0x00, 0x00, //0x00001b09 jge          LBB0_387
	0x49, 0x63, 0xf1, //0x00001b0f movslq       %r9d, %rsi
	0x4d, 0x63, 0xc5, //0x00001b12 movslq       %r13d, %r8
	0x49, 0x8d, 0x14, 0x36, //0x00001b15 leaq         (%r14,%rsi), %rdx
	0x31, 0xff, //0x00001b19 xorl         %edi, %edi
	//0x00001b1b LBB0_385
	0x48, 0x89, 0xc3, //0x00001b1b movq         %rax, %rbx
	0x48, 0xd3, 0xeb, //0x00001b1e shrq         %cl, %rbx
	0x4c, 0x21, 0xe0, //0x00001b21 andq         %r12, %rax
	0x80, 0xc3, 0x30, //0x00001b24 addb         $48, %bl
	0x41, 0x88, 0x1c, 0x3e, //0x00001b27 movb         %bl, (%r14,%rdi)
	0x48, 0x0f, 0xbe, 0x1c, 0x3a, //0x00001b2b movsbq       (%rdx,%rdi), %rbx
	0x4c, 0x8d, 0x3c, 0x3e, //0x00001b30 leaq         (%rsi,%rdi), %r15
	0x49, 0x83, 0xc7, 0x01, //0x00001b34 addq         $1, %r15
	0x48, 0x83, 0xc7, 0x01, //0x00001b38 addq         $1, %rdi
	0x48, 0x8d, 0x04, 0x80, //0x00001b3c leaq         (%rax,%rax,4), %rax
	0x48, 0x8d, 0x04, 0x43, //0x00001b40 leaq         (%rbx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00001b44 addq         $-48, %rax
	0x4d, 0x39, 0xc7, //0x00001b48 cmpq         %r8, %r15
	0x0f, 0x8c, 0xca, 0xff, 0xff, 0xff, //0x00001b4b jl           LBB0_385
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00001b51 movl         $1, %r15d
	//0x00001b57 LBB0_387
	0x48, 0x8b, 0x55, 0xd0, //0x00001b57 movq         $-48(%rbp), %rdx
	0x44, 0x29, 0xca, //0x00001b5b subl         %r9d, %edx
	0x83, 0xc2, 0x01, //0x00001b5e addl         $1, %edx
	0x48, 0x89, 0x55, 0xd0, //0x00001b61 movq         %rdx, $-48(%rbp)
	0x48, 0x85, 0xc0, //0x00001b65 testq        %rax, %rax
	0x0f, 0x85, 0x42, 0x00, 0x00, 0x00, //0x00001b68 jne          LBB0_392
	0x48, 0x8b, 0x5d, 0xb8, //0x00001b6e movq         $-72(%rbp), %rbx
	0x4c, 0x8b, 0x4d, 0xc8, //0x00001b72 movq         $-56(%rbp), %r9
	0xe9, 0x8b, 0x00, 0x00, 0x00, //0x00001b76 jmp          LBB0_396
	//0x00001b7b LBB0_389
	0x48, 0x85, 0xc0, //0x00001b7b testq        %rax, %rax
	0x0f, 0x84, 0xf4, 0x00, 0x00, 0x00, //0x00001b7e je           LBB0_404
	0x48, 0x89, 0xc2, //0x00001b84 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00001b87 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00001b8a testq        %rdx, %rdx
	0x0f, 0x84, 0xb6, 0x00, 0x00, 0x00, //0x00001b8d je           LBB0_401
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001b93 movq         $-1, %r12
	0x49, 0xd3, 0xe4, //0x00001b9a shlq         %cl, %r12
	0x49, 0xf7, 0xd4, //0x00001b9d notq         %r12
	0x48, 0x8b, 0x55, 0xd0, //0x00001ba0 movq         $-48(%rbp), %rdx
	0x44, 0x29, 0xca, //0x00001ba4 subl         %r9d, %edx
	0x83, 0xc2, 0x01, //0x00001ba7 addl         $1, %edx
	0x48, 0x89, 0x55, 0xd0, //0x00001baa movq         %rdx, $-48(%rbp)
	0x31, 0xff, //0x00001bae xorl         %edi, %edi
	//0x00001bb0 LBB0_392
	0x48, 0x8b, 0x5d, 0xb8, //0x00001bb0 movq         $-72(%rbp), %rbx
	0x4c, 0x8b, 0x4d, 0xc8, //0x00001bb4 movq         $-56(%rbp), %r9
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00001bb8 jmp          LBB0_394
	//0x00001bbd LBB0_393
	0x48, 0x85, 0xd2, //0x00001bbd testq        %rdx, %rdx
	0x45, 0x0f, 0x45, 0xd7, //0x00001bc0 cmovnel      %r15d, %r10d
	0x48, 0x01, 0xc0, //0x00001bc4 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001bc7 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x00001bcb testq        %rax, %rax
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x00001bce je           LBB0_396
	//0x00001bd4 LBB0_394
	0x48, 0x89, 0xc2, //0x00001bd4 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00001bd7 shrq         %cl, %rdx
	0x4c, 0x21, 0xe0, //0x00001bda andq         %r12, %rax
	0x48, 0x63, 0xf7, //0x00001bdd movslq       %edi, %rsi
	0x48, 0x39, 0x75, 0xb0, //0x00001be0 cmpq         %rsi, $-80(%rbp)
	0x0f, 0x86, 0xd3, 0xff, 0xff, 0xff, //0x00001be4 jbe          LBB0_393
	0x80, 0xc2, 0x30, //0x00001bea addb         $48, %dl
	0x41, 0x88, 0x14, 0x36, //0x00001bed movb         %dl, (%r14,%rsi)
	0x83, 0xc6, 0x01, //0x00001bf1 addl         $1, %esi
	0x89, 0xf7, //0x00001bf4 movl         %esi, %edi
	0x48, 0x01, 0xc0, //0x00001bf6 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001bf9 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x00001bfd testq        %rax, %rax
	0x0f, 0x85, 0xce, 0xff, 0xff, 0xff, //0x00001c00 jne          LBB0_394
	//0x00001c06 LBB0_396
	0x85, 0xff, //0x00001c06 testl        %edi, %edi
	0x4c, 0x8b, 0x65, 0xb0, //0x00001c08 movq         $-80(%rbp), %r12
	0x0f, 0x8e, 0x56, 0x00, 0x00, 0x00, //0x00001c0c jle          LBB0_402
	0x41, 0x89, 0xfd, //0x00001c12 movl         %edi, %r13d
	0x43, 0x80, 0x7c, 0x35, 0xff, 0x30, //0x00001c15 cmpb         $48, $-1(%r13,%r14)
	0x0f, 0x85, 0x4d, 0x00, 0x00, 0x00, //0x00001c1b jne          LBB0_403
	//0x00001c21 LBB0_398
	0x49, 0x83, 0xfd, 0x01, //0x00001c21 cmpq         $1, %r13
	0x0f, 0x86, 0x61, 0x00, 0x00, 0x00, //0x00001c25 jbe          LBB0_405
	0x41, 0x8d, 0x45, 0xfe, //0x00001c2b leal         $-2(%r13), %eax
	0x49, 0x83, 0xc5, 0xff, //0x00001c2f addq         $-1, %r13
	0x41, 0x80, 0x3c, 0x06, 0x30, //0x00001c33 cmpb         $48, (%r14,%rax)
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x00001c38 je           LBB0_398
	0x44, 0x89, 0xef, //0x00001c3e movl         %r13d, %edi
	0x44, 0x89, 0xee, //0x00001c41 movl         %r13d, %esi
	0xe9, 0x63, 0xfc, 0xff, 0xff, //0x00001c44 jmp          LBB0_345
	//0x00001c49 LBB0_401
	0x48, 0x01, 0xc0, //0x00001c49 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001c4c leaq         (%rax,%rax,4), %rax
	0x41, 0x83, 0xc1, 0x01, //0x00001c50 addl         $1, %r9d
	0x48, 0x89, 0xc2, //0x00001c54 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00001c57 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00001c5a testq        %rdx, %rdx
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x00001c5d je           LBB0_401
	0xe9, 0x8f, 0xfe, 0xff, 0xff, //0x00001c63 jmp          LBB0_383
	//0x00001c68 LBB0_402
	0x0f, 0x84, 0x78, 0x10, 0x00, 0x00, //0x00001c68 je           LBB0_660
	//0x00001c6e LBB0_403
	0x41, 0x89, 0xfd, //0x00001c6e movl         %edi, %r13d
	0x89, 0xfe, //0x00001c71 movl         %edi, %esi
	0xe9, 0x34, 0xfc, 0xff, 0xff, //0x00001c73 jmp          LBB0_345
	//0x00001c78 LBB0_404
	0x45, 0x31, 0xed, //0x00001c78 xorl         %r13d, %r13d
	0x31, 0xff, //0x00001c7b xorl         %edi, %edi
	0x31, 0xf6, //0x00001c7d xorl         %esi, %esi
	0x48, 0x8b, 0x5d, 0xb8, //0x00001c7f movq         $-72(%rbp), %rbx
	0x4c, 0x8b, 0x4d, 0xc8, //0x00001c83 movq         $-56(%rbp), %r9
	0xe9, 0x20, 0xfc, 0xff, 0xff, //0x00001c87 jmp          LBB0_345
	//0x00001c8c LBB0_405
	0x41, 0x83, 0xc5, 0xff, //0x00001c8c addl         $-1, %r13d
	//0x00001c90 LBB0_406
	0x44, 0x01, 0xcb, //0x00001c90 addl         %r9d, %ebx
	0x45, 0x31, 0xc0, //0x00001c93 xorl         %r8d, %r8d
	//0x00001c96 LBB0_407
	0x44, 0x89, 0xef, //0x00001c96 movl         %r13d, %edi
	//0x00001c99 LBB0_408
	0x48, 0x89, 0x5d, 0xb8, //0x00001c99 movq         %rbx, $-72(%rbp)
	0x4c, 0x89, 0x45, 0xd0, //0x00001c9d movq         %r8, $-48(%rbp)
	0x49, 0x8d, 0x46, 0x01, //0x00001ca1 leaq         $1(%r14), %rax
	0x48, 0x89, 0x45, 0x98, //0x00001ca5 movq         %rax, $-104(%rbp)
	0x49, 0xb8, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001ca9 movabsq      $-3689348814741910323, %r8
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x00001cb3 movl         $1, %r9d
	0x41, 0x89, 0xff, //0x00001cb9 movl         %edi, %r15d
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00001cbc jmp          LBB0_411
	//0x00001cc1 LBB0_489
	0x31, 0xff, //0x00001cc1 xorl         %edi, %edi
	//0x00001cc3 LBB0_409
	0x45, 0x31, 0xff, //0x00001cc3 xorl         %r15d, %r15d
	//0x00001cc6 LBB0_410
	0x48, 0x8b, 0x45, 0xb8, //0x00001cc6 movq         $-72(%rbp), %rax
	0x29, 0xd0, //0x00001cca subl         %edx, %eax
	0x48, 0x89, 0x45, 0xb8, //0x00001ccc movq         %rax, $-72(%rbp)
	//0x00001cd0 LBB0_411
	0x48, 0x8b, 0x45, 0xd0, //0x00001cd0 movq         $-48(%rbp), %rax
	0x85, 0xc0, //0x00001cd4 testl        %eax, %eax
	0x0f, 0x88, 0x15, 0x00, 0x00, 0x00, //0x00001cd6 js           LBB0_414
	0x0f, 0x85, 0xce, 0x06, 0x00, 0x00, //0x00001cdc jne          LBB0_515
	0x41, 0x80, 0x3e, 0x35, //0x00001ce2 cmpb         $53, (%r14)
	0x0f, 0x8c, 0x23, 0x00, 0x00, 0x00, //0x00001ce6 jl           LBB0_417
	0xe9, 0xbf, 0x06, 0x00, 0x00, //0x00001cec jmp          LBB0_515
	//0x00001cf1 LBB0_414
	0x83, 0xf8, 0xf8, //0x00001cf1 cmpl         $-8, %eax
	0x0f, 0x83, 0x15, 0x00, 0x00, 0x00, //0x00001cf4 jae          LBB0_417
	0xba, 0x1b, 0x00, 0x00, 0x00, //0x00001cfa movl         $27, %edx
	0x85, 0xff, //0x00001cff testl        %edi, %edi
	0x0f, 0x84, 0xba, 0xff, 0xff, 0xff, //0x00001d01 je           LBB0_489
	0x41, 0x89, 0xff, //0x00001d07 movl         %edi, %r15d
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x00001d0a jmp          LBB0_418
	//0x00001d0f LBB0_417
	0x48, 0x8b, 0x45, 0xd0, //0x00001d0f movq         $-48(%rbp), %rax
	0xf7, 0xd8, //0x00001d13 negl         %eax
	0x48, 0x8d, 0x0d, 0x14, 0x42, 0x00, 0x00, //0x00001d15 leaq         $16916(%rip), %rcx  /* _POW_TAB+0(%rip) */
	0x8b, 0x14, 0x81, //0x00001d1c movl         (%rcx,%rax,4), %edx
	0x45, 0x85, 0xff, //0x00001d1f testl        %r15d, %r15d
	0x0f, 0x84, 0x9b, 0xff, 0xff, 0xff, //0x00001d22 je           LBB0_409
	//0x00001d28 LBB0_418
	0x89, 0x55, 0xc8, //0x00001d28 movl         %edx, $-56(%rbp)
	0x89, 0xd1, //0x00001d2b movl         %edx, %ecx
	0x48, 0x6b, 0xd1, 0x68, //0x00001d2d imulq        $104, %rcx, %rdx
	0x48, 0x8d, 0x05, 0x28, 0x42, 0x00, 0x00, //0x00001d31 leaq         $16936(%rip), %rax  /* _LSHIFT_TAB+0(%rip) */
	0x44, 0x8b, 0x24, 0x02, //0x00001d38 movl         (%rdx,%rax), %r12d
	0x49, 0x63, 0xf7, //0x00001d3c movslq       %r15d, %rsi
	0x48, 0x8d, 0x3c, 0x02, //0x00001d3f leaq         (%rdx,%rax), %rdi
	0x48, 0x83, 0xc7, 0x04, //0x00001d43 addq         $4, %rdi
	0x31, 0xdb, //0x00001d47 xorl         %ebx, %ebx
	//0x00001d49 LBB0_419
	0x0f, 0xb6, 0x04, 0x1f, //0x00001d49 movzbl       (%rdi,%rbx), %eax
	0x84, 0xc0, //0x00001d4d testb        %al, %al
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00001d4f je           LBB0_424
	0x41, 0x38, 0x04, 0x1e, //0x00001d55 cmpb         %al, (%r14,%rbx)
	0x0f, 0x85, 0x37, 0x01, 0x00, 0x00, //0x00001d59 jne          LBB0_439
	0x48, 0x83, 0xc3, 0x01, //0x00001d5f addq         $1, %rbx
	0x48, 0x39, 0xde, //0x00001d63 cmpq         %rbx, %rsi
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00001d66 jne          LBB0_419
	0x44, 0x89, 0xf8, //0x00001d6c movl         %r15d, %eax
	0x48, 0x8d, 0x35, 0xea, 0x41, 0x00, 0x00, //0x00001d6f leaq         $16874(%rip), %rsi  /* _LSHIFT_TAB+0(%rip) */
	0x48, 0x01, 0xf2, //0x00001d76 addq         %rsi, %rdx
	0x80, 0x7c, 0x10, 0x04, 0x00, //0x00001d79 cmpb         $0, $4(%rax,%rdx)
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001d7e je           LBB0_424
	//0x00001d84 LBB0_423
	0x41, 0x83, 0xc4, 0xff, //0x00001d84 addl         $-1, %r12d
	//0x00001d88 LBB0_424
	0x45, 0x85, 0xff, //0x00001d88 testl        %r15d, %r15d
	0x4c, 0x89, 0x65, 0x90, //0x00001d8b movq         %r12, $-112(%rbp)
	0x0f, 0x8e, 0x96, 0x00, 0x00, 0x00, //0x00001d8f jle          LBB0_431
	0x43, 0x8d, 0x04, 0x3c, //0x00001d95 leal         (%r12,%r15), %eax
	0x44, 0x89, 0xfb, //0x00001d99 movl         %r15d, %ebx
	0x48, 0x98, //0x00001d9c cltq         
	0x48, 0x89, 0xc6, //0x00001d9e movq         %rax, %rsi
	0x48, 0xc1, 0xe6, 0x20, //0x00001da1 shlq         $32, %rsi
	0x48, 0x83, 0xc0, 0xff, //0x00001da5 addq         $-1, %rax
	0x48, 0x83, 0xc3, 0x01, //0x00001da9 addq         $1, %rbx
	0x45, 0x31, 0xff, //0x00001dad xorl         %r15d, %r15d
	0xe9, 0x27, 0x00, 0x00, 0x00, //0x00001db0 jmp          LBB0_428
	//0x00001db5 LBB0_426
	0x48, 0x85, 0xc0, //0x00001db5 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xd1, //0x00001db8 cmovnel      %r9d, %r10d
	//0x00001dbc LBB0_427
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00001dbc movabsq      $-4294967296, %rax
	0x48, 0x01, 0xc6, //0x00001dc6 addq         %rax, %rsi
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x00001dc9 leaq         $-1(%r12), %rax
	0x48, 0x83, 0xc3, 0xff, //0x00001dce addq         $-1, %rbx
	0x48, 0x83, 0xfb, 0x01, //0x00001dd2 cmpq         $1, %rbx
	0x0f, 0x86, 0x45, 0x00, 0x00, 0x00, //0x00001dd6 jbe          LBB0_430
	//0x00001ddc LBB0_428
	0x49, 0x89, 0xc4, //0x00001ddc movq         %rax, %r12
	0x8d, 0x43, 0xfe, //0x00001ddf leal         $-2(%rbx), %eax
	0x49, 0x0f, 0xbe, 0x3c, 0x06, //0x00001de2 movsbq       (%r14,%rax), %rdi
	0x48, 0x83, 0xc7, 0xd0, //0x00001de7 addq         $-48, %rdi
	0x48, 0xd3, 0xe7, //0x00001deb shlq         %cl, %rdi
	0x4c, 0x01, 0xff, //0x00001dee addq         %r15, %rdi
	0x48, 0x89, 0xf8, //0x00001df1 movq         %rdi, %rax
	0x49, 0xf7, 0xe0, //0x00001df4 mulq         %r8
	0x49, 0x89, 0xd7, //0x00001df7 movq         %rdx, %r15
	0x49, 0xc1, 0xef, 0x03, //0x00001dfa shrq         $3, %r15
	0x4b, 0x8d, 0x04, 0x3f, //0x00001dfe leaq         (%r15,%r15), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00001e02 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf8, //0x00001e06 movq         %rdi, %rax
	0x48, 0x29, 0xd0, //0x00001e09 subq         %rdx, %rax
	0x4c, 0x3b, 0x65, 0xb0, //0x00001e0c cmpq         $-80(%rbp), %r12
	0x0f, 0x83, 0x9f, 0xff, 0xff, 0xff, //0x00001e10 jae          LBB0_426
	0x04, 0x30, //0x00001e16 addb         $48, %al
	0x43, 0x88, 0x04, 0x26, //0x00001e18 movb         %al, (%r14,%r12)
	0xe9, 0x9b, 0xff, 0xff, 0xff, //0x00001e1c jmp          LBB0_427
	//0x00001e21 LBB0_430
	0x48, 0x83, 0xff, 0x0a, //0x00001e21 cmpq         $10, %rdi
	0x0f, 0x83, 0x76, 0x00, 0x00, 0x00, //0x00001e25 jae          LBB0_440
	//0x00001e2b LBB0_431
	0x4c, 0x8b, 0x65, 0xb0, //0x00001e2b movq         $-80(%rbp), %r12
	0x48, 0x8b, 0x7d, 0xd0, //0x00001e2f movq         $-48(%rbp), %rdi
	//0x00001e33 LBB0_432
	0x48, 0x8b, 0x45, 0x90, //0x00001e33 movq         $-112(%rbp), %rax
	0x41, 0x01, 0xc5, //0x00001e37 addl         %eax, %r13d
	0x4d, 0x63, 0xed, //0x00001e3a movslq       %r13d, %r13
	0x4d, 0x39, 0xec, //0x00001e3d cmpq         %r13, %r12
	0x45, 0x0f, 0x46, 0xec, //0x00001e40 cmovbel      %r12d, %r13d
	0x01, 0xc7, //0x00001e44 addl         %eax, %edi
	0x45, 0x85, 0xed, //0x00001e46 testl        %r13d, %r13d
	0x0f, 0x8e, 0x36, 0x00, 0x00, 0x00, //0x00001e49 jle          LBB0_437
	0x44, 0x89, 0xe8, //0x00001e4f movl         %r13d, %eax
	0x42, 0x80, 0x7c, 0x30, 0xff, 0x30, //0x00001e52 cmpb         $48, $-1(%rax,%r14)
	0x8b, 0x55, 0xc8, //0x00001e58 movl         $-56(%rbp), %edx
	0x0f, 0x85, 0xb1, 0x00, 0x00, 0x00, //0x00001e5b jne          LBB0_446
	//0x00001e61 LBB0_434
	0x48, 0x83, 0xf8, 0x01, //0x00001e61 cmpq         $1, %rax
	0x0f, 0x86, 0x9f, 0x00, 0x00, 0x00, //0x00001e65 jbe          LBB0_444
	0x8d, 0x48, 0xfe, //0x00001e6b leal         $-2(%rax), %ecx
	0x48, 0x83, 0xc0, 0xff, //0x00001e6e addq         $-1, %rax
	0x41, 0x80, 0x3c, 0x0e, 0x30, //0x00001e72 cmpb         $48, (%r14,%rcx)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00001e77 je           LBB0_434
	0x41, 0x89, 0xc5, //0x00001e7d movl         %eax, %r13d
	0xe9, 0x8d, 0x00, 0x00, 0x00, //0x00001e80 jmp          LBB0_446
	//0x00001e85 LBB0_437
	0x8b, 0x55, 0xc8, //0x00001e85 movl         $-56(%rbp), %edx
	0x0f, 0x85, 0x84, 0x00, 0x00, 0x00, //0x00001e88 jne          LBB0_446
	0x45, 0x31, 0xed, //0x00001e8e xorl         %r13d, %r13d
	0xe9, 0x7a, 0x00, 0x00, 0x00, //0x00001e91 jmp          LBB0_445
	//0x00001e96 LBB0_439
	0x0f, 0x8c, 0xe8, 0xfe, 0xff, 0xff, //0x00001e96 jl           LBB0_423
	0xe9, 0xe7, 0xfe, 0xff, 0xff, //0x00001e9c jmp          LBB0_424
	//0x00001ea1 LBB0_440
	0x49, 0x63, 0xcc, //0x00001ea1 movslq       %r12d, %rcx
	0x48, 0x83, 0xc1, 0xff, //0x00001ea4 addq         $-1, %rcx
	0x4c, 0x8b, 0x65, 0xb0, //0x00001ea8 movq         $-80(%rbp), %r12
	0x48, 0x8b, 0x7d, 0xd0, //0x00001eac movq         $-48(%rbp), %rdi
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00001eb0 jmp          LBB0_442
	//0x00001eb5 LBB0_441
	0x48, 0x85, 0xc0, //0x00001eb5 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xd1, //0x00001eb8 cmovnel      %r9d, %r10d
	0x48, 0x83, 0xc1, 0xff, //0x00001ebc addq         $-1, %rcx
	0x49, 0x83, 0xff, 0x09, //0x00001ec0 cmpq         $9, %r15
	0x49, 0x89, 0xd7, //0x00001ec4 movq         %rdx, %r15
	0x0f, 0x86, 0x66, 0xff, 0xff, 0xff, //0x00001ec7 jbe          LBB0_432
	//0x00001ecd LBB0_442
	0x4c, 0x89, 0xf8, //0x00001ecd movq         %r15, %rax
	0x49, 0xf7, 0xe0, //0x00001ed0 mulq         %r8
	0x48, 0xc1, 0xea, 0x03, //0x00001ed3 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00001ed7 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x34, 0x80, //0x00001edb leaq         (%rax,%rax,4), %rsi
	0x4c, 0x89, 0xf8, //0x00001edf movq         %r15, %rax
	0x48, 0x29, 0xf0, //0x00001ee2 subq         %rsi, %rax
	0x4c, 0x39, 0xe1, //0x00001ee5 cmpq         %r12, %rcx
	0x0f, 0x83, 0xc7, 0xff, 0xff, 0xff, //0x00001ee8 jae          LBB0_441
	0x04, 0x30, //0x00001eee addb         $48, %al
	0x41, 0x88, 0x04, 0x0e, //0x00001ef0 movb         %al, (%r14,%rcx)
	0x48, 0x83, 0xc1, 0xff, //0x00001ef4 addq         $-1, %rcx
	0x49, 0x83, 0xff, 0x09, //0x00001ef8 cmpq         $9, %r15
	0x49, 0x89, 0xd7, //0x00001efc movq         %rdx, %r15
	0x0f, 0x87, 0xc8, 0xff, 0xff, 0xff, //0x00001eff ja           LBB0_442
	0xe9, 0x29, 0xff, 0xff, 0xff, //0x00001f05 jmp          LBB0_432
	//0x00001f0a LBB0_444
	0x83, 0xc0, 0xff, //0x00001f0a addl         $-1, %eax
	0x41, 0x89, 0xc5, //0x00001f0d movl         %eax, %r13d
	//0x00001f10 LBB0_445
	0x31, 0xff, //0x00001f10 xorl         %edi, %edi
	//0x00001f12 LBB0_446
	0x85, 0xd2, //0x00001f12 testl        %edx, %edx
	0x0f, 0x88, 0x0f, 0x00, 0x00, 0x00, //0x00001f14 js           LBB0_449
	//0x00001f1a LBB0_447
	0x48, 0x89, 0x7d, 0xd0, //0x00001f1a movq         %rdi, $-48(%rbp)
	//0x00001f1e LBB0_448
	0x44, 0x89, 0xef, //0x00001f1e movl         %r13d, %edi
	0x45, 0x89, 0xef, //0x00001f21 movl         %r13d, %r15d
	0xe9, 0x9d, 0xfd, 0xff, 0xff, //0x00001f24 jmp          LBB0_410
	//0x00001f29 LBB0_449
	0x83, 0xfa, 0xc3, //0x00001f29 cmpl         $-61, %edx
	0x0f, 0x87, 0x15, 0x02, 0x00, 0x00, //0x00001f2c ja           LBB0_478
	0x41, 0x89, 0xd7, //0x00001f32 movl         %edx, %r15d
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x00001f35 jmp          LBB0_453
	//0x00001f3a LBB0_477
	0x31, 0xff, //0x00001f3a xorl         %edi, %edi
	//0x00001f3c LBB0_451
	0x45, 0x31, 0xc0, //0x00001f3c xorl         %r8d, %r8d
	//0x00001f3f LBB0_452
	0x41, 0x8d, 0x4f, 0x3c, //0x00001f3f leal         $60(%r15), %ecx
	0x45, 0x89, 0xc5, //0x00001f43 movl         %r8d, %r13d
	0x41, 0x83, 0xff, 0x88, //0x00001f46 cmpl         $-120, %r15d
	0x41, 0x89, 0xcf, //0x00001f4a movl         %ecx, %r15d
	0x0f, 0x8d, 0xf9, 0x01, 0x00, 0x00, //0x00001f4d jge          LBB0_479
	//0x00001f53 LBB0_453
	0x45, 0x85, 0xed, //0x00001f53 testl        %r13d, %r13d
	0xbe, 0x00, 0x00, 0x00, 0x00, //0x00001f56 movl         $0, %esi
	0x41, 0x0f, 0x4f, 0xf5, //0x00001f5b cmovgl       %r13d, %esi
	0x31, 0xc0, //0x00001f5f xorl         %eax, %eax
	0x31, 0xc9, //0x00001f61 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001f63 .p2align 4, 0x90
	//0x00001f70 LBB0_454
	0x48, 0x39, 0xc6, //0x00001f70 cmpq         %rax, %rsi
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00001f73 je           LBB0_457
	0x48, 0x8d, 0x0c, 0x89, //0x00001f79 leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x14, 0x06, //0x00001f7d movsbq       (%r14,%rax), %rdx
	0x48, 0x8d, 0x0c, 0x4a, //0x00001f82 leaq         (%rdx,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00001f86 addq         $-48, %rcx
	0x48, 0x83, 0xc0, 0x01, //0x00001f8a addq         $1, %rax
	0x4c, 0x39, 0xd9, //0x00001f8e cmpq         %r11, %rcx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x00001f91 jb           LBB0_454
	0x89, 0xc6, //0x00001f97 movl         %eax, %esi
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00001f99 jmp          LBB0_459
	//0x00001f9e LBB0_457
	0x48, 0x85, 0xc9, //0x00001f9e testq        %rcx, %rcx
	0x0f, 0x84, 0x95, 0xff, 0xff, 0xff, //0x00001fa1 je           LBB0_451
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001fa7 .p2align 4, 0x90
	//0x00001fb0 LBB0_458
	0x48, 0x01, 0xc9, //0x00001fb0 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00001fb3 leaq         (%rcx,%rcx,4), %rcx
	0x83, 0xc6, 0x01, //0x00001fb7 addl         $1, %esi
	0x4c, 0x39, 0xd9, //0x00001fba cmpq         %r11, %rcx
	0x0f, 0x82, 0xed, 0xff, 0xff, 0xff, //0x00001fbd jb           LBB0_458
	//0x00001fc3 LBB0_459
	0x29, 0xf7, //0x00001fc3 subl         %esi, %edi
	0x45, 0x31, 0xc0, //0x00001fc5 xorl         %r8d, %r8d
	0x44, 0x39, 0xee, //0x00001fc8 cmpl         %r13d, %esi
	0x0f, 0x8d, 0xf7, 0x00, 0x00, 0x00, //0x00001fcb jge          LBB0_469
	0x48, 0x89, 0x7d, 0xd0, //0x00001fd1 movq         %rdi, $-48(%rbp)
	0x48, 0x63, 0xf6, //0x00001fd5 movslq       %esi, %rsi
	0x49, 0x63, 0xc5, //0x00001fd8 movslq       %r13d, %rax
	0x49, 0x89, 0xc0, //0x00001fdb movq         %rax, %r8
	0x49, 0x29, 0xf0, //0x00001fde subq         %rsi, %r8
	0x48, 0x89, 0xf7, //0x00001fe1 movq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x00001fe4 notq         %rdi
	0x48, 0x01, 0xc7, //0x00001fe7 addq         %rax, %rdi
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00001fea jne          LBB0_462
	0x31, 0xff, //0x00001ff0 xorl         %edi, %edi
	0xe9, 0x7a, 0x00, 0x00, 0x00, //0x00001ff2 jmp          LBB0_465
	//0x00001ff7 LBB0_462
	0x4d, 0x89, 0xc4, //0x00001ff7 movq         %r8, %r12
	0x49, 0x83, 0xe4, 0xfe, //0x00001ffa andq         $-2, %r12
	0x49, 0xf7, 0xdc, //0x00001ffe negq         %r12
	0x31, 0xff, //0x00002001 xorl         %edi, %edi
	0x48, 0x8b, 0x45, 0x98, //0x00002003 movq         $-104(%rbp), %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002007 .p2align 4, 0x90
	//0x00002010 LBB0_463
	0x48, 0x89, 0xcb, //0x00002010 movq         %rcx, %rbx
	0x48, 0xc1, 0xeb, 0x3c, //0x00002013 shrq         $60, %rbx
	0x49, 0x8d, 0x53, 0xff, //0x00002017 leaq         $-1(%r11), %rdx
	0x48, 0x21, 0xd1, //0x0000201b andq         %rdx, %rcx
	0x80, 0xcb, 0x30, //0x0000201e orb          $48, %bl
	0x88, 0x58, 0xff, //0x00002021 movb         %bl, $-1(%rax)
	0x48, 0x8d, 0x0c, 0x89, //0x00002024 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x0f, 0xbe, 0x5c, 0x30, 0xff, //0x00002028 movsbq       $-1(%rax,%rsi), %rbx
	0x48, 0x8d, 0x0c, 0x4b, //0x0000202e leaq         (%rbx,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00002032 addq         $-48, %rcx
	0x48, 0x89, 0xcb, //0x00002036 movq         %rcx, %rbx
	0x48, 0xc1, 0xeb, 0x3c, //0x00002039 shrq         $60, %rbx
	0x48, 0x21, 0xd1, //0x0000203d andq         %rdx, %rcx
	0x80, 0xcb, 0x30, //0x00002040 orb          $48, %bl
	0x88, 0x18, //0x00002043 movb         %bl, (%rax)
	0x48, 0x8d, 0x0c, 0x89, //0x00002045 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x0f, 0xbe, 0x14, 0x30, //0x00002049 movsbq       (%rax,%rsi), %rdx
	0x48, 0x8d, 0x0c, 0x4a, //0x0000204e leaq         (%rdx,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00002052 addq         $-48, %rcx
	0x48, 0x83, 0xc0, 0x02, //0x00002056 addq         $2, %rax
	0x48, 0x83, 0xc7, 0xfe, //0x0000205a addq         $-2, %rdi
	0x49, 0x39, 0xfc, //0x0000205e cmpq         %rdi, %r12
	0x0f, 0x85, 0xa9, 0xff, 0xff, 0xff, //0x00002061 jne          LBB0_463
	0x48, 0x29, 0xfe, //0x00002067 subq         %rdi, %rsi
	0x48, 0xf7, 0xdf, //0x0000206a negq         %rdi
	0x4c, 0x8b, 0x65, 0xb0, //0x0000206d movq         $-80(%rbp), %r12
	//0x00002071 LBB0_465
	0x41, 0xf6, 0xc0, 0x01, //0x00002071 testb        $1, %r8b
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x00002075 je           LBB0_467
	0x49, 0x8d, 0x43, 0xff, //0x0000207b leaq         $-1(%r11), %rax
	0x48, 0x21, 0xc8, //0x0000207f andq         %rcx, %rax
	0x48, 0xc1, 0xe9, 0x3c, //0x00002082 shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x00002086 orb          $48, %cl
	0x41, 0x88, 0x0c, 0x3e, //0x00002089 movb         %cl, (%r14,%rdi)
	0x48, 0x8d, 0x04, 0x80, //0x0000208d leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x36, //0x00002091 movsbq       (%r14,%rsi), %rcx
	0x48, 0x8d, 0x0c, 0x41, //0x00002096 leaq         (%rcx,%rax,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x0000209a addq         $-48, %rcx
	//0x0000209e LBB0_467
	0x48, 0x85, 0xc9, //0x0000209e testq        %rcx, %rcx
	0x48, 0x8b, 0x7d, 0xd0, //0x000020a1 movq         $-48(%rbp), %rdi
	0x0f, 0x85, 0x1d, 0x00, 0x00, 0x00, //0x000020a5 jne          LBB0_469
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x000020ab jmp          LBB0_471
	//0x000020b0 .p2align 4, 0x90
	//0x000020b0 LBB0_468
	0x4c, 0x39, 0xd9, //0x000020b0 cmpq         %r11, %rcx
	0x45, 0x0f, 0x43, 0xd1, //0x000020b3 cmovael      %r9d, %r10d
	0x48, 0x8d, 0x0c, 0x00, //0x000020b7 leaq         (%rax,%rax), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000020bb leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc0, //0x000020bf testq        %rax, %rax
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x000020c2 je           LBB0_471
	//0x000020c8 LBB0_469
	0x49, 0x8d, 0x43, 0xff, //0x000020c8 leaq         $-1(%r11), %rax
	0x48, 0x21, 0xc8, //0x000020cc andq         %rcx, %rax
	0x49, 0x63, 0xf0, //0x000020cf movslq       %r8d, %rsi
	0x49, 0x39, 0xf4, //0x000020d2 cmpq         %rsi, %r12
	0x0f, 0x86, 0xd5, 0xff, 0xff, 0xff, //0x000020d5 jbe          LBB0_468
	0x48, 0xc1, 0xe9, 0x3c, //0x000020db shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x000020df orb          $48, %cl
	0x41, 0x88, 0x0c, 0x36, //0x000020e2 movb         %cl, (%r14,%rsi)
	0x83, 0xc6, 0x01, //0x000020e6 addl         $1, %esi
	0x41, 0x89, 0xf0, //0x000020e9 movl         %esi, %r8d
	0x48, 0x8d, 0x0c, 0x00, //0x000020ec leaq         (%rax,%rax), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000020f0 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc0, //0x000020f4 testq        %rax, %rax
	0x0f, 0x85, 0xcb, 0xff, 0xff, 0xff, //0x000020f7 jne          LBB0_469
	//0x000020fd LBB0_471
	0x83, 0xc7, 0x01, //0x000020fd addl         $1, %edi
	0x45, 0x85, 0xc0, //0x00002100 testl        %r8d, %r8d
	0x0f, 0x8e, 0x33, 0x00, 0x00, 0x00, //0x00002103 jle          LBB0_476
	0x44, 0x89, 0xc0, //0x00002109 movl         %r8d, %eax
	0x42, 0x80, 0x7c, 0x30, 0xff, 0x30, //0x0000210c cmpb         $48, $-1(%rax,%r14)
	0x0f, 0x85, 0x27, 0xfe, 0xff, 0xff, //0x00002112 jne          LBB0_452
	//0x00002118 LBB0_473
	0x48, 0x83, 0xf8, 0x01, //0x00002118 cmpq         $1, %rax
	0x0f, 0x86, 0x18, 0xfe, 0xff, 0xff, //0x0000211c jbe          LBB0_477
	0x8d, 0x48, 0xfe, //0x00002122 leal         $-2(%rax), %ecx
	0x48, 0x83, 0xc0, 0xff, //0x00002125 addq         $-1, %rax
	0x41, 0x80, 0x3c, 0x0e, 0x30, //0x00002129 cmpb         $48, (%r14,%rcx)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x0000212e je           LBB0_473
	0x41, 0x89, 0xc0, //0x00002134 movl         %eax, %r8d
	0xe9, 0x03, 0xfe, 0xff, 0xff, //0x00002137 jmp          LBB0_452
	//0x0000213c LBB0_476
	0x0f, 0x85, 0xfd, 0xfd, 0xff, 0xff, //0x0000213c jne          LBB0_452
	0xe9, 0xf3, 0xfd, 0xff, 0xff, //0x00002142 jmp          LBB0_477
	//0x00002147 LBB0_478
	0x45, 0x89, 0xe8, //0x00002147 movl         %r13d, %r8d
	0x89, 0xd1, //0x0000214a movl         %edx, %ecx
	//0x0000214c LBB0_479
	0x48, 0x89, 0x7d, 0xd0, //0x0000214c movq         %rdi, $-48(%rbp)
	0xf7, 0xd9, //0x00002150 negl         %ecx
	0x45, 0x85, 0xc0, //0x00002152 testl        %r8d, %r8d
	0xbe, 0x00, 0x00, 0x00, 0x00, //0x00002155 movl         $0, %esi
	0x41, 0x0f, 0x4f, 0xf0, //0x0000215a cmovgl       %r8d, %esi
	0x31, 0xff, //0x0000215e xorl         %edi, %edi
	0x31, 0xc0, //0x00002160 xorl         %eax, %eax
	//0x00002162 LBB0_480
	0x48, 0x39, 0xfe, //0x00002162 cmpq         %rdi, %rsi
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x00002165 je           LBB0_486
	0x48, 0x8d, 0x04, 0x80, //0x0000216b leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x14, 0x3e, //0x0000216f movsbq       (%r14,%rdi), %rdx
	0x48, 0x8d, 0x04, 0x42, //0x00002174 leaq         (%rdx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00002178 addq         $-48, %rax
	0x48, 0x83, 0xc7, 0x01, //0x0000217c addq         $1, %rdi
	0x48, 0x89, 0xc2, //0x00002180 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00002183 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00002186 testq        %rdx, %rdx
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00002189 je           LBB0_480
	0x89, 0xfe, //0x0000218f movl         %edi, %esi
	0x48, 0x8b, 0x7d, 0xd0, //0x00002191 movq         $-48(%rbp), %rdi
	//0x00002195 LBB0_483
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002195 movq         $-1, %r15
	0x49, 0xd3, 0xe7, //0x0000219c shlq         %cl, %r15
	0x49, 0xf7, 0xd7, //0x0000219f notq         %r15
	0x45, 0x31, 0xed, //0x000021a2 xorl         %r13d, %r13d
	0x44, 0x39, 0xc6, //0x000021a5 cmpl         %r8d, %esi
	0x0f, 0x8d, 0x62, 0x00, 0x00, 0x00, //0x000021a8 jge          LBB0_490
	0x4c, 0x63, 0xe6, //0x000021ae movslq       %esi, %r12
	0x49, 0x63, 0xd0, //0x000021b1 movslq       %r8d, %rdx
	0x49, 0x89, 0xd5, //0x000021b4 movq         %rdx, %r13
	0x4d, 0x29, 0xe5, //0x000021b7 subq         %r12, %r13
	0x4c, 0x89, 0xe3, //0x000021ba movq         %r12, %rbx
	0x48, 0xf7, 0xd3, //0x000021bd notq         %rbx
	0x48, 0x01, 0xd3, //0x000021c0 addq         %rdx, %rbx
	0x0f, 0x85, 0x70, 0x00, 0x00, 0x00, //0x000021c3 jne          LBB0_492
	0x31, 0xd2, //0x000021c9 xorl         %edx, %edx
	0xe9, 0xd8, 0x00, 0x00, 0x00, //0x000021cb jmp          LBB0_495
	//0x000021d0 LBB0_486
	0x48, 0x85, 0xc0, //0x000021d0 testq        %rax, %rax
	0x0f, 0x84, 0x46, 0x00, 0x00, 0x00, //0x000021d3 je           LBB0_491
	0x48, 0x89, 0xc2, //0x000021d9 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x000021dc shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x000021df testq        %rdx, %rdx
	0x0f, 0x84, 0x03, 0x01, 0x00, 0x00, //0x000021e2 je           LBB0_499
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000021e8 movq         $-1, %r15
	0x49, 0xd3, 0xe7, //0x000021ef shlq         %cl, %r15
	0x49, 0xf7, 0xd7, //0x000021f2 notq         %r15
	0x48, 0x8b, 0x7d, 0xd0, //0x000021f5 movq         $-48(%rbp), %rdi
	0x29, 0xf7, //0x000021f9 subl         %esi, %edi
	0x83, 0xc7, 0x01, //0x000021fb addl         $1, %edi
	0x45, 0x31, 0xed, //0x000021fe xorl         %r13d, %r13d
	0x49, 0xb8, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002201 movabsq      $-3689348814741910323, %r8
	0xe9, 0xfd, 0x00, 0x00, 0x00, //0x0000220b jmp          LBB0_502
	//0x00002210 LBB0_490
	0x49, 0xb8, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002210 movabsq      $-3689348814741910323, %r8
	0xe9, 0xc2, 0x00, 0x00, 0x00, //0x0000221a jmp          LBB0_498
	//0x0000221f LBB0_491
	0x45, 0x31, 0xed, //0x0000221f xorl         %r13d, %r13d
	0x31, 0xff, //0x00002222 xorl         %edi, %edi
	0x45, 0x31, 0xff, //0x00002224 xorl         %r15d, %r15d
	0x49, 0xb8, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002227 movabsq      $-3689348814741910323, %r8
	0x8b, 0x55, 0xc8, //0x00002231 movl         $-56(%rbp), %edx
	0xe9, 0x8d, 0xfa, 0xff, 0xff, //0x00002234 jmp          LBB0_410
	//0x00002239 LBB0_492
	0x4d, 0x89, 0xe8, //0x00002239 movq         %r13, %r8
	0x49, 0x83, 0xe0, 0xfe, //0x0000223c andq         $-2, %r8
	0x49, 0xf7, 0xd8, //0x00002240 negq         %r8
	0x31, 0xd2, //0x00002243 xorl         %edx, %edx
	0x48, 0x8b, 0x5d, 0x98, //0x00002245 movq         $-104(%rbp), %rbx
	//0x00002249 LBB0_493
	0x48, 0x89, 0xc7, //0x00002249 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x0000224c shrq         %cl, %rdi
	0x4c, 0x21, 0xf8, //0x0000224f andq         %r15, %rax
	0x40, 0x80, 0xc7, 0x30, //0x00002252 addb         $48, %dil
	0x40, 0x88, 0x7b, 0xff, //0x00002256 movb         %dil, $-1(%rbx)
	0x48, 0x8d, 0x04, 0x80, //0x0000225a leaq         (%rax,%rax,4), %rax
	0x4a, 0x0f, 0xbe, 0x7c, 0x23, 0xff, //0x0000225e movsbq       $-1(%rbx,%r12), %rdi
	0x48, 0x8d, 0x04, 0x47, //0x00002264 leaq         (%rdi,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00002268 addq         $-48, %rax
	0x48, 0x89, 0xc7, //0x0000226c movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x0000226f shrq         %cl, %rdi
	0x4c, 0x21, 0xf8, //0x00002272 andq         %r15, %rax
	0x40, 0x80, 0xc7, 0x30, //0x00002275 addb         $48, %dil
	0x40, 0x88, 0x3b, //0x00002279 movb         %dil, (%rbx)
	0x48, 0x8d, 0x04, 0x80, //0x0000227c leaq         (%rax,%rax,4), %rax
	0x4a, 0x0f, 0xbe, 0x3c, 0x23, //0x00002280 movsbq       (%rbx,%r12), %rdi
	0x48, 0x8d, 0x04, 0x47, //0x00002285 leaq         (%rdi,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00002289 addq         $-48, %rax
	0x48, 0x83, 0xc3, 0x02, //0x0000228d addq         $2, %rbx
	0x48, 0x83, 0xc2, 0xfe, //0x00002291 addq         $-2, %rdx
	0x49, 0x39, 0xd0, //0x00002295 cmpq         %rdx, %r8
	0x0f, 0x85, 0xab, 0xff, 0xff, 0xff, //0x00002298 jne          LBB0_493
	0x49, 0x29, 0xd4, //0x0000229e subq         %rdx, %r12
	0x48, 0xf7, 0xda, //0x000022a1 negq         %rdx
	0x48, 0x8b, 0x7d, 0xd0, //0x000022a4 movq         $-48(%rbp), %rdi
	//0x000022a8 LBB0_495
	0x41, 0xf6, 0xc5, 0x01, //0x000022a8 testb        $1, %r13b
	0x49, 0xb8, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000022ac movabsq      $-3689348814741910323, %r8
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x000022b6 je           LBB0_497
	0x48, 0x89, 0xc3, //0x000022bc movq         %rax, %rbx
	0x48, 0xd3, 0xeb, //0x000022bf shrq         %cl, %rbx
	0x4c, 0x21, 0xf8, //0x000022c2 andq         %r15, %rax
	0x80, 0xc3, 0x30, //0x000022c5 addb         $48, %bl
	0x41, 0x88, 0x1c, 0x16, //0x000022c8 movb         %bl, (%r14,%rdx)
	0x48, 0x8d, 0x04, 0x80, //0x000022cc leaq         (%rax,%rax,4), %rax
	0x4b, 0x0f, 0xbe, 0x14, 0x26, //0x000022d0 movsbq       (%r14,%r12), %rdx
	0x48, 0x8d, 0x04, 0x42, //0x000022d5 leaq         (%rdx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x000022d9 addq         $-48, %rax
	//0x000022dd LBB0_497
	0x4c, 0x8b, 0x65, 0xb0, //0x000022dd movq         $-80(%rbp), %r12
	//0x000022e1 LBB0_498
	0x29, 0xf7, //0x000022e1 subl         %esi, %edi
	0x83, 0xc7, 0x01, //0x000022e3 addl         $1, %edi
	0xe9, 0x57, 0x00, 0x00, 0x00, //0x000022e6 jmp          LBB0_501
	//0x000022eb LBB0_499
	0x48, 0x8b, 0x7d, 0xd0, //0x000022eb movq         $-48(%rbp), %rdi
	//0x000022ef LBB0_500
	0x48, 0x01, 0xc0, //0x000022ef addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x000022f2 leaq         (%rax,%rax,4), %rax
	0x83, 0xc6, 0x01, //0x000022f6 addl         $1, %esi
	0x48, 0x89, 0xc2, //0x000022f9 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x000022fc shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x000022ff testq        %rdx, %rdx
	0x0f, 0x84, 0xe7, 0xff, 0xff, 0xff, //0x00002302 je           LBB0_500
	0xe9, 0x88, 0xfe, 0xff, 0xff, //0x00002308 jmp          LBB0_483
	//0x0000230d LBB0_502
	0x48, 0x89, 0xc2, //0x0000230d movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00002310 shrq         %cl, %rdx
	0x4c, 0x21, 0xf8, //0x00002313 andq         %r15, %rax
	0x49, 0x63, 0xf5, //0x00002316 movslq       %r13d, %rsi
	0x49, 0x39, 0xf4, //0x00002319 cmpq         %rsi, %r12
	0x0f, 0x86, 0x12, 0x00, 0x00, 0x00, //0x0000231c jbe          LBB0_504
	0x80, 0xc2, 0x30, //0x00002322 addb         $48, %dl
	0x41, 0x88, 0x14, 0x36, //0x00002325 movb         %dl, (%r14,%rsi)
	0x83, 0xc6, 0x01, //0x00002329 addl         $1, %esi
	0x41, 0x89, 0xf5, //0x0000232c movl         %esi, %r13d
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x0000232f jmp          LBB0_505
	//0x00002334 LBB0_504
	0x48, 0x85, 0xd2, //0x00002334 testq        %rdx, %rdx
	0x45, 0x0f, 0x45, 0xd1, //0x00002337 cmovnel      %r9d, %r10d
	//0x0000233b LBB0_505
	0x48, 0x01, 0xc0, //0x0000233b addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x0000233e leaq         (%rax,%rax,4), %rax
	//0x00002342 LBB0_501
	0x48, 0x85, 0xc0, //0x00002342 testq        %rax, %rax
	0x0f, 0x85, 0xc2, 0xff, 0xff, 0xff, //0x00002345 jne          LBB0_502
	0x45, 0x85, 0xed, //0x0000234b testl        %r13d, %r13d
	0x8b, 0x55, 0xc8, //0x0000234e movl         $-56(%rbp), %edx
	0x0f, 0x8e, 0x3a, 0x00, 0x00, 0x00, //0x00002351 jle          LBB0_511
	0x48, 0x89, 0x7d, 0xd0, //0x00002357 movq         %rdi, $-48(%rbp)
	0x44, 0x89, 0xef, //0x0000235b movl         %r13d, %edi
	0x42, 0x80, 0x7c, 0x37, 0xff, 0x30, //0x0000235e cmpb         $48, $-1(%rdi,%r14)
	0x0f, 0x85, 0xb4, 0xfb, 0xff, 0xff, //0x00002364 jne          LBB0_448
	//0x0000236a LBB0_508
	0x48, 0x83, 0xff, 0x01, //0x0000236a cmpq         $1, %rdi
	0x0f, 0x86, 0x2b, 0x00, 0x00, 0x00, //0x0000236e jbe          LBB0_513
	0x8d, 0x47, 0xfe, //0x00002374 leal         $-2(%rdi), %eax
	0x48, 0x83, 0xc7, 0xff, //0x00002377 addq         $-1, %rdi
	0x41, 0x80, 0x3c, 0x06, 0x30, //0x0000237b cmpb         $48, (%r14,%rax)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00002380 je           LBB0_508
	0x41, 0x89, 0xfd, //0x00002386 movl         %edi, %r13d
	0x41, 0x89, 0xff, //0x00002389 movl         %edi, %r15d
	0xe9, 0x35, 0xf9, 0xff, 0xff, //0x0000238c jmp          LBB0_410
	//0x00002391 LBB0_511
	0x0f, 0x85, 0x83, 0xfb, 0xff, 0xff, //0x00002391 jne          LBB0_447
	0x45, 0x31, 0xed, //0x00002397 xorl         %r13d, %r13d
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x0000239a jmp          LBB0_514
	//0x0000239f LBB0_513
	0x83, 0xc7, 0xff, //0x0000239f addl         $-1, %edi
	0x41, 0x89, 0xfd, //0x000023a2 movl         %edi, %r13d
	//0x000023a5 LBB0_514
	0x31, 0xc0, //0x000023a5 xorl         %eax, %eax
	0x48, 0x89, 0x45, 0xd0, //0x000023a7 movq         %rax, $-48(%rbp)
	0xe9, 0x6e, 0xfb, 0xff, 0xff, //0x000023ab jmp          LBB0_448
	//0x000023b0 LBB0_515
	0x48, 0x8b, 0x45, 0xb8, //0x000023b0 movq         $-72(%rbp), %rax
	0x3d, 0x02, 0xfc, 0xff, 0xff, //0x000023b4 cmpl         $-1022, %eax
	0x0f, 0x8f, 0xba, 0x01, 0x00, 0x00, //0x000023b9 jg           LBB0_545
	0x45, 0x85, 0xff, //0x000023bf testl        %r15d, %r15d
	0x0f, 0x84, 0xc4, 0x01, 0x00, 0x00, //0x000023c2 je           LBB0_547
	0xb9, 0x02, 0xfc, 0xff, 0xff, //0x000023c8 movl         $-1022, %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x000023cd movq         %rcx, $-56(%rbp)
	0x44, 0x8d, 0x80, 0xfd, 0x03, 0x00, 0x00, //0x000023d1 leal         $1021(%rax), %r8d
	0x3d, 0xc6, 0xfb, 0xff, 0xff, //0x000023d8 cmpl         $-1082, %eax
	0x0f, 0x87, 0xba, 0x01, 0x00, 0x00, //0x000023dd ja           LBB0_548
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x000023e3 movl         $1, %r9d
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x000023e9 jmp          LBB0_521
	//0x000023ee LBB0_541
	0x0f, 0x84, 0x77, 0x01, 0x00, 0x00, //0x000023ee je           LBB0_543
	//0x000023f4 LBB0_519
	0x48, 0x89, 0x55, 0xd0, //0x000023f4 movq         %rdx, $-48(%rbp)
	//0x000023f8 LBB0_520
	0x41, 0x8d, 0x48, 0x3c, //0x000023f8 leal         $60(%r8), %ecx
	0x45, 0x89, 0xef, //0x000023fc movl         %r13d, %r15d
	0x44, 0x89, 0xef, //0x000023ff movl         %r13d, %edi
	0x41, 0x83, 0xf8, 0x88, //0x00002402 cmpl         $-120, %r8d
	0x41, 0x89, 0xc8, //0x00002406 movl         %ecx, %r8d
	0x0f, 0x8d, 0x94, 0x01, 0x00, 0x00, //0x00002409 jge          LBB0_549
	//0x0000240f LBB0_521
	0x45, 0x85, 0xff, //0x0000240f testl        %r15d, %r15d
	0xbb, 0x00, 0x00, 0x00, 0x00, //0x00002412 movl         $0, %ebx
	0x41, 0x0f, 0x4f, 0xdf, //0x00002417 cmovgl       %r15d, %ebx
	0x31, 0xff, //0x0000241b xorl         %edi, %edi
	0x31, 0xc9, //0x0000241d xorl         %ecx, %ecx
	//0x0000241f LBB0_522
	0x48, 0x39, 0xfb, //0x0000241f cmpq         %rdi, %rbx
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00002422 je           LBB0_525
	0x48, 0x8d, 0x04, 0x89, //0x00002428 leaq         (%rcx,%rcx,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x3e, //0x0000242c movsbq       (%r14,%rdi), %rcx
	0x48, 0x8d, 0x0c, 0x41, //0x00002431 leaq         (%rcx,%rax,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00002435 addq         $-48, %rcx
	0x48, 0x83, 0xc7, 0x01, //0x00002439 addq         $1, %rdi
	0x4c, 0x39, 0xd9, //0x0000243d cmpq         %r11, %rcx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x00002440 jb           LBB0_522
	0x89, 0xfb, //0x00002446 movl         %edi, %ebx
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00002448 jmp          LBB0_527
	//0x0000244d LBB0_525
	0x48, 0x85, 0xc9, //0x0000244d testq        %rcx, %rcx
	0x0f, 0x84, 0x1b, 0x01, 0x00, 0x00, //0x00002450 je           LBB0_544
	//0x00002456 LBB0_526
	0x48, 0x01, 0xc9, //0x00002456 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002459 leaq         (%rcx,%rcx,4), %rcx
	0x83, 0xc3, 0x01, //0x0000245d addl         $1, %ebx
	0x4c, 0x39, 0xd9, //0x00002460 cmpq         %r11, %rcx
	0x0f, 0x82, 0xed, 0xff, 0xff, 0xff, //0x00002463 jb           LBB0_526
	//0x00002469 LBB0_527
	0x48, 0x8b, 0x45, 0xd0, //0x00002469 movq         $-48(%rbp), %rax
	0x29, 0xd8, //0x0000246d subl         %ebx, %eax
	0x48, 0x89, 0x45, 0xd0, //0x0000246f movq         %rax, $-48(%rbp)
	0x31, 0xff, //0x00002473 xorl         %edi, %edi
	0x44, 0x39, 0xfb, //0x00002475 cmpl         %r15d, %ebx
	0x0f, 0x8d, 0x51, 0x00, 0x00, 0x00, //0x00002478 jge          LBB0_532
	0x48, 0x63, 0xc3, //0x0000247e movslq       %ebx, %rax
	0x49, 0x63, 0xfd, //0x00002481 movslq       %r13d, %rdi
	0x49, 0x8d, 0x1c, 0x06, //0x00002484 leaq         (%r14,%rax), %rbx
	0x45, 0x31, 0xed, //0x00002488 xorl         %r13d, %r13d
	//0x0000248b LBB0_529
	0x49, 0x8d, 0x53, 0xff, //0x0000248b leaq         $-1(%r11), %rdx
	0x48, 0x21, 0xca, //0x0000248f andq         %rcx, %rdx
	0x48, 0xc1, 0xe9, 0x3c, //0x00002492 shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x00002496 orb          $48, %cl
	0x43, 0x88, 0x0c, 0x2e, //0x00002499 movb         %cl, (%r14,%r13)
	0x4a, 0x0f, 0xbe, 0x0c, 0x2b, //0x0000249d movsbq       (%rbx,%r13), %rcx
	0x4a, 0x8d, 0x34, 0x28, //0x000024a2 leaq         (%rax,%r13), %rsi
	0x48, 0x83, 0xc6, 0x01, //0x000024a6 addq         $1, %rsi
	0x49, 0x83, 0xc5, 0x01, //0x000024aa addq         $1, %r13
	0x48, 0x8d, 0x14, 0x92, //0x000024ae leaq         (%rdx,%rdx,4), %rdx
	0x48, 0x8d, 0x0c, 0x51, //0x000024b2 leaq         (%rcx,%rdx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x000024b6 addq         $-48, %rcx
	0x48, 0x39, 0xfe, //0x000024ba cmpq         %rdi, %rsi
	0x0f, 0x8c, 0xc8, 0xff, 0xff, 0xff, //0x000024bd jl           LBB0_529
	0x48, 0x85, 0xc9, //0x000024c3 testq        %rcx, %rcx
	0x0f, 0x84, 0x58, 0x00, 0x00, 0x00, //0x000024c6 je           LBB0_536
	0x44, 0x89, 0xef, //0x000024cc movl         %r13d, %edi
	//0x000024cf LBB0_532
	0x41, 0x89, 0xfd, //0x000024cf movl         %edi, %r13d
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x000024d2 jmp          LBB0_534
	//0x000024d7 LBB0_533
	0x4c, 0x39, 0xd9, //0x000024d7 cmpq         %r11, %rcx
	0x45, 0x0f, 0x43, 0xd1, //0x000024da cmovael      %r9d, %r10d
	0x48, 0x8d, 0x0c, 0x00, //0x000024de leaq         (%rax,%rax), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000024e2 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc0, //0x000024e6 testq        %rax, %rax
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x000024e9 je           LBB0_536
	//0x000024ef LBB0_534
	0x49, 0x8d, 0x43, 0xff, //0x000024ef leaq         $-1(%r11), %rax
	0x48, 0x21, 0xc8, //0x000024f3 andq         %rcx, %rax
	0x49, 0x63, 0xfd, //0x000024f6 movslq       %r13d, %rdi
	0x49, 0x39, 0xfc, //0x000024f9 cmpq         %rdi, %r12
	0x0f, 0x86, 0xd5, 0xff, 0xff, 0xff, //0x000024fc jbe          LBB0_533
	0x48, 0xc1, 0xe9, 0x3c, //0x00002502 shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x00002506 orb          $48, %cl
	0x41, 0x88, 0x0c, 0x3e, //0x00002509 movb         %cl, (%r14,%rdi)
	0x83, 0xc7, 0x01, //0x0000250d addl         $1, %edi
	0x41, 0x89, 0xfd, //0x00002510 movl         %edi, %r13d
	0x48, 0x8d, 0x0c, 0x00, //0x00002513 leaq         (%rax,%rax), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002517 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc0, //0x0000251b testq        %rax, %rax
	0x0f, 0x85, 0xcb, 0xff, 0xff, 0xff, //0x0000251e jne          LBB0_534
	//0x00002524 LBB0_536
	0x48, 0x8b, 0x55, 0xd0, //0x00002524 movq         $-48(%rbp), %rdx
	0x83, 0xc2, 0x01, //0x00002528 addl         $1, %edx
	0x45, 0x85, 0xed, //0x0000252b testl        %r13d, %r13d
	0x0f, 0x8e, 0xba, 0xfe, 0xff, 0xff, //0x0000252e jle          LBB0_541
	0x44, 0x89, 0xe8, //0x00002534 movl         %r13d, %eax
	0x42, 0x80, 0x7c, 0x30, 0xff, 0x30, //0x00002537 cmpb         $48, $-1(%rax,%r14)
	0x0f, 0x85, 0xb1, 0xfe, 0xff, 0xff, //0x0000253d jne          LBB0_519
	//0x00002543 LBB0_538
	0x48, 0x83, 0xf8, 0x01, //0x00002543 cmpq         $1, %rax
	0x0f, 0x86, 0x1e, 0x00, 0x00, 0x00, //0x00002547 jbe          LBB0_543
	0x8d, 0x48, 0xfe, //0x0000254d leal         $-2(%rax), %ecx
	0x48, 0x83, 0xc0, 0xff, //0x00002550 addq         $-1, %rax
	0x41, 0x80, 0x3c, 0x0e, 0x30, //0x00002554 cmpb         $48, (%r14,%rcx)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00002559 je           LBB0_538
	0x48, 0x89, 0x55, 0xd0, //0x0000255f movq         %rdx, $-48(%rbp)
	0x41, 0x89, 0xc5, //0x00002563 movl         %eax, %r13d
	0xe9, 0x8d, 0xfe, 0xff, 0xff, //0x00002566 jmp          LBB0_520
	//0x0000256b LBB0_543
	0x31, 0xc0, //0x0000256b xorl         %eax, %eax
	0x48, 0x89, 0x45, 0xd0, //0x0000256d movq         %rax, $-48(%rbp)
	//0x00002571 LBB0_544
	0x45, 0x31, 0xed, //0x00002571 xorl         %r13d, %r13d
	0xe9, 0x7f, 0xfe, 0xff, 0xff, //0x00002574 jmp          LBB0_520
	//0x00002579 LBB0_545
	0x3d, 0x00, 0x04, 0x00, 0x00, //0x00002579 cmpl         $1024, %eax
	0x0f, 0x8f, 0x99, 0x06, 0x00, 0x00, //0x0000257e jg           LBB0_650
	0x83, 0xc0, 0xff, //0x00002584 addl         $-1, %eax
	0xe9, 0xbb, 0x02, 0x00, 0x00, //0x00002587 jmp          LBB0_583
	//0x0000258c LBB0_547
	0xb8, 0x02, 0xfc, 0xff, 0xff, //0x0000258c movl         $-1022, %eax
	0x48, 0x89, 0x45, 0xc8, //0x00002591 movq         %rax, $-56(%rbp)
	0x45, 0x31, 0xc9, //0x00002595 xorl         %r9d, %r9d
	0xe9, 0x88, 0x04, 0x00, 0x00, //0x00002598 jmp          LBB0_617
	//0x0000259d LBB0_548
	0x44, 0x89, 0xff, //0x0000259d movl         %r15d, %edi
	0x44, 0x89, 0xc1, //0x000025a0 movl         %r8d, %ecx
	//0x000025a3 LBB0_549
	0xf7, 0xd9, //0x000025a3 negl         %ecx
	0x31, 0xd2, //0x000025a5 xorl         %edx, %edx
	0x85, 0xff, //0x000025a7 testl        %edi, %edi
	0xbe, 0x00, 0x00, 0x00, 0x00, //0x000025a9 movl         $0, %esi
	0x0f, 0x4f, 0xf7, //0x000025ae cmovgl       %edi, %esi
	0x31, 0xc0, //0x000025b1 xorl         %eax, %eax
	//0x000025b3 LBB0_550
	0x48, 0x39, 0xd6, //0x000025b3 cmpq         %rdx, %rsi
	0x0f, 0x84, 0x9d, 0x00, 0x00, 0x00, //0x000025b6 je           LBB0_557
	0x48, 0x8d, 0x04, 0x80, //0x000025bc leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x1c, 0x16, //0x000025c0 movsbq       (%r14,%rdx), %rbx
	0x48, 0x8d, 0x04, 0x43, //0x000025c5 leaq         (%rbx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x000025c9 addq         $-48, %rax
	0x48, 0x83, 0xc2, 0x01, //0x000025cd addq         $1, %rdx
	0x48, 0x89, 0xc3, //0x000025d1 movq         %rax, %rbx
	0x48, 0xd3, 0xeb, //0x000025d4 shrq         %cl, %rbx
	0x48, 0x85, 0xdb, //0x000025d7 testq        %rbx, %rbx
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x000025da je           LBB0_550
	0x89, 0xd6, //0x000025e0 movl         %edx, %esi
	//0x000025e2 LBB0_553
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000025e2 movq         $-1, %rdx
	0x48, 0xd3, 0xe2, //0x000025e9 shlq         %cl, %rdx
	0x48, 0xf7, 0xd2, //0x000025ec notq         %rdx
	0x45, 0x31, 0xc9, //0x000025ef xorl         %r9d, %r9d
	0x39, 0xfe, //0x000025f2 cmpl         %edi, %esi
	0x0f, 0x8d, 0x44, 0x00, 0x00, 0x00, //0x000025f4 jge          LBB0_556
	0x4c, 0x63, 0xde, //0x000025fa movslq       %esi, %r11
	0x4d, 0x63, 0xc5, //0x000025fd movslq       %r13d, %r8
	0x4f, 0x8d, 0x3c, 0x1e, //0x00002600 leaq         (%r14,%r11), %r15
	0x45, 0x31, 0xc9, //0x00002604 xorl         %r9d, %r9d
	//0x00002607 LBB0_555
	0x48, 0x89, 0xc7, //0x00002607 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x0000260a shrq         %cl, %rdi
	0x48, 0x21, 0xd0, //0x0000260d andq         %rdx, %rax
	0x40, 0x80, 0xc7, 0x30, //0x00002610 addb         $48, %dil
	0x43, 0x88, 0x3c, 0x0e, //0x00002614 movb         %dil, (%r14,%r9)
	0x4b, 0x0f, 0xbe, 0x3c, 0x0f, //0x00002618 movsbq       (%r15,%r9), %rdi
	0x4b, 0x8d, 0x1c, 0x0b, //0x0000261d leaq         (%r11,%r9), %rbx
	0x48, 0x83, 0xc3, 0x01, //0x00002621 addq         $1, %rbx
	0x49, 0x83, 0xc1, 0x01, //0x00002625 addq         $1, %r9
	0x48, 0x8d, 0x04, 0x80, //0x00002629 leaq         (%rax,%rax,4), %rax
	0x48, 0x8d, 0x04, 0x47, //0x0000262d leaq         (%rdi,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00002631 addq         $-48, %rax
	0x4c, 0x39, 0xc3, //0x00002635 cmpq         %r8, %rbx
	0x0f, 0x8c, 0xc9, 0xff, 0xff, 0xff, //0x00002638 jl           LBB0_555
	//0x0000263e LBB0_556
	0x48, 0x8b, 0x7d, 0xd0, //0x0000263e movq         $-48(%rbp), %rdi
	0x29, 0xf7, //0x00002642 subl         %esi, %edi
	0x83, 0xc7, 0x01, //0x00002644 addl         $1, %edi
	0x48, 0x89, 0x7d, 0xd0, //0x00002647 movq         %rdi, $-48(%rbp)
	0x48, 0x85, 0xc0, //0x0000264b testq        %rax, %rax
	0x0f, 0x85, 0x3a, 0x00, 0x00, 0x00, //0x0000264e jne          LBB0_560
	0xe9, 0x8e, 0x00, 0x00, 0x00, //0x00002654 jmp          LBB0_564
	//0x00002659 LBB0_557
	0x48, 0x85, 0xc0, //0x00002659 testq        %rax, %rax
	0x0f, 0x84, 0xa4, 0x02, 0x00, 0x00, //0x0000265c je           LBB0_595
	0x48, 0x89, 0xc2, //0x00002662 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00002665 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00002668 testq        %rdx, %rdx
	0x0f, 0x84, 0xbc, 0x00, 0x00, 0x00, //0x0000266b je           LBB0_568
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002671 movq         $-1, %rdx
	0x48, 0xd3, 0xe2, //0x00002678 shlq         %cl, %rdx
	0x48, 0xf7, 0xd2, //0x0000267b notq         %rdx
	0x48, 0x8b, 0x7d, 0xd0, //0x0000267e movq         $-48(%rbp), %rdi
	0x29, 0xf7, //0x00002682 subl         %esi, %edi
	0x83, 0xc7, 0x01, //0x00002684 addl         $1, %edi
	0x48, 0x89, 0x7d, 0xd0, //0x00002687 movq         %rdi, $-48(%rbp)
	0x45, 0x31, 0xc9, //0x0000268b xorl         %r9d, %r9d
	//0x0000268e LBB0_560
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000268e movl         $1, %r8d
	0x48, 0x8b, 0x75, 0xb0, //0x00002694 movq         $-80(%rbp), %rsi
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00002698 jmp          LBB0_562
	//0x0000269d LBB0_561
	0x48, 0x85, 0xff, //0x0000269d testq        %rdi, %rdi
	0x45, 0x0f, 0x45, 0xd0, //0x000026a0 cmovnel      %r8d, %r10d
	0x48, 0x01, 0xc0, //0x000026a4 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x000026a7 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x000026ab testq        %rax, %rax
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x000026ae je           LBB0_564
	//0x000026b4 LBB0_562
	0x48, 0x89, 0xc7, //0x000026b4 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x000026b7 shrq         %cl, %rdi
	0x48, 0x21, 0xd0, //0x000026ba andq         %rdx, %rax
	0x49, 0x63, 0xd9, //0x000026bd movslq       %r9d, %rbx
	0x48, 0x39, 0xde, //0x000026c0 cmpq         %rbx, %rsi
	0x0f, 0x86, 0xd4, 0xff, 0xff, 0xff, //0x000026c3 jbe          LBB0_561
	0x40, 0x80, 0xc7, 0x30, //0x000026c9 addb         $48, %dil
	0x41, 0x88, 0x3c, 0x1e, //0x000026cd movb         %dil, (%r14,%rbx)
	0x83, 0xc3, 0x01, //0x000026d1 addl         $1, %ebx
	0x41, 0x89, 0xd9, //0x000026d4 movl         %ebx, %r9d
	0x48, 0x01, 0xc0, //0x000026d7 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x000026da leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x000026de testq        %rax, %rax
	0x0f, 0x85, 0xcd, 0xff, 0xff, 0xff, //0x000026e1 jne          LBB0_562
	//0x000026e7 LBB0_564
	0x45, 0x85, 0xc9, //0x000026e7 testl        %r9d, %r9d
	0x0f, 0x8e, 0x0a, 0x01, 0x00, 0x00, //0x000026ea jle          LBB0_570
	0x44, 0x89, 0xc8, //0x000026f0 movl         %r9d, %eax
	0xb9, 0x02, 0xfc, 0xff, 0xff, //0x000026f3 movl         $-1022, %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x000026f8 movq         %rcx, $-56(%rbp)
	0x42, 0x80, 0x7c, 0x30, 0xff, 0x30, //0x000026fc cmpb         $48, $-1(%rax,%r14)
	0x0f, 0x85, 0x01, 0x01, 0x00, 0x00, //0x00002702 jne          LBB0_573
	//0x00002708 LBB0_566
	0x49, 0x89, 0xc5, //0x00002708 movq         %rax, %r13
	0x48, 0x83, 0xf8, 0x01, //0x0000270b cmpq         $1, %rax
	0x0f, 0x86, 0x20, 0x01, 0x00, 0x00, //0x0000270f jbe          LBB0_581
	0x41, 0x8d, 0x4d, 0xfe, //0x00002715 leal         $-2(%r13), %ecx
	0x49, 0x8d, 0x45, 0xff, //0x00002719 leaq         $-1(%r13), %rax
	0x41, 0x80, 0x3c, 0x0e, 0x30, //0x0000271d cmpb         $48, (%r14,%rcx)
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00002722 je           LBB0_566
	0xe9, 0x0e, 0x01, 0x00, 0x00, //0x00002728 jmp          LBB0_582
	//0x0000272d LBB0_568
	0x48, 0x01, 0xc0, //0x0000272d addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00002730 leaq         (%rax,%rax,4), %rax
	0x83, 0xc6, 0x01, //0x00002734 addl         $1, %esi
	0x48, 0x89, 0xc2, //0x00002737 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x0000273a shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x0000273d testq        %rdx, %rdx
	0x0f, 0x84, 0xe7, 0xff, 0xff, 0xff, //0x00002740 je           LBB0_568
	0xe9, 0x97, 0xfe, 0xff, 0xff, //0x00002746 jmp          LBB0_553
	//0x0000274b LBB0_569
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000274b movq         $-1, %r9
	0x49, 0x29, 0xfc, //0x00002752 subq         %rdi, %r12
	0x4d, 0x89, 0x4d, 0x00, //0x00002755 movq         %r9, (%r13)
	0xe9, 0x64, 0xdb, 0xff, 0xff, //0x00002759 jmp          LBB0_38
	//0x0000275e LBB0_575
	0x48, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, //0x0000275e movabsq      $18014398509481984, %rsi
	//0x00002768 LBB0_576
	0x48, 0x89, 0xd8, //0x00002768 movq         %rbx, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x0000276b shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x0000276f leal         $9(%rax), %ecx
	0x48, 0xd3, 0xeb, //0x00002772 shrq         %cl, %rbx
	0x4d, 0x85, 0xe4, //0x00002775 testq        %r12, %r12
	0x0f, 0x85, 0x17, 0x00, 0x00, 0x00, //0x00002778 jne          LBB0_579
	0x48, 0x85, 0xd2, //0x0000277e testq        %rdx, %rdx
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00002781 jne          LBB0_579
	0x89, 0xd9, //0x00002787 movl         %ebx, %ecx
	0x83, 0xe1, 0x03, //0x00002789 andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x0000278c cmpl         $1, %ecx
	0x0f, 0x84, 0x4b, 0xeb, 0xff, 0xff, //0x0000278f je           LBB0_265
	//0x00002795 LBB0_579
	0x4d, 0x29, 0xf8, //0x00002795 subq         %r15, %r8
	0x89, 0xda, //0x00002798 movl         %ebx, %edx
	0x83, 0xe2, 0x01, //0x0000279a andl         $1, %edx
	0x48, 0x01, 0xda, //0x0000279d addq         %rbx, %rdx
	0x49, 0x01, 0xc0, //0x000027a0 addq         %rax, %r8
	0x48, 0x39, 0xf2, //0x000027a3 cmpq         %rsi, %rdx
	0x49, 0x83, 0xd8, 0x00, //0x000027a6 sbbq         $0, %r8
	0x49, 0x8d, 0x80, 0x01, 0xf8, 0xff, 0xff, //0x000027aa leaq         $-2047(%r8), %rax
	0x48, 0x3d, 0x02, 0xf8, 0xff, 0xff, //0x000027b1 cmpq         $-2046, %rax
	0x0f, 0x82, 0x23, 0xeb, 0xff, 0xff, //0x000027b7 jb           LBB0_265
	0x48, 0x39, 0xf2, //0x000027bd cmpq         %rsi, %rdx
	0xb1, 0x02, //0x000027c0 movb         $2, %cl
	0x80, 0xd9, 0x00, //0x000027c2 sbbb         $0, %cl
	0x48, 0xd3, 0xea, //0x000027c5 shrq         %cl, %rdx
	0x49, 0xc1, 0xe0, 0x34, //0x000027c8 shlq         $52, %r8
	0x4c, 0x21, 0xda, //0x000027cc andq         %r11, %rdx
	0x4c, 0x09, 0xc2, //0x000027cf orq          %r8, %rdx
	0x48, 0x89, 0xd0, //0x000027d2 movq         %rdx, %rax
	0x48, 0x09, 0xf8, //0x000027d5 orq          %rdi, %rax
	0x80, 0x7d, 0xc8, 0x2d, //0x000027d8 cmpb         $45, $-56(%rbp)
	0x48, 0x0f, 0x45, 0xc2, //0x000027dc cmovneq      %rdx, %rax
	0x66, 0x48, 0x0f, 0x6e, 0xc8, //0x000027e0 movq         %rax, %xmm1
	0x66, 0x0f, 0x2e, 0xc1, //0x000027e5 ucomisd      %xmm1, %xmm0
	0x0f, 0x85, 0xf1, 0xea, 0xff, 0xff, //0x000027e9 jne          LBB0_265
	0x0f, 0x8b, 0x67, 0x04, 0x00, 0x00, //0x000027ef jnp          LBB0_653
	0xe9, 0xe6, 0xea, 0xff, 0xff, //0x000027f5 jmp          LBB0_265
	//0x000027fa LBB0_570
	0xb8, 0x02, 0xfc, 0xff, 0xff, //0x000027fa movl         $-1022, %eax
	0x48, 0x89, 0x45, 0xc8, //0x000027ff movq         %rax, $-56(%rbp)
	0x0f, 0x84, 0x99, 0x04, 0x00, 0x00, //0x00002803 je           LBB0_657
	//0x00002809 LBB0_573
	0x45, 0x89, 0xcf, //0x00002809 movl         %r9d, %r15d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x0000280c jmp          LBB0_585
	//0x00002811 LBB0_572
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002811 movq         $-1, %r10
	0x4c, 0x89, 0xe1, //0x00002818 movq         %r12, %rcx
	0x4c, 0x89, 0xfb, //0x0000281b movq         %r15, %rbx
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x0000281e movq         $-1, %r13
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00002825 movq         $-1, %r11
	0x48, 0x8b, 0x7d, 0xb8, //0x0000282c movq         $-72(%rbp), %rdi
	0xe9, 0xaa, 0xdc, 0xff, 0xff, //0x00002830 jmp          LBB0_64
	//0x00002835 LBB0_581
	0x31, 0xc0, //0x00002835 xorl         %eax, %eax
	0x48, 0x89, 0x45, 0xd0, //0x00002837 movq         %rax, $-48(%rbp)
	//0x0000283b LBB0_582
	0x41, 0x83, 0xc5, 0xff, //0x0000283b addl         $-1, %r13d
	0xb8, 0x02, 0xfc, 0xff, 0xff, //0x0000283f movl         $-1022, %eax
	0x45, 0x89, 0xef, //0x00002844 movl         %r13d, %r15d
	//0x00002847 LBB0_583
	0x48, 0x89, 0x45, 0xc8, //0x00002847 movq         %rax, $-56(%rbp)
	0x45, 0x85, 0xff, //0x0000284b testl        %r15d, %r15d
	0x0f, 0x84, 0xb2, 0x00, 0x00, 0x00, //0x0000284e je           LBB0_595
	0x45, 0x89, 0xe9, //0x00002854 movl         %r13d, %r9d
	//0x00002857 LBB0_585
	0x49, 0x63, 0xcf, //0x00002857 movslq       %r15d, %rcx
	0x4c, 0x8d, 0x41, 0xfe, //0x0000285a leaq         $-2(%rcx), %r8
	0x48, 0x8d, 0x71, 0xff, //0x0000285e leaq         $-1(%rcx), %rsi
	0x31, 0xff, //0x00002862 xorl         %edi, %edi
	//0x00002864 LBB0_586
	0x48, 0x8d, 0x05, 0xf5, 0x36, 0x00, 0x00, //0x00002864 leaq         $14069(%rip), %rax  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x9c, 0x07, 0x8c, 0x15, 0x00, 0x00, //0x0000286b movzbl       $5516(%rdi,%rax), %ebx
	0x41, 0x0f, 0xb6, 0x14, 0x3e, //0x00002873 movzbl       (%r14,%rdi), %edx
	0x38, 0xda, //0x00002878 cmpb         %bl, %dl
	0x0f, 0x85, 0x8e, 0x00, 0x00, 0x00, //0x0000287a jne          LBB0_596
	0x48, 0x39, 0xfe, //0x00002880 cmpq         %rdi, %rsi
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x00002883 je           LBB0_593
	0x48, 0x8d, 0x05, 0xd0, 0x36, 0x00, 0x00, //0x00002889 leaq         $14032(%rip), %rax  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x9c, 0x07, 0x8d, 0x15, 0x00, 0x00, //0x00002890 movzbl       $5517(%rdi,%rax), %ebx
	0x41, 0x0f, 0xb6, 0x54, 0x3e, 0x01, //0x00002898 movzbl       $1(%r14,%rdi), %edx
	0x38, 0xda, //0x0000289e cmpb         %bl, %dl
	0x0f, 0x85, 0x68, 0x00, 0x00, 0x00, //0x000028a0 jne          LBB0_596
	0x49, 0x39, 0xf8, //0x000028a6 cmpq         %rdi, %r8
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x000028a9 je           LBB0_593
	0x48, 0x83, 0xff, 0x24, //0x000028af cmpq         $36, %rdi
	0x0f, 0x84, 0x42, 0x00, 0x00, 0x00, //0x000028b3 je           LBB0_594
	0x48, 0x8d, 0x05, 0xa0, 0x36, 0x00, 0x00, //0x000028b9 leaq         $13984(%rip), %rax  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x9c, 0x07, 0x8e, 0x15, 0x00, 0x00, //0x000028c0 movzbl       $5518(%rdi,%rax), %ebx
	0x41, 0x0f, 0xb6, 0x54, 0x3e, 0x02, //0x000028c8 movzbl       $2(%r14,%rdi), %edx
	0x38, 0xda, //0x000028ce cmpb         %bl, %dl
	0x0f, 0x85, 0x38, 0x00, 0x00, 0x00, //0x000028d0 jne          LBB0_596
	0x48, 0x83, 0xc7, 0x03, //0x000028d6 addq         $3, %rdi
	0x48, 0x39, 0xf9, //0x000028da cmpq         %rdi, %rcx
	0x0f, 0x85, 0x81, 0xff, 0xff, 0xff, //0x000028dd jne          LBB0_586
	//0x000028e3 LBB0_593
	0x44, 0x89, 0xf8, //0x000028e3 movl         %r15d, %eax
	0x48, 0x8d, 0x0d, 0x73, 0x36, 0x00, 0x00, //0x000028e6 leaq         $13939(%rip), %rcx  /* _LSHIFT_TAB+0(%rip) */
	0x80, 0xbc, 0x08, 0x8c, 0x15, 0x00, 0x00, 0x00, //0x000028ed cmpb         $0, $5516(%rax,%rcx)
	0x0f, 0x85, 0x21, 0x00, 0x00, 0x00, //0x000028f5 jne          LBB0_597
	//0x000028fb LBB0_594
	0x41, 0xbd, 0x10, 0x00, 0x00, 0x00, //0x000028fb movl         $16, %r13d
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00002901 jmp          LBB0_598
	//0x00002906 LBB0_595
	0x45, 0x31, 0xc9, //0x00002906 xorl         %r9d, %r9d
	0xe9, 0x17, 0x01, 0x00, 0x00, //0x00002909 jmp          LBB0_617
	//0x0000290e LBB0_596
	0x41, 0xbd, 0x10, 0x00, 0x00, 0x00, //0x0000290e movl         $16, %r13d
	0x38, 0xda, //0x00002914 cmpb         %bl, %dl
	0x0f, 0x8d, 0x06, 0x00, 0x00, 0x00, //0x00002916 jge          LBB0_598
	//0x0000291c LBB0_597
	0x41, 0xbd, 0x0f, 0x00, 0x00, 0x00, //0x0000291c movl         $15, %r13d
	//0x00002922 LBB0_598
	0x45, 0x85, 0xff, //0x00002922 testl        %r15d, %r15d
	0x0f, 0x8e, 0x9c, 0x00, 0x00, 0x00, //0x00002925 jle          LBB0_606
	0x47, 0x8d, 0x1c, 0x2f, //0x0000292b leal         (%r15,%r13), %r11d
	0x44, 0x89, 0xfb, //0x0000292f movl         %r15d, %ebx
	0x49, 0x63, 0xfb, //0x00002932 movslq       %r11d, %rdi
	0x48, 0x83, 0xc7, 0xff, //0x00002935 addq         $-1, %rdi
	0x48, 0x83, 0xc3, 0x01, //0x00002939 addq         $1, %rbx
	0x31, 0xc9, //0x0000293d xorl         %ecx, %ecx
	0x49, 0xbc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x0000293f movabsq      $-432345564227567616, %r12
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00002949 movl         $1, %r8d
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x0000294f jmp          LBB0_602
	//0x00002954 LBB0_600
	0x48, 0x85, 0xc0, //0x00002954 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xd0, //0x00002957 cmovnel      %r8d, %r10d
	//0x0000295b LBB0_601
	0x41, 0x83, 0xc3, 0xff, //0x0000295b addl         $-1, %r11d
	0x48, 0x83, 0xc7, 0xff, //0x0000295f addq         $-1, %rdi
	0x48, 0x83, 0xc3, 0xff, //0x00002963 addq         $-1, %rbx
	0x48, 0x83, 0xfb, 0x01, //0x00002967 cmpq         $1, %rbx
	0x0f, 0x86, 0x4c, 0x00, 0x00, 0x00, //0x0000296b jbe          LBB0_604
	//0x00002971 LBB0_602
	0x8d, 0x43, 0xfe, //0x00002971 leal         $-2(%rbx), %eax
	0x49, 0x0f, 0xbe, 0x34, 0x06, //0x00002974 movsbq       (%r14,%rax), %rsi
	0x48, 0xc1, 0xe6, 0x35, //0x00002979 shlq         $53, %rsi
	0x48, 0x01, 0xce, //0x0000297d addq         %rcx, %rsi
	0x4c, 0x01, 0xe6, //0x00002980 addq         %r12, %rsi
	0x48, 0x89, 0xf0, //0x00002983 movq         %rsi, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002986 movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x00002990 mulq         %rcx
	0x48, 0x89, 0xd1, //0x00002993 movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x00002996 shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x0000299a leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x0000299e leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf0, //0x000029a2 movq         %rsi, %rax
	0x48, 0x29, 0xd0, //0x000029a5 subq         %rdx, %rax
	0x48, 0x3b, 0x7d, 0xb0, //0x000029a8 cmpq         $-80(%rbp), %rdi
	0x0f, 0x83, 0xa2, 0xff, 0xff, 0xff, //0x000029ac jae          LBB0_600
	0x04, 0x30, //0x000029b2 addb         $48, %al
	0x41, 0x88, 0x04, 0x3e, //0x000029b4 movb         %al, (%r14,%rdi)
	0xe9, 0x9e, 0xff, 0xff, 0xff, //0x000029b8 jmp          LBB0_601
	//0x000029bd LBB0_604
	0x48, 0x83, 0xfe, 0x0a, //0x000029bd cmpq         $10, %rsi
	0x0f, 0x83, 0x5b, 0x01, 0x00, 0x00, //0x000029c1 jae          LBB0_607
	//0x000029c7 LBB0_606
	0x48, 0x8b, 0x7d, 0xb0, //0x000029c7 movq         $-80(%rbp), %rdi
	//0x000029cb LBB0_611
	0x45, 0x01, 0xe9, //0x000029cb addl         %r13d, %r9d
	0x4d, 0x63, 0xc9, //0x000029ce movslq       %r9d, %r9
	0x4c, 0x39, 0xcf, //0x000029d1 cmpq         %r9, %rdi
	0x44, 0x0f, 0x46, 0xcf, //0x000029d4 cmovbel      %edi, %r9d
	0x48, 0x8b, 0x45, 0xd0, //0x000029d8 movq         $-48(%rbp), %rax
	0x44, 0x01, 0xe8, //0x000029dc addl         %r13d, %eax
	0x45, 0x85, 0xc9, //0x000029df testl        %r9d, %r9d
	0x48, 0x89, 0x45, 0xd0, //0x000029e2 movq         %rax, $-48(%rbp)
	0x0f, 0x8e, 0x33, 0x00, 0x00, 0x00, //0x000029e6 jle          LBB0_616
	0x44, 0x89, 0xca, //0x000029ec movl         %r9d, %edx
	0x42, 0x80, 0x7c, 0x32, 0xff, 0x30, //0x000029ef cmpb         $48, $-1(%rdx,%r14)
	0x0f, 0x85, 0x2a, 0x00, 0x00, 0x00, //0x000029f5 jne          LBB0_617
	//0x000029fb LBB0_613
	0x48, 0x83, 0xfa, 0x01, //0x000029fb cmpq         $1, %rdx
	0x0f, 0x86, 0x92, 0x01, 0x00, 0x00, //0x000029ff jbe          LBB0_637
	0x8d, 0x42, 0xfe, //0x00002a05 leal         $-2(%rdx), %eax
	0x48, 0x83, 0xc2, 0xff, //0x00002a08 addq         $-1, %rdx
	0x41, 0x80, 0x3c, 0x06, 0x30, //0x00002a0c cmpb         $48, (%r14,%rax)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00002a11 je           LBB0_613
	0x41, 0x89, 0xd1, //0x00002a17 movl         %edx, %r9d
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00002a1a jmp          LBB0_617
	//0x00002a1f LBB0_616
	0x0f, 0x84, 0xcc, 0x01, 0x00, 0x00, //0x00002a1f je           LBB0_646
	//0x00002a25 LBB0_617
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002a25 movq         $-64(%rbp), %r13
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00002a29 movabsq      $-9223372036854775808, %rdi
	0x48, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002a33 movabsq      $4503599627370495, %rsi
	0x48, 0x8b, 0x45, 0xd0, //0x00002a3d movq         $-48(%rbp), %rax
	0x83, 0xf8, 0x14, //0x00002a41 cmpl         $20, %eax
	0x0f, 0x8e, 0x0f, 0x00, 0x00, 0x00, //0x00002a44 jle          LBB0_619
	0x8a, 0x55, 0xa0, //0x00002a4a movb         $-96(%rbp), %dl
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00002a4d movq         $-1, %rbx
	0xe9, 0x66, 0x02, 0x00, 0x00, //0x00002a54 jmp          LBB0_659
	//0x00002a59 LBB0_619
	0x85, 0xc0, //0x00002a59 testl        %eax, %eax
	0x0f, 0x8e, 0x54, 0x00, 0x00, 0x00, //0x00002a5b jle          LBB0_625
	0x31, 0xf6, //0x00002a61 xorl         %esi, %esi
	0x45, 0x85, 0xc9, //0x00002a63 testl        %r9d, %r9d
	0xbf, 0x00, 0x00, 0x00, 0x00, //0x00002a66 movl         $0, %edi
	0x41, 0x0f, 0x4f, 0xf9, //0x00002a6b cmovgl       %r9d, %edi
	0x89, 0xc3, //0x00002a6f movl         %eax, %ebx
	0x48, 0x8d, 0x53, 0xff, //0x00002a71 leaq         $-1(%rbx), %rdx
	0x48, 0x39, 0xfa, //0x00002a75 cmpq         %rdi, %rdx
	0x48, 0x0f, 0x43, 0xd7, //0x00002a78 cmovaeq      %rdi, %rdx
	0x4c, 0x8d, 0x42, 0x01, //0x00002a7c leaq         $1(%rdx), %r8
	0x31, 0xc9, //0x00002a80 xorl         %ecx, %ecx
	//0x00002a82 LBB0_621
	0x48, 0x39, 0xf7, //0x00002a82 cmpq         %rsi, %rdi
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00002a85 je           LBB0_624
	0x48, 0x8d, 0x0c, 0x89, //0x00002a8b leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x04, 0x36, //0x00002a8f movsbq       (%r14,%rsi), %rax
	0x48, 0x8d, 0x0c, 0x48, //0x00002a94 leaq         (%rax,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00002a98 addq         $-48, %rcx
	0x48, 0x83, 0xc6, 0x01, //0x00002a9c addq         $1, %rsi
	0x48, 0x39, 0xf3, //0x00002aa0 cmpq         %rsi, %rbx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00002aa3 jne          LBB0_621
	0x4c, 0x89, 0xc2, //0x00002aa9 movq         %r8, %rdx
	//0x00002aac LBB0_624
	0x48, 0x8b, 0x45, 0xd0, //0x00002aac movq         $-48(%rbp), %rax
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00002ab0 jmp          LBB0_626
	//0x00002ab5 LBB0_625
	0x31, 0xd2, //0x00002ab5 xorl         %edx, %edx
	0x31, 0xc9, //0x00002ab7 xorl         %ecx, %ecx
	//0x00002ab9 LBB0_626
	0x89, 0xc7, //0x00002ab9 movl         %eax, %edi
	0x29, 0xd7, //0x00002abb subl         %edx, %edi
	0x0f, 0x8e, 0x4e, 0x00, 0x00, 0x00, //0x00002abd jle          LBB0_634
	0x89, 0xd6, //0x00002ac3 movl         %edx, %esi
	0xf7, 0xd6, //0x00002ac5 notl         %esi
	0x03, 0x75, 0xd0, //0x00002ac7 addl         $-48(%rbp), %esi
	0x83, 0xe7, 0x07, //0x00002aca andl         $7, %edi
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00002acd je           LBB0_631
	0xf7, 0xdf, //0x00002ad3 negl         %edi
	0x31, 0xdb, //0x00002ad5 xorl         %ebx, %ebx
	//0x00002ad7 LBB0_629
	0x48, 0x01, 0xc9, //0x00002ad7 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002ada leaq         (%rcx,%rcx,4), %rcx
	0x83, 0xc3, 0xff, //0x00002ade addl         $-1, %ebx
	0x39, 0xdf, //0x00002ae1 cmpl         %ebx, %edi
	0x0f, 0x85, 0xee, 0xff, 0xff, 0xff, //0x00002ae3 jne          LBB0_629
	0x29, 0xda, //0x00002ae9 subl         %ebx, %edx
	//0x00002aeb LBB0_631
	0x83, 0xfe, 0x07, //0x00002aeb cmpl         $7, %esi
	0x48, 0x8b, 0x7d, 0xd0, //0x00002aee movq         $-48(%rbp), %rdi
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x00002af2 jb           LBB0_635
	0x89, 0xfe, //0x00002af8 movl         %edi, %esi
	0x29, 0xd6, //0x00002afa subl         %edx, %esi
	//0x00002afc LBB0_633
	0x48, 0x69, 0xc9, 0x00, 0xe1, 0xf5, 0x05, //0x00002afc imulq        $100000000, %rcx, %rcx
	0x83, 0xc6, 0xf8, //0x00002b03 addl         $-8, %esi
	0x0f, 0x85, 0xf0, 0xff, 0xff, 0xff, //0x00002b06 jne          LBB0_633
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00002b0c jmp          LBB0_635
	//0x00002b11 LBB0_634
	0x48, 0x8b, 0x7d, 0xd0, //0x00002b11 movq         $-48(%rbp), %rdi
	//0x00002b15 LBB0_635
	0x85, 0xff, //0x00002b15 testl        %edi, %edi
	0x0f, 0x89, 0x84, 0x00, 0x00, 0x00, //0x00002b17 jns          LBB0_638
	0xe9, 0xd1, 0x00, 0x00, 0x00, //0x00002b1d jmp          LBB0_647
	//0x00002b22 LBB0_607
	0x49, 0x63, 0xf3, //0x00002b22 movslq       %r11d, %rsi
	0x48, 0x83, 0xc6, 0xff, //0x00002b25 addq         $-1, %rsi
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00002b29 movl         $1, %r8d
	0x48, 0x8b, 0x7d, 0xb0, //0x00002b2f movq         $-80(%rbp), %rdi
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00002b33 jmp          LBB0_609
	//0x00002b38 LBB0_608
	0x48, 0x85, 0xc0, //0x00002b38 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xd0, //0x00002b3b cmovnel      %r8d, %r10d
	0x48, 0x83, 0xc6, 0xff, //0x00002b3f addq         $-1, %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00002b43 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00002b47 movq         %rdx, %rcx
	0x0f, 0x86, 0x7b, 0xfe, 0xff, 0xff, //0x00002b4a jbe          LBB0_611
	//0x00002b50 LBB0_609
	0x48, 0x89, 0xc8, //0x00002b50 movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002b53 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00002b5d mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00002b60 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00002b64 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x1c, 0x80, //0x00002b68 leaq         (%rax,%rax,4), %rbx
	0x48, 0x89, 0xc8, //0x00002b6c movq         %rcx, %rax
	0x48, 0x29, 0xd8, //0x00002b6f subq         %rbx, %rax
	0x48, 0x39, 0xfe, //0x00002b72 cmpq         %rdi, %rsi
	0x0f, 0x83, 0xbd, 0xff, 0xff, 0xff, //0x00002b75 jae          LBB0_608
	0x04, 0x30, //0x00002b7b addb         $48, %al
	0x41, 0x88, 0x04, 0x36, //0x00002b7d movb         %al, (%r14,%rsi)
	0x48, 0x83, 0xc6, 0xff, //0x00002b81 addq         $-1, %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00002b85 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00002b89 movq         %rdx, %rcx
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x00002b8c ja           LBB0_609
	0xe9, 0x34, 0xfe, 0xff, 0xff, //0x00002b92 jmp          LBB0_611
	//0x00002b97 LBB0_637
	0x83, 0xc2, 0xff, //0x00002b97 addl         $-1, %edx
	0x31, 0xc9, //0x00002b9a xorl         %ecx, %ecx
	0x41, 0x89, 0xd1, //0x00002b9c movl         %edx, %r9d
	0x31, 0xff, //0x00002b9f xorl         %edi, %edi
	//0x00002ba1 LBB0_638
	0x41, 0x39, 0xf9, //0x00002ba1 cmpl         %edi, %r9d
	0x0f, 0x8e, 0x49, 0x00, 0x00, 0x00, //0x00002ba4 jle          LBB0_647
	0x89, 0xf8, //0x00002baa movl         %edi, %eax
	0x41, 0x8a, 0x14, 0x06, //0x00002bac movb         (%r14,%rax), %dl
	0x80, 0xfa, 0x35, //0x00002bb0 cmpb         $53, %dl
	0x0f, 0x85, 0x2d, 0x00, 0x00, 0x00, //0x00002bb3 jne          LBB0_645
	0x8d, 0x47, 0x01, //0x00002bb9 leal         $1(%rdi), %eax
	0x44, 0x39, 0xc8, //0x00002bbc cmpl         %r9d, %eax
	0x0f, 0x85, 0x21, 0x00, 0x00, 0x00, //0x00002bbf jne          LBB0_645
	0xb0, 0x01, //0x00002bc5 movb         $1, %al
	0x45, 0x85, 0xd2, //0x00002bc7 testl        %r10d, %r10d
	0x0f, 0x85, 0x25, 0x00, 0x00, 0x00, //0x00002bca jne          LBB0_648
	0x85, 0xff, //0x00002bd0 testl        %edi, %edi
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x00002bd2 je           LBB0_647
	0x83, 0xc7, 0xff, //0x00002bd8 addl         $-1, %edi
	0x41, 0x8a, 0x04, 0x3e, //0x00002bdb movb         (%r14,%rdi), %al
	0x24, 0x01, //0x00002bdf andb         $1, %al
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00002be1 jmp          LBB0_648
	//0x00002be6 LBB0_645
	0x80, 0xfa, 0x35, //0x00002be6 cmpb         $53, %dl
	0x0f, 0x9d, 0xc0, //0x00002be9 setge        %al
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00002bec jmp          LBB0_648
	//0x00002bf1 LBB0_646
	0x31, 0xc9, //0x00002bf1 xorl         %ecx, %ecx
	//0x00002bf3 LBB0_647
	0x31, 0xc0, //0x00002bf3 xorl         %eax, %eax
	//0x00002bf5 LBB0_648
	0x0f, 0xb6, 0xd8, //0x00002bf5 movzbl       %al, %ebx
	0x48, 0x01, 0xcb, //0x00002bf8 addq         %rcx, %rbx
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, //0x00002bfb movabsq      $9007199254740992, %rax
	0x48, 0x39, 0xc3, //0x00002c05 cmpq         %rax, %rbx
	0x0f, 0x85, 0x96, 0x00, 0x00, 0x00, //0x00002c08 jne          LBB0_658
	0x48, 0x8b, 0x45, 0xc8, //0x00002c0e movq         $-56(%rbp), %rax
	0x3d, 0xfe, 0x03, 0x00, 0x00, //0x00002c12 cmpl         $1022, %eax
	0x0f, 0x8e, 0x75, 0x00, 0x00, 0x00, //0x00002c17 jle          LBB0_656
	//0x00002c1d LBB0_650
	0x31, 0xdb, //0x00002c1d xorl         %ebx, %ebx
	0x48, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00002c1f movabsq      $9218868437227405312, %rcx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002c29 movq         $-64(%rbp), %r13
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00002c2d movabsq      $-9223372036854775808, %rdi
	0x48, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002c37 movabsq      $4503599627370495, %rsi
	//0x00002c41 LBB0_651
	0x8a, 0x55, 0xa0, //0x00002c41 movb         $-96(%rbp), %dl
	//0x00002c44 LBB0_652
	0x48, 0x21, 0xf3, //0x00002c44 andq         %rsi, %rbx
	0x48, 0x09, 0xcb, //0x00002c47 orq          %rcx, %rbx
	0x48, 0x89, 0xd8, //0x00002c4a movq         %rbx, %rax
	0x48, 0x09, 0xf8, //0x00002c4d orq          %rdi, %rax
	0x80, 0xfa, 0x2d, //0x00002c50 cmpb         $45, %dl
	0x48, 0x0f, 0x45, 0xc3, //0x00002c53 cmovneq      %rbx, %rax
	0x66, 0x48, 0x0f, 0x6e, 0xc0, //0x00002c57 movq         %rax, %xmm0
	//0x00002c5c LBB0_653
	0x66, 0x48, 0x0f, 0x7e, 0xc0, //0x00002c5c movq         %xmm0, %rax
	0x48, 0x83, 0xc7, 0xff, //0x00002c61 addq         $-1, %rdi
	0x48, 0x21, 0xc7, //0x00002c65 andq         %rax, %rdi
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00002c68 movabsq      $9218868437227405312, %rax
	0x48, 0x39, 0xc7, //0x00002c72 cmpq         %rax, %rdi
	0x0f, 0x85, 0x08, 0x00, 0x00, 0x00, //0x00002c75 jne          LBB0_655
	0x49, 0xc7, 0x45, 0x00, 0xf8, 0xff, 0xff, 0xff, //0x00002c7b movq         $-8, (%r13)
	//0x00002c83 LBB0_655
	0xf2, 0x41, 0x0f, 0x11, 0x45, 0x08, //0x00002c83 movsd        %xmm0, $8(%r13)
	0x4c, 0x8b, 0x65, 0xa8, //0x00002c89 movq         $-88(%rbp), %r12
	0xe9, 0x30, 0xd6, 0xff, 0xff, //0x00002c8d jmp          LBB0_38
	//0x00002c92 LBB0_656
	0x83, 0xc0, 0x01, //0x00002c92 addl         $1, %eax
	0x48, 0x89, 0x45, 0xc8, //0x00002c95 movq         %rax, $-56(%rbp)
	0x48, 0x8b, 0x5d, 0x88, //0x00002c99 movq         $-120(%rbp), %rbx
	0xe9, 0x02, 0x00, 0x00, 0x00, //0x00002c9d jmp          LBB0_658
	//0x00002ca2 LBB0_657
	0x31, 0xdb, //0x00002ca2 xorl         %ebx, %ebx
	//0x00002ca4 LBB0_658
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002ca4 movq         $-64(%rbp), %r13
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00002ca8 movabsq      $-9223372036854775808, %rdi
	0x48, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002cb2 movabsq      $4503599627370495, %rsi
	0x8a, 0x55, 0xa0, //0x00002cbc movb         $-96(%rbp), %dl
	//0x00002cbf LBB0_659
	0x48, 0x8b, 0x45, 0x88, //0x00002cbf movq         $-120(%rbp), %rax
	0x48, 0x21, 0xd8, //0x00002cc3 andq         %rbx, %rax
	0x48, 0x8b, 0x4d, 0xc8, //0x00002cc6 movq         $-56(%rbp), %rcx
	0x81, 0xc1, 0xff, 0x03, 0x00, 0x00, //0x00002cca addl         $1023, %ecx
	0x81, 0xe1, 0xff, 0x07, 0x00, 0x00, //0x00002cd0 andl         $2047, %ecx
	0x48, 0xc1, 0xe1, 0x34, //0x00002cd6 shlq         $52, %rcx
	0x48, 0x85, 0xc0, //0x00002cda testq        %rax, %rax
	0x48, 0x0f, 0x44, 0xc8, //0x00002cdd cmoveq       %rax, %rcx
	0xe9, 0x5e, 0xff, 0xff, 0xff, //0x00002ce1 jmp          LBB0_652
	//0x00002ce6 LBB0_660
	0x45, 0x31, 0xed, //0x00002ce6 xorl         %r13d, %r13d
	0xe9, 0xa2, 0xef, 0xff, 0xff, //0x00002ce9 jmp          LBB0_406
	//0x00002cee LBB0_662
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002cee movq         $-1, %rdi
	0xe9, 0x93, 0xe0, 0xff, 0xff, //0x00002cf5 jmp          LBB0_178
	//0x00002cfa LBB0_663
	0x49, 0x01, 0xfc, //0x00002cfa addq         %rdi, %r12
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00002cfd movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc9, //0x00002d05 xorl         %r9d, %r9d
	0x49, 0x83, 0xfb, 0x20, //0x00002d08 cmpq         $32, %r11
	0x0f, 0x83, 0x03, 0xe8, 0xff, 0xff, //0x00002d0c jae          LBB0_291
	//0x00002d12 LBB0_664
	0x48, 0x89, 0xfa, //0x00002d12 movq         %rdi, %rdx
	0xe9, 0x9f, 0x00, 0x00, 0x00, //0x00002d15 jmp          LBB0_674
	//0x00002d1a LBB0_665
	0x4a, 0x8d, 0x14, 0x27, //0x00002d1a leaq         (%rdi,%r12), %rdx
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00002d1e movq         $-1, $-56(%rbp)
	0x31, 0xf6, //0x00002d26 xorl         %esi, %esi
	0x49, 0x89, 0xfa, //0x00002d28 movq         %rdi, %r10
	0x48, 0x83, 0xf9, 0x20, //0x00002d2b cmpq         $32, %rcx
	0x0f, 0x83, 0x6d, 0xe8, 0xff, 0xff, //0x00002d2f jae          LBB0_296
	//0x00002d35 LBB0_666
	0x48, 0x89, 0xf7, //0x00002d35 movq         %rsi, %rdi
	0x48, 0x89, 0xcb, //0x00002d38 movq         %rcx, %rbx
	0xe9, 0xd3, 0x01, 0x00, 0x00, //0x00002d3b jmp          LBB0_696
	//0x00002d40 LBB0_667
	0x48, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x00002d40 movq         $-2, %rdi
	0xe9, 0x41, 0xe0, 0xff, 0xff, //0x00002d47 jmp          LBB0_178
	//0x00002d4c LBB0_669
	0x48, 0x89, 0xfb, //0x00002d4c movq         %rdi, %rbx
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00002d4f cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x19, 0x00, 0x00, 0x00, //0x00002d54 jne          LBB0_672
	0x4c, 0x89, 0xe2, //0x00002d5a movq         %r12, %rdx
	0x48, 0x29, 0xda, //0x00002d5d subq         %rbx, %rdx
	0x48, 0x0f, 0xbc, 0xf1, //0x00002d60 bsfq         %rcx, %rsi
	0x48, 0x01, 0xd6, //0x00002d64 addq         %rdx, %rsi
	0x48, 0x89, 0x75, 0xc8, //0x00002d67 movq         %rsi, $-56(%rbp)
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00002d6b jmp          LBB0_672
	//0x00002d70 LBB0_671
	0x48, 0x89, 0xfb, //0x00002d70 movq         %rdi, %rbx
	//0x00002d73 LBB0_672
	0x44, 0x89, 0xca, //0x00002d73 movl         %r9d, %edx
	0xf7, 0xd2, //0x00002d76 notl         %edx
	0x21, 0xca, //0x00002d78 andl         %ecx, %edx
	0x41, 0x8d, 0x34, 0x51, //0x00002d7a leal         (%r9,%rdx,2), %esi
	0x8d, 0x3c, 0x12, //0x00002d7e leal         (%rdx,%rdx), %edi
	0xf7, 0xd7, //0x00002d81 notl         %edi
	0x21, 0xcf, //0x00002d83 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002d85 andl         $-1431655766, %edi
	0x45, 0x31, 0xc9, //0x00002d8b xorl         %r9d, %r9d
	0x01, 0xd7, //0x00002d8e addl         %edx, %edi
	0x41, 0x0f, 0x92, 0xc1, //0x00002d90 setb         %r9b
	0x01, 0xff, //0x00002d94 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00002d96 xorl         $1431655765, %edi
	0x21, 0xf7, //0x00002d9c andl         %esi, %edi
	0xf7, 0xd7, //0x00002d9e notl         %edi
	0x21, 0xf8, //0x00002da0 andl         %edi, %eax
	0x48, 0x89, 0xdf, //0x00002da2 movq         %rbx, %rdi
	0x48, 0x85, 0xc0, //0x00002da5 testq        %rax, %rax
	0x0f, 0x85, 0xd1, 0xe7, 0xff, 0xff, //0x00002da8 jne          LBB0_294
	//0x00002dae LBB0_673
	0x48, 0x89, 0xfa, //0x00002dae movq         %rdi, %rdx
	0x49, 0x83, 0xc4, 0x20, //0x00002db1 addq         $32, %r12
	0x49, 0x83, 0xc3, 0xe0, //0x00002db5 addq         $-32, %r11
	//0x00002db9 LBB0_674
	0x4d, 0x85, 0xc9, //0x00002db9 testq        %r9, %r9
	0x0f, 0x85, 0x15, 0x02, 0x00, 0x00, //0x00002dbc jne          LBB0_712
	0x48, 0x8b, 0x75, 0xc8, //0x00002dc2 movq         $-56(%rbp), %rsi
	0x4d, 0x85, 0xdb, //0x00002dc6 testq        %r11, %r11
	0x0f, 0x84, 0x8f, 0x00, 0x00, 0x00, //0x00002dc9 je           LBB0_684
	//0x00002dcf LBB0_676
	0x48, 0x89, 0xd1, //0x00002dcf movq         %rdx, %rcx
	0x48, 0xf7, 0xd9, //0x00002dd2 negq         %rcx
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002dd5 movq         $-1, %rdi
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002ddc movq         $-64(%rbp), %r13
	//0x00002de0 LBB0_677
	0x31, 0xdb, //0x00002de0 xorl         %ebx, %ebx
	//0x00002de2 LBB0_678
	0x41, 0x0f, 0xb6, 0x04, 0x1c, //0x00002de2 movzbl       (%r12,%rbx), %eax
	0x3c, 0x22, //0x00002de7 cmpb         $34, %al
	0x0f, 0x84, 0x68, 0x00, 0x00, 0x00, //0x00002de9 je           LBB0_683
	0x3c, 0x5c, //0x00002def cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002df1 je           LBB0_681
	0x48, 0x83, 0xc3, 0x01, //0x00002df7 addq         $1, %rbx
	0x49, 0x39, 0xdb, //0x00002dfb cmpq         %rbx, %r11
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x00002dfe jne          LBB0_678
	0xe9, 0x5d, 0x00, 0x00, 0x00, //0x00002e04 jmp          LBB0_685
	//0x00002e09 LBB0_681
	0x49, 0x8d, 0x43, 0xff, //0x00002e09 leaq         $-1(%r11), %rax
	0x48, 0x39, 0xd8, //0x00002e0d cmpq         %rbx, %rax
	0x0f, 0x84, 0x4d, 0x02, 0x00, 0x00, //0x00002e10 je           LBB0_716
	0x4e, 0x8d, 0x04, 0x21, //0x00002e16 leaq         (%rcx,%r12), %r8
	0x49, 0x01, 0xd8, //0x00002e1a addq         %rbx, %r8
	0x48, 0x83, 0xfe, 0xff, //0x00002e1d cmpq         $-1, %rsi
	0x48, 0x8b, 0x45, 0xc8, //0x00002e21 movq         $-56(%rbp), %rax
	0x49, 0x0f, 0x44, 0xc0, //0x00002e25 cmoveq       %r8, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00002e29 movq         %rax, $-56(%rbp)
	0x49, 0x0f, 0x44, 0xf0, //0x00002e2d cmoveq       %r8, %rsi
	0x49, 0x01, 0xdc, //0x00002e31 addq         %rbx, %r12
	0x49, 0x83, 0xc4, 0x02, //0x00002e34 addq         $2, %r12
	0x4c, 0x89, 0xd8, //0x00002e38 movq         %r11, %rax
	0x48, 0x29, 0xd8, //0x00002e3b subq         %rbx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00002e3e addq         $-2, %rax
	0x49, 0x83, 0xc3, 0xfe, //0x00002e42 addq         $-2, %r11
	0x49, 0x39, 0xdb, //0x00002e46 cmpq         %rbx, %r11
	0x49, 0x89, 0xc3, //0x00002e49 movq         %rax, %r11
	0x0f, 0x85, 0x8e, 0xff, 0xff, 0xff, //0x00002e4c jne          LBB0_677
	0xe9, 0x36, 0xdf, 0xff, 0xff, //0x00002e52 jmp          LBB0_178
	//0x00002e57 LBB0_683
	0x49, 0x01, 0xdc, //0x00002e57 addq         %rbx, %r12
	0x49, 0x83, 0xc4, 0x01, //0x00002e5a addq         $1, %r12
	//0x00002e5e LBB0_684
	0x49, 0x29, 0xd4, //0x00002e5e subq         %rdx, %r12
	0xe9, 0x78, 0xda, 0xff, 0xff, //0x00002e61 jmp          LBB0_121
	//0x00002e66 LBB0_685
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002e66 movq         $-1, %rdi
	0x3c, 0x22, //0x00002e6d cmpb         $34, %al
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002e6f movq         $-64(%rbp), %r13
	0x0f, 0x85, 0x14, 0xdf, 0xff, 0xff, //0x00002e73 jne          LBB0_178
	0x4d, 0x01, 0xdc, //0x00002e79 addq         %r11, %r12
	0xe9, 0xdd, 0xff, 0xff, 0xff, //0x00002e7c jmp          LBB0_684
	//0x00002e81 LBB0_687
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00002e81 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00002e86 jne          LBB0_689
	0x48, 0x89, 0xd7, //0x00002e8c movq         %rdx, %rdi
	0x4c, 0x29, 0xd7, //0x00002e8f subq         %r10, %rdi
	0x49, 0x0f, 0xbc, 0xde, //0x00002e92 bsfq         %r14, %rbx
	0x48, 0x01, 0xfb, //0x00002e96 addq         %rdi, %rbx
	0x48, 0x89, 0x5d, 0xc8, //0x00002e99 movq         %rbx, $-56(%rbp)
	//0x00002e9d LBB0_689
	0x48, 0x89, 0xf3, //0x00002e9d movq         %rsi, %rbx
	0xf7, 0xd6, //0x00002ea0 notl         %esi
	0x44, 0x21, 0xf6, //0x00002ea2 andl         %r14d, %esi
	0x44, 0x8d, 0x0c, 0x73, //0x00002ea5 leal         (%rbx,%rsi,2), %r9d
	0x8d, 0x1c, 0x36, //0x00002ea9 leal         (%rsi,%rsi), %ebx
	0xf7, 0xd3, //0x00002eac notl         %ebx
	0x44, 0x21, 0xf3, //0x00002eae andl         %r14d, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002eb1 andl         $-1431655766, %ebx
	0x31, 0xff, //0x00002eb7 xorl         %edi, %edi
	0x01, 0xf3, //0x00002eb9 addl         %esi, %ebx
	0x40, 0x0f, 0x92, 0xc7, //0x00002ebb setb         %dil
	0x01, 0xdb, //0x00002ebf addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00002ec1 xorl         $1431655765, %ebx
	0x44, 0x21, 0xcb, //0x00002ec7 andl         %r9d, %ebx
	0xf7, 0xd3, //0x00002eca notl         %ebx
	0x41, 0x21, 0xdb, //0x00002ecc andl         %ebx, %r11d
	0x48, 0xc1, 0xe0, 0x10, //0x00002ecf shlq         $16, %rax
	0x4d, 0x85, 0xdb, //0x00002ed3 testq        %r11, %r11
	0x0f, 0x85, 0x56, 0xe7, 0xff, 0xff, //0x00002ed6 jne          LBB0_299
	//0x00002edc LBB0_690
	0xbe, 0x40, 0x00, 0x00, 0x00, //0x00002edc movl         $64, %esi
	//0x00002ee1 LBB0_691
	0x4c, 0x09, 0xc0, //0x00002ee1 orq          %r8, %rax
	0x4d, 0x85, 0xdb, //0x00002ee4 testq        %r11, %r11
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002ee7 je           LBB0_694
	0x48, 0x85, 0xc0, //0x00002eed testq        %rax, %rax
	0x0f, 0x84, 0xa4, 0x00, 0x00, 0x00, //0x00002ef0 je           LBB0_706
	0x48, 0x0f, 0xbc, 0xc0, //0x00002ef6 bsfq         %rax, %rax
	0xe9, 0xa0, 0x00, 0x00, 0x00, //0x00002efa jmp          LBB0_707
	//0x00002eff LBB0_694
	0x48, 0x85, 0xc0, //0x00002eff testq        %rax, %rax
	0x0f, 0x85, 0xb0, 0x00, 0x00, 0x00, //0x00002f02 jne          LBB0_709
	0x48, 0x89, 0xcb, //0x00002f08 movq         %rcx, %rbx
	0x48, 0x83, 0xc2, 0x20, //0x00002f0b addq         $32, %rdx
	0x48, 0x83, 0xc3, 0xe0, //0x00002f0f addq         $-32, %rbx
	//0x00002f13 LBB0_696
	0x48, 0x85, 0xff, //0x00002f13 testq        %rdi, %rdi
	0x0f, 0x85, 0xfa, 0x00, 0x00, 0x00, //0x00002f16 jne          LBB0_714
	0x48, 0x8b, 0x45, 0xc8, //0x00002f1c movq         $-56(%rbp), %rax
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002f20 movq         $-1, %rdi
	0x48, 0x85, 0xdb, //0x00002f27 testq        %rbx, %rbx
	0x0f, 0x84, 0x33, 0x01, 0x00, 0x00, //0x00002f2a je           LBB0_716
	//0x00002f30 LBB0_698
	0x0f, 0xb6, 0x0a, //0x00002f30 movzbl       (%rdx), %ecx
	0x80, 0xf9, 0x22, //0x00002f33 cmpb         $34, %cl
	0x0f, 0x84, 0x8c, 0x00, 0x00, 0x00, //0x00002f36 je           LBB0_710
	0x80, 0xf9, 0x5c, //0x00002f3c cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002f3f je           LBB0_703
	0x80, 0xf9, 0x20, //0x00002f45 cmpb         $32, %cl
	0x0f, 0x82, 0x6a, 0x00, 0x00, 0x00, //0x00002f48 jb           LBB0_709
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002f4e movq         $-1, %rcx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00002f55 movl         $1, %esi
	//0x00002f5a LBB0_702
	0x48, 0x01, 0xf2, //0x00002f5a addq         %rsi, %rdx
	0x48, 0x01, 0xcb, //0x00002f5d addq         %rcx, %rbx
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x00002f60 jne          LBB0_698
	0xe9, 0xf8, 0x00, 0x00, 0x00, //0x00002f66 jmp          LBB0_716
	//0x00002f6b LBB0_703
	0x48, 0x83, 0xfb, 0x01, //0x00002f6b cmpq         $1, %rbx
	0x0f, 0x84, 0xee, 0x00, 0x00, 0x00, //0x00002f6f je           LBB0_716
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002f75 movq         $-2, %rcx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00002f7c movl         $2, %esi
	0x48, 0x83, 0xf8, 0xff, //0x00002f81 cmpq         $-1, %rax
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00002f85 jne          LBB0_702
	0x48, 0x89, 0xd0, //0x00002f8b movq         %rdx, %rax
	0x4c, 0x29, 0xd0, //0x00002f8e subq         %r10, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00002f91 movq         %rax, $-56(%rbp)
	0xe9, 0xc0, 0xff, 0xff, 0xff, //0x00002f95 jmp          LBB0_702
	//0x00002f9a LBB0_706
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00002f9a movl         $64, %eax
	//0x00002f9f LBB0_707
	0x48, 0x39, 0xf0, //0x00002f9f cmpq         %rsi, %rax
	0x0f, 0x82, 0x10, 0x00, 0x00, 0x00, //0x00002fa2 jb           LBB0_709
	0x4c, 0x29, 0xd2, //0x00002fa8 subq         %r10, %rdx
	0x4c, 0x8d, 0x24, 0x32, //0x00002fab leaq         (%rdx,%rsi), %r12
	0x49, 0x83, 0xc4, 0x01, //0x00002faf addq         $1, %r12
	0xe9, 0x26, 0xd9, 0xff, 0xff, //0x00002fb3 jmp          LBB0_121
	//0x00002fb8 LBB0_709
	0x48, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x00002fb8 movq         $-2, %rdi
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002fbf movq         $-64(%rbp), %r13
	0xe9, 0xc5, 0xdd, 0xff, 0xff, //0x00002fc3 jmp          LBB0_178
	//0x00002fc8 LBB0_710
	0x4c, 0x29, 0xd2, //0x00002fc8 subq         %r10, %rdx
	0x48, 0x83, 0xc2, 0x01, //0x00002fcb addq         $1, %rdx
	0x49, 0x89, 0xd4, //0x00002fcf movq         %rdx, %r12
	0xe9, 0x07, 0xd9, 0xff, 0xff, //0x00002fd2 jmp          LBB0_121
	//0x00002fd7 LBB0_712
	0x4d, 0x85, 0xdb, //0x00002fd7 testq        %r11, %r11
	0x0f, 0x84, 0x7c, 0x00, 0x00, 0x00, //0x00002fda je           LBB0_717
	0x48, 0x89, 0xd6, //0x00002fe0 movq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00002fe3 notq         %rsi
	0x4c, 0x01, 0xe6, //0x00002fe6 addq         %r12, %rsi
	0x48, 0x8b, 0x4d, 0xc8, //0x00002fe9 movq         $-56(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x00002fed cmpq         $-1, %rcx
	0x48, 0x89, 0xc8, //0x00002ff1 movq         %rcx, %rax
	0x48, 0x0f, 0x44, 0xc6, //0x00002ff4 cmoveq       %rsi, %rax
	0x48, 0x0f, 0x45, 0xf1, //0x00002ff8 cmovneq      %rcx, %rsi
	0x49, 0x83, 0xc4, 0x01, //0x00002ffc addq         $1, %r12
	0x49, 0x83, 0xc3, 0xff, //0x00003000 addq         $-1, %r11
	0x48, 0x89, 0x45, 0xc8, //0x00003004 movq         %rax, $-56(%rbp)
	0x4d, 0x85, 0xdb, //0x00003008 testq        %r11, %r11
	0x0f, 0x85, 0xbe, 0xfd, 0xff, 0xff, //0x0000300b jne          LBB0_676
	0xe9, 0x48, 0xfe, 0xff, 0xff, //0x00003011 jmp          LBB0_684
	//0x00003016 LBB0_714
	0x48, 0x85, 0xdb, //0x00003016 testq        %rbx, %rbx
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x00003019 je           LBB0_717
	0x4c, 0x89, 0xd0, //0x0000301f movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00003022 notq         %rax
	0x48, 0x01, 0xd0, //0x00003025 addq         %rdx, %rax
	0x48, 0x8b, 0x75, 0xc8, //0x00003028 movq         $-56(%rbp), %rsi
	0x48, 0x83, 0xfe, 0xff, //0x0000302c cmpq         $-1, %rsi
	0x48, 0x89, 0xf1, //0x00003030 movq         %rsi, %rcx
	0x48, 0x0f, 0x44, 0xc8, //0x00003033 cmoveq       %rax, %rcx
	0x48, 0x0f, 0x45, 0xc6, //0x00003037 cmovneq      %rsi, %rax
	0x48, 0x83, 0xc2, 0x01, //0x0000303b addq         $1, %rdx
	0x48, 0x83, 0xc3, 0xff, //0x0000303f addq         $-1, %rbx
	0x48, 0x89, 0x4d, 0xc8, //0x00003043 movq         %rcx, $-56(%rbp)
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00003047 movq         $-1, %rdi
	0x48, 0x85, 0xdb, //0x0000304e testq        %rbx, %rbx
	0x0f, 0x85, 0xd9, 0xfe, 0xff, 0xff, //0x00003051 jne          LBB0_698
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00003057 jmp          LBB0_716
	//0x0000305c LBB0_717
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000305c movq         $-1, %rdi
	//0x00003063 LBB0_716
	0x4c, 0x8b, 0x6d, 0xc0, //0x00003063 movq         $-64(%rbp), %r13
	0xe9, 0x21, 0xdd, 0xff, 0xff, //0x00003067 jmp          LBB0_178
	//0x0000306c .p2align 2, 0x90
	// // .set L0_0_set_37, LBB0_37-LJTI0_0
	// // .set L0_0_set_107, LBB0_107-LJTI0_0
	// // .set L0_0_set_108, LBB0_108-LJTI0_0
	// // .set L0_0_set_102, LBB0_102-LJTI0_0
	// // .set L0_0_set_30, LBB0_30-LJTI0_0
	// // .set L0_0_set_122, LBB0_122-LJTI0_0
	// // .set L0_0_set_123, LBB0_123-LJTI0_0
	// // .set L0_0_set_127, LBB0_127-LJTI0_0
	// // .set L0_0_set_129, LBB0_129-LJTI0_0
	// // .set L0_0_set_103, LBB0_103-LJTI0_0
	// // .set L0_0_set_124, LBB0_124-LJTI0_0
	// // .set L0_0_set_132, LBB0_132-LJTI0_0
	// // .set L0_0_set_106, LBB0_106-LJTI0_0
	//0x0000306c LJTI0_0
	0x4e, 0xd2, 0xff, 0xff, //0x0000306c .long L0_0_set_37
	0xbf, 0xd6, 0xff, 0xff, //0x00003070 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003074 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003078 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000307c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003080 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003084 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003088 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000308c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003090 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003094 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003098 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000309c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030a0 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030a4 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030a8 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030ac .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030b0 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030b4 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030b8 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030bc .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030c0 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030c4 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030c8 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030cc .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030d0 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030d4 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030d8 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030dc .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030e0 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030e4 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030e8 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030ec .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030f0 .long L0_0_set_107
	0xcf, 0xd6, 0xff, 0xff, //0x000030f4 .long L0_0_set_108
	0xbf, 0xd6, 0xff, 0xff, //0x000030f8 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000030fc .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003100 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003104 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003108 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000310c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003110 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003114 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003118 .long L0_0_set_107
	0x54, 0xd6, 0xff, 0xff, //0x0000311c .long L0_0_set_102
	0xd3, 0xd1, 0xff, 0xff, //0x00003120 .long L0_0_set_30
	0xbf, 0xd6, 0xff, 0xff, //0x00003124 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003128 .long L0_0_set_107
	0xd3, 0xd1, 0xff, 0xff, //0x0000312c .long L0_0_set_30
	0xd3, 0xd1, 0xff, 0xff, //0x00003130 .long L0_0_set_30
	0xd3, 0xd1, 0xff, 0xff, //0x00003134 .long L0_0_set_30
	0xd3, 0xd1, 0xff, 0xff, //0x00003138 .long L0_0_set_30
	0xd3, 0xd1, 0xff, 0xff, //0x0000313c .long L0_0_set_30
	0xd3, 0xd1, 0xff, 0xff, //0x00003140 .long L0_0_set_30
	0xd3, 0xd1, 0xff, 0xff, //0x00003144 .long L0_0_set_30
	0xd3, 0xd1, 0xff, 0xff, //0x00003148 .long L0_0_set_30
	0xd3, 0xd1, 0xff, 0xff, //0x0000314c .long L0_0_set_30
	0xd3, 0xd1, 0xff, 0xff, //0x00003150 .long L0_0_set_30
	0x7b, 0xd8, 0xff, 0xff, //0x00003154 .long L0_0_set_122
	0xbf, 0xd6, 0xff, 0xff, //0x00003158 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000315c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003160 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003164 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003168 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000316c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003170 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003174 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003178 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000317c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003180 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003184 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003188 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000318c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003190 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003194 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003198 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000319c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031a0 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031a4 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031a8 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031ac .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031b0 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031b4 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031b8 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031bc .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031c0 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031c4 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031c8 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031cc .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031d0 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031d4 .long L0_0_set_107
	0x98, 0xd8, 0xff, 0xff, //0x000031d8 .long L0_0_set_123
	0xbf, 0xd6, 0xff, 0xff, //0x000031dc .long L0_0_set_107
	0xd6, 0xd8, 0xff, 0xff, //0x000031e0 .long L0_0_set_127
	0xbf, 0xd6, 0xff, 0xff, //0x000031e4 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031e8 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031ec .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031f0 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031f4 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031f8 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x000031fc .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003200 .long L0_0_set_107
	0xfe, 0xd8, 0xff, 0xff, //0x00003204 .long L0_0_set_129
	0xbf, 0xd6, 0xff, 0xff, //0x00003208 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000320c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003210 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003214 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003218 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000321c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003220 .long L0_0_set_107
	0x71, 0xd6, 0xff, 0xff, //0x00003224 .long L0_0_set_103
	0xbf, 0xd6, 0xff, 0xff, //0x00003228 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000322c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003230 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003234 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003238 .long L0_0_set_107
	0xa5, 0xd8, 0xff, 0xff, //0x0000323c .long L0_0_set_124
	0xbf, 0xd6, 0xff, 0xff, //0x00003240 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003244 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003248 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x0000324c .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003250 .long L0_0_set_107
	0xbf, 0xd6, 0xff, 0xff, //0x00003254 .long L0_0_set_107
	0x30, 0xd9, 0xff, 0xff, //0x00003258 .long L0_0_set_132
	0xbf, 0xd6, 0xff, 0xff, //0x0000325c .long L0_0_set_107
	0xa2, 0xd6, 0xff, 0xff, //0x00003260 .long L0_0_set_106
	// // .set L0_1_set_65, LBB0_65-LJTI0_1
	// // .set L0_1_set_77, LBB0_77-LJTI0_1
	// // .set L0_1_set_71, LBB0_71-LJTI0_1
	// // .set L0_1_set_74, LBB0_74-LJTI0_1
	//0x00003264 LJTI0_1
	0x93, 0xd2, 0xff, 0xff, //0x00003264 .long L0_1_set_65
	0x1f, 0xd3, 0xff, 0xff, //0x00003268 .long L0_1_set_77
	0x93, 0xd2, 0xff, 0xff, //0x0000326c .long L0_1_set_65
	0xde, 0xd2, 0xff, 0xff, //0x00003270 .long L0_1_set_71
	0x1f, 0xd3, 0xff, 0xff, //0x00003274 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x00003278 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x0000327c .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x00003280 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x00003284 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x00003288 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x0000328c .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x00003290 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x00003294 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x00003298 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x0000329c .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x000032a0 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x000032a4 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x000032a8 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x000032ac .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x000032b0 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x000032b4 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x000032b8 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x000032bc .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x000032c0 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x000032c4 .long L0_1_set_77
	0x1f, 0xd3, 0xff, 0xff, //0x000032c8 .long L0_1_set_77
	0xfa, 0xd2, 0xff, 0xff, //0x000032cc .long L0_1_set_74
	//0x000032d0 .p2align 2, 0x00
	//0x000032d0 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x000032d0 .long 2
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000032d4 .p2align 4, 0x00
	//0x000032e0 _P10_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, //0x000032e0 .quad 0x3ff0000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x40, //0x000032e8 .quad 0x4024000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x40, //0x000032f0 .quad 0x4059000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x8f, 0x40, //0x000032f8 .quad 0x408f400000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0xc3, 0x40, //0x00003300 .quad 0x40c3880000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x6a, 0xf8, 0x40, //0x00003308 .quad 0x40f86a0000000000
	0x00, 0x00, 0x00, 0x00, 0x80, 0x84, 0x2e, 0x41, //0x00003310 .quad 0x412e848000000000
	0x00, 0x00, 0x00, 0x00, 0xd0, 0x12, 0x63, 0x41, //0x00003318 .quad 0x416312d000000000
	0x00, 0x00, 0x00, 0x00, 0x84, 0xd7, 0x97, 0x41, //0x00003320 .quad 0x4197d78400000000
	0x00, 0x00, 0x00, 0x00, 0x65, 0xcd, 0xcd, 0x41, //0x00003328 .quad 0x41cdcd6500000000
	0x00, 0x00, 0x00, 0x20, 0x5f, 0xa0, 0x02, 0x42, //0x00003330 .quad 0x4202a05f20000000
	0x00, 0x00, 0x00, 0xe8, 0x76, 0x48, 0x37, 0x42, //0x00003338 .quad 0x42374876e8000000
	0x00, 0x00, 0x00, 0xa2, 0x94, 0x1a, 0x6d, 0x42, //0x00003340 .quad 0x426d1a94a2000000
	0x00, 0x00, 0x40, 0xe5, 0x9c, 0x30, 0xa2, 0x42, //0x00003348 .quad 0x42a2309ce5400000
	0x00, 0x00, 0x90, 0x1e, 0xc4, 0xbc, 0xd6, 0x42, //0x00003350 .quad 0x42d6bcc41e900000
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x00003358 .quad 0x430c6bf526340000
	0x00, 0x80, 0xe0, 0x37, 0x79, 0xc3, 0x41, 0x43, //0x00003360 .quad 0x4341c37937e08000
	0x00, 0xa0, 0xd8, 0x85, 0x57, 0x34, 0x76, 0x43, //0x00003368 .quad 0x4376345785d8a000
	0x00, 0xc8, 0x4e, 0x67, 0x6d, 0xc1, 0xab, 0x43, //0x00003370 .quad 0x43abc16d674ec800
	0x00, 0x3d, 0x91, 0x60, 0xe4, 0x58, 0xe1, 0x43, //0x00003378 .quad 0x43e158e460913d00
	0x40, 0x8c, 0xb5, 0x78, 0x1d, 0xaf, 0x15, 0x44, //0x00003380 .quad 0x4415af1d78b58c40
	0x50, 0xef, 0xe2, 0xd6, 0xe4, 0x1a, 0x4b, 0x44, //0x00003388 .quad 0x444b1ae4d6e2ef50
	0x92, 0xd5, 0x4d, 0x06, 0xcf, 0xf0, 0x80, 0x44, //0x00003390 .quad 0x4480f0cf064dd592
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003398 .p2align 4, 0x00
	//0x000033a0 _POW10_M128_TAB
	0x53, 0xe4, 0x60, 0xcd, 0x69, 0xc8, 0x32, 0x17, //0x000033a0 .quad 1671618768450675795
	0x88, 0x02, 0x1c, 0x08, 0xa0, 0xd5, 0x8f, 0xfa, //0x000033a8 .quad -391859759250406776
	0xb4, 0x8e, 0x5c, 0x20, 0x42, 0xbd, 0x7f, 0x0e, //0x000033b0 .quad 1044761730281672372
	0x95, 0x81, 0x11, 0x05, 0x84, 0xe5, 0x99, 0x9c, //0x000033b8 .quad -7162441377172586091
	0x61, 0xb2, 0x73, 0xa8, 0x92, 0xac, 0x1f, 0x52, //0x000033c0 .quad 5917638181279478369
	0xfa, 0xe1, 0x55, 0x06, 0xe5, 0x5e, 0xc0, 0xc3, //0x000033c8 .quad -4341365703038344710
	0xf9, 0x9e, 0x90, 0x52, 0xb7, 0x97, 0xa7, 0xe6, //0x000033d0 .quad -1826324310255427847
	0x78, 0x5a, 0xeb, 0x47, 0x9e, 0x76, 0xb0, 0xf4, //0x000033d8 .quad -815021110370542984
	0x5c, 0x63, 0x9a, 0x93, 0xd2, 0xbe, 0x28, 0x90, //0x000033e0 .quad -8058981721550724260
	0x8b, 0x18, 0xf3, 0xec, 0x22, 0x4a, 0xee, 0x98, //0x000033e8 .quad -7426917221622671221
	0x33, 0xfc, 0x80, 0x38, 0x87, 0xee, 0x32, 0x74, //0x000033f0 .quad 8373016921771146291
	0xae, 0xde, 0x2f, 0xa8, 0xab, 0xdc, 0x29, 0xbf, //0x000033f8 .quad -4671960508600951122
	0x3f, 0x3b, 0xa1, 0x06, 0x29, 0xaa, 0x3f, 0x11, //0x00003400 .quad 1242899115359157055
	0x5a, 0xd6, 0x3b, 0x92, 0xd6, 0x53, 0xf4, 0xee, //0x00003408 .quad -1228264617323800998
	0x07, 0xc5, 0x24, 0xa4, 0x59, 0xca, 0xc7, 0x4a, //0x00003410 .quad 5388497965526861063
	0xf8, 0x65, 0x65, 0x1b, 0x66, 0xb4, 0x58, 0x95, //0x00003418 .quad -7685194413468457480
	0x49, 0xf6, 0x2d, 0x0d, 0xf0, 0xbc, 0x79, 0x5d, //0x00003420 .quad 6735622456908576329
	0x76, 0xbf, 0x3e, 0xa2, 0x7f, 0xe1, 0xae, 0xba, //0x00003428 .quad -4994806998408183946
	0xdc, 0x73, 0x79, 0x10, 0x2c, 0x2c, 0xd8, 0xf4, //0x00003430 .quad -803843965719055396
	0x53, 0x6f, 0xce, 0x8a, 0xdf, 0x99, 0x5a, 0xe9, //0x00003438 .quad -1631822729582842029
	0x69, 0xe8, 0x4b, 0x8a, 0x9b, 0x1b, 0x07, 0x79, //0x00003440 .quad 8720969558280366185
	0x94, 0x05, 0xc1, 0xb6, 0x2b, 0xa0, 0xd8, 0x91, //0x00003448 .quad -7937418233630358124
	0x84, 0xe2, 0xde, 0x6c, 0x82, 0xe2, 0x48, 0x97, //0x00003450 .quad -7545532125859093884
	0xf9, 0x46, 0x71, 0xa4, 0x36, 0xc8, 0x4e, 0xb6, //0x00003458 .quad -5310086773610559751
	0x25, 0x9b, 0x16, 0x08, 0x23, 0x1b, 0x1b, 0xfd, //0x00003460 .quad -208543120469091547
	0xb7, 0x98, 0x8d, 0x4d, 0x44, 0x7a, 0xe2, 0xe3, //0x00003468 .quad -2025922448585811785
	0xf7, 0x20, 0x0e, 0xe5, 0xf5, 0xf0, 0x30, 0xfe, //0x00003470 .quad -130339450293182217
	0x72, 0x7f, 0x78, 0xb0, 0x6a, 0x8c, 0x6d, 0x8e, //0x00003478 .quad -8183730558007214222
	0x35, 0xa9, 0x51, 0x5e, 0x33, 0x2d, 0xbd, 0xbd, //0x00003480 .quad -4774610331293865675
	0x4f, 0x9f, 0x96, 0x5c, 0x85, 0xef, 0x08, 0xb2, //0x00003488 .quad -5617977179081629873
	0x82, 0x13, 0xe6, 0x35, 0x80, 0x78, 0x2c, 0xad, //0x00003490 .quad -5968262914117332094
	0x23, 0x47, 0xbc, 0xb3, 0x66, 0x2b, 0x8b, 0xde, //0x00003498 .quad -2410785455424649437
	0x31, 0xcc, 0xaf, 0x21, 0x50, 0xcb, 0x3b, 0x4c, //0x000034a0 .quad 5493207715531443249
	0x76, 0xac, 0x55, 0x30, 0x20, 0xfb, 0x16, 0x8b, //0x000034a8 .quad -8424269937281487754
	0x3d, 0xbf, 0x1b, 0x2a, 0x24, 0xbe, 0x4a, 0xdf, //0x000034b0 .quad -2356862392440471747
	0x93, 0x17, 0x6b, 0x3c, 0xe8, 0xb9, 0xdc, 0xad, //0x000034b8 .quad -5918651403174471789
	0x0d, 0xaf, 0xa2, 0x34, 0xad, 0x6d, 0x1d, 0xd7, //0x000034c0 .quad -2946077990550589683
	0x78, 0xdd, 0x85, 0x4b, 0x62, 0xe8, 0x53, 0xd9, //0x000034c8 .quad -2786628235540701832
	0x68, 0xad, 0xe5, 0x40, 0x8c, 0x64, 0x72, 0x86, //0x000034d0 .quad -8758827771735200408
	0x6b, 0xaa, 0x33, 0x6f, 0x3d, 0x71, 0xd4, 0x87, //0x000034d8 .quad -8659171674854020501
	0xc2, 0x18, 0x1f, 0x51, 0xaf, 0xfd, 0x0e, 0x68, //0x000034e0 .quad 7498209359040551106
	0x06, 0x95, 0x00, 0xcb, 0x8c, 0x8d, 0xc9, 0xa9, //0x000034e8 .quad -6212278575140137722
	0xf2, 0xde, 0x66, 0x25, 0x1b, 0xbd, 0x12, 0x02, //0x000034f0 .quad 149389661945913074
	0x48, 0xba, 0xc0, 0xfd, 0xef, 0xf0, 0x3b, 0xd4, //0x000034f8 .quad -3153662200497784248
	0x57, 0x4b, 0x60, 0xf7, 0x30, 0xb6, 0x4b, 0x01, //0x00003500 .quad 93368538716195671
	0x6d, 0x74, 0x98, 0xfe, 0x95, 0x76, 0xa5, 0x84, //0x00003508 .quad -8888567902952197011
	0x2d, 0x5e, 0x38, 0x35, 0xbd, 0xa3, 0x9e, 0x41, //0x00003510 .quad 4728396691822632493
	0x88, 0x91, 0x3e, 0x7e, 0x3b, 0xd4, 0xce, 0xa5, //0x00003518 .quad -6499023860262858360
	0xb9, 0x75, 0x86, 0x82, 0xac, 0x4c, 0x06, 0x52, //0x00003520 .quad 5910495864778290617
	0xea, 0x35, 0xce, 0x5d, 0x4a, 0x89, 0x42, 0xcf, //0x00003528 .quad -3512093806901185046
	0x93, 0x09, 0x94, 0xd1, 0xeb, 0xef, 0x43, 0x73, //0x00003530 .quad 8305745933913819539
	0xb2, 0xe1, 0xa0, 0x7a, 0xce, 0x95, 0x89, 0x81, //0x00003538 .quad -9112587656954322510
	0xf8, 0x0b, 0xf9, 0xc5, 0xe6, 0xeb, 0x14, 0x10, //0x00003540 .quad 1158810380537498616
	0x1f, 0x1a, 0x49, 0x19, 0x42, 0xfb, 0xeb, 0xa1, //0x00003548 .quad -6779048552765515233
	0xf6, 0x4e, 0x77, 0x77, 0xe0, 0x26, 0x1a, 0xd4, //0x00003550 .quad -3163173042755514634
	0xa6, 0x60, 0x9b, 0x9f, 0x12, 0xfa, 0x66, 0xca, //0x00003558 .quad -3862124672529506138
	0xb4, 0x22, 0x55, 0x95, 0x98, 0xb0, 0x20, 0x89, //0x00003560 .quad -8565652321871781196
	0xd0, 0x38, 0x82, 0x47, 0x97, 0xb8, 0x00, 0xfd, //0x00003568 .quad -215969822234494768
	0xb0, 0x35, 0x55, 0x5d, 0x5f, 0x6e, 0xb4, 0x55, //0x00003570 .quad 6175682344898606512
	0x82, 0x63, 0xb1, 0x8c, 0x5e, 0x73, 0x20, 0x9e, //0x00003578 .quad -7052510166537641086
	0x1d, 0x83, 0xaa, 0x34, 0xf7, 0x89, 0x21, 0xeb, //0x00003580 .quad -1503769105731517667
	0x62, 0xbc, 0xdd, 0x2f, 0x36, 0x90, 0xa8, 0xc5, //0x00003588 .quad -4203951689744663454
	0xe4, 0x23, 0xd5, 0x01, 0x75, 0xec, 0xe9, 0xa5, //0x00003590 .quad -6491397400591784988
	0x7b, 0x2b, 0xd5, 0xbb, 0x43, 0xb4, 0x12, 0xf7, //0x00003598 .quad -643253593753441413
	0x6e, 0x36, 0x25, 0x21, 0xc9, 0x33, 0xb2, 0x47, //0x000035a0 .quad 5166248661484910190
	0x2d, 0x3b, 0x65, 0x55, 0xaa, 0xb0, 0x6b, 0x9a, //0x000035a8 .quad -7319562523736982739
	0x0a, 0x84, 0x6e, 0x69, 0xbb, 0xc0, 0x9e, 0x99, //0x000035b0 .quad -7377247228426025974
	0xf8, 0x89, 0xbe, 0xea, 0xd4, 0x9c, 0x06, 0xc1, //0x000035b8 .quad -4537767136243840520
	0x0d, 0x25, 0xca, 0x43, 0xea, 0x70, 0x06, 0xc0, //0x000035c0 .quad -4609873017105144563
	0x76, 0x2c, 0x6e, 0x25, 0x0a, 0x44, 0x48, 0xf1, //0x000035c8 .quad -1060522901877412746
	0x28, 0x57, 0x5e, 0x6a, 0x92, 0x06, 0x04, 0x38, //0x000035d0 .quad 4036358391950366504
	0xca, 0xdb, 0x64, 0x57, 0x86, 0x2a, 0xcd, 0x96, //0x000035d8 .quad -7580355841314464822
	0xf2, 0xec, 0xf5, 0x04, 0x37, 0x08, 0x05, 0xc6, //0x000035e0 .quad -4177924046916817678
	0xbc, 0x12, 0x3e, 0xed, 0x27, 0x75, 0x80, 0xbc, //0x000035e8 .quad -4863758783215693124
	0x2e, 0x68, 0x33, 0xc6, 0x44, 0x4a, 0x86, 0xf7, //0x000035f0 .quad -610719040218634194
	0x6b, 0x97, 0x8d, 0xe8, 0x71, 0x92, 0xa0, 0xeb, //0x000035f8 .quad -1468012460592228501
	0x1d, 0x21, 0xe0, 0xfb, 0x6a, 0xee, 0xb3, 0x7a, //0x00003600 .quad 8841672636718129437
	0xa3, 0x7e, 0x58, 0x31, 0x87, 0x5b, 0x44, 0x93, //0x00003608 .quad -7835036815511224669
	0x64, 0x29, 0xd8, 0xba, 0x05, 0xea, 0x60, 0x59, //0x00003610 .quad 6440404777470273892
	0x4c, 0x9e, 0xae, 0xfd, 0x68, 0x72, 0x15, 0xb8, //0x00003618 .quad -5182110000961642932
	0xbd, 0x33, 0x8e, 0x29, 0x87, 0x24, 0xb9, 0x6f, //0x00003620 .quad 8050505971837842365
	0xdf, 0x45, 0x1a, 0x3d, 0x03, 0xcf, 0x1a, 0xe6, //0x00003628 .quad -1865951482774665761
	0x56, 0xe0, 0xf8, 0x79, 0xd4, 0xb6, 0xd3, 0xa5, //0x00003630 .quad -6497648813669818282
	0xab, 0x6b, 0x30, 0x06, 0x62, 0xc1, 0xd0, 0x8f, //0x00003638 .quad -8083748704375247957
	0x6c, 0x18, 0x77, 0x98, 0x89, 0xa4, 0x48, 0x8f, //0x00003640 .quad -8122061017087272852
	0x96, 0x86, 0xbc, 0x87, 0xba, 0xf1, 0xc4, 0xb3, //0x00003648 .quad -5492999862041672042
	0x87, 0xde, 0x94, 0xfe, 0xab, 0xcd, 0x1a, 0x33, //0x00003650 .quad 3682481783923072647
	0x3c, 0xa8, 0xab, 0x29, 0x29, 0x2e, 0xb6, 0xe0, //0x00003658 .quad -2254563809124702148
	0x14, 0x0b, 0x1d, 0x7f, 0x8b, 0xc0, 0xf0, 0x9f, //0x00003660 .quad -6921820921902855404
	0x25, 0x49, 0x0b, 0xba, 0xd9, 0xdc, 0x71, 0x8c, //0x00003668 .quad -8326631408344020699
	0xd9, 0x4d, 0xe4, 0x5e, 0xae, 0xf0, 0xec, 0x07, //0x00003670 .quad 571095884476206553
	0x6f, 0x1b, 0x8e, 0x28, 0x10, 0x54, 0x8e, 0xaf, //0x00003678 .quad -5796603242002637969
	0x50, 0x61, 0x9d, 0xf6, 0xd9, 0x2c, 0xe8, 0xc9, //0x00003680 .quad -3897816162832129712
	0x4a, 0xa2, 0xb1, 0x32, 0x14, 0xe9, 0x71, 0xdb, //0x00003688 .quad -2634068034075909558
	0xd2, 0x5c, 0x22, 0x3a, 0x08, 0x1c, 0x31, 0xbe, //0x00003690 .quad -4741978110983775022
	0x6e, 0x05, 0xaf, 0x9f, 0xac, 0x31, 0x27, 0x89, //0x00003698 .quad -8563821548938525330
	0x06, 0xf4, 0xaa, 0x48, 0x0a, 0x63, 0xbd, 0x6d, //0x000036a0 .quad 7907585416552444934
	0xca, 0xc6, 0x9a, 0xc7, 0x17, 0xfe, 0x70, 0xab, //0x000036a8 .quad -6093090917745768758
	0x08, 0xb1, 0xd5, 0xda, 0xcc, 0xbb, 0x2c, 0x09, //0x000036b0 .quad 661109733835780360
	0x7d, 0x78, 0x81, 0xb9, 0x9d, 0x3d, 0x4d, 0xd6, //0x000036b8 .quad -3004677628754823043
	0xa5, 0x8e, 0xc5, 0x08, 0x60, 0xf5, 0xbb, 0x25, //0x000036c0 .quad 2719036592861056677
	0x4e, 0xeb, 0xf0, 0x93, 0x82, 0x46, 0xf0, 0x85, //0x000036c8 .quad -8795452545612846258
	0x4e, 0xf2, 0xf6, 0x0a, 0xb8, 0xf2, 0x2a, 0xaf, //0x000036d0 .quad -5824576295778454962
	0x21, 0x26, 0xed, 0x38, 0x23, 0x58, 0x6c, 0xa7, //0x000036d8 .quad -6382629663588669919
	0xe1, 0xae, 0xb4, 0x0d, 0x66, 0xaf, 0xf5, 0x1a, //0x000036e0 .quad 1942651667131707105
	0xaa, 0x6f, 0x28, 0x07, 0x2c, 0x6e, 0x47, 0xd1, //0x000036e8 .quad -3366601061058449494
	0x4d, 0xed, 0x90, 0xc8, 0x9f, 0x8d, 0xd9, 0x50, //0x000036f0 .quad 5825843310384704845
	0xca, 0x45, 0x79, 0x84, 0xdb, 0xa4, 0xcc, 0x82, //0x000036f8 .quad -9021654690802612790
	0xa0, 0x28, 0xb5, 0xba, 0x07, 0xf1, 0x0f, 0xe5, //0x00003700 .quad -1941067898873894752
	0x3c, 0x97, 0x97, 0x65, 0x12, 0xce, 0x7f, 0xa3, //0x00003708 .quad -6665382345075878084
	0xc8, 0x72, 0x62, 0xa9, 0x49, 0xed, 0x53, 0x1e, //0x00003710 .quad 2185351144835019464
	0x0c, 0x7d, 0xfd, 0xfe, 0x96, 0xc1, 0x5f, 0xcc, //0x00003718 .quad -3720041912917459700
	0x7a, 0x0f, 0xbb, 0x13, 0x9c, 0xe8, 0xe8, 0x25, //0x00003720 .quad 2731688931043774330
	0x4f, 0xdc, 0xbc, 0xbe, 0xfc, 0xb1, 0x77, 0xff, //0x00003728 .quad -38366372719436721
	0xac, 0xe9, 0x54, 0x8c, 0x61, 0x91, 0xb1, 0x77, //0x00003730 .quad 8624834609543440812
	0xb1, 0x09, 0x36, 0xf7, 0x3d, 0xcf, 0xaa, 0x9f, //0x00003738 .quad -6941508010590729807
	0x17, 0x24, 0x6a, 0xef, 0xb9, 0xf5, 0x9d, 0xd5, //0x00003740 .quad -3054014793352862697
	0x1d, 0x8c, 0x03, 0x75, 0x0d, 0x83, 0x95, 0xc7, //0x00003748 .quad -4065198994811024355
	0x1d, 0xad, 0x44, 0x6b, 0x28, 0x73, 0x05, 0x4b, //0x00003750 .quad 5405853545163697437
	0x25, 0x6f, 0x44, 0xd2, 0xd0, 0xe3, 0x7a, 0xf9, //0x00003758 .quad -469812725086392539
	0x32, 0xec, 0x0a, 0x43, 0xf9, 0x67, 0xe3, 0x4e, //0x00003760 .quad 5684501474941004850
	0x77, 0xc5, 0x6a, 0x83, 0x62, 0xce, 0xec, 0x9b, //0x00003768 .quad -7211161980820077193
	0x3f, 0xa7, 0xcd, 0x93, 0xf7, 0x41, 0x9c, 0x22, //0x00003770 .quad 2493940825248868159
	0xd5, 0x76, 0x45, 0x24, 0xfb, 0x01, 0xe8, 0xc2, //0x00003778 .quad -4402266457597708587
	0x0f, 0x11, 0xc1, 0x78, 0x75, 0x52, 0x43, 0x6b, //0x00003780 .quad 7729112049988473103
	0x8a, 0xd4, 0x56, 0xed, 0x79, 0x02, 0xa2, 0xf3, //0x00003788 .quad -891147053569747830
	0xa9, 0xaa, 0x78, 0x6b, 0x89, 0x13, 0x0a, 0x83, //0x00003790 .quad -9004363024039368023
	0xd6, 0x44, 0x56, 0x34, 0x8c, 0x41, 0x45, 0x98, //0x00003798 .quad -7474495936122174250
	0x53, 0xd5, 0x56, 0xc6, 0x6b, 0x98, 0xcc, 0x23, //0x000037a0 .quad 2579604275232953683
	0x0c, 0xd6, 0x6b, 0x41, 0xef, 0x91, 0x56, 0xbe, //0x000037a8 .quad -4731433901725329908
	0xa8, 0x8a, 0xec, 0xb7, 0x86, 0xbe, 0xbf, 0x2c, //0x000037b0 .quad 3224505344041192104
	0x8f, 0xcb, 0xc6, 0x11, 0x6b, 0x36, 0xec, 0xed, //0x000037b8 .quad -1302606358729274481
	0xa9, 0xd6, 0xf3, 0x32, 0x14, 0xd7, 0xf7, 0x7b, //0x000037c0 .quad 8932844867666826921
	0x39, 0x3f, 0x1c, 0xeb, 0x02, 0xa2, 0xb3, 0x94, //0x000037c8 .quad -7731658001846878407
	0x53, 0xcc, 0xb0, 0x3f, 0xd9, 0xcc, 0xf5, 0xda, //0x000037d0 .quad -2669001970698630061
	0x07, 0x4f, 0xe3, 0xa5, 0x83, 0x8a, 0xe0, 0xb9, //0x000037d8 .quad -5052886483881210105
	0x68, 0xff, 0x9c, 0x8f, 0x0f, 0x40, 0xb3, 0xd1, //0x000037e0 .quad -3336252463373287576
	0xc9, 0x22, 0x5c, 0x8f, 0x24, 0xad, 0x58, 0xe8, //0x000037e8 .quad -1704422086424124727
	0xa1, 0x1f, 0xc2, 0xb9, 0x09, 0x08, 0x10, 0x23, //0x000037f0 .quad 2526528228819083169
	0xbe, 0x95, 0x99, 0xd9, 0x36, 0x6c, 0x37, 0x91, //0x000037f8 .quad -7982792831656159810
	0x8a, 0xa7, 0x32, 0x28, 0x0c, 0x0a, 0xd4, 0xab, //0x00003800 .quad -6065211750830921846
	0x2d, 0xfb, 0xff, 0x8f, 0x44, 0x47, 0x85, 0xb5, //0x00003808 .quad -5366805021142811859
	0x6c, 0x51, 0x3f, 0x32, 0x8f, 0x0c, 0xc9, 0x16, //0x00003810 .quad 1641857348316123500
	0xf9, 0xf9, 0xff, 0xb3, 0x15, 0x99, 0xe6, 0xe2, //0x00003818 .quad -2096820258001126919
	0xe3, 0x92, 0x67, 0x7f, 0xd9, 0xa7, 0x3d, 0xae, //0x00003820 .quad -5891368184943504669
	0x3b, 0xfc, 0x7f, 0x90, 0xad, 0x1f, 0xd0, 0x8d, //0x00003828 .quad -8228041688891786181
	0x9c, 0x77, 0x41, 0xdf, 0xcf, 0x11, 0xcd, 0x99, //0x00003830 .quad -7364210231179380836
	0x4a, 0xfb, 0x9f, 0xf4, 0x98, 0x27, 0x44, 0xb1, //0x00003838 .quad -5673366092687344822
	0x83, 0xd5, 0x11, 0xd7, 0x43, 0x56, 0x40, 0x40, //0x00003840 .quad 4629795266307937667
	0x1d, 0xfa, 0xc7, 0x31, 0x7f, 0x31, 0x95, 0xdd, //0x00003848 .quad -2480021597431793123
	0x72, 0x25, 0x6b, 0x66, 0xea, 0x35, 0x28, 0x48, //0x00003850 .quad 5199465050656154994
	0x52, 0xfc, 0x1c, 0x7f, 0xef, 0x3e, 0x7d, 0x8a, //0x00003858 .quad -8467542526035952558
	0xcf, 0xee, 0x05, 0x00, 0x65, 0x43, 0x32, 0xda, //0x00003860 .quad -2724040723534582065
	0x66, 0x3b, 0xe4, 0x5e, 0xab, 0x8e, 0x1c, 0xad, //0x00003868 .quad -5972742139117552794
	0x82, 0x6a, 0x07, 0x40, 0x3e, 0xd4, 0xbe, 0x90, //0x00003870 .quad -8016736922845615486
	0x40, 0x4a, 0x9d, 0x36, 0x56, 0xb2, 0x63, 0xd8, //0x00003878 .quad -2854241655469553088
	0x91, 0xa2, 0x04, 0xe8, 0xa6, 0x44, 0x77, 0x5a, //0x00003880 .quad 6518754469289960081
	0x68, 0x4e, 0x22, 0xe2, 0x75, 0x4f, 0x3e, 0x87, //0x00003888 .quad -8701430062309552536
	0x36, 0xcb, 0x05, 0xa2, 0xd0, 0x15, 0x15, 0x71, //0x00003890 .quad 8148443086612450102
	0x02, 0xe2, 0xaa, 0x5a, 0x53, 0xe3, 0x0d, 0xa9, //0x00003898 .quad -6265101559459552766
	0x03, 0x3e, 0x87, 0xca, 0x44, 0x5b, 0x5a, 0x0d, //0x000038a0 .quad 962181821410786819
	0x83, 0x9a, 0x55, 0x31, 0x28, 0x5c, 0x51, 0xd3, //0x000038a8 .quad -3219690930897053053
	0xc2, 0x86, 0x94, 0xfe, 0x0a, 0x79, 0x58, 0xe8, //0x000038b0 .quad -1704479370831952190
	0x91, 0x80, 0xd5, 0x1e, 0x99, 0xd9, 0x12, 0x84, //0x000038b8 .quad -8929835859451740015
	0x72, 0xa8, 0x39, 0xbe, 0x4d, 0x97, 0x6e, 0x62, //0x000038c0 .quad 7092772823314835570
	0xb6, 0xe0, 0x8a, 0x66, 0xff, 0x8f, 0x17, 0xa5, //0x000038c8 .quad -6550608805887287114
	0x8f, 0x12, 0xc8, 0x2d, 0x21, 0x3d, 0x0a, 0xfb, //0x000038d0 .quad -357406007711231345
	0xe3, 0x98, 0x2d, 0x40, 0xff, 0x73, 0x5d, 0xce, //0x000038d8 .quad -3576574988931720989
	0x99, 0x0b, 0x9d, 0xbc, 0x34, 0x66, 0xe6, 0x7c, //0x000038e0 .quad 8999993282035256217
	0x8e, 0x7f, 0x1c, 0x88, 0x7f, 0x68, 0xfa, 0x80, //0x000038e8 .quad -9152888395723407474
	0x80, 0x4e, 0xc4, 0xeb, 0xc1, 0xff, 0x1f, 0x1c, //0x000038f0 .quad 2026619565689294464
	0x72, 0x9f, 0x23, 0x6a, 0x9f, 0x02, 0x39, 0xa1, //0x000038f8 .quad -6829424476226871438
	0x20, 0x62, 0xb5, 0x66, 0xb2, 0xff, 0x27, 0xa3, //0x00003900 .quad -6690097579743157728
	0x4e, 0x87, 0xac, 0x44, 0x47, 0x43, 0x87, 0xc9, //0x00003908 .quad -3925094576856201394
	0xa8, 0xba, 0x62, 0x00, 0x9f, 0xff, 0xf1, 0x4b, //0x00003910 .quad 5472436080603216552
	0x22, 0xa9, 0xd7, 0x15, 0x19, 0x14, 0xe9, 0xfb, //0x00003918 .quad -294682202642863838
	0xa9, 0xb4, 0x3d, 0x60, 0xc3, 0x3f, 0x77, 0x6f, //0x00003920 .quad 8031958568804398249
	0xb5, 0xc9, 0xa6, 0xad, 0x8f, 0xac, 0x71, 0x9d, //0x00003928 .quad -7101705404292871755
	0xd3, 0x21, 0x4d, 0x38, 0xb4, 0x0f, 0x55, 0xcb, //0x00003930 .quad -3795109844276665901
	0x22, 0x7c, 0x10, 0x99, 0xb3, 0x17, 0xce, 0xc4, //0x00003938 .quad -4265445736938701790
	0x48, 0x6a, 0x60, 0x46, 0xa1, 0x53, 0x2a, 0x7e, //0x00003940 .quad 9091170749936331336
	0x2b, 0x9b, 0x54, 0x7f, 0xa0, 0x9d, 0x01, 0xf6, //0x00003948 .quad -720121152745989333
	0x6d, 0x42, 0xfc, 0xcb, 0x44, 0x74, 0xda, 0x2e, //0x00003950 .quad 3376138709496513133
	0xfb, 0xe0, 0x94, 0x4f, 0x84, 0x02, 0xc1, 0x99, //0x00003958 .quad -7367604748107325189
	0x08, 0x53, 0xfb, 0xfe, 0x55, 0x11, 0x91, 0xfa, //0x00003960 .quad -391512631556746488
	0x39, 0x19, 0x7a, 0x63, 0x25, 0x43, 0x31, 0xc0, //0x00003968 .quad -4597819916706768583
	0xca, 0x27, 0xba, 0x7e, 0xab, 0x55, 0x35, 0x79, //0x00003970 .quad 8733981247408842698
	0x88, 0x9f, 0x58, 0xbc, 0xee, 0x93, 0x3d, 0xf0, //0x00003978 .quad -1135588877456072824
	0xde, 0x58, 0x34, 0x2f, 0x8b, 0x55, 0xc1, 0x4b, //0x00003980 .quad 5458738279630526686
	0xb5, 0x63, 0xb7, 0x35, 0x75, 0x7c, 0x26, 0x96, //0x00003988 .quad -7627272076051127371
	0x16, 0x6f, 0x01, 0xfb, 0xed, 0xaa, 0xb1, 0x9e, //0x00003990 .quad -7011635205744005354
	0xa2, 0x3c, 0x25, 0x83, 0x92, 0x1b, 0xb0, 0xbb, //0x00003998 .quad -4922404076636521310
	0xdc, 0xca, 0xc1, 0x79, 0xa9, 0x15, 0x5e, 0x46, //0x000039a0 .quad 5070514048102157020
	0xcb, 0x8b, 0xee, 0x23, 0x77, 0x22, 0x9c, 0xea, //0x000039a8 .quad -1541319077368263733
	0xc9, 0x1e, 0x19, 0xec, 0x89, 0xcd, 0xfa, 0x0b, //0x000039b0 .quad 863228270850154185
	0x5f, 0x17, 0x75, 0x76, 0x8a, 0x95, 0xa1, 0x92, //0x000039b8 .quad -7880853450996246689
	0x7b, 0x66, 0x1f, 0x67, 0xec, 0x80, 0xf9, 0xce, //0x000039c0 .quad -3532650679864695173
	0x36, 0x5d, 0x12, 0x14, 0xed, 0xfa, 0x49, 0xb7, //0x000039c8 .quad -5239380795317920458
	0x1a, 0x40, 0xe7, 0x80, 0x27, 0xe1, 0xb7, 0x82, //0x000039d0 .quad -9027499368258256870
	0x84, 0xf4, 0x16, 0x59, 0xa8, 0x79, 0x1c, 0xe5, //0x000039d8 .quad -1937539975720012668
	0x10, 0x88, 0x90, 0xb0, 0xb8, 0xec, 0xb2, 0xd1, //0x000039e0 .quad -3336344095947716592
	0xd2, 0x58, 0xae, 0x37, 0x09, 0xcc, 0x31, 0x8f, //0x000039e8 .quad -8128491512466089774
	0x15, 0xaa, 0xb4, 0xdc, 0xe6, 0xa7, 0x1f, 0x86, //0x000039f0 .quad -8782116138362033643
	0x07, 0xef, 0x99, 0x85, 0x0b, 0x3f, 0xfe, 0xb2, //0x000039f8 .quad -5548928372155224313
	0x9a, 0xd4, 0xe1, 0x93, 0xe0, 0x91, 0xa7, 0x67, //0x00003a00 .quad 7469098900757009562
	0xc9, 0x6a, 0x00, 0x67, 0xce, 0xce, 0xbd, 0xdf, //0x00003a08 .quad -2324474446766642487
	0xe0, 0x24, 0x6d, 0x5c, 0x2c, 0xbb, 0xc8, 0xe0, //0x00003a10 .quad -2249342214667950880
	0xbd, 0x42, 0x60, 0x00, 0x41, 0xa1, 0xd6, 0x8b, //0x00003a18 .quad -8370325556870233411
	0x18, 0x6e, 0x88, 0x73, 0xf7, 0xe9, 0xfa, 0x58, //0x00003a20 .quad 6411694268519837208
	0x6d, 0x53, 0x78, 0x40, 0x91, 0x49, 0xcc, 0xae, //0x00003a28 .quad -5851220927660403859
	0x9e, 0x89, 0x6a, 0x50, 0x75, 0xa4, 0x39, 0xaf, //0x00003a30 .quad -5820440219632367202
	0x48, 0x68, 0x96, 0x90, 0xf5, 0x5b, 0x7f, 0xda, //0x00003a38 .quad -2702340141148116920
	0x03, 0x96, 0x42, 0x52, 0xc9, 0x06, 0x84, 0x6d, //0x00003a40 .quad 7891439908798240259
	0x2d, 0x01, 0x5e, 0x7a, 0x79, 0x99, 0x8f, 0x88, //0x00003a48 .quad -8606491615858654931
	0x83, 0x3b, 0xd3, 0xa6, 0x7b, 0x08, 0xe5, 0xc8, //0x00003a50 .quad -3970758169284363389
	0x78, 0x81, 0xf5, 0xd8, 0xd7, 0x7f, 0xb3, 0xaa, //0x00003a58 .quad -6146428501395930760
	0x64, 0x0a, 0x88, 0x90, 0x9a, 0x4a, 0x1e, 0xfb, //0x00003a60 .quad -351761693178066332
	0xd6, 0xe1, 0x32, 0xcf, 0xcd, 0x5f, 0x60, 0xd5, //0x00003a68 .quad -3071349608317525546
	0x7f, 0x06, 0x55, 0x9a, 0xa0, 0xee, 0xf2, 0x5c, //0x00003a70 .quad 6697677969404790399
	0x26, 0xcd, 0x7f, 0xa1, 0xe0, 0x3b, 0x5c, 0x85, //0x00003a78 .quad -8837122532839535322
	0x1e, 0x48, 0xea, 0xc0, 0x48, 0xaa, 0x2f, 0xf4, //0x00003a80 .quad -851274575098787810
	0x6f, 0xc0, 0xdf, 0xc9, 0xd8, 0x4a, 0xb3, 0xa6, //0x00003a88 .quad -6434717147622031249
	0x26, 0xda, 0x24, 0xf1, 0xda, 0x94, 0x3b, 0xf1, //0x00003a90 .quad -1064093218873484762
	0x8b, 0xb0, 0x57, 0xfc, 0x8e, 0x1d, 0x60, 0xd0, //0x00003a98 .quad -3431710416100151157
	0x58, 0x08, 0xb7, 0xd6, 0x08, 0x3d, 0xc5, 0x76, //0x00003aa0 .quad 8558313775058847832
	0x57, 0xce, 0xb6, 0x5d, 0x79, 0x12, 0x3c, 0x82, //0x00003aa8 .quad -9062348037703676329
	0x6e, 0xca, 0x64, 0x0c, 0x4b, 0x8c, 0x76, 0x54, //0x00003ab0 .quad 6086206200396171886
	0xed, 0x81, 0x24, 0xb5, 0x17, 0x17, 0xcb, 0xa2, //0x00003ab8 .quad -6716249028702207507
	0x09, 0xfd, 0x7d, 0xcf, 0x5d, 0x2f, 0x94, 0xa9, //0x00003ac0 .quad -6227300304786948855
	0x68, 0xa2, 0x6d, 0xa2, 0xdd, 0xdc, 0x7d, 0xcb, //0x00003ac8 .quad -3783625267450371480
	0x4c, 0x7c, 0x5d, 0x43, 0x35, 0x3b, 0xf9, 0xd3, //0x00003ad0 .quad -3172439362556298164
	0x02, 0x0b, 0x09, 0x0b, 0x15, 0x54, 0x5d, 0xfe, //0x00003ad8 .quad -117845565885576446
	0xaf, 0x6d, 0x1a, 0x4a, 0x01, 0xc5, 0x7b, 0xc4, //0x00003ae0 .quad -4288617610811380305
	0xe1, 0xa6, 0xe5, 0x26, 0x8d, 0x54, 0xfa, 0x9e, //0x00003ae8 .quad -6991182506319567135
	0x1b, 0x09, 0xa1, 0x9c, 0x41, 0xb6, 0x9a, 0x35, //0x00003af0 .quad 3862600023340550427
	0x9a, 0x10, 0x9f, 0x70, 0xb0, 0xe9, 0xb8, 0xc6, //0x00003af8 .quad -4127292114472071014
	0x62, 0x4b, 0xc9, 0x03, 0xd2, 0x63, 0x01, 0xc3, //0x00003b00 .quad -4395122007679087774
	0xc0, 0xd4, 0xc6, 0x8c, 0x1c, 0x24, 0x67, 0xf8, //0x00003b08 .quad -547429124662700864
	0x1d, 0xcf, 0x5d, 0x42, 0x63, 0xde, 0xe0, 0x79, //0x00003b10 .quad 8782263791269039901
	0xf8, 0x44, 0xfc, 0xd7, 0x91, 0x76, 0x40, 0x9b, //0x00003b18 .quad -7259672230555269896
	0xe4, 0x42, 0xf5, 0x12, 0xfc, 0x15, 0x59, 0x98, //0x00003b20 .quad -7468914334623251740
	0x36, 0x56, 0xfb, 0x4d, 0x36, 0x94, 0x10, 0xc2, //0x00003b28 .quad -4462904269766699466
	0x9d, 0x93, 0xb2, 0x17, 0x7b, 0x5b, 0x6f, 0x3e, //0x00003b30 .quad 4498915137003099037
	0xc4, 0x2b, 0x7a, 0xe1, 0x43, 0xb9, 0x94, 0xf2, //0x00003b38 .quad -966944318780986428
	0x42, 0x9c, 0xcf, 0xee, 0x2c, 0x99, 0x05, 0xa7, //0x00003b40 .quad -6411550076227838910
	0x5a, 0x5b, 0xec, 0x6c, 0xca, 0xf3, 0x9c, 0x97, //0x00003b48 .quad -7521869226879198374
	0x53, 0x83, 0x83, 0x2a, 0x78, 0xff, 0xc6, 0x50, //0x00003b50 .quad 5820620459997365075
	0x31, 0x72, 0x27, 0x08, 0xbd, 0x30, 0x84, 0xbd, //0x00003b58 .quad -4790650515171610063
	0x28, 0x64, 0x24, 0x35, 0x56, 0xbf, 0xf8, 0xa4, //0x00003b60 .quad -6559282480285457368
	0xbd, 0x4e, 0x31, 0x4a, 0xec, 0x3c, 0xe5, 0xec, //0x00003b68 .quad -1376627125537124675
	0x99, 0xbe, 0x36, 0xe1, 0x95, 0x77, 0x1b, 0x87, //0x00003b70 .quad -8711237568605798759
	0x36, 0xd1, 0x5e, 0xae, 0x13, 0x46, 0x0f, 0x94, //0x00003b78 .quad -7777920981101784778
	0x3f, 0x6e, 0x84, 0x59, 0x7b, 0x55, 0xe2, 0x28, //0x00003b80 .quad 2946011094524915263
	0x84, 0x85, 0xf6, 0x99, 0x98, 0x17, 0x13, 0xb9, //0x00003b88 .quad -5110715207949843068
	0xcf, 0x89, 0xe5, 0x2f, 0xda, 0xea, 0x1a, 0x33, //0x00003b90 .quad 3682513868156144079
	0xe5, 0x26, 0x74, 0xc0, 0x7e, 0xdd, 0x57, 0xe7, //0x00003b98 .quad -1776707991509915931
	0x21, 0x76, 0xef, 0x5d, 0xc8, 0xd2, 0xf0, 0x3f, //0x00003ba0 .quad 4607414176811284001
	0x4f, 0x98, 0x48, 0x38, 0x6f, 0xea, 0x96, 0x90, //0x00003ba8 .quad -8027971522334779313
	0xa9, 0x53, 0x6b, 0x75, 0x7a, 0x07, 0xed, 0x0f, //0x00003bb0 .quad 1147581702586717097
	0x63, 0xbe, 0x5a, 0x06, 0x0b, 0xa5, 0xbc, 0xb4, //0x00003bb8 .quad -5423278384491086237
	0x94, 0x28, 0xc6, 0x12, 0x59, 0x49, 0xe8, 0xd3, //0x00003bc0 .quad -3177208890193991532
	0xfb, 0x6d, 0xf1, 0xc7, 0x4d, 0xce, 0xeb, 0xe1, //0x00003bc8 .quad -2167411962186469893
	0x5c, 0xd9, 0xbb, 0xab, 0xd7, 0x2d, 0x71, 0x64, //0x00003bd0 .quad 7237616480483531100
	0xbd, 0xe4, 0xf6, 0x9c, 0xf0, 0x60, 0x33, 0x8d, //0x00003bd8 .quad -8272161504007625539
	0xb3, 0xcf, 0xaa, 0x96, 0x4d, 0x79, 0x8d, 0xbd, //0x00003be0 .quad -4788037454677749837
	0xec, 0x9d, 0x34, 0xc4, 0x2c, 0x39, 0x80, 0xb0, //0x00003be8 .quad -5728515861582144020
	0xa0, 0x83, 0x55, 0xfc, 0xa0, 0xd7, 0xf0, 0xec, //0x00003bf0 .quad -1373360799919799392
	0x67, 0xc5, 0x41, 0xf5, 0x77, 0x47, 0xa0, 0xdc, //0x00003bf8 .quad -2548958808550292121
	0x44, 0x72, 0xb5, 0x9d, 0xc4, 0x86, 0x16, 0xf4, //0x00003c00 .quad -858350499949874620
	0x60, 0x1b, 0x49, 0xf9, 0xaa, 0x2c, 0xe4, 0x89, //0x00003c08 .quad -8510628282985014432
	0xd5, 0xce, 0x22, 0xc5, 0x75, 0x28, 0x1c, 0x31, //0x00003c10 .quad 3538747893490044629
	0x39, 0x62, 0x9b, 0xb7, 0xd5, 0x37, 0x5d, 0xac, //0x00003c18 .quad -6026599335303880135
	0x8b, 0x82, 0x6b, 0x36, 0x93, 0x32, 0x63, 0x7d, //0x00003c20 .quad 9035120885289943691
	0xc7, 0x3a, 0x82, 0x25, 0xcb, 0x85, 0x74, 0xd7, //0x00003c28 .quad -2921563150702462265
	0x97, 0x31, 0x03, 0x02, 0x9c, 0xff, 0x5d, 0xae, //0x00003c30 .quad -5882264492762254953
	0xbc, 0x64, 0x71, 0xf7, 0x9e, 0xd3, 0xa8, 0x86, //0x00003c38 .quad -8743505996830120772
	0xfc, 0xfd, 0x83, 0x02, 0x83, 0x7f, 0xf5, 0xd9, //0x00003c40 .quad -2741144597525430788
	0xeb, 0xbd, 0x4d, 0xb5, 0x86, 0x08, 0x53, 0xa8, //0x00003c48 .quad -6317696477610263061
	0x7b, 0xfd, 0x24, 0xc3, 0x63, 0xdf, 0x72, 0xd0, //0x00003c50 .quad -3426430746906788485
	0x66, 0x2d, 0xa1, 0x62, 0xa8, 0xca, 0x67, 0xd2, //0x00003c58 .quad -3285434578585440922
	0x6d, 0x1e, 0xf7, 0x59, 0x9e, 0xcb, 0x47, 0x42, //0x00003c60 .quad 4776009810824339053
	0x60, 0xbc, 0xa4, 0x3d, 0xa9, 0xde, 0x80, 0x83, //0x00003c68 .quad -8970925639256982432
	0x08, 0xe6, 0x74, 0xf0, 0x85, 0xbe, 0xd9, 0x52, //0x00003c70 .quad 5970012263530423816
	0x78, 0xeb, 0x0d, 0x8d, 0x53, 0x16, 0x61, 0xa4, //0x00003c78 .quad -6601971030643840136
	0x8b, 0x1f, 0x92, 0x6c, 0x27, 0x2e, 0x90, 0x67, //0x00003c80 .quad 7462515329413029771
	0x56, 0x66, 0x51, 0x70, 0xe8, 0x5b, 0x79, 0xcd, //0x00003c88 .quad -3640777769877412266
	0xb6, 0x53, 0xdb, 0xa3, 0xd8, 0x1c, 0xba, 0x00, //0x00003c90 .quad 52386062455755702
	0xf6, 0xdf, 0x32, 0x46, 0x71, 0xd9, 0x6b, 0x80, //0x00003c98 .quad -9193015133814464522
	0xa4, 0x28, 0xd2, 0xcc, 0x0e, 0xa4, 0xe8, 0x80, //0x00003ca0 .quad -9157889458785081180
	0xf3, 0x97, 0xbf, 0x97, 0xcd, 0xcf, 0x86, 0xa0, //0x00003ca8 .quad -6879582898840692749
	0xcd, 0xb2, 0x06, 0x80, 0x12, 0xcd, 0x22, 0x61, //0x00003cb0 .quad 6999382250228200141
	0xf0, 0x7d, 0xaf, 0xfd, 0xc0, 0x83, 0xa8, 0xc8, //0x00003cb8 .quad -3987792605123478032
	0x81, 0x5f, 0x08, 0x20, 0x57, 0x80, 0x6b, 0x79, //0x00003cc0 .quad 8749227812785250177
	0x6c, 0x5d, 0x1b, 0x3d, 0xb1, 0xa4, 0xd2, 0xfa, //0x00003cc8 .quad -373054737976959636
	0xb0, 0x3b, 0x05, 0x74, 0x36, 0x30, 0xe3, 0xcb, //0x00003cd0 .quad -3755104653863994448
	0x63, 0x1a, 0x31, 0xc6, 0xee, 0xa6, 0xc3, 0x9c, //0x00003cd8 .quad -7150688238876681629
	0x9c, 0x8a, 0x06, 0x11, 0x44, 0xfc, 0xdb, 0xbe, //0x00003ce0 .quad -4693880817329993060
	0xfc, 0x60, 0xbd, 0x77, 0xaa, 0x90, 0xf4, 0xc3, //0x00003ce8 .quad -4326674280168464132
	0x44, 0x2d, 0x48, 0x15, 0x55, 0xfb, 0x92, 0xee, //0x00003cf0 .quad -1255665003235103420
	0x3b, 0xb9, 0xac, 0x15, 0xd5, 0xb4, 0xf1, 0xf4, //0x00003cf8 .quad -796656831783192261
	0x4a, 0x1c, 0x4d, 0x2d, 0x15, 0xdd, 0x1b, 0x75, //0x00003d00 .quad 8438581409832836170
	0xc5, 0xf3, 0x8b, 0x2d, 0x05, 0x11, 0x17, 0x99, //0x00003d08 .quad -7415439547505577019
	0x5d, 0x63, 0xa0, 0x78, 0x5a, 0xd4, 0x62, 0xd2, //0x00003d10 .quad -3286831292991118499
	0xb6, 0xf0, 0xee, 0x78, 0x46, 0xd5, 0x5c, 0xbf, //0x00003d18 .quad -4657613415954583370
	0x34, 0x7c, 0xc8, 0x16, 0x71, 0x89, 0xfb, 0x86, //0x00003d20 .quad -8720225134666286028
	0xe4, 0xac, 0x2a, 0x17, 0x98, 0x0a, 0x34, 0xef, //0x00003d28 .quad -1210330751515841308
	0xa0, 0x4d, 0x3d, 0xae, 0xe6, 0x35, 0x5d, 0xd4, //0x00003d30 .quad -3144297699952734816
	0x0e, 0xac, 0x7a, 0x0e, 0x9f, 0x86, 0x80, 0x95, //0x00003d38 .quad -7673985747338482674
	0x09, 0xa1, 0xcc, 0x59, 0x60, 0x83, 0x74, 0x89, //0x00003d40 .quad -8542058143368306423
	0x12, 0x57, 0x19, 0xd2, 0x46, 0xa8, 0xe0, 0xba, //0x00003d48 .quad -4980796165745715438
	0x4b, 0xc9, 0x3f, 0x70, 0x38, 0xa4, 0xd1, 0x2b, //0x00003d50 .quad 3157485376071780683
	0xd7, 0xac, 0x9f, 0x86, 0x58, 0xd2, 0x98, 0xe9, //0x00003d58 .quad -1614309188754756393
	0xcf, 0xdd, 0x27, 0x46, 0xa3, 0x06, 0x63, 0x7b, //0x00003d60 .quad 8890957387685944783
	0x06, 0xcc, 0x23, 0x54, 0x77, 0x83, 0xff, 0x91, //0x00003d68 .quad -7926472270612804602
	0x42, 0xd5, 0xb1, 0x17, 0x4c, 0xc8, 0x3b, 0x1a, //0x00003d70 .quad 1890324697752655170
	0x08, 0xbf, 0x2c, 0x29, 0x55, 0x64, 0x7f, 0xb6, //0x00003d78 .quad -5296404319838617848
	0x93, 0x4a, 0x9e, 0x1d, 0x5f, 0xba, 0xca, 0x20, //0x00003d80 .quad 2362905872190818963
	0xca, 0xee, 0x77, 0x73, 0x6a, 0x3d, 0x1f, 0xe4, //0x00003d88 .quad -2008819381370884406
	0x9c, 0xee, 0x82, 0x72, 0x7b, 0xb4, 0x7e, 0x54, //0x00003d90 .quad 6088502188546649756
	0x3e, 0xf5, 0x2a, 0x88, 0x62, 0x86, 0x93, 0x8e, //0x00003d98 .quad -8173041140997884610
	0x43, 0xaa, 0x23, 0x4f, 0x9a, 0x61, 0x9e, 0xe9, //0x00003da0 .quad -1612744301171463613
	0x8d, 0xb2, 0x35, 0x2a, 0xfb, 0x67, 0x38, 0xb2, //0x00003da8 .quad -5604615407819967859
	0xd4, 0x94, 0xec, 0xe2, 0x00, 0xfa, 0x05, 0x64, //0x00003db0 .quad 7207441660390446292
	0x31, 0x1f, 0xc3, 0xf4, 0xf9, 0x81, 0xc6, 0xde, //0x00003db8 .quad -2394083241347571919
	0x04, 0xdd, 0xd3, 0x8d, 0x40, 0xbc, 0x83, 0xde, //0x00003dc0 .quad -2412877989897052924
	0x7e, 0xf3, 0xf9, 0x38, 0x3c, 0x11, 0x3c, 0x8b, //0x00003dc8 .quad -8413831053483314306
	0x45, 0xd4, 0x48, 0xb1, 0x50, 0xab, 0x24, 0x96, //0x00003dd0 .quad -7627783505798704059
	0x5e, 0x70, 0x38, 0x47, 0x8b, 0x15, 0x0b, 0xae, //0x00003dd8 .quad -5905602798426754978
	0x57, 0x09, 0x9b, 0xdd, 0x24, 0xd6, 0xad, 0x3b, //0x00003de0 .quad 4300328673033783639
	0x76, 0x8c, 0x06, 0x19, 0xee, 0xda, 0x8d, 0xd9, //0x00003de8 .quad -2770317479606055818
	0xd6, 0xe5, 0x80, 0x0a, 0xd7, 0xa5, 0x4c, 0xe5, //0x00003df0 .quad -1923980597781273130
	0xc9, 0x17, 0xa4, 0xcf, 0xd4, 0xa8, 0xf8, 0x87, //0x00003df8 .quad -8648977452394866743
	0x4c, 0x1f, 0x21, 0xcd, 0x4c, 0xcf, 0x9f, 0x5e, //0x00003e00 .quad 6818396289628184396
	0xbc, 0x1d, 0x8d, 0x03, 0x0a, 0xd3, 0xf6, 0xa9, //0x00003e08 .quad -6199535797066195524
	0x1f, 0x67, 0x69, 0x00, 0x20, 0xc3, 0x47, 0x76, //0x00003e10 .quad 8522995362035230495
	0x2b, 0x65, 0x70, 0x84, 0xcc, 0x87, 0x74, 0xd4, //0x00003e18 .quad -3137733727905356501
	0x73, 0xe0, 0x41, 0x00, 0xf4, 0xd9, 0xec, 0x29, //0x00003e20 .quad 3021029092058325107
	0x3b, 0x3f, 0xc6, 0xd2, 0xdf, 0xd4, 0xc8, 0x84, //0x00003e28 .quad -8878612607581929669
	0x90, 0x58, 0x52, 0x00, 0x71, 0x10, 0x68, 0xf4, //0x00003e30 .quad -835399653354481520
	0x09, 0xcf, 0x77, 0xc7, 0x17, 0x0a, 0xfb, 0xa5, //0x00003e38 .quad -6486579741050024183
	0xb4, 0xee, 0x66, 0x40, 0x8d, 0x14, 0x82, 0x71, //0x00003e40 .quad 8179122470161673908
	0xcc, 0xc2, 0x55, 0xb9, 0x9d, 0xcc, 0x79, 0xcf, //0x00003e48 .quad -3496538657885142324
	0x30, 0x55, 0x40, 0x48, 0xd8, 0x4c, 0xf1, 0xc6, //0x00003e50 .quad -4111420493003729616
	0xbf, 0x99, 0xd5, 0x93, 0xe2, 0x1f, 0xac, 0x81, //0x00003e58 .quad -9102865688819295809
	0x7c, 0x6a, 0x50, 0x5a, 0x0e, 0xa0, 0xad, 0xb8, //0x00003e60 .quad -5139275616254662020
	0x2f, 0x00, 0xcb, 0x38, 0xdb, 0x27, 0x17, 0xa2, //0x00003e68 .quad -6766896092596731857
	0x1c, 0x85, 0xe4, 0xf0, 0x11, 0x08, 0xd9, 0xa6, //0x00003e70 .quad -6424094520318327524
	0x3b, 0xc0, 0xfd, 0x06, 0xd2, 0xf1, 0x9c, 0xca, //0x00003e78 .quad -3846934097318526917
	0x63, 0xa6, 0x1d, 0x6d, 0x16, 0x4a, 0x8f, 0x90, //0x00003e80 .quad -8030118150397909405
	0x4a, 0x30, 0xbd, 0x88, 0x46, 0x2e, 0x44, 0xfd, //0x00003e88 .quad -196981603220770742
	0xfe, 0x87, 0x32, 0x04, 0x4e, 0x8e, 0x59, 0x9a, //0x00003e90 .quad -7324666853212387330
	0x2e, 0x3e, 0x76, 0x15, 0xec, 0x9c, 0x4a, 0x9e, //0x00003e98 .quad -7040642529654063570
	0xfd, 0x29, 0x3f, 0x85, 0xe1, 0xf1, 0xef, 0x40, //0x00003ea0 .quad 4679224488766679549
	0xba, 0xcd, 0xd3, 0x1a, 0x27, 0x44, 0xdd, 0xc5, //0x00003ea8 .quad -4189117143640191558
	0x7c, 0xf4, 0x8e, 0xe6, 0x59, 0xee, 0x2b, 0xd1, //0x00003eb0 .quad -3374341425896426372
	0x28, 0xc1, 0x88, 0xe1, 0x30, 0x95, 0x54, 0xf7, //0x00003eb8 .quad -624710411122851544
	0xce, 0x58, 0x19, 0x30, 0xf8, 0x74, 0xbb, 0x82, //0x00003ec0 .quad -9026492418826348338
	0xb9, 0x78, 0xf5, 0x8c, 0x3e, 0xdd, 0x94, 0x9a, //0x00003ec8 .quad -7307973034592864071
	0x01, 0xaf, 0x1f, 0x3c, 0x36, 0x52, 0x6a, 0xe3, //0x00003ed0 .quad -2059743486678159615
	0xe7, 0xd6, 0x32, 0x30, 0x8e, 0x14, 0x3a, 0xc1, //0x00003ed8 .quad -4523280274813692185
	0xc1, 0x9a, 0x27, 0xcb, 0xc3, 0xe6, 0x44, 0xdc, //0x00003ee0 .quad -2574679358347699519
	0xa1, 0x8c, 0x3f, 0xbc, 0xb1, 0x99, 0x88, 0xf1, //0x00003ee8 .quad -1042414325089727327
	0xb9, 0xc0, 0xf8, 0x5e, 0x3a, 0x10, 0xab, 0x29, //0x00003ef0 .quad 3002511419460075705
	0xe5, 0xb7, 0xa7, 0x15, 0x0f, 0x60, 0xf5, 0x96, //0x00003ef8 .quad -7569037980822161435
	0xe7, 0xf0, 0xb6, 0xf6, 0x48, 0xd4, 0x15, 0x74, //0x00003f00 .quad 8364825292752482535
	0xde, 0xa5, 0x11, 0xdb, 0x12, 0xb8, 0xb2, 0xbc, //0x00003f08 .quad -4849611457600313890
	0x21, 0xad, 0x64, 0x34, 0x5b, 0x49, 0x1b, 0x11, //0x00003f10 .quad 1232659579085827361
	0x56, 0x0f, 0xd6, 0x91, 0x17, 0x66, 0xdf, 0xeb, //0x00003f18 .quad -1450328303573004458
	0x34, 0xec, 0xbe, 0x00, 0xd9, 0x0d, 0xb1, 0xca, //0x00003f20 .quad -3841273781498745804
	0x95, 0xc9, 0x25, 0xbb, 0xce, 0x9f, 0x6b, 0x93, //0x00003f28 .quad -7823984217374209643
	0x42, 0xa7, 0xee, 0x40, 0x4f, 0x51, 0x5d, 0x3d, //0x00003f30 .quad 4421779809981343554
	0xfb, 0x3b, 0xef, 0x69, 0xc2, 0x87, 0x46, 0xb8, //0x00003f38 .quad -5168294253290374149
	0x12, 0x51, 0x2a, 0x11, 0xa3, 0xa5, 0xb4, 0x0c, //0x00003f40 .quad 915538744049291538
	0xfa, 0x0a, 0x6b, 0x04, 0xb3, 0x29, 0x58, 0xe6, //0x00003f48 .quad -1848681798185579782
	0xab, 0x72, 0xba, 0xea, 0x85, 0xe7, 0xf0, 0x47, //0x00003f50 .quad 5183897733458195115
	0xdc, 0xe6, 0xc2, 0xe2, 0x0f, 0x1a, 0xf7, 0x8f, //0x00003f58 .quad -8072955151507069220
	0x56, 0x0f, 0x69, 0x65, 0x67, 0x21, 0xed, 0x59, //0x00003f60 .quad 6479872166822743894
	0x93, 0xa0, 0x73, 0xdb, 0x93, 0xe0, 0xf4, 0xb3, //0x00003f68 .quad -5479507920956448621
	0x2c, 0x53, 0xc3, 0x3e, 0xc1, 0x69, 0x68, 0x30, //0x00003f70 .quad 3488154190101041964
	0xb8, 0x88, 0x50, 0xd2, 0xb8, 0x18, 0xf2, 0xe0, //0x00003f78 .quad -2237698882768172872
	0xfb, 0x13, 0x3a, 0xc7, 0x18, 0x42, 0x41, 0x1e, //0x00003f80 .quad 2180096368813151227
	0x73, 0x55, 0x72, 0x83, 0x73, 0x4f, 0x97, 0x8c, //0x00003f88 .quad -8316090829371189901
	0xfa, 0x98, 0x08, 0xf9, 0x9e, 0x92, 0xd1, 0xe5, //0x00003f90 .quad -1886565557410948870
	0xcf, 0xea, 0x4e, 0x64, 0x50, 0x23, 0xbd, 0xaf, //0x00003f98 .quad -5783427518286599473
	0x39, 0xbf, 0x4a, 0xb7, 0x46, 0xf7, 0x45, 0xdf, //0x00003fa0 .quad -2358206946763686087
	0x83, 0xa5, 0x62, 0x7d, 0x24, 0x6c, 0xac, 0xdb, //0x00003fa8 .quad -2617598379430861437
	0x83, 0xb7, 0x8e, 0x32, 0x8c, 0xba, 0x8b, 0x6b, //0x00003fb0 .quad 7749492695127472003
	0x72, 0xa7, 0x5d, 0xce, 0x96, 0xc3, 0x4b, 0x89, //0x00003fb8 .quad -8553528014785370254
	0x64, 0x65, 0x32, 0x3f, 0x2f, 0xa9, 0x6e, 0x06, //0x00003fc0 .quad 463493832054564196
	0x4f, 0x11, 0xf5, 0x81, 0x7c, 0xb4, 0x9e, 0xab, //0x00003fc8 .quad -6080224000054324913
	0xbd, 0xfe, 0xfe, 0x0e, 0x7b, 0x53, 0x0a, 0xc8, //0x00003fd0 .quad -4032318728359182659
	0xa2, 0x55, 0x72, 0xa2, 0x9b, 0x61, 0x86, 0xd6, //0x00003fd8 .quad -2988593981640518238
	0x36, 0x5f, 0x5f, 0xe9, 0x2c, 0x74, 0x06, 0xbd, //0x00003fe0 .quad -4826042214438183114
	0x85, 0x75, 0x87, 0x45, 0x01, 0xfd, 0x13, 0x86, //0x00003fe8 .quad -8785400266166405755
	0x04, 0x37, 0xb7, 0x23, 0x38, 0x11, 0x48, 0x2c, //0x00003ff0 .quad 3190819268807046916
	0xe7, 0x52, 0xe9, 0x96, 0x41, 0xfc, 0x98, 0xa7, //0x00003ff8 .quad -6370064314280619289
	0xc5, 0x04, 0xa5, 0x2c, 0x86, 0x15, 0x5a, 0xf7, //0x00004000 .quad -623161932418579259
	0xa0, 0xa7, 0xa3, 0xfc, 0x51, 0x3b, 0x7f, 0xd1, //0x00004008 .quad -3350894374423386208
	0xfb, 0x22, 0xe7, 0xdb, 0x73, 0x4d, 0x98, 0x9a, //0x00004010 .quad -7307005235402693893
	0xc4, 0x48, 0xe6, 0x3d, 0x13, 0x85, 0xef, 0x82, //0x00004018 .quad -9011838011655698236
	0xba, 0xeb, 0xe0, 0xd2, 0xd0, 0x60, 0x3e, 0xc1, //0x00004020 .quad -4522070525825979462
	0xf5, 0xda, 0x5f, 0x0d, 0x58, 0x66, 0xab, 0xa3, //0x00004028 .quad -6653111496142234891
	0xa8, 0x26, 0x99, 0x07, 0x05, 0xf9, 0x8d, 0x31, //0x00004030 .quad 3570783879572301480
	0xb3, 0xd1, 0xb7, 0x10, 0xee, 0x3f, 0x96, 0xcc, //0x00004038 .quad -3704703351750405709
	0x52, 0x70, 0x7f, 0x49, 0x46, 0x77, 0xf1, 0xfd, //0x00004040 .quad -148206168962011054
	0x1f, 0xc6, 0xe5, 0x94, 0xe9, 0xcf, 0xbb, 0xff, //0x00004048 .quad -19193171260619233
	0x33, 0xa6, 0xef, 0xed, 0x8b, 0xea, 0xb6, 0xfe, //0x00004050 .quad -92628855601256909
	0xd3, 0x9b, 0x0f, 0xfd, 0xf1, 0x61, 0xd5, 0x9f, //0x00004058 .quad -6929524759678968877
	0xc0, 0x8f, 0x6b, 0xe9, 0x2e, 0xa5, 0x64, 0xfe, //0x00004060 .quad -115786069501571136
	0xc8, 0x82, 0x53, 0x7c, 0x6e, 0xba, 0xca, 0xc7, //0x00004068 .quad -4050219931171323192
	0xb0, 0x73, 0xc6, 0xa3, 0x7a, 0xce, 0xfd, 0x3d, //0x00004070 .quad 4466953431550423984
	0x7b, 0x63, 0x68, 0x1b, 0x0a, 0x69, 0xbd, 0xf9, //0x00004078 .quad -451088895536766085
	0x4e, 0x08, 0x5c, 0xa6, 0x0c, 0xa1, 0xbe, 0x06, //0x00004080 .quad 486002885505321038
	0x2d, 0x3e, 0x21, 0x51, 0xa6, 0x61, 0x16, 0x9c, //0x00004088 .quad -7199459587351560659
	0x62, 0x0a, 0xf3, 0xcf, 0x4f, 0x49, 0x6e, 0x48, //0x00004090 .quad 5219189625309039202
	0xb8, 0x8d, 0x69, 0xe5, 0x0f, 0xfa, 0x1b, 0xc3, //0x00004098 .quad -4387638465762062920
	0xfa, 0xcc, 0xef, 0xc3, 0xa3, 0xdb, 0x89, 0x5a, //0x000040a0 .quad 6523987031636299002
	0x26, 0xf1, 0xc3, 0xde, 0x93, 0xf8, 0xe2, 0xf3, //0x000040a8 .quad -872862063775190746
	0x1c, 0xe0, 0x75, 0x5a, 0x46, 0x29, 0x96, 0xf8, //0x000040b0 .quad -534194123654701028
	0xb7, 0x76, 0x3a, 0x6b, 0x5c, 0xdb, 0x6d, 0x98, //0x000040b8 .quad -7463067817500576073
	0x23, 0x58, 0x13, 0xf1, 0x97, 0xb3, 0xbb, 0xf6, //0x000040c0 .quad -667742654568376285
	0x65, 0x14, 0x09, 0x86, 0x33, 0x52, 0x89, 0xbe, //0x000040c8 .quad -4717148753448332187
	0x2c, 0x2e, 0x58, 0xed, 0x7d, 0xa0, 0x6a, 0x74, //0x000040d0 .quad 8388693718644305452
	0x7f, 0x59, 0x8b, 0x67, 0xc0, 0xa6, 0x2b, 0xee, //0x000040d8 .quad -1284749923383027329
	0xdc, 0x1c, 0x57, 0xb4, 0x4e, 0xa4, 0xc2, 0xa8, //0x000040e0 .quad -6286281471915778852
	0xef, 0x17, 0xb7, 0x40, 0x38, 0x48, 0xdb, 0x94, //0x000040e8 .quad -7720497729755473937
	0x13, 0xe4, 0x6c, 0x61, 0x62, 0x4d, 0xf3, 0x92, //0x000040f0 .quad -7857851839894723565
	0xeb, 0xdd, 0xe4, 0x50, 0x46, 0x1a, 0x12, 0xba, //0x000040f8 .quad -5038936143766954517
	0x17, 0x1d, 0xc8, 0xf9, 0xba, 0x20, 0xb0, 0x77, //0x00004100 .quad 8624429273841147159
	0x66, 0x15, 0x1e, 0xe5, 0xd7, 0xa0, 0x96, 0xe8, //0x00004108 .quad -1686984161281305242
	0x2e, 0x12, 0x1d, 0xdc, 0x74, 0x14, 0xce, 0x0a, //0x00004110 .quad 778582277723329070
	0x60, 0xcd, 0x32, 0xef, 0x86, 0x24, 0x5e, 0x91, //0x00004118 .quad -7971894128441897632
	0xba, 0x56, 0x24, 0x13, 0x92, 0x99, 0x81, 0x0d, //0x00004120 .quad 973227847154161338
	0xb8, 0x80, 0xff, 0xaa, 0xa8, 0xad, 0xb5, 0xb5, //0x00004128 .quad -5353181642124984136
	0x69, 0x6c, 0xed, 0x97, 0xf6, 0xff, 0xe1, 0x10, //0x00004130 .quad 1216534808942701673
	0xe6, 0x60, 0xbf, 0xd5, 0x12, 0x19, 0x23, 0xe3, //0x00004138 .quad -2079791034228842266
	0xc1, 0x63, 0xf4, 0x1e, 0xfa, 0x3f, 0x8d, 0xca, //0x00004140 .quad -3851351762838199359
	0x8f, 0x9c, 0x97, 0xc5, 0xab, 0xef, 0xf5, 0x8d, //0x00004148 .quad -8217398424034108273
	0xb2, 0x7c, 0xb1, 0xa6, 0xf8, 0x8f, 0x30, 0xbd, //0x00004150 .quad -4814189703547749198
	0xb3, 0x83, 0xfd, 0xb6, 0x96, 0x6b, 0x73, 0xb1, //0x00004158 .quad -5660062011615247437
	0xde, 0xdb, 0x5d, 0xd0, 0xf6, 0xb3, 0x7c, 0xac, //0x00004160 .quad -6017737129434686498
	0xa0, 0xe4, 0xbc, 0x64, 0x7c, 0x46, 0xd0, 0xdd, //0x00004168 .quad -2463391496091671392
	0x6b, 0xa9, 0x3a, 0x42, 0x7a, 0xf0, 0xcd, 0x6b, //0x00004170 .quad 7768129340171790699
	0xe4, 0x0e, 0xf6, 0xbe, 0x0d, 0x2c, 0xa2, 0x8a, //0x00004178 .quad -8457148712698376476
	0xc6, 0x53, 0xc9, 0xd2, 0x98, 0x6c, 0xc1, 0x86, //0x00004180 .quad -8736582398494813242
	0x9d, 0x92, 0xb3, 0x2e, 0x11, 0xb7, 0x4a, 0xad, //0x00004188 .quad -5959749872445582691
	0xb7, 0xa8, 0x7b, 0x07, 0xbf, 0xc7, 0x71, 0xe8, //0x00004190 .quad -1697355961263740745
	0x44, 0x77, 0x60, 0x7a, 0xd5, 0x64, 0x9d, 0xd8, //0x00004198 .quad -2838001322129590460
	0x72, 0x49, 0xad, 0x64, 0xd7, 0x1c, 0x47, 0x11, //0x000041a0 .quad 1244995533423855986
	0x8b, 0x4a, 0x7c, 0x6c, 0x05, 0x5f, 0x62, 0x87, //0x000041a8 .quad -8691279853972075893
	0xcf, 0x9b, 0xd8, 0x3d, 0x0d, 0xe4, 0x98, 0xd5, //0x000041b0 .quad -3055441601647567921
	0x2d, 0x5d, 0x9b, 0xc7, 0xc6, 0xf6, 0x3a, 0xa9, //0x000041b8 .quad -6252413799037706963
	0xc3, 0xc2, 0x4e, 0x8d, 0x10, 0x1d, 0xff, 0x4a, //0x000041c0 .quad 5404070034795315907
	0x79, 0x34, 0x82, 0x79, 0x78, 0xb4, 0x89, 0xd3, //0x000041c8 .quad -3203831230369745799
	0xba, 0x39, 0x51, 0x58, 0x2a, 0x72, 0xdf, 0xce, //0x000041d0 .quad -3539985255894009414
	0xcb, 0x60, 0xf1, 0x4b, 0xcb, 0x10, 0x36, 0x84, //0x000041d8 .quad -8919923546622172981
	0x28, 0x88, 0x65, 0xee, 0xb4, 0x4e, 0x97, 0xc2, //0x000041e0 .quad -4424981569867511768
	0xfe, 0xb8, 0xed, 0x1e, 0xfe, 0x94, 0x43, 0xa5, //0x000041e8 .quad -6538218414850328322
	0x32, 0xea, 0xfe, 0x29, 0x62, 0x22, 0x3d, 0x73, //0x000041f0 .quad 8303831092947774002
	0x3e, 0x27, 0xa9, 0xa6, 0x3d, 0x7a, 0x94, 0xce, //0x000041f8 .quad -3561087000135522498
	0x5f, 0x52, 0x3f, 0x5a, 0x7d, 0x35, 0x06, 0x08, //0x00004200 .quad 578208414664970847
	0x87, 0xb8, 0x29, 0x88, 0x66, 0xcc, 0x1c, 0x81, //0x00004208 .quad -9143208402725783417
	0xf7, 0x26, 0xcf, 0xb0, 0xdc, 0xc2, 0x07, 0xca, //0x00004210 .quad -3888925500096174345
	0xa8, 0x26, 0x34, 0x2a, 0x80, 0xff, 0x63, 0xa1, //0x00004218 .quad -6817324484979841368
	0xb5, 0xf0, 0x02, 0xdd, 0x93, 0xb3, 0x89, 0xfc, //0x00004220 .quad -249470856692830027
	0x52, 0x30, 0xc1, 0x34, 0x60, 0xff, 0xbc, 0xc9, //0x00004228 .quad -3909969587797413806
	0xe2, 0xac, 0x43, 0xd4, 0x78, 0x20, 0xac, 0xbb, //0x00004230 .quad -4923524589293425438
	0x67, 0x7c, 0xf1, 0x41, 0x38, 0x3f, 0x2c, 0xfc, //0x00004238 .quad -275775966319379353
	0x0d, 0x4c, 0xaa, 0x84, 0x4b, 0x94, 0x4b, 0xd5, //0x00004240 .quad -3077202868308390899
	0xc0, 0xed, 0x36, 0x29, 0x83, 0xa7, 0x9b, 0x9d, //0x00004248 .quad -7089889006590693952
	0x11, 0xdf, 0xd4, 0x65, 0x5e, 0x79, 0x9e, 0x0a, //0x00004250 .quad 765182433041899281
	0x31, 0xa9, 0x84, 0xf3, 0x63, 0x91, 0x02, 0xc5, //0x00004258 .quad -4250675239810979535
	0xd5, 0x16, 0x4a, 0xff, 0xb5, 0x17, 0x46, 0x4d, //0x00004260 .quad 5568164059729762005
	0x7d, 0xd3, 0x65, 0xf0, 0xbc, 0x35, 0x43, 0xf6, //0x00004268 .quad -701658031336336515
	0x45, 0x4e, 0x8e, 0xbf, 0xd1, 0xce, 0x4b, 0x50, //0x00004270 .quad 5785945546544795205
	0x2e, 0xa4, 0x3f, 0x16, 0x96, 0x01, 0xea, 0x99, //0x00004278 .quad -7356065297226292178
	0xd6, 0xe1, 0x71, 0x2f, 0x86, 0xc2, 0x5e, 0xe4, //0x00004280 .quad -1990940103673781802
	0x39, 0x8d, 0xcf, 0x9b, 0xfb, 0x81, 0x64, 0xc0, //0x00004288 .quad -4583395603105477319
	0x4c, 0x5a, 0x4e, 0xbb, 0x27, 0x73, 0x76, 0x5d, //0x00004290 .quad 6734696907262548556
	0x88, 0x70, 0xc3, 0x82, 0x7a, 0xa2, 0x7d, 0xf0, //0x00004298 .quad -1117558485454458744
	0x6f, 0xf8, 0x10, 0xd5, 0xf8, 0x07, 0x6a, 0x3a, //0x000042a0 .quad 4209185567039092847
	0x55, 0x26, 0xba, 0x91, 0x8c, 0x85, 0x4e, 0x96, //0x000042a8 .quad -7616003081050118571
	0x8b, 0x36, 0x55, 0x0a, 0xf7, 0x89, 0x04, 0x89, //0x000042b0 .quad -8573576096483297653
	0xea, 0xaf, 0x28, 0xb6, 0xef, 0x26, 0xe2, 0xbb, //0x000042b8 .quad -4908317832885260310
	0x2e, 0x84, 0xea, 0xcc, 0x74, 0xac, 0x45, 0x2b, //0x000042c0 .quad 3118087934678041646
	0xe5, 0xdb, 0xb2, 0xa3, 0xab, 0xb0, 0xda, 0xea, //0x000042c8 .quad -1523711272679187483
	0x9d, 0x92, 0x12, 0x00, 0xc9, 0x8b, 0x0b, 0x3b, //0x000042d0 .quad 4254647968387469981
	0x6f, 0xc9, 0x4f, 0x46, 0x6b, 0xae, 0xc8, 0x92, //0x000042d8 .quad -7869848573065574033
	0x44, 0x37, 0x17, 0x40, 0xbb, 0x6e, 0xce, 0x09, //0x000042e0 .quad 706623942056949572
	0xcb, 0xbb, 0xe3, 0x17, 0x06, 0xda, 0x7a, 0xb7, //0x000042e8 .quad -5225624697904579637
	0x15, 0x05, 0x1d, 0x10, 0x6a, 0x0a, 0x42, 0xcc, //0x000042f0 .quad -3728406090856200939
	0xbd, 0xaa, 0xdc, 0x9d, 0x87, 0x90, 0x59, 0xe5, //0x000042f8 .quad -1920344853953336643
	0x2d, 0x23, 0x12, 0x4a, 0x82, 0x46, 0xa9, 0x9f, //0x00004300 .quad -6941939825212513491
	0xb6, 0xea, 0xa9, 0xc2, 0x54, 0xfa, 0x57, 0x8f, //0x00004308 .quad -8117744561361917258
	0xf9, 0xab, 0x96, 0xdc, 0x22, 0x98, 0x93, 0x47, //0x00004310 .quad 5157633273766521849
	0x64, 0x65, 0x54, 0xf3, 0xe9, 0xf8, 0x2d, 0xb3, //0x00004318 .quad -5535494683275008668
	0xf7, 0x56, 0xbc, 0x93, 0x2b, 0x7e, 0x78, 0x59, //0x00004320 .quad 6447041592208152311
	0xbd, 0x7e, 0x29, 0x70, 0x24, 0x77, 0xf9, 0xdf, //0x00004328 .quad -2307682335666372931
	0x5a, 0xb6, 0x55, 0x3c, 0xdb, 0x4e, 0xeb, 0x57, //0x00004330 .quad 6335244004343789146
	0x36, 0xef, 0x19, 0xc6, 0x76, 0xea, 0xfb, 0x8b, //0x00004338 .quad -8359830487432564938
	0xf1, 0x23, 0x6b, 0x0b, 0x92, 0x22, 0xe6, 0xed, //0x00004340 .quad -1304317031425039375
	0x03, 0x6b, 0xa0, 0x77, 0x14, 0xe5, 0xfa, 0xae, //0x00004348 .quad -5838102090863318269
	0xed, 0xec, 0x45, 0x8e, 0x36, 0xab, 0x5f, 0xe9, //0x00004350 .quad -1630396289281299219
	0xc4, 0x85, 0x88, 0x95, 0x59, 0x9e, 0xb9, 0xda, //0x00004358 .quad -2685941595151759932
	0x14, 0xb4, 0xeb, 0x18, 0x02, 0xcb, 0xdb, 0x11, //0x00004360 .quad 1286845328412881940
	0x9b, 0x53, 0x75, 0xfd, 0xf7, 0x02, 0xb4, 0x88, //0x00004368 .quad -8596242524610931813
	0x19, 0xa1, 0x26, 0x9f, 0xc2, 0xbd, 0x52, 0xd6, //0x00004370 .quad -3003129357911285479
	0x81, 0xa8, 0xd2, 0xfc, 0xb5, 0x03, 0xe1, 0xaa, //0x00004378 .quad -6133617137336276863
	0x5f, 0x49, 0xf0, 0x46, 0x33, 0x6d, 0xe7, 0x4b, //0x00004380 .quad 5469460339465668959
	0xa2, 0x52, 0x07, 0x7c, 0xa3, 0x44, 0x99, 0xd5, //0x00004388 .quad -3055335403242958174
	0xdb, 0x2d, 0x56, 0x0c, 0x40, 0xa4, 0x70, 0x6f, //0x00004390 .quad 8030098730593431003
	0xa5, 0x93, 0x84, 0x2d, 0xe6, 0xca, 0x7f, 0x85, //0x00004398 .quad -8827113654667930715
	0x52, 0xb9, 0x6b, 0x0f, 0x50, 0xcd, 0x4c, 0xcb, //0x000043a0 .quad -3797434642040374958
	0x8e, 0xb8, 0xe5, 0xb8, 0x9f, 0xbd, 0xdf, 0xa6, //0x000043a8 .quad -6422206049907525490
	0xa7, 0xa7, 0x46, 0x13, 0xa4, 0x00, 0x20, 0x7e, //0x000043b0 .quad 9088264752731695015
	0xb2, 0x26, 0x1f, 0xa7, 0x07, 0xad, 0x97, 0xd0, //0x000043b8 .quad -3416071543957018958
	0xc8, 0x28, 0x0c, 0x8c, 0x66, 0x00, 0xd4, 0x8e, //0x000043c0 .quad -8154892584824854328
	0x2f, 0x78, 0x73, 0xc8, 0x24, 0xcc, 0x5e, 0x82, //0x000043c8 .quad -9052573742614218705
	0xfa, 0x32, 0x0f, 0x2f, 0x80, 0x00, 0x89, 0x72, //0x000043d0 .quad 8253128342678483706
	0x3b, 0x56, 0x90, 0xfa, 0x2d, 0x7f, 0xf6, 0xa2, //0x000043d8 .quad -6704031159840385477
	0xb9, 0xff, 0xd2, 0x3a, 0xa0, 0x40, 0x2b, 0x4f, //0x000043e0 .quad 5704724409920716729
	0xca, 0x6b, 0x34, 0x79, 0xf9, 0x1e, 0xb4, 0xcb, //0x000043e8 .quad -3768352931373093942
	0xa8, 0xbf, 0x87, 0x49, 0xc8, 0x10, 0xf6, 0xe2, //0x000043f0 .quad -2092466524453879896
	0xbc, 0x86, 0x81, 0xd7, 0xb7, 0x26, 0xa1, 0xfe, //0x000043f8 .quad -98755145788979524
	0xc9, 0xd7, 0xf4, 0x2d, 0x7d, 0xca, 0xd9, 0x0d, //0x00004400 .quad 998051431430019017
	0x36, 0xf4, 0xb0, 0xe6, 0x32, 0xb8, 0x24, 0x9f, //0x00004408 .quad -6979250993759194058
	0xbb, 0x0d, 0x72, 0x79, 0x1c, 0x3d, 0x50, 0x91, //0x00004410 .quad -7975807747567252037
	0x43, 0x31, 0x5d, 0xa0, 0x3f, 0xe6, 0xed, 0xc6, //0x00004418 .quad -4112377723771604669
	0x2a, 0x91, 0xce, 0x97, 0x63, 0x4c, 0xa4, 0x75, //0x00004420 .quad 8476984389250486570
	0x94, 0x7d, 0x74, 0x88, 0xcf, 0x5f, 0xa9, 0xf8, //0x00004428 .quad -528786136287117932
	0xba, 0x1a, 0xe1, 0x3e, 0xbe, 0xaf, 0x86, 0xc9, //0x00004430 .quad -3925256793573221702
	0x7c, 0xce, 0x48, 0xb5, 0xe1, 0xdb, 0x69, 0x9b, //0x00004438 .quad -7248020362820530564
	0x68, 0x61, 0x99, 0xce, 0xad, 0x5b, 0xe8, 0xfb, //0x00004440 .quad -294884973539139224
	0x1b, 0x02, 0x9b, 0x22, 0xda, 0x52, 0x44, 0xc2, //0x00004448 .quad -4448339435098275301
	0xc3, 0xb9, 0x3f, 0x42, 0x99, 0x72, 0xe2, 0xfa, //0x00004450 .quad -368606216923924029
	0xa2, 0xc2, 0x41, 0xab, 0x90, 0x67, 0xd5, 0xf2, //0x00004458 .quad -948738275445456222
	0x1a, 0xd4, 0x67, 0xc9, 0x9f, 0x87, 0xcd, 0xdc, //0x00004460 .quad -2536221894791146470
	0xa5, 0x19, 0x09, 0x6b, 0xba, 0x60, 0xc5, 0x97, //0x00004468 .quad -7510490449794491995
	0x20, 0xc9, 0xc1, 0xbb, 0x87, 0xe9, 0x00, 0x54, //0x00004470 .quad 6053094668365842720
	0x0f, 0x60, 0xcb, 0x05, 0xe9, 0xb8, 0xb6, 0xbd, //0x00004478 .quad -4776427043815727089
	0x68, 0x3b, 0xb2, 0xaa, 0xe9, 0x23, 0x01, 0x29, //0x00004480 .quad 2954682317029915496
	0x13, 0x38, 0x3e, 0x47, 0x23, 0x67, 0x24, 0xed, //0x00004488 .quad -1358847786342270957
	0x21, 0x65, 0xaf, 0x0a, 0x72, 0xb6, 0xa0, 0xf9, //0x00004490 .quad -459166561069996767
	0x0b, 0xe3, 0x86, 0x0c, 0x76, 0xc0, 0x36, 0x94, //0x00004498 .quad -7766808894105001205
	0x69, 0x3e, 0x5b, 0x8d, 0x0e, 0xe4, 0x08, 0xf8, //0x000044a0 .quad -573958201337495959
	0xce, 0x9b, 0xa8, 0x8f, 0x93, 0x70, 0x44, 0xb9, //0x000044a8 .quad -5096825099203863602
	0x04, 0x0e, 0xb2, 0x30, 0x12, 0x1d, 0x0b, 0xb6, //0x000044b0 .quad -5329133770099257852
	0xc2, 0xc2, 0x92, 0x73, 0xb8, 0x8c, 0x95, 0xe7, //0x000044b8 .quad -1759345355577441598
	0xc2, 0x48, 0x6f, 0x5e, 0x2b, 0xf2, 0xc6, 0xb1, //0x000044c0 .quad -5636551615525730110
	0xb9, 0xb9, 0x3b, 0x48, 0xf3, 0x77, 0xbd, 0x90, //0x000044c8 .quad -8017119874876982855
	0xf3, 0x1a, 0x0b, 0x36, 0xb6, 0xae, 0x38, 0x1e, //0x000044d0 .quad 2177682517447613171
	0x28, 0xa8, 0x4a, 0x1a, 0xf0, 0xd5, 0xec, 0xb4, //0x000044d8 .quad -5409713825168840664
	0xb0, 0xe1, 0x8d, 0xc3, 0x63, 0xda, 0xc6, 0x25, //0x000044e0 .quad 2722103146809516464
	0x32, 0x52, 0xdd, 0x20, 0x6c, 0x0b, 0x28, 0xe2, //0x000044e8 .quad -2150456263033662926
	0x0e, 0xad, 0x38, 0x5a, 0x7e, 0x48, 0x9c, 0x57, //0x000044f0 .quad 6313000485183335694
	0x5f, 0x53, 0x8a, 0x94, 0x23, 0x07, 0x59, 0x8d, //0x000044f8 .quad -8261564192037121185
	0x51, 0xd8, 0xc6, 0xf0, 0x9d, 0x5a, 0x83, 0x2d, //0x00004500 .quad 3279564588051781713
	0x37, 0xe8, 0xac, 0x79, 0xec, 0x48, 0xaf, 0xb0, //0x00004508 .quad -5715269221619013577
	0x65, 0x8e, 0xf8, 0x6c, 0x45, 0x31, 0xe4, 0xf8, //0x00004510 .quad -512230283362660763
	0x44, 0x22, 0x18, 0x98, 0x27, 0x1b, 0xdb, 0xdc, //0x00004518 .quad -2532400508596379068
	0xff, 0x58, 0x1b, 0x64, 0xcb, 0x9e, 0x8e, 0x1b, //0x00004520 .quad 1985699082112030975
	0x6b, 0x15, 0x0f, 0xbf, 0xf8, 0xf0, 0x08, 0x8a, //0x00004528 .quad -8500279345513818773
	0x3f, 0x2f, 0x22, 0x3d, 0x7e, 0x46, 0x72, 0xe2, //0x00004530 .quad -2129562165787349185
	0xc5, 0xda, 0xd2, 0xee, 0x36, 0x2d, 0x8b, 0xac, //0x00004538 .quad -6013663163464885563
	0x0f, 0xbb, 0x6a, 0xcc, 0x1d, 0xd8, 0x0e, 0x5b, //0x00004540 .quad 6561419329620589327
	0x77, 0x91, 0x87, 0xaa, 0x84, 0xf8, 0xad, 0xd7, //0x00004548 .quad -2905392935903719049
	0xe9, 0xb4, 0xc2, 0x9f, 0x12, 0x47, 0xe9, 0x98, //0x00004550 .quad -7428327965055601431
	0xea, 0xba, 0x94, 0xea, 0x52, 0xbb, 0xcc, 0x86, //0x00004558 .quad -8733399612580906262
	0x24, 0x62, 0xb3, 0x47, 0xd7, 0x98, 0x23, 0x3f, //0x00004560 .quad 4549648098962661924
	0xa5, 0xe9, 0x39, 0xa5, 0x27, 0xea, 0x7f, 0xa8, //0x00004568 .quad -6305063497298744923
	0xad, 0x3a, 0xa0, 0x19, 0x0d, 0x7f, 0xec, 0x8e, //0x00004570 .quad -8147997931578836307
	0x0e, 0x64, 0x88, 0x8e, 0xb1, 0xe4, 0x9f, 0xd2, //0x00004578 .quad -3269643353196043250
	0xac, 0x24, 0x04, 0x30, 0x68, 0xcf, 0x53, 0x19, //0x00004580 .quad 1825030320404309164
	0x89, 0x3e, 0x15, 0xf9, 0xee, 0xee, 0xa3, 0x83, //0x00004588 .quad -8961056123388608887
	0xd7, 0x2d, 0x05, 0x3c, 0x42, 0xc3, 0xa8, 0x5f, //0x00004590 .quad 6892973918932774359
	0x2b, 0x8e, 0x5a, 0xb7, 0xaa, 0xea, 0x8c, 0xa4, //0x00004598 .quad -6589634135808373205
	0x4d, 0x79, 0x06, 0xcb, 0x12, 0xf4, 0x92, 0x37, //0x000045a0 .quad 4004531380238580045
	0xb6, 0x31, 0x31, 0x65, 0x55, 0x25, 0xb0, 0xcd, //0x000045a8 .quad -3625356651333078602
	0xd0, 0x0b, 0xe4, 0xbe, 0x8b, 0xd8, 0xbb, 0xe2, //0x000045b0 .quad -2108853905778275376
	0x11, 0xbf, 0x3e, 0x5f, 0x55, 0x17, 0x8e, 0x80, //0x000045b8 .quad -9183376934724255983
	0xc4, 0x0e, 0x9d, 0xae, 0xae, 0xce, 0x6a, 0x5b, //0x000045c0 .quad 6587304654631931588
	0xd6, 0x6e, 0x0e, 0xb7, 0x2a, 0x9d, 0xb1, 0xa0, //0x000045c8 .quad -6867535149977932074
	0x75, 0x52, 0x44, 0x5a, 0x5a, 0x82, 0x45, 0xf2, //0x000045d0 .quad -989241218564861323
	0x8b, 0x0a, 0xd2, 0x64, 0x75, 0x04, 0xde, 0xc8, //0x000045d8 .quad -3972732919045027189
	0x12, 0x67, 0xd5, 0xf0, 0xf0, 0xe2, 0xd6, 0xee, //0x000045e0 .quad -1236551523206076654
	0x2e, 0x8d, 0x06, 0xbe, 0x92, 0x85, 0x15, 0xfb, //0x000045e8 .quad -354230130378896082
	0x6b, 0x60, 0x85, 0x96, 0xd6, 0x4d, 0x46, 0x55, //0x000045f0 .quad 6144684325637283947
	0x3d, 0x18, 0xc4, 0xb6, 0x7b, 0x73, 0xed, 0x9c, //0x000045f8 .quad -7138922859127891907
	0x86, 0xb8, 0x26, 0x3c, 0x4c, 0xe1, 0x97, 0xaa, //0x00004600 .quad -6154202648235558778
	0x4c, 0x1e, 0x75, 0xa4, 0x5a, 0xd0, 0x28, 0xc4, //0x00004608 .quad -4311967555482476980
	0xa8, 0x66, 0x30, 0x4b, 0x9f, 0xd9, 0x3d, 0xd5, //0x00004610 .quad -3081067291867060568
	0xdf, 0x65, 0x92, 0x4d, 0x71, 0x04, 0x33, 0xf5, //0x00004618 .quad -778273425925708321
	0x29, 0x40, 0xfe, 0x8e, 0x03, 0xa8, 0x46, 0xe5, //0x00004620 .quad -1925667057416912855
	0xab, 0x7f, 0x7b, 0xd0, 0xc6, 0xe2, 0x3f, 0x99, //0x00004628 .quad -7403949918844649557
	0x33, 0xd0, 0xbd, 0x72, 0x04, 0x52, 0x98, 0xde, //0x00004630 .quad -2407083821771141069
	0x96, 0x5f, 0x9a, 0x84, 0x78, 0xdb, 0x8f, 0xbf, //0x00004638 .quad -4643251380128424042
	0x40, 0x44, 0x6d, 0x8f, 0x85, 0x66, 0x3e, 0x96, //0x00004640 .quad -7620540795641314240
	0x7c, 0xf7, 0xc0, 0xa5, 0x56, 0xd2, 0x73, 0xef, //0x00004648 .quad -1192378206733142148
	0xa8, 0x4a, 0xa4, 0x79, 0x13, 0x00, 0xe7, 0xdd, //0x00004650 .quad -2456994988062127448
	0xad, 0x9a, 0x98, 0x27, 0x76, 0x63, 0xa8, 0x95, //0x00004658 .quad -7662765406849295699
	0x52, 0x5d, 0x0d, 0x58, 0x18, 0xc0, 0x60, 0x55, //0x00004660 .quad 6152128301777116498
	0x59, 0xc1, 0x7e, 0xb1, 0x53, 0x7c, 0x12, 0xbb, //0x00004668 .quad -4966770740134231719
	0xa6, 0xb4, 0x10, 0x6e, 0x1e, 0xf0, 0xb8, 0xaa, //0x00004670 .quad -6144897678060768090
	0xaf, 0x71, 0xde, 0x9d, 0x68, 0x1b, 0xd7, 0xe9, //0x00004678 .quad -1596777406740401745
	0xe8, 0x70, 0xca, 0x04, 0x13, 0x96, 0xb3, 0xca, //0x00004680 .quad -3840561048787980056
	0x0d, 0x07, 0xab, 0x62, 0x21, 0x71, 0x26, 0x92, //0x00004688 .quad -7915514906853832947
	0x22, 0x0d, 0xfd, 0xc5, 0x97, 0x7b, 0x60, 0x3d, //0x00004690 .quad 4422670725869800738
	0xd1, 0xc8, 0x55, 0xbb, 0x69, 0x0d, 0xb0, 0xb6, //0x00004698 .quad -5282707615139903279
	0x6a, 0x50, 0x7c, 0xb7, 0x7d, 0x9a, 0xb8, 0x8c, //0x000046a0 .quad -8306719647944912790
	0x05, 0x3b, 0x2b, 0x2a, 0xc4, 0x10, 0x5c, 0xe4, //0x000046a8 .quad -1991698500497491195
	0x42, 0xb2, 0xad, 0x92, 0x8e, 0x60, 0xf3, 0x77, //0x000046b0 .quad 8643358275316593218
	0xe3, 0x04, 0x5b, 0x9a, 0x7a, 0x8a, 0xb9, 0x8e, //0x000046b8 .quad -8162340590452013853
	0xd3, 0x1e, 0x59, 0x37, 0xb2, 0x38, 0xf0, 0x55, //0x000046c0 .quad 6192511825718353619
	0x1c, 0xc6, 0xf1, 0x40, 0x19, 0xed, 0x67, 0xb2, //0x000046c8 .quad -5591239719637629412
	0x88, 0x66, 0x2f, 0xc5, 0xde, 0x46, 0x6c, 0x6b, //0x000046d0 .quad 7740639782147942024
	0xa3, 0x37, 0x2e, 0x91, 0x5f, 0xe8, 0x01, 0xdf, //0x000046d8 .quad -2377363631119648861
	0x15, 0xa0, 0x3d, 0x3b, 0x4b, 0xac, 0x23, 0x23, //0x000046e0 .quad 2532056854628769813
	0xc6, 0xe2, 0xbc, 0xba, 0x3b, 0x31, 0x61, 0x8b, //0x000046e8 .quad -8403381297090862394
	0x1a, 0x08, 0x0d, 0x0a, 0x5e, 0x97, 0xec, 0xab, //0x000046f0 .quad -6058300968568813542
	0x77, 0x1b, 0x6c, 0xa9, 0x8a, 0x7d, 0x39, 0xae, //0x000046f8 .quad -5892540602936190089
	0x21, 0x4a, 0x90, 0x8c, 0x35, 0xbd, 0xe7, 0x96, //0x00004700 .quad -7572876210711016927
	0x55, 0x22, 0xc7, 0x53, 0xed, 0xdc, 0xc7, 0xd9, //0x00004708 .quad -2753989735242849707
	0x54, 0x2e, 0xda, 0x77, 0x41, 0xd6, 0x50, 0x7e, //0x00004710 .quad 9102010423587778132
	0x75, 0x75, 0x5c, 0x54, 0x14, 0xea, 0x1c, 0x88, //0x00004718 .quad -8638772612167862923
	0xe9, 0xb9, 0xd0, 0xd5, 0xd1, 0x0b, 0xe5, 0xdd, //0x00004720 .quad -2457545025797441047
	0xd2, 0x92, 0x73, 0x69, 0x99, 0x24, 0x24, 0xaa, //0x00004728 .quad -6186779746782440750
	0x64, 0xe8, 0x44, 0x4b, 0xc6, 0x4e, 0x5e, 0x95, //0x00004730 .quad -7683617300674189212
	0x87, 0x77, 0xd0, 0xc3, 0xbf, 0x2d, 0xad, 0xd4, //0x00004738 .quad -3121788665050663033
	0x3e, 0x11, 0x0b, 0xef, 0x3b, 0xf1, 0x5a, 0xbd, //0x00004740 .quad -4802260812921368258
	0xb4, 0x4a, 0x62, 0xda, 0x97, 0x3c, 0xec, 0x84, //0x00004748 .quad -8868646943297746252
	0x8e, 0xd5, 0xcd, 0xea, 0x8a, 0xad, 0xb1, 0xec, //0x00004750 .quad -1391139997724322418
	0x61, 0xdd, 0xfa, 0xd0, 0xbd, 0x4b, 0x27, 0xa6, //0x00004758 .quad -6474122660694794911
	0xf2, 0x4a, 0x81, 0xa5, 0xed, 0x18, 0xde, 0x67, //0x00004760 .quad 7484447039699372786
	0xba, 0x94, 0x39, 0x45, 0xad, 0x1e, 0xb1, 0xcf, //0x00004768 .quad -3480967307441105734
	0xd7, 0xce, 0x70, 0x87, 0x94, 0xcf, 0xea, 0x80, //0x00004770 .quad -9157278655470055721
	0xf4, 0xfc, 0x43, 0x4b, 0x2c, 0xb3, 0xce, 0x81, //0x00004778 .quad -9093133594791772940
	0x8d, 0x02, 0x4d, 0xa9, 0x79, 0x83, 0x25, 0xa1, //0x00004780 .quad -6834912300910181747
	0x31, 0xfc, 0x14, 0x5e, 0xf7, 0x5f, 0x42, 0xa2, //0x00004788 .quad -6754730975062328271
	0x30, 0x43, 0xa0, 0x13, 0x58, 0xe4, 0x6e, 0x09, //0x00004790 .quad 679731660717048624
	0x3e, 0x3b, 0x9a, 0x35, 0xf5, 0xf7, 0xd2, 0xca, //0x00004798 .quad -3831727700400522434
	0xfc, 0x53, 0x88, 0x18, 0x6e, 0x9d, 0xca, 0x8b, //0x000047a0 .quad -8373707460958465028
	0x0d, 0xca, 0x00, 0x83, 0xf2, 0xb5, 0x87, 0xfd, //0x000047a8 .quad -177973607073265139
	0x7d, 0x34, 0x55, 0xcf, 0x64, 0xa2, 0x5e, 0x77, //0x000047b0 .quad 8601490892183123069
	0x48, 0x7e, 0xe0, 0x91, 0xb7, 0xd1, 0x74, 0x9e, //0x000047b8 .quad -7028762532061872568
	0x9d, 0x81, 0x2a, 0x03, 0xfe, 0x4a, 0x36, 0x95, //0x000047c0 .quad -7694880458480647779
	0xda, 0x9d, 0x58, 0x76, 0x25, 0x06, 0x12, 0xc6, //0x000047c8 .quad -4174267146649952806
	0x04, 0x22, 0xf5, 0x83, 0xbd, 0xdd, 0x83, 0x3a, //0x000047d0 .quad 4216457482181353988
	0x51, 0xc5, 0xee, 0xd3, 0xae, 0x87, 0x96, 0xf7, //0x000047d8 .quad -606147914885053103
	0x42, 0x35, 0x79, 0x72, 0x96, 0x6a, 0x92, 0xc4, //0x000047e0 .quad -4282243101277735614
	0x52, 0x3b, 0x75, 0x44, 0xcd, 0x14, 0xbe, 0x9a, //0x000047e8 .quad -7296371474444240046
	0x93, 0x82, 0x17, 0x0f, 0x3c, 0x05, 0xb7, 0x75, //0x000047f0 .quad 8482254178684994195
	0x27, 0x8a, 0x92, 0x95, 0x00, 0x9a, 0x6d, 0xc1, //0x000047f8 .quad -4508778324627912153
	0x38, 0x63, 0xdd, 0x12, 0x8b, 0xc6, 0x24, 0x53, //0x00004800 .quad 5991131704928854840
	0xb1, 0x2c, 0xf7, 0xba, 0x80, 0x00, 0xc9, 0xf1, //0x00004808 .quad -1024286887357502287
	0x03, 0x5e, 0xca, 0xeb, 0x16, 0xfc, 0xf6, 0xd3, //0x00004810 .quad -3173071712060547581
	0xee, 0x7b, 0xda, 0x74, 0x50, 0xa0, 0x1d, 0x97, //0x00004818 .quad -7557708332239520786
	0x84, 0xf5, 0xbc, 0xa6, 0x1c, 0xbb, 0xf4, 0x88, //0x00004820 .quad -8578025658503072380
	0xea, 0x1a, 0x11, 0x92, 0x64, 0x08, 0xe5, 0xbc, //0x00004828 .quad -4835449396872013078
	0xe5, 0x32, 0x6c, 0xd0, 0xe3, 0xe9, 0x31, 0x2b, //0x00004830 .quad 3112525982153323237
	0xa5, 0x61, 0x95, 0xb6, 0x7d, 0x4a, 0x1e, 0xec, //0x00004838 .quad -1432625727662628443
	0xcf, 0x9f, 0x43, 0x62, 0x2e, 0x32, 0xff, 0x3a, //0x00004840 .quad 4251171748059520975
	0x07, 0x5d, 0x1d, 0x92, 0x8e, 0xee, 0x92, 0x93, //0x00004848 .quad -7812920107430224633
	0xc2, 0x87, 0xd4, 0xfa, 0xb9, 0xfe, 0xbe, 0x09, //0x00004850 .quad 702278666647013314
	0x49, 0xb4, 0xa4, 0x36, 0x32, 0xaa, 0x77, 0xb8, //0x00004858 .quad -5154464115860392887
	0xb3, 0xa9, 0x89, 0x79, 0x68, 0xbe, 0x2e, 0x4c, //0x00004860 .quad 5489534351736154547
	0x5b, 0xe1, 0x4d, 0xc4, 0xbe, 0x94, 0x95, 0xe6, //0x00004868 .quad -1831394126398103205
	0x10, 0x0a, 0xf6, 0x4b, 0x01, 0x37, 0x9d, 0x0f, //0x00004870 .quad 1125115960621402640
	0xd9, 0xac, 0xb0, 0x3a, 0xf7, 0x7c, 0x1d, 0x90, //0x00004878 .quad -8062150356639896359
	0x94, 0x8c, 0xf3, 0x9e, 0xc1, 0x84, 0x84, 0x53, //0x00004880 .quad 6018080969204141204
	0x0f, 0xd8, 0x5c, 0x09, 0x35, 0xdc, 0x24, 0xb4, //0x00004888 .quad -5466001927372482545
	0xb9, 0x6f, 0xb0, 0x06, 0xf2, 0xa5, 0x65, 0x28, //0x00004890 .quad 2910915193077788601
	0x13, 0x0e, 0xb4, 0x4b, 0x42, 0x13, 0x2e, 0xe1, //0x00004898 .quad -2220816390788215277
	0xd3, 0x45, 0x2e, 0x44, 0xb7, 0x87, 0x3f, 0xf9, //0x000048a0 .quad -486521013540076077
	0xcb, 0x88, 0x50, 0x6f, 0x09, 0xcc, 0xbc, 0x8c, //0x000048a8 .quad -8305539271883716405
	0x48, 0xd7, 0x39, 0x15, 0xa5, 0x69, 0x8f, 0xf7, //0x000048b0 .quad -608151266925095096
	0xfe, 0xaa, 0x24, 0xcb, 0x0b, 0xff, 0xeb, 0xaf, //0x000048b8 .quad -5770238071427257602
	0x1b, 0x4d, 0x88, 0x5a, 0x0e, 0x44, 0x73, 0xb5, //0x000048c0 .quad -5371875102083756773
	0xbe, 0xd5, 0xed, 0xbd, 0xce, 0xfe, 0xe6, 0xdb, //0x000048c8 .quad -2601111570856684098
	0x30, 0x30, 0x95, 0xf8, 0x88, 0x0a, 0x68, 0x31, //0x000048d0 .quad 3560107088838733872
	0x97, 0xa5, 0xb4, 0x36, 0x41, 0x5f, 0x70, 0x89, //0x000048d8 .quad -8543223759426509417
	0x3d, 0x7c, 0xba, 0x36, 0x2b, 0x0d, 0xc2, 0xfd, //0x000048e0 .quad -161552157378970563
	0xfc, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x000048e8 .quad -6067343680855748868
	0x4c, 0x1b, 0x69, 0x04, 0x76, 0x90, 0x32, 0x3d, //0x000048f0 .quad 4409745821703674700
	0xbc, 0x42, 0x7a, 0xe5, 0xd5, 0x94, 0xbf, 0xd6, //0x000048f8 .quad -2972493582642298180
	0x0f, 0xb1, 0xc1, 0xc2, 0x49, 0x9a, 0x3f, 0xa6, //0x00004900 .quad -6467280898289979121
	0xb5, 0x69, 0x6c, 0xaf, 0x05, 0xbd, 0x37, 0x86, //0x00004908 .quad -8775337516792518219
	0x53, 0x1d, 0x72, 0x33, 0xdc, 0x80, 0xcf, 0x0f, //0x00004910 .quad 1139270913992301907
	0x23, 0x84, 0x47, 0x1b, 0x47, 0xac, 0xc5, 0xa7, //0x00004918 .quad -6357485877563259869
	0xa8, 0xa4, 0x4e, 0x40, 0x13, 0x61, 0xc3, 0xd3, //0x00004920 .quad -3187597375937010520
	0x2b, 0x65, 0x19, 0xe2, 0x58, 0x17, 0xb7, 0xd1, //0x00004928 .quad -3335171328526686933
	0xe9, 0x26, 0x31, 0x08, 0xac, 0x1c, 0x5a, 0x64, //0x00004930 .quad 7231123676894144233
	0x3b, 0xdf, 0x4f, 0x8d, 0x97, 0x6e, 0x12, 0x83, //0x00004938 .quad -9002011107970261189
	0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, 0x70, 0x3d, //0x00004940 .quad 4427218577690292387
	0x0a, 0xd7, 0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, //0x00004948 .quad -6640827866535438582
	0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00004950 QUAD $0xcccccccccccccccc; QUAD $0xcccccccccccccccc  // .space 16, '\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004960 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00004968 .quad -9223372036854775808
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004970 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, //0x00004978 .quad -6917529027641081856
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004980 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, //0x00004988 .quad -4035225266123964416
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004990 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00004998 .quad -432345564227567616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000049a0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x9c, //0x000049a8 .quad -7187745005283311616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000049b0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xc3, //0x000049b8 .quad -4372995238176751616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000049c0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xf4, //0x000049c8 .quad -854558029293551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000049d0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x96, 0x98, //0x000049d8 .quad -7451627795949551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000049e0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xbc, 0xbe, //0x000049e8 .quad -4702848726509551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000049f0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6b, 0xee, //0x000049f8 .quad -1266874889709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004a00 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x02, 0x95, //0x00004a08 .quad -7709325833709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004a10 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x40, 0xb7, 0x43, 0xba, //0x00004a18 .quad -5024971273709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004a20 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x10, 0xa5, 0xd4, 0xe8, //0x00004a28 .quad -1669528073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004a30 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x2a, 0xe7, 0x84, 0x91, //0x00004a38 .quad -7960984073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004a40 .quad 0
	0x00, 0x00, 0x00, 0x80, 0xf4, 0x20, 0xe6, 0xb5, //0x00004a48 .quad -5339544073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004a50 .quad 0
	0x00, 0x00, 0x00, 0xa0, 0x31, 0xa9, 0x5f, 0xe3, //0x00004a58 .quad -2062744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004a60 .quad 0
	0x00, 0x00, 0x00, 0x04, 0xbf, 0xc9, 0x1b, 0x8e, //0x00004a68 .quad -8206744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004a70 .quad 0
	0x00, 0x00, 0x00, 0xc5, 0x2e, 0xbc, 0xa2, 0xb1, //0x00004a78 .quad -5646744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004a80 .quad 0
	0x00, 0x00, 0x40, 0x76, 0x3a, 0x6b, 0x0b, 0xde, //0x00004a88 .quad -2446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004a90 .quad 0
	0x00, 0x00, 0xe8, 0x89, 0x04, 0x23, 0xc7, 0x8a, //0x00004a98 .quad -8446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004aa0 .quad 0
	0x00, 0x00, 0x62, 0xac, 0xc5, 0xeb, 0x78, 0xad, //0x00004aa8 .quad -5946744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ab0 .quad 0
	0x00, 0x80, 0x7a, 0x17, 0xb7, 0x26, 0xd7, 0xd8, //0x00004ab8 .quad -2821744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ac0 .quad 0
	0x00, 0x90, 0xac, 0x6e, 0x32, 0x78, 0x86, 0x87, //0x00004ac8 .quad -8681119073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ad0 .quad 0
	0x00, 0xb4, 0x57, 0x0a, 0x3f, 0x16, 0x68, 0xa9, //0x00004ad8 .quad -6239712823709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ae0 .quad 0
	0x00, 0xa1, 0xed, 0xcc, 0xce, 0x1b, 0xc2, 0xd3, //0x00004ae8 .quad -3187955011209551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004af0 .quad 0
	0xa0, 0x84, 0x14, 0x40, 0x61, 0x51, 0x59, 0x84, //0x00004af8 .quad -8910000909647051616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004b00 .quad 0
	0xc8, 0xa5, 0x19, 0x90, 0xb9, 0xa5, 0x6f, 0xa5, //0x00004b08 .quad -6525815118631426616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004b10 .quad 0
	0x3a, 0x0f, 0x20, 0xf4, 0x27, 0x8f, 0xcb, 0xce, //0x00004b18 .quad -3545582879861895366
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, //0x00004b20 .quad 4611686018427387904
	0x84, 0x09, 0x94, 0xf8, 0x78, 0x39, 0x3f, 0x81, //0x00004b28 .quad -9133518327554766460
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, //0x00004b30 .quad 5764607523034234880
	0xe5, 0x0b, 0xb9, 0x36, 0xd7, 0x07, 0x8f, 0xa1, //0x00004b38 .quad -6805211891016070171
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa4, //0x00004b40 .quad -6629298651489370112
	0xde, 0x4e, 0x67, 0x04, 0xcd, 0xc9, 0xf2, 0xc9, //0x00004b48 .quad -3894828845342699810
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4d, //0x00004b50 .quad 5548434740920451072
	0x96, 0x22, 0x81, 0x45, 0x40, 0x7c, 0x6f, 0xfc, //0x00004b58 .quad -256850038250986858
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xf0, //0x00004b60 .quad -1143914305352105984
	0x9d, 0xb5, 0x70, 0x2b, 0xa8, 0xad, 0xc5, 0x9d, //0x00004b68 .quad -7078060301547948643
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6c, //0x00004b70 .quad 7793479155164643328
	0x05, 0xe3, 0x4c, 0x36, 0x12, 0x19, 0x37, 0xc5, //0x00004b78 .quad -4235889358507547899
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0xc7, //0x00004b80 .quad -4093209111326359552
	0xc6, 0x1b, 0xe0, 0xc3, 0x56, 0xdf, 0x84, 0xf6, //0x00004b88 .quad -683175679707046970
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x7f, 0x3c, //0x00004b90 .quad 4359273333062107136
	0x5c, 0x11, 0x6c, 0x3a, 0x96, 0x0b, 0x13, 0x9a, //0x00004b98 .quad -7344513827457986212
	0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x9f, 0x4b, //0x00004ba0 .quad 5449091666327633920
	0xb3, 0x15, 0x07, 0xc9, 0x7b, 0xce, 0x97, 0xc0, //0x00004ba8 .quad -4568956265895094861
	0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x86, 0x1e, //0x00004bb0 .quad 2199678564482154496
	0x20, 0xdb, 0x48, 0xbb, 0x1a, 0xc2, 0xbd, 0xf0, //0x00004bb8 .quad -1099509313941480672
	0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x14, 0x13, //0x00004bc0 .quad 1374799102801346560
	0xf4, 0x88, 0x0d, 0xb5, 0x50, 0x99, 0x76, 0x96, //0x00004bc8 .quad -7604722348854507276
	0x00, 0x00, 0x00, 0x00, 0xa0, 0x55, 0xd9, 0x17, //0x00004bd0 .quad 1718498878501683200
	0x31, 0xeb, 0x50, 0xe2, 0xa4, 0x3f, 0x14, 0xbc, //0x00004bd8 .quad -4894216917640746191
	0x00, 0x00, 0x00, 0x00, 0x08, 0xab, 0xcf, 0x5d, //0x00004be0 .quad 6759809616554491904
	0xfd, 0x25, 0xe5, 0x1a, 0x8e, 0x4f, 0x19, 0xeb, //0x00004be8 .quad -1506085128623544835
	0x00, 0x00, 0x00, 0x00, 0xe5, 0xca, 0xa1, 0x5a, //0x00004bf0 .quad 6530724019560251392
	0xbe, 0x37, 0xcf, 0xd0, 0xb8, 0xd1, 0xef, 0x92, //0x00004bf8 .quad -7858832233030797378
	0x00, 0x00, 0x00, 0x40, 0x9e, 0x3d, 0x4a, 0xf1, //0x00004c00 .quad -1059967012404461568
	0xad, 0x05, 0x03, 0x05, 0x27, 0xc6, 0xab, 0xb7, //0x00004c08 .quad -5211854272861108819
	0x00, 0x00, 0x00, 0xd0, 0x05, 0xcd, 0x9c, 0x6d, //0x00004c10 .quad 7898413271349198848
	0x19, 0xc7, 0x43, 0xc6, 0xb0, 0xb7, 0x96, 0xe5, //0x00004c18 .quad -1903131822648998119
	0x00, 0x00, 0x00, 0xa2, 0x23, 0x00, 0x82, 0xe4, //0x00004c20 .quad -1981020733047832576
	0x6f, 0x5c, 0xea, 0x7b, 0xce, 0x32, 0x7e, 0x8f, //0x00004c28 .quad -8106986416796705681
	0x00, 0x00, 0x80, 0x8a, 0x2c, 0x80, 0xa2, 0xdd, //0x00004c30 .quad -2476275916309790720
	0x8b, 0xf3, 0xe4, 0x1a, 0x82, 0xbf, 0x5d, 0xb3, //0x00004c38 .quad -5522047002568494197
	0x00, 0x00, 0x20, 0xad, 0x37, 0x20, 0x0b, 0xd5, //0x00004c40 .quad -3095344895387238400
	0x6e, 0x30, 0x9e, 0xa1, 0x62, 0x2f, 0x35, 0xe0, //0x00004c48 .quad -2290872734783229842
	0x00, 0x00, 0x34, 0xcc, 0x22, 0xf4, 0x26, 0x45, //0x00004c50 .quad 4982938468024057856
	0x45, 0xde, 0x02, 0xa5, 0x9d, 0x3d, 0x21, 0x8c, //0x00004c58 .quad -8349324486880600507
	0x00, 0x00, 0x41, 0x7f, 0x2b, 0xb1, 0x70, 0x96, //0x00004c60 .quad -7606384970252091392
	0xd6, 0x95, 0x43, 0x0e, 0x05, 0x8d, 0x29, 0xaf, //0x00004c68 .quad -5824969590173362730
	0x00, 0x40, 0x11, 0x5f, 0x76, 0xdd, 0x0c, 0x3c, //0x00004c70 .quad 4327076842467049472
	0x4c, 0x7b, 0xd4, 0x51, 0x46, 0xf0, 0xf3, 0xda, //0x00004c78 .quad -2669525969289315508
	0x00, 0xc8, 0x6a, 0xfb, 0x69, 0x0a, 0x88, 0xa5, //0x00004c80 .quad -6518949010312869888
	0x0f, 0xcd, 0x24, 0xf3, 0x2b, 0x76, 0xd8, 0x88, //0x00004c88 .quad -8585982758446904049
	0x00, 0x7a, 0x45, 0x7a, 0x04, 0x0d, 0xea, 0x8e, //0x00004c90 .quad -8148686262891087360
	0x53, 0x00, 0xee, 0xef, 0xb6, 0x93, 0x0e, 0xab, //0x00004c98 .quad -6120792429631242157
	0x80, 0xd8, 0xd6, 0x98, 0x45, 0x90, 0xa4, 0x72, //0x00004ca0 .quad 8260886245095692416
	0x68, 0x80, 0xe9, 0xab, 0xa4, 0x38, 0xd2, 0xd5, //0x00004ca8 .quad -3039304518611664792
	0x50, 0x47, 0x86, 0x7f, 0x2b, 0xda, 0xa6, 0x47, //0x00004cb0 .quad 5163053903184807760
	0x41, 0xf0, 0x71, 0xeb, 0x66, 0x63, 0xa3, 0x85, //0x00004cb8 .quad -8817094351773372351
	0x24, 0xd9, 0x67, 0x5f, 0xb6, 0x90, 0x90, 0x99, //0x00004cc0 .quad -7381240676301154012
	0x51, 0x6c, 0x4e, 0xa6, 0x40, 0x3c, 0x0c, 0xa7, //0x00004cc8 .quad -6409681921289327535
	0x6d, 0xcf, 0x41, 0xf7, 0xe3, 0xb4, 0xf4, 0xff, //0x00004cd0 .quad -3178808521666707
	0x65, 0x07, 0xe2, 0xcf, 0x50, 0x4b, 0xcf, 0xd0, //0x00004cd8 .quad -3400416383184271515
	0xa4, 0x21, 0x89, 0x7a, 0x0e, 0xf1, 0xf8, 0xbf, //0x00004ce0 .quad -4613672773753429596
	0x9f, 0x44, 0xed, 0x81, 0x12, 0x8f, 0x81, 0x82, //0x00004ce8 .quad -9042789267131251553
	0x0d, 0x6a, 0x2b, 0x19, 0x52, 0x2d, 0xf7, 0xaf, //0x00004cf0 .quad -5767090967191786995
	0xc7, 0x95, 0x68, 0x22, 0xd7, 0xf2, 0x21, 0xa3, //0x00004cf8 .quad -6691800565486676537
	0x90, 0x44, 0x76, 0x9f, 0xa6, 0xf8, 0xf4, 0x9b, //0x00004d00 .quad -7208863708989733744
	0x39, 0xbb, 0x02, 0xeb, 0x8c, 0x6f, 0xea, 0xcb, //0x00004d08 .quad -3753064688430957767
	0xb4, 0xd5, 0x53, 0x47, 0xd0, 0x36, 0xf2, 0x02, //0x00004d10 .quad 212292400617608628
	0x08, 0x6a, 0xc3, 0x25, 0x70, 0x0b, 0xe5, 0xfe, //0x00004d18 .quad -79644842111309304
	0x90, 0x65, 0x94, 0x2c, 0x42, 0x62, 0xd7, 0x01, //0x00004d20 .quad 132682750386005392
	0x45, 0x22, 0x9a, 0x17, 0x26, 0x27, 0x4f, 0x9f, //0x00004d28 .quad -6967307053960650171
	0xf5, 0x7e, 0xb9, 0xb7, 0xd2, 0x3a, 0x4d, 0x42, //0x00004d30 .quad 4777539456409894645
	0xd6, 0xaa, 0x80, 0x9d, 0xef, 0xf0, 0x22, 0xc7, //0x00004d38 .quad -4097447799023424810
	0xb2, 0xde, 0xa7, 0x65, 0x87, 0x89, 0xe0, 0xd2, //0x00004d40 .quad -3251447716342407502
	0x8b, 0xd5, 0xe0, 0x84, 0x2b, 0xad, 0xeb, 0xf8, //0x00004d48 .quad -510123730351893109
	0x2f, 0xeb, 0x88, 0x9f, 0xf4, 0x55, 0xcc, 0x63, //0x00004d50 .quad 7191217214140771119
	0x77, 0x85, 0x0c, 0x33, 0x3b, 0x4c, 0x93, 0x9b, //0x00004d58 .quad -7236356359111015049
	0xfb, 0x25, 0x6b, 0xc7, 0x71, 0x6b, 0xbf, 0x3c, //0x00004d60 .quad 4377335499248575995
	0xd5, 0xa6, 0xcf, 0xff, 0x49, 0x1f, 0x78, 0xc2, //0x00004d68 .quad -4433759430461380907
	0x7a, 0xef, 0x45, 0x39, 0x4e, 0x46, 0xef, 0x8b, //0x00004d70 .quad -8363388681221443718
	0x8a, 0x90, 0xc3, 0x7f, 0x1c, 0x27, 0x16, 0xf3, //0x00004d78 .quad -930513269649338230
	0xac, 0xb5, 0xcb, 0xe3, 0xf0, 0x8b, 0x75, 0x97, //0x00004d80 .quad -7532960934977096276
	0x56, 0x3a, 0xda, 0xcf, 0x71, 0xd8, 0xed, 0x97, //0x00004d88 .quad -7499099821171918250
	0x17, 0xa3, 0xbe, 0x1c, 0xed, 0xee, 0x52, 0x3d, //0x00004d90 .quad 4418856886560793367
	0xec, 0xc8, 0xd0, 0x43, 0x8e, 0x4e, 0xe9, 0xbd, //0x00004d98 .quad -4762188758037509908
	0xdd, 0x4b, 0xee, 0x63, 0xa8, 0xaa, 0xa7, 0x4c, //0x00004da0 .quad 5523571108200991709
	0x27, 0xfb, 0xc4, 0xd4, 0x31, 0xa2, 0x63, 0xed, //0x00004da8 .quad -1341049929119499481
	0x6a, 0xef, 0x74, 0x3e, 0xa9, 0xca, 0xe8, 0x8f, //0x00004db0 .quad -8076983103442849942
	0xf8, 0x1c, 0xfb, 0x24, 0x5f, 0x45, 0x5e, 0x94, //0x00004db8 .quad -7755685233340769032
	0x44, 0x2b, 0x12, 0x8e, 0x53, 0xfd, 0xe2, 0xb3, //0x00004dc0 .quad -5484542860876174524
	0x36, 0xe4, 0x39, 0xee, 0xb6, 0xd6, 0x75, 0xb9, //0x00004dc8 .quad -5082920523248573386
	0x16, 0xb6, 0x96, 0x71, 0xa8, 0xbc, 0xdb, 0x60, //0x00004dd0 .quad 6979379479186945558
	0x44, 0x5d, 0xc8, 0xa9, 0x64, 0x4c, 0xd3, 0xe7, //0x00004dd8 .quad -1741964635633328828
	0xcd, 0x31, 0xfe, 0x46, 0xe9, 0x55, 0x89, 0xbc, //0x00004de0 .quad -4861259862362934835
	0x4a, 0x3a, 0x1d, 0xea, 0xbe, 0x0f, 0xe4, 0x90, //0x00004de8 .quad -8006256924911912374
	0x41, 0xbe, 0xbd, 0x98, 0x63, 0xab, 0xab, 0x6b, //0x00004df0 .quad 7758483227328495169
	0xdd, 0x88, 0xa4, 0xa4, 0xae, 0x13, 0x1d, 0xb5, //0x00004df8 .quad -5396135137712502563
	0xd1, 0x2d, 0xed, 0x7e, 0x3c, 0x96, 0x96, 0xc6, //0x00004e00 .quad -4136954021121544751
	0x14, 0xab, 0xcd, 0x4d, 0x9a, 0x58, 0x64, 0xe2, //0x00004e08 .quad -2133482903713240300
	0xa2, 0x3c, 0x54, 0xcf, 0xe5, 0x1d, 0x1e, 0xfc, //0x00004e10 .quad -279753253987271518
	0xec, 0x8a, 0xa0, 0x70, 0x60, 0xb7, 0x7e, 0x8d, //0x00004e18 .quad -8250955842461857044
	0xcb, 0x4b, 0x29, 0x43, 0x5f, 0xa5, 0x25, 0x3b, //0x00004e20 .quad 4261994450943298507
	0xa8, 0xad, 0xc8, 0x8c, 0x38, 0x65, 0xde, 0xb0, //0x00004e28 .quad -5702008784649933400
	0xbe, 0x9e, 0xf3, 0x13, 0xb7, 0x0e, 0xef, 0x49, //0x00004e30 .quad 5327493063679123134
	0x12, 0xd9, 0xfa, 0xaf, 0x86, 0xfe, 0x15, 0xdd, //0x00004e38 .quad -2515824962385028846
	0x37, 0x43, 0x78, 0x6c, 0x32, 0x69, 0x35, 0x6e, //0x00004e40 .quad 7941369183226839863
	0xab, 0xc7, 0xfc, 0x2d, 0x14, 0xbf, 0x2d, 0x8a, //0x00004e48 .quad -8489919629131724885
	0x04, 0x54, 0x96, 0x07, 0x7f, 0xc3, 0xc2, 0x49, //0x00004e50 .quad 5315025460606161924
	0x96, 0xf9, 0x7b, 0x39, 0xd9, 0x2e, 0xb9, 0xac, //0x00004e58 .quad -6000713517987268202
	0x06, 0xe9, 0x7b, 0xc9, 0x5e, 0x74, 0x33, 0xdc, //0x00004e60 .quad -2579590211097073402
	0xfb, 0xf7, 0xda, 0x87, 0x8f, 0x7a, 0xe7, 0xd7, //0x00004e68 .quad -2889205879056697349
	0xa3, 0x71, 0xed, 0x3d, 0xbb, 0x28, 0xa0, 0x69, //0x00004e70 .quad 7611128154919104931
	0xfd, 0xda, 0xe8, 0xb4, 0x99, 0xac, 0xf0, 0x86, //0x00004e78 .quad -8723282702051517699
	0x0c, 0xce, 0x68, 0x0d, 0xea, 0x32, 0x08, 0xc4, //0x00004e80 .quad -4321147861633282548
	0xbc, 0x11, 0x23, 0x22, 0xc0, 0xd7, 0xac, 0xa8, //0x00004e88 .quad -6292417359137009220
	0x90, 0x01, 0xc3, 0x90, 0xa4, 0x3f, 0x0a, 0xf5, //0x00004e90 .quad -789748808614215280
	0x2b, 0xd6, 0xab, 0x2a, 0xb0, 0x0d, 0xd8, 0xd2, //0x00004e98 .quad -3253835680493873621
	0xfa, 0xe0, 0x79, 0xda, 0xc6, 0x67, 0x26, 0x79, //0x00004ea0 .quad 8729779031470891258
	0xdb, 0x65, 0xab, 0x1a, 0x8e, 0x08, 0xc7, 0x83, //0x00004ea8 .quad -8951176327949752869
	0x38, 0x59, 0x18, 0x91, 0xb8, 0x01, 0x70, 0x57, //0x00004eb0 .quad 6300537770911226168
	0x52, 0x3f, 0x56, 0xa1, 0xb1, 0xca, 0xb8, 0xa4, //0x00004eb8 .quad -6577284391509803182
	0x86, 0x6f, 0x5e, 0xb5, 0x26, 0x02, 0x4c, 0xed, //0x00004ec0 .quad -1347699823215743098
	0x26, 0xcf, 0xab, 0x09, 0x5e, 0xfd, 0xe6, 0xcd, //0x00004ec8 .quad -3609919470959866074
	0xb4, 0x05, 0x5b, 0x31, 0x58, 0x81, 0x4f, 0x54, //0x00004ed0 .quad 6075216638131242420
	0x78, 0x61, 0x0b, 0xc6, 0x5a, 0x5e, 0xb0, 0x80, //0x00004ed8 .quad -9173728696990998152
	0x21, 0xc7, 0xb1, 0x3d, 0xae, 0x61, 0x63, 0x69, //0x00004ee0 .quad 7594020797664053025
	0xd6, 0x39, 0x8e, 0x77, 0xf1, 0x75, 0xdc, 0xa0, //0x00004ee8 .quad -6855474852811359786
	0xe9, 0x38, 0x1e, 0xcd, 0x19, 0x3a, 0xbc, 0x03, //0x00004ef0 .quad 269153960225290473
	0x4c, 0xc8, 0x71, 0xd5, 0x6d, 0x93, 0x13, 0xc9, //0x00004ef8 .quad -3957657547586811828
	0x23, 0xc7, 0x65, 0x40, 0xa0, 0x48, 0xab, 0x04, //0x00004f00 .quad 336442450281613091
	0x5f, 0x3a, 0xce, 0x4a, 0x49, 0x78, 0x58, 0xfb, //0x00004f08 .quad -335385916056126881
	0x76, 0x9c, 0x3f, 0x28, 0x64, 0x0d, 0xeb, 0x62, //0x00004f10 .quad 7127805559067090038
	0x7b, 0xe4, 0xc0, 0xce, 0x2d, 0x4b, 0x17, 0x9d, //0x00004f18 .quad -7127145225176161157
	0x94, 0x83, 0x4f, 0x32, 0xbd, 0xd0, 0xa5, 0x3b, //0x00004f20 .quad 4298070930406474644
	0x9a, 0x1d, 0x71, 0x42, 0xf9, 0x1d, 0x5d, 0xc4, //0x00004f28 .quad -4297245513042813542
	0x79, 0x64, 0xe3, 0x7e, 0xec, 0x44, 0x8f, 0xca, //0x00004f30 .quad -3850783373846682503
	0x00, 0x65, 0x0d, 0x93, 0x77, 0x65, 0x74, 0xf5, //0x00004f38 .quad -759870872876129024
	0xcb, 0x1e, 0x4e, 0xcf, 0x13, 0x8b, 0x99, 0x7e, //0x00004f40 .quad 9122475437414293195
	0x20, 0x5f, 0xe8, 0xbb, 0x6a, 0xbf, 0x68, 0x99, //0x00004f48 .quad -7392448323188662496
	0x7e, 0xa6, 0x21, 0xc3, 0xd8, 0xed, 0x3f, 0x9e, //0x00004f50 .quad -7043649776941685122
	0xe8, 0x76, 0xe2, 0x6a, 0x45, 0xef, 0xc2, 0xbf, //0x00004f58 .quad -4628874385558440216
	0x1e, 0x10, 0xea, 0xf3, 0x4e, 0xe9, 0xcf, 0xc5, //0x00004f60 .quad -4192876202749718498
	0xa2, 0x14, 0x9b, 0xc5, 0x16, 0xab, 0xb3, 0xef, //0x00004f68 .quad -1174406963520662366
	0x12, 0x4a, 0x72, 0x58, 0xd1, 0xf1, 0xa1, 0xbb, //0x00004f70 .quad -4926390635932268014
	0xe5, 0xec, 0x80, 0x3b, 0xee, 0x4a, 0xd0, 0x95, //0x00004f78 .quad -7651533379841495835
	0x97, 0xdc, 0x8e, 0xae, 0x45, 0x6e, 0x8a, 0x2a, //0x00004f80 .quad 3065383741939440791
	0x1f, 0x28, 0x61, 0xca, 0xa9, 0x5d, 0x44, 0xbb, //0x00004f88 .quad -4952730706374481889
	0xbd, 0x93, 0x32, 0x1a, 0xd7, 0x09, 0x2d, 0xf5, //0x00004f90 .quad -779956341003086915
	0x26, 0x72, 0xf9, 0x3c, 0x14, 0x75, 0x15, 0xea, //0x00004f98 .quad -1579227364540714458
	0x56, 0x9c, 0x5f, 0x70, 0x26, 0x26, 0x3c, 0x59, //0x00004fa0 .quad 6430056314514152534
	0x58, 0xe7, 0x1b, 0xa6, 0x2c, 0x69, 0x4d, 0x92, //0x00004fa8 .quad -7904546130479028392
	0x6c, 0x83, 0x77, 0x0c, 0xb0, 0x2f, 0x8b, 0x6f, //0x00004fb0 .quad 8037570393142690668
	0x2e, 0xe1, 0xa2, 0xcf, 0x77, 0xc3, 0xe0, 0xb6, //0x00004fb8 .quad -5268996644671397586
	0x47, 0x64, 0x95, 0x0f, 0x9c, 0xfb, 0x6d, 0x0b, //0x00004fc0 .quad 823590954573587527
	0x7a, 0x99, 0x8b, 0xc3, 0x55, 0xf4, 0x98, 0xe4, //0x00004fc8 .quad -1974559787411859078
	0xac, 0x5e, 0xbd, 0x89, 0x41, 0xbd, 0x24, 0x47, //0x00004fd0 .quad 5126430365035880108
	0xec, 0x3f, 0x37, 0x9a, 0xb5, 0x98, 0xdf, 0x8e, //0x00004fd8 .quad -8151628894773493780
	0x57, 0xb6, 0x2c, 0xec, 0x91, 0xec, 0xed, 0x58, //0x00004fe0 .quad 6408037956294850135
	0xe7, 0x0f, 0xc5, 0x00, 0xe3, 0x7e, 0x97, 0xb2, //0x00004fe8 .quad -5577850100039479321
	0xed, 0xe3, 0x37, 0x67, 0xb6, 0x67, 0x29, 0x2f, //0x00004ff0 .quad 3398361426941174765
	0xe1, 0x53, 0xf6, 0xc0, 0x9b, 0x5e, 0x3d, 0xdf, //0x00004ff8 .quad -2360626606621961247
	0x74, 0xee, 0x82, 0x00, 0xd2, 0xe0, 0x79, 0xbd, //0x00005000 .quad -4793553135802847628
	0x6c, 0xf4, 0x99, 0x58, 0x21, 0x5b, 0x86, 0x8b, //0x00005008 .quad -8392920656779807636
	0x11, 0xaa, 0xa3, 0x80, 0x06, 0x59, 0xd8, 0xec, //0x00005010 .quad -1380255401326171631
	0x87, 0x71, 0xc0, 0xae, 0xe9, 0xf1, 0x67, 0xae, //0x00005018 .quad -5879464802547371641
	0x95, 0x94, 0xcc, 0x20, 0x48, 0x6f, 0x0e, 0xe8, //0x00005020 .quad -1725319251657714539
	0xe9, 0x8d, 0x70, 0x1a, 0x64, 0xee, 0x01, 0xda, //0x00005028 .quad -2737644984756826647
	0xdd, 0xdc, 0x7f, 0x14, 0x8d, 0x05, 0x09, 0x31, //0x00005030 .quad 3533361486141316317
	0xb2, 0x58, 0x86, 0x90, 0xfe, 0x34, 0x41, 0x88, //0x00005038 .quad -8628557143114098510
	0x15, 0xd4, 0x9f, 0x59, 0xf0, 0x46, 0x4b, 0xbd, //0x00005040 .quad -4806670179178130411
	0xde, 0xee, 0xa7, 0x34, 0x3e, 0x82, 0x51, 0xaa, //0x00005048 .quad -6174010410465235234
	0x1a, 0xc9, 0x07, 0x70, 0xac, 0x18, 0x9e, 0x6c, //0x00005050 .quad 7826720331309500698
	0x96, 0xea, 0xd1, 0xc1, 0xcd, 0xe2, 0xe5, 0xd4, //0x00005058 .quad -3105826994654156138
	0xb0, 0xdd, 0x04, 0xc6, 0x6b, 0xcf, 0xe2, 0x03, //0x00005060 .quad 280014188641050032
	0x9e, 0x32, 0x23, 0x99, 0xc0, 0xad, 0x0f, 0x85, //0x00005068 .quad -8858670899299929442
	0x1c, 0x15, 0x86, 0xb7, 0x46, 0x83, 0xdb, 0x84, //0x00005070 .quad -8873354301053463268
	0x45, 0xff, 0x6b, 0xbf, 0x30, 0x99, 0x53, 0xa6, //0x00005078 .quad -6461652605697523899
	0x63, 0x9a, 0x67, 0x65, 0x18, 0x64, 0x12, 0xe6, //0x00005080 .quad -1868320839462053277
	0x16, 0xff, 0x46, 0xef, 0x7c, 0x7f, 0xe8, 0xcf, //0x00005088 .quad -3465379738694516970
	0x7e, 0xc0, 0x60, 0x3f, 0x8f, 0x7e, 0xcb, 0x4f, //0x00005090 .quad 5749828502977298558
	0x6e, 0x5f, 0x8c, 0x15, 0xae, 0x4f, 0xf1, 0x81, //0x00005098 .quad -9083391364325154962
	0x9d, 0xf0, 0x38, 0x0f, 0x33, 0x5e, 0xbe, 0xe3, //0x000050a0 .quad -2036086408133152611
	0x49, 0x77, 0xef, 0x9a, 0x99, 0xa3, 0x6d, 0xa2, //0x000050a8 .quad -6742553186979055799
	0xc5, 0x2c, 0x07, 0xd3, 0xbf, 0xf5, 0xad, 0x5c, //0x000050b0 .quad 6678264026688335045
	0x1c, 0x55, 0xab, 0x01, 0x80, 0x0c, 0x09, 0xcb, //0x000050b8 .quad -3816505465296431844
	0xf6, 0xf7, 0xc8, 0xc7, 0x2f, 0x73, 0xd9, 0x73, //0x000050c0 .quad 8347830033360418806
	0x63, 0x2a, 0x16, 0x02, 0xa0, 0x4f, 0xcb, 0xfd, //0x000050c8 .quad -158945813193151901
	0xfa, 0x9a, 0xdd, 0xdc, 0xfd, 0xe7, 0x67, 0x28, //0x000050d0 .quad 2911550761636567802
	0x7e, 0xda, 0x4d, 0x01, 0xc4, 0x11, 0x9f, 0x9e, //0x000050d8 .quad -7016870160886801794
	0xb8, 0x01, 0x15, 0x54, 0xfd, 0xe1, 0x81, 0xb2, //0x000050e0 .quad -5583933584809066056
	0x1d, 0x51, 0xa1, 0x01, 0x35, 0xd6, 0x46, 0xc6, //0x000050e8 .quad -4159401682681114339
	0x26, 0x42, 0x1a, 0xa9, 0x7c, 0x5a, 0x22, 0x1f, //0x000050f0 .quad 2243455055843443238
	0x65, 0xa5, 0x09, 0x42, 0xc2, 0x8b, 0xd8, 0xf7, //0x000050f8 .quad -587566084924005019
	0x58, 0x69, 0xb0, 0xe9, 0x8d, 0x78, 0x75, 0x33, //0x00005100 .quad 3708002419115845976
	0x5f, 0x07, 0x46, 0x69, 0x59, 0x57, 0xe7, 0x9a, //0x00005108 .quad -7284757830718584993
	0xae, 0x83, 0x1c, 0x64, 0xb1, 0xd6, 0x52, 0x00, //0x00005110 .quad 23317005467419566
	0x37, 0x89, 0x97, 0xc3, 0x2f, 0x2d, 0xa1, 0xc1, //0x00005118 .quad -4494261269970843337
	0x9a, 0xa4, 0x23, 0xbd, 0x5d, 0x8c, 0x67, 0xc0, //0x00005120 .quad -4582539761593113446
	0x84, 0x6b, 0x7d, 0xb4, 0x7b, 0x78, 0x09, 0xf2, //0x00005128 .quad -1006140569036166268
	0xe0, 0x46, 0x36, 0x96, 0xba, 0xb7, 0x40, 0xf8, //0x00005130 .quad -558244341782001952
	0x32, 0x63, 0xce, 0x50, 0x4d, 0xeb, 0x45, 0x97, //0x00005138 .quad -7546366883288685774
	0x98, 0xd8, 0xc3, 0x3b, 0xa9, 0xe5, 0x50, 0xb6, //0x00005140 .quad -5309491445654890344
	0xff, 0xfb, 0x01, 0xa5, 0x20, 0x66, 0x17, 0xbd, //0x00005148 .quad -4821272585683469313
	0xbe, 0xce, 0xb4, 0x8a, 0x13, 0x1f, 0xe5, 0xa3, //0x00005150 .quad -6636864307068612930
	0xff, 0x7a, 0x42, 0xce, 0xa8, 0x3f, 0x5d, 0xec, //0x00005158 .quad -1414904713676948737
	0x37, 0x01, 0xb1, 0x36, 0x6c, 0x33, 0x6f, 0xc6, //0x00005160 .quad -4148040191917883081
	0xdf, 0x8c, 0xe9, 0x80, 0xc9, 0x47, 0xba, 0x93, //0x00005168 .quad -7801844473689174817
	0x84, 0x41, 0x5d, 0x44, 0x47, 0x00, 0x0b, 0xb8, //0x00005170 .quad -5185050239897353852
	0x17, 0xf0, 0x23, 0xe1, 0xbb, 0xd9, 0xa8, 0xb8, //0x00005178 .quad -5140619573684080617
	0xe5, 0x91, 0x74, 0x15, 0x59, 0xc0, 0x0d, 0xa6, //0x00005180 .quad -6481312799871692315
	0x1d, 0xec, 0x6c, 0xd9, 0x2a, 0x10, 0xd3, 0xe6, //0x00005188 .quad -1814088448677712867
	0x2f, 0xdb, 0x68, 0xad, 0x37, 0x98, 0xc8, 0x87, //0x00005190 .quad -8662506518347195601
	0x92, 0x13, 0xe4, 0xc7, 0x1a, 0xea, 0x43, 0x90, //0x00005198 .quad -8051334308064652398
	0xfb, 0x11, 0xc3, 0x98, 0x45, 0xbe, 0xba, 0x29, //0x000051a0 .quad 3006924907348169211
	0x77, 0x18, 0xdd, 0x79, 0xa1, 0xe4, 0x54, 0xb4, //0x000051a8 .quad -5452481866653427593
	0x7a, 0xd6, 0xf3, 0xfe, 0xd6, 0x6d, 0x29, 0xf4, //0x000051b0 .quad -853029884242176390
	0x94, 0x5e, 0x54, 0xd8, 0xc9, 0x1d, 0x6a, 0xe1, //0x000051b8 .quad -2203916314889396588
	0x0c, 0x66, 0x58, 0x5f, 0xa6, 0xe4, 0x99, 0x18, //0x000051c0 .quad 1772699331562333708
	0x1d, 0xbb, 0x34, 0x27, 0x9e, 0x52, 0xe2, 0x8c, //0x000051c8 .quad -8294976724446954723
	0x8f, 0x7f, 0x2e, 0xf7, 0xcf, 0x5d, 0xc0, 0x5e, //0x000051d0 .quad 6827560182880305039
	0xe4, 0xe9, 0x01, 0xb1, 0x45, 0xe7, 0x1a, 0xb0, //0x000051d8 .quad -5757034887131305500
	0x73, 0x1f, 0xfa, 0xf4, 0x43, 0x75, 0x70, 0x76, //0x000051e0 .quad 8534450228600381299
	0x5d, 0x64, 0x42, 0x1d, 0x17, 0xa1, 0x21, 0xdc, //0x000051e8 .quad -2584607590486743971
	0xa8, 0x53, 0x1c, 0x79, 0x4a, 0x49, 0x06, 0x6a, //0x000051f0 .quad 7639874402088932264
	0xba, 0x7e, 0x49, 0x72, 0xae, 0x04, 0x95, 0x89, //0x000051f8 .quad -8532908771695296838
	0x92, 0x68, 0x63, 0x17, 0x9d, 0xdb, 0x87, 0x04, //0x00005200 .quad 326470965756389522
	0x69, 0xde, 0xdb, 0x0e, 0xda, 0x45, 0xfa, 0xab, //0x00005208 .quad -6054449946191733143
	0xb6, 0x42, 0x3c, 0x5d, 0x84, 0xd2, 0xa9, 0x45, //0x00005210 .quad 5019774725622874806
	0x03, 0xd6, 0x92, 0x92, 0x50, 0xd7, 0xf8, 0xd6, //0x00005218 .quad -2956376414312278525
	0xb2, 0xa9, 0x45, 0xba, 0x92, 0x23, 0x8a, 0x0b, //0x00005220 .quad 831516194300602802
	0xc2, 0xc5, 0x9b, 0x5b, 0x92, 0x86, 0x5b, 0x86, //0x00005228 .quad -8765264286586255934
	0x1e, 0x14, 0xd7, 0x68, 0x77, 0xac, 0x6c, 0x8e, //0x00005230 .quad -8183976793979022306
	0x32, 0xb7, 0x82, 0xf2, 0x36, 0x68, 0xf2, 0xa7, //0x00005238 .quad -6344894339805432014
	0x26, 0xd9, 0x0c, 0x43, 0x95, 0xd7, 0x07, 0x32, //0x00005240 .quad 3605087062808385830
	0xff, 0x64, 0x23, 0xaf, 0x44, 0x02, 0xef, 0xd1, //0x00005248 .quad -3319431906329402113
	0xb8, 0x07, 0xe8, 0x49, 0xbd, 0xe6, 0x44, 0x7f, //0x00005250 .quad 9170708441896323000
	0x1f, 0x1f, 0x76, 0xed, 0x6a, 0x61, 0x35, 0x83, //0x00005258 .quad -8992173969096958177
	0xa6, 0x09, 0x62, 0x9c, 0x6c, 0x20, 0x16, 0x5f, //0x00005260 .quad 6851699533943015846
	0xe7, 0xa6, 0xd3, 0xa8, 0xc5, 0xb9, 0x02, 0xa4, //0x00005268 .quad -6628531442943809817
	0x0f, 0x8c, 0x7a, 0xc3, 0x87, 0xa8, 0xdb, 0x36, //0x00005270 .quad 3952938399001381903
	0xa1, 0x90, 0x08, 0x13, 0x37, 0x68, 0x03, 0xcd, //0x00005278 .quad -3673978285252374367
	0x89, 0x97, 0x2c, 0xda, 0x54, 0x49, 0x49, 0xc2, //0x00005280 .quad -4446942528265218167
	0x64, 0x5a, 0xe5, 0x6b, 0x22, 0x21, 0x22, 0x80, //0x00005288 .quad -9213765455923815836
	0x6c, 0xbd, 0xb7, 0x10, 0xaa, 0x9b, 0xdb, 0xf2, //0x00005290 .quad -946992141904134804
	0xfd, 0xb0, 0xde, 0x06, 0x6b, 0xa9, 0x2a, 0xa0, //0x00005298 .quad -6905520801477381891
	0xc7, 0xac, 0xe5, 0x94, 0x94, 0x82, 0x92, 0x6f, //0x000052a0 .quad 8039631859474607303
	0x3d, 0x5d, 0x96, 0xc8, 0xc5, 0x53, 0x35, 0xc8, //0x000052a8 .quad -4020214983419339459
	0xf9, 0x17, 0x1f, 0xba, 0x39, 0x23, 0x77, 0xcb, //0x000052b0 .quad -3785518230938904583
	0x8c, 0xf4, 0xbb, 0x3a, 0xb7, 0xa8, 0x42, 0xfa, //0x000052b8 .quad -413582710846786420
	0xfb, 0x6e, 0x53, 0x14, 0x04, 0x76, 0x2a, 0xff, //0x000052c0 .quad -60105885123121413
	0xd7, 0x78, 0xb5, 0x84, 0x72, 0xa9, 0x69, 0x9c, //0x000052c8 .quad -7176018221920323369
	0xba, 0x4a, 0x68, 0x19, 0x85, 0x13, 0xf5, 0xfe, //0x000052d0 .quad -75132356403901766
	0x0d, 0xd7, 0xe2, 0x25, 0xcf, 0x13, 0x84, 0xc3, //0x000052d8 .quad -4358336758973016307
	0x69, 0x5d, 0xc2, 0x5f, 0x66, 0x58, 0xb2, 0x7e, //0x000052e0 .quad 9129456591349898601
	0xd1, 0x8c, 0x5b, 0xef, 0xc2, 0x18, 0x65, 0xf4, //0x000052e8 .quad -836234930288882479
	0x61, 0x7a, 0xd9, 0xfb, 0x3f, 0x77, 0x2f, 0xef, //0x000052f0 .quad -1211618658047395231
	0x02, 0x38, 0x99, 0xd5, 0x79, 0x2f, 0xbf, 0x98, //0x000052f8 .quad -7440175859071633406
	0xfa, 0xd8, 0xcf, 0xfa, 0x0f, 0x55, 0xfb, 0xaa, //0x00005300 .quad -6126209340986631942
	0x03, 0x86, 0xff, 0x4a, 0x58, 0xfb, 0xee, 0xbe, //0x00005308 .quad -4688533805412153853
	0x38, 0xcf, 0x83, 0xf9, 0x53, 0x2a, 0xba, 0x95, //0x00005310 .quad -7657761676233289928
	0x84, 0x67, 0xbf, 0x5d, 0x2e, 0xba, 0xaa, 0xee, //0x00005318 .quad -1248981238337804412
	0x83, 0x61, 0xf2, 0x7b, 0x74, 0x5a, 0x94, 0xdd, //0x00005320 .quad -2480258038432112253
	0xb2, 0xa0, 0x97, 0xfa, 0x5c, 0xb4, 0x2a, 0x95, //0x00005328 .quad -7698142301602209614
	0xe4, 0xf9, 0xee, 0x9a, 0x11, 0x71, 0xf9, 0x94, //0x00005330 .quad -7712008566467528220
	0xdf, 0x88, 0x3d, 0x39, 0x74, 0x61, 0x75, 0xba, //0x00005338 .quad -5010991858575374113
	0x5d, 0xb8, 0xaa, 0x01, 0x56, 0xcd, 0x37, 0x7a, //0x00005340 .quad 8806733365625141341
	0x17, 0xeb, 0x8c, 0x47, 0xd1, 0xb9, 0x12, 0xe9, //0x00005348 .quad -1652053804791829737
	0x3a, 0xb3, 0x0a, 0xc1, 0x55, 0xe0, 0x62, 0xac, //0x00005350 .quad -6025006692552756422
	0xee, 0x12, 0xb8, 0xcc, 0x22, 0xb4, 0xab, 0x91, //0x00005358 .quad -7950062655635975442
	0x09, 0x60, 0x4d, 0x31, 0x6b, 0x98, 0x7b, 0x57, //0x00005360 .quad 6303799689591218185
	0xaa, 0x17, 0xe6, 0x7f, 0x2b, 0xa1, 0x16, 0xb6, //0x00005368 .quad -5325892301117581398
	0x0b, 0xb8, 0xa0, 0xfd, 0x85, 0x7e, 0x5a, 0xed, //0x00005370 .quad -1343622424865753077
	0x94, 0x9d, 0xdf, 0x5f, 0x76, 0x49, 0x9c, 0xe3, //0x00005378 .quad -2045679357969588844
	0x07, 0x73, 0x84, 0xbe, 0x13, 0x8f, 0x58, 0x14, //0x00005380 .quad 1466078993672598279
	0x7d, 0xc2, 0xeb, 0xfb, 0xe9, 0xad, 0x41, 0x8e, //0x00005388 .quad -8196078626372074883
	0xc8, 0x8f, 0x25, 0xae, 0xd8, 0xb2, 0x6e, 0x59, //0x00005390 .quad 6444284760518135752
	0x1c, 0xb3, 0xe6, 0x7a, 0x64, 0x19, 0xd2, 0xb1, //0x00005398 .quad -5633412264537705700
	0xbb, 0xf3, 0xae, 0xd9, 0x8e, 0x5f, 0xca, 0x6f, //0x000053a0 .quad 8055355950647669691
	0xe3, 0x5f, 0xa0, 0x99, 0xbd, 0x9f, 0x46, 0xde, //0x000053a8 .quad -2430079312244744221
	0x54, 0x58, 0x0d, 0x48, 0xb9, 0x7b, 0xde, 0x25, //0x000053b0 .quad 2728754459941099604
	0xee, 0x3b, 0x04, 0x80, 0xd6, 0x23, 0xec, 0x8a, //0x000053b8 .quad -8436328597794046994
	0x6a, 0xae, 0x10, 0x9a, 0xa7, 0x1a, 0x56, 0xaf, //0x000053c0 .quad -5812428961928401302
	0xe9, 0x4a, 0x05, 0x20, 0xcc, 0x2c, 0xa7, 0xad, //0x000053c8 .quad -5933724728815170839
	0x04, 0xda, 0x94, 0x80, 0x51, 0xa1, 0x2b, 0x1b, //0x000053d0 .quad 1957835834444274180
	0xa4, 0x9d, 0x06, 0x28, 0xff, 0xf7, 0x10, 0xd9, //0x000053d8 .quad -2805469892591575644
	0x42, 0x08, 0x5d, 0xf0, 0xd2, 0x44, 0xfb, 0x90, //0x000053e0 .quad -7999724640327104446
	0x86, 0x22, 0x04, 0x79, 0xff, 0x9a, 0xaa, 0x87, //0x000053e8 .quad -8670947710510816634
	0x53, 0x4a, 0x74, 0xac, 0x07, 0x16, 0x3a, 0x35, //0x000053f0 .quad 3835402254873283155
	0x28, 0x2b, 0x45, 0x57, 0xbf, 0x41, 0x95, 0xa9, //0x000053f8 .quad -6226998619711132888
	0xe8, 0x5c, 0x91, 0x97, 0x89, 0x9b, 0x88, 0x42, //0x00005400 .quad 4794252818591603944
	0xf2, 0x75, 0x16, 0x2d, 0x2f, 0x92, 0xfa, 0xd3, //0x00005408 .quad -3172062256211528206
	0x11, 0xda, 0xba, 0xfe, 0x35, 0x61, 0x95, 0x69, //0x00005410 .quad 7608094030047140369
	0xb7, 0x09, 0x2e, 0x7c, 0x5d, 0x9b, 0x7c, 0x84, //0x00005418 .quad -8900067937773286985
	0x95, 0x90, 0x69, 0x7e, 0x83, 0xb9, 0xfa, 0x43, //0x00005420 .quad 4898431519131537557
	0x25, 0x8c, 0x39, 0xdb, 0x34, 0xc2, 0x9b, 0xa5, //0x00005428 .quad -6513398903789220827
	0xbb, 0xf4, 0x03, 0x5e, 0xe4, 0x67, 0xf9, 0x94, //0x00005430 .quad -7712018656367741765
	0x2e, 0xef, 0x07, 0x12, 0xc2, 0xb2, 0x02, 0xcf, //0x00005438 .quad -3530062611309138130
	0xf5, 0x78, 0xc2, 0xba, 0xee, 0xe0, 0x1b, 0x1d, //0x00005440 .quad 2097517367411243253
	0x7d, 0xf5, 0x44, 0x4b, 0xb9, 0xaf, 0x61, 0x81, //0x00005448 .quad -9123818159709293187
	0x32, 0x17, 0x73, 0x69, 0x2a, 0xd9, 0x62, 0x64, //0x00005450 .quad 7233582727691441970
	0xdc, 0x32, 0x16, 0x9e, 0xa7, 0x1b, 0xba, 0xa1, //0x00005458 .quad -6793086681209228580
	0xfe, 0xdc, 0xcf, 0x03, 0x75, 0x8f, 0x7b, 0x7d, //0x00005460 .quad 9041978409614302462
	0x93, 0xbf, 0x9b, 0x85, 0x91, 0xa2, 0x28, 0xca, //0x00005468 .quad -3879672333084147821
	0x3e, 0xd4, 0xc3, 0x44, 0x52, 0x73, 0xda, 0x5c, //0x00005470 .quad 6690786993590490174
	0x78, 0xaf, 0x02, 0xe7, 0x35, 0xcb, 0xb2, 0xfc, //0x00005478 .quad -237904397927796872
	0xa7, 0x64, 0xfa, 0x6a, 0x13, 0x88, 0x08, 0x3a, //0x00005480 .quad 4181741870994056359
	0xab, 0xad, 0x61, 0xb0, 0x01, 0xbf, 0xef, 0x9d, //0x00005488 .quad -7066219276345954901
	0xd0, 0xfd, 0xb8, 0x45, 0x18, 0xaa, 0x8a, 0x08, //0x00005490 .quad 615491320315182544
	0x16, 0x19, 0x7a, 0x1c, 0xc2, 0xae, 0x6b, 0xc5, //0x00005498 .quad -4221088077005055722
	0x45, 0x3d, 0x27, 0x57, 0x9e, 0x54, 0xad, 0x8a, //0x000054a0 .quad -8454007886460797627
	0x5b, 0x9f, 0x98, 0xa3, 0x72, 0x9a, 0xc6, 0xf6, //0x000054a8 .quad -664674077828931749
	0x4b, 0x86, 0x78, 0xf6, 0xe2, 0x54, 0xac, 0x36, //0x000054b0 .quad 3939617107816777291
	0x99, 0x63, 0x3f, 0xa6, 0x87, 0x20, 0x3c, 0x9a, //0x000054b8 .quad -7332950326284164199
	0xdd, 0xa7, 0x16, 0xb4, 0x1b, 0x6a, 0x57, 0x84, //0x000054c0 .quad -8910536670511192099
	0x7f, 0x3c, 0xcf, 0x8f, 0xa9, 0x28, 0xcb, 0xc0, //0x000054c8 .quad -4554501889427817345
	0xd5, 0x51, 0x1c, 0xa1, 0xa2, 0x44, 0x6d, 0x65, //0x000054d0 .quad 7308573235570561493
	0x9f, 0x0b, 0xc3, 0xf3, 0xd3, 0xf2, 0xfd, 0xf0, //0x000054d8 .quad -1081441343357383777
	0x25, 0xb3, 0xb1, 0xa4, 0xe5, 0x4a, 0x64, 0x9f, //0x000054e0 .quad -6961356773836868827
	0x43, 0xe7, 0x59, 0x78, 0xc4, 0xb7, 0x9e, 0x96, //0x000054e8 .quad -7593429867239446717
	0xee, 0x1f, 0xde, 0x0d, 0x9f, 0x5d, 0x3d, 0x87, //0x000054f0 .quad -8701695967296086034
	0x14, 0x61, 0x70, 0x96, 0xb5, 0x65, 0x46, 0xbc, //0x000054f8 .quad -4880101315621920492
	0xea, 0xa7, 0x55, 0xd1, 0x06, 0xb5, 0x0c, 0xa9, //0x00005500 .quad -6265433940692719638
	0x59, 0x79, 0x0c, 0xfc, 0x22, 0xff, 0x57, 0xeb, //0x00005508 .quad -1488440626100012711
	0xf2, 0x88, 0xd5, 0x42, 0x24, 0xf1, 0xa7, 0x09, //0x00005510 .quad 695789805494438130
	0xd8, 0xcb, 0x87, 0xdd, 0x75, 0xff, 0x16, 0x93, //0x00005518 .quad -7847804418953589800
	0x2f, 0xeb, 0x8a, 0x53, 0x6d, 0xed, 0x11, 0x0c, //0x00005520 .quad 869737256868047663
	0xce, 0xbe, 0xe9, 0x54, 0x53, 0xbf, 0xdc, 0xb7, //0x00005528 .quad -5198069505264599346
	0xfa, 0xa5, 0x6d, 0xa8, 0xc8, 0x68, 0x16, 0x8f, //0x00005530 .quad -8136200465769716230
	0x81, 0x2e, 0x24, 0x2a, 0x28, 0xef, 0xd3, 0xe5, //0x00005538 .quad -1885900863153361279
	0xbc, 0x87, 0x44, 0x69, 0x7d, 0x01, 0x6e, 0xf9, //0x00005540 .quad -473439272678684740
	0x10, 0x9d, 0x56, 0x1a, 0x79, 0x75, 0xa4, 0x8f, //0x00005548 .quad -8096217067111932656
	0xac, 0xa9, 0x95, 0xc3, 0xdc, 0x81, 0xc9, 0x37, //0x00005550 .quad 4019886927579031980
	0x55, 0x44, 0xec, 0x60, 0xd7, 0x92, 0x8d, 0xb3, //0x00005558 .quad -5508585315462527915
	0x17, 0x14, 0x7b, 0xf4, 0x53, 0xe2, 0xbb, 0x85, //0x00005560 .quad -8810199395808373737
	0x6a, 0x55, 0x27, 0x39, 0x8d, 0xf7, 0x70, 0xe0, //0x00005568 .quad -2274045625900771990
	0x8e, 0xec, 0xcc, 0x78, 0x74, 0x6d, 0x95, 0x93, //0x00005570 .quad -7812217631593927538
	0x62, 0x95, 0xb8, 0x43, 0xb8, 0x9a, 0x46, 0x8c, //0x00005578 .quad -8338807543829064350
	0xb2, 0x27, 0x00, 0x97, 0xd1, 0xc8, 0x7a, 0x38, //0x00005580 .quad 4069786015789754290
	0xbb, 0xba, 0xa6, 0x54, 0x66, 0x41, 0x58, 0xaf, //0x00005588 .quad -5811823411358942533
	0x9e, 0x31, 0xc0, 0xfc, 0x05, 0x7b, 0x99, 0x06, //0x00005590 .quad 475546501309804958
	0x6a, 0x69, 0xd0, 0xe9, 0xbf, 0x51, 0x2e, 0xdb, //0x00005598 .quad -2653093245771290262
	0x03, 0x1f, 0xf8, 0xbd, 0xe3, 0xec, 0x1f, 0x44, //0x000055a0 .quad 4908902581746016003
	0xe2, 0x41, 0x22, 0xf2, 0x17, 0xf3, 0xfc, 0x88, //0x000055a8 .quad -8575712306248138270
	0xc3, 0x26, 0x76, 0xad, 0x1c, 0xe8, 0x27, 0xd5, //0x000055b0 .quad -3087243809672255805
	0x5a, 0xd2, 0xaa, 0xee, 0xdd, 0x2f, 0x3c, 0xab, //0x000055b8 .quad -6107954364382784934
	0x74, 0xb0, 0xd3, 0xd8, 0x23, 0xe2, 0x71, 0x8a, //0x000055c0 .quad -8470740780517707660
	0xf1, 0x86, 0x55, 0x6a, 0xd5, 0x3b, 0x0b, 0xd6, //0x000055c8 .quad -3023256937051093263
	0x49, 0x4e, 0x84, 0x67, 0x56, 0x2d, 0x87, 0xf6, //0x000055d0 .quad -682526969396179383
	0x56, 0x74, 0x75, 0x62, 0x65, 0x05, 0xc7, 0x85, //0x000055d8 .quad -8807064613298015146
	0xdb, 0x61, 0x65, 0x01, 0xac, 0xf8, 0x28, 0xb4, //0x000055e0 .quad -5464844730172612133
	0x6c, 0xd1, 0x12, 0xbb, 0xbe, 0xc6, 0x38, 0xa7, //0x000055e8 .quad -6397144748195131028
	0x52, 0xba, 0xbe, 0x01, 0xd7, 0x36, 0x33, 0xe1, //0x000055f0 .quad -2219369894288377262
	0xc7, 0x85, 0xd7, 0x69, 0x6e, 0xf8, 0x06, 0xd1, //0x000055f8 .quad -3384744916816525881
	0x73, 0x34, 0x17, 0x61, 0x46, 0x02, 0xc0, 0xec, //0x00005600 .quad -1387106183930235789
	0x9c, 0xb3, 0x26, 0x02, 0x45, 0x5b, 0xa4, 0x82, //0x00005608 .quad -9032994600651410532
	0x90, 0x01, 0x5d, 0xf9, 0xd7, 0x02, 0xf0, 0x27, //0x00005610 .quad 2877803288514593168
	0x84, 0x60, 0xb0, 0x42, 0x16, 0x72, 0x4d, 0xa3, //0x00005618 .quad -6679557232386875260
	0xf4, 0x41, 0xb4, 0xf7, 0x8d, 0x03, 0xec, 0x31, //0x00005620 .quad 3597254110643241460
	0xa5, 0x78, 0x5c, 0xd3, 0x9b, 0xce, 0x20, 0xcc, //0x00005628 .quad -3737760522056206171
	0x71, 0x52, 0xa1, 0x75, 0x71, 0x04, 0x67, 0x7e, //0x00005630 .quad 9108253656731439729
	0xce, 0x96, 0x33, 0xc8, 0x42, 0x02, 0x29, 0xff, //0x00005638 .quad -60514634142869810
	0x86, 0xd3, 0x84, 0xe9, 0xc6, 0x62, 0x00, 0x0f, //0x00005640 .quad 1080972517029761926
	0x41, 0x3e, 0x20, 0xbd, 0x69, 0xa1, 0x79, 0x9f, //0x00005648 .quad -6955350673980375487
	0x68, 0x08, 0xe6, 0xa3, 0x78, 0x7b, 0xc0, 0x52, //0x00005650 .quad 5962901664714590312
	0xd1, 0x4d, 0x68, 0x2c, 0xc4, 0x09, 0x58, 0xc7, //0x00005658 .quad -4082502324048081455
	0x82, 0x8a, 0xdf, 0xcc, 0x56, 0x9a, 0x70, 0xa7, //0x00005660 .quad -6381430974388925822
	0x45, 0x61, 0x82, 0x37, 0x35, 0x0c, 0x2e, 0xf9, //0x00005668 .quad -491441886632713915
	0x91, 0xb6, 0x0b, 0x40, 0x76, 0x60, 0xa6, 0x88, //0x00005670 .quad -8600080377420466543
	0xcb, 0x7c, 0xb1, 0x42, 0xa1, 0xc7, 0xbc, 0x9b, //0x00005678 .quad -7224680206786528053
	0x35, 0xa4, 0x0e, 0xd0, 0x93, 0xf8, 0xcf, 0x6a, //0x00005680 .quad 7696643601933968437
	0xfe, 0xdb, 0x5d, 0x93, 0x89, 0xf9, 0xab, 0xc2, //0x00005688 .quad -4419164240055772162
	0x43, 0x4d, 0x12, 0xc4, 0xb8, 0xf6, 0x83, 0x05, //0x00005690 .quad 397432465562684739
	0xfe, 0x52, 0x35, 0xf8, 0xeb, 0xf7, 0x56, 0xf3, //0x00005698 .quad -912269281642327298
	0x4a, 0x70, 0x8b, 0x7a, 0x33, 0x7a, 0x72, 0xc3, //0x000056a0 .quad -4363290727450709942
	0xde, 0x53, 0x21, 0x7b, 0xf3, 0x5a, 0x16, 0x98, //0x000056a8 .quad -7487697328667536418
	0x5c, 0x4c, 0x2e, 0x59, 0xc0, 0x18, 0x4f, 0x74, //0x000056b0 .quad 8380944645968776284
	0xd6, 0xa8, 0xe9, 0x59, 0xb0, 0xf1, 0x1b, 0xbe, //0x000056b8 .quad -4747935642407032618
	0x73, 0xdf, 0x79, 0x6f, 0xf0, 0xde, 0x62, 0x11, //0x000056c0 .quad 1252808770606194547
	0x0c, 0x13, 0x64, 0x70, 0x1c, 0xee, 0xa2, 0xed, //0x000056c8 .quad -1323233534581402868
	0xa8, 0x2b, 0xac, 0x45, 0x56, 0xcb, 0xdd, 0x8a, //0x000056d0 .quad -8440366555225904216
	0xe7, 0x8b, 0x3e, 0xc6, 0xd1, 0xd4, 0x85, 0x94, //0x000056d8 .quad -7744549986754458649
	0x92, 0x36, 0x17, 0xd7, 0x2b, 0x3e, 0x95, 0x6d, //0x000056e0 .quad 7896285879677171346
	0xe1, 0x2e, 0xce, 0x37, 0x06, 0x4a, 0xa7, 0xb9, //0x000056e8 .quad -5069001465015685407
	0x37, 0x04, 0xdd, 0xcc, 0xb6, 0x8d, 0xfa, 0xc8, //0x000056f0 .quad -3964700705685699529
	0x99, 0xba, 0xc1, 0xc5, 0x87, 0x1c, 0x11, 0xe8, //0x000056f8 .quad -1724565812842218855
	0xa2, 0x22, 0x0a, 0x40, 0x92, 0x98, 0x9c, 0x1d, //0x00005700 .quad 2133748077373825698
	0xa0, 0x14, 0x99, 0xdb, 0xd4, 0xb1, 0x0a, 0x91, //0x00005708 .quad -7995382660667468640
	0x4b, 0xab, 0x0c, 0xd0, 0xb6, 0xbe, 0x03, 0x25, //0x00005710 .quad 2667185096717282123
	0xc8, 0x59, 0x7f, 0x12, 0x4a, 0x5e, 0x4d, 0xb5, //0x00005718 .quad -5382542307406947896
	0x1d, 0xd6, 0x0f, 0x84, 0x64, 0xae, 0x44, 0x2e, //0x00005720 .quad 3333981370896602653
	0x3a, 0x30, 0x1f, 0x97, 0xdc, 0xb5, 0xa0, 0xe2, //0x00005728 .quad -2116491865831296966
	0xd2, 0xe5, 0x89, 0xd2, 0xfe, 0xec, 0xea, 0x5c, //0x00005730 .quad 6695424375237764562
	0x24, 0x7e, 0x73, 0xde, 0xa9, 0x71, 0xa4, 0x8d, //0x00005738 .quad -8240336443785642460
	0x47, 0x5f, 0x2c, 0x87, 0x3e, 0xa8, 0x25, 0x74, //0x00005740 .quad 8369280469047205703
	0xad, 0x5d, 0x10, 0x56, 0x14, 0x8e, 0x0d, 0xb1, //0x00005748 .quad -5688734536304665171
	0x19, 0x77, 0xf7, 0x28, 0x4e, 0x12, 0x2f, 0xd1, //0x00005750 .quad -3373457468973156583
	0x18, 0x75, 0x94, 0x6b, 0x99, 0xf1, 0x50, 0xdd, //0x00005758 .quad -2499232151953443560
	0x6f, 0xaa, 0x9a, 0xd9, 0x70, 0x6b, 0xbd, 0x82, //0x00005760 .quad -9025939945749304721
	0x2f, 0xc9, 0x3c, 0xe3, 0xff, 0x96, 0x52, 0x8a, //0x00005768 .quad -8479549122611984081
	0x0b, 0x55, 0x01, 0x10, 0x4d, 0xc6, 0x6c, 0x63, //0x00005770 .quad 7164319141522920715
	0x7b, 0xfb, 0x0b, 0xdc, 0xbf, 0x3c, 0xe7, 0xac, //0x00005778 .quad -5987750384837592197
	0x4e, 0xaa, 0x01, 0x54, 0xe0, 0xf7, 0x47, 0x3c, //0x00005780 .quad 4343712908476262990
	0x5a, 0xfa, 0x0e, 0xd3, 0xef, 0x0b, 0x21, 0xd8, //0x00005788 .quad -2873001962619602342
	0x71, 0x0a, 0x81, 0x34, 0xec, 0xfa, 0xac, 0x65, //0x00005790 .quad 7326506586225052273
	0x78, 0x5c, 0xe9, 0xe3, 0x75, 0xa7, 0x14, 0x87, //0x00005798 .quad -8713155254278333320
	0x0d, 0x4d, 0xa1, 0x41, 0xa7, 0x39, 0x18, 0x7f, //0x000057a0 .quad 9158133232781315341
	0x96, 0xb3, 0xe3, 0x5c, 0x53, 0xd1, 0xd9, 0xa8, //0x000057a8 .quad -6279758049420528746
	0x50, 0xa0, 0x09, 0x12, 0x11, 0x48, 0xde, 0x1e, //0x000057b0 .quad 2224294504121868368
	0x7c, 0xa0, 0x1c, 0x34, 0xa8, 0x45, 0x10, 0xd3, //0x000057b8 .quad -3238011543348273028
	0x32, 0x04, 0x46, 0xab, 0x0a, 0xed, 0x4a, 0x93, //0x000057c0 .quad -7833187971778608078
	0x4d, 0xe4, 0x91, 0x20, 0x89, 0x2b, 0xea, 0x83, //0x000057c8 .quad -8941286242233752499
	0x3f, 0x85, 0x17, 0x56, 0x4d, 0xa8, 0x1d, 0xf8, //0x000057d0 .quad -568112927868484289
	0x60, 0x5d, 0xb6, 0x68, 0x6b, 0xb6, 0xe4, 0xa4, //0x000057d8 .quad -6564921784364802720
	0x8e, 0x66, 0x9d, 0xab, 0x60, 0x12, 0x25, 0x36, //0x000057e0 .quad 3901544858591782542
	0xb9, 0xf4, 0xe3, 0x42, 0x06, 0xe4, 0x1d, 0xce, //0x000057e8 .quad -3594466212028615495
	0x19, 0x60, 0x42, 0x6b, 0x7c, 0x2b, 0xd7, 0xc1, //0x000057f0 .quad -4479063491021217767
	0xf3, 0x78, 0xce, 0xe9, 0x83, 0xae, 0xd2, 0x80, //0x000057f8 .quad -9164070410158966541
	0x1f, 0xf8, 0x12, 0x86, 0x5b, 0xf6, 0x4c, 0xb2, //0x00005800 .quad -5598829363776522209
	0x30, 0x17, 0x42, 0xe4, 0x24, 0x5a, 0x07, 0xa1, //0x00005808 .quad -6843401994271320272
	0x27, 0xb6, 0x97, 0x67, 0xf2, 0x33, 0xe0, 0xde, //0x00005810 .quad -2386850686293264857
	0xfc, 0x9c, 0x52, 0x1d, 0xae, 0x30, 0x49, 0xc9, //0x00005818 .quad -3942566474411762436
	0xb1, 0xa3, 0x7d, 0x01, 0xef, 0x40, 0x98, 0x16, //0x00005820 .quad 1628122660560806833
	0x3c, 0x44, 0xa7, 0xa4, 0xd9, 0x7c, 0x9b, 0xfb, //0x00005828 .quad -316522074587315140
	0x4e, 0x86, 0xee, 0x60, 0x95, 0x28, 0x1f, 0x8e, //0x00005830 .quad -8205795374004271538
	0xa5, 0x8a, 0xe8, 0x06, 0x08, 0x2e, 0x41, 0x9d, //0x00005838 .quad -7115355324258153819
	0xe2, 0x27, 0x2a, 0xb9, 0xba, 0xf2, 0xa6, 0xf1, //0x00005840 .quad -1033872180650563614
	0x4e, 0xad, 0xa2, 0x08, 0x8a, 0x79, 0x91, 0xc4, //0x00005848 .quad -4282508136895304370
	0xdb, 0xb1, 0x74, 0x67, 0x69, 0xaf, 0x10, 0xae, //0x00005850 .quad -5904026244240592421
	0xa2, 0x58, 0xcb, 0x8a, 0xec, 0xd7, 0xb5, 0xf5, //0x00005858 .quad -741449152691742558
	0x29, 0xef, 0xa8, 0xe0, 0xa1, 0x6d, 0xca, 0xac, //0x00005860 .quad -5995859411864064215
	0x65, 0x17, 0xbf, 0xd6, 0xf3, 0xa6, 0x91, 0x99, //0x00005868 .quad -7380934748073420955
	0xf3, 0x2a, 0xd3, 0x58, 0x0a, 0x09, 0xfd, 0x17, //0x00005870 .quad 1728547772024695539
	0x3f, 0xdd, 0x6e, 0xcc, 0xb0, 0x10, 0xf6, 0xbf, //0x00005878 .quad -4614482416664388289
	0xb0, 0xf5, 0x07, 0xef, 0x4c, 0x4b, 0xfc, 0xdd, //0x00005880 .quad -2451001303396518480
	0x8e, 0x94, 0x8a, 0xff, 0xdc, 0x94, 0xf3, 0xef, //0x00005888 .quad -1156417002403097458
	0x8e, 0xf9, 0x64, 0x15, 0x10, 0xaf, 0xbd, 0x4a, //0x00005890 .quad 5385653213018257806
	0xd9, 0x9c, 0xb6, 0x1f, 0x0a, 0x3d, 0xf8, 0x95, //0x00005898 .quad -7640289654143017767
	0xf1, 0x37, 0xbe, 0x1a, 0xd4, 0x1a, 0x6d, 0x9d, //0x000058a0 .quad -7102991539009341455
	0x0f, 0x44, 0xa4, 0xa7, 0x4c, 0x4c, 0x76, 0xbb, //0x000058a8 .quad -4938676049251384305
	0xed, 0xc5, 0x6d, 0x21, 0x89, 0x61, 0xc8, 0x84, //0x000058b0 .quad -8878739423761676819
	0x13, 0x55, 0x8d, 0xd1, 0x5f, 0xdf, 0x53, 0xea, //0x000058b8 .quad -1561659043136842477
	0xb4, 0x9b, 0xe4, 0xb4, 0xf5, 0x3c, 0xfd, 0x32, //0x000058c0 .quad 3674159897003727796
	0x2c, 0x55, 0xf8, 0xe2, 0x9b, 0x6b, 0x74, 0x92, //0x000058c8 .quad -7893565929601608404
	0xa1, 0xc2, 0x1d, 0x22, 0x33, 0x8c, 0xbc, 0x3f, //0x000058d0 .quad 4592699871254659745
	0x77, 0x6a, 0xb6, 0xdb, 0x82, 0x86, 0x11, 0xb7, //0x000058d8 .quad -5255271393574622601
	0x4a, 0x33, 0xa5, 0xea, 0x3f, 0xaf, 0xab, 0x0f, //0x000058e0 .quad 1129188820640936778
	0x15, 0x05, 0xa4, 0x92, 0x23, 0xe8, 0xd5, 0xe4, //0x000058e8 .quad -1957403223540890347
	0x0e, 0x40, 0xa7, 0xf2, 0x87, 0x4d, 0xcb, 0x29, //0x000058f0 .quad 3011586022114279438
	0x2d, 0x83, 0xa6, 0x3b, 0x16, 0xb1, 0x05, 0x8f, //0x000058f8 .quad -8140906042354138323
	0x12, 0x10, 0x51, 0xef, 0xe9, 0x20, 0x3e, 0x74, //0x00005900 .quad 8376168546070237202
	0xf8, 0x23, 0x90, 0xca, 0x5b, 0x1d, 0xc7, 0xb2, //0x00005908 .quad -5564446534515285000
	0x16, 0x54, 0x25, 0x6b, 0x24, 0xa9, 0x4d, 0x91, //0x00005910 .quad -7976533391121755114
	0xf6, 0x2c, 0x34, 0xbd, 0xb2, 0xe4, 0x78, 0xdf, //0x00005918 .quad -2343872149716718346
	0x8e, 0x54, 0xf7, 0xc2, 0xb6, 0x89, 0xd0, 0x1a, //0x00005920 .quad 1932195658189984910
	0x1a, 0x9c, 0x40, 0xb6, 0xef, 0x8e, 0xab, 0x8b, //0x00005928 .quad -8382449121214030822
	0xb1, 0x29, 0xb5, 0x73, 0x24, 0xac, 0x84, 0xa1, //0x00005930 .quad -6808127464117294671
	0x20, 0xc3, 0xd0, 0xa3, 0xab, 0x72, 0x96, 0xae, //0x00005938 .quad -5866375383090150624
	0x1e, 0x74, 0xa2, 0x90, 0x2d, 0xd7, 0xe5, 0xc9, //0x00005940 .quad -3898473311719230434
	0xe8, 0xf3, 0xc4, 0x8c, 0x56, 0x0f, 0x3c, 0xda, //0x00005948 .quad -2721283210435300376
	0x92, 0x88, 0x65, 0x7a, 0x7c, 0xa6, 0x2f, 0x7e, //0x00005950 .quad 9092669226243950738
	0x71, 0x18, 0xfb, 0x17, 0x96, 0x89, 0x65, 0x88, //0x00005958 .quad -8618331034163144591
	0xb7, 0xea, 0xfe, 0x98, 0x1b, 0x90, 0xbb, 0xdd, //0x00005960 .quad -2469221522477225289
	0x8d, 0xde, 0xf9, 0x9d, 0xfb, 0xeb, 0x7e, 0xaa, //0x00005968 .quad -6161227774276542835
	0x65, 0xa5, 0x3e, 0x7f, 0x22, 0x74, 0x2a, 0x55, //0x00005970 .quad 6136845133758244197
	0x31, 0x56, 0x78, 0x85, 0xfa, 0xa6, 0x1e, 0xd5, //0x00005978 .quad -3089848699418290639
	0x5f, 0x27, 0x87, 0x8f, 0x95, 0x88, 0x3a, 0xd5, //0x00005980 .quad -3082000819042179233
	0xde, 0x35, 0x6b, 0x93, 0x5c, 0x28, 0x33, 0x85, //0x00005988 .quad -8848684464777513506
	0x37, 0xf1, 0x68, 0xf3, 0xba, 0x2a, 0x89, 0x8a, //0x00005990 .quad -8464187042230111945
	0x56, 0x03, 0x46, 0xb8, 0x73, 0xf2, 0x7f, 0xa6, //0x00005998 .quad -6449169562544503978
	0x85, 0x2d, 0x43, 0xb0, 0x69, 0x75, 0x2b, 0x2d, //0x000059a0 .quad 3254824252494523781
	0x2c, 0x84, 0x57, 0xa6, 0x10, 0xef, 0x1f, 0xd0, //0x000059a8 .quad -3449775934753242068
	0x73, 0xfc, 0x29, 0x0e, 0x62, 0x29, 0x3b, 0x9c, //0x000059b0 .quad -7189106879045698445
	0x9b, 0xb2, 0xf6, 0x67, 0x6a, 0xf5, 0x13, 0x82, //0x000059b8 .quad -9073638986861858149
	0x8f, 0x7b, 0xb4, 0x91, 0xba, 0xf3, 0x49, 0x83, //0x000059c0 .quad -8986383598807123057
	0x42, 0x5f, 0xf4, 0x01, 0xc5, 0xf2, 0x98, 0xa2, //0x000059c8 .quad -6730362715149934782
	0x73, 0x9a, 0x21, 0x36, 0xa9, 0x70, 0x1c, 0x24, //0x000059d0 .quad 2602078556773259891
	0x13, 0x77, 0x71, 0x42, 0x76, 0x2f, 0x3f, 0xcb, //0x000059d8 .quad -3801267375510030573
	0x10, 0x01, 0xaa, 0x83, 0xd3, 0x8c, 0x23, 0xed, //0x000059e0 .quad -1359087822460813040
	0xd7, 0xd4, 0x0d, 0xd3, 0x53, 0xfb, 0x0e, 0xfe, //0x000059e8 .quad -139898200960150313
	0xaa, 0x40, 0x4a, 0x32, 0x04, 0x38, 0x36, 0xf4, //0x000059f0 .quad -849429889038008150
	0x06, 0xa5, 0xe8, 0x63, 0x14, 0x5d, 0xc9, 0x9e, //0x000059f8 .quad -7004965403241175802
	0xd5, 0xd0, 0xdc, 0x3e, 0x05, 0xc6, 0x43, 0xb1, //0x00005a00 .quad -5673473379724898091
	0x48, 0xce, 0xe2, 0x7c, 0x59, 0xb4, 0x7b, 0xc6, //0x00005a08 .quad -4144520735624081848
	0x0a, 0x05, 0x94, 0x8e, 0x86, 0xb7, 0x94, 0xdd, //0x00005a10 .quad -2480155706228734710
	0xda, 0x81, 0x1b, 0xdc, 0x6f, 0xa1, 0x1a, 0xf8, //0x00005a18 .quad -568964901102714406
	0x26, 0x83, 0x1c, 0x19, 0xb4, 0xf2, 0x7c, 0xca, //0x00005a20 .quad -3855940325606653146
	0x28, 0x31, 0x91, 0xe9, 0xe5, 0xa4, 0x10, 0x9b, //0x00005a28 .quad -7273132090830278360
	0xf0, 0xa3, 0x63, 0x1f, 0x61, 0x2f, 0x1c, 0xfd, //0x00005a30 .quad -208239388580928528
	0x72, 0x7d, 0xf5, 0x63, 0x1f, 0xce, 0xd4, 0xc1, //0x00005a38 .quad -4479729095110460046
	0xec, 0x8c, 0x3c, 0x67, 0x39, 0x3b, 0x63, 0xbc, //0x00005a40 .quad -4871985254153548564
	0xcf, 0xdc, 0xf2, 0x3c, 0xa7, 0x01, 0x4a, 0xf2, //0x00005a48 .quad -987975350460687153
	0x13, 0xd8, 0x85, 0xe0, 0x03, 0x05, 0xbe, 0xd5, //0x00005a50 .quad -3044990783845967853
	0x01, 0xca, 0x17, 0x86, 0x08, 0x41, 0x6e, 0x97, //0x00005a58 .quad -7535013621679011327
	0x18, 0x4e, 0xa7, 0xd8, 0x44, 0x86, 0x2d, 0x4b, //0x00005a60 .quad 5417133557047315992
	0x82, 0xbc, 0x9d, 0xa7, 0x4a, 0xd1, 0x49, 0xbd, //0x00005a68 .quad -4807081008671376254
	0x9e, 0x21, 0xd1, 0x0e, 0xd6, 0xe7, 0xf8, 0xdd, //0x00005a70 .quad -2451955090545630818
	0xa2, 0x2b, 0x85, 0x51, 0x9d, 0x45, 0x9c, 0xec, //0x00005a78 .quad -1397165242411832414
	0x03, 0xb5, 0x42, 0xc9, 0xe5, 0x90, 0xbb, 0xca, //0x00005a80 .quad -3838314940804713213
	0x45, 0x3b, 0xf3, 0x52, 0x82, 0xab, 0xe1, 0x93, //0x00005a88 .quad -7790757304148477115
	0x43, 0x62, 0x93, 0x3b, 0x1f, 0x75, 0x6a, 0x3d, //0x00005a90 .quad 4425478360848884291
	0x17, 0x0a, 0xb0, 0xe7, 0x62, 0x16, 0xda, 0xb8, //0x00005a98 .quad -5126760611758208489
	0xd4, 0x3a, 0x78, 0x0a, 0x67, 0x12, 0xc5, 0x0c, //0x00005aa0 .quad 920161932633717460
	0x9d, 0x0c, 0x9c, 0xa1, 0xfb, 0x9b, 0x10, 0xe7, //0x00005aa8 .quad -1796764746270372707
	0xc5, 0x24, 0x8b, 0x66, 0x80, 0x2b, 0xfb, 0x27, //0x00005ab0 .quad 2880944217109767365
	0xe2, 0x87, 0x01, 0x45, 0x7d, 0x61, 0x6a, 0x90, //0x00005ab8 .quad -8040506994060064798
	0xf6, 0xed, 0x2d, 0x80, 0x60, 0xf6, 0xf9, 0xb1, //0x00005ac0 .quad -5622191765467566602
	0xda, 0xe9, 0x41, 0x96, 0xdc, 0xf9, 0x84, 0xb4, //0x00005ac8 .quad -5438947724147693094
	0x73, 0x69, 0x39, 0xa0, 0xf8, 0x73, 0x78, 0x5e, //0x00005ad0 .quad 6807318348447705459
	0x51, 0x64, 0xd2, 0xbb, 0x53, 0x38, 0xa6, 0xe1, //0x00005ad8 .quad -2186998636757228463
	0xe8, 0xe1, 0x23, 0x64, 0x7b, 0x48, 0x0b, 0xdb, //0x00005ae0 .quad -2662955059861265944
	0xb2, 0x7e, 0x63, 0x55, 0x34, 0xe3, 0x07, 0x8d, //0x00005ae8 .quad -8284403175614349646
	0x62, 0xda, 0x2c, 0x3d, 0x9a, 0x1a, 0xce, 0x91, //0x00005af0 .quad -7940379843253970334
	0x5f, 0x5e, 0xbc, 0x6a, 0x01, 0xdc, 0x49, 0xb0, //0x00005af8 .quad -5743817951090549153
	0xfb, 0x10, 0x78, 0xcc, 0x40, 0xa1, 0x41, 0x76, //0x00005b00 .quad 8521269269642088699
	0xf7, 0x75, 0x6b, 0xc5, 0x01, 0x53, 0x5c, 0xdc, //0x00005b08 .quad -2568086420435798537
	0x9d, 0x0a, 0xcb, 0x7f, 0xc8, 0x04, 0xe9, 0xa9, //0x00005b10 .quad -6203421752542164323
	0xba, 0x29, 0x63, 0x1b, 0xe1, 0xb3, 0xb9, 0x89, //0x00005b18 .quad -8522583040413455942
	0x44, 0xcd, 0xbd, 0x9f, 0xfa, 0x45, 0x63, 0x54, //0x00005b20 .quad 6080780864604458308
	0x29, 0xf4, 0x3b, 0x62, 0xd9, 0x20, 0x28, 0xac, //0x00005b28 .quad -6041542782089432023
	0x95, 0x40, 0xad, 0x47, 0x79, 0x17, 0x7c, 0xa9, //0x00005b30 .quad -6234081974526590827
	0x33, 0xf1, 0xca, 0xba, 0x0f, 0x29, 0x32, 0xd7, //0x00005b38 .quad -2940242459184402125
	0x5d, 0x48, 0xcc, 0xcc, 0xab, 0x8e, 0xed, 0x49, //0x00005b40 .quad 5327070802775656541
	0xc0, 0xd6, 0xbe, 0xd4, 0xa9, 0x59, 0x7f, 0x86, //0x00005b48 .quad -8755180564631333184
	0x74, 0x5a, 0xff, 0xbf, 0x56, 0xf2, 0x68, 0x5c, //0x00005b50 .quad 6658838503469570676
	0x70, 0x8c, 0xee, 0x49, 0x14, 0x30, 0x1f, 0xa8, //0x00005b58 .quad -6332289687361778576
	0x11, 0x31, 0xff, 0x6f, 0xec, 0x2e, 0x83, 0x73, //0x00005b60 .quad 8323548129336963345
	0x8c, 0x2f, 0x6a, 0x5c, 0x19, 0xfc, 0x26, 0xd2, //0x00005b68 .quad -3303676090774835316
	0xab, 0x7e, 0xff, 0xc5, 0x53, 0xfd, 0x31, 0xc8, //0x00005b70 .quad -4021154456019173717
	0xb7, 0x5d, 0xc2, 0xd9, 0x8f, 0x5d, 0x58, 0x83, //0x00005b78 .quad -8982326584375353929
	0x55, 0x5e, 0x7f, 0xb7, 0xa8, 0x7c, 0x3e, 0xba, //0x00005b80 .quad -5026443070023967147
	0x25, 0xf5, 0x32, 0xd0, 0xf3, 0x74, 0x2e, 0xa4, //0x00005b88 .quad -6616222212041804507
	0xeb, 0x35, 0x5f, 0xe5, 0xd2, 0x1b, 0xce, 0x28, //0x00005b90 .quad 2940318199324816875
	0x6f, 0xb2, 0x3f, 0xc4, 0x30, 0x12, 0x3a, 0xcd, //0x00005b98 .quad -3658591746624867729
	0xb3, 0x81, 0x5b, 0xcf, 0x63, 0xd1, 0x80, 0x79, //0x00005ba0 .quad 8755227902219092403
	0x85, 0xcf, 0xa7, 0x7a, 0x5e, 0x4b, 0x44, 0x80, //0x00005ba8 .quad -9204148869281624187
	0x1f, 0x62, 0x32, 0xc3, 0xbc, 0x05, 0xe1, 0xd7, //0x00005bb0 .quad -2891023177508298209
	0x66, 0xc3, 0x51, 0x19, 0x36, 0x5e, 0x55, 0xa0, //0x00005bb8 .quad -6893500068174642330
	0xa7, 0xfa, 0xfe, 0xf3, 0x2b, 0x47, 0xd9, 0x8d, //0x00005bc0 .quad -8225464990312760665
	0x40, 0x34, 0xa6, 0x9f, 0xc3, 0xb5, 0x6a, 0xc8, //0x00005bc8 .quad -4005189066790915008
	0x51, 0xb9, 0xfe, 0xf0, 0xf6, 0x98, 0x4f, 0xb1, //0x00005bd0 .quad -5670145219463562927
	0x50, 0xc1, 0x8f, 0x87, 0x34, 0x63, 0x85, 0xfa, //0x00005bd8 .quad -394800315061255856
	0xd3, 0x33, 0x9f, 0x56, 0x9a, 0xbf, 0xd1, 0x6e, //0x00005be0 .quad 7985374283903742931
	0xd2, 0xd8, 0xb9, 0xd4, 0x00, 0x5e, 0x93, 0x9c, //0x00005be8 .quad -7164279224554366766
	0xc8, 0x00, 0x47, 0xec, 0x80, 0x2f, 0x86, 0x0a, //0x00005bf0 .quad 758345818024902856
	0x07, 0x4f, 0xe8, 0x09, 0x81, 0x35, 0xb8, 0xc3, //0x00005bf8 .quad -4343663012265570553
	0xfa, 0xc0, 0x58, 0x27, 0x61, 0xbb, 0x27, 0xcd, //0x00005c00 .quad -3663753745896259334
	0xc8, 0x62, 0x62, 0x4c, 0xe1, 0x42, 0xa6, 0xf4, //0x00005c08 .quad -817892746904575288
	0x9c, 0x78, 0x97, 0xb8, 0x1c, 0xd5, 0x38, 0x80, //0x00005c10 .quad -9207375118826243940
	0xbd, 0x7d, 0xbd, 0xcf, 0xcc, 0xe9, 0xe7, 0x98, //0x00005c18 .quad -7428711994456441411
	0xc3, 0x56, 0xbd, 0xe6, 0x63, 0x0a, 0x47, 0xe0, //0x00005c20 .quad -2285846861678029117
	0x2c, 0xdd, 0xac, 0x03, 0x40, 0xe4, 0x21, 0xbf, //0x00005c28 .quad -4674203974643163860
	0x74, 0xac, 0x6c, 0xe0, 0xfc, 0xcc, 0x58, 0x18, //0x00005c30 .quad 1754377441329851508
	0x78, 0x14, 0x98, 0x04, 0x50, 0x5d, 0xea, 0xee, //0x00005c38 .quad -1231068949876566920
	0xc8, 0xeb, 0x43, 0x0c, 0x1e, 0x80, 0x37, 0x0f, //0x00005c40 .quad 1096485900831157192
	0xcb, 0x0c, 0xdf, 0x02, 0x52, 0x7a, 0x52, 0x95, //0x00005c48 .quad -7686947121313936181
	0xba, 0xe6, 0x54, 0x8f, 0x25, 0x60, 0x05, 0xd3, //0x00005c50 .quad -3241078642388441414
	0xfd, 0xcf, 0x96, 0x83, 0xe6, 0x18, 0xa7, 0xba, //0x00005c58 .quad -4996997883215032323
	0x69, 0x20, 0x2a, 0xf3, 0x2e, 0xb8, 0xc6, 0x47, //0x00005c60 .quad 5172023733869224041
	0xfd, 0x83, 0x7c, 0x24, 0x20, 0xdf, 0x50, 0xe9, //0x00005c68 .quad -1634561335591402499
	0x41, 0x54, 0xfa, 0x57, 0x1d, 0x33, 0xdc, 0x4c, //0x00005c70 .quad 5538357842881958977
	0x7e, 0xd2, 0xcd, 0x16, 0x74, 0x8b, 0xd2, 0x91, //0x00005c78 .quad -7939129862385708418
	0x52, 0xe9, 0xf8, 0xad, 0xe4, 0x3f, 0x13, 0xe0, //0x00005c80 .quad -2300424733252327086
	0x1d, 0x47, 0x81, 0x1c, 0x51, 0x2e, 0x47, 0xb6, //0x00005c88 .quad -5312226309554747619
	0xa6, 0x23, 0x77, 0xd9, 0xdd, 0x0f, 0x18, 0x58, //0x00005c90 .quad 6347841120289366950
	0xe5, 0x98, 0xa1, 0x63, 0xe5, 0xf9, 0xd8, 0xe3, //0x00005c98 .quad -2028596868516046619
	0x48, 0x76, 0xea, 0xa7, 0xea, 0x09, 0x0f, 0x57, //0x00005ca0 .quad 6273243709394548296
	0x8f, 0xff, 0x44, 0x5e, 0x2f, 0x9c, 0x67, 0x8e, //0x00005ca8 .quad -8185402070463610993
	0xda, 0x13, 0xe5, 0x51, 0x65, 0xcc, 0xd2, 0x2c, //0x00005cb0 .quad 3229868618315797466
	0x73, 0x3f, 0xd6, 0x35, 0x3b, 0x83, 0x01, 0xb2, //0x00005cb8 .quad -5620066569652125837
	0xd1, 0x58, 0x5e, 0xa6, 0x7e, 0x7f, 0x07, 0xf8, //0x00005cc0 .quad -574350245532641071
	0x4f, 0xcf, 0x4b, 0x03, 0x0a, 0xe4, 0x81, 0xde, //0x00005cc8 .quad -2413397193637769393
	0x82, 0xf7, 0xfa, 0x27, 0xaf, 0xaf, 0x04, 0xfb, //0x00005cd0 .quad -358968903457900670
	0x91, 0x61, 0x0f, 0x42, 0x86, 0x2e, 0x11, 0x8b, //0x00005cd8 .quad -8425902273664687727
	0x63, 0xb5, 0xf9, 0xf1, 0x9a, 0xdb, 0xc5, 0x79, //0x00005ce0 .quad 8774660907532399971
	0xf6, 0x39, 0x93, 0xd2, 0x27, 0x7a, 0xd5, 0xad, //0x00005ce8 .quad -5920691823653471754
	0xbc, 0x22, 0x78, 0xae, 0x81, 0x52, 0x37, 0x18, //0x00005cf0 .quad 1744954097560724156
	0x74, 0x08, 0x38, 0xc7, 0xb1, 0xd8, 0x4a, 0xd9, //0x00005cf8 .quad -2789178761139451788
	0xb5, 0x15, 0x0b, 0x0d, 0x91, 0x93, 0x22, 0x8f, //0x00005d00 .quad -8132775725879323211
	0x48, 0x05, 0x83, 0x1c, 0x6f, 0xc7, 0xce, 0x87, //0x00005d08 .quad -8660765753353239224
	0x22, 0xdb, 0x4d, 0x50, 0x75, 0x38, 0xeb, 0xb2, //0x00005d10 .quad -5554283638921766110
	0x9a, 0xc6, 0xa3, 0xe3, 0x4a, 0x79, 0xc2, 0xa9, //0x00005d18 .quad -6214271173264161126
	0xeb, 0x51, 0x61, 0xa4, 0x92, 0x06, 0xa6, 0x5f, //0x00005d20 .quad 6892203506629956075
	0x41, 0xb8, 0x8c, 0x9c, 0x9d, 0x17, 0x33, 0xd4, //0x00005d28 .quad -3156152948152813503
	0x33, 0xd3, 0xbc, 0xa6, 0x1b, 0xc4, 0xc7, 0xdb, //0x00005d30 .quad -2609901835997359309
	0x28, 0xf3, 0xd7, 0x81, 0xc2, 0xee, 0x9f, 0x84, //0x00005d38 .quad -8890124620236590296
	0x00, 0x08, 0x6c, 0x90, 0x22, 0xb5, 0xb9, 0x12, //0x00005d40 .quad 1349308723430688768
	0xf3, 0xef, 0x4d, 0x22, 0x73, 0xea, 0xc7, 0xa5, //0x00005d48 .quad -6500969756868349965
	0x00, 0x0a, 0x87, 0x34, 0x6b, 0x22, 0x68, 0xd7, //0x00005d50 .quad -2925050114139026944
	0xef, 0x6b, 0xe1, 0xea, 0x0f, 0xe5, 0x39, 0xcf, //0x00005d58 .quad -3514526177658049553
	0x40, 0x66, 0xd4, 0x00, 0x83, 0x15, 0xa1, 0xe6, //0x00005d60 .quad -1828156321336891840
	0x75, 0xe3, 0xcc, 0xf2, 0x29, 0x2f, 0x84, 0x81, //0x00005d68 .quad -9114107888677362827
	0xd0, 0x7f, 0x09, 0xc1, 0xe3, 0x5a, 0x49, 0x60, //0x00005d70 .quad 6938176635183661008
	0x53, 0x1c, 0x80, 0x6f, 0xf4, 0x3a, 0xe5, 0xa1, //0x00005d78 .quad -6780948842419315629
	0xc4, 0xdf, 0x4b, 0xb1, 0x9c, 0xb1, 0x5b, 0x38, //0x00005d80 .quad 4061034775552188356
	0x68, 0x23, 0x60, 0x8b, 0xb1, 0x89, 0x5e, 0xca, //0x00005d88 .quad -3864500034596756632
	0xb5, 0xd7, 0x9e, 0xdd, 0x03, 0x9e, 0x72, 0x46, //0x00005d90 .quad 5076293469440235445
	0x42, 0x2c, 0x38, 0xee, 0x1d, 0x2c, 0xf6, 0xfc, //0x00005d98 .quad -218939024818557886
	0xd1, 0x46, 0x83, 0x6a, 0xc2, 0xa2, 0x07, 0x6c, //0x00005da0 .quad 7784369436827535057
	0xa9, 0x1b, 0xe3, 0xb4, 0x92, 0xdb, 0x19, 0x9e, //0x00005da8 .quad -7054365918152680535
	0x85, 0x18, 0x24, 0x05, 0x73, 0x8b, 0x09, 0xc7, //0x00005db0 .quad -4104596259247744891
	0x93, 0xe2, 0x1b, 0x62, 0x77, 0x52, 0xa0, 0xc5, //0x00005db8 .quad -4206271379263462765
	0xa7, 0x1e, 0x6d, 0xc6, 0x4f, 0xee, 0xcb, 0xb8, //0x00005dc0 .quad -5130745324059681113
	0x38, 0xdb, 0xa2, 0x3a, 0x15, 0x67, 0x08, 0xf7, //0x00005dc8 .quad -646153205651940552
	0x28, 0x33, 0x04, 0xdc, 0xf1, 0x74, 0x7f, 0x73, //0x00005dd0 .quad 8322499218531169064
	0x03, 0xc9, 0xa5, 0x44, 0x6d, 0x40, 0x65, 0x9a, //0x00005dd8 .quad -7321374781173544701
	0xf2, 0x3f, 0x05, 0x53, 0x2e, 0x52, 0x5f, 0x50, //0x00005de0 .quad 5791438004736573426
	0x44, 0x3b, 0xcf, 0x95, 0x88, 0x90, 0xfe, 0xc0, //0x00005de8 .quad -4540032458039542972
	0xef, 0x8f, 0xc6, 0xe7, 0xb9, 0x26, 0x77, 0x64, //0x00005df0 .quad 7239297505920716783
	0x15, 0x0a, 0x43, 0xbb, 0xaa, 0x34, 0x3e, 0xf1, //0x00005df8 .quad -1063354554122040811
	0xf5, 0x19, 0xdc, 0x30, 0x34, 0x78, 0xca, 0x5e, //0x00005e00 .quad 6830403950414141941
	0x4d, 0xe6, 0x09, 0xb5, 0xea, 0xe0, 0xc6, 0x96, //0x00005e08 .quad -7582125623967357363
	0x72, 0x20, 0x13, 0x3d, 0x41, 0x16, 0x7d, 0xb6, //0x00005e10 .quad -5297053117264486286
	0xe0, 0x5f, 0x4c, 0x62, 0x25, 0x99, 0x78, 0xbc, //0x00005e18 .quad -4865971011531808800
	0x8f, 0xe8, 0x57, 0x8c, 0xd1, 0x5b, 0x1c, 0xe4, //0x00005e20 .quad -2009630378153219953
	0xd8, 0x77, 0xdf, 0xba, 0x6e, 0xbf, 0x96, 0xeb, //0x00005e28 .quad -1470777745987373096
	0x59, 0xf1, 0xb6, 0xf7, 0x62, 0xb9, 0x91, 0x8e, //0x00005e30 .quad -8173548013986844327
	0xe7, 0xaa, 0xcb, 0x34, 0xa5, 0x37, 0x3e, 0x93, //0x00005e38 .quad -7836765118883190041
	0xb0, 0xad, 0xa4, 0xb5, 0xbb, 0x27, 0x36, 0x72, //0x00005e40 .quad 8229809056225996208
	0xa1, 0x95, 0xfe, 0x81, 0x8e, 0xc5, 0x0d, 0xb8, //0x00005e48 .quad -5184270380176599647
	0x1c, 0xd9, 0x0d, 0xa3, 0xaa, 0xb1, 0xc3, 0xce, //0x00005e50 .quad -3547796734999668452
	0x09, 0x3b, 0x7e, 0x22, 0xf2, 0x36, 0x11, 0xe6, //0x00005e58 .quad -1868651956793361655
	0xb1, 0xa7, 0xe8, 0xa5, 0x0a, 0x4f, 0x3a, 0x21, //0x00005e60 .quad 2394313059052595121
	0xe6, 0xe4, 0x8e, 0x55, 0x57, 0xc2, 0xca, 0x8f, //0x00005e68 .quad -8085436500636932890
	0x9d, 0xd1, 0x62, 0x4f, 0xcd, 0xe2, 0x88, 0xa9, //0x00005e70 .quad -6230480713039031907
	0x1f, 0x9e, 0xf2, 0x2a, 0xed, 0x72, 0xbd, 0xb3, //0x00005e78 .quad -5495109607368778209
	0x05, 0x86, 0x3b, 0xa3, 0x80, 0x1b, 0xeb, 0x93, //0x00005e80 .quad -7788100891298789883
	0xa7, 0x45, 0xaf, 0x75, 0xa8, 0xcf, 0xac, 0xe0, //0x00005e88 .quad -2257200990783584857
	0xc3, 0x33, 0x05, 0x66, 0x30, 0xf1, 0x72, 0xbc, //0x00005e90 .quad -4867563057061743677
	0x88, 0x8b, 0x8d, 0x49, 0xc9, 0x01, 0x6c, 0x8c, //0x00005e98 .quad -8328279646880822392
	0xb4, 0x80, 0x86, 0x7f, 0x7c, 0xad, 0x8f, 0xeb, //0x00005ea0 .quad -1472767802899791692
	0x6a, 0xee, 0xf0, 0x9b, 0x3b, 0x02, 0x87, 0xaf, //0x00005ea8 .quad -5798663540173640086
	0xe1, 0x20, 0x68, 0x9f, 0xdb, 0x98, 0x73, 0xa6, //0x00005eb0 .quad -6452645772052127519
	0x05, 0x2a, 0xed, 0x82, 0xca, 0xc2, 0x68, 0xdb, //0x00005eb8 .quad -2636643406789662203
	0x8c, 0x14, 0xa1, 0x43, 0x89, 0x3f, 0x08, 0x88, //0x00005ec0 .quad -8644589625959967604
	0x43, 0x3a, 0xd4, 0x91, 0xbe, 0x79, 0x21, 0x89, //0x00005ec8 .quad -8565431156884620733
	0xb0, 0x59, 0x89, 0x94, 0x6b, 0x4f, 0x0a, 0x6a, //0x00005ed0 .quad 7641007041259592112
	0xd4, 0x48, 0x49, 0x36, 0x2e, 0xd8, 0x69, 0xab, //0x00005ed8 .quad -6095102927678388012
	0x1c, 0xb0, 0xab, 0x79, 0x46, 0xe3, 0x8c, 0x84, //0x00005ee0 .quad -8895485272135061476
	0x09, 0x9b, 0xdb, 0xc3, 0x39, 0x4e, 0x44, 0xd6, //0x00005ee8 .quad -3007192641170597111
	0x11, 0x4e, 0x0b, 0x0c, 0x0c, 0x0e, 0xd8, 0xf2, //0x00005ef0 .quad -947992276657025519
	0xe5, 0x40, 0x69, 0x1a, 0xe4, 0xb0, 0xea, 0x85, //0x00005ef8 .quad -8797024428372705051
	0x95, 0x21, 0x0e, 0x0f, 0x8f, 0x11, 0x8e, 0x6f, //0x00005f00 .quad 8038381691033493909
	0x1f, 0x91, 0x03, 0x21, 0x1d, 0x5d, 0x65, 0xa7, //0x00005f08 .quad -6384594517038493409
	0xfb, 0xa9, 0xd1, 0xd2, 0xf2, 0x95, 0x71, 0x4b, //0x00005f10 .quad 5436291095364479483
	0x67, 0x75, 0x44, 0x69, 0x64, 0xb4, 0x3e, 0xd1, //0x00005f18 .quad -3369057127870728857
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f20 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	//0x00005f30 .p2align 4, 0x00
	//0x00005f30 _POW_TAB
	0x01, 0x00, 0x00, 0x00, //0x00005f30 .long 1
	0x03, 0x00, 0x00, 0x00, //0x00005f34 .long 3
	0x06, 0x00, 0x00, 0x00, //0x00005f38 .long 6
	0x09, 0x00, 0x00, 0x00, //0x00005f3c .long 9
	0x0d, 0x00, 0x00, 0x00, //0x00005f40 .long 13
	0x10, 0x00, 0x00, 0x00, //0x00005f44 .long 16
	0x13, 0x00, 0x00, 0x00, //0x00005f48 .long 19
	0x17, 0x00, 0x00, 0x00, //0x00005f4c .long 23
	0x1a, 0x00, 0x00, 0x00, //0x00005f50 .long 26
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f54 .p2align 4, 0x00
	//0x00005f60 _LSHIFT_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f60 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f70 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f80 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f90 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fa0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fb0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fc0 QUAD $0x0000000000000000  // .space 8, '\x00\x00\x00\x00\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00005fc8 .long 1
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fcc QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fdc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ffc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000600c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000601c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000602c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00006030 .long 1
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006034 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006044 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006054 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006064 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006074 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006084 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006094 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00006098 .long 1
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000609c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000060ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000060bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000060cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000060dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000060ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000060fc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x00006100 .long 2
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006104 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006114 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006124 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006134 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006144 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006154 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006164 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x00006168 .long 2
	0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000616c QUAD $0x0000000035323133; QUAD $0x0000000000000000  // .asciz 16, '3125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000617c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000618c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000619c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000061cc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x000061d0 .long 2
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061d4 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006204 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006214 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006224 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006234 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x00006238 .long 3
	0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000623c QUAD $0x0000003532313837; QUAD $0x0000000000000000  // .asciz 16, '78125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000624c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000625c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000626c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000627c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000628c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000629c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x000062a0 .long 3
	0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062a4 QUAD $0x0000353236303933; QUAD $0x0000000000000000  // .asciz 16, '390625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006304 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x00006308 .long 3
	0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000630c QUAD $0x0035323133353931; QUAD $0x0000000000000000  // .asciz 16, '1953125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000631c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000632c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000633c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000634c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000635c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000636c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006370 .long 4
	0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006374 QUAD $0x0035323635363739; QUAD $0x0000000000000000  // .asciz 16, '9765625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006384 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006394 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000063d4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x000063d8 .long 4
	0x34, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063dc QUAD $0x3532313832383834; QUAD $0x0000000000000000  // .asciz 16, '48828125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000640c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000641c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000642c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000643c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006440 .long 4
	0x32, 0x34, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006444 QUAD $0x3236303431343432; QUAD $0x0000000000000035  // .asciz 16, '244140625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006454 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006464 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006474 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006484 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006494 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000064a4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x000064a8 .long 4
	0x31, 0x32, 0x32, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064ac QUAD $0x3133303730323231; QUAD $0x0000000000003532  // .asciz 16, '1220703125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000650c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00006510 .long 5
	0x36, 0x31, 0x30, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006514 QUAD $0x3635313533303136; QUAD $0x0000000000003532  // .asciz 16, '6103515625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006524 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006534 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006544 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006554 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006564 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006574 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00006578 .long 5
	0x33, 0x30, 0x35, 0x31, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000657c QUAD $0x3837353731353033; QUAD $0x0000000000353231  // .asciz 16, '30517578125\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000658c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000659c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000065dc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x000065e0 .long 5
	0x31, 0x35, 0x32, 0x35, 0x38, 0x37, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x000065e4 QUAD $0x3938373835323531; QUAD $0x0000000035323630  // .asciz 16, '152587890625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006604 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006614 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006624 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006634 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006644 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00006648 .long 6
	0x37, 0x36, 0x32, 0x39, 0x33, 0x39, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x0000664c QUAD $0x3534393339323637; QUAD $0x0000000035323133  // .asciz 16, '762939453125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000665c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000666c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000667c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000668c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000669c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000066ac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x000066b0 .long 6
	0x33, 0x38, 0x31, 0x34, 0x36, 0x39, 0x37, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, //0x000066b4 QUAD $0x3237393634313833; QUAD $0x0000003532363536  // .asciz 16, '3814697265625\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006704 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006714 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00006718 .long 6
	0x31, 0x39, 0x30, 0x37, 0x33, 0x34, 0x38, 0x36, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, //0x0000671c QUAD $0x3638343337303931; QUAD $0x0000353231383233  // .asciz 16, '19073486328125\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000672c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000673c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000674c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000675c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000676c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000677c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006780 .long 7
	0x39, 0x35, 0x33, 0x36, 0x37, 0x34, 0x33, 0x31, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, //0x00006784 QUAD $0x3133343736333539; QUAD $0x0000353236303436  // .asciz 16, '95367431640625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006794 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000067e4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x000067e8 .long 7
	0x34, 0x37, 0x36, 0x38, 0x33, 0x37, 0x31, 0x35, 0x38, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, //0x000067ec QUAD $0x3531373338363734; QUAD $0x0035323133303238  // .asciz 16, '476837158203125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000680c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000681c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000682c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000683c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000684c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006850 .long 7
	0x32, 0x33, 0x38, 0x34, 0x31, 0x38, 0x35, 0x37, 0x39, 0x31, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, //0x00006854 QUAD $0x3735383134383332; QUAD $0x3532363531303139  // .asciz 16, '2384185791015625'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006864 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006874 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006884 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006894 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000068b4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x000068b8 .long 7
	0x31, 0x31, 0x39, 0x32, 0x30, 0x39, 0x32, 0x38, 0x39, 0x35, 0x35, 0x30, 0x37, 0x38, 0x31, 0x32, //0x000068bc QUAD $0x3832393032393131; QUAD $0x3231383730353539  // .asciz 16, '1192092895507812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068cc QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000690c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000691c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00006920 .long 8
	0x35, 0x39, 0x36, 0x30, 0x34, 0x36, 0x34, 0x34, 0x37, 0x37, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, //0x00006924 QUAD $0x3434363430363935; QUAD $0x3236303933353737  // .asciz 16, '5960464477539062'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006934 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006944 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006954 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006964 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006974 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006984 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00006988 .long 8
	0x32, 0x39, 0x38, 0x30, 0x32, 0x33, 0x32, 0x32, 0x33, 0x38, 0x37, 0x36, 0x39, 0x35, 0x33, 0x31, //0x0000698c QUAD $0x3232333230383932; QUAD $0x3133353936373833  // .asciz 16, '2980232238769531'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000699c QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000069ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x000069f0 .long 8
	0x31, 0x34, 0x39, 0x30, 0x31, 0x31, 0x36, 0x31, 0x31, 0x39, 0x33, 0x38, 0x34, 0x37, 0x36, 0x35, //0x000069f4 QUAD $0x3136313130393431; QUAD $0x3536373438333931  // .asciz 16, '1490116119384765'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a04 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006a54 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00006a58 .long 9
	0x37, 0x34, 0x35, 0x30, 0x35, 0x38, 0x30, 0x35, 0x39, 0x36, 0x39, 0x32, 0x33, 0x38, 0x32, 0x38, //0x00006a5c QUAD $0x3530383530353437; QUAD $0x3832383332393639  // .asciz 16, '7450580596923828'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a6c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006aac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006abc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00006ac0 .long 9
	0x33, 0x37, 0x32, 0x35, 0x32, 0x39, 0x30, 0x32, 0x39, 0x38, 0x34, 0x36, 0x31, 0x39, 0x31, 0x34, //0x00006ac4 QUAD $0x3230393235323733; QUAD $0x3431393136343839  // .asciz 16, '3725290298461914'
	0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ad4 QUAD $0x0000000035323630; QUAD $0x0000000000000000  // .asciz 16, '0625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ae4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006af4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006b24 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00006b28 .long 9
	0x31, 0x38, 0x36, 0x32, 0x36, 0x34, 0x35, 0x31, 0x34, 0x39, 0x32, 0x33, 0x30, 0x39, 0x35, 0x37, //0x00006b2c QUAD $0x3135343632363831; QUAD $0x3735393033323934  // .asciz 16, '1862645149230957'
	0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b3c QUAD $0x0000003532313330; QUAD $0x0000000000000000  // .asciz 16, '03125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006b8c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006b90 .long 10
	0x39, 0x33, 0x31, 0x33, 0x32, 0x32, 0x35, 0x37, 0x34, 0x36, 0x31, 0x35, 0x34, 0x37, 0x38, 0x35, //0x00006b94 QUAD $0x3735323233313339; QUAD $0x3538373435313634  // .asciz 16, '9313225746154785'
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ba4 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bb4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bc4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bd4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006be4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006bf4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006bf8 .long 10
	0x34, 0x36, 0x35, 0x36, 0x36, 0x31, 0x32, 0x38, 0x37, 0x33, 0x30, 0x37, 0x37, 0x33, 0x39, 0x32, //0x00006bfc QUAD $0x3832313636353634; QUAD $0x3239333737303337  // .asciz 16, '4656612873077392'
	0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c0c QUAD $0x0000353231383735; QUAD $0x0000000000000000  // .asciz 16, '578125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c3c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006c5c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006c60 .long 10
	0x32, 0x33, 0x32, 0x38, 0x33, 0x30, 0x36, 0x34, 0x33, 0x36, 0x35, 0x33, 0x38, 0x36, 0x39, 0x36, //0x00006c64 QUAD $0x3436303338323332; QUAD $0x3639363833353633  // .asciz 16, '2328306436538696'
	0x32, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c74 QUAD $0x0035323630393832; QUAD $0x0000000000000000  // .asciz 16, '2890625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ca4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cb4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006cc4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006cc8 .long 10
	0x31, 0x31, 0x36, 0x34, 0x31, 0x35, 0x33, 0x32, 0x31, 0x38, 0x32, 0x36, 0x39, 0x33, 0x34, 0x38, //0x00006ccc QUAD $0x3233353134363131; QUAD $0x3834333936323831  // .asciz 16, '1164153218269348'
	0x31, 0x34, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cdc QUAD $0x3532313335343431; QUAD $0x0000000000000000  // .asciz 16, '14453125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cfc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006d2c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00006d30 .long 11
	0x35, 0x38, 0x32, 0x30, 0x37, 0x36, 0x36, 0x30, 0x39, 0x31, 0x33, 0x34, 0x36, 0x37, 0x34, 0x30, //0x00006d34 QUAD $0x3036363730323835; QUAD $0x3034373634333139  // .asciz 16, '5820766091346740'
	0x37, 0x32, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d44 QUAD $0x3532363536323237; QUAD $0x0000000000000000  // .asciz 16, '72265625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006d94 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00006d98 .long 11
	0x32, 0x39, 0x31, 0x30, 0x33, 0x38, 0x33, 0x30, 0x34, 0x35, 0x36, 0x37, 0x33, 0x33, 0x37, 0x30, //0x00006d9c QUAD $0x3033383330313932; QUAD $0x3037333337363534  // .asciz 16, '2910383045673370'
	0x33, 0x36, 0x31, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dac QUAD $0x3231383233313633; QUAD $0x0000000000000035  // .asciz 16, '361328125\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dbc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dcc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ddc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006dfc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00006e00 .long 11
	0x31, 0x34, 0x35, 0x35, 0x31, 0x39, 0x31, 0x35, 0x32, 0x32, 0x38, 0x33, 0x36, 0x36, 0x38, 0x35, //0x00006e04 QUAD $0x3531393135353431; QUAD $0x3538363633383232  // .asciz 16, '1455191522836685'
	0x31, 0x38, 0x30, 0x36, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e14 QUAD $0x3630343636303831; QUAD $0x0000000000003532  // .asciz 16, '1806640625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006e64 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00006e68 .long 12
	0x37, 0x32, 0x37, 0x35, 0x39, 0x35, 0x37, 0x36, 0x31, 0x34, 0x31, 0x38, 0x33, 0x34, 0x32, 0x35, //0x00006e6c QUAD $0x3637353935373237; QUAD $0x3532343338313431  // .asciz 16, '7275957614183425'
	0x39, 0x30, 0x33, 0x33, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e7c QUAD $0x3133303233333039; QUAD $0x0000000000003532  // .asciz 16, '9033203125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006eac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ebc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006ecc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00006ed0 .long 12
	0x33, 0x36, 0x33, 0x37, 0x39, 0x37, 0x38, 0x38, 0x30, 0x37, 0x30, 0x39, 0x31, 0x37, 0x31, 0x32, //0x00006ed4 QUAD $0x3838373937333633; QUAD $0x3231373139303730  // .asciz 16, '3637978807091712'
	0x39, 0x35, 0x31, 0x36, 0x36, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ee4 QUAD $0x3531303636313539; QUAD $0x0000000000353236  // .asciz 16, '95166015625\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ef4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006f34 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00006f38 .long 12
	0x31, 0x38, 0x31, 0x38, 0x39, 0x38, 0x39, 0x34, 0x30, 0x33, 0x35, 0x34, 0x35, 0x38, 0x35, 0x36, //0x00006f3c QUAD $0x3439383938313831; QUAD $0x3635383534353330  // .asciz 16, '1818989403545856'
	0x34, 0x37, 0x35, 0x38, 0x33, 0x30, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00006f4c QUAD $0x3730303338353734; QUAD $0x0000000035323138  // .asciz 16, '475830078125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006f9c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00006fa0 .long 13
	0x39, 0x30, 0x39, 0x34, 0x39, 0x34, 0x37, 0x30, 0x31, 0x37, 0x37, 0x32, 0x39, 0x32, 0x38, 0x32, //0x00006fa4 QUAD $0x3037343934393039; QUAD $0x3238323932373731  // .asciz 16, '9094947017729282'
	0x33, 0x37, 0x39, 0x31, 0x35, 0x30, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00006fb4 QUAD $0x3933303531393733; QUAD $0x0000000035323630  // .asciz 16, '379150390625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fc4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fd4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fe4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ff4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007004 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00007008 .long 13
	0x34, 0x35, 0x34, 0x37, 0x34, 0x37, 0x33, 0x35, 0x30, 0x38, 0x38, 0x36, 0x34, 0x36, 0x34, 0x31, //0x0000700c QUAD $0x3533373437343534; QUAD $0x3134363436383830  // .asciz 16, '4547473508864641'
	0x31, 0x38, 0x39, 0x35, 0x37, 0x35, 0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, //0x0000701c QUAD $0x3931353735393831; QUAD $0x0000003532313335  // .asciz 16, '1895751953125\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000702c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000703c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000704c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000705c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000706c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00007070 .long 13
	0x32, 0x32, 0x37, 0x33, 0x37, 0x33, 0x36, 0x37, 0x35, 0x34, 0x34, 0x33, 0x32, 0x33, 0x32, 0x30, //0x00007074 QUAD $0x3736333733373232; QUAD $0x3032333233343435  // .asciz 16, '2273736754432320'
	0x35, 0x39, 0x34, 0x37, 0x38, 0x37, 0x35, 0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, //0x00007084 QUAD $0x3935373837343935; QUAD $0x0000353236353637  // .asciz 16, '59478759765625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007094 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000070d4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x000070d8 .long 13
	0x31, 0x31, 0x33, 0x36, 0x38, 0x36, 0x38, 0x33, 0x37, 0x37, 0x32, 0x31, 0x36, 0x31, 0x36, 0x30, //0x000070dc QUAD $0x3338363836333131; QUAD $0x3036313631323737  // .asciz 16, '1136868377216160'
	0x32, 0x39, 0x37, 0x33, 0x39, 0x33, 0x37, 0x39, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, //0x000070ec QUAD $0x3937333933373932; QUAD $0x0035323138323838  // .asciz 16, '297393798828125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000710c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000711c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000712c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000713c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00007140 .long 14
	0x35, 0x36, 0x38, 0x34, 0x33, 0x34, 0x31, 0x38, 0x38, 0x36, 0x30, 0x38, 0x30, 0x38, 0x30, 0x31, //0x00007144 QUAD $0x3831343334383635; QUAD $0x3130383038303638  // .asciz 16, '5684341886080801'
	0x34, 0x38, 0x36, 0x39, 0x36, 0x38, 0x39, 0x39, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, //0x00007154 QUAD $0x3939383639363834; QUAD $0x0035323630343134  // .asciz 16, '486968994140625\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007164 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007174 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007184 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007194 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000071a4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x000071a8 .long 14
	0x32, 0x38, 0x34, 0x32, 0x31, 0x37, 0x30, 0x39, 0x34, 0x33, 0x30, 0x34, 0x30, 0x34, 0x30, 0x30, //0x000071ac QUAD $0x3930373132343832; QUAD $0x3030343034303334  // .asciz 16, '2842170943040400'
	0x37, 0x34, 0x33, 0x34, 0x38, 0x34, 0x34, 0x39, 0x37, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, //0x000071bc QUAD $0x3934343834333437; QUAD $0x3532313330373037  // .asciz 16, '7434844970703125'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000720c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00007210 .long 14
	0x31, 0x34, 0x32, 0x31, 0x30, 0x38, 0x35, 0x34, 0x37, 0x31, 0x35, 0x32, 0x30, 0x32, 0x30, 0x30, //0x00007214 QUAD $0x3435383031323431; QUAD $0x3030323032353137  // .asciz 16, '1421085471520200'
	0x33, 0x37, 0x31, 0x37, 0x34, 0x32, 0x32, 0x34, 0x38, 0x35, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, //0x00007224 QUAD $0x3432323437313733; QUAD $0x3236353135333538  // .asciz 16, '3717422485351562'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007234 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007244 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007254 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007264 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007274 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00007278 .long 15
	0x37, 0x31, 0x30, 0x35, 0x34, 0x32, 0x37, 0x33, 0x35, 0x37, 0x36, 0x30, 0x31, 0x30, 0x30, 0x31, //0x0000727c QUAD $0x3337323435303137; QUAD $0x3130303130363735  // .asciz 16, '7105427357601001'
	0x38, 0x35, 0x38, 0x37, 0x31, 0x31, 0x32, 0x34, 0x32, 0x36, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, //0x0000728c QUAD $0x3432313137383538; QUAD $0x3231383735373632  // .asciz 16, '8587112426757812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000729c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000072dc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x000072e0 .long 15
	0x33, 0x35, 0x35, 0x32, 0x37, 0x31, 0x33, 0x36, 0x37, 0x38, 0x38, 0x30, 0x30, 0x35, 0x30, 0x30, //0x000072e4 QUAD $0x3633313732353533; QUAD $0x3030353030383837  // .asciz 16, '3552713678800500'
	0x39, 0x32, 0x39, 0x33, 0x35, 0x35, 0x36, 0x32, 0x31, 0x33, 0x33, 0x37, 0x38, 0x39, 0x30, 0x36, //0x000072f4 QUAD $0x3236353533393239; QUAD $0x3630393837333331  // .asciz 16, '9293556213378906'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007304 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007314 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007324 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007334 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007344 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00007348 .long 15
	0x31, 0x37, 0x37, 0x36, 0x33, 0x35, 0x36, 0x38, 0x33, 0x39, 0x34, 0x30, 0x30, 0x32, 0x35, 0x30, //0x0000734c QUAD $0x3836353336373731; QUAD $0x3035323030343933  // .asciz 16, '1776356839400250'
	0x34, 0x36, 0x34, 0x36, 0x37, 0x37, 0x38, 0x31, 0x30, 0x36, 0x36, 0x38, 0x39, 0x34, 0x35, 0x33, //0x0000735c QUAD $0x3138373736343634; QUAD $0x3335343938363630  // .asciz 16, '4646778106689453'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000736c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000737c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000738c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000739c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000073ac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x000073b0 .long 16
	0x38, 0x38, 0x38, 0x31, 0x37, 0x38, 0x34, 0x31, 0x39, 0x37, 0x30, 0x30, 0x31, 0x32, 0x35, 0x32, //0x000073b4 QUAD $0x3134383731383838; QUAD $0x3235323130303739  // .asciz 16, '8881784197001252'
	0x33, 0x32, 0x33, 0x33, 0x38, 0x39, 0x30, 0x35, 0x33, 0x33, 0x34, 0x34, 0x37, 0x32, 0x36, 0x35, //0x000073c4 QUAD $0x3530393833333233; QUAD $0x3536323734343333  // .asciz 16, '3233890533447265'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073d4 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007404 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007414 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007418 .long 16
	0x34, 0x34, 0x34, 0x30, 0x38, 0x39, 0x32, 0x30, 0x39, 0x38, 0x35, 0x30, 0x30, 0x36, 0x32, 0x36, //0x0000741c QUAD $0x3032393830343434; QUAD $0x3632363030353839  // .asciz 16, '4440892098500626'
	0x31, 0x36, 0x31, 0x36, 0x39, 0x34, 0x35, 0x32, 0x36, 0x36, 0x37, 0x32, 0x33, 0x36, 0x33, 0x32, //0x0000742c QUAD $0x3235343936313631; QUAD $0x3233363332373636  // .asciz 16, '1616945266723632'
	0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000743c QUAD $0x0000000035323138; QUAD $0x0000000000000000  // .asciz 16, '8125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000744c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000745c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000746c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000747c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007480 .long 16
	0x32, 0x32, 0x32, 0x30, 0x34, 0x34, 0x36, 0x30, 0x34, 0x39, 0x32, 0x35, 0x30, 0x33, 0x31, 0x33, //0x00007484 QUAD $0x3036343430323232; QUAD $0x3331333035323934  // .asciz 16, '2220446049250313'
	0x30, 0x38, 0x30, 0x38, 0x34, 0x37, 0x32, 0x36, 0x33, 0x33, 0x33, 0x36, 0x31, 0x38, 0x31, 0x36, //0x00007494 QUAD $0x3632373438303830; QUAD $0x3631383136333333  // .asciz 16, '0808472633361816'
	0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074a4 QUAD $0x0000003532363034; QUAD $0x0000000000000000  // .asciz 16, '40625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000074e4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x000074e8 .long 16
	0x31, 0x31, 0x31, 0x30, 0x32, 0x32, 0x33, 0x30, 0x32, 0x34, 0x36, 0x32, 0x35, 0x31, 0x35, 0x36, //0x000074ec QUAD $0x3033323230313131; QUAD $0x3635313532363432  // .asciz 16, '1110223024625156'
	0x35, 0x34, 0x30, 0x34, 0x32, 0x33, 0x36, 0x33, 0x31, 0x36, 0x36, 0x38, 0x30, 0x39, 0x30, 0x38, //0x000074fc QUAD $0x3336333234303435; QUAD $0x3830393038363631  // .asciz 16, '5404236316680908'
	0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000750c QUAD $0x0000353231333032; QUAD $0x0000000000000000  // .asciz 16, '203125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000751c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000752c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000753c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000754c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00007550 .long 17
	0x35, 0x35, 0x35, 0x31, 0x31, 0x31, 0x35, 0x31, 0x32, 0x33, 0x31, 0x32, 0x35, 0x37, 0x38, 0x32, //0x00007554 QUAD $0x3135313131353535; QUAD $0x3238373532313332  // .asciz 16, '5551115123125782'
	0x37, 0x30, 0x32, 0x31, 0x31, 0x38, 0x31, 0x35, 0x38, 0x33, 0x34, 0x30, 0x34, 0x35, 0x34, 0x31, //0x00007564 QUAD $0x3531383131323037; QUAD $0x3134353430343338  // .asciz 16, '7021181583404541'
	0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007574 QUAD $0x0000353236353130; QUAD $0x0000000000000000  // .asciz 16, '015625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007584 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007594 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000075b4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x000075b8 .long 17
	0x32, 0x37, 0x37, 0x35, 0x35, 0x35, 0x37, 0x35, 0x36, 0x31, 0x35, 0x36, 0x32, 0x38, 0x39, 0x31, //0x000075bc QUAD $0x3537353535373732; QUAD $0x3139383236353136  // .asciz 16, '2775557561562891'
	0x33, 0x35, 0x31, 0x30, 0x35, 0x39, 0x30, 0x37, 0x39, 0x31, 0x37, 0x30, 0x32, 0x32, 0x37, 0x30, //0x000075cc QUAD $0x3730393530313533; QUAD $0x3037323230373139  // .asciz 16, '3510590791702270'
	0x35, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075dc QUAD $0x0035323138373035; QUAD $0x0000000000000000  // .asciz 16, '5078125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000760c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000761c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00007620 .long 17
	0x31, 0x33, 0x38, 0x37, 0x37, 0x37, 0x38, 0x37, 0x38, 0x30, 0x37, 0x38, 0x31, 0x34, 0x34, 0x35, //0x00007624 QUAD $0x3738373737383331; QUAD $0x3534343138373038  // .asciz 16, '1387778780781445'
	0x36, 0x37, 0x35, 0x35, 0x32, 0x39, 0x35, 0x33, 0x39, 0x35, 0x38, 0x35, 0x31, 0x31, 0x33, 0x35, //0x00007634 QUAD $0x3335393235353736; QUAD $0x3533313135383539  // .asciz 16, '6755295395851135'
	0x32, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007644 QUAD $0x3532363039333532; QUAD $0x0000000000000000  // .asciz 16, '25390625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007654 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007664 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007674 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007684 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00007688 .long 18
	0x36, 0x39, 0x33, 0x38, 0x38, 0x39, 0x33, 0x39, 0x30, 0x33, 0x39, 0x30, 0x37, 0x32, 0x32, 0x38, //0x0000768c QUAD $0x3933393838333936; QUAD $0x3832323730393330  // .asciz 16, '6938893903907228'
	0x33, 0x37, 0x37, 0x36, 0x34, 0x37, 0x36, 0x39, 0x37, 0x39, 0x32, 0x35, 0x35, 0x36, 0x37, 0x36, //0x0000769c QUAD $0x3936373436373733; QUAD $0x3637363535323937  // .asciz 16, '3776476979255676'
	0x32, 0x36, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076ac QUAD $0x3532313335393632; QUAD $0x0000000000000000  // .asciz 16, '26953125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000076ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x000076f0 .long 18
	0x33, 0x34, 0x36, 0x39, 0x34, 0x34, 0x36, 0x39, 0x35, 0x31, 0x39, 0x35, 0x33, 0x36, 0x31, 0x34, //0x000076f4 QUAD $0x3936343439363433; QUAD $0x3431363335393135  // .asciz 16, '3469446951953614'
	0x31, 0x38, 0x38, 0x38, 0x32, 0x33, 0x38, 0x34, 0x38, 0x39, 0x36, 0x32, 0x37, 0x38, 0x33, 0x38, //0x00007704 QUAD $0x3438333238383831; QUAD $0x3833383732363938  // .asciz 16, '1888238489627838'
	0x31, 0x33, 0x34, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007714 QUAD $0x3236353637343331; QUAD $0x0000000000000035  // .asciz 16, '134765625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007724 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007734 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007744 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007754 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00007758 .long 18
	0x31, 0x37, 0x33, 0x34, 0x37, 0x32, 0x33, 0x34, 0x37, 0x35, 0x39, 0x37, 0x36, 0x38, 0x30, 0x37, //0x0000775c QUAD $0x3433323734333731; QUAD $0x3730383637393537  // .asciz 16, '1734723475976807'
	0x30, 0x39, 0x34, 0x34, 0x31, 0x31, 0x39, 0x32, 0x34, 0x34, 0x38, 0x31, 0x33, 0x39, 0x31, 0x39, //0x0000776c QUAD $0x3239313134343930; QUAD $0x3931393331383434  // .asciz 16, '0944119244813919'
	0x30, 0x36, 0x37, 0x33, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000777c QUAD $0x3138323833373630; QUAD $0x0000000000003532  // .asciz 16, '0673828125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000778c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000779c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000077bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x13, 0x00, 0x00, 0x00, //0x000077c0 .long 19
	0x38, 0x36, 0x37, 0x33, 0x36, 0x31, 0x37, 0x33, 0x37, 0x39, 0x38, 0x38, 0x34, 0x30, 0x33, 0x35, //0x000077c4 QUAD $0x3337313633373638; QUAD $0x3533303438383937  // .asciz 16, '8673617379884035'
	0x34, 0x37, 0x32, 0x30, 0x35, 0x39, 0x36, 0x32, 0x32, 0x34, 0x30, 0x36, 0x39, 0x35, 0x39, 0x35, //0x000077d4 QUAD $0x3236393530323734; QUAD $0x3539353936303432  // .asciz 16, '4720596224069595'
	0x33, 0x33, 0x36, 0x39, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077e4 QUAD $0x3630343139363333; QUAD $0x0000000000003532  // .asciz 16, '3369140625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007804 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007814 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007824 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
}
 
