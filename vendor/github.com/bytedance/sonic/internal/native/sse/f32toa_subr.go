// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__f32toa = 16
)

const (
    _stack__f32toa = 56
)

const (
    _size__f32toa = 3680
)

var (
    _pcsp__f32toa = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0xe, 48},
        {0xe3a, 56},
        {0xe3b, 48},
        {0xe3d, 40},
        {0xe3f, 32},
        {0xe41, 24},
        {0xe43, 16},
        {0xe44, 8},
        {0xe45, 0},
        {0xe60, 56},
    }
)

var _cfunc_f32toa = []loader.CFunc{
    {"_f32toa_entry", 0,  _entry__f32toa, 0, nil},
    {"_f32toa", _entry__f32toa, _size__f32toa, _stack__f32toa, _pcsp__f32toa},
}
