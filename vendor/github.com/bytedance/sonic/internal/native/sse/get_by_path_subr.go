// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__get_by_path = 240
)

const (
    _stack__get_by_path = 200
)

const (
    _size__get_by_path = 21228
)

var (
    _pcsp__get_by_path = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x14, 48},
        {0x44fd, 200},
        {0x44fe, 48},
        {0x4500, 40},
        {0x4502, 32},
        {0x4504, 24},
        {0x4506, 16},
        {0x4507, 8},
        {0x4508, 0},
        {0x52ec, 200},
    }
)

var _cfunc_get_by_path = []loader.CFunc{
    {"_get_by_path_entry", 0,  _entry__get_by_path, 0, nil},
    {"_get_by_path", _entry__get_by_path, _size__get_by_path, _stack__get_by_path, _pcsp__get_by_path},
}
