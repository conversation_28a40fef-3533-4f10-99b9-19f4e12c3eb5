// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__quote = 48
)

const (
    _stack__quote = 80
)

const (
    _size__quote = 1712
)

var (
    _pcsp__quote = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0x67f, 80},
        {0x680, 48},
        {0x682, 40},
        {0x684, 32},
        {0x686, 24},
        {0x688, 16},
        {0x689, 8},
        {0x68a, 0},
        {0x6b0, 80},
    }
)

var _cfunc_quote = []loader.CFunc{
    {"_quote_entry", 0,  _entry__quote, 0, nil},
    {"_quote", _entry__quote, _size__quote, _stack__quote, _pcsp__quote},
}
