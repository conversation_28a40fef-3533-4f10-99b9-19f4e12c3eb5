// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__validate_utf8 = 0
)

const (
    _stack__validate_utf8 = 48
)

const (
    _size__validate_utf8 = 684
)

var (
    _pcsp__validate_utf8 = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xb, 32},
        {0xc, 40},
        {0x283, 48},
        {0x284, 40},
        {0x286, 32},
        {0x288, 24},
        {0x28a, 16},
        {0x28b, 8},
        {0x28c, 0},
        {0x2ac, 48},
    }
)

var _cfunc_validate_utf8 = []loader.CFunc{
    {"_validate_utf8_entry", 0,  _entry__validate_utf8, 0, nil},
    {"_validate_utf8", _entry__validate_utf8, _size__validate_utf8, _stack__validate_utf8, _pcsp__validate_utf8},
}
