// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_get_by_path = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, // QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000010 LCPI0_1
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000010 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000020 LCPI0_2
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000020 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000030 LCPI0_3
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000030 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000040 LCPI0_4
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000040 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000050 LCPI0_5
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000050 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x00000060 LCPI0_6
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000060 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000070 LCPI0_7
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000070 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000080 LCPI0_8
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000080 .quad 1
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000088 .quad 0
	//0x00000090 LCPI0_9
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000090 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000000a0 LCPI0_10
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000a0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x000000b0 LCPI0_11
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x000000b0 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x000000c0 LCPI0_12
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x000000c0 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x000000d0 LCPI0_13
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000000d0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000000e0 LCPI0_14
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x000000e0 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x000000f0 .p2align 4, 0x90
	//0x000000f0 _get_by_path
	0x55, //0x000000f0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000000f1 movq         %rsp, %rbp
	0x41, 0x57, //0x000000f4 pushq        %r15
	0x41, 0x56, //0x000000f6 pushq        %r14
	0x41, 0x55, //0x000000f8 pushq        %r13
	0x41, 0x54, //0x000000fa pushq        %r12
	0x53, //0x000000fc pushq        %rbx
	0x48, 0x81, 0xec, 0x98, 0x00, 0x00, 0x00, //0x000000fd subq         $152, %rsp
	0x49, 0x89, 0xf3, //0x00000104 movq         %rsi, %r11
	0x48, 0x8b, 0x42, 0x08, //0x00000107 movq         $8(%rdx), %rax
	0x48, 0x85, 0xc0, //0x0000010b testq        %rax, %rax
	0x48, 0x89, 0x75, 0xd0, //0x0000010e movq         %rsi, $-48(%rbp)
	0x48, 0x89, 0x7d, 0xb8, //0x00000112 movq         %rdi, $-72(%rbp)
	0x48, 0x89, 0x4d, 0x90, //0x00000116 movq         %rcx, $-112(%rbp)
	0x0f, 0x84, 0x18, 0x2c, 0x00, 0x00, //0x0000011a je           LBB0_481
	0x4c, 0x8b, 0x32, //0x00000120 movq         (%rdx), %r14
	0x48, 0xc1, 0xe0, 0x04, //0x00000123 shlq         $4, %rax
	0x4c, 0x01, 0xf0, //0x00000127 addq         %r14, %rax
	0x48, 0x89, 0x85, 0x40, 0xff, 0xff, 0xff, //0x0000012a movq         %rax, $-192(%rbp)
	0x4c, 0x8d, 0x4f, 0x08, //0x00000131 leaq         $8(%rdi), %r9
	0x4c, 0x8b, 0x2f, //0x00000135 movq         (%rdi), %r13
	0x49, 0x8b, 0x03, //0x00000138 movq         (%r11), %rax
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000013b movabsq      $4294977024, %r12
	0xf3, 0x0f, 0x6f, 0x05, 0xe3, 0xfe, 0xff, 0xff, //0x00000145 movdqu       $-285(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0xeb, 0xfe, 0xff, 0xff, //0x0000014d movdqu       $-277(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0xa2, 0xfe, 0xff, 0xff, //0x00000155 movdqu       $-350(%rip), %xmm13  /* LCPI0_0+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x35, 0xa9, 0xfe, 0xff, 0xff, //0x0000015e movdqu       $-343(%rip), %xmm14  /* LCPI0_1+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0xb1, 0xfe, 0xff, 0xff, //0x00000167 movdqu       $-335(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xc9, //0x0000016f pcmpeqd      %xmm9, %xmm9
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xf3, 0xfe, 0xff, 0xff, //0x00000174 movdqu       $-269(%rip), %xmm10  /* LCPI0_7+0(%rip) */
	0x66, 0x45, 0x0f, 0xef, 0xc0, //0x0000017d pxor         %xmm8, %xmm8
	0xf3, 0x44, 0x0f, 0x6f, 0x1d, 0xc5, 0xfe, 0xff, 0xff, //0x00000182 movdqu       $-315(%rip), %xmm11  /* LCPI0_5+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x25, 0xcc, 0xfe, 0xff, 0xff, //0x0000018b movdqu       $-308(%rip), %xmm12  /* LCPI0_6+0(%rip) */
	0x4c, 0x89, 0x4d, 0xb0, //0x00000194 movq         %r9, $-80(%rbp)
	//0x00000198 LBB0_2
	0x49, 0x8b, 0x09, //0x00000198 movq         (%r9), %rcx
	0x48, 0x39, 0xc8, //0x0000019b cmpq         %rcx, %rax
	0x0f, 0x83, 0x2c, 0x00, 0x00, 0x00, //0x0000019e jae          LBB0_7
	0x41, 0x8a, 0x54, 0x05, 0x00, //0x000001a4 movb         (%r13,%rax), %dl
	0x80, 0xfa, 0x0d, //0x000001a9 cmpb         $13, %dl
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x000001ac je           LBB0_7
	0x80, 0xfa, 0x20, //0x000001b2 cmpb         $32, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000001b5 je           LBB0_7
	0x80, 0xc2, 0xf5, //0x000001bb addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x000001be cmpb         $-2, %dl
	0x0f, 0x83, 0x09, 0x00, 0x00, 0x00, //0x000001c1 jae          LBB0_7
	0x48, 0x89, 0xc2, //0x000001c7 movq         %rax, %rdx
	0xe9, 0x0f, 0x01, 0x00, 0x00, //0x000001ca jmp          LBB0_28
	0x90, //0x000001cf .p2align 4, 0x90
	//0x000001d0 LBB0_7
	0x48, 0x8d, 0x50, 0x01, //0x000001d0 leaq         $1(%rax), %rdx
	0x48, 0x39, 0xca, //0x000001d4 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000001d7 jae          LBB0_11
	0x41, 0x8a, 0x5c, 0x15, 0x00, //0x000001dd movb         (%r13,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x000001e2 cmpb         $13, %bl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000001e5 je           LBB0_11
	0x80, 0xfb, 0x20, //0x000001eb cmpb         $32, %bl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000001ee je           LBB0_11
	0x80, 0xc3, 0xf5, //0x000001f4 addb         $-11, %bl
	0x80, 0xfb, 0xfe, //0x000001f7 cmpb         $-2, %bl
	0x0f, 0x82, 0xde, 0x00, 0x00, 0x00, //0x000001fa jb           LBB0_28
	//0x00000200 .p2align 4, 0x90
	//0x00000200 LBB0_11
	0x48, 0x8d, 0x50, 0x02, //0x00000200 leaq         $2(%rax), %rdx
	0x48, 0x39, 0xca, //0x00000204 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000207 jae          LBB0_15
	0x41, 0x8a, 0x5c, 0x15, 0x00, //0x0000020d movb         (%r13,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000212 cmpb         $13, %bl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000215 je           LBB0_15
	0x80, 0xfb, 0x20, //0x0000021b cmpb         $32, %bl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000021e je           LBB0_15
	0x80, 0xc3, 0xf5, //0x00000224 addb         $-11, %bl
	0x80, 0xfb, 0xfe, //0x00000227 cmpb         $-2, %bl
	0x0f, 0x82, 0xae, 0x00, 0x00, 0x00, //0x0000022a jb           LBB0_28
	//0x00000230 .p2align 4, 0x90
	//0x00000230 LBB0_15
	0x48, 0x8d, 0x50, 0x03, //0x00000230 leaq         $3(%rax), %rdx
	0x48, 0x39, 0xca, //0x00000234 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000237 jae          LBB0_19
	0x41, 0x8a, 0x5c, 0x15, 0x00, //0x0000023d movb         (%r13,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000242 cmpb         $13, %bl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000245 je           LBB0_19
	0x80, 0xfb, 0x20, //0x0000024b cmpb         $32, %bl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000024e je           LBB0_19
	0x80, 0xc3, 0xf5, //0x00000254 addb         $-11, %bl
	0x80, 0xfb, 0xfe, //0x00000257 cmpb         $-2, %bl
	0x0f, 0x82, 0x7e, 0x00, 0x00, 0x00, //0x0000025a jb           LBB0_28
	//0x00000260 .p2align 4, 0x90
	//0x00000260 LBB0_19
	0x4c, 0x8d, 0x50, 0x04, //0x00000260 leaq         $4(%rax), %r10
	0x4c, 0x39, 0xd1, //0x00000264 cmpq         %r10, %rcx
	0x0f, 0x86, 0x43, 0x00, 0x00, 0x00, //0x00000267 jbe          LBB0_547
	0x0f, 0x84, 0x53, 0x00, 0x00, 0x00, //0x0000026d je           LBB0_25
	0x4a, 0x8d, 0x14, 0x29, //0x00000273 leaq         (%rcx,%r13), %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000277 .p2align 4, 0x90
	//0x00000280 LBB0_22
	0x43, 0x0f, 0xbe, 0x74, 0x15, 0x00, //0x00000280 movsbl       (%r13,%r10), %esi
	0x83, 0xfe, 0x20, //0x00000286 cmpl         $32, %esi
	0x0f, 0x87, 0x43, 0x00, 0x00, 0x00, //0x00000289 ja           LBB0_27
	0x49, 0x0f, 0xa3, 0xf4, //0x0000028f btq          %rsi, %r12
	0x0f, 0x83, 0x39, 0x00, 0x00, 0x00, //0x00000293 jae          LBB0_27
	0x49, 0x83, 0xc2, 0x01, //0x00000299 addq         $1, %r10
	0x4c, 0x39, 0xd1, //0x0000029d cmpq         %r10, %rcx
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x000002a0 jne          LBB0_22
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x000002a6 jmp          LBB0_26
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000002ab .p2align 4, 0x90
	//0x000002b0 LBB0_547
	0x4d, 0x89, 0x13, //0x000002b0 movq         %r10, (%r11)
	0x31, 0xc9, //0x000002b3 xorl         %ecx, %ecx
	0x49, 0x8b, 0x06, //0x000002b5 movq         (%r14), %rax
	0x48, 0x85, 0xc0, //0x000002b8 testq        %rax, %rax
	0x0f, 0x85, 0x4f, 0x00, 0x00, 0x00, //0x000002bb jne          LBB0_30
	0xe9, 0x68, 0x2c, 0x00, 0x00, //0x000002c1 jmp          LBB0_548
	//0x000002c6 LBB0_25
	0x4d, 0x01, 0xea, //0x000002c6 addq         %r13, %r10
	0x4c, 0x89, 0xd2, //0x000002c9 movq         %r10, %rdx
	//0x000002cc LBB0_26
	0x4c, 0x29, 0xea, //0x000002cc subq         %r13, %rdx
	0x49, 0x89, 0xd2, //0x000002cf movq         %rdx, %r10
	//0x000002d2 LBB0_27
	0x4c, 0x89, 0xd2, //0x000002d2 movq         %r10, %rdx
	0x49, 0x39, 0xca, //0x000002d5 cmpq         %rcx, %r10
	0x0f, 0x83, 0x1d, 0x00, 0x00, 0x00, //0x000002d8 jae          LBB0_29
	//0x000002de LBB0_28
	0x4c, 0x8d, 0x52, 0x01, //0x000002de leaq         $1(%rdx), %r10
	0x4d, 0x89, 0x13, //0x000002e2 movq         %r10, (%r11)
	0x41, 0x8a, 0x4c, 0x15, 0x00, //0x000002e5 movb         (%r13,%rdx), %cl
	0x49, 0x8b, 0x06, //0x000002ea movq         (%r14), %rax
	0x48, 0x85, 0xc0, //0x000002ed testq        %rax, %rax
	0x0f, 0x85, 0x1a, 0x00, 0x00, 0x00, //0x000002f0 jne          LBB0_30
	0xe9, 0x33, 0x2c, 0x00, 0x00, //0x000002f6 jmp          LBB0_548
	//0x000002fb LBB0_29
	0x31, 0xc9, //0x000002fb xorl         %ecx, %ecx
	0x49, 0x89, 0xc2, //0x000002fd movq         %rax, %r10
	0x49, 0x8b, 0x06, //0x00000300 movq         (%r14), %rax
	0x48, 0x85, 0xc0, //0x00000303 testq        %rax, %rax
	0x0f, 0x84, 0x22, 0x2c, 0x00, 0x00, //0x00000306 je           LBB0_548
	0x90, 0x90, 0x90, 0x90, //0x0000030c .p2align 4, 0x90
	//0x00000310 LBB0_30
	0x8a, 0x40, 0x17, //0x00000310 movb         $23(%rax), %al
	0x24, 0x1f, //0x00000313 andb         $31, %al
	0x3c, 0x02, //0x00000315 cmpb         $2, %al
	0x0f, 0x84, 0x33, 0x1a, 0x00, 0x00, //0x00000317 je           LBB0_340
	0x3c, 0x18, //0x0000031d cmpb         $24, %al
	0x0f, 0x85, 0x09, 0x2c, 0x00, 0x00, //0x0000031f jne          LBB0_548
	0x80, 0xf9, 0x7b, //0x00000325 cmpb         $123, %cl
	0x4c, 0x89, 0x75, 0xc8, //0x00000328 movq         %r14, $-56(%rbp)
	0x0f, 0x85, 0xa3, 0x42, 0x00, 0x00, //0x0000032c jne          LBB0_864
	//0x00000332 LBB0_33
	0x49, 0x8b, 0x01, //0x00000332 movq         (%r9), %rax
	0x49, 0x39, 0xc2, //0x00000335 cmpq         %rax, %r10
	0x0f, 0x83, 0x32, 0x00, 0x00, 0x00, //0x00000338 jae          LBB0_38
	0x43, 0x8a, 0x4c, 0x15, 0x00, //0x0000033e movb         (%r13,%r10), %cl
	0x80, 0xf9, 0x0d, //0x00000343 cmpb         $13, %cl
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00000346 je           LBB0_38
	0x80, 0xf9, 0x20, //0x0000034c cmpb         $32, %cl
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x0000034f je           LBB0_38
	0x80, 0xc1, 0xf5, //0x00000355 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000358 cmpb         $-2, %cl
	0x0f, 0x83, 0x0f, 0x00, 0x00, 0x00, //0x0000035b jae          LBB0_38
	0x4c, 0x89, 0xd2, //0x00000361 movq         %r10, %rdx
	0xe9, 0xfc, 0x00, 0x00, 0x00, //0x00000364 jmp          LBB0_59
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000369 .p2align 4, 0x90
	//0x00000370 LBB0_38
	0x49, 0x8d, 0x52, 0x01, //0x00000370 leaq         $1(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00000374 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000377 jae          LBB0_42
	0x41, 0x8a, 0x4c, 0x15, 0x00, //0x0000037d movb         (%r13,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00000382 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000385 je           LBB0_42
	0x80, 0xf9, 0x20, //0x0000038b cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000038e je           LBB0_42
	0x80, 0xc1, 0xf5, //0x00000394 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000397 cmpb         $-2, %cl
	0x0f, 0x82, 0xc5, 0x00, 0x00, 0x00, //0x0000039a jb           LBB0_59
	//0x000003a0 .p2align 4, 0x90
	//0x000003a0 LBB0_42
	0x49, 0x8d, 0x52, 0x02, //0x000003a0 leaq         $2(%r10), %rdx
	0x48, 0x39, 0xc2, //0x000003a4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000003a7 jae          LBB0_46
	0x41, 0x8a, 0x4c, 0x15, 0x00, //0x000003ad movb         (%r13,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x000003b2 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000003b5 je           LBB0_46
	0x80, 0xf9, 0x20, //0x000003bb cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000003be je           LBB0_46
	0x80, 0xc1, 0xf5, //0x000003c4 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x000003c7 cmpb         $-2, %cl
	0x0f, 0x82, 0x95, 0x00, 0x00, 0x00, //0x000003ca jb           LBB0_59
	//0x000003d0 .p2align 4, 0x90
	//0x000003d0 LBB0_46
	0x49, 0x8d, 0x52, 0x03, //0x000003d0 leaq         $3(%r10), %rdx
	0x48, 0x39, 0xc2, //0x000003d4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000003d7 jae          LBB0_50
	0x41, 0x8a, 0x4c, 0x15, 0x00, //0x000003dd movb         (%r13,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x000003e2 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000003e5 je           LBB0_50
	0x80, 0xf9, 0x20, //0x000003eb cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000003ee je           LBB0_50
	0x80, 0xc1, 0xf5, //0x000003f4 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x000003f7 cmpb         $-2, %cl
	0x0f, 0x82, 0x65, 0x00, 0x00, 0x00, //0x000003fa jb           LBB0_59
	//0x00000400 .p2align 4, 0x90
	//0x00000400 LBB0_50
	0x49, 0x8d, 0x52, 0x04, //0x00000400 leaq         $4(%r10), %rdx
	0x48, 0x39, 0xd0, //0x00000404 cmpq         %rdx, %rax
	0x0f, 0x86, 0x19, 0x2b, 0x00, 0x00, //0x00000407 jbe          LBB0_862
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x0000040d je           LBB0_56
	0x4a, 0x8d, 0x0c, 0x28, //0x00000413 leaq         (%rax,%r13), %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000417 .p2align 4, 0x90
	//0x00000420 LBB0_53
	0x41, 0x0f, 0xbe, 0x74, 0x15, 0x00, //0x00000420 movsbl       (%r13,%rdx), %esi
	0x83, 0xfe, 0x20, //0x00000426 cmpl         $32, %esi
	0x0f, 0x87, 0x2d, 0x00, 0x00, 0x00, //0x00000429 ja           LBB0_58
	0x49, 0x0f, 0xa3, 0xf4, //0x0000042f btq          %rsi, %r12
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000433 jae          LBB0_58
	0x48, 0x83, 0xc2, 0x01, //0x00000439 addq         $1, %rdx
	0x48, 0x39, 0xd0, //0x0000043d cmpq         %rdx, %rax
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x00000440 jne          LBB0_53
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00000446 jmp          LBB0_57
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000044b .p2align 4, 0x90
	//0x00000450 LBB0_56
	0x4c, 0x01, 0xea, //0x00000450 addq         %r13, %rdx
	0x48, 0x89, 0xd1, //0x00000453 movq         %rdx, %rcx
	//0x00000456 LBB0_57
	0x4c, 0x29, 0xe9, //0x00000456 subq         %r13, %rcx
	0x48, 0x89, 0xca, //0x00000459 movq         %rcx, %rdx
	//0x0000045c LBB0_58
	0x48, 0x39, 0xc2, //0x0000045c cmpq         %rax, %rdx
	0x0f, 0x83, 0x70, 0x41, 0x00, 0x00, //0x0000045f jae          LBB0_864
	//0x00000465 LBB0_59
	0x4c, 0x8d, 0x52, 0x01, //0x00000465 leaq         $1(%rdx), %r10
	0x4d, 0x89, 0x13, //0x00000469 movq         %r10, (%r11)
	0x41, 0x8a, 0x44, 0x15, 0x00, //0x0000046c movb         (%r13,%rdx), %al
	0x3c, 0x22, //0x00000471 cmpb         $34, %al
	0x0f, 0x85, 0x73, 0x29, 0x00, 0x00, //0x00000473 jne          LBB0_187
	0x49, 0x8b, 0x01, //0x00000479 movq         (%r9), %rax
	0x48, 0x89, 0xc1, //0x0000047c movq         %rax, %rcx
	0x4c, 0x29, 0xd1, //0x0000047f subq         %r10, %rcx
	0x0f, 0x84, 0x61, 0x4d, 0x00, 0x00, //0x00000482 je           LBB0_963
	0x48, 0x89, 0x45, 0x98, //0x00000488 movq         %rax, $-104(%rbp)
	0x49, 0x8b, 0x46, 0x08, //0x0000048c movq         $8(%r14), %rax
	0x4c, 0x8b, 0x38, //0x00000490 movq         (%rax), %r15
	0x48, 0x8b, 0x40, 0x08, //0x00000493 movq         $8(%rax), %rax
	0x48, 0x89, 0x45, 0xa0, //0x00000497 movq         %rax, $-96(%rbp)
	0x4b, 0x8d, 0x04, 0x2a, //0x0000049b leaq         (%r10,%r13), %rax
	0x48, 0x89, 0x45, 0xc0, //0x0000049f movq         %rax, $-64(%rbp)
	0x48, 0x83, 0xf9, 0x40, //0x000004a3 cmpq         $64, %rcx
	0x0f, 0x82, 0x59, 0x12, 0x00, 0x00, //0x000004a7 jb           LBB0_263
	0x4c, 0x89, 0xbd, 0x48, 0xff, 0xff, 0xff, //0x000004ad movq         %r15, $-184(%rbp)
	0x48, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x000004b4 movq         $-2, %rdi
	0x48, 0x29, 0xd7, //0x000004bb subq         %rdx, %rdi
	0x48, 0xc7, 0x45, 0xa8, 0xff, 0xff, 0xff, 0xff, //0x000004be movq         $-1, $-88(%rbp)
	0x45, 0x31, 0xdb, //0x000004c6 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000004c9 .p2align 4, 0x90
	//0x000004d0 LBB0_63
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x15, 0x00, //0x000004d0 movdqu       (%r13,%r10), %xmm3
	0xf3, 0x43, 0x0f, 0x6f, 0x6c, 0x15, 0x10, //0x000004d7 movdqu       $16(%r13,%r10), %xmm5
	0xf3, 0x43, 0x0f, 0x6f, 0x74, 0x15, 0x20, //0x000004de movdqu       $32(%r13,%r10), %xmm6
	0xf3, 0x43, 0x0f, 0x6f, 0x7c, 0x15, 0x30, //0x000004e5 movdqu       $48(%r13,%r10), %xmm7
	0x66, 0x0f, 0x6f, 0xd3, //0x000004ec movdqa       %xmm3, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x000004f0 pcmpeqb      %xmm0, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x000004f4 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x6f, 0xd5, //0x000004f8 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x000004fc pcmpeqb      %xmm0, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xe2, //0x00000500 pmovmskb     %xmm2, %r12d
	0x66, 0x0f, 0x6f, 0xd6, //0x00000505 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00000509 pcmpeqb      %xmm0, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xfa, //0x0000050d pmovmskb     %xmm2, %r15d
	0x66, 0x0f, 0x6f, 0xd7, //0x00000512 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00000516 pcmpeqb      %xmm0, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xc2, //0x0000051a pmovmskb     %xmm2, %r8d
	0x66, 0x0f, 0x74, 0xd9, //0x0000051f pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x00000523 pmovmskb     %xmm3, %ebx
	0x66, 0x0f, 0x74, 0xe9, //0x00000527 pcmpeqb      %xmm1, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xcd, //0x0000052b pmovmskb     %xmm5, %r9d
	0x66, 0x0f, 0x74, 0xf1, //0x00000530 pcmpeqb      %xmm1, %xmm6
	0x66, 0x0f, 0xd7, 0xc6, //0x00000534 pmovmskb     %xmm6, %eax
	0x66, 0x0f, 0x74, 0xf9, //0x00000538 pcmpeqb      %xmm1, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xf7, //0x0000053c pmovmskb     %xmm7, %r14d
	0x49, 0xc1, 0xe0, 0x30, //0x00000541 shlq         $48, %r8
	0x49, 0xc1, 0xe7, 0x20, //0x00000545 shlq         $32, %r15
	0x4d, 0x09, 0xc7, //0x00000549 orq          %r8, %r15
	0x49, 0xc1, 0xe4, 0x10, //0x0000054c shlq         $16, %r12
	0x4d, 0x09, 0xfc, //0x00000550 orq          %r15, %r12
	0x4c, 0x09, 0xe6, //0x00000553 orq          %r12, %rsi
	0x49, 0xc1, 0xe6, 0x30, //0x00000556 shlq         $48, %r14
	0x48, 0xc1, 0xe0, 0x20, //0x0000055a shlq         $32, %rax
	0x4c, 0x09, 0xf0, //0x0000055e orq          %r14, %rax
	0x49, 0xc1, 0xe1, 0x10, //0x00000561 shlq         $16, %r9
	0x49, 0x09, 0xc1, //0x00000565 orq          %rax, %r9
	0x4c, 0x09, 0xcb, //0x00000568 orq          %r9, %rbx
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x0000056b jne          LBB0_72
	0x4d, 0x85, 0xdb, //0x00000571 testq        %r11, %r11
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000574 jne          LBB0_74
	0x45, 0x31, 0xdb, //0x0000057a xorl         %r11d, %r11d
	0x48, 0x85, 0xf6, //0x0000057d testq        %rsi, %rsi
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00000580 jne          LBB0_75
	//0x00000586 LBB0_66
	0x48, 0x83, 0xc1, 0xc0, //0x00000586 addq         $-64, %rcx
	0x48, 0x83, 0xc7, 0xc0, //0x0000058a addq         $-64, %rdi
	0x49, 0x83, 0xc2, 0x40, //0x0000058e addq         $64, %r10
	0x48, 0x83, 0xf9, 0x3f, //0x00000592 cmpq         $63, %rcx
	0x0f, 0x87, 0x34, 0xff, 0xff, 0xff, //0x00000596 ja           LBB0_63
	0xe9, 0x6e, 0x10, 0x00, 0x00, //0x0000059c jmp          LBB0_67
	//0x000005a1 LBB0_72
	0x48, 0x83, 0x7d, 0xa8, 0xff, //0x000005a1 cmpq         $-1, $-88(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x000005a6 jne          LBB0_74
	0x48, 0x0f, 0xbc, 0xc3, //0x000005ac bsfq         %rbx, %rax
	0x4c, 0x01, 0xd0, //0x000005b0 addq         %r10, %rax
	0x48, 0x89, 0x45, 0xa8, //0x000005b3 movq         %rax, $-88(%rbp)
	//0x000005b7 LBB0_74
	0x4d, 0x89, 0xd9, //0x000005b7 movq         %r11, %r9
	0x49, 0xf7, 0xd1, //0x000005ba notq         %r9
	0x49, 0x21, 0xd9, //0x000005bd andq         %rbx, %r9
	0x4f, 0x8d, 0x04, 0x09, //0x000005c0 leaq         (%r9,%r9), %r8
	0x4d, 0x09, 0xd8, //0x000005c4 orq          %r11, %r8
	0x4c, 0x89, 0xc0, //0x000005c7 movq         %r8, %rax
	0x48, 0xf7, 0xd0, //0x000005ca notq         %rax
	0x48, 0x21, 0xd8, //0x000005cd andq         %rbx, %rax
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000005d0 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xd8, //0x000005da andq         %rbx, %rax
	0x45, 0x31, 0xdb, //0x000005dd xorl         %r11d, %r11d
	0x4c, 0x01, 0xc8, //0x000005e0 addq         %r9, %rax
	0x41, 0x0f, 0x92, 0xc3, //0x000005e3 setb         %r11b
	0x48, 0x01, 0xc0, //0x000005e7 addq         %rax, %rax
	0x48, 0xbb, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000005ea movabsq      $6148914691236517205, %rbx
	0x48, 0x31, 0xd8, //0x000005f4 xorq         %rbx, %rax
	0x4c, 0x21, 0xc0, //0x000005f7 andq         %r8, %rax
	0x48, 0xf7, 0xd0, //0x000005fa notq         %rax
	0x48, 0x21, 0xc6, //0x000005fd andq         %rax, %rsi
	0x48, 0x85, 0xf6, //0x00000600 testq        %rsi, %rsi
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00000603 je           LBB0_66
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000609 .p2align 4, 0x90
	//0x00000610 LBB0_75
	0x4c, 0x0f, 0xbc, 0xd6, //0x00000610 bsfq         %rsi, %r10
	0x49, 0x29, 0xfa, //0x00000614 subq         %rdi, %r10
	0x4c, 0x8b, 0x5d, 0xd0, //0x00000617 movq         $-48(%rbp), %r11
	0x48, 0x8b, 0x7d, 0xb8, //0x0000061b movq         $-72(%rbp), %rdi
	0x4c, 0x8b, 0x75, 0xc8, //0x0000061f movq         $-56(%rbp), %r14
	0x4c, 0x8b, 0x4d, 0xb0, //0x00000623 movq         $-80(%rbp), %r9
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000627 movabsq      $4294977024, %r12
	0x4c, 0x8b, 0xbd, 0x48, 0xff, 0xff, 0xff, //0x00000631 movq         $-184(%rbp), %r15
	0x4c, 0x8b, 0x45, 0xa8, //0x00000638 movq         $-88(%rbp), %r8
	//0x0000063c LBB0_76
	0x4d, 0x85, 0xd2, //0x0000063c testq        %r10, %r10
	0x0f, 0x88, 0xa8, 0x4b, 0x00, 0x00, //0x0000063f js           LBB0_964
	0x4d, 0x89, 0x13, //0x00000645 movq         %r10, (%r11)
	0x49, 0x83, 0xf8, 0xff, //0x00000648 cmpq         $-1, %r8
	0x0f, 0x84, 0x09, 0x00, 0x00, 0x00, //0x0000064c je           LBB0_79
	0x4d, 0x39, 0xd0, //0x00000652 cmpq         %r10, %r8
	0x0f, 0x8e, 0xcd, 0x10, 0x00, 0x00, //0x00000655 jle          LBB0_265
	//0x0000065b LBB0_79
	0x4c, 0x89, 0xd0, //0x0000065b movq         %r10, %rax
	0x48, 0x29, 0xd0, //0x0000065e subq         %rdx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00000661 addq         $-2, %rax
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000665 movl         $1, %ecx
	0x48, 0x89, 0xc6, //0x0000066a movq         %rax, %rsi
	0x48, 0x8b, 0x5d, 0xa0, //0x0000066d movq         $-96(%rbp), %rbx
	0x48, 0x09, 0xde, //0x00000671 orq          %rbx, %rsi
	0x0f, 0x84, 0x4c, 0x00, 0x00, 0x00, //0x00000674 je           LBB0_85
	0x48, 0x39, 0xd8, //0x0000067a cmpq         %rbx, %rax
	0x0f, 0x85, 0x41, 0x00, 0x00, 0x00, //0x0000067d jne          LBB0_84
	0x48, 0x89, 0xde, //0x00000683 movq         %rbx, %rsi
	0x31, 0xdb, //0x00000686 xorl         %ebx, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000688 .p2align 4, 0x90
	//0x00000690 LBB0_82
	0x48, 0x83, 0xfe, 0x10, //0x00000690 cmpq         $16, %rsi
	0x0f, 0x82, 0xa1, 0x02, 0x00, 0x00, //0x00000694 jb           LBB0_137
	0x48, 0x8b, 0x45, 0xc0, //0x0000069a movq         $-64(%rbp), %rax
	0xf3, 0x0f, 0x6f, 0x14, 0x18, //0x0000069e movdqu       (%rax,%rbx), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x1c, 0x1f, //0x000006a3 movdqu       (%r15,%rbx), %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x000006a9 pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x000006ad pmovmskb     %xmm3, %eax
	0x48, 0x83, 0xc6, 0xf0, //0x000006b1 addq         $-16, %rsi
	0x48, 0x83, 0xc3, 0x10, //0x000006b5 addq         $16, %rbx
	0x3d, 0xff, 0xff, 0x00, 0x00, //0x000006b9 cmpl         $65535, %eax
	0x0f, 0x84, 0xcc, 0xff, 0xff, 0xff, //0x000006be je           LBB0_82
	//0x000006c4 LBB0_84
	0x31, 0xc9, //0x000006c4 xorl         %ecx, %ecx
	//0x000006c6 LBB0_85
	0x49, 0x8b, 0x01, //0x000006c6 movq         (%r9), %rax
	0x49, 0x39, 0xc2, //0x000006c9 cmpq         %rax, %r10
	0x0f, 0x83, 0x2e, 0x00, 0x00, 0x00, //0x000006cc jae          LBB0_90
	0x43, 0x8a, 0x54, 0x15, 0x00, //0x000006d2 movb         (%r13,%r10), %dl
	0x80, 0xfa, 0x0d, //0x000006d7 cmpb         $13, %dl
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x000006da je           LBB0_90
	0x80, 0xfa, 0x20, //0x000006e0 cmpb         $32, %dl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000006e3 je           LBB0_90
	0x80, 0xc2, 0xf5, //0x000006e9 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x000006ec cmpb         $-2, %dl
	0x0f, 0x83, 0x0b, 0x00, 0x00, 0x00, //0x000006ef jae          LBB0_90
	0x4c, 0x89, 0xd2, //0x000006f5 movq         %r10, %rdx
	0xe9, 0xf8, 0x00, 0x00, 0x00, //0x000006f8 jmp          LBB0_111
	0x90, 0x90, 0x90, //0x000006fd .p2align 4, 0x90
	//0x00000700 LBB0_90
	0x49, 0x8d, 0x52, 0x01, //0x00000700 leaq         $1(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00000704 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000707 jae          LBB0_94
	0x41, 0x8a, 0x5c, 0x15, 0x00, //0x0000070d movb         (%r13,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000712 cmpb         $13, %bl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000715 je           LBB0_94
	0x80, 0xfb, 0x20, //0x0000071b cmpb         $32, %bl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000071e je           LBB0_94
	0x80, 0xc3, 0xf5, //0x00000724 addb         $-11, %bl
	0x80, 0xfb, 0xfe, //0x00000727 cmpb         $-2, %bl
	0x0f, 0x82, 0xc5, 0x00, 0x00, 0x00, //0x0000072a jb           LBB0_111
	//0x00000730 .p2align 4, 0x90
	//0x00000730 LBB0_94
	0x49, 0x8d, 0x52, 0x02, //0x00000730 leaq         $2(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00000734 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000737 jae          LBB0_98
	0x41, 0x8a, 0x5c, 0x15, 0x00, //0x0000073d movb         (%r13,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000742 cmpb         $13, %bl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000745 je           LBB0_98
	0x80, 0xfb, 0x20, //0x0000074b cmpb         $32, %bl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000074e je           LBB0_98
	0x80, 0xc3, 0xf5, //0x00000754 addb         $-11, %bl
	0x80, 0xfb, 0xfe, //0x00000757 cmpb         $-2, %bl
	0x0f, 0x82, 0x95, 0x00, 0x00, 0x00, //0x0000075a jb           LBB0_111
	//0x00000760 .p2align 4, 0x90
	//0x00000760 LBB0_98
	0x49, 0x8d, 0x52, 0x03, //0x00000760 leaq         $3(%r10), %rdx
	0x48, 0x39, 0xc2, //0x00000764 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000767 jae          LBB0_102
	0x41, 0x8a, 0x5c, 0x15, 0x00, //0x0000076d movb         (%r13,%rdx), %bl
	0x80, 0xfb, 0x0d, //0x00000772 cmpb         $13, %bl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000775 je           LBB0_102
	0x80, 0xfb, 0x20, //0x0000077b cmpb         $32, %bl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000077e je           LBB0_102
	0x80, 0xc3, 0xf5, //0x00000784 addb         $-11, %bl
	0x80, 0xfb, 0xfe, //0x00000787 cmpb         $-2, %bl
	0x0f, 0x82, 0x65, 0x00, 0x00, 0x00, //0x0000078a jb           LBB0_111
	//0x00000790 .p2align 4, 0x90
	//0x00000790 LBB0_102
	0x49, 0x8d, 0x52, 0x04, //0x00000790 leaq         $4(%r10), %rdx
	0x48, 0x39, 0xd0, //0x00000794 cmpq         %rdx, %rax
	0x0f, 0x86, 0x89, 0x27, 0x00, 0x00, //0x00000797 jbe          LBB0_862
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x0000079d je           LBB0_108
	0x4a, 0x8d, 0x34, 0x28, //0x000007a3 leaq         (%rax,%r13), %rsi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000007a7 .p2align 4, 0x90
	//0x000007b0 LBB0_105
	0x41, 0x0f, 0xbe, 0x5c, 0x15, 0x00, //0x000007b0 movsbl       (%r13,%rdx), %ebx
	0x83, 0xfb, 0x20, //0x000007b6 cmpl         $32, %ebx
	0x0f, 0x87, 0x2d, 0x00, 0x00, 0x00, //0x000007b9 ja           LBB0_110
	0x49, 0x0f, 0xa3, 0xdc, //0x000007bf btq          %rbx, %r12
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000007c3 jae          LBB0_110
	0x48, 0x83, 0xc2, 0x01, //0x000007c9 addq         $1, %rdx
	0x48, 0x39, 0xd0, //0x000007cd cmpq         %rdx, %rax
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x000007d0 jne          LBB0_105
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x000007d6 jmp          LBB0_109
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000007db .p2align 4, 0x90
	//0x000007e0 LBB0_108
	0x4c, 0x01, 0xea, //0x000007e0 addq         %r13, %rdx
	0x48, 0x89, 0xd6, //0x000007e3 movq         %rdx, %rsi
	//0x000007e6 LBB0_109
	0x4c, 0x29, 0xee, //0x000007e6 subq         %r13, %rsi
	0x48, 0x89, 0xf2, //0x000007e9 movq         %rsi, %rdx
	//0x000007ec LBB0_110
	0x48, 0x39, 0xc2, //0x000007ec cmpq         %rax, %rdx
	0x0f, 0x83, 0xe0, 0x3d, 0x00, 0x00, //0x000007ef jae          LBB0_864
	//0x000007f5 LBB0_111
	0x4c, 0x8d, 0x52, 0x01, //0x000007f5 leaq         $1(%rdx), %r10
	0x4d, 0x89, 0x13, //0x000007f9 movq         %r10, (%r11)
	0x41, 0x80, 0x7c, 0x15, 0x00, 0x3a, //0x000007fc cmpb         $58, (%r13,%rdx)
	0x0f, 0x85, 0xcd, 0x3d, 0x00, 0x00, //0x00000802 jne          LBB0_864
	0x48, 0x85, 0xc9, //0x00000808 testq        %rcx, %rcx
	0x0f, 0x85, 0x0f, 0x25, 0x00, 0x00, //0x0000080b jne          LBB0_480
	0x49, 0x8b, 0x09, //0x00000811 movq         (%r9), %rcx
	0x49, 0x39, 0xca, //0x00000814 cmpq         %rcx, %r10
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x00000817 jae          LBB0_118
	0x43, 0x8a, 0x44, 0x15, 0x00, //0x0000081d movb         (%r13,%r10), %al
	0x3c, 0x0d, //0x00000822 cmpb         $13, %al
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00000824 je           LBB0_118
	0x3c, 0x20, //0x0000082a cmpb         $32, %al
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x0000082c je           LBB0_118
	0x04, 0xf5, //0x00000832 addb         $-11, %al
	0x3c, 0xfe, //0x00000834 cmpb         $-2, %al
	0x0f, 0x83, 0x14, 0x00, 0x00, 0x00, //0x00000836 jae          LBB0_118
	0x4c, 0x89, 0xd0, //0x0000083c movq         %r10, %rax
	0xe9, 0x73, 0x01, 0x00, 0x00, //0x0000083f jmp          LBB0_144
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000844 .p2align 4, 0x90
	//0x00000850 LBB0_118
	0x48, 0x8d, 0x42, 0x02, //0x00000850 leaq         $2(%rdx), %rax
	0x48, 0x39, 0xc8, //0x00000854 cmpq         %rcx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000857 jae          LBB0_122
	0x41, 0x8a, 0x5c, 0x05, 0x00, //0x0000085d movb         (%r13,%rax), %bl
	0x80, 0xfb, 0x0d, //0x00000862 cmpb         $13, %bl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000865 je           LBB0_122
	0x80, 0xfb, 0x20, //0x0000086b cmpb         $32, %bl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000086e je           LBB0_122
	0x80, 0xc3, 0xf5, //0x00000874 addb         $-11, %bl
	0x80, 0xfb, 0xfe, //0x00000877 cmpb         $-2, %bl
	0x0f, 0x82, 0x37, 0x01, 0x00, 0x00, //0x0000087a jb           LBB0_144
	//0x00000880 .p2align 4, 0x90
	//0x00000880 LBB0_122
	0x48, 0x8d, 0x42, 0x03, //0x00000880 leaq         $3(%rdx), %rax
	0x48, 0x39, 0xc8, //0x00000884 cmpq         %rcx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000887 jae          LBB0_126
	0x41, 0x8a, 0x5c, 0x05, 0x00, //0x0000088d movb         (%r13,%rax), %bl
	0x80, 0xfb, 0x0d, //0x00000892 cmpb         $13, %bl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000895 je           LBB0_126
	0x80, 0xfb, 0x20, //0x0000089b cmpb         $32, %bl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000089e je           LBB0_126
	0x80, 0xc3, 0xf5, //0x000008a4 addb         $-11, %bl
	0x80, 0xfb, 0xfe, //0x000008a7 cmpb         $-2, %bl
	0x0f, 0x82, 0x07, 0x01, 0x00, 0x00, //0x000008aa jb           LBB0_144
	//0x000008b0 .p2align 4, 0x90
	//0x000008b0 LBB0_126
	0x48, 0x8d, 0x42, 0x04, //0x000008b0 leaq         $4(%rdx), %rax
	0x48, 0x39, 0xc8, //0x000008b4 cmpq         %rcx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000008b7 jae          LBB0_130
	0x41, 0x8a, 0x5c, 0x05, 0x00, //0x000008bd movb         (%r13,%rax), %bl
	0x80, 0xfb, 0x0d, //0x000008c2 cmpb         $13, %bl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000008c5 je           LBB0_130
	0x80, 0xfb, 0x20, //0x000008cb cmpb         $32, %bl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000008ce je           LBB0_130
	0x80, 0xc3, 0xf5, //0x000008d4 addb         $-11, %bl
	0x80, 0xfb, 0xfe, //0x000008d7 cmpb         $-2, %bl
	0x0f, 0x82, 0xd7, 0x00, 0x00, 0x00, //0x000008da jb           LBB0_144
	//0x000008e0 .p2align 4, 0x90
	//0x000008e0 LBB0_130
	0x48, 0x83, 0xc2, 0x05, //0x000008e0 addq         $5, %rdx
	0x48, 0x39, 0xd1, //0x000008e4 cmpq         %rdx, %rcx
	0x0f, 0x86, 0x43, 0x00, 0x00, 0x00, //0x000008e7 jbe          LBB0_136
	0x0f, 0x84, 0xac, 0x00, 0x00, 0x00, //0x000008ed je           LBB0_141
	0x4a, 0x8d, 0x04, 0x29, //0x000008f3 leaq         (%rcx,%r13), %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008f7 .p2align 4, 0x90
	//0x00000900 LBB0_133
	0x41, 0x0f, 0xbe, 0x74, 0x15, 0x00, //0x00000900 movsbl       (%r13,%rdx), %esi
	0x83, 0xfe, 0x20, //0x00000906 cmpl         $32, %esi
	0x0f, 0x87, 0x9c, 0x00, 0x00, 0x00, //0x00000909 ja           LBB0_143
	0x49, 0x0f, 0xa3, 0xf4, //0x0000090f btq          %rsi, %r12
	0x0f, 0x83, 0x92, 0x00, 0x00, 0x00, //0x00000913 jae          LBB0_143
	0x48, 0x83, 0xc2, 0x01, //0x00000919 addq         $1, %rdx
	0x48, 0x39, 0xd1, //0x0000091d cmpq         %rdx, %rcx
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x00000920 jne          LBB0_133
	0xe9, 0x7a, 0x00, 0x00, 0x00, //0x00000926 jmp          LBB0_142
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000092b .p2align 4, 0x90
	//0x00000930 LBB0_136
	0x49, 0x89, 0x13, //0x00000930 movq         %rdx, (%r11)
	0x49, 0x89, 0xd2, //0x00000933 movq         %rdx, %r10
	0xe9, 0x83, 0x01, 0x00, 0x00, //0x00000936 jmp          LBB0_160
	//0x0000093b LBB0_137
	0x42, 0x8d, 0x04, 0x2a, //0x0000093b leal         (%rdx,%r13), %eax
	0x01, 0xd8, //0x0000093f addl         %ebx, %eax
	0x83, 0xc0, 0x01, //0x00000941 addl         $1, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00000944 andl         $4095, %eax
	0x3d, 0xf0, 0x0f, 0x00, 0x00, //0x00000949 cmpl         $4080, %eax
	0x0f, 0x87, 0xba, 0x02, 0x00, 0x00, //0x0000094e ja           LBB0_188
	0x41, 0x8d, 0x04, 0x1f, //0x00000954 leal         (%r15,%rbx), %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00000958 andl         $4095, %eax
	0x3d, 0xf1, 0x0f, 0x00, 0x00, //0x0000095d cmpl         $4081, %eax
	0x0f, 0x83, 0xa6, 0x02, 0x00, 0x00, //0x00000962 jae          LBB0_188
	0x48, 0x8b, 0x45, 0xc0, //0x00000968 movq         $-64(%rbp), %rax
	0xf3, 0x0f, 0x6f, 0x14, 0x18, //0x0000096c movdqu       (%rax,%rbx), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x1c, 0x1f, //0x00000971 movdqu       (%r15,%rbx), %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x00000977 pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x0000097b pmovmskb     %xmm3, %eax
	0x66, 0x83, 0xf8, 0xff, //0x0000097f cmpw         $-1, %ax
	0x0f, 0x84, 0x3d, 0xfd, 0xff, 0xff, //0x00000983 je           LBB0_85
	0xf7, 0xd0, //0x00000989 notl         %eax
	0x66, 0x0f, 0xbc, 0xc0, //0x0000098b bsfw         %ax, %ax
	0x0f, 0xb7, 0xc0, //0x0000098f movzwl       %ax, %eax
	0x31, 0xc9, //0x00000992 xorl         %ecx, %ecx
	0x48, 0x39, 0xc6, //0x00000994 cmpq         %rax, %rsi
	0x0f, 0x96, 0xc1, //0x00000997 setbe        %cl
	0xe9, 0x27, 0xfd, 0xff, 0xff, //0x0000099a jmp          LBB0_85
	//0x0000099f LBB0_141
	0x4c, 0x01, 0xea, //0x0000099f addq         %r13, %rdx
	0x48, 0x89, 0xd0, //0x000009a2 movq         %rdx, %rax
	//0x000009a5 LBB0_142
	0x4c, 0x29, 0xe8, //0x000009a5 subq         %r13, %rax
	0x48, 0x89, 0xc2, //0x000009a8 movq         %rax, %rdx
	//0x000009ab LBB0_143
	0x48, 0x89, 0xd0, //0x000009ab movq         %rdx, %rax
	0x48, 0x39, 0xca, //0x000009ae cmpq         %rcx, %rdx
	0x0f, 0x83, 0x07, 0x01, 0x00, 0x00, //0x000009b1 jae          LBB0_160
	//0x000009b7 LBB0_144
	0x4c, 0x8d, 0x50, 0x01, //0x000009b7 leaq         $1(%rax), %r10
	0x4d, 0x89, 0x13, //0x000009bb movq         %r10, (%r11)
	0x41, 0x0f, 0xbe, 0x4c, 0x05, 0x00, //0x000009be movsbl       (%r13,%rax), %ecx
	0x83, 0xf9, 0x7b, //0x000009c4 cmpl         $123, %ecx
	0x0f, 0x87, 0xb6, 0x07, 0x00, 0x00, //0x000009c7 ja           LBB0_228
	0x48, 0x8d, 0x15, 0xf8, 0x4b, 0x00, 0x00, //0x000009cd leaq         $19448(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x0c, 0x8a, //0x000009d4 movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x000009d8 addq         %rdx, %rcx
	0xff, 0xe1, //0x000009db jmpq         *%rcx
	//0x000009dd LBB0_146
	0x49, 0x8b, 0x09, //0x000009dd movq         (%r9), %rcx
	0x48, 0x89, 0xca, //0x000009e0 movq         %rcx, %rdx
	0x4c, 0x29, 0xd2, //0x000009e3 subq         %r10, %rdx
	0x48, 0x83, 0xfa, 0x10, //0x000009e6 cmpq         $16, %rdx
	0x0f, 0x82, 0x4f, 0x11, 0x00, 0x00, //0x000009ea jb           LBB0_314
	0x48, 0xf7, 0xd0, //0x000009f0 notq         %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000009f3 .p2align 4, 0x90
	//0x00000a00 LBB0_148
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x15, 0x00, //0x00000a00 movdqu       (%r13,%r10), %xmm2
	0x66, 0x0f, 0x6f, 0xda, //0x00000a07 movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x00000a0b pcmpeqb      %xmm13, %xmm3
	0x66, 0x41, 0x0f, 0xdb, 0xd6, //0x00000a10 pand         %xmm14, %xmm2
	0x66, 0x0f, 0x74, 0xd4, //0x00000a15 pcmpeqb      %xmm4, %xmm2
	0x66, 0x0f, 0xeb, 0xd3, //0x00000a19 por          %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00000a1d pmovmskb     %xmm2, %edx
	0x85, 0xd2, //0x00000a21 testl        %edx, %edx
	0x0f, 0x85, 0x87, 0x00, 0x00, 0x00, //0x00000a23 jne          LBB0_158
	0x49, 0x83, 0xc2, 0x10, //0x00000a29 addq         $16, %r10
	0x48, 0x8d, 0x14, 0x01, //0x00000a2d leaq         (%rcx,%rax), %rdx
	0x48, 0x83, 0xc2, 0xf0, //0x00000a31 addq         $-16, %rdx
	0x48, 0x83, 0xc0, 0xf0, //0x00000a35 addq         $-16, %rax
	0x48, 0x83, 0xfa, 0x0f, //0x00000a39 cmpq         $15, %rdx
	0x0f, 0x87, 0xbd, 0xff, 0xff, 0xff, //0x00000a3d ja           LBB0_148
	0x4d, 0x89, 0xea, //0x00000a43 movq         %r13, %r10
	0x49, 0x29, 0xc2, //0x00000a46 subq         %rax, %r10
	0x48, 0x01, 0xc1, //0x00000a49 addq         %rax, %rcx
	0x48, 0x89, 0xca, //0x00000a4c movq         %rcx, %rdx
	0x48, 0x85, 0xd2, //0x00000a4f testq        %rdx, %rdx
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00000a52 je           LBB0_157
	//0x00000a58 LBB0_151
	0x49, 0x8d, 0x0c, 0x12, //0x00000a58 leaq         (%r10,%rdx), %rcx
	0x31, 0xc0, //0x00000a5c xorl         %eax, %eax
	//0x00000a5e LBB0_152
	0x41, 0x0f, 0xb6, 0x1c, 0x02, //0x00000a5e movzbl       (%r10,%rax), %ebx
	0x80, 0xfb, 0x2c, //0x00000a63 cmpb         $44, %bl
	0x0f, 0x84, 0x8f, 0x0c, 0x00, 0x00, //0x00000a66 je           LBB0_262
	0x80, 0xfb, 0x7d, //0x00000a6c cmpb         $125, %bl
	0x0f, 0x84, 0x86, 0x0c, 0x00, 0x00, //0x00000a6f je           LBB0_262
	0x80, 0xfb, 0x5d, //0x00000a75 cmpb         $93, %bl
	0x0f, 0x84, 0x7d, 0x0c, 0x00, 0x00, //0x00000a78 je           LBB0_262
	0x48, 0x83, 0xc0, 0x01, //0x00000a7e addq         $1, %rax
	0x48, 0x39, 0xc2, //0x00000a82 cmpq         %rax, %rdx
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x00000a85 jne          LBB0_152
	0x49, 0x89, 0xca, //0x00000a8b movq         %rcx, %r10
	//0x00000a8e LBB0_157
	0x4d, 0x29, 0xea, //0x00000a8e subq         %r13, %r10
	0x4c, 0x8b, 0x5d, 0xd0, //0x00000a91 movq         $-48(%rbp), %r11
	0x4d, 0x89, 0x13, //0x00000a95 movq         %r10, (%r11)
	0x48, 0x8b, 0x7d, 0xb8, //0x00000a98 movq         $-72(%rbp), %rdi
	0xe9, 0x5b, 0x0b, 0x00, 0x00, //0x00000a9c jmp          LBB0_257
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000aa1 .p2align 4, 0x90
	//0x00000ab0 LBB0_158
	0x66, 0x0f, 0xbc, 0xca, //0x00000ab0 bsfw         %dx, %cx
	0x44, 0x0f, 0xb7, 0xd1, //0x00000ab4 movzwl       %cx, %r10d
	0x49, 0x29, 0xc2, //0x00000ab8 subq         %rax, %r10
	//0x00000abb LBB0_159
	0x4d, 0x89, 0x13, //0x00000abb movq         %r10, (%r11)
	//0x00000abe LBB0_160
	0x4c, 0x8b, 0x2f, //0x00000abe movq         (%rdi), %r13
	0x48, 0x8b, 0x47, 0x08, //0x00000ac1 movq         $8(%rdi), %rax
	0x49, 0x39, 0xc2, //0x00000ac5 cmpq         %rax, %r10
	0x0f, 0x83, 0x32, 0x00, 0x00, 0x00, //0x00000ac8 jae          LBB0_165
	0x43, 0x8a, 0x4c, 0x15, 0x00, //0x00000ace movb         (%r13,%r10), %cl
	0x80, 0xf9, 0x0d, //0x00000ad3 cmpb         $13, %cl
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00000ad6 je           LBB0_165
	0x80, 0xf9, 0x20, //0x00000adc cmpb         $32, %cl
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x00000adf je           LBB0_165
	0x80, 0xc1, 0xf5, //0x00000ae5 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000ae8 cmpb         $-2, %cl
	0x0f, 0x83, 0x0f, 0x00, 0x00, 0x00, //0x00000aeb jae          LBB0_165
	0x4c, 0x89, 0xd1, //0x00000af1 movq         %r10, %rcx
	0xe9, 0xfc, 0x00, 0x00, 0x00, //0x00000af4 jmp          LBB0_186
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000af9 .p2align 4, 0x90
	//0x00000b00 LBB0_165
	0x49, 0x8d, 0x4a, 0x01, //0x00000b00 leaq         $1(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00000b04 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000b07 jae          LBB0_169
	0x41, 0x8a, 0x54, 0x0d, 0x00, //0x00000b0d movb         (%r13,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00000b12 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000b15 je           LBB0_169
	0x80, 0xfa, 0x20, //0x00000b1b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00000b1e je           LBB0_169
	0x80, 0xc2, 0xf5, //0x00000b24 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00000b27 cmpb         $-2, %dl
	0x0f, 0x82, 0xc5, 0x00, 0x00, 0x00, //0x00000b2a jb           LBB0_186
	//0x00000b30 .p2align 4, 0x90
	//0x00000b30 LBB0_169
	0x49, 0x8d, 0x4a, 0x02, //0x00000b30 leaq         $2(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00000b34 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000b37 jae          LBB0_173
	0x41, 0x8a, 0x54, 0x0d, 0x00, //0x00000b3d movb         (%r13,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00000b42 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000b45 je           LBB0_173
	0x80, 0xfa, 0x20, //0x00000b4b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00000b4e je           LBB0_173
	0x80, 0xc2, 0xf5, //0x00000b54 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00000b57 cmpb         $-2, %dl
	0x0f, 0x82, 0x95, 0x00, 0x00, 0x00, //0x00000b5a jb           LBB0_186
	//0x00000b60 .p2align 4, 0x90
	//0x00000b60 LBB0_173
	0x49, 0x8d, 0x4a, 0x03, //0x00000b60 leaq         $3(%r10), %rcx
	0x48, 0x39, 0xc1, //0x00000b64 cmpq         %rax, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000b67 jae          LBB0_177
	0x41, 0x8a, 0x54, 0x0d, 0x00, //0x00000b6d movb         (%r13,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00000b72 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000b75 je           LBB0_177
	0x80, 0xfa, 0x20, //0x00000b7b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00000b7e je           LBB0_177
	0x80, 0xc2, 0xf5, //0x00000b84 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00000b87 cmpb         $-2, %dl
	0x0f, 0x82, 0x65, 0x00, 0x00, 0x00, //0x00000b8a jb           LBB0_186
	//0x00000b90 .p2align 4, 0x90
	//0x00000b90 LBB0_177
	0x49, 0x8d, 0x4a, 0x04, //0x00000b90 leaq         $4(%r10), %rcx
	0x48, 0x39, 0xc8, //0x00000b94 cmpq         %rcx, %rax
	0x0f, 0x86, 0x35, 0x3a, 0x00, 0x00, //0x00000b97 jbe          LBB0_863
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x00000b9d je           LBB0_183
	0x4a, 0x8d, 0x14, 0x28, //0x00000ba3 leaq         (%rax,%r13), %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000ba7 .p2align 4, 0x90
	//0x00000bb0 LBB0_180
	0x41, 0x0f, 0xbe, 0x74, 0x0d, 0x00, //0x00000bb0 movsbl       (%r13,%rcx), %esi
	0x83, 0xfe, 0x20, //0x00000bb6 cmpl         $32, %esi
	0x0f, 0x87, 0x2d, 0x00, 0x00, 0x00, //0x00000bb9 ja           LBB0_185
	0x49, 0x0f, 0xa3, 0xf4, //0x00000bbf btq          %rsi, %r12
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000bc3 jae          LBB0_185
	0x48, 0x83, 0xc1, 0x01, //0x00000bc9 addq         $1, %rcx
	0x48, 0x39, 0xc8, //0x00000bcd cmpq         %rcx, %rax
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x00000bd0 jne          LBB0_180
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00000bd6 jmp          LBB0_184
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00000bdb .p2align 4, 0x90
	//0x00000be0 LBB0_183
	0x4c, 0x01, 0xe9, //0x00000be0 addq         %r13, %rcx
	0x48, 0x89, 0xca, //0x00000be3 movq         %rcx, %rdx
	//0x00000be6 LBB0_184
	0x4c, 0x29, 0xea, //0x00000be6 subq         %r13, %rdx
	0x48, 0x89, 0xd1, //0x00000be9 movq         %rdx, %rcx
	//0x00000bec LBB0_185
	0x48, 0x39, 0xc1, //0x00000bec cmpq         %rax, %rcx
	0x0f, 0x83, 0xe0, 0x39, 0x00, 0x00, //0x00000bef jae          LBB0_864
	//0x00000bf5 LBB0_186
	0x4c, 0x8d, 0x51, 0x01, //0x00000bf5 leaq         $1(%rcx), %r10
	0x4d, 0x89, 0x13, //0x00000bf9 movq         %r10, (%r11)
	0x41, 0x8a, 0x44, 0x0d, 0x00, //0x00000bfc movb         (%r13,%rcx), %al
	0x3c, 0x2c, //0x00000c01 cmpb         $44, %al
	0x0f, 0x84, 0x29, 0xf7, 0xff, 0xff, //0x00000c03 je           LBB0_33
	0xe9, 0xde, 0x21, 0x00, 0x00, //0x00000c09 jmp          LBB0_187
	//0x00000c0e LBB0_188
	0x48, 0x8b, 0x75, 0xa0, //0x00000c0e movq         $-96(%rbp), %rsi
	0x48, 0x39, 0xde, //0x00000c12 cmpq         %rbx, %rsi
	0x48, 0x8b, 0x55, 0xc0, //0x00000c15 movq         $-64(%rbp), %rdx
	0x0f, 0x84, 0xa7, 0xfa, 0xff, 0xff, //0x00000c19 je           LBB0_85
	0x90, //0x00000c1f .p2align 4, 0x90
	//0x00000c20 LBB0_189
	0x0f, 0xb6, 0x04, 0x1a, //0x00000c20 movzbl       (%rdx,%rbx), %eax
	0x41, 0x3a, 0x04, 0x1f, //0x00000c24 cmpb         (%r15,%rbx), %al
	0x0f, 0x85, 0x96, 0xfa, 0xff, 0xff, //0x00000c28 jne          LBB0_84
	0x48, 0x83, 0xc3, 0x01, //0x00000c2e addq         $1, %rbx
	0x48, 0x39, 0xde, //0x00000c32 cmpq         %rbx, %rsi
	0x0f, 0x85, 0xe5, 0xff, 0xff, 0xff, //0x00000c35 jne          LBB0_189
	0xe9, 0x86, 0xfa, 0xff, 0xff, //0x00000c3b jmp          LBB0_85
	//0x00000c40 LBB0_191
	0x48, 0x83, 0xc0, 0x04, //0x00000c40 addq         $4, %rax
	0x49, 0x3b, 0x01, //0x00000c44 cmpq         (%r9), %rax
	0x0f, 0x87, 0x71, 0xfe, 0xff, 0xff, //0x00000c47 ja           LBB0_160
	0xe9, 0x31, 0x05, 0x00, 0x00, //0x00000c4d jmp          LBB0_228
	//0x00000c52 LBB0_192
	0x4d, 0x89, 0xf4, //0x00000c52 movq         %r14, %r12
	0x4d, 0x8b, 0x09, //0x00000c55 movq         (%r9), %r9
	0x4d, 0x89, 0xce, //0x00000c58 movq         %r9, %r14
	0x4d, 0x29, 0xd6, //0x00000c5b subq         %r10, %r14
	0x49, 0x83, 0xfe, 0x20, //0x00000c5e cmpq         $32, %r14
	0x0f, 0x8c, 0xef, 0x0e, 0x00, 0x00, //0x00000c62 jl           LBB0_316
	0x4e, 0x8d, 0x1c, 0x28, //0x00000c68 leaq         (%rax,%r13), %r11
	0x49, 0x29, 0xc1, //0x00000c6c subq         %rax, %r9
	0xbf, 0x1f, 0x00, 0x00, 0x00, //0x00000c6f movl         $31, %edi
	0x45, 0x31, 0xf6, //0x00000c74 xorl         %r14d, %r14d
	0x45, 0x31, 0xc0, //0x00000c77 xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000c7a .p2align 4, 0x90
	//0x00000c80 LBB0_194
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x33, 0x01, //0x00000c80 movdqu       $1(%r11,%r14), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x33, 0x11, //0x00000c87 movdqu       $17(%r11,%r14), %xmm3
	0x66, 0x0f, 0x6f, 0xea, //0x00000c8e movdqa       %xmm2, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00000c92 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x00000c96 pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x6f, 0xeb, //0x00000c9a movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00000c9e pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00000ca2 pmovmskb     %xmm5, %ebx
	0x48, 0xc1, 0xe3, 0x10, //0x00000ca6 shlq         $16, %rbx
	0x48, 0x09, 0xf3, //0x00000caa orq          %rsi, %rbx
	0x66, 0x0f, 0x74, 0xd1, //0x00000cad pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00000cb1 pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x74, 0xd9, //0x00000cb5 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00000cb9 pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe6, 0x10, //0x00000cbd shlq         $16, %rsi
	0x48, 0x09, 0xd6, //0x00000cc1 orq          %rdx, %rsi
	0x48, 0x89, 0xf2, //0x00000cc4 movq         %rsi, %rdx
	0x4c, 0x09, 0xc2, //0x00000cc7 orq          %r8, %rdx
	0x0f, 0x84, 0x40, 0x00, 0x00, 0x00, //0x00000cca je           LBB0_196
	0x44, 0x89, 0xc2, //0x00000cd0 movl         %r8d, %edx
	0xf7, 0xd2, //0x00000cd3 notl         %edx
	0x21, 0xf2, //0x00000cd5 andl         %esi, %edx
	0x44, 0x8d, 0x3c, 0x12, //0x00000cd7 leal         (%rdx,%rdx), %r15d
	0x45, 0x09, 0xc7, //0x00000cdb orl          %r8d, %r15d
	0x44, 0x89, 0xf9, //0x00000cde movl         %r15d, %ecx
	0xf7, 0xd1, //0x00000ce1 notl         %ecx
	0x21, 0xf1, //0x00000ce3 andl         %esi, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000ce5 andl         $-1431655766, %ecx
	0x45, 0x31, 0xc0, //0x00000ceb xorl         %r8d, %r8d
	0x01, 0xd1, //0x00000cee addl         %edx, %ecx
	0x41, 0x0f, 0x92, 0xc0, //0x00000cf0 setb         %r8b
	0x01, 0xc9, //0x00000cf4 addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x00000cf6 xorl         $1431655765, %ecx
	0x44, 0x21, 0xf9, //0x00000cfc andl         %r15d, %ecx
	0xf7, 0xd1, //0x00000cff notl         %ecx
	0x21, 0xcb, //0x00000d01 andl         %ecx, %ebx
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00000d03 jmp          LBB0_197
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000d08 .p2align 4, 0x90
	//0x00000d10 LBB0_196
	0x45, 0x31, 0xc0, //0x00000d10 xorl         %r8d, %r8d
	//0x00000d13 LBB0_197
	0x48, 0x85, 0xdb, //0x00000d13 testq        %rbx, %rbx
	0x0f, 0x85, 0xc4, 0x08, 0x00, 0x00, //0x00000d16 jne          LBB0_255
	0x49, 0x83, 0xc6, 0x20, //0x00000d1c addq         $32, %r14
	0x49, 0x8d, 0x0c, 0x39, //0x00000d20 leaq         (%r9,%rdi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00000d24 addq         $-32, %rcx
	0x48, 0x83, 0xc7, 0xe0, //0x00000d28 addq         $-32, %rdi
	0x48, 0x83, 0xf9, 0x3f, //0x00000d2c cmpq         $63, %rcx
	0x0f, 0x8f, 0x4a, 0xff, 0xff, 0xff, //0x00000d30 jg           LBB0_194
	0x4d, 0x85, 0xc0, //0x00000d36 testq        %r8, %r8
	0x0f, 0x85, 0xd9, 0x0f, 0x00, 0x00, //0x00000d39 jne          LBB0_337
	0x4b, 0x8d, 0x04, 0x1e, //0x00000d3f leaq         (%r14,%r11), %rax
	0x48, 0x83, 0xc0, 0x01, //0x00000d43 addq         $1, %rax
	0x49, 0xf7, 0xd6, //0x00000d47 notq         %r14
	0x4d, 0x01, 0xce, //0x00000d4a addq         %r9, %r14
	//0x00000d4d LBB0_201
	0x4d, 0x85, 0xf6, //0x00000d4d testq        %r14, %r14
	0x4c, 0x8b, 0x5d, 0xd0, //0x00000d50 movq         $-48(%rbp), %r11
	0x48, 0x8b, 0x7d, 0xb8, //0x00000d54 movq         $-72(%rbp), %rdi
	0x4c, 0x8b, 0x4d, 0xb0, //0x00000d58 movq         $-80(%rbp), %r9
	0x0f, 0x8f, 0x67, 0x09, 0x00, 0x00, //0x00000d5c jg           LBB0_259
	0xe9, 0xab, 0x0d, 0x00, 0x00, //0x00000d62 jmp          LBB0_313
	//0x00000d67 LBB0_202
	0x4d, 0x8b, 0x09, //0x00000d67 movq         (%r9), %r9
	0x4d, 0x29, 0xd1, //0x00000d6a subq         %r10, %r9
	0x4d, 0x01, 0xd5, //0x00000d6d addq         %r10, %r13
	0x45, 0x31, 0xd2, //0x00000d70 xorl         %r10d, %r10d
	0x45, 0x31, 0xdb, //0x00000d73 xorl         %r11d, %r11d
	0x45, 0x31, 0xf6, //0x00000d76 xorl         %r14d, %r14d
	0x31, 0xd2, //0x00000d79 xorl         %edx, %edx
	0xe9, 0x6b, 0x00, 0x00, 0x00, //0x00000d7b jmp          LBB0_204
	//0x00000d80 LBB0_203
	0x49, 0xc1, 0xf8, 0x3f, //0x00000d80 sarq         $63, %r8
	0x4c, 0x89, 0xf9, //0x00000d84 movq         %r15, %rcx
	0x48, 0xd1, 0xe9, //0x00000d87 shrq         %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000d8a movabsq      $6148914691236517205, %rax
	0x48, 0x21, 0xc1, //0x00000d94 andq         %rax, %rcx
	0x49, 0x29, 0xcf, //0x00000d97 subq         %rcx, %r15
	0x4c, 0x89, 0xf9, //0x00000d9a movq         %r15, %rcx
	0x48, 0xb8, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00000d9d movabsq      $3689348814741910323, %rax
	0x48, 0x21, 0xc1, //0x00000da7 andq         %rax, %rcx
	0x49, 0xc1, 0xef, 0x02, //0x00000daa shrq         $2, %r15
	0x49, 0x21, 0xc7, //0x00000dae andq         %rax, %r15
	0x49, 0x01, 0xcf, //0x00000db1 addq         %rcx, %r15
	0x4c, 0x89, 0xf9, //0x00000db4 movq         %r15, %rcx
	0x48, 0xc1, 0xe9, 0x04, //0x00000db7 shrq         $4, %rcx
	0x4c, 0x01, 0xf9, //0x00000dbb addq         %r15, %rcx
	0x48, 0xb8, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000dbe movabsq      $1085102592571150095, %rax
	0x48, 0x21, 0xc1, //0x00000dc8 andq         %rax, %rcx
	0x48, 0xb8, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00000dcb movabsq      $72340172838076673, %rax
	0x48, 0x0f, 0xaf, 0xc8, //0x00000dd5 imulq        %rax, %rcx
	0x48, 0xc1, 0xe9, 0x38, //0x00000dd9 shrq         $56, %rcx
	0x49, 0x01, 0xce, //0x00000ddd addq         %rcx, %r14
	0x49, 0x83, 0xc5, 0x40, //0x00000de0 addq         $64, %r13
	0x49, 0x83, 0xc1, 0xc0, //0x00000de4 addq         $-64, %r9
	0x4d, 0x89, 0xc2, //0x00000de8 movq         %r8, %r10
	//0x00000deb LBB0_204
	0x49, 0x83, 0xf9, 0x40, //0x00000deb cmpq         $64, %r9
	0x0f, 0x8c, 0x2c, 0x02, 0x00, 0x00, //0x00000def jl           LBB0_211
	//0x00000df5 LBB0_205
	0xf3, 0x41, 0x0f, 0x6f, 0x55, 0x00, //0x00000df5 movdqu       (%r13), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x6d, 0x10, //0x00000dfb movdqu       $16(%r13), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x7d, 0x20, //0x00000e01 movdqu       $32(%r13), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x75, 0x30, //0x00000e07 movdqu       $48(%r13), %xmm6
	0x66, 0x0f, 0x6f, 0xda, //0x00000e0d movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00000e11 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00000e15 pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdd, //0x00000e19 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00000e1d pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00000e21 pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xdf, //0x00000e25 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00000e29 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00000e2d pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x6f, 0xde, //0x00000e31 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00000e35 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00000e39 pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe7, 0x30, //0x00000e3d shlq         $48, %rdi
	0x48, 0xc1, 0xe6, 0x20, //0x00000e41 shlq         $32, %rsi
	0x48, 0x09, 0xfe, //0x00000e45 orq          %rdi, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x00000e48 shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x00000e4c orq          %rsi, %rcx
	0x48, 0x09, 0xc8, //0x00000e4f orq          %rcx, %rax
	0x48, 0x89, 0xc1, //0x00000e52 movq         %rax, %rcx
	0x4c, 0x09, 0xd9, //0x00000e55 orq          %r11, %rcx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00000e58 jne          LBB0_207
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000e5e movq         $-1, %rax
	0x45, 0x31, 0xdb, //0x00000e65 xorl         %r11d, %r11d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00000e68 jmp          LBB0_208
	//0x00000e6d LBB0_207
	0x4c, 0x89, 0xd9, //0x00000e6d movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00000e70 notq         %rcx
	0x48, 0x21, 0xc1, //0x00000e73 andq         %rax, %rcx
	0x48, 0x8d, 0x34, 0x09, //0x00000e76 leaq         (%rcx,%rcx), %rsi
	0x4c, 0x09, 0xde, //0x00000e7a orq          %r11, %rsi
	0x48, 0x89, 0xf7, //0x00000e7d movq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x00000e80 notq         %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000e83 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xd8, //0x00000e8d andq         %rbx, %rax
	0x48, 0x21, 0xf8, //0x00000e90 andq         %rdi, %rax
	0x45, 0x31, 0xdb, //0x00000e93 xorl         %r11d, %r11d
	0x48, 0x01, 0xc8, //0x00000e96 addq         %rcx, %rax
	0x41, 0x0f, 0x92, 0xc3, //0x00000e99 setb         %r11b
	0x48, 0x01, 0xc0, //0x00000e9d addq         %rax, %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000ea0 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xc8, //0x00000eaa xorq         %rcx, %rax
	0x48, 0x21, 0xf0, //0x00000ead andq         %rsi, %rax
	0x48, 0xf7, 0xd0, //0x00000eb0 notq         %rax
	//0x00000eb3 LBB0_208
	0x66, 0x0f, 0x6f, 0xde, //0x00000eb3 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00000eb7 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00000ebb pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x00000ebf shlq         $48, %rcx
	0x66, 0x0f, 0x6f, 0xdf, //0x00000ec3 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00000ec7 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00000ecb pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x00000ecf shlq         $32, %rsi
	0x48, 0x09, 0xce, //0x00000ed3 orq          %rcx, %rsi
	0x66, 0x0f, 0x6f, 0xdd, //0x00000ed6 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00000eda pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00000ede pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x10, //0x00000ee2 shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x00000ee6 orq          %rsi, %rcx
	0x66, 0x0f, 0x6f, 0xda, //0x00000ee9 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00000eed pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00000ef1 pmovmskb     %xmm3, %esi
	0x48, 0x09, 0xce, //0x00000ef5 orq          %rcx, %rsi
	0x48, 0x21, 0xc6, //0x00000ef8 andq         %rax, %rsi
	0x66, 0x48, 0x0f, 0x6e, 0xde, //0x00000efb movq         %rsi, %xmm3
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd9, 0x00, //0x00000f00 pclmulqdq    $0, %xmm9, %xmm3
	0x66, 0x49, 0x0f, 0x7e, 0xd8, //0x00000f07 movq         %xmm3, %r8
	0x4d, 0x31, 0xd0, //0x00000f0c xorq         %r10, %r8
	0x66, 0x0f, 0x6f, 0xda, //0x00000f0f movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00000f13 pcmpeqb      %xmm10, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xfb, //0x00000f18 pmovmskb     %xmm3, %r15d
	0x66, 0x0f, 0x6f, 0xdd, //0x00000f1d movdqa       %xmm5, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00000f21 pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00000f26 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x6f, 0xdf, //0x00000f2a movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00000f2e pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00000f33 pmovmskb     %xmm3, %edi
	0x66, 0x0f, 0x6f, 0xde, //0x00000f37 movdqa       %xmm6, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00000f3b pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x00000f40 pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00000f44 shlq         $48, %rbx
	0x48, 0xc1, 0xe7, 0x20, //0x00000f48 shlq         $32, %rdi
	0x48, 0x09, 0xdf, //0x00000f4c orq          %rbx, %rdi
	0x48, 0xc1, 0xe6, 0x10, //0x00000f4f shlq         $16, %rsi
	0x48, 0x09, 0xfe, //0x00000f53 orq          %rdi, %rsi
	0x49, 0x09, 0xf7, //0x00000f56 orq          %rsi, %r15
	0x4d, 0x89, 0xc2, //0x00000f59 movq         %r8, %r10
	0x49, 0xf7, 0xd2, //0x00000f5c notq         %r10
	0x4d, 0x21, 0xd7, //0x00000f5f andq         %r10, %r15
	0x66, 0x0f, 0x74, 0xd4, //0x00000f62 pcmpeqb      %xmm4, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00000f66 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x74, 0xec, //0x00000f6a pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00000f6e pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x74, 0xfc, //0x00000f72 pcmpeqb      %xmm4, %xmm7
	0x66, 0x0f, 0xd7, 0xcf, //0x00000f76 pmovmskb     %xmm7, %ecx
	0x66, 0x0f, 0x74, 0xf4, //0x00000f7a pcmpeqb      %xmm4, %xmm6
	0x66, 0x0f, 0xd7, 0xfe, //0x00000f7e pmovmskb     %xmm6, %edi
	0x48, 0xc1, 0xe7, 0x30, //0x00000f82 shlq         $48, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00000f86 shlq         $32, %rcx
	0x48, 0x09, 0xf9, //0x00000f8a orq          %rdi, %rcx
	0x48, 0xc1, 0xe3, 0x10, //0x00000f8d shlq         $16, %rbx
	0x48, 0x09, 0xcb, //0x00000f91 orq          %rcx, %rbx
	0x48, 0x09, 0xde, //0x00000f94 orq          %rbx, %rsi
	0x4c, 0x21, 0xd6, //0x00000f97 andq         %r10, %rsi
	0x0f, 0x84, 0xe0, 0xfd, 0xff, 0xff, //0x00000f9a je           LBB0_203
	//0x00000fa0 .p2align 4, 0x90
	//0x00000fa0 LBB0_209
	0x48, 0x8d, 0x5e, 0xff, //0x00000fa0 leaq         $-1(%rsi), %rbx
	0x48, 0x89, 0xd9, //0x00000fa4 movq         %rbx, %rcx
	0x4c, 0x21, 0xf9, //0x00000fa7 andq         %r15, %rcx
	0x48, 0x89, 0xcf, //0x00000faa movq         %rcx, %rdi
	0x48, 0xd1, 0xef, //0x00000fad shrq         %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000fb0 movabsq      $6148914691236517205, %rax
	0x48, 0x21, 0xc7, //0x00000fba andq         %rax, %rdi
	0x48, 0x29, 0xf9, //0x00000fbd subq         %rdi, %rcx
	0x48, 0x89, 0xcf, //0x00000fc0 movq         %rcx, %rdi
	0x48, 0xb8, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00000fc3 movabsq      $3689348814741910323, %rax
	0x48, 0x21, 0xc7, //0x00000fcd andq         %rax, %rdi
	0x48, 0xc1, 0xe9, 0x02, //0x00000fd0 shrq         $2, %rcx
	0x48, 0x21, 0xc1, //0x00000fd4 andq         %rax, %rcx
	0x48, 0x01, 0xf9, //0x00000fd7 addq         %rdi, %rcx
	0x48, 0x89, 0xcf, //0x00000fda movq         %rcx, %rdi
	0x48, 0xc1, 0xef, 0x04, //0x00000fdd shrq         $4, %rdi
	0x48, 0x01, 0xcf, //0x00000fe1 addq         %rcx, %rdi
	0x48, 0xb8, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000fe4 movabsq      $1085102592571150095, %rax
	0x48, 0x21, 0xc7, //0x00000fee andq         %rax, %rdi
	0x48, 0xb8, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00000ff1 movabsq      $72340172838076673, %rax
	0x48, 0x0f, 0xaf, 0xf8, //0x00000ffb imulq        %rax, %rdi
	0x48, 0xc1, 0xef, 0x38, //0x00000fff shrq         $56, %rdi
	0x4c, 0x01, 0xf7, //0x00001003 addq         %r14, %rdi
	0x48, 0x39, 0xd7, //0x00001006 cmpq         %rdx, %rdi
	0x0f, 0x86, 0x97, 0x05, 0x00, 0x00, //0x00001009 jbe          LBB0_254
	0x48, 0x83, 0xc2, 0x01, //0x0000100f addq         $1, %rdx
	0x48, 0x21, 0xde, //0x00001013 andq         %rbx, %rsi
	0x0f, 0x85, 0x84, 0xff, 0xff, 0xff, //0x00001016 jne          LBB0_209
	0xe9, 0x5f, 0xfd, 0xff, 0xff, //0x0000101c jmp          LBB0_203
	//0x00001021 LBB0_211
	0x4d, 0x85, 0xc9, //0x00001021 testq        %r9, %r9
	0x0f, 0x8e, 0xc9, 0x0c, 0x00, 0x00, //0x00001024 jle          LBB0_336
	0xf3, 0x44, 0x0f, 0x7f, 0x45, 0x80, //0x0000102a movdqu       %xmm8, $-128(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00001030 movdqu       %xmm8, $-144(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00001039 movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00001042 movdqu       %xmm8, $-176(%rbp)
	0x44, 0x89, 0xe8, //0x0000104b movl         %r13d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x0000104e andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00001053 cmpl         $4033, %eax
	0x0f, 0x82, 0x38, 0x00, 0x00, 0x00, //0x00001058 jb           LBB0_215
	0x49, 0x83, 0xf9, 0x20, //0x0000105e cmpq         $32, %r9
	0x0f, 0x82, 0x3d, 0x00, 0x00, 0x00, //0x00001062 jb           LBB0_216
	0x41, 0x0f, 0x10, 0x55, 0x00, //0x00001068 movups       (%r13), %xmm2
	0x0f, 0x11, 0x95, 0x50, 0xff, 0xff, 0xff, //0x0000106d movups       %xmm2, $-176(%rbp)
	0xf3, 0x41, 0x0f, 0x6f, 0x55, 0x10, //0x00001074 movdqu       $16(%r13), %xmm2
	0xf3, 0x0f, 0x7f, 0x95, 0x60, 0xff, 0xff, 0xff, //0x0000107a movdqu       %xmm2, $-160(%rbp)
	0x49, 0x83, 0xc5, 0x20, //0x00001082 addq         $32, %r13
	0x49, 0x8d, 0x71, 0xe0, //0x00001086 leaq         $-32(%r9), %rsi
	0x48, 0x8d, 0x85, 0x70, 0xff, 0xff, 0xff, //0x0000108a leaq         $-144(%rbp), %rax
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x00001091 jmp          LBB0_217
	//0x00001096 LBB0_215
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001096 movabsq      $4294977024, %r12
	0xe9, 0x50, 0xfd, 0xff, 0xff, //0x000010a0 jmp          LBB0_205
	//0x000010a5 LBB0_216
	0x48, 0x8d, 0x85, 0x50, 0xff, 0xff, 0xff, //0x000010a5 leaq         $-176(%rbp), %rax
	0x4c, 0x89, 0xce, //0x000010ac movq         %r9, %rsi
	//0x000010af LBB0_217
	0x48, 0x83, 0xfe, 0x10, //0x000010af cmpq         $16, %rsi
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x000010b3 jb           LBB0_218
	0xf3, 0x41, 0x0f, 0x6f, 0x55, 0x00, //0x000010b9 movdqu       (%r13), %xmm2
	0xf3, 0x0f, 0x7f, 0x10, //0x000010bf movdqu       %xmm2, (%rax)
	0x49, 0x83, 0xc5, 0x10, //0x000010c3 addq         $16, %r13
	0x48, 0x83, 0xc0, 0x10, //0x000010c7 addq         $16, %rax
	0x48, 0x83, 0xc6, 0xf0, //0x000010cb addq         $-16, %rsi
	0x48, 0x83, 0xfe, 0x08, //0x000010cf cmpq         $8, %rsi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x000010d3 jae          LBB0_225
	//0x000010d9 LBB0_219
	0x48, 0x83, 0xfe, 0x04, //0x000010d9 cmpq         $4, %rsi
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x000010dd jb           LBB0_220
	//0x000010e3 LBB0_226
	0x41, 0x8b, 0x4d, 0x00, //0x000010e3 movl         (%r13), %ecx
	0x89, 0x08, //0x000010e7 movl         %ecx, (%rax)
	0x49, 0x83, 0xc5, 0x04, //0x000010e9 addq         $4, %r13
	0x48, 0x83, 0xc0, 0x04, //0x000010ed addq         $4, %rax
	0x48, 0x83, 0xc6, 0xfc, //0x000010f1 addq         $-4, %rsi
	0x48, 0x83, 0xfe, 0x02, //0x000010f5 cmpq         $2, %rsi
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x000010f9 jae          LBB0_221
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x000010ff jmp          LBB0_222
	//0x00001104 LBB0_218
	0x48, 0x83, 0xfe, 0x08, //0x00001104 cmpq         $8, %rsi
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x00001108 jb           LBB0_219
	//0x0000110e LBB0_225
	0x49, 0x8b, 0x4d, 0x00, //0x0000110e movq         (%r13), %rcx
	0x48, 0x89, 0x08, //0x00001112 movq         %rcx, (%rax)
	0x49, 0x83, 0xc5, 0x08, //0x00001115 addq         $8, %r13
	0x48, 0x83, 0xc0, 0x08, //0x00001119 addq         $8, %rax
	0x48, 0x83, 0xc6, 0xf8, //0x0000111d addq         $-8, %rsi
	0x48, 0x83, 0xfe, 0x04, //0x00001121 cmpq         $4, %rsi
	0x0f, 0x83, 0xb8, 0xff, 0xff, 0xff, //0x00001125 jae          LBB0_226
	//0x0000112b LBB0_220
	0x48, 0x83, 0xfe, 0x02, //0x0000112b cmpq         $2, %rsi
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x0000112f jb           LBB0_222
	//0x00001135 LBB0_221
	0x41, 0x0f, 0xb7, 0x4d, 0x00, //0x00001135 movzwl       (%r13), %ecx
	0x66, 0x89, 0x08, //0x0000113a movw         %cx, (%rax)
	0x49, 0x83, 0xc5, 0x02, //0x0000113d addq         $2, %r13
	0x48, 0x83, 0xc0, 0x02, //0x00001141 addq         $2, %rax
	0x48, 0x83, 0xc6, 0xfe, //0x00001145 addq         $-2, %rsi
	//0x00001149 LBB0_222
	0x4c, 0x89, 0xe9, //0x00001149 movq         %r13, %rcx
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x0000114c leaq         $-176(%rbp), %r13
	0x48, 0x85, 0xf6, //0x00001153 testq        %rsi, %rsi
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001156 movabsq      $4294977024, %r12
	0x0f, 0x84, 0x8f, 0xfc, 0xff, 0xff, //0x00001160 je           LBB0_205
	0x8a, 0x09, //0x00001166 movb         (%rcx), %cl
	0x88, 0x08, //0x00001168 movb         %cl, (%rax)
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x0000116a leaq         $-176(%rbp), %r13
	0xe9, 0x7f, 0xfc, 0xff, 0xff, //0x00001171 jmp          LBB0_205
	//0x00001176 LBB0_227
	0x48, 0x83, 0xc0, 0x05, //0x00001176 addq         $5, %rax
	0x49, 0x3b, 0x01, //0x0000117a cmpq         (%r9), %rax
	0x0f, 0x87, 0x3b, 0xf9, 0xff, 0xff, //0x0000117d ja           LBB0_160
	//0x00001183 LBB0_228
	0x49, 0x89, 0x03, //0x00001183 movq         %rax, (%r11)
	0x49, 0x89, 0xc2, //0x00001186 movq         %rax, %r10
	0xe9, 0x30, 0xf9, 0xff, 0xff, //0x00001189 jmp          LBB0_160
	//0x0000118e LBB0_229
	0x4d, 0x8b, 0x09, //0x0000118e movq         (%r9), %r9
	0x4d, 0x29, 0xd1, //0x00001191 subq         %r10, %r9
	0x4d, 0x01, 0xd5, //0x00001194 addq         %r10, %r13
	0x45, 0x31, 0xd2, //0x00001197 xorl         %r10d, %r10d
	0x45, 0x31, 0xdb, //0x0000119a xorl         %r11d, %r11d
	0x45, 0x31, 0xf6, //0x0000119d xorl         %r14d, %r14d
	0x31, 0xd2, //0x000011a0 xorl         %edx, %edx
	0xe9, 0x6b, 0x00, 0x00, 0x00, //0x000011a2 jmp          LBB0_231
	//0x000011a7 LBB0_230
	0x49, 0xc1, 0xf8, 0x3f, //0x000011a7 sarq         $63, %r8
	0x4c, 0x89, 0xf9, //0x000011ab movq         %r15, %rcx
	0x48, 0xd1, 0xe9, //0x000011ae shrq         %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000011b1 movabsq      $6148914691236517205, %rax
	0x48, 0x21, 0xc1, //0x000011bb andq         %rax, %rcx
	0x49, 0x29, 0xcf, //0x000011be subq         %rcx, %r15
	0x4c, 0x89, 0xf9, //0x000011c1 movq         %r15, %rcx
	0x48, 0xb8, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x000011c4 movabsq      $3689348814741910323, %rax
	0x48, 0x21, 0xc1, //0x000011ce andq         %rax, %rcx
	0x49, 0xc1, 0xef, 0x02, //0x000011d1 shrq         $2, %r15
	0x49, 0x21, 0xc7, //0x000011d5 andq         %rax, %r15
	0x49, 0x01, 0xcf, //0x000011d8 addq         %rcx, %r15
	0x4c, 0x89, 0xf9, //0x000011db movq         %r15, %rcx
	0x48, 0xc1, 0xe9, 0x04, //0x000011de shrq         $4, %rcx
	0x4c, 0x01, 0xf9, //0x000011e2 addq         %r15, %rcx
	0x48, 0xb8, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x000011e5 movabsq      $1085102592571150095, %rax
	0x48, 0x21, 0xc1, //0x000011ef andq         %rax, %rcx
	0x48, 0xb8, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000011f2 movabsq      $72340172838076673, %rax
	0x48, 0x0f, 0xaf, 0xc8, //0x000011fc imulq        %rax, %rcx
	0x48, 0xc1, 0xe9, 0x38, //0x00001200 shrq         $56, %rcx
	0x49, 0x01, 0xce, //0x00001204 addq         %rcx, %r14
	0x49, 0x83, 0xc5, 0x40, //0x00001207 addq         $64, %r13
	0x49, 0x83, 0xc1, 0xc0, //0x0000120b addq         $-64, %r9
	0x4d, 0x89, 0xc2, //0x0000120f movq         %r8, %r10
	//0x00001212 LBB0_231
	0x49, 0x83, 0xf9, 0x40, //0x00001212 cmpq         $64, %r9
	0x0f, 0x8c, 0x35, 0x02, 0x00, 0x00, //0x00001216 jl           LBB0_238
	//0x0000121c LBB0_232
	0xf3, 0x41, 0x0f, 0x6f, 0x55, 0x00, //0x0000121c movdqu       (%r13), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x6d, 0x10, //0x00001222 movdqu       $16(%r13), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x7d, 0x20, //0x00001228 movdqu       $32(%r13), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x75, 0x30, //0x0000122e movdqu       $48(%r13), %xmm6
	0x66, 0x0f, 0x6f, 0xda, //0x00001234 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00001238 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x0000123c pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdd, //0x00001240 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00001244 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00001248 pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xdf, //0x0000124c movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00001250 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00001254 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x6f, 0xde, //0x00001258 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x0000125c pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00001260 pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe7, 0x30, //0x00001264 shlq         $48, %rdi
	0x48, 0xc1, 0xe6, 0x20, //0x00001268 shlq         $32, %rsi
	0x48, 0x09, 0xfe, //0x0000126c orq          %rdi, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x0000126f shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x00001273 orq          %rsi, %rcx
	0x48, 0x09, 0xc8, //0x00001276 orq          %rcx, %rax
	0x48, 0x89, 0xc1, //0x00001279 movq         %rax, %rcx
	0x4c, 0x09, 0xd9, //0x0000127c orq          %r11, %rcx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x0000127f jne          LBB0_234
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001285 movq         $-1, %rax
	0x45, 0x31, 0xdb, //0x0000128c xorl         %r11d, %r11d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x0000128f jmp          LBB0_235
	//0x00001294 LBB0_234
	0x4c, 0x89, 0xd9, //0x00001294 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00001297 notq         %rcx
	0x48, 0x21, 0xc1, //0x0000129a andq         %rax, %rcx
	0x48, 0x8d, 0x34, 0x09, //0x0000129d leaq         (%rcx,%rcx), %rsi
	0x4c, 0x09, 0xde, //0x000012a1 orq          %r11, %rsi
	0x48, 0x89, 0xf7, //0x000012a4 movq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x000012a7 notq         %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000012aa movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xd8, //0x000012b4 andq         %rbx, %rax
	0x48, 0x21, 0xf8, //0x000012b7 andq         %rdi, %rax
	0x45, 0x31, 0xdb, //0x000012ba xorl         %r11d, %r11d
	0x48, 0x01, 0xc8, //0x000012bd addq         %rcx, %rax
	0x41, 0x0f, 0x92, 0xc3, //0x000012c0 setb         %r11b
	0x48, 0x01, 0xc0, //0x000012c4 addq         %rax, %rax
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000012c7 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xc8, //0x000012d1 xorq         %rcx, %rax
	0x48, 0x21, 0xf0, //0x000012d4 andq         %rsi, %rax
	0x48, 0xf7, 0xd0, //0x000012d7 notq         %rax
	//0x000012da LBB0_235
	0x66, 0x0f, 0x6f, 0xde, //0x000012da movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000012de pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x000012e2 pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x000012e6 shlq         $48, %rcx
	0x66, 0x0f, 0x6f, 0xdf, //0x000012ea movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000012ee pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x000012f2 pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x000012f6 shlq         $32, %rsi
	0x48, 0x09, 0xce, //0x000012fa orq          %rcx, %rsi
	0x66, 0x0f, 0x6f, 0xdd, //0x000012fd movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001301 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00001305 pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x10, //0x00001309 shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x0000130d orq          %rsi, %rcx
	0x66, 0x0f, 0x6f, 0xda, //0x00001310 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00001314 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00001318 pmovmskb     %xmm3, %esi
	0x48, 0x09, 0xce, //0x0000131c orq          %rcx, %rsi
	0x48, 0x21, 0xc6, //0x0000131f andq         %rax, %rsi
	0x66, 0x48, 0x0f, 0x6e, 0xde, //0x00001322 movq         %rsi, %xmm3
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd9, 0x00, //0x00001327 pclmulqdq    $0, %xmm9, %xmm3
	0x66, 0x49, 0x0f, 0x7e, 0xd8, //0x0000132e movq         %xmm3, %r8
	0x4d, 0x31, 0xd0, //0x00001333 xorq         %r10, %r8
	0x66, 0x0f, 0x6f, 0xda, //0x00001336 movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x0000133a pcmpeqb      %xmm11, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xfb, //0x0000133f pmovmskb     %xmm3, %r15d
	0x66, 0x0f, 0x6f, 0xdd, //0x00001344 movdqa       %xmm5, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x00001348 pcmpeqb      %xmm11, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x0000134d pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x6f, 0xdf, //0x00001351 movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x00001355 pcmpeqb      %xmm11, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x0000135a pmovmskb     %xmm3, %edi
	0x66, 0x0f, 0x6f, 0xde, //0x0000135e movdqa       %xmm6, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x00001362 pcmpeqb      %xmm11, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x00001367 pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x0000136b shlq         $48, %rbx
	0x48, 0xc1, 0xe7, 0x20, //0x0000136f shlq         $32, %rdi
	0x48, 0x09, 0xdf, //0x00001373 orq          %rbx, %rdi
	0x48, 0xc1, 0xe6, 0x10, //0x00001376 shlq         $16, %rsi
	0x48, 0x09, 0xfe, //0x0000137a orq          %rdi, %rsi
	0x49, 0x09, 0xf7, //0x0000137d orq          %rsi, %r15
	0x4d, 0x89, 0xc2, //0x00001380 movq         %r8, %r10
	0x49, 0xf7, 0xd2, //0x00001383 notq         %r10
	0x4d, 0x21, 0xd7, //0x00001386 andq         %r10, %r15
	0x66, 0x41, 0x0f, 0x74, 0xd4, //0x00001389 pcmpeqb      %xmm12, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x0000138e pmovmskb     %xmm2, %esi
	0x66, 0x41, 0x0f, 0x74, 0xec, //0x00001392 pcmpeqb      %xmm12, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00001397 pmovmskb     %xmm5, %ebx
	0x66, 0x41, 0x0f, 0x74, 0xfc, //0x0000139b pcmpeqb      %xmm12, %xmm7
	0x66, 0x0f, 0xd7, 0xcf, //0x000013a0 pmovmskb     %xmm7, %ecx
	0x66, 0x41, 0x0f, 0x74, 0xf4, //0x000013a4 pcmpeqb      %xmm12, %xmm6
	0x66, 0x0f, 0xd7, 0xfe, //0x000013a9 pmovmskb     %xmm6, %edi
	0x48, 0xc1, 0xe7, 0x30, //0x000013ad shlq         $48, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x000013b1 shlq         $32, %rcx
	0x48, 0x09, 0xf9, //0x000013b5 orq          %rdi, %rcx
	0x48, 0xc1, 0xe3, 0x10, //0x000013b8 shlq         $16, %rbx
	0x48, 0x09, 0xcb, //0x000013bc orq          %rcx, %rbx
	0x48, 0x09, 0xde, //0x000013bf orq          %rbx, %rsi
	0x4c, 0x21, 0xd6, //0x000013c2 andq         %r10, %rsi
	0x0f, 0x84, 0xdc, 0xfd, 0xff, 0xff, //0x000013c5 je           LBB0_230
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000013cb .p2align 4, 0x90
	//0x000013d0 LBB0_236
	0x48, 0x8d, 0x5e, 0xff, //0x000013d0 leaq         $-1(%rsi), %rbx
	0x48, 0x89, 0xd9, //0x000013d4 movq         %rbx, %rcx
	0x4c, 0x21, 0xf9, //0x000013d7 andq         %r15, %rcx
	0x48, 0x89, 0xcf, //0x000013da movq         %rcx, %rdi
	0x48, 0xd1, 0xef, //0x000013dd shrq         %rdi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000013e0 movabsq      $6148914691236517205, %rax
	0x48, 0x21, 0xc7, //0x000013ea andq         %rax, %rdi
	0x48, 0x29, 0xf9, //0x000013ed subq         %rdi, %rcx
	0x48, 0x89, 0xcf, //0x000013f0 movq         %rcx, %rdi
	0x48, 0xb8, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x000013f3 movabsq      $3689348814741910323, %rax
	0x48, 0x21, 0xc7, //0x000013fd andq         %rax, %rdi
	0x48, 0xc1, 0xe9, 0x02, //0x00001400 shrq         $2, %rcx
	0x48, 0x21, 0xc1, //0x00001404 andq         %rax, %rcx
	0x48, 0x01, 0xf9, //0x00001407 addq         %rdi, %rcx
	0x48, 0x89, 0xcf, //0x0000140a movq         %rcx, %rdi
	0x48, 0xc1, 0xef, 0x04, //0x0000140d shrq         $4, %rdi
	0x48, 0x01, 0xcf, //0x00001411 addq         %rcx, %rdi
	0x48, 0xb8, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001414 movabsq      $1085102592571150095, %rax
	0x48, 0x21, 0xc7, //0x0000141e andq         %rax, %rdi
	0x48, 0xb8, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00001421 movabsq      $72340172838076673, %rax
	0x48, 0x0f, 0xaf, 0xf8, //0x0000142b imulq        %rax, %rdi
	0x48, 0xc1, 0xef, 0x38, //0x0000142f shrq         $56, %rdi
	0x4c, 0x01, 0xf7, //0x00001433 addq         %r14, %rdi
	0x48, 0x39, 0xd7, //0x00001436 cmpq         %rdx, %rdi
	0x0f, 0x86, 0x67, 0x01, 0x00, 0x00, //0x00001439 jbe          LBB0_254
	0x48, 0x83, 0xc2, 0x01, //0x0000143f addq         $1, %rdx
	0x48, 0x21, 0xde, //0x00001443 andq         %rbx, %rsi
	0x0f, 0x85, 0x84, 0xff, 0xff, 0xff, //0x00001446 jne          LBB0_236
	0xe9, 0x56, 0xfd, 0xff, 0xff, //0x0000144c jmp          LBB0_230
	//0x00001451 LBB0_238
	0x4d, 0x85, 0xc9, //0x00001451 testq        %r9, %r9
	0x0f, 0x8e, 0x99, 0x08, 0x00, 0x00, //0x00001454 jle          LBB0_336
	0xf3, 0x44, 0x0f, 0x7f, 0x45, 0x80, //0x0000145a movdqu       %xmm8, $-128(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00001460 movdqu       %xmm8, $-144(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00001469 movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00001472 movdqu       %xmm8, $-176(%rbp)
	0x44, 0x89, 0xe8, //0x0000147b movl         %r13d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x0000147e andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00001483 cmpl         $4033, %eax
	0x0f, 0x82, 0x38, 0x00, 0x00, 0x00, //0x00001488 jb           LBB0_242
	0x49, 0x83, 0xf9, 0x20, //0x0000148e cmpq         $32, %r9
	0x0f, 0x82, 0x3d, 0x00, 0x00, 0x00, //0x00001492 jb           LBB0_243
	0x41, 0x0f, 0x10, 0x55, 0x00, //0x00001498 movups       (%r13), %xmm2
	0x0f, 0x11, 0x95, 0x50, 0xff, 0xff, 0xff, //0x0000149d movups       %xmm2, $-176(%rbp)
	0xf3, 0x41, 0x0f, 0x6f, 0x55, 0x10, //0x000014a4 movdqu       $16(%r13), %xmm2
	0xf3, 0x0f, 0x7f, 0x95, 0x60, 0xff, 0xff, 0xff, //0x000014aa movdqu       %xmm2, $-160(%rbp)
	0x49, 0x83, 0xc5, 0x20, //0x000014b2 addq         $32, %r13
	0x49, 0x8d, 0x71, 0xe0, //0x000014b6 leaq         $-32(%r9), %rsi
	0x48, 0x8d, 0x85, 0x70, 0xff, 0xff, 0xff, //0x000014ba leaq         $-144(%rbp), %rax
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x000014c1 jmp          LBB0_244
	//0x000014c6 LBB0_242
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000014c6 movabsq      $4294977024, %r12
	0xe9, 0x47, 0xfd, 0xff, 0xff, //0x000014d0 jmp          LBB0_232
	//0x000014d5 LBB0_243
	0x48, 0x8d, 0x85, 0x50, 0xff, 0xff, 0xff, //0x000014d5 leaq         $-176(%rbp), %rax
	0x4c, 0x89, 0xce, //0x000014dc movq         %r9, %rsi
	//0x000014df LBB0_244
	0x48, 0x83, 0xfe, 0x10, //0x000014df cmpq         $16, %rsi
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x000014e3 jb           LBB0_245
	0xf3, 0x41, 0x0f, 0x6f, 0x55, 0x00, //0x000014e9 movdqu       (%r13), %xmm2
	0xf3, 0x0f, 0x7f, 0x10, //0x000014ef movdqu       %xmm2, (%rax)
	0x49, 0x83, 0xc5, 0x10, //0x000014f3 addq         $16, %r13
	0x48, 0x83, 0xc0, 0x10, //0x000014f7 addq         $16, %rax
	0x48, 0x83, 0xc6, 0xf0, //0x000014fb addq         $-16, %rsi
	0x48, 0x83, 0xfe, 0x08, //0x000014ff cmpq         $8, %rsi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00001503 jae          LBB0_252
	//0x00001509 LBB0_246
	0x48, 0x83, 0xfe, 0x04, //0x00001509 cmpq         $4, %rsi
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x0000150d jb           LBB0_247
	//0x00001513 LBB0_253
	0x41, 0x8b, 0x4d, 0x00, //0x00001513 movl         (%r13), %ecx
	0x89, 0x08, //0x00001517 movl         %ecx, (%rax)
	0x49, 0x83, 0xc5, 0x04, //0x00001519 addq         $4, %r13
	0x48, 0x83, 0xc0, 0x04, //0x0000151d addq         $4, %rax
	0x48, 0x83, 0xc6, 0xfc, //0x00001521 addq         $-4, %rsi
	0x48, 0x83, 0xfe, 0x02, //0x00001525 cmpq         $2, %rsi
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x00001529 jae          LBB0_248
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x0000152f jmp          LBB0_249
	//0x00001534 LBB0_245
	0x48, 0x83, 0xfe, 0x08, //0x00001534 cmpq         $8, %rsi
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x00001538 jb           LBB0_246
	//0x0000153e LBB0_252
	0x49, 0x8b, 0x4d, 0x00, //0x0000153e movq         (%r13), %rcx
	0x48, 0x89, 0x08, //0x00001542 movq         %rcx, (%rax)
	0x49, 0x83, 0xc5, 0x08, //0x00001545 addq         $8, %r13
	0x48, 0x83, 0xc0, 0x08, //0x00001549 addq         $8, %rax
	0x48, 0x83, 0xc6, 0xf8, //0x0000154d addq         $-8, %rsi
	0x48, 0x83, 0xfe, 0x04, //0x00001551 cmpq         $4, %rsi
	0x0f, 0x83, 0xb8, 0xff, 0xff, 0xff, //0x00001555 jae          LBB0_253
	//0x0000155b LBB0_247
	0x48, 0x83, 0xfe, 0x02, //0x0000155b cmpq         $2, %rsi
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x0000155f jb           LBB0_249
	//0x00001565 LBB0_248
	0x41, 0x0f, 0xb7, 0x4d, 0x00, //0x00001565 movzwl       (%r13), %ecx
	0x66, 0x89, 0x08, //0x0000156a movw         %cx, (%rax)
	0x49, 0x83, 0xc5, 0x02, //0x0000156d addq         $2, %r13
	0x48, 0x83, 0xc0, 0x02, //0x00001571 addq         $2, %rax
	0x48, 0x83, 0xc6, 0xfe, //0x00001575 addq         $-2, %rsi
	//0x00001579 LBB0_249
	0x4c, 0x89, 0xe9, //0x00001579 movq         %r13, %rcx
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x0000157c leaq         $-176(%rbp), %r13
	0x48, 0x85, 0xf6, //0x00001583 testq        %rsi, %rsi
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001586 movabsq      $4294977024, %r12
	0x0f, 0x84, 0x86, 0xfc, 0xff, 0xff, //0x00001590 je           LBB0_232
	0x8a, 0x09, //0x00001596 movb         (%rcx), %cl
	0x88, 0x08, //0x00001598 movb         %cl, (%rax)
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x0000159a leaq         $-176(%rbp), %r13
	0xe9, 0x76, 0xfc, 0xff, 0xff, //0x000015a1 jmp          LBB0_232
	//0x000015a6 LBB0_254
	0x48, 0x8b, 0x55, 0xb0, //0x000015a6 movq         $-80(%rbp), %rdx
	0x48, 0x8b, 0x02, //0x000015aa movq         (%rdx), %rax
	0x48, 0x0f, 0xbc, 0xce, //0x000015ad bsfq         %rsi, %rcx
	0x4c, 0x29, 0xc9, //0x000015b1 subq         %r9, %rcx
	0x49, 0x89, 0xd1, //0x000015b4 movq         %rdx, %r9
	0x4c, 0x8d, 0x14, 0x01, //0x000015b7 leaq         (%rcx,%rax), %r10
	0x49, 0x83, 0xc2, 0x01, //0x000015bb addq         $1, %r10
	0x4c, 0x8b, 0x5d, 0xd0, //0x000015bf movq         $-48(%rbp), %r11
	0x4d, 0x89, 0x13, //0x000015c3 movq         %r10, (%r11)
	0x48, 0x8b, 0x02, //0x000015c6 movq         (%rdx), %rax
	0x49, 0x39, 0xc2, //0x000015c9 cmpq         %rax, %r10
	0x4c, 0x0f, 0x47, 0xd0, //0x000015cc cmovaq       %rax, %r10
	0x4d, 0x89, 0x13, //0x000015d0 movq         %r10, (%r11)
	0x48, 0x8b, 0x7d, 0xb8, //0x000015d3 movq         $-72(%rbp), %rdi
	0x4c, 0x8b, 0x75, 0xc8, //0x000015d7 movq         $-56(%rbp), %r14
	0xe9, 0xde, 0xf4, 0xff, 0xff, //0x000015db jmp          LBB0_160
	//0x000015e0 LBB0_255
	0x0f, 0xbc, 0xcb, //0x000015e0 bsfl         %ebx, %ecx
	0x48, 0x01, 0xc8, //0x000015e3 addq         %rcx, %rax
	0x4d, 0x8d, 0x14, 0x06, //0x000015e6 leaq         (%r14,%rax), %r10
	0x49, 0x83, 0xc2, 0x02, //0x000015ea addq         $2, %r10
	0x4c, 0x8b, 0x5d, 0xd0, //0x000015ee movq         $-48(%rbp), %r11
	0x4d, 0x89, 0x13, //0x000015f2 movq         %r10, (%r11)
	//0x000015f5 LBB0_256
	0x48, 0x8b, 0x7d, 0xb8, //0x000015f5 movq         $-72(%rbp), %rdi
	0x4d, 0x89, 0xe6, //0x000015f9 movq         %r12, %r14
	//0x000015fc LBB0_257
	0x4c, 0x8b, 0x4d, 0xb0, //0x000015fc movq         $-80(%rbp), %r9
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001600 movabsq      $4294977024, %r12
	0xe9, 0xaf, 0xf4, 0xff, 0xff, //0x0000160a jmp          LBB0_160
	//0x0000160f LBB0_67
	0x4d, 0x01, 0xea, //0x0000160f addq         %r13, %r10
	0x48, 0x8b, 0x7d, 0xb8, //0x00001612 movq         $-72(%rbp), %rdi
	0x4c, 0x8b, 0x75, 0xc8, //0x00001616 movq         $-56(%rbp), %r14
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000161a movabsq      $4294977024, %r12
	0x4c, 0x8b, 0xbd, 0x48, 0xff, 0xff, 0xff, //0x00001624 movq         $-184(%rbp), %r15
	0x48, 0x83, 0xf9, 0x20, //0x0000162b cmpq         $32, %rcx
	0x0f, 0x82, 0xea, 0x00, 0x00, 0x00, //0x0000162f jb           LBB0_264
	//0x00001635 LBB0_68
	0xf3, 0x41, 0x0f, 0x6f, 0x12, //0x00001635 movdqu       (%r10), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x5a, 0x10, //0x0000163a movdqu       $16(%r10), %xmm3
	0x66, 0x0f, 0x6f, 0xea, //0x00001640 movdqa       %xmm2, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00001644 pcmpeqb      %xmm0, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xcd, //0x00001648 pmovmskb     %xmm5, %r9d
	0x66, 0x0f, 0x6f, 0xeb, //0x0000164d movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00001651 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xc5, //0x00001655 pmovmskb     %xmm5, %eax
	0x66, 0x0f, 0x74, 0xd1, //0x00001659 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x0000165d pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x74, 0xd9, //0x00001661 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x00001665 pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe0, 0x10, //0x00001669 shlq         $16, %rax
	0x49, 0x09, 0xc1, //0x0000166d orq          %rax, %r9
	0x48, 0xc1, 0xe3, 0x10, //0x00001670 shlq         $16, %rbx
	0x48, 0x09, 0xde, //0x00001674 orq          %rbx, %rsi
	0x0f, 0x85, 0xe3, 0x04, 0x00, 0x00, //0x00001677 jne          LBB0_317
	0x4d, 0x85, 0xdb, //0x0000167d testq        %r11, %r11
	0x0f, 0x85, 0xf6, 0x04, 0x00, 0x00, //0x00001680 jne          LBB0_319
	0x45, 0x31, 0xdb, //0x00001686 xorl         %r11d, %r11d
	0x4d, 0x85, 0xc9, //0x00001689 testq        %r9, %r9
	0x4c, 0x8b, 0x45, 0xa8, //0x0000168c movq         $-88(%rbp), %r8
	0x0f, 0x84, 0x32, 0x05, 0x00, 0x00, //0x00001690 je           LBB0_320
	//0x00001696 LBB0_71
	0x49, 0x0f, 0xbc, 0xc1, //0x00001696 bsfq         %r9, %rax
	0x4d, 0x29, 0xea, //0x0000169a subq         %r13, %r10
	0x49, 0x01, 0xc2, //0x0000169d addq         %rax, %r10
	0x49, 0x83, 0xc2, 0x01, //0x000016a0 addq         $1, %r10
	0x4c, 0x8b, 0x5d, 0xd0, //0x000016a4 movq         $-48(%rbp), %r11
	0x4c, 0x8b, 0x4d, 0xb0, //0x000016a8 movq         $-80(%rbp), %r9
	0xe9, 0x8b, 0xef, 0xff, 0xff, //0x000016ac jmp          LBB0_76
	//0x000016b1 LBB0_258
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x000016b1 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x000016b8 movl         $2, %esi
	0x48, 0x01, 0xf0, //0x000016bd addq         %rsi, %rax
	0x49, 0x01, 0xd6, //0x000016c0 addq         %rdx, %r14
	0x0f, 0x8e, 0x49, 0x04, 0x00, 0x00, //0x000016c3 jle          LBB0_313
	//0x000016c9 LBB0_259
	0x0f, 0xb6, 0x10, //0x000016c9 movzbl       (%rax), %edx
	0x80, 0xfa, 0x5c, //0x000016cc cmpb         $92, %dl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x000016cf je           LBB0_258
	0x80, 0xfa, 0x22, //0x000016d5 cmpb         $34, %dl
	0x0f, 0x84, 0x27, 0x04, 0x00, 0x00, //0x000016d8 je           LBB0_312
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000016de movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x000016e5 movl         $1, %esi
	0x48, 0x01, 0xf0, //0x000016ea addq         %rsi, %rax
	0x49, 0x01, 0xd6, //0x000016ed addq         %rdx, %r14
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x000016f0 jg           LBB0_259
	0xe9, 0x17, 0x04, 0x00, 0x00, //0x000016f6 jmp          LBB0_313
	//0x000016fb LBB0_262
	0x4d, 0x29, 0xea, //0x000016fb subq         %r13, %r10
	0x49, 0x01, 0xc2, //0x000016fe addq         %rax, %r10
	0xe9, 0xb5, 0xf3, 0xff, 0xff, //0x00001701 jmp          LBB0_159
	//0x00001706 LBB0_263
	0x48, 0xc7, 0x45, 0xa8, 0xff, 0xff, 0xff, 0xff, //0x00001706 movq         $-1, $-88(%rbp)
	0x45, 0x31, 0xdb, //0x0000170e xorl         %r11d, %r11d
	0x4c, 0x8b, 0x55, 0xc0, //0x00001711 movq         $-64(%rbp), %r10
	0x48, 0x83, 0xf9, 0x20, //0x00001715 cmpq         $32, %rcx
	0x0f, 0x83, 0x16, 0xff, 0xff, 0xff, //0x00001719 jae          LBB0_68
	//0x0000171f LBB0_264
	0x4c, 0x8b, 0x45, 0xa8, //0x0000171f movq         $-88(%rbp), %r8
	0xe9, 0xa8, 0x04, 0x00, 0x00, //0x00001723 jmp          LBB0_321
	//0x00001728 LBB0_265
	0x48, 0xc7, 0x85, 0x50, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, //0x00001728 movq         $0, $-176(%rbp)
	0x4b, 0x8d, 0x1c, 0x2a, //0x00001733 leaq         (%r10,%r13), %rbx
	0x48, 0x83, 0xc3, 0xff, //0x00001737 addq         $-1, %rbx
	0x48, 0x8b, 0x45, 0xa0, //0x0000173b movq         $-96(%rbp), %rax
	0x4d, 0x8d, 0x04, 0x07, //0x0000173f leaq         (%r15,%rax), %r8
	0x48, 0x8b, 0x55, 0xc0, //0x00001743 movq         $-64(%rbp), %rdx
	0x48, 0x39, 0xda, //0x00001747 cmpq         %rbx, %rdx
	0x0f, 0x83, 0xd4, 0x03, 0x00, 0x00, //0x0000174a jae          LBB0_297
	0x48, 0x85, 0xc0, //0x00001750 testq        %rax, %rax
	0x0f, 0x8e, 0xcb, 0x03, 0x00, 0x00, //0x00001753 jle          LBB0_297
	0x48, 0x89, 0x5d, 0xa8, //0x00001759 movq         %rbx, $-88(%rbp)
	//0x0000175d LBB0_268
	0x8a, 0x02, //0x0000175d movb         (%rdx), %al
	0x3c, 0x5c, //0x0000175f cmpb         $92, %al
	0x0f, 0x85, 0x56, 0x00, 0x00, 0x00, //0x00001761 jne          LBB0_273
	0x48, 0x89, 0xde, //0x00001767 movq         %rbx, %rsi
	0x48, 0x29, 0xd6, //0x0000176a subq         %rdx, %rsi
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000176d movq         $-1, %r12
	0x48, 0x85, 0xf6, //0x00001774 testq        %rsi, %rsi
	0x0f, 0x8e, 0xd3, 0x3b, 0x00, 0x00, //0x00001777 jle          LBB0_976
	0x49, 0x89, 0xd1, //0x0000177d movq         %rdx, %r9
	0x0f, 0xb6, 0x42, 0x01, //0x00001780 movzbl       $1(%rdx), %eax
	0x48, 0x8d, 0x0d, 0x05, 0x45, 0x00, 0x00, //0x00001784 leaq         $17669(%rip), %rcx  /* __UnquoteTab+0(%rip) */
	0x8a, 0x14, 0x08, //0x0000178b movb         (%rax,%rcx), %dl
	0x80, 0xfa, 0xff, //0x0000178e cmpb         $-1, %dl
	0x0f, 0x84, 0x3c, 0x00, 0x00, 0x00, //0x00001791 je           LBB0_275
	0x84, 0xd2, //0x00001797 testb        %dl, %dl
	0x0f, 0x84, 0xb9, 0x3b, 0x00, 0x00, //0x00001799 je           LBB0_977
	0x88, 0x95, 0x50, 0xff, 0xff, 0xff, //0x0000179f movb         %dl, $-176(%rbp)
	0x49, 0x83, 0xc1, 0x02, //0x000017a5 addq         $2, %r9
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000017a9 movl         $1, %eax
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000017ae movabsq      $4294977024, %r12
	0xe9, 0x31, 0x01, 0x00, 0x00, //0x000017b8 jmp          LBB0_286
	//0x000017bd LBB0_273
	0x41, 0x3a, 0x07, //0x000017bd cmpb         (%r15), %al
	0x0f, 0x85, 0x8a, 0x03, 0x00, 0x00, //0x000017c0 jne          LBB0_315
	0x48, 0x83, 0xc2, 0x01, //0x000017c6 addq         $1, %rdx
	0x49, 0x83, 0xc7, 0x01, //0x000017ca addq         $1, %r15
	0xe9, 0x9a, 0x01, 0x00, 0x00, //0x000017ce jmp          LBB0_295
	//0x000017d3 LBB0_275
	0x48, 0x83, 0xfe, 0x04, //0x000017d3 cmpq         $4, %rsi
	0x0f, 0x82, 0x8b, 0x3b, 0x00, 0x00, //0x000017d7 jb           LBB0_978
	0x4c, 0x89, 0xc8, //0x000017dd movq         %r9, %rax
	0x4d, 0x8d, 0x59, 0x02, //0x000017e0 leaq         $2(%r9), %r11
	0x41, 0x8b, 0x79, 0x02, //0x000017e4 movl         $2(%r9), %edi
	0x89, 0xfa, //0x000017e8 movl         %edi, %edx
	0xf7, 0xd2, //0x000017ea notl         %edx
	0x8d, 0x87, 0xd0, 0xcf, 0xcf, 0xcf, //0x000017ec leal         $-808464432(%rdi), %eax
	0x81, 0xe2, 0x80, 0x80, 0x80, 0x80, //0x000017f2 andl         $-2139062144, %edx
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000017f8 movq         $-2, %r12
	0x85, 0xc2, //0x000017ff testl        %eax, %edx
	0x0f, 0x85, 0x27, 0x3b, 0x00, 0x00, //0x00001801 jne          LBB0_981
	0x8d, 0x87, 0x19, 0x19, 0x19, 0x19, //0x00001807 leal         $421075225(%rdi), %eax
	0x09, 0xf8, //0x0000180d orl          %edi, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x0000180f testl        $-2139062144, %eax
	0x0f, 0x85, 0x14, 0x3b, 0x00, 0x00, //0x00001814 jne          LBB0_981
	0x89, 0xf8, //0x0000181a movl         %edi, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x0000181c andl         $2139062143, %eax
	0xbb, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001821 movl         $-1061109568, %ebx
	0x29, 0xc3, //0x00001826 subl         %eax, %ebx
	0x8d, 0x88, 0x46, 0x46, 0x46, 0x46, //0x00001828 leal         $1179010630(%rax), %ecx
	0x21, 0xd3, //0x0000182e andl         %edx, %ebx
	0x85, 0xcb, //0x00001830 testl        %ecx, %ebx
	0x0f, 0x85, 0xf6, 0x3a, 0x00, 0x00, //0x00001832 jne          LBB0_981
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001838 movl         $-522133280, %ecx
	0x29, 0xc1, //0x0000183d subl         %eax, %ecx
	0x05, 0x39, 0x39, 0x39, 0x39, //0x0000183f addl         $960051513, %eax
	0x21, 0xca, //0x00001844 andl         %ecx, %edx
	0x85, 0xc2, //0x00001846 testl        %eax, %edx
	0x0f, 0x85, 0x21, 0x3b, 0x00, 0x00, //0x00001848 jne          LBB0_982
	0x0f, 0xcf, //0x0000184e bswapl       %edi
	0x89, 0xf8, //0x00001850 movl         %edi, %eax
	0xc1, 0xe8, 0x04, //0x00001852 shrl         $4, %eax
	0xf7, 0xd0, //0x00001855 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00001857 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x0000185c leal         (%rax,%rax,8), %eax
	0x81, 0xe7, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000185f andl         $252645135, %edi
	0x01, 0xc7, //0x00001865 addl         %eax, %edi
	0x89, 0xfa, //0x00001867 movl         %edi, %edx
	0xc1, 0xea, 0x04, //0x00001869 shrl         $4, %edx
	0x09, 0xfa, //0x0000186c orl          %edi, %edx
	0x89, 0xd3, //0x0000186e movl         %edx, %ebx
	0xc1, 0xeb, 0x08, //0x00001870 shrl         $8, %ebx
	0x81, 0xe3, 0x00, 0xff, 0x00, 0x00, //0x00001873 andl         $65280, %ebx
	0x0f, 0xb6, 0xfa, //0x00001879 movzbl       %dl, %edi
	0x09, 0xdf, //0x0000187c orl          %ebx, %edi
	0x4d, 0x8d, 0x59, 0x06, //0x0000187e leaq         $6(%r9), %r11
	0x83, 0xff, 0x7f, //0x00001882 cmpl         $127, %edi
	0x0f, 0x86, 0xf9, 0x00, 0x00, 0x00, //0x00001885 jbe          LBB0_299
	0x81, 0xff, 0xff, 0x07, 0x00, 0x00, //0x0000188b cmpl         $2047, %edi
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001891 movabsq      $4294977024, %r12
	0x0f, 0x86, 0xf6, 0x00, 0x00, 0x00, //0x0000189b jbe          LBB0_300
	0x8d, 0x87, 0x00, 0x20, 0xff, 0xff, //0x000018a1 leal         $-57344(%rdi), %eax
	0x3d, 0xff, 0xf7, 0xff, 0xff, //0x000018a7 cmpl         $-2049, %eax
	0x0f, 0x87, 0x0b, 0x01, 0x00, 0x00, //0x000018ac ja           LBB0_301
	0xc1, 0xeb, 0x0c, //0x000018b2 shrl         $12, %ebx
	0x80, 0xcb, 0xe0, //0x000018b5 orb          $-32, %bl
	0x88, 0x9d, 0x50, 0xff, 0xff, 0xff, //0x000018b8 movb         %bl, $-176(%rbp)
	0xc1, 0xef, 0x06, //0x000018be shrl         $6, %edi
	0x40, 0x80, 0xe7, 0x3f, //0x000018c1 andb         $63, %dil
	0x40, 0x80, 0xcf, 0x80, //0x000018c5 orb          $-128, %dil
	0x40, 0x88, 0xbd, 0x51, 0xff, 0xff, 0xff, //0x000018c9 movb         %dil, $-175(%rbp)
	0x80, 0xe2, 0x3f, //0x000018d0 andb         $63, %dl
	0x80, 0xca, 0x80, //0x000018d3 orb          $-128, %dl
	0x88, 0x95, 0x52, 0xff, 0xff, 0xff, //0x000018d6 movb         %dl, $-174(%rbp)
	0xb8, 0x03, 0x00, 0x00, 0x00, //0x000018dc movl         $3, %eax
	0x89, 0xda, //0x000018e1 movl         %ebx, %edx
	//0x000018e3 LBB0_284
	0x4d, 0x89, 0xd9, //0x000018e3 movq         %r11, %r9
	0x4c, 0x8b, 0x5d, 0xd0, //0x000018e6 movq         $-48(%rbp), %r11
	//0x000018ea LBB0_285
	0x48, 0x8b, 0x5d, 0xa8, //0x000018ea movq         $-88(%rbp), %rbx
	//0x000018ee LBB0_286
	0x48, 0x8d, 0x34, 0x28, //0x000018ee leaq         (%rax,%rbp), %rsi
	0x48, 0x81, 0xc6, 0x50, 0xff, 0xff, 0xff, //0x000018f2 addq         $-176, %rsi
	0x4d, 0x39, 0xc7, //0x000018f9 cmpq         %r8, %r15
	0x0f, 0x83, 0x58, 0x00, 0x00, 0x00, //0x000018fc jae          LBB0_293
	0x48, 0x8d, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00001902 leaq         $-176(%rbp), %rax
	0x48, 0x39, 0xc6, //0x00001909 cmpq         %rax, %rsi
	0x0f, 0x86, 0x48, 0x00, 0x00, 0x00, //0x0000190c jbe          LBB0_293
	0x41, 0x38, 0x17, //0x00001912 cmpb         %dl, (%r15)
	0x0f, 0x85, 0x3f, 0x00, 0x00, 0x00, //0x00001915 jne          LBB0_293
	0x49, 0x83, 0xc7, 0x01, //0x0000191b addq         $1, %r15
	0x48, 0x8d, 0x85, 0x51, 0xff, 0xff, 0xff, //0x0000191f leaq         $-175(%rbp), %rax
	0x4c, 0x89, 0xff, //0x00001926 movq         %r15, %rdi
	//0x00001929 LBB0_290
	0x49, 0x89, 0xff, //0x00001929 movq         %rdi, %r15
	0x48, 0x89, 0xc2, //0x0000192c movq         %rax, %rdx
	0x4c, 0x39, 0xc7, //0x0000192f cmpq         %r8, %rdi
	0x0f, 0x83, 0x29, 0x00, 0x00, 0x00, //0x00001932 jae          LBB0_294
	0x48, 0x39, 0xf2, //0x00001938 cmpq         %rsi, %rdx
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x0000193b jae          LBB0_294
	0x41, 0x0f, 0xb6, 0x0f, //0x00001941 movzbl       (%r15), %ecx
	0x49, 0x8d, 0x7f, 0x01, //0x00001945 leaq         $1(%r15), %rdi
	0x48, 0x8d, 0x42, 0x01, //0x00001949 leaq         $1(%rdx), %rax
	0x3a, 0x0a, //0x0000194d cmpb         (%rdx), %cl
	0x0f, 0x84, 0xd4, 0xff, 0xff, 0xff, //0x0000194f je           LBB0_290
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00001955 jmp          LBB0_294
	//0x0000195a LBB0_293
	0x48, 0x8d, 0x95, 0x50, 0xff, 0xff, 0xff, //0x0000195a leaq         $-176(%rbp), %rdx
	//0x00001961 LBB0_294
	0x48, 0x39, 0xf2, //0x00001961 cmpq         %rsi, %rdx
	0x4c, 0x89, 0xca, //0x00001964 movq         %r9, %rdx
	0x0f, 0x85, 0xe3, 0x01, 0x00, 0x00, //0x00001967 jne          LBB0_315
	//0x0000196d LBB0_295
	0x48, 0x39, 0xda, //0x0000196d cmpq         %rbx, %rdx
	0x0f, 0x83, 0xae, 0x01, 0x00, 0x00, //0x00001970 jae          LBB0_297
	0x4d, 0x39, 0xc7, //0x00001976 cmpq         %r8, %r15
	0x0f, 0x82, 0xde, 0xfd, 0xff, 0xff, //0x00001979 jb           LBB0_268
	0xe9, 0xa0, 0x01, 0x00, 0x00, //0x0000197f jmp          LBB0_297
	//0x00001984 LBB0_299
	0x88, 0x95, 0x50, 0xff, 0xff, 0xff, //0x00001984 movb         %dl, $-176(%rbp)
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000198a movl         $1, %eax
	0x4d, 0x89, 0xd9, //0x0000198f movq         %r11, %r9
	0xe9, 0x5b, 0x01, 0x00, 0x00, //0x00001992 jmp          LBB0_311
	//0x00001997 LBB0_300
	0xc1, 0xef, 0x06, //0x00001997 shrl         $6, %edi
	0x40, 0x80, 0xcf, 0xc0, //0x0000199a orb          $-64, %dil
	0x40, 0x88, 0xbd, 0x50, 0xff, 0xff, 0xff, //0x0000199e movb         %dil, $-176(%rbp)
	0x80, 0xe2, 0x3f, //0x000019a5 andb         $63, %dl
	0x80, 0xca, 0x80, //0x000019a8 orb          $-128, %dl
	0x88, 0x95, 0x51, 0xff, 0xff, 0xff, //0x000019ab movb         %dl, $-175(%rbp)
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x000019b1 movl         $2, %eax
	0x89, 0xfa, //0x000019b6 movl         %edi, %edx
	0xe9, 0x26, 0xff, 0xff, 0xff, //0x000019b8 jmp          LBB0_284
	//0x000019bd LBB0_301
	0x49, 0xc7, 0xc4, 0xfc, 0xff, 0xff, 0xff, //0x000019bd movq         $-4, %r12
	0x48, 0x83, 0xfe, 0x06, //0x000019c4 cmpq         $6, %rsi
	0x0f, 0x82, 0xa1, 0x39, 0x00, 0x00, //0x000019c8 jb           LBB0_982
	0x81, 0xff, 0xff, 0xdb, 0x00, 0x00, //0x000019ce cmpl         $56319, %edi
	0x0f, 0x87, 0x95, 0x39, 0x00, 0x00, //0x000019d4 ja           LBB0_982
	0x41, 0x80, 0x3b, 0x5c, //0x000019da cmpb         $92, (%r11)
	0x0f, 0x85, 0x8b, 0x39, 0x00, 0x00, //0x000019de jne          LBB0_982
	0x41, 0x80, 0x79, 0x07, 0x75, //0x000019e4 cmpb         $117, $7(%r9)
	0x0f, 0x85, 0x80, 0x39, 0x00, 0x00, //0x000019e9 jne          LBB0_982
	0x4c, 0x89, 0xc8, //0x000019ef movq         %r9, %rax
	0x4d, 0x8d, 0x59, 0x08, //0x000019f2 leaq         $8(%r9), %r11
	0x41, 0x8b, 0x51, 0x08, //0x000019f6 movl         $8(%r9), %edx
	0x89, 0xd6, //0x000019fa movl         %edx, %esi
	0xf7, 0xd6, //0x000019fc notl         %esi
	0x8d, 0x82, 0xd0, 0xcf, 0xcf, 0xcf, //0x000019fe leal         $-808464432(%rdx), %eax
	0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x00001a04 andl         $-2139062144, %esi
	0x85, 0xc6, //0x00001a0a testl        %eax, %esi
	0x0f, 0x85, 0x1c, 0x39, 0x00, 0x00, //0x00001a0c jne          LBB0_981
	0x8d, 0x82, 0x19, 0x19, 0x19, 0x19, //0x00001a12 leal         $421075225(%rdx), %eax
	0x09, 0xd0, //0x00001a18 orl          %edx, %eax
	0xa9, 0x80, 0x80, 0x80, 0x80, //0x00001a1a testl        $-2139062144, %eax
	0x0f, 0x85, 0x09, 0x39, 0x00, 0x00, //0x00001a1f jne          LBB0_981
	0x89, 0xd0, //0x00001a25 movl         %edx, %eax
	0x25, 0x7f, 0x7f, 0x7f, 0x7f, //0x00001a27 andl         $2139062143, %eax
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x00001a2c movl         $-1061109568, %ecx
	0x29, 0xc1, //0x00001a31 subl         %eax, %ecx
	0x8d, 0x98, 0x46, 0x46, 0x46, 0x46, //0x00001a33 leal         $1179010630(%rax), %ebx
	0x21, 0xf1, //0x00001a39 andl         %esi, %ecx
	0x85, 0xd9, //0x00001a3b testl        %ebx, %ecx
	0x0f, 0x85, 0xeb, 0x38, 0x00, 0x00, //0x00001a3d jne          LBB0_981
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x00001a43 movl         $-522133280, %ecx
	0x29, 0xc1, //0x00001a48 subl         %eax, %ecx
	0x05, 0x39, 0x39, 0x39, 0x39, //0x00001a4a addl         $960051513, %eax
	0x21, 0xce, //0x00001a4f andl         %ecx, %esi
	0x85, 0xc6, //0x00001a51 testl        %eax, %esi
	0x0f, 0x85, 0xd5, 0x38, 0x00, 0x00, //0x00001a53 jne          LBB0_981
	0x0f, 0xca, //0x00001a59 bswapl       %edx
	0x89, 0xd0, //0x00001a5b movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x00001a5d shrl         $4, %eax
	0xf7, 0xd0, //0x00001a60 notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00001a62 andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00001a67 leal         (%rax,%rax,8), %eax
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001a6a andl         $252645135, %edx
	0x01, 0xc2, //0x00001a70 addl         %eax, %edx
	0x89, 0xd1, //0x00001a72 movl         %edx, %ecx
	0xc1, 0xe9, 0x04, //0x00001a74 shrl         $4, %ecx
	0x09, 0xd1, //0x00001a77 orl          %edx, %ecx
	0x0f, 0xb6, 0xc1, //0x00001a79 movzbl       %cl, %eax
	0x89, 0xca, //0x00001a7c movl         %ecx, %edx
	0xc1, 0xea, 0x08, //0x00001a7e shrl         $8, %edx
	0x81, 0xe2, 0x00, 0xff, 0x00, 0x00, //0x00001a81 andl         $65280, %edx
	0x8d, 0x0c, 0x02, //0x00001a87 leal         (%rdx,%rax), %ecx
	0x81, 0xc1, 0x00, 0x20, 0xff, 0xff, //0x00001a8a addl         $-57344, %ecx
	0x81, 0xf9, 0x00, 0xfc, 0xff, 0xff, //0x00001a90 cmpl         $-1024, %ecx
	0x0f, 0x82, 0xd3, 0x38, 0x00, 0x00, //0x00001a96 jb           LBB0_982
	0x09, 0xc2, //0x00001a9c orl          %eax, %edx
	0xc1, 0xe7, 0x0a, //0x00001a9e shll         $10, %edi
	0x89, 0xd0, //0x00001aa1 movl         %edx, %eax
	0x01, 0xf8, //0x00001aa3 addl         %edi, %eax
	0x8d, 0x0c, 0x3a, //0x00001aa5 leal         (%rdx,%rdi), %ecx
	0x81, 0xc1, 0x00, 0x24, 0xa0, 0xfc, //0x00001aa8 addl         $-56613888, %ecx
	0x89, 0xca, //0x00001aae movl         %ecx, %edx
	0xc1, 0xea, 0x12, //0x00001ab0 shrl         $18, %edx
	0x80, 0xca, 0xf0, //0x00001ab3 orb          $-16, %dl
	0x88, 0x95, 0x50, 0xff, 0xff, 0xff, //0x00001ab6 movb         %dl, $-176(%rbp)
	0x89, 0xce, //0x00001abc movl         %ecx, %esi
	0xc1, 0xee, 0x0c, //0x00001abe shrl         $12, %esi
	0x40, 0x80, 0xe6, 0x3f, //0x00001ac1 andb         $63, %sil
	0x40, 0x80, 0xce, 0x80, //0x00001ac5 orb          $-128, %sil
	0x40, 0x88, 0xb5, 0x51, 0xff, 0xff, 0xff, //0x00001ac9 movb         %sil, $-175(%rbp)
	0xc1, 0xe9, 0x06, //0x00001ad0 shrl         $6, %ecx
	0x80, 0xe1, 0x3f, //0x00001ad3 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00001ad6 orb          $-128, %cl
	0x88, 0x8d, 0x52, 0xff, 0xff, 0xff, //0x00001ad9 movb         %cl, $-174(%rbp)
	0x24, 0x3f, //0x00001adf andb         $63, %al
	0x0c, 0x80, //0x00001ae1 orb          $-128, %al
	0x88, 0x85, 0x53, 0xff, 0xff, 0xff, //0x00001ae3 movb         %al, $-173(%rbp)
	0x49, 0x83, 0xc1, 0x0c, //0x00001ae9 addq         $12, %r9
	0xb8, 0x04, 0x00, 0x00, 0x00, //0x00001aed movl         $4, %eax
	//0x00001af2 LBB0_311
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001af2 movq         $-48(%rbp), %r11
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001af6 movabsq      $4294977024, %r12
	0xe9, 0xe5, 0xfd, 0xff, 0xff, //0x00001b00 jmp          LBB0_285
	//0x00001b05 LBB0_312
	0x4c, 0x29, 0xe8, //0x00001b05 subq         %r13, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00001b08 addq         $1, %rax
	0x49, 0x89, 0x03, //0x00001b0c movq         %rax, (%r11)
	0x49, 0x89, 0xc2, //0x00001b0f movq         %rax, %r10
	//0x00001b12 LBB0_313
	0x4d, 0x89, 0xe6, //0x00001b12 movq         %r12, %r14
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001b15 movabsq      $4294977024, %r12
	0xe9, 0x9a, 0xef, 0xff, 0xff, //0x00001b1f jmp          LBB0_160
	//0x00001b24 LBB0_297
	0x48, 0x31, 0xda, //0x00001b24 xorq         %rbx, %rdx
	0x4d, 0x31, 0xc7, //0x00001b27 xorq         %r8, %r15
	0x31, 0xc9, //0x00001b2a xorl         %ecx, %ecx
	0x49, 0x09, 0xd7, //0x00001b2c orq          %rdx, %r15
	0x0f, 0x94, 0xc1, //0x00001b2f sete         %cl
	//0x00001b32 LBB0_298
	0x48, 0x8b, 0x7d, 0xb8, //0x00001b32 movq         $-72(%rbp), %rdi
	0x4c, 0x8b, 0x4d, 0xb0, //0x00001b36 movq         $-80(%rbp), %r9
	0xe9, 0x87, 0xeb, 0xff, 0xff, //0x00001b3a jmp          LBB0_85
	//0x00001b3f LBB0_314
	0x4d, 0x01, 0xea, //0x00001b3f addq         %r13, %r10
	0x48, 0x85, 0xd2, //0x00001b42 testq        %rdx, %rdx
	0x0f, 0x85, 0x0d, 0xef, 0xff, 0xff, //0x00001b45 jne          LBB0_151
	0xe9, 0x3e, 0xef, 0xff, 0xff, //0x00001b4b jmp          LBB0_157
	//0x00001b50 LBB0_315
	0x31, 0xc9, //0x00001b50 xorl         %ecx, %ecx
	0xe9, 0xdb, 0xff, 0xff, 0xff, //0x00001b52 jmp          LBB0_298
	//0x00001b57 LBB0_316
	0x4b, 0x8d, 0x04, 0x2a, //0x00001b57 leaq         (%r10,%r13), %rax
	0xe9, 0xed, 0xf1, 0xff, 0xff, //0x00001b5b jmp          LBB0_201
	//0x00001b60 LBB0_317
	0x48, 0x83, 0x7d, 0xa8, 0xff, //0x00001b60 cmpq         $-1, $-88(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00001b65 jne          LBB0_319
	0x4c, 0x89, 0xd0, //0x00001b6b movq         %r10, %rax
	0x4c, 0x29, 0xe8, //0x00001b6e subq         %r13, %rax
	0x48, 0x0f, 0xbc, 0xfe, //0x00001b71 bsfq         %rsi, %rdi
	0x48, 0x01, 0xc7, //0x00001b75 addq         %rax, %rdi
	0x48, 0x89, 0x7d, 0xa8, //0x00001b78 movq         %rdi, $-88(%rbp)
	//0x00001b7c LBB0_319
	0x44, 0x89, 0xd8, //0x00001b7c movl         %r11d, %eax
	0xf7, 0xd0, //0x00001b7f notl         %eax
	0x21, 0xf0, //0x00001b81 andl         %esi, %eax
	0x45, 0x8d, 0x04, 0x43, //0x00001b83 leal         (%r11,%rax,2), %r8d
	0x8d, 0x1c, 0x00, //0x00001b87 leal         (%rax,%rax), %ebx
	0xf7, 0xd3, //0x00001b8a notl         %ebx
	0x21, 0xf3, //0x00001b8c andl         %esi, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001b8e andl         $-1431655766, %ebx
	0x45, 0x31, 0xdb, //0x00001b94 xorl         %r11d, %r11d
	0x01, 0xc3, //0x00001b97 addl         %eax, %ebx
	0x41, 0x0f, 0x92, 0xc3, //0x00001b99 setb         %r11b
	0x01, 0xdb, //0x00001b9d addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00001b9f xorl         $1431655765, %ebx
	0x44, 0x21, 0xc3, //0x00001ba5 andl         %r8d, %ebx
	0xf7, 0xd3, //0x00001ba8 notl         %ebx
	0x41, 0x21, 0xd9, //0x00001baa andl         %ebx, %r9d
	0x48, 0x8b, 0x7d, 0xb8, //0x00001bad movq         $-72(%rbp), %rdi
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001bb1 movabsq      $4294977024, %r12
	0x4d, 0x85, 0xc9, //0x00001bbb testq        %r9, %r9
	0x4c, 0x8b, 0x45, 0xa8, //0x00001bbe movq         $-88(%rbp), %r8
	0x0f, 0x85, 0xce, 0xfa, 0xff, 0xff, //0x00001bc2 jne          LBB0_71
	//0x00001bc8 LBB0_320
	0x49, 0x83, 0xc2, 0x20, //0x00001bc8 addq         $32, %r10
	0x48, 0x83, 0xc1, 0xe0, //0x00001bcc addq         $-32, %rcx
	//0x00001bd0 LBB0_321
	0x4d, 0x85, 0xdb, //0x00001bd0 testq        %r11, %r11
	0x4c, 0x8b, 0x4d, 0xb0, //0x00001bd3 movq         $-80(%rbp), %r9
	0x0f, 0x85, 0xc6, 0x00, 0x00, 0x00, //0x00001bd7 jne          LBB0_334
	0x4c, 0x89, 0xc3, //0x00001bdd movq         %r8, %rbx
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001be0 movq         $-48(%rbp), %r11
	0x48, 0x85, 0xc9, //0x00001be4 testq        %rcx, %rcx
	0x0f, 0x84, 0x9e, 0x00, 0x00, 0x00, //0x00001be7 je           LBB0_333
	//0x00001bed LBB0_323
	0x48, 0x89, 0x5d, 0xa8, //0x00001bed movq         %rbx, $-88(%rbp)
	0x4c, 0x89, 0xee, //0x00001bf1 movq         %r13, %rsi
	0x48, 0xf7, 0xde, //0x00001bf4 negq         %rsi
	//0x00001bf7 LBB0_324
	0x31, 0xdb, //0x00001bf7 xorl         %ebx, %ebx
	//0x00001bf9 LBB0_325
	0x41, 0x0f, 0xb6, 0x04, 0x1a, //0x00001bf9 movzbl       (%r10,%rbx), %eax
	0x3c, 0x22, //0x00001bfe cmpb         $34, %al
	0x0f, 0x84, 0x7e, 0x00, 0x00, 0x00, //0x00001c00 je           LBB0_332
	0x3c, 0x5c, //0x00001c06 cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00001c08 je           LBB0_330
	0x48, 0x83, 0xc3, 0x01, //0x00001c0e addq         $1, %rbx
	0x48, 0x39, 0xd9, //0x00001c12 cmpq         %rbx, %rcx
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x00001c15 jne          LBB0_325
	0xe9, 0x73, 0x00, 0x00, 0x00, //0x00001c1b jmp          LBB0_328
	//0x00001c20 LBB0_330
	0x48, 0x8d, 0x41, 0xff, //0x00001c20 leaq         $-1(%rcx), %rax
	0x48, 0x39, 0xd8, //0x00001c24 cmpq         %rbx, %rax
	0x0f, 0x84, 0x82, 0x37, 0x00, 0x00, //0x00001c27 je           LBB0_988
	0x4a, 0x8d, 0x04, 0x16, //0x00001c2d leaq         (%rsi,%r10), %rax
	0x48, 0x01, 0xd8, //0x00001c31 addq         %rbx, %rax
	0x48, 0x8b, 0x7d, 0xa8, //0x00001c34 movq         $-88(%rbp), %rdi
	0x48, 0x83, 0xff, 0xff, //0x00001c38 cmpq         $-1, %rdi
	0x4c, 0x0f, 0x44, 0xc0, //0x00001c3c cmoveq       %rax, %r8
	0x48, 0x0f, 0x44, 0xf8, //0x00001c40 cmoveq       %rax, %rdi
	0x48, 0x89, 0x7d, 0xa8, //0x00001c44 movq         %rdi, $-88(%rbp)
	0x49, 0x01, 0xda, //0x00001c48 addq         %rbx, %r10
	0x49, 0x83, 0xc2, 0x02, //0x00001c4b addq         $2, %r10
	0x48, 0x89, 0xc8, //0x00001c4f movq         %rcx, %rax
	0x48, 0x29, 0xd8, //0x00001c52 subq         %rbx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00001c55 addq         $-2, %rax
	0x48, 0x83, 0xc1, 0xfe, //0x00001c59 addq         $-2, %rcx
	0x48, 0x39, 0xd9, //0x00001c5d cmpq         %rbx, %rcx
	0x48, 0x89, 0xc1, //0x00001c60 movq         %rax, %rcx
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001c63 movq         $-48(%rbp), %r11
	0x48, 0x8b, 0x7d, 0xb8, //0x00001c67 movq         $-72(%rbp), %rdi
	0x4c, 0x8b, 0x4d, 0xb0, //0x00001c6b movq         $-80(%rbp), %r9
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001c6f movabsq      $4294977024, %r12
	0x0f, 0x85, 0x78, 0xff, 0xff, 0xff, //0x00001c79 jne          LBB0_324
	0xe9, 0x69, 0x35, 0x00, 0x00, //0x00001c7f jmp          LBB0_964
	//0x00001c84 LBB0_332
	0x49, 0x01, 0xda, //0x00001c84 addq         %rbx, %r10
	0x49, 0x83, 0xc2, 0x01, //0x00001c87 addq         $1, %r10
	//0x00001c8b LBB0_333
	0x4d, 0x29, 0xea, //0x00001c8b subq         %r13, %r10
	0xe9, 0xa9, 0xe9, 0xff, 0xff, //0x00001c8e jmp          LBB0_76
	//0x00001c93 LBB0_328
	0x3c, 0x22, //0x00001c93 cmpb         $34, %al
	0x0f, 0x85, 0x52, 0x35, 0x00, 0x00, //0x00001c95 jne          LBB0_964
	0x49, 0x01, 0xca, //0x00001c9b addq         %rcx, %r10
	0xe9, 0xe8, 0xff, 0xff, 0xff, //0x00001c9e jmp          LBB0_333
	//0x00001ca3 LBB0_334
	0x48, 0x85, 0xc9, //0x00001ca3 testq        %rcx, %rcx
	0x0f, 0x84, 0x03, 0x37, 0x00, 0x00, //0x00001ca6 je           LBB0_988
	0x4c, 0x89, 0xeb, //0x00001cac movq         %r13, %rbx
	0x48, 0xf7, 0xd3, //0x00001caf notq         %rbx
	0x4c, 0x01, 0xd3, //0x00001cb2 addq         %r10, %rbx
	0x49, 0x83, 0xf8, 0xff, //0x00001cb5 cmpq         $-1, %r8
	0x4c, 0x89, 0xc0, //0x00001cb9 movq         %r8, %rax
	0x48, 0x0f, 0x44, 0xc3, //0x00001cbc cmoveq       %rbx, %rax
	0x49, 0x0f, 0x45, 0xd8, //0x00001cc0 cmovneq      %r8, %rbx
	0x49, 0x83, 0xc2, 0x01, //0x00001cc4 addq         $1, %r10
	0x48, 0x83, 0xc1, 0xff, //0x00001cc8 addq         $-1, %rcx
	0x49, 0x89, 0xc0, //0x00001ccc movq         %rax, %r8
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001ccf movq         $-48(%rbp), %r11
	0x48, 0x8b, 0x7d, 0xb8, //0x00001cd3 movq         $-72(%rbp), %rdi
	0x4c, 0x8b, 0x4d, 0xb0, //0x00001cd7 movq         $-80(%rbp), %r9
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001cdb movabsq      $4294977024, %r12
	0x48, 0x85, 0xc9, //0x00001ce5 testq        %rcx, %rcx
	0x0f, 0x85, 0xff, 0xfe, 0xff, 0xff, //0x00001ce8 jne          LBB0_323
	0xe9, 0x98, 0xff, 0xff, 0xff, //0x00001cee jmp          LBB0_333
	//0x00001cf3 LBB0_336
	0x4c, 0x8b, 0x4d, 0xb0, //0x00001cf3 movq         $-80(%rbp), %r9
	0x4d, 0x8b, 0x11, //0x00001cf7 movq         (%r9), %r10
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001cfa movq         $-48(%rbp), %r11
	0x4d, 0x89, 0x13, //0x00001cfe movq         %r10, (%r11)
	0x48, 0x8b, 0x7d, 0xb8, //0x00001d01 movq         $-72(%rbp), %rdi
	0x4c, 0x8b, 0x75, 0xc8, //0x00001d05 movq         $-56(%rbp), %r14
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001d09 movabsq      $4294977024, %r12
	0xe9, 0xa6, 0xed, 0xff, 0xff, //0x00001d13 jmp          LBB0_160
	//0x00001d18 LBB0_337
	0x49, 0x8d, 0x41, 0xff, //0x00001d18 leaq         $-1(%r9), %rax
	0x4c, 0x39, 0xf0, //0x00001d1c cmpq         %r14, %rax
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x00001d1f jne          LBB0_339
	0x4c, 0x8b, 0x5d, 0xd0, //0x00001d25 movq         $-48(%rbp), %r11
	0xe9, 0xc7, 0xf8, 0xff, 0xff, //0x00001d29 jmp          LBB0_256
	//0x00001d2e LBB0_339
	0x4b, 0x8d, 0x04, 0x1e, //0x00001d2e leaq         (%r14,%r11), %rax
	0x48, 0x83, 0xc0, 0x02, //0x00001d32 addq         $2, %rax
	0x4d, 0x29, 0xf1, //0x00001d36 subq         %r14, %r9
	0x49, 0x83, 0xc1, 0xfe, //0x00001d39 addq         $-2, %r9
	0x4d, 0x89, 0xce, //0x00001d3d movq         %r9, %r14
	0xe9, 0x08, 0xf0, 0xff, 0xff, //0x00001d40 jmp          LBB0_201
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001d45 .p2align 4, 0x90
	//0x00001d50 LBB0_340
	0x80, 0xf9, 0x5b, //0x00001d50 cmpb         $91, %cl
	0x0f, 0x85, 0x7c, 0x28, 0x00, 0x00, //0x00001d53 jne          LBB0_864
	0x49, 0x8b, 0x46, 0x08, //0x00001d59 movq         $8(%r14), %rax
	0x4c, 0x8b, 0x38, //0x00001d5d movq         (%rax), %r15
	0x4d, 0x85, 0xff, //0x00001d60 testq        %r15, %r15
	0x0f, 0x88, 0xc5, 0x11, 0x00, 0x00, //0x00001d63 js           LBB0_548
	0x49, 0x8b, 0x09, //0x00001d69 movq         (%r9), %rcx
	0x49, 0x39, 0xca, //0x00001d6c cmpq         %rcx, %r10
	0x0f, 0x83, 0x2b, 0x00, 0x00, 0x00, //0x00001d6f jae          LBB0_347
	0x43, 0x8a, 0x44, 0x15, 0x00, //0x00001d75 movb         (%r13,%r10), %al
	0x3c, 0x0d, //0x00001d7a cmpb         $13, %al
	0x0f, 0x84, 0x1e, 0x00, 0x00, 0x00, //0x00001d7c je           LBB0_347
	0x3c, 0x20, //0x00001d82 cmpb         $32, %al
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001d84 je           LBB0_347
	0x04, 0xf5, //0x00001d8a addb         $-11, %al
	0x3c, 0xfe, //0x00001d8c cmpb         $-2, %al
	0x0f, 0x83, 0x0c, 0x00, 0x00, 0x00, //0x00001d8e jae          LBB0_347
	0x4c, 0x89, 0xd2, //0x00001d94 movq         %r10, %rdx
	0xe9, 0xfc, 0x00, 0x00, 0x00, //0x00001d97 jmp          LBB0_369
	0x90, 0x90, 0x90, 0x90, //0x00001d9c .p2align 4, 0x90
	//0x00001da0 LBB0_347
	0x49, 0x8d, 0x52, 0x01, //0x00001da0 leaq         $1(%r10), %rdx
	0x48, 0x39, 0xca, //0x00001da4 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001da7 jae          LBB0_351
	0x41, 0x8a, 0x44, 0x15, 0x00, //0x00001dad movb         (%r13,%rdx), %al
	0x3c, 0x0d, //0x00001db2 cmpb         $13, %al
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001db4 je           LBB0_351
	0x3c, 0x20, //0x00001dba cmpb         $32, %al
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001dbc je           LBB0_351
	0x04, 0xf5, //0x00001dc2 addb         $-11, %al
	0x3c, 0xfe, //0x00001dc4 cmpb         $-2, %al
	0x0f, 0x82, 0xcc, 0x00, 0x00, 0x00, //0x00001dc6 jb           LBB0_369
	0x90, 0x90, 0x90, 0x90, //0x00001dcc .p2align 4, 0x90
	//0x00001dd0 LBB0_351
	0x49, 0x8d, 0x52, 0x02, //0x00001dd0 leaq         $2(%r10), %rdx
	0x48, 0x39, 0xca, //0x00001dd4 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001dd7 jae          LBB0_355
	0x41, 0x8a, 0x44, 0x15, 0x00, //0x00001ddd movb         (%r13,%rdx), %al
	0x3c, 0x0d, //0x00001de2 cmpb         $13, %al
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001de4 je           LBB0_355
	0x3c, 0x20, //0x00001dea cmpb         $32, %al
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001dec je           LBB0_355
	0x04, 0xf5, //0x00001df2 addb         $-11, %al
	0x3c, 0xfe, //0x00001df4 cmpb         $-2, %al
	0x0f, 0x82, 0x9c, 0x00, 0x00, 0x00, //0x00001df6 jb           LBB0_369
	0x90, 0x90, 0x90, 0x90, //0x00001dfc .p2align 4, 0x90
	//0x00001e00 LBB0_355
	0x49, 0x8d, 0x52, 0x03, //0x00001e00 leaq         $3(%r10), %rdx
	0x48, 0x39, 0xca, //0x00001e04 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001e07 jae          LBB0_359
	0x41, 0x8a, 0x44, 0x15, 0x00, //0x00001e0d movb         (%r13,%rdx), %al
	0x3c, 0x0d, //0x00001e12 cmpb         $13, %al
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001e14 je           LBB0_359
	0x3c, 0x20, //0x00001e1a cmpb         $32, %al
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001e1c je           LBB0_359
	0x04, 0xf5, //0x00001e22 addb         $-11, %al
	0x3c, 0xfe, //0x00001e24 cmpb         $-2, %al
	0x0f, 0x82, 0x6c, 0x00, 0x00, 0x00, //0x00001e26 jb           LBB0_369
	0x90, 0x90, 0x90, 0x90, //0x00001e2c .p2align 4, 0x90
	//0x00001e30 LBB0_359
	0x49, 0x8d, 0x52, 0x04, //0x00001e30 leaq         $4(%r10), %rdx
	0x48, 0x39, 0xd1, //0x00001e34 cmpq         %rdx, %rcx
	0x0f, 0x86, 0x3e, 0x00, 0x00, 0x00, //0x00001e37 jbe          LBB0_365
	0x0f, 0x84, 0x40, 0x00, 0x00, 0x00, //0x00001e3d je           LBB0_366
	0x4a, 0x8d, 0x04, 0x29, //0x00001e43 leaq         (%rcx,%r13), %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001e47 .p2align 4, 0x90
	//0x00001e50 LBB0_362
	0x41, 0x0f, 0xbe, 0x74, 0x15, 0x00, //0x00001e50 movsbl       (%r13,%rdx), %esi
	0x83, 0xfe, 0x20, //0x00001e56 cmpl         $32, %esi
	0x0f, 0x87, 0x30, 0x00, 0x00, 0x00, //0x00001e59 ja           LBB0_368
	0x49, 0x0f, 0xa3, 0xf4, //0x00001e5f btq          %rsi, %r12
	0x0f, 0x83, 0x26, 0x00, 0x00, 0x00, //0x00001e63 jae          LBB0_368
	0x48, 0x83, 0xc2, 0x01, //0x00001e69 addq         $1, %rdx
	0x48, 0x39, 0xd1, //0x00001e6d cmpq         %rdx, %rcx
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x00001e70 jne          LBB0_362
	0xe9, 0x0e, 0x00, 0x00, 0x00, //0x00001e76 jmp          LBB0_367
	//0x00001e7b LBB0_365
	0x49, 0x89, 0xd2, //0x00001e7b movq         %rdx, %r10
	0xe9, 0x28, 0x00, 0x00, 0x00, //0x00001e7e jmp          LBB0_370
	//0x00001e83 LBB0_366
	0x4c, 0x01, 0xea, //0x00001e83 addq         %r13, %rdx
	0x48, 0x89, 0xd0, //0x00001e86 movq         %rdx, %rax
	//0x00001e89 LBB0_367
	0x4c, 0x29, 0xe8, //0x00001e89 subq         %r13, %rax
	0x48, 0x89, 0xc2, //0x00001e8c movq         %rax, %rdx
	//0x00001e8f LBB0_368
	0x48, 0x39, 0xca, //0x00001e8f cmpq         %rcx, %rdx
	0x0f, 0x83, 0x13, 0x00, 0x00, 0x00, //0x00001e92 jae          LBB0_370
	//0x00001e98 LBB0_369
	0x4c, 0x8d, 0x52, 0x01, //0x00001e98 leaq         $1(%rdx), %r10
	0x4d, 0x89, 0x13, //0x00001e9c movq         %r10, (%r11)
	0x41, 0x80, 0x7c, 0x15, 0x00, 0x5d, //0x00001e9f cmpb         $93, (%r13,%rdx)
	0x0f, 0x84, 0x68, 0x10, 0x00, 0x00, //0x00001ea5 je           LBB0_546
	//0x00001eab LBB0_370
	0x49, 0x83, 0xc2, 0xff, //0x00001eab addq         $-1, %r10
	0x4d, 0x89, 0x13, //0x00001eaf movq         %r10, (%r11)
	0x4d, 0x85, 0xff, //0x00001eb2 testq        %r15, %r15
	0x0f, 0x8e, 0x65, 0x0e, 0x00, 0x00, //0x00001eb5 jle          LBB0_480
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00001ebb .p2align 4, 0x90
	//0x00001ec0 LBB0_371
	0x49, 0x8b, 0x11, //0x00001ec0 movq         (%r9), %rdx
	0x49, 0x39, 0xd2, //0x00001ec3 cmpq         %rdx, %r10
	0x0f, 0x83, 0x34, 0x00, 0x00, 0x00, //0x00001ec6 jae          LBB0_376
	0x43, 0x8a, 0x44, 0x15, 0x00, //0x00001ecc movb         (%r13,%r10), %al
	0x3c, 0x0d, //0x00001ed1 cmpb         $13, %al
	0x0f, 0x84, 0x27, 0x00, 0x00, 0x00, //0x00001ed3 je           LBB0_376
	0x3c, 0x20, //0x00001ed9 cmpb         $32, %al
	0x0f, 0x84, 0x1f, 0x00, 0x00, 0x00, //0x00001edb je           LBB0_376
	0x04, 0xf5, //0x00001ee1 addb         $-11, %al
	0x3c, 0xfe, //0x00001ee3 cmpb         $-2, %al
	0x0f, 0x83, 0x15, 0x00, 0x00, 0x00, //0x00001ee5 jae          LBB0_376
	0x4c, 0x89, 0xd1, //0x00001eeb movq         %r10, %rcx
	0xe9, 0xfd, 0x00, 0x00, 0x00, //0x00001eee jmp          LBB0_397
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001ef3 .p2align 4, 0x90
	//0x00001f00 LBB0_376
	0x49, 0x8d, 0x4a, 0x01, //0x00001f00 leaq         $1(%r10), %rcx
	0x48, 0x39, 0xd1, //0x00001f04 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001f07 jae          LBB0_380
	0x41, 0x8a, 0x44, 0x0d, 0x00, //0x00001f0d movb         (%r13,%rcx), %al
	0x3c, 0x0d, //0x00001f12 cmpb         $13, %al
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001f14 je           LBB0_380
	0x3c, 0x20, //0x00001f1a cmpb         $32, %al
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001f1c je           LBB0_380
	0x04, 0xf5, //0x00001f22 addb         $-11, %al
	0x3c, 0xfe, //0x00001f24 cmpb         $-2, %al
	0x0f, 0x82, 0xc4, 0x00, 0x00, 0x00, //0x00001f26 jb           LBB0_397
	0x90, 0x90, 0x90, 0x90, //0x00001f2c .p2align 4, 0x90
	//0x00001f30 LBB0_380
	0x49, 0x8d, 0x4a, 0x02, //0x00001f30 leaq         $2(%r10), %rcx
	0x48, 0x39, 0xd1, //0x00001f34 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001f37 jae          LBB0_384
	0x41, 0x8a, 0x44, 0x0d, 0x00, //0x00001f3d movb         (%r13,%rcx), %al
	0x3c, 0x0d, //0x00001f42 cmpb         $13, %al
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001f44 je           LBB0_384
	0x3c, 0x20, //0x00001f4a cmpb         $32, %al
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001f4c je           LBB0_384
	0x04, 0xf5, //0x00001f52 addb         $-11, %al
	0x3c, 0xfe, //0x00001f54 cmpb         $-2, %al
	0x0f, 0x82, 0x94, 0x00, 0x00, 0x00, //0x00001f56 jb           LBB0_397
	0x90, 0x90, 0x90, 0x90, //0x00001f5c .p2align 4, 0x90
	//0x00001f60 LBB0_384
	0x49, 0x8d, 0x4a, 0x03, //0x00001f60 leaq         $3(%r10), %rcx
	0x48, 0x39, 0xd1, //0x00001f64 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001f67 jae          LBB0_388
	0x41, 0x8a, 0x44, 0x0d, 0x00, //0x00001f6d movb         (%r13,%rcx), %al
	0x3c, 0x0d, //0x00001f72 cmpb         $13, %al
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001f74 je           LBB0_388
	0x3c, 0x20, //0x00001f7a cmpb         $32, %al
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00001f7c je           LBB0_388
	0x04, 0xf5, //0x00001f82 addb         $-11, %al
	0x3c, 0xfe, //0x00001f84 cmpb         $-2, %al
	0x0f, 0x82, 0x64, 0x00, 0x00, 0x00, //0x00001f86 jb           LBB0_397
	0x90, 0x90, 0x90, 0x90, //0x00001f8c .p2align 4, 0x90
	//0x00001f90 LBB0_388
	0x49, 0x8d, 0x4a, 0x04, //0x00001f90 leaq         $4(%r10), %rcx
	0x48, 0x39, 0xca, //0x00001f94 cmpq         %rcx, %rdx
	0x0f, 0x86, 0xb3, 0x06, 0x00, 0x00, //0x00001f97 jbe          LBB0_451
	0x0f, 0x84, 0x38, 0x00, 0x00, 0x00, //0x00001f9d je           LBB0_394
	0x4a, 0x8d, 0x04, 0x2a, //0x00001fa3 leaq         (%rdx,%r13), %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001fa7 .p2align 4, 0x90
	//0x00001fb0 LBB0_391
	0x41, 0x0f, 0xbe, 0x74, 0x0d, 0x00, //0x00001fb0 movsbl       (%r13,%rcx), %esi
	0x83, 0xfe, 0x20, //0x00001fb6 cmpl         $32, %esi
	0x0f, 0x87, 0x28, 0x00, 0x00, 0x00, //0x00001fb9 ja           LBB0_396
	0x49, 0x0f, 0xa3, 0xf4, //0x00001fbf btq          %rsi, %r12
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x00001fc3 jae          LBB0_396
	0x48, 0x83, 0xc1, 0x01, //0x00001fc9 addq         $1, %rcx
	0x48, 0x39, 0xca, //0x00001fcd cmpq         %rcx, %rdx
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x00001fd0 jne          LBB0_391
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00001fd6 jmp          LBB0_395
	//0x00001fdb LBB0_394
	0x4c, 0x01, 0xe9, //0x00001fdb addq         %r13, %rcx
	0x48, 0x89, 0xc8, //0x00001fde movq         %rcx, %rax
	//0x00001fe1 LBB0_395
	0x4c, 0x29, 0xe8, //0x00001fe1 subq         %r13, %rax
	0x48, 0x89, 0xc1, //0x00001fe4 movq         %rax, %rcx
	//0x00001fe7 LBB0_396
	0x48, 0x39, 0xd1, //0x00001fe7 cmpq         %rdx, %rcx
	0x0f, 0x83, 0x66, 0x06, 0x00, 0x00, //0x00001fea jae          LBB0_452
	//0x00001ff0 LBB0_397
	0x4c, 0x8d, 0x51, 0x01, //0x00001ff0 leaq         $1(%rcx), %r10
	0x4d, 0x89, 0x13, //0x00001ff4 movq         %r10, (%r11)
	0x41, 0x0f, 0xbe, 0x44, 0x0d, 0x00, //0x00001ff7 movsbl       (%r13,%rcx), %eax
	0x83, 0xf8, 0x7b, //0x00001ffd cmpl         $123, %eax
	0x0f, 0x87, 0x4a, 0x06, 0x00, 0x00, //0x00002000 ja           LBB0_451
	0x48, 0x8d, 0x15, 0xcf, 0x33, 0x00, 0x00, //0x00002006 leaq         $13263(%rip), %rdx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x0000200d movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x00002011 addq         %rdx, %rax
	0xff, 0xe0, //0x00002014 jmpq         *%rax
	//0x00002016 LBB0_399
	0x49, 0x8b, 0x01, //0x00002016 movq         (%r9), %rax
	0x48, 0x89, 0xc2, //0x00002019 movq         %rax, %rdx
	0x4c, 0x29, 0xd2, //0x0000201c subq         %r10, %rdx
	0x48, 0x83, 0xfa, 0x10, //0x0000201f cmpq         $16, %rdx
	0x0f, 0x82, 0x8d, 0x0c, 0x00, 0x00, //0x00002023 jb           LBB0_516
	0x48, 0xf7, 0xd1, //0x00002029 notq         %rcx
	0x90, 0x90, 0x90, 0x90, //0x0000202c .p2align 4, 0x90
	//0x00002030 LBB0_401
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x15, 0x00, //0x00002030 movdqu       (%r13,%r10), %xmm2
	0x66, 0x0f, 0x6f, 0xda, //0x00002037 movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x0000203b pcmpeqb      %xmm13, %xmm3
	0x66, 0x41, 0x0f, 0xdb, 0xd6, //0x00002040 pand         %xmm14, %xmm2
	0x66, 0x0f, 0x74, 0xd4, //0x00002045 pcmpeqb      %xmm4, %xmm2
	0x66, 0x0f, 0xeb, 0xd3, //0x00002049 por          %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x0000204d pmovmskb     %xmm2, %edx
	0x85, 0xd2, //0x00002051 testl        %edx, %edx
	0x0f, 0x85, 0x87, 0x00, 0x00, 0x00, //0x00002053 jne          LBB0_412
	0x49, 0x83, 0xc2, 0x10, //0x00002059 addq         $16, %r10
	0x48, 0x8d, 0x14, 0x08, //0x0000205d leaq         (%rax,%rcx), %rdx
	0x48, 0x83, 0xc2, 0xf0, //0x00002061 addq         $-16, %rdx
	0x48, 0x83, 0xc1, 0xf0, //0x00002065 addq         $-16, %rcx
	0x48, 0x83, 0xfa, 0x0f, //0x00002069 cmpq         $15, %rdx
	0x0f, 0x87, 0xbd, 0xff, 0xff, 0xff, //0x0000206d ja           LBB0_401
	0x4d, 0x89, 0xea, //0x00002073 movq         %r13, %r10
	0x49, 0x29, 0xca, //0x00002076 subq         %rcx, %r10
	0x48, 0x01, 0xc8, //0x00002079 addq         %rcx, %rax
	0x48, 0x89, 0xc2, //0x0000207c movq         %rax, %rdx
	0x48, 0x85, 0xd2, //0x0000207f testq        %rdx, %rdx
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00002082 je           LBB0_410
	//0x00002088 LBB0_404
	0x49, 0x8d, 0x0c, 0x12, //0x00002088 leaq         (%r10,%rdx), %rcx
	0x31, 0xc0, //0x0000208c xorl         %eax, %eax
	//0x0000208e LBB0_405
	0x41, 0x0f, 0xb6, 0x1c, 0x02, //0x0000208e movzbl       (%r10,%rax), %ebx
	0x80, 0xfb, 0x2c, //0x00002093 cmpb         $44, %bl
	0x0f, 0x84, 0x77, 0x0b, 0x00, 0x00, //0x00002096 je           LBB0_514
	0x80, 0xfb, 0x7d, //0x0000209c cmpb         $125, %bl
	0x0f, 0x84, 0x6e, 0x0b, 0x00, 0x00, //0x0000209f je           LBB0_514
	0x80, 0xfb, 0x5d, //0x000020a5 cmpb         $93, %bl
	0x0f, 0x84, 0x65, 0x0b, 0x00, 0x00, //0x000020a8 je           LBB0_514
	0x48, 0x83, 0xc0, 0x01, //0x000020ae addq         $1, %rax
	0x48, 0x39, 0xc2, //0x000020b2 cmpq         %rax, %rdx
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x000020b5 jne          LBB0_405
	0x49, 0x89, 0xca, //0x000020bb movq         %rcx, %r10
	//0x000020be LBB0_410
	0x4d, 0x29, 0xea, //0x000020be subq         %r13, %r10
	0x4c, 0x8b, 0x5d, 0xd0, //0x000020c1 movq         $-48(%rbp), %r11
	0x4d, 0x89, 0x13, //0x000020c5 movq         %r10, (%r11)
	0x48, 0x8b, 0x7d, 0xb8, //0x000020c8 movq         $-72(%rbp), %rdi
	//0x000020cc LBB0_411
	0x4c, 0x8b, 0x4d, 0xb0, //0x000020cc movq         $-80(%rbp), %r9
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000020d0 movabsq      $4294977024, %r12
	0xe9, 0x77, 0x05, 0x00, 0x00, //0x000020da jmp          LBB0_452
	0x90, //0x000020df .p2align 4, 0x90
	//0x000020e0 LBB0_412
	0x66, 0x0f, 0xbc, 0xc2, //0x000020e0 bsfw         %dx, %ax
	0x44, 0x0f, 0xb7, 0xd0, //0x000020e4 movzwl       %ax, %r10d
	0x49, 0x29, 0xca, //0x000020e8 subq         %rcx, %r10
	0x4d, 0x89, 0x13, //0x000020eb movq         %r10, (%r11)
	0xe9, 0x63, 0x05, 0x00, 0x00, //0x000020ee jmp          LBB0_452
	//0x000020f3 LBB0_413
	0x48, 0x83, 0xc1, 0x04, //0x000020f3 addq         $4, %rcx
	0x49, 0x3b, 0x09, //0x000020f7 cmpq         (%r9), %rcx
	0x0f, 0x86, 0x50, 0x05, 0x00, 0x00, //0x000020fa jbe          LBB0_451
	0xe9, 0x51, 0x05, 0x00, 0x00, //0x00002100 jmp          LBB0_452
	//0x00002105 LBB0_414
	0x4c, 0x89, 0x75, 0xc8, //0x00002105 movq         %r14, $-56(%rbp)
	0x4d, 0x8b, 0x09, //0x00002109 movq         (%r9), %r9
	0x4d, 0x89, 0xce, //0x0000210c movq         %r9, %r14
	0x4d, 0x29, 0xd6, //0x0000210f subq         %r10, %r14
	0x49, 0x83, 0xfe, 0x20, //0x00002112 cmpq         $32, %r14
	0x0f, 0x8c, 0xab, 0x0b, 0x00, 0x00, //0x00002116 jl           LBB0_517
	0x4e, 0x8d, 0x1c, 0x29, //0x0000211c leaq         (%rcx,%r13), %r11
	0x49, 0x29, 0xc9, //0x00002120 subq         %rcx, %r9
	0xb8, 0x1f, 0x00, 0x00, 0x00, //0x00002123 movl         $31, %eax
	0x45, 0x31, 0xf6, //0x00002128 xorl         %r14d, %r14d
	0x45, 0x31, 0xc0, //0x0000212b xorl         %r8d, %r8d
	0xe9, 0x33, 0x00, 0x00, 0x00, //0x0000212e jmp          LBB0_416
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002133 .p2align 4, 0x90
	//0x00002140 LBB0_419
	0x45, 0x31, 0xc0, //0x00002140 xorl         %r8d, %r8d
	0x48, 0x85, 0xf6, //0x00002143 testq        %rsi, %rsi
	0x0f, 0x85, 0xb0, 0x00, 0x00, 0x00, //0x00002146 jne          LBB0_418
	//0x0000214c LBB0_420
	0x49, 0x83, 0xc6, 0x20, //0x0000214c addq         $32, %r14
	0x49, 0x8d, 0x14, 0x01, //0x00002150 leaq         (%r9,%rax), %rdx
	0x48, 0x83, 0xc2, 0xe0, //0x00002154 addq         $-32, %rdx
	0x48, 0x83, 0xc0, 0xe0, //0x00002158 addq         $-32, %rax
	0x48, 0x83, 0xfa, 0x3f, //0x0000215c cmpq         $63, %rdx
	0x0f, 0x8e, 0xbb, 0x0a, 0x00, 0x00, //0x00002160 jle          LBB0_421
	//0x00002166 LBB0_416
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x33, 0x01, //0x00002166 movdqu       $1(%r11,%r14), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x33, 0x11, //0x0000216d movdqu       $17(%r11,%r14), %xmm3
	0x66, 0x0f, 0x6f, 0xea, //0x00002174 movdqa       %xmm2, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00002178 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xfd, //0x0000217c pmovmskb     %xmm5, %edi
	0x66, 0x0f, 0x6f, 0xeb, //0x00002180 movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00002184 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x00002188 pmovmskb     %xmm5, %esi
	0x48, 0xc1, 0xe6, 0x10, //0x0000218c shlq         $16, %rsi
	0x48, 0x09, 0xfe, //0x00002190 orq          %rdi, %rsi
	0x66, 0x0f, 0x74, 0xd1, //0x00002193 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00002197 pmovmskb     %xmm2, %ebx
	0x66, 0x0f, 0x74, 0xd9, //0x0000219b pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x0000219f pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe7, 0x10, //0x000021a3 shlq         $16, %rdi
	0x48, 0x09, 0xdf, //0x000021a7 orq          %rbx, %rdi
	0x48, 0x89, 0xfb, //0x000021aa movq         %rdi, %rbx
	0x4c, 0x09, 0xc3, //0x000021ad orq          %r8, %rbx
	0x0f, 0x84, 0x8a, 0xff, 0xff, 0xff, //0x000021b0 je           LBB0_419
	0x44, 0x89, 0xc3, //0x000021b6 movl         %r8d, %ebx
	0xf7, 0xd3, //0x000021b9 notl         %ebx
	0x21, 0xfb, //0x000021bb andl         %edi, %ebx
	0x44, 0x8d, 0x24, 0x1b, //0x000021bd leal         (%rbx,%rbx), %r12d
	0x45, 0x09, 0xc4, //0x000021c1 orl          %r8d, %r12d
	0x44, 0x89, 0xe2, //0x000021c4 movl         %r12d, %edx
	0xf7, 0xd2, //0x000021c7 notl         %edx
	0x21, 0xfa, //0x000021c9 andl         %edi, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x000021cb andl         $-1431655766, %edx
	0x45, 0x31, 0xc0, //0x000021d1 xorl         %r8d, %r8d
	0x01, 0xda, //0x000021d4 addl         %ebx, %edx
	0x41, 0x0f, 0x92, 0xc0, //0x000021d6 setb         %r8b
	0x01, 0xd2, //0x000021da addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x000021dc xorl         $1431655765, %edx
	0x44, 0x21, 0xe2, //0x000021e2 andl         %r12d, %edx
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000021e5 movabsq      $4294977024, %r12
	0xf7, 0xd2, //0x000021ef notl         %edx
	0x21, 0xd6, //0x000021f1 andl         %edx, %esi
	0x48, 0x85, 0xf6, //0x000021f3 testq        %rsi, %rsi
	0x0f, 0x84, 0x50, 0xff, 0xff, 0xff, //0x000021f6 je           LBB0_420
	//0x000021fc LBB0_418
	0x0f, 0xbc, 0xc6, //0x000021fc bsfl         %esi, %eax
	0x48, 0x01, 0xc1, //0x000021ff addq         %rax, %rcx
	0x4d, 0x8d, 0x14, 0x0e, //0x00002202 leaq         (%r14,%rcx), %r10
	0x49, 0x83, 0xc2, 0x02, //0x00002206 addq         $2, %r10
	0x4c, 0x8b, 0x5d, 0xd0, //0x0000220a movq         $-48(%rbp), %r11
	0x4d, 0x89, 0x13, //0x0000220e movq         %r10, (%r11)
	0x48, 0x8b, 0x7d, 0xb8, //0x00002211 movq         $-72(%rbp), %rdi
	0x4c, 0x8b, 0x75, 0xc8, //0x00002215 movq         $-56(%rbp), %r14
	0x4c, 0x8b, 0x4d, 0xb0, //0x00002219 movq         $-80(%rbp), %r9
	0xe9, 0x34, 0x04, 0x00, 0x00, //0x0000221d jmp          LBB0_452
	//0x00002222 LBB0_425
	0x4c, 0x89, 0x75, 0xc8, //0x00002222 movq         %r14, $-56(%rbp)
	0x49, 0x8b, 0x01, //0x00002226 movq         (%r9), %rax
	0x4c, 0x29, 0xd0, //0x00002229 subq         %r10, %rax
	0x4d, 0x01, 0xd5, //0x0000222c addq         %r10, %r13
	0x45, 0x31, 0xdb, //0x0000222f xorl         %r11d, %r11d
	0x45, 0x31, 0xd2, //0x00002232 xorl         %r10d, %r10d
	0x45, 0x31, 0xf6, //0x00002235 xorl         %r14d, %r14d
	0x31, 0xc9, //0x00002238 xorl         %ecx, %ecx
	0xe9, 0x6f, 0x00, 0x00, 0x00, //0x0000223a jmp          LBB0_427
	//0x0000223f LBB0_426
	0x49, 0xc1, 0xf8, 0x3f, //0x0000223f sarq         $63, %r8
	0x4c, 0x89, 0xc8, //0x00002243 movq         %r9, %rax
	0x48, 0xd1, 0xe8, //0x00002246 shrq         %rax
	0x48, 0xbe, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002249 movabsq      $6148914691236517205, %rsi
	0x48, 0x21, 0xf0, //0x00002253 andq         %rsi, %rax
	0x49, 0x29, 0xc1, //0x00002256 subq         %rax, %r9
	0x4c, 0x89, 0xc8, //0x00002259 movq         %r9, %rax
	0x48, 0xba, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x0000225c movabsq      $3689348814741910323, %rdx
	0x48, 0x21, 0xd0, //0x00002266 andq         %rdx, %rax
	0x49, 0xc1, 0xe9, 0x02, //0x00002269 shrq         $2, %r9
	0x49, 0x21, 0xd1, //0x0000226d andq         %rdx, %r9
	0x49, 0x01, 0xc1, //0x00002270 addq         %rax, %r9
	0x4c, 0x89, 0xc8, //0x00002273 movq         %r9, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x00002276 shrq         $4, %rax
	0x4c, 0x01, 0xc8, //0x0000227a addq         %r9, %rax
	0x48, 0xba, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000227d movabsq      $1085102592571150095, %rdx
	0x48, 0x21, 0xd0, //0x00002287 andq         %rdx, %rax
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x0000228a movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xc2, //0x00002294 imulq        %rdx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x00002298 shrq         $56, %rax
	0x49, 0x01, 0xc6, //0x0000229c addq         %rax, %r14
	0x49, 0x83, 0xc5, 0x40, //0x0000229f addq         $64, %r13
	0x48, 0x8b, 0x45, 0xc0, //0x000022a3 movq         $-64(%rbp), %rax
	0x48, 0x83, 0xc0, 0xc0, //0x000022a7 addq         $-64, %rax
	0x4d, 0x89, 0xc3, //0x000022ab movq         %r8, %r11
	//0x000022ae LBB0_427
	0x48, 0x83, 0xf8, 0x40, //0x000022ae cmpq         $64, %rax
	0x48, 0x89, 0x45, 0xc0, //0x000022b2 movq         %rax, $-64(%rbp)
	0x0f, 0x8c, 0x35, 0x02, 0x00, 0x00, //0x000022b6 jl           LBB0_435
	//0x000022bc LBB0_428
	0xf3, 0x41, 0x0f, 0x6f, 0x55, 0x00, //0x000022bc movdqu       (%r13), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x6d, 0x10, //0x000022c2 movdqu       $16(%r13), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x7d, 0x20, //0x000022c8 movdqu       $32(%r13), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x75, 0x30, //0x000022ce movdqu       $48(%r13), %xmm6
	0x66, 0x0f, 0x6f, 0xda, //0x000022d4 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000022d8 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x000022dc pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdd, //0x000022e0 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000022e4 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x000022e8 pmovmskb     %xmm3, %edx
	0x66, 0x0f, 0x6f, 0xdf, //0x000022ec movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000022f0 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x000022f4 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x6f, 0xde, //0x000022f8 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000022fc pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00002300 pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe7, 0x30, //0x00002304 shlq         $48, %rdi
	0x48, 0xc1, 0xe6, 0x20, //0x00002308 shlq         $32, %rsi
	0x48, 0x09, 0xfe, //0x0000230c orq          %rdi, %rsi
	0x48, 0xc1, 0xe2, 0x10, //0x0000230f shlq         $16, %rdx
	0x48, 0x09, 0xf2, //0x00002313 orq          %rsi, %rdx
	0x48, 0x09, 0xd0, //0x00002316 orq          %rdx, %rax
	0x48, 0x89, 0xc2, //0x00002319 movq         %rax, %rdx
	0x4c, 0x09, 0xd2, //0x0000231c orq          %r10, %rdx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x0000231f jne          LBB0_430
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002325 movq         $-1, %rax
	0x45, 0x31, 0xd2, //0x0000232c xorl         %r10d, %r10d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x0000232f jmp          LBB0_431
	//0x00002334 LBB0_430
	0x4c, 0x89, 0xd2, //0x00002334 movq         %r10, %rdx
	0x48, 0xf7, 0xd2, //0x00002337 notq         %rdx
	0x48, 0x21, 0xc2, //0x0000233a andq         %rax, %rdx
	0x48, 0x8d, 0x34, 0x12, //0x0000233d leaq         (%rdx,%rdx), %rsi
	0x4c, 0x09, 0xd6, //0x00002341 orq          %r10, %rsi
	0x48, 0x89, 0xf7, //0x00002344 movq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x00002347 notq         %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000234a movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xd8, //0x00002354 andq         %rbx, %rax
	0x48, 0x21, 0xf8, //0x00002357 andq         %rdi, %rax
	0x45, 0x31, 0xd2, //0x0000235a xorl         %r10d, %r10d
	0x48, 0x01, 0xd0, //0x0000235d addq         %rdx, %rax
	0x41, 0x0f, 0x92, 0xc2, //0x00002360 setb         %r10b
	0x48, 0x01, 0xc0, //0x00002364 addq         %rax, %rax
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002367 movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd0, //0x00002371 xorq         %rdx, %rax
	0x48, 0x21, 0xf0, //0x00002374 andq         %rsi, %rax
	0x48, 0xf7, 0xd0, //0x00002377 notq         %rax
	//0x0000237a LBB0_431
	0x66, 0x0f, 0x6f, 0xde, //0x0000237a movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x0000237e pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x00002382 pmovmskb     %xmm3, %edx
	0x48, 0xc1, 0xe2, 0x30, //0x00002386 shlq         $48, %rdx
	0x66, 0x0f, 0x6f, 0xdf, //0x0000238a movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x0000238e pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00002392 pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x00002396 shlq         $32, %rsi
	0x48, 0x09, 0xd6, //0x0000239a orq          %rdx, %rsi
	0x66, 0x0f, 0x6f, 0xdd, //0x0000239d movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000023a1 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x000023a5 pmovmskb     %xmm3, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x000023a9 shlq         $16, %rdx
	0x48, 0x09, 0xf2, //0x000023ad orq          %rsi, %rdx
	0x66, 0x0f, 0x6f, 0xda, //0x000023b0 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000023b4 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x000023b8 pmovmskb     %xmm3, %esi
	0x48, 0x09, 0xd6, //0x000023bc orq          %rdx, %rsi
	0x48, 0x21, 0xc6, //0x000023bf andq         %rax, %rsi
	0x66, 0x48, 0x0f, 0x6e, 0xde, //0x000023c2 movq         %rsi, %xmm3
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd9, 0x00, //0x000023c7 pclmulqdq    $0, %xmm9, %xmm3
	0x66, 0x49, 0x0f, 0x7e, 0xd8, //0x000023ce movq         %xmm3, %r8
	0x4d, 0x31, 0xd8, //0x000023d3 xorq         %r11, %r8
	0x66, 0x0f, 0x6f, 0xda, //0x000023d6 movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x000023da pcmpeqb      %xmm10, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xcb, //0x000023df pmovmskb     %xmm3, %r9d
	0x66, 0x0f, 0x6f, 0xdd, //0x000023e4 movdqa       %xmm5, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x000023e8 pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x000023ed pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdf, //0x000023f1 movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x000023f5 pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x000023fa pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x6f, 0xde, //0x000023fe movdqa       %xmm6, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00002402 pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x00002407 pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x0000240b shlq         $48, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x0000240f shlq         $32, %rsi
	0x48, 0x09, 0xde, //0x00002413 orq          %rbx, %rsi
	0x48, 0xc1, 0xe0, 0x10, //0x00002416 shlq         $16, %rax
	0x48, 0x09, 0xf0, //0x0000241a orq          %rsi, %rax
	0x49, 0x09, 0xc1, //0x0000241d orq          %rax, %r9
	0x4d, 0x89, 0xc3, //0x00002420 movq         %r8, %r11
	0x49, 0xf7, 0xd3, //0x00002423 notq         %r11
	0x4d, 0x21, 0xd9, //0x00002426 andq         %r11, %r9
	0x66, 0x0f, 0x74, 0xd4, //0x00002429 pcmpeqb      %xmm4, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x0000242d pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x74, 0xec, //0x00002431 pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00002435 pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x74, 0xfc, //0x00002439 pcmpeqb      %xmm4, %xmm7
	0x66, 0x0f, 0xd7, 0xff, //0x0000243d pmovmskb     %xmm7, %edi
	0x66, 0x0f, 0x74, 0xf4, //0x00002441 pcmpeqb      %xmm4, %xmm6
	0x66, 0x0f, 0xd7, 0xf6, //0x00002445 pmovmskb     %xmm6, %esi
	0x48, 0xc1, 0xe6, 0x30, //0x00002449 shlq         $48, %rsi
	0x48, 0xc1, 0xe7, 0x20, //0x0000244d shlq         $32, %rdi
	0x48, 0x09, 0xf7, //0x00002451 orq          %rsi, %rdi
	0x48, 0xc1, 0xe3, 0x10, //0x00002454 shlq         $16, %rbx
	0x48, 0x09, 0xfb, //0x00002458 orq          %rdi, %rbx
	0x48, 0x09, 0xd8, //0x0000245b orq          %rbx, %rax
	0x4c, 0x21, 0xd8, //0x0000245e andq         %r11, %rax
	0x0f, 0x84, 0xd8, 0xfd, 0xff, 0xff, //0x00002461 je           LBB0_426
	0x4c, 0x8b, 0x5d, 0xd0, //0x00002467 movq         $-48(%rbp), %r11
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000246b .p2align 4, 0x90
	//0x00002470 LBB0_433
	0x48, 0x8d, 0x58, 0xff, //0x00002470 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xde, //0x00002474 movq         %rbx, %rsi
	0x4c, 0x21, 0xce, //0x00002477 andq         %r9, %rsi
	0x48, 0x89, 0xf7, //0x0000247a movq         %rsi, %rdi
	0x48, 0xd1, 0xef, //0x0000247d shrq         %rdi
	0x49, 0xbc, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002480 movabsq      $6148914691236517205, %r12
	0x4c, 0x21, 0xe7, //0x0000248a andq         %r12, %rdi
	0x48, 0x29, 0xfe, //0x0000248d subq         %rdi, %rsi
	0x48, 0x89, 0xf7, //0x00002490 movq         %rsi, %rdi
	0x48, 0xba, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00002493 movabsq      $3689348814741910323, %rdx
	0x48, 0x21, 0xd7, //0x0000249d andq         %rdx, %rdi
	0x48, 0xc1, 0xee, 0x02, //0x000024a0 shrq         $2, %rsi
	0x48, 0x21, 0xd6, //0x000024a4 andq         %rdx, %rsi
	0x48, 0x01, 0xfe, //0x000024a7 addq         %rdi, %rsi
	0x48, 0x89, 0xf7, //0x000024aa movq         %rsi, %rdi
	0x48, 0xc1, 0xef, 0x04, //0x000024ad shrq         $4, %rdi
	0x48, 0x01, 0xf7, //0x000024b1 addq         %rsi, %rdi
	0x48, 0xba, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x000024b4 movabsq      $1085102592571150095, %rdx
	0x48, 0x21, 0xd7, //0x000024be andq         %rdx, %rdi
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000024c1 movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xfa, //0x000024cb imulq        %rdx, %rdi
	0x48, 0xc1, 0xef, 0x38, //0x000024cf shrq         $56, %rdi
	0x4c, 0x01, 0xf7, //0x000024d3 addq         %r14, %rdi
	0x48, 0x39, 0xcf, //0x000024d6 cmpq         %rcx, %rdi
	0x0f, 0x86, 0xf3, 0x06, 0x00, 0x00, //0x000024d9 jbe          LBB0_508
	0x48, 0x83, 0xc1, 0x01, //0x000024df addq         $1, %rcx
	0x48, 0x21, 0xd8, //0x000024e3 andq         %rbx, %rax
	0x0f, 0x85, 0x84, 0xff, 0xff, 0xff, //0x000024e6 jne          LBB0_433
	0xe9, 0x4e, 0xfd, 0xff, 0xff, //0x000024ec jmp          LBB0_426
	//0x000024f1 LBB0_435
	0x48, 0x85, 0xc0, //0x000024f1 testq        %rax, %rax
	0x0f, 0x8e, 0xd6, 0x07, 0x00, 0x00, //0x000024f4 jle          LBB0_518
	0xf3, 0x44, 0x0f, 0x7f, 0x45, 0x80, //0x000024fa movdqu       %xmm8, $-128(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00002500 movdqu       %xmm8, $-144(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00002509 movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00002512 movdqu       %xmm8, $-176(%rbp)
	0x44, 0x89, 0xe8, //0x0000251b movl         %r13d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x0000251e andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00002523 cmpl         $4033, %eax
	0x0f, 0x82, 0x8e, 0xfd, 0xff, 0xff, //0x00002528 jb           LBB0_428
	0x48, 0x83, 0x7d, 0xc0, 0x20, //0x0000252e cmpq         $32, $-64(%rbp)
	0x0f, 0x82, 0x30, 0x00, 0x00, 0x00, //0x00002533 jb           LBB0_439
	0x41, 0x0f, 0x10, 0x55, 0x00, //0x00002539 movups       (%r13), %xmm2
	0x0f, 0x11, 0x95, 0x50, 0xff, 0xff, 0xff, //0x0000253e movups       %xmm2, $-176(%rbp)
	0x41, 0x0f, 0x10, 0x55, 0x10, //0x00002545 movups       $16(%r13), %xmm2
	0x0f, 0x11, 0x95, 0x60, 0xff, 0xff, 0xff, //0x0000254a movups       %xmm2, $-160(%rbp)
	0x49, 0x83, 0xc5, 0x20, //0x00002551 addq         $32, %r13
	0x48, 0x8b, 0x45, 0xc0, //0x00002555 movq         $-64(%rbp), %rax
	0x48, 0x8d, 0x70, 0xe0, //0x00002559 leaq         $-32(%rax), %rsi
	0x48, 0x8d, 0x95, 0x70, 0xff, 0xff, 0xff, //0x0000255d leaq         $-144(%rbp), %rdx
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00002564 jmp          LBB0_440
	//0x00002569 LBB0_439
	0x48, 0x8d, 0x95, 0x50, 0xff, 0xff, 0xff, //0x00002569 leaq         $-176(%rbp), %rdx
	0x48, 0x8b, 0x75, 0xc0, //0x00002570 movq         $-64(%rbp), %rsi
	//0x00002574 LBB0_440
	0x48, 0x83, 0xfe, 0x10, //0x00002574 cmpq         $16, %rsi
	0x0f, 0x82, 0x5c, 0x00, 0x00, 0x00, //0x00002578 jb           LBB0_441
	0x41, 0x0f, 0x10, 0x55, 0x00, //0x0000257e movups       (%r13), %xmm2
	0x0f, 0x11, 0x12, //0x00002583 movups       %xmm2, (%rdx)
	0x49, 0x83, 0xc5, 0x10, //0x00002586 addq         $16, %r13
	0x48, 0x83, 0xc2, 0x10, //0x0000258a addq         $16, %rdx
	0x48, 0x83, 0xc6, 0xf0, //0x0000258e addq         $-16, %rsi
	0x48, 0x83, 0xfe, 0x08, //0x00002592 cmpq         $8, %rsi
	0x0f, 0x83, 0x48, 0x00, 0x00, 0x00, //0x00002596 jae          LBB0_446
	//0x0000259c LBB0_442
	0x48, 0x83, 0xfe, 0x04, //0x0000259c cmpq         $4, %rsi
	0x0f, 0x82, 0x5b, 0x00, 0x00, 0x00, //0x000025a0 jb           LBB0_443
	//0x000025a6 LBB0_447
	0x41, 0x8b, 0x45, 0x00, //0x000025a6 movl         (%r13), %eax
	0x89, 0x02, //0x000025aa movl         %eax, (%rdx)
	0x49, 0x83, 0xc5, 0x04, //0x000025ac addq         $4, %r13
	0x48, 0x83, 0xc2, 0x04, //0x000025b0 addq         $4, %rdx
	0x48, 0x83, 0xc6, 0xfc, //0x000025b4 addq         $-4, %rsi
	0x48, 0x83, 0xfe, 0x02, //0x000025b8 cmpq         $2, %rsi
	0x0f, 0x83, 0x49, 0x00, 0x00, 0x00, //0x000025bc jae          LBB0_448
	//0x000025c2 LBB0_444
	0x4c, 0x89, 0xe8, //0x000025c2 movq         %r13, %rax
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x000025c5 leaq         $-176(%rbp), %r13
	0x48, 0x85, 0xf6, //0x000025cc testq        %rsi, %rsi
	0x0f, 0x85, 0x5d, 0x00, 0x00, 0x00, //0x000025cf jne          LBB0_449
	0xe9, 0xe2, 0xfc, 0xff, 0xff, //0x000025d5 jmp          LBB0_428
	//0x000025da LBB0_441
	0x48, 0x83, 0xfe, 0x08, //0x000025da cmpq         $8, %rsi
	0x0f, 0x82, 0xb8, 0xff, 0xff, 0xff, //0x000025de jb           LBB0_442
	//0x000025e4 LBB0_446
	0x49, 0x8b, 0x45, 0x00, //0x000025e4 movq         (%r13), %rax
	0x48, 0x89, 0x02, //0x000025e8 movq         %rax, (%rdx)
	0x49, 0x83, 0xc5, 0x08, //0x000025eb addq         $8, %r13
	0x48, 0x83, 0xc2, 0x08, //0x000025ef addq         $8, %rdx
	0x48, 0x83, 0xc6, 0xf8, //0x000025f3 addq         $-8, %rsi
	0x48, 0x83, 0xfe, 0x04, //0x000025f7 cmpq         $4, %rsi
	0x0f, 0x83, 0xa5, 0xff, 0xff, 0xff, //0x000025fb jae          LBB0_447
	//0x00002601 LBB0_443
	0x48, 0x83, 0xfe, 0x02, //0x00002601 cmpq         $2, %rsi
	0x0f, 0x82, 0xb7, 0xff, 0xff, 0xff, //0x00002605 jb           LBB0_444
	//0x0000260b LBB0_448
	0x41, 0x0f, 0xb7, 0x45, 0x00, //0x0000260b movzwl       (%r13), %eax
	0x66, 0x89, 0x02, //0x00002610 movw         %ax, (%rdx)
	0x49, 0x83, 0xc5, 0x02, //0x00002613 addq         $2, %r13
	0x48, 0x83, 0xc2, 0x02, //0x00002617 addq         $2, %rdx
	0x48, 0x83, 0xc6, 0xfe, //0x0000261b addq         $-2, %rsi
	0x4c, 0x89, 0xe8, //0x0000261f movq         %r13, %rax
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x00002622 leaq         $-176(%rbp), %r13
	0x48, 0x85, 0xf6, //0x00002629 testq        %rsi, %rsi
	0x0f, 0x84, 0x8a, 0xfc, 0xff, 0xff, //0x0000262c je           LBB0_428
	//0x00002632 LBB0_449
	0x8a, 0x00, //0x00002632 movb         (%rax), %al
	0x88, 0x02, //0x00002634 movb         %al, (%rdx)
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x00002636 leaq         $-176(%rbp), %r13
	0xe9, 0x7a, 0xfc, 0xff, 0xff, //0x0000263d jmp          LBB0_428
	//0x00002642 LBB0_450
	0x48, 0x83, 0xc1, 0x05, //0x00002642 addq         $5, %rcx
	0x49, 0x3b, 0x09, //0x00002646 cmpq         (%r9), %rcx
	0x0f, 0x87, 0x07, 0x00, 0x00, 0x00, //0x00002649 ja           LBB0_452
	0x90, //0x0000264f .p2align 4, 0x90
	//0x00002650 LBB0_451
	0x49, 0x89, 0x0b, //0x00002650 movq         %rcx, (%r11)
	0x49, 0x89, 0xca, //0x00002653 movq         %rcx, %r10
	//0x00002656 LBB0_452
	0x4c, 0x8b, 0x2f, //0x00002656 movq         (%rdi), %r13
	0x48, 0x8b, 0x4f, 0x08, //0x00002659 movq         $8(%rdi), %rcx
	0x49, 0x39, 0xca, //0x0000265d cmpq         %rcx, %r10
	0x0f, 0x83, 0x2a, 0x00, 0x00, 0x00, //0x00002660 jae          LBB0_457
	0x43, 0x8a, 0x44, 0x15, 0x00, //0x00002666 movb         (%r13,%r10), %al
	0x3c, 0x0d, //0x0000266b cmpb         $13, %al
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x0000266d je           LBB0_457
	0x3c, 0x20, //0x00002673 cmpb         $32, %al
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00002675 je           LBB0_457
	0x04, 0xf5, //0x0000267b addb         $-11, %al
	0x3c, 0xfe, //0x0000267d cmpb         $-2, %al
	0x0f, 0x83, 0x0b, 0x00, 0x00, 0x00, //0x0000267f jae          LBB0_457
	0x4c, 0x89, 0xd2, //0x00002685 movq         %r10, %rdx
	0xe9, 0xf8, 0x00, 0x00, 0x00, //0x00002688 jmp          LBB0_478
	0x90, 0x90, 0x90, //0x0000268d .p2align 4, 0x90
	//0x00002690 LBB0_457
	0x49, 0x8d, 0x52, 0x01, //0x00002690 leaq         $1(%r10), %rdx
	0x48, 0x39, 0xca, //0x00002694 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002697 jae          LBB0_461
	0x41, 0x8a, 0x44, 0x15, 0x00, //0x0000269d movb         (%r13,%rdx), %al
	0x3c, 0x0d, //0x000026a2 cmpb         $13, %al
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000026a4 je           LBB0_461
	0x3c, 0x20, //0x000026aa cmpb         $32, %al
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000026ac je           LBB0_461
	0x04, 0xf5, //0x000026b2 addb         $-11, %al
	0x3c, 0xfe, //0x000026b4 cmpb         $-2, %al
	0x0f, 0x82, 0xc9, 0x00, 0x00, 0x00, //0x000026b6 jb           LBB0_478
	0x90, 0x90, 0x90, 0x90, //0x000026bc .p2align 4, 0x90
	//0x000026c0 LBB0_461
	0x49, 0x8d, 0x52, 0x02, //0x000026c0 leaq         $2(%r10), %rdx
	0x48, 0x39, 0xca, //0x000026c4 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000026c7 jae          LBB0_465
	0x41, 0x8a, 0x44, 0x15, 0x00, //0x000026cd movb         (%r13,%rdx), %al
	0x3c, 0x0d, //0x000026d2 cmpb         $13, %al
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000026d4 je           LBB0_465
	0x3c, 0x20, //0x000026da cmpb         $32, %al
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000026dc je           LBB0_465
	0x04, 0xf5, //0x000026e2 addb         $-11, %al
	0x3c, 0xfe, //0x000026e4 cmpb         $-2, %al
	0x0f, 0x82, 0x99, 0x00, 0x00, 0x00, //0x000026e6 jb           LBB0_478
	0x90, 0x90, 0x90, 0x90, //0x000026ec .p2align 4, 0x90
	//0x000026f0 LBB0_465
	0x49, 0x8d, 0x52, 0x03, //0x000026f0 leaq         $3(%r10), %rdx
	0x48, 0x39, 0xca, //0x000026f4 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000026f7 jae          LBB0_469
	0x41, 0x8a, 0x44, 0x15, 0x00, //0x000026fd movb         (%r13,%rdx), %al
	0x3c, 0x0d, //0x00002702 cmpb         $13, %al
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002704 je           LBB0_469
	0x3c, 0x20, //0x0000270a cmpb         $32, %al
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x0000270c je           LBB0_469
	0x04, 0xf5, //0x00002712 addb         $-11, %al
	0x3c, 0xfe, //0x00002714 cmpb         $-2, %al
	0x0f, 0x82, 0x69, 0x00, 0x00, 0x00, //0x00002716 jb           LBB0_478
	0x90, 0x90, 0x90, 0x90, //0x0000271c .p2align 4, 0x90
	//0x00002720 LBB0_469
	0x49, 0x8d, 0x52, 0x04, //0x00002720 leaq         $4(%r10), %rdx
	0x48, 0x39, 0xd1, //0x00002724 cmpq         %rdx, %rcx
	0x0f, 0x86, 0xf9, 0x07, 0x00, 0x00, //0x00002727 jbe          LBB0_862
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x0000272d je           LBB0_475
	0x4a, 0x8d, 0x04, 0x29, //0x00002733 leaq         (%rcx,%r13), %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002737 .p2align 4, 0x90
	//0x00002740 LBB0_472
	0x41, 0x0f, 0xbe, 0x74, 0x15, 0x00, //0x00002740 movsbl       (%r13,%rdx), %esi
	0x83, 0xfe, 0x20, //0x00002746 cmpl         $32, %esi
	0x0f, 0x87, 0x2d, 0x00, 0x00, 0x00, //0x00002749 ja           LBB0_477
	0x49, 0x0f, 0xa3, 0xf4, //0x0000274f btq          %rsi, %r12
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002753 jae          LBB0_477
	0x48, 0x83, 0xc2, 0x01, //0x00002759 addq         $1, %rdx
	0x48, 0x39, 0xd1, //0x0000275d cmpq         %rdx, %rcx
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x00002760 jne          LBB0_472
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00002766 jmp          LBB0_476
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000276b .p2align 4, 0x90
	//0x00002770 LBB0_475
	0x4c, 0x01, 0xea, //0x00002770 addq         %r13, %rdx
	0x48, 0x89, 0xd0, //0x00002773 movq         %rdx, %rax
	//0x00002776 LBB0_476
	0x4c, 0x29, 0xe8, //0x00002776 subq         %r13, %rax
	0x48, 0x89, 0xc2, //0x00002779 movq         %rax, %rdx
	//0x0000277c LBB0_477
	0x48, 0x39, 0xca, //0x0000277c cmpq         %rcx, %rdx
	0x0f, 0x83, 0x50, 0x1e, 0x00, 0x00, //0x0000277f jae          LBB0_864
	//0x00002785 LBB0_478
	0x4c, 0x8d, 0x52, 0x01, //0x00002785 leaq         $1(%rdx), %r10
	0x4d, 0x89, 0x13, //0x00002789 movq         %r10, (%r11)
	0x41, 0x8a, 0x44, 0x15, 0x00, //0x0000278c movb         (%r13,%rdx), %al
	0x3c, 0x2c, //0x00002791 cmpb         $44, %al
	0x0f, 0x85, 0x72, 0x07, 0x00, 0x00, //0x00002793 jne          LBB0_545
	0x49, 0x8d, 0x47, 0xff, //0x00002799 leaq         $-1(%r15), %rax
	0x49, 0x83, 0xff, 0x02, //0x0000279d cmpq         $2, %r15
	0x49, 0x89, 0xc7, //0x000027a1 movq         %rax, %r15
	0x0f, 0x8d, 0x16, 0xf7, 0xff, 0xff, //0x000027a4 jge          LBB0_371
	0xe9, 0x71, 0x05, 0x00, 0x00, //0x000027aa jmp          LBB0_480
	//0x000027af LBB0_483
	0x4c, 0x89, 0x75, 0xc8, //0x000027af movq         %r14, $-56(%rbp)
	0x49, 0x8b, 0x01, //0x000027b3 movq         (%r9), %rax
	0x4c, 0x29, 0xd0, //0x000027b6 subq         %r10, %rax
	0x4d, 0x01, 0xd5, //0x000027b9 addq         %r10, %r13
	0x45, 0x31, 0xdb, //0x000027bc xorl         %r11d, %r11d
	0x45, 0x31, 0xd2, //0x000027bf xorl         %r10d, %r10d
	0x45, 0x31, 0xf6, //0x000027c2 xorl         %r14d, %r14d
	0x31, 0xc9, //0x000027c5 xorl         %ecx, %ecx
	0xe9, 0x6f, 0x00, 0x00, 0x00, //0x000027c7 jmp          LBB0_485
	//0x000027cc LBB0_484
	0x49, 0xc1, 0xf8, 0x3f, //0x000027cc sarq         $63, %r8
	0x4c, 0x89, 0xc8, //0x000027d0 movq         %r9, %rax
	0x48, 0xd1, 0xe8, //0x000027d3 shrq         %rax
	0x48, 0xbe, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000027d6 movabsq      $6148914691236517205, %rsi
	0x48, 0x21, 0xf0, //0x000027e0 andq         %rsi, %rax
	0x49, 0x29, 0xc1, //0x000027e3 subq         %rax, %r9
	0x4c, 0x89, 0xc8, //0x000027e6 movq         %r9, %rax
	0x48, 0xba, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x000027e9 movabsq      $3689348814741910323, %rdx
	0x48, 0x21, 0xd0, //0x000027f3 andq         %rdx, %rax
	0x49, 0xc1, 0xe9, 0x02, //0x000027f6 shrq         $2, %r9
	0x49, 0x21, 0xd1, //0x000027fa andq         %rdx, %r9
	0x49, 0x01, 0xc1, //0x000027fd addq         %rax, %r9
	0x4c, 0x89, 0xc8, //0x00002800 movq         %r9, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x00002803 shrq         $4, %rax
	0x4c, 0x01, 0xc8, //0x00002807 addq         %r9, %rax
	0x48, 0xba, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000280a movabsq      $1085102592571150095, %rdx
	0x48, 0x21, 0xd0, //0x00002814 andq         %rdx, %rax
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00002817 movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xc2, //0x00002821 imulq        %rdx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x00002825 shrq         $56, %rax
	0x49, 0x01, 0xc6, //0x00002829 addq         %rax, %r14
	0x49, 0x83, 0xc5, 0x40, //0x0000282c addq         $64, %r13
	0x48, 0x8b, 0x45, 0xc0, //0x00002830 movq         $-64(%rbp), %rax
	0x48, 0x83, 0xc0, 0xc0, //0x00002834 addq         $-64, %rax
	0x4d, 0x89, 0xc3, //0x00002838 movq         %r8, %r11
	//0x0000283b LBB0_485
	0x48, 0x83, 0xf8, 0x40, //0x0000283b cmpq         $64, %rax
	0x48, 0x89, 0x45, 0xc0, //0x0000283f movq         %rax, $-64(%rbp)
	0x0f, 0x8c, 0x38, 0x02, 0x00, 0x00, //0x00002843 jl           LBB0_493
	//0x00002849 LBB0_486
	0xf3, 0x41, 0x0f, 0x6f, 0x55, 0x00, //0x00002849 movdqu       (%r13), %xmm2
	0xf3, 0x41, 0x0f, 0x6f, 0x6d, 0x10, //0x0000284f movdqu       $16(%r13), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x7d, 0x20, //0x00002855 movdqu       $32(%r13), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x75, 0x30, //0x0000285b movdqu       $48(%r13), %xmm6
	0x66, 0x0f, 0x6f, 0xda, //0x00002861 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002865 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00002869 pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdd, //0x0000286d movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002871 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x00002875 pmovmskb     %xmm3, %edx
	0x66, 0x0f, 0x6f, 0xdf, //0x00002879 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x0000287d pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00002881 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x6f, 0xde, //0x00002885 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00002889 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x0000288d pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe7, 0x30, //0x00002891 shlq         $48, %rdi
	0x48, 0xc1, 0xe6, 0x20, //0x00002895 shlq         $32, %rsi
	0x48, 0x09, 0xfe, //0x00002899 orq          %rdi, %rsi
	0x48, 0xc1, 0xe2, 0x10, //0x0000289c shlq         $16, %rdx
	0x48, 0x09, 0xf2, //0x000028a0 orq          %rsi, %rdx
	0x48, 0x09, 0xd0, //0x000028a3 orq          %rdx, %rax
	0x48, 0x89, 0xc2, //0x000028a6 movq         %rax, %rdx
	0x4c, 0x09, 0xd2, //0x000028a9 orq          %r10, %rdx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x000028ac jne          LBB0_488
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000028b2 movq         $-1, %rax
	0x45, 0x31, 0xd2, //0x000028b9 xorl         %r10d, %r10d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x000028bc jmp          LBB0_489
	//0x000028c1 LBB0_488
	0x4c, 0x89, 0xd2, //0x000028c1 movq         %r10, %rdx
	0x48, 0xf7, 0xd2, //0x000028c4 notq         %rdx
	0x48, 0x21, 0xc2, //0x000028c7 andq         %rax, %rdx
	0x48, 0x8d, 0x34, 0x12, //0x000028ca leaq         (%rdx,%rdx), %rsi
	0x4c, 0x09, 0xd6, //0x000028ce orq          %r10, %rsi
	0x48, 0x89, 0xf7, //0x000028d1 movq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x000028d4 notq         %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000028d7 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xd8, //0x000028e1 andq         %rbx, %rax
	0x48, 0x21, 0xf8, //0x000028e4 andq         %rdi, %rax
	0x45, 0x31, 0xd2, //0x000028e7 xorl         %r10d, %r10d
	0x48, 0x01, 0xd0, //0x000028ea addq         %rdx, %rax
	0x41, 0x0f, 0x92, 0xc2, //0x000028ed setb         %r10b
	0x48, 0x01, 0xc0, //0x000028f1 addq         %rax, %rax
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000028f4 movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd0, //0x000028fe xorq         %rdx, %rax
	0x48, 0x21, 0xf0, //0x00002901 andq         %rsi, %rax
	0x48, 0xf7, 0xd0, //0x00002904 notq         %rax
	//0x00002907 LBB0_489
	0x66, 0x0f, 0x6f, 0xde, //0x00002907 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x0000290b pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x0000290f pmovmskb     %xmm3, %edx
	0x48, 0xc1, 0xe2, 0x30, //0x00002913 shlq         $48, %rdx
	0x66, 0x0f, 0x6f, 0xdf, //0x00002917 movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x0000291b pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x0000291f pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x00002923 shlq         $32, %rsi
	0x48, 0x09, 0xd6, //0x00002927 orq          %rdx, %rsi
	0x66, 0x0f, 0x6f, 0xdd, //0x0000292a movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x0000292e pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x00002932 pmovmskb     %xmm3, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x00002936 shlq         $16, %rdx
	0x48, 0x09, 0xf2, //0x0000293a orq          %rsi, %rdx
	0x66, 0x0f, 0x6f, 0xda, //0x0000293d movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x00002941 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00002945 pmovmskb     %xmm3, %esi
	0x48, 0x09, 0xd6, //0x00002949 orq          %rdx, %rsi
	0x48, 0x21, 0xc6, //0x0000294c andq         %rax, %rsi
	0x66, 0x48, 0x0f, 0x6e, 0xde, //0x0000294f movq         %rsi, %xmm3
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd9, 0x00, //0x00002954 pclmulqdq    $0, %xmm9, %xmm3
	0x66, 0x49, 0x0f, 0x7e, 0xd8, //0x0000295b movq         %xmm3, %r8
	0x4d, 0x31, 0xd8, //0x00002960 xorq         %r11, %r8
	0x66, 0x0f, 0x6f, 0xda, //0x00002963 movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x00002967 pcmpeqb      %xmm11, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xcb, //0x0000296c pmovmskb     %xmm3, %r9d
	0x66, 0x0f, 0x6f, 0xdd, //0x00002971 movdqa       %xmm5, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x00002975 pcmpeqb      %xmm11, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x0000297a pmovmskb     %xmm3, %eax
	0x66, 0x0f, 0x6f, 0xdf, //0x0000297e movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x00002982 pcmpeqb      %xmm11, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00002987 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x6f, 0xde, //0x0000298b movdqa       %xmm6, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x0000298f pcmpeqb      %xmm11, %xmm3
	0x66, 0x0f, 0xd7, 0xdb, //0x00002994 pmovmskb     %xmm3, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00002998 shlq         $48, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x0000299c shlq         $32, %rsi
	0x48, 0x09, 0xde, //0x000029a0 orq          %rbx, %rsi
	0x48, 0xc1, 0xe0, 0x10, //0x000029a3 shlq         $16, %rax
	0x48, 0x09, 0xf0, //0x000029a7 orq          %rsi, %rax
	0x49, 0x09, 0xc1, //0x000029aa orq          %rax, %r9
	0x4d, 0x89, 0xc3, //0x000029ad movq         %r8, %r11
	0x49, 0xf7, 0xd3, //0x000029b0 notq         %r11
	0x4d, 0x21, 0xd9, //0x000029b3 andq         %r11, %r9
	0x66, 0x41, 0x0f, 0x74, 0xd4, //0x000029b6 pcmpeqb      %xmm12, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x000029bb pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xec, //0x000029bf pcmpeqb      %xmm12, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x000029c4 pmovmskb     %xmm5, %ebx
	0x66, 0x41, 0x0f, 0x74, 0xfc, //0x000029c8 pcmpeqb      %xmm12, %xmm7
	0x66, 0x0f, 0xd7, 0xff, //0x000029cd pmovmskb     %xmm7, %edi
	0x66, 0x41, 0x0f, 0x74, 0xf4, //0x000029d1 pcmpeqb      %xmm12, %xmm6
	0x66, 0x0f, 0xd7, 0xf6, //0x000029d6 pmovmskb     %xmm6, %esi
	0x48, 0xc1, 0xe6, 0x30, //0x000029da shlq         $48, %rsi
	0x48, 0xc1, 0xe7, 0x20, //0x000029de shlq         $32, %rdi
	0x48, 0x09, 0xf7, //0x000029e2 orq          %rsi, %rdi
	0x48, 0xc1, 0xe3, 0x10, //0x000029e5 shlq         $16, %rbx
	0x48, 0x09, 0xfb, //0x000029e9 orq          %rdi, %rbx
	0x48, 0x09, 0xd8, //0x000029ec orq          %rbx, %rax
	0x4c, 0x21, 0xd8, //0x000029ef andq         %r11, %rax
	0x0f, 0x84, 0xd4, 0xfd, 0xff, 0xff, //0x000029f2 je           LBB0_484
	0x4c, 0x8b, 0x5d, 0xd0, //0x000029f8 movq         $-48(%rbp), %r11
	0x90, 0x90, 0x90, 0x90, //0x000029fc .p2align 4, 0x90
	//0x00002a00 LBB0_491
	0x48, 0x8d, 0x58, 0xff, //0x00002a00 leaq         $-1(%rax), %rbx
	0x48, 0x89, 0xde, //0x00002a04 movq         %rbx, %rsi
	0x4c, 0x21, 0xce, //0x00002a07 andq         %r9, %rsi
	0x48, 0x89, 0xf7, //0x00002a0a movq         %rsi, %rdi
	0x48, 0xd1, 0xef, //0x00002a0d shrq         %rdi
	0x49, 0xbc, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002a10 movabsq      $6148914691236517205, %r12
	0x4c, 0x21, 0xe7, //0x00002a1a andq         %r12, %rdi
	0x48, 0x29, 0xfe, //0x00002a1d subq         %rdi, %rsi
	0x48, 0x89, 0xf7, //0x00002a20 movq         %rsi, %rdi
	0x48, 0xba, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00002a23 movabsq      $3689348814741910323, %rdx
	0x48, 0x21, 0xd7, //0x00002a2d andq         %rdx, %rdi
	0x48, 0xc1, 0xee, 0x02, //0x00002a30 shrq         $2, %rsi
	0x48, 0x21, 0xd6, //0x00002a34 andq         %rdx, %rsi
	0x48, 0x01, 0xfe, //0x00002a37 addq         %rdi, %rsi
	0x48, 0x89, 0xf7, //0x00002a3a movq         %rsi, %rdi
	0x48, 0xc1, 0xef, 0x04, //0x00002a3d shrq         $4, %rdi
	0x48, 0x01, 0xf7, //0x00002a41 addq         %rsi, %rdi
	0x48, 0xba, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002a44 movabsq      $1085102592571150095, %rdx
	0x48, 0x21, 0xd7, //0x00002a4e andq         %rdx, %rdi
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00002a51 movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xfa, //0x00002a5b imulq        %rdx, %rdi
	0x48, 0xc1, 0xef, 0x38, //0x00002a5f shrq         $56, %rdi
	0x4c, 0x01, 0xf7, //0x00002a63 addq         %r14, %rdi
	0x48, 0x39, 0xcf, //0x00002a66 cmpq         %rcx, %rdi
	0x0f, 0x86, 0x63, 0x01, 0x00, 0x00, //0x00002a69 jbe          LBB0_508
	0x48, 0x83, 0xc1, 0x01, //0x00002a6f addq         $1, %rcx
	0x48, 0x21, 0xd8, //0x00002a73 andq         %rbx, %rax
	0x0f, 0x85, 0x84, 0xff, 0xff, 0xff, //0x00002a76 jne          LBB0_491
	0xe9, 0x4b, 0xfd, 0xff, 0xff, //0x00002a7c jmp          LBB0_484
	//0x00002a81 LBB0_493
	0x48, 0x85, 0xc0, //0x00002a81 testq        %rax, %rax
	0x0f, 0x8e, 0x46, 0x02, 0x00, 0x00, //0x00002a84 jle          LBB0_518
	0xf3, 0x44, 0x0f, 0x7f, 0x45, 0x80, //0x00002a8a movdqu       %xmm8, $-128(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00002a90 movdqu       %xmm8, $-144(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00002a99 movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00002aa2 movdqu       %xmm8, $-176(%rbp)
	0x44, 0x89, 0xe8, //0x00002aab movl         %r13d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00002aae andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00002ab3 cmpl         $4033, %eax
	0x0f, 0x82, 0x8b, 0xfd, 0xff, 0xff, //0x00002ab8 jb           LBB0_486
	0x48, 0x83, 0x7d, 0xc0, 0x20, //0x00002abe cmpq         $32, $-64(%rbp)
	0x0f, 0x82, 0x30, 0x00, 0x00, 0x00, //0x00002ac3 jb           LBB0_497
	0x41, 0x0f, 0x10, 0x55, 0x00, //0x00002ac9 movups       (%r13), %xmm2
	0x0f, 0x11, 0x95, 0x50, 0xff, 0xff, 0xff, //0x00002ace movups       %xmm2, $-176(%rbp)
	0x41, 0x0f, 0x10, 0x55, 0x10, //0x00002ad5 movups       $16(%r13), %xmm2
	0x0f, 0x11, 0x95, 0x60, 0xff, 0xff, 0xff, //0x00002ada movups       %xmm2, $-160(%rbp)
	0x49, 0x83, 0xc5, 0x20, //0x00002ae1 addq         $32, %r13
	0x48, 0x8b, 0x45, 0xc0, //0x00002ae5 movq         $-64(%rbp), %rax
	0x48, 0x8d, 0x70, 0xe0, //0x00002ae9 leaq         $-32(%rax), %rsi
	0x48, 0x8d, 0x95, 0x70, 0xff, 0xff, 0xff, //0x00002aed leaq         $-144(%rbp), %rdx
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00002af4 jmp          LBB0_498
	//0x00002af9 LBB0_497
	0x48, 0x8d, 0x95, 0x50, 0xff, 0xff, 0xff, //0x00002af9 leaq         $-176(%rbp), %rdx
	0x48, 0x8b, 0x75, 0xc0, //0x00002b00 movq         $-64(%rbp), %rsi
	//0x00002b04 LBB0_498
	0x48, 0x83, 0xfe, 0x10, //0x00002b04 cmpq         $16, %rsi
	0x0f, 0x82, 0x5c, 0x00, 0x00, 0x00, //0x00002b08 jb           LBB0_499
	0x41, 0x0f, 0x10, 0x55, 0x00, //0x00002b0e movups       (%r13), %xmm2
	0x0f, 0x11, 0x12, //0x00002b13 movups       %xmm2, (%rdx)
	0x49, 0x83, 0xc5, 0x10, //0x00002b16 addq         $16, %r13
	0x48, 0x83, 0xc2, 0x10, //0x00002b1a addq         $16, %rdx
	0x48, 0x83, 0xc6, 0xf0, //0x00002b1e addq         $-16, %rsi
	0x48, 0x83, 0xfe, 0x08, //0x00002b22 cmpq         $8, %rsi
	0x0f, 0x83, 0x48, 0x00, 0x00, 0x00, //0x00002b26 jae          LBB0_504
	//0x00002b2c LBB0_500
	0x48, 0x83, 0xfe, 0x04, //0x00002b2c cmpq         $4, %rsi
	0x0f, 0x82, 0x5b, 0x00, 0x00, 0x00, //0x00002b30 jb           LBB0_501
	//0x00002b36 LBB0_505
	0x41, 0x8b, 0x45, 0x00, //0x00002b36 movl         (%r13), %eax
	0x89, 0x02, //0x00002b3a movl         %eax, (%rdx)
	0x49, 0x83, 0xc5, 0x04, //0x00002b3c addq         $4, %r13
	0x48, 0x83, 0xc2, 0x04, //0x00002b40 addq         $4, %rdx
	0x48, 0x83, 0xc6, 0xfc, //0x00002b44 addq         $-4, %rsi
	0x48, 0x83, 0xfe, 0x02, //0x00002b48 cmpq         $2, %rsi
	0x0f, 0x83, 0x49, 0x00, 0x00, 0x00, //0x00002b4c jae          LBB0_506
	//0x00002b52 LBB0_502
	0x4c, 0x89, 0xe8, //0x00002b52 movq         %r13, %rax
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x00002b55 leaq         $-176(%rbp), %r13
	0x48, 0x85, 0xf6, //0x00002b5c testq        %rsi, %rsi
	0x0f, 0x85, 0x5d, 0x00, 0x00, 0x00, //0x00002b5f jne          LBB0_507
	0xe9, 0xdf, 0xfc, 0xff, 0xff, //0x00002b65 jmp          LBB0_486
	//0x00002b6a LBB0_499
	0x48, 0x83, 0xfe, 0x08, //0x00002b6a cmpq         $8, %rsi
	0x0f, 0x82, 0xb8, 0xff, 0xff, 0xff, //0x00002b6e jb           LBB0_500
	//0x00002b74 LBB0_504
	0x49, 0x8b, 0x45, 0x00, //0x00002b74 movq         (%r13), %rax
	0x48, 0x89, 0x02, //0x00002b78 movq         %rax, (%rdx)
	0x49, 0x83, 0xc5, 0x08, //0x00002b7b addq         $8, %r13
	0x48, 0x83, 0xc2, 0x08, //0x00002b7f addq         $8, %rdx
	0x48, 0x83, 0xc6, 0xf8, //0x00002b83 addq         $-8, %rsi
	0x48, 0x83, 0xfe, 0x04, //0x00002b87 cmpq         $4, %rsi
	0x0f, 0x83, 0xa5, 0xff, 0xff, 0xff, //0x00002b8b jae          LBB0_505
	//0x00002b91 LBB0_501
	0x48, 0x83, 0xfe, 0x02, //0x00002b91 cmpq         $2, %rsi
	0x0f, 0x82, 0xb7, 0xff, 0xff, 0xff, //0x00002b95 jb           LBB0_502
	//0x00002b9b LBB0_506
	0x41, 0x0f, 0xb7, 0x45, 0x00, //0x00002b9b movzwl       (%r13), %eax
	0x66, 0x89, 0x02, //0x00002ba0 movw         %ax, (%rdx)
	0x49, 0x83, 0xc5, 0x02, //0x00002ba3 addq         $2, %r13
	0x48, 0x83, 0xc2, 0x02, //0x00002ba7 addq         $2, %rdx
	0x48, 0x83, 0xc6, 0xfe, //0x00002bab addq         $-2, %rsi
	0x4c, 0x89, 0xe8, //0x00002baf movq         %r13, %rax
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x00002bb2 leaq         $-176(%rbp), %r13
	0x48, 0x85, 0xf6, //0x00002bb9 testq        %rsi, %rsi
	0x0f, 0x84, 0x87, 0xfc, 0xff, 0xff, //0x00002bbc je           LBB0_486
	//0x00002bc2 LBB0_507
	0x8a, 0x00, //0x00002bc2 movb         (%rax), %al
	0x88, 0x02, //0x00002bc4 movb         %al, (%rdx)
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x00002bc6 leaq         $-176(%rbp), %r13
	0xe9, 0x77, 0xfc, 0xff, 0xff, //0x00002bcd jmp          LBB0_486
	//0x00002bd2 LBB0_508
	0x48, 0x8b, 0x55, 0xb0, //0x00002bd2 movq         $-80(%rbp), %rdx
	0x48, 0x8b, 0x0a, //0x00002bd6 movq         (%rdx), %rcx
	0x48, 0x0f, 0xbc, 0xc0, //0x00002bd9 bsfq         %rax, %rax
	0x48, 0x2b, 0x45, 0xc0, //0x00002bdd subq         $-64(%rbp), %rax
	0x49, 0x89, 0xd1, //0x00002be1 movq         %rdx, %r9
	0x4c, 0x8d, 0x14, 0x08, //0x00002be4 leaq         (%rax,%rcx), %r10
	0x49, 0x83, 0xc2, 0x01, //0x00002be8 addq         $1, %r10
	0x4d, 0x89, 0x13, //0x00002bec movq         %r10, (%r11)
	0x48, 0x8b, 0x02, //0x00002bef movq         (%rdx), %rax
	0x49, 0x39, 0xc2, //0x00002bf2 cmpq         %rax, %r10
	0x4c, 0x0f, 0x47, 0xd0, //0x00002bf5 cmovaq       %rax, %r10
	//0x00002bf9 LBB0_509
	0x4d, 0x89, 0x13, //0x00002bf9 movq         %r10, (%r11)
	0x48, 0x8b, 0x7d, 0xb8, //0x00002bfc movq         $-72(%rbp), %rdi
	0x4c, 0x8b, 0x75, 0xc8, //0x00002c00 movq         $-56(%rbp), %r14
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002c04 movabsq      $4294977024, %r12
	0xe9, 0x43, 0xfa, 0xff, 0xff, //0x00002c0e jmp          LBB0_452
	//0x00002c13 LBB0_514
	0x4d, 0x29, 0xea, //0x00002c13 subq         %r13, %r10
	0x49, 0x01, 0xc2, //0x00002c16 addq         %rax, %r10
	0x4d, 0x89, 0x13, //0x00002c19 movq         %r10, (%r11)
	0xe9, 0x35, 0xfa, 0xff, 0xff, //0x00002c1c jmp          LBB0_452
	//0x00002c21 LBB0_421
	0x4d, 0x85, 0xc0, //0x00002c21 testq        %r8, %r8
	0x0f, 0x85, 0xb6, 0x00, 0x00, 0x00, //0x00002c24 jne          LBB0_519
	0x4b, 0x8d, 0x04, 0x1e, //0x00002c2a leaq         (%r14,%r11), %rax
	0x48, 0x83, 0xc0, 0x01, //0x00002c2e addq         $1, %rax
	0x49, 0xf7, 0xd6, //0x00002c32 notq         %r14
	0x4d, 0x01, 0xce, //0x00002c35 addq         %r9, %r14
	//0x00002c38 LBB0_423
	0x4d, 0x85, 0xf6, //0x00002c38 testq        %r14, %r14
	0x4c, 0x8b, 0x5d, 0xd0, //0x00002c3b movq         $-48(%rbp), %r11
	0x48, 0x8b, 0x7d, 0xb8, //0x00002c3f movq         $-72(%rbp), %rdi
	0x4c, 0x8b, 0x4d, 0xb0, //0x00002c43 movq         $-80(%rbp), %r9
	0x0f, 0x8f, 0x1d, 0x00, 0x00, 0x00, //0x00002c47 jg           LBB0_511
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x00002c4d jmp          LBB0_424
	//0x00002c52 LBB0_510
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002c52 movq         $-2, %rcx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00002c59 movl         $2, %esi
	0x48, 0x01, 0xf0, //0x00002c5e addq         %rsi, %rax
	0x49, 0x01, 0xce, //0x00002c61 addq         %rcx, %r14
	0x0f, 0x8e, 0x2d, 0x00, 0x00, 0x00, //0x00002c64 jle          LBB0_424
	//0x00002c6a LBB0_511
	0x0f, 0xb6, 0x08, //0x00002c6a movzbl       (%rax), %ecx
	0x80, 0xf9, 0x5c, //0x00002c6d cmpb         $92, %cl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x00002c70 je           LBB0_510
	0x80, 0xf9, 0x22, //0x00002c76 cmpb         $34, %cl
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00002c79 je           LBB0_515
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002c7f movq         $-1, %rcx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00002c86 movl         $1, %esi
	0x48, 0x01, 0xf0, //0x00002c8b addq         %rsi, %rax
	0x49, 0x01, 0xce, //0x00002c8e addq         %rcx, %r14
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x00002c91 jg           LBB0_511
	//0x00002c97 LBB0_424
	0x4c, 0x8b, 0x75, 0xc8, //0x00002c97 movq         $-56(%rbp), %r14
	0xe9, 0xb6, 0xf9, 0xff, 0xff, //0x00002c9b jmp          LBB0_452
	//0x00002ca0 LBB0_515
	0x4c, 0x29, 0xe8, //0x00002ca0 subq         %r13, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00002ca3 addq         $1, %rax
	0x49, 0x89, 0x03, //0x00002ca7 movq         %rax, (%r11)
	0x49, 0x89, 0xc2, //0x00002caa movq         %rax, %r10
	0x4c, 0x8b, 0x75, 0xc8, //0x00002cad movq         $-56(%rbp), %r14
	0xe9, 0xa0, 0xf9, 0xff, 0xff, //0x00002cb1 jmp          LBB0_452
	//0x00002cb6 LBB0_516
	0x4d, 0x01, 0xea, //0x00002cb6 addq         %r13, %r10
	0x48, 0x85, 0xd2, //0x00002cb9 testq        %rdx, %rdx
	0x0f, 0x85, 0xc6, 0xf3, 0xff, 0xff, //0x00002cbc jne          LBB0_404
	0xe9, 0xf7, 0xf3, 0xff, 0xff, //0x00002cc2 jmp          LBB0_410
	//0x00002cc7 LBB0_517
	0x4b, 0x8d, 0x04, 0x2a, //0x00002cc7 leaq         (%r10,%r13), %rax
	0xe9, 0x68, 0xff, 0xff, 0xff, //0x00002ccb jmp          LBB0_423
	//0x00002cd0 LBB0_518
	0x4c, 0x8b, 0x4d, 0xb0, //0x00002cd0 movq         $-80(%rbp), %r9
	0x4d, 0x8b, 0x11, //0x00002cd4 movq         (%r9), %r10
	0x4c, 0x8b, 0x5d, 0xd0, //0x00002cd7 movq         $-48(%rbp), %r11
	0xe9, 0x19, 0xff, 0xff, 0xff, //0x00002cdb jmp          LBB0_509
	//0x00002ce0 LBB0_519
	0x49, 0x8d, 0x41, 0xff, //0x00002ce0 leaq         $-1(%r9), %rax
	0x4c, 0x39, 0xf0, //0x00002ce4 cmpq         %r14, %rax
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00002ce7 jne          LBB0_521
	0x4c, 0x8b, 0x5d, 0xd0, //0x00002ced movq         $-48(%rbp), %r11
	0x48, 0x8b, 0x7d, 0xb8, //0x00002cf1 movq         $-72(%rbp), %rdi
	0x4c, 0x8b, 0x75, 0xc8, //0x00002cf5 movq         $-56(%rbp), %r14
	0xe9, 0xce, 0xf3, 0xff, 0xff, //0x00002cf9 jmp          LBB0_411
	//0x00002cfe LBB0_521
	0x4b, 0x8d, 0x04, 0x1e, //0x00002cfe leaq         (%r14,%r11), %rax
	0x48, 0x83, 0xc0, 0x02, //0x00002d02 addq         $2, %rax
	0x4d, 0x29, 0xf1, //0x00002d06 subq         %r14, %r9
	0x49, 0x83, 0xc1, 0xfe, //0x00002d09 addq         $-2, %r9
	0x4d, 0x89, 0xce, //0x00002d0d movq         %r9, %r14
	0x49, 0xbc, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002d10 movabsq      $4294977024, %r12
	0xe9, 0x19, 0xff, 0xff, 0xff, //0x00002d1a jmp          LBB0_423
	0x90, //0x00002d1f .p2align 4, 0x90
	//0x00002d20 LBB0_480
	0x49, 0x83, 0xc6, 0x10, //0x00002d20 addq         $16, %r14
	0x4c, 0x89, 0xd0, //0x00002d24 movq         %r10, %rax
	0x4c, 0x3b, 0xb5, 0x40, 0xff, 0xff, 0xff, //0x00002d27 cmpq         $-192(%rbp), %r14
	0x48, 0x8b, 0x4d, 0x90, //0x00002d2e movq         $-112(%rbp), %rcx
	0x0f, 0x85, 0x60, 0xd4, 0xff, 0xff, //0x00002d32 jne          LBB0_2
	//0x00002d38 LBB0_481
	0x48, 0x85, 0xc9, //0x00002d38 testq        %rcx, %rcx
	0x0f, 0x84, 0xb8, 0x00, 0x00, 0x00, //0x00002d3b je           LBB0_522
	0x0f, 0x10, 0x05, 0x38, 0xd3, 0xff, 0xff, //0x00002d41 movups       $-11464(%rip), %xmm0  /* LCPI0_8+0(%rip) */
	0x0f, 0x11, 0x01, //0x00002d48 movups       %xmm0, (%rcx)
	0x4c, 0x8b, 0x37, //0x00002d4b movq         (%rdi), %r14
	0x4c, 0x89, 0xf0, //0x00002d4e movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x00002d51 notq         %rax
	0x48, 0x89, 0x45, 0xa0, //0x00002d54 movq         %rax, $-96(%rbp)
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00002d58 movl         $1, %ebx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00002d5d movl         $1, %eax
	0x4c, 0x29, 0xf0, //0x00002d62 subq         %r14, %rax
	0x48, 0x89, 0x45, 0xa8, //0x00002d65 movq         %rax, $-88(%rbp)
	0x4d, 0x8b, 0x13, //0x00002d69 movq         (%r11), %r10
	0x4c, 0x89, 0xf0, //0x00002d6c movq         %r14, %rax
	0x48, 0xf7, 0xd8, //0x00002d6f negq         %rax
	0x48, 0x89, 0x45, 0xb0, //0x00002d72 movq         %rax, $-80(%rbp)
	0x49, 0x8d, 0x46, 0xff, //0x00002d76 leaq         $-1(%r14), %rax
	0x48, 0x89, 0x85, 0x48, 0xff, 0xff, 0xff, //0x00002d7a movq         %rax, $-184(%rbp)
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002d81 movq         $-1, %r15
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002d88 movabsq      $4294977024, %r13
	0xf3, 0x0f, 0x6f, 0x05, 0x96, 0xd2, 0xff, 0xff, //0x00002d92 movdqu       $-11626(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x9e, 0xd2, 0xff, 0xff, //0x00002d9a movdqu       $-11618(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x05, 0xe5, 0xd2, 0xff, 0xff, //0x00002da2 movdqu       $-11547(%rip), %xmm8  /* LCPI0_9+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x0d, 0xec, 0xd2, 0xff, 0xff, //0x00002dab movdqu       $-11540(%rip), %xmm9  /* LCPI0_10+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xf3, 0xd2, 0xff, 0xff, //0x00002db4 movdqu       $-11533(%rip), %xmm10  /* LCPI0_11+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x1d, 0xfa, 0xd2, 0xff, 0xff, //0x00002dbd movdqu       $-11526(%rip), %xmm11  /* LCPI0_12+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x25, 0x41, 0xd2, 0xff, 0xff, //0x00002dc6 movdqu       $-11711(%rip), %xmm12  /* LCPI0_1+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0xf8, 0xd2, 0xff, 0xff, //0x00002dcf movdqu       $-11528(%rip), %xmm13  /* LCPI0_13+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x15, 0x00, 0xd3, 0xff, 0xff, //0x00002dd8 movdqu       $-11520(%rip), %xmm2  /* LCPI0_14+0(%rip) */
	0x4c, 0x8d, 0x0d, 0xd5, 0x29, 0x00, 0x00, //0x00002de0 leaq         $10709(%rip), %r9  /* LJTI0_2+0(%rip) */
	0xe9, 0x9b, 0x01, 0x00, 0x00, //0x00002de7 jmp          LBB0_554
	//0x00002dec LBB0_187
	0x3c, 0x7d, //0x00002dec cmpb         $125, %al
	0x0f, 0x84, 0x1f, 0x01, 0x00, 0x00, //0x00002dee je           LBB0_546
	0xe9, 0xdc, 0x17, 0x00, 0x00, //0x00002df4 jmp          LBB0_864
	//0x00002df9 LBB0_522
	0x4c, 0x8b, 0x2f, //0x00002df9 movq         (%rdi), %r13
	0x48, 0x8b, 0x57, 0x08, //0x00002dfc movq         $8(%rdi), %rdx
	0x49, 0x8b, 0x33, //0x00002e00 movq         (%r11), %rsi
	0x48, 0x39, 0xd6, //0x00002e03 cmpq         %rdx, %rsi
	0x0f, 0x83, 0x27, 0x00, 0x00, 0x00, //0x00002e06 jae          LBB0_527
	0x41, 0x8a, 0x44, 0x35, 0x00, //0x00002e0c movb         (%r13,%rsi), %al
	0x3c, 0x0d, //0x00002e11 cmpb         $13, %al
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00002e13 je           LBB0_527
	0x3c, 0x20, //0x00002e19 cmpb         $32, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002e1b je           LBB0_527
	0x04, 0xf5, //0x00002e21 addb         $-11, %al
	0x3c, 0xfe, //0x00002e23 cmpb         $-2, %al
	0x0f, 0x83, 0x08, 0x00, 0x00, 0x00, //0x00002e25 jae          LBB0_527
	0x49, 0x89, 0xf2, //0x00002e2b movq         %rsi, %r10
	0xe9, 0xe4, 0x17, 0x00, 0x00, //0x00002e2e jmp          LBB0_870
	//0x00002e33 LBB0_527
	0x4c, 0x8d, 0x56, 0x01, //0x00002e33 leaq         $1(%rsi), %r10
	0x49, 0x39, 0xd2, //0x00002e37 cmpq         %rdx, %r10
	0x0f, 0x83, 0x1f, 0x00, 0x00, 0x00, //0x00002e3a jae          LBB0_531
	0x43, 0x8a, 0x44, 0x15, 0x00, //0x00002e40 movb         (%r13,%r10), %al
	0x3c, 0x0d, //0x00002e45 cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002e47 je           LBB0_531
	0x3c, 0x20, //0x00002e4d cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00002e4f je           LBB0_531
	0x04, 0xf5, //0x00002e55 addb         $-11, %al
	0x3c, 0xfe, //0x00002e57 cmpb         $-2, %al
	0x0f, 0x82, 0xb8, 0x17, 0x00, 0x00, //0x00002e59 jb           LBB0_870
	//0x00002e5f LBB0_531
	0x4c, 0x8d, 0x56, 0x02, //0x00002e5f leaq         $2(%rsi), %r10
	0x49, 0x39, 0xd2, //0x00002e63 cmpq         %rdx, %r10
	0x0f, 0x83, 0x1f, 0x00, 0x00, 0x00, //0x00002e66 jae          LBB0_535
	0x43, 0x8a, 0x44, 0x15, 0x00, //0x00002e6c movb         (%r13,%r10), %al
	0x3c, 0x0d, //0x00002e71 cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002e73 je           LBB0_535
	0x3c, 0x20, //0x00002e79 cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00002e7b je           LBB0_535
	0x04, 0xf5, //0x00002e81 addb         $-11, %al
	0x3c, 0xfe, //0x00002e83 cmpb         $-2, %al
	0x0f, 0x82, 0x8c, 0x17, 0x00, 0x00, //0x00002e85 jb           LBB0_870
	//0x00002e8b LBB0_535
	0x4c, 0x8d, 0x56, 0x03, //0x00002e8b leaq         $3(%rsi), %r10
	0x49, 0x39, 0xd2, //0x00002e8f cmpq         %rdx, %r10
	0x0f, 0x83, 0x1f, 0x00, 0x00, 0x00, //0x00002e92 jae          LBB0_539
	0x43, 0x8a, 0x44, 0x15, 0x00, //0x00002e98 movb         (%r13,%r10), %al
	0x3c, 0x0d, //0x00002e9d cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002e9f je           LBB0_539
	0x3c, 0x20, //0x00002ea5 cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00002ea7 je           LBB0_539
	0x04, 0xf5, //0x00002ead addb         $-11, %al
	0x3c, 0xfe, //0x00002eaf cmpb         $-2, %al
	0x0f, 0x82, 0x60, 0x17, 0x00, 0x00, //0x00002eb1 jb           LBB0_870
	//0x00002eb7 LBB0_539
	0x48, 0x83, 0xc6, 0x04, //0x00002eb7 addq         $4, %rsi
	0x48, 0x39, 0xf2, //0x00002ebb cmpq         %rsi, %rdx
	0x0f, 0x86, 0x7d, 0x00, 0x00, 0x00, //0x00002ebe jbe          LBB0_549
	0x0f, 0x84, 0x2e, 0x17, 0x00, 0x00, //0x00002ec4 je           LBB0_867
	0x4a, 0x8d, 0x04, 0x2a, //0x00002eca leaq         (%rdx,%r13), %rax
	0x48, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002ece movabsq      $4294977024, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002ed8 .p2align 4, 0x90
	//0x00002ee0 LBB0_542
	0x41, 0x0f, 0xbe, 0x5c, 0x35, 0x00, //0x00002ee0 movsbl       (%r13,%rsi), %ebx
	0x83, 0xfb, 0x20, //0x00002ee6 cmpl         $32, %ebx
	0x0f, 0x87, 0x15, 0x17, 0x00, 0x00, //0x00002ee9 ja           LBB0_869
	0x48, 0x0f, 0xa3, 0xd9, //0x00002eef btq          %rbx, %rcx
	0x0f, 0x83, 0x0b, 0x17, 0x00, 0x00, //0x00002ef3 jae          LBB0_869
	0x48, 0x83, 0xc6, 0x01, //0x00002ef9 addq         $1, %rsi
	0x48, 0x39, 0xf2, //0x00002efd cmpq         %rsi, %rdx
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x00002f00 jne          LBB0_542
	0xe9, 0xf3, 0x16, 0x00, 0x00, //0x00002f06 jmp          LBB0_868
	//0x00002f0b LBB0_545
	0x3c, 0x5d, //0x00002f0b cmpb         $93, %al
	0x0f, 0x85, 0xc2, 0x16, 0x00, 0x00, //0x00002f0d jne          LBB0_864
	//0x00002f13 LBB0_546
	0x49, 0x83, 0xc2, 0xff, //0x00002f13 addq         $-1, %r10
	0x4d, 0x89, 0x13, //0x00002f17 movq         %r10, (%r11)
	0x49, 0xc7, 0xc4, 0xdf, 0xff, 0xff, 0xff, //0x00002f1a movq         $-33, %r12
	0xe9, 0xbd, 0x16, 0x00, 0x00, //0x00002f21 jmp          LBB0_866
	//0x00002f26 LBB0_862
	0x49, 0x89, 0xd2, //0x00002f26 movq         %rdx, %r10
	0xe9, 0xa7, 0x16, 0x00, 0x00, //0x00002f29 jmp          LBB0_864
	//0x00002f2e LBB0_548
	0x49, 0x83, 0xc2, 0xff, //0x00002f2e addq         $-1, %r10
	0x4d, 0x89, 0x13, //0x00002f32 movq         %r10, (%r11)
	0x49, 0xc7, 0xc4, 0xde, 0xff, 0xff, 0xff, //0x00002f35 movq         $-34, %r12
	0xe9, 0xa2, 0x16, 0x00, 0x00, //0x00002f3c jmp          LBB0_866
	//0x00002f41 LBB0_549
	0x48, 0x8b, 0x45, 0xd0, //0x00002f41 movq         $-48(%rbp), %rax
	0x48, 0x89, 0x30, //0x00002f45 movq         %rsi, (%rax)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00002f48 movq         $-1, %r12
	0xe9, 0x8f, 0x16, 0x00, 0x00, //0x00002f4f jmp          LBB0_866
	//0x00002f54 LBB0_550
	0x4d, 0x8d, 0x53, 0x04, //0x00002f54 leaq         $4(%r11), %r10
	//0x00002f58 LBB0_551
	0x48, 0x8b, 0x45, 0xd0, //0x00002f58 movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x10, //0x00002f5c movq         %r10, (%rax)
	0x4d, 0x89, 0xdc, //0x00002f5f movq         %r11, %r12
	0x48, 0xb8, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00002f62 movabsq      $9223372036854775806, %rax
	0x49, 0x39, 0xc3, //0x00002f6c cmpq         %rax, %r11
	0x0f, 0x87, 0x6e, 0x16, 0x00, 0x00, //0x00002f6f ja           LBB0_866
	//0x00002f75 LBB0_552
	0x48, 0x8b, 0x11, //0x00002f75 movq         (%rcx), %rdx
	0x48, 0x89, 0xd3, //0x00002f78 movq         %rdx, %rbx
	0x4d, 0x89, 0xfc, //0x00002f7b movq         %r15, %r12
	0x48, 0x85, 0xd2, //0x00002f7e testq        %rdx, %rdx
	0x0f, 0x84, 0x5c, 0x16, 0x00, 0x00, //0x00002f81 je           LBB0_866
	//0x00002f87 LBB0_554
	0x48, 0x8b, 0x57, 0x08, //0x00002f87 movq         $8(%rdi), %rdx
	0x49, 0x39, 0xd2, //0x00002f8b cmpq         %rdx, %r10
	0x0f, 0x83, 0x2c, 0x00, 0x00, 0x00, //0x00002f8e jae          LBB0_559
	0x43, 0x8a, 0x04, 0x16, //0x00002f94 movb         (%r14,%r10), %al
	0x3c, 0x0d, //0x00002f98 cmpb         $13, %al
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x00002f9a je           LBB0_559
	0x3c, 0x20, //0x00002fa0 cmpb         $32, %al
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00002fa2 je           LBB0_559
	0x04, 0xf5, //0x00002fa8 addb         $-11, %al
	0x3c, 0xfe, //0x00002faa cmpb         $-2, %al
	0x0f, 0x83, 0x0e, 0x00, 0x00, 0x00, //0x00002fac jae          LBB0_559
	0x4d, 0x89, 0xd3, //0x00002fb2 movq         %r10, %r11
	0xe9, 0xfe, 0x00, 0x00, 0x00, //0x00002fb5 jmp          LBB0_580
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002fba .p2align 4, 0x90
	//0x00002fc0 LBB0_559
	0x4d, 0x8d, 0x5a, 0x01, //0x00002fc0 leaq         $1(%r10), %r11
	0x49, 0x39, 0xd3, //0x00002fc4 cmpq         %rdx, %r11
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002fc7 jae          LBB0_563
	0x43, 0x8a, 0x04, 0x1e, //0x00002fcd movb         (%r14,%r11), %al
	0x3c, 0x0d, //0x00002fd1 cmpb         $13, %al
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00002fd3 je           LBB0_563
	0x3c, 0x20, //0x00002fd9 cmpb         $32, %al
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x00002fdb je           LBB0_563
	0x04, 0xf5, //0x00002fe1 addb         $-11, %al
	0x3c, 0xfe, //0x00002fe3 cmpb         $-2, %al
	0x0f, 0x82, 0xcd, 0x00, 0x00, 0x00, //0x00002fe5 jb           LBB0_580
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00002feb .p2align 4, 0x90
	//0x00002ff0 LBB0_563
	0x4d, 0x8d, 0x5a, 0x02, //0x00002ff0 leaq         $2(%r10), %r11
	0x49, 0x39, 0xd3, //0x00002ff4 cmpq         %rdx, %r11
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002ff7 jae          LBB0_567
	0x43, 0x8a, 0x04, 0x1e, //0x00002ffd movb         (%r14,%r11), %al
	0x3c, 0x0d, //0x00003001 cmpb         $13, %al
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00003003 je           LBB0_567
	0x3c, 0x20, //0x00003009 cmpb         $32, %al
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x0000300b je           LBB0_567
	0x04, 0xf5, //0x00003011 addb         $-11, %al
	0x3c, 0xfe, //0x00003013 cmpb         $-2, %al
	0x0f, 0x82, 0x9d, 0x00, 0x00, 0x00, //0x00003015 jb           LBB0_580
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000301b .p2align 4, 0x90
	//0x00003020 LBB0_567
	0x4d, 0x8d, 0x5a, 0x03, //0x00003020 leaq         $3(%r10), %r11
	0x49, 0x39, 0xd3, //0x00003024 cmpq         %rdx, %r11
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00003027 jae          LBB0_571
	0x43, 0x8a, 0x04, 0x1e, //0x0000302d movb         (%r14,%r11), %al
	0x3c, 0x0d, //0x00003031 cmpb         $13, %al
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00003033 je           LBB0_571
	0x3c, 0x20, //0x00003039 cmpb         $32, %al
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x0000303b je           LBB0_571
	0x04, 0xf5, //0x00003041 addb         $-11, %al
	0x3c, 0xfe, //0x00003043 cmpb         $-2, %al
	0x0f, 0x82, 0x6d, 0x00, 0x00, 0x00, //0x00003045 jb           LBB0_580
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000304b .p2align 4, 0x90
	//0x00003050 LBB0_571
	0x49, 0x83, 0xc2, 0x04, //0x00003050 addq         $4, %r10
	0x4c, 0x39, 0xd2, //0x00003054 cmpq         %r10, %rdx
	0x0f, 0x86, 0x62, 0x15, 0x00, 0x00, //0x00003057 jbe          LBB0_860
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x0000305d je           LBB0_577
	0x49, 0x8d, 0x04, 0x16, //0x00003063 leaq         (%r14,%rdx), %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003067 .p2align 4, 0x90
	//0x00003070 LBB0_574
	0x43, 0x0f, 0xbe, 0x34, 0x16, //0x00003070 movsbl       (%r14,%r10), %esi
	0x83, 0xfe, 0x20, //0x00003075 cmpl         $32, %esi
	0x0f, 0x87, 0x2e, 0x00, 0x00, 0x00, //0x00003078 ja           LBB0_579
	0x49, 0x0f, 0xa3, 0xf5, //0x0000307e btq          %rsi, %r13
	0x0f, 0x83, 0x24, 0x00, 0x00, 0x00, //0x00003082 jae          LBB0_579
	0x49, 0x83, 0xc2, 0x01, //0x00003088 addq         $1, %r10
	0x4c, 0x39, 0xd2, //0x0000308c cmpq         %r10, %rdx
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x0000308f jne          LBB0_574
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x00003095 jmp          LBB0_578
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000309a .p2align 4, 0x90
	//0x000030a0 LBB0_577
	0x4d, 0x01, 0xf2, //0x000030a0 addq         %r14, %r10
	0x4c, 0x89, 0xd0, //0x000030a3 movq         %r10, %rax
	//0x000030a6 LBB0_578
	0x4c, 0x29, 0xf0, //0x000030a6 subq         %r14, %rax
	0x49, 0x89, 0xc2, //0x000030a9 movq         %rax, %r10
	//0x000030ac LBB0_579
	0x4d, 0x89, 0xd3, //0x000030ac movq         %r10, %r11
	0x49, 0x39, 0xd2, //0x000030af cmpq         %rdx, %r10
	0x0f, 0x83, 0x0e, 0x15, 0x00, 0x00, //0x000030b2 jae          LBB0_861
	//0x000030b8 LBB0_580
	0x4d, 0x8d, 0x53, 0x01, //0x000030b8 leaq         $1(%r11), %r10
	0x48, 0x8b, 0x45, 0xd0, //0x000030bc movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x10, //0x000030c0 movq         %r10, (%rax)
	0x43, 0x0f, 0xbe, 0x34, 0x1e, //0x000030c3 movsbl       (%r14,%r11), %esi
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000030c8 movq         $-1, %r12
	0x85, 0xf6, //0x000030cf testl        %esi, %esi
	0x0f, 0x84, 0x0c, 0x15, 0x00, 0x00, //0x000030d1 je           LBB0_866
	0x48, 0x8d, 0x53, 0xff, //0x000030d7 leaq         $-1(%rbx), %rdx
	0x8b, 0x04, 0xd9, //0x000030db movl         (%rcx,%rbx,8), %eax
	0x49, 0x83, 0xff, 0xff, //0x000030de cmpq         $-1, %r15
	0x4d, 0x0f, 0x44, 0xfb, //0x000030e2 cmoveq       %r11, %r15
	0x83, 0xc0, 0xff, //0x000030e6 addl         $-1, %eax
	0x83, 0xf8, 0x05, //0x000030e9 cmpl         $5, %eax
	0x0f, 0x87, 0x20, 0x00, 0x00, 0x00, //0x000030ec ja           LBB0_586
	0x49, 0x63, 0x04, 0x81, //0x000030f2 movslq       (%r9,%rax,4), %rax
	0x4c, 0x01, 0xc8, //0x000030f6 addq         %r9, %rax
	0xff, 0xe0, //0x000030f9 jmpq         *%rax
	//0x000030fb LBB0_583
	0x83, 0xfe, 0x2c, //0x000030fb cmpl         $44, %esi
	0x0f, 0x84, 0x96, 0x04, 0x00, 0x00, //0x000030fe je           LBB0_654
	0x83, 0xfe, 0x5d, //0x00003104 cmpl         $93, %esi
	0x0f, 0x84, 0x76, 0x04, 0x00, 0x00, //0x00003107 je           LBB0_585
	0xe9, 0xca, 0x14, 0x00, 0x00, //0x0000310d jmp          LBB0_865
	//0x00003112 LBB0_586
	0x48, 0x89, 0x11, //0x00003112 movq         %rdx, (%rcx)
	0x83, 0xfe, 0x7b, //0x00003115 cmpl         $123, %esi
	0x0f, 0x86, 0x1c, 0x02, 0x00, 0x00, //0x00003118 jbe          LBB0_615
	0xe9, 0xb9, 0x14, 0x00, 0x00, //0x0000311e jmp          LBB0_865
	//0x00003123 LBB0_587
	0x83, 0xfe, 0x2c, //0x00003123 cmpl         $44, %esi
	0x0f, 0x85, 0x4e, 0x04, 0x00, 0x00, //0x00003126 jne          LBB0_588
	0x48, 0x81, 0xfb, 0xff, 0x0f, 0x00, 0x00, //0x0000312c cmpq         $4095, %rbx
	0x0f, 0x8f, 0xef, 0x15, 0x00, 0x00, //0x00003133 jg           LBB0_968
	0x48, 0x8d, 0x43, 0x01, //0x00003139 leaq         $1(%rbx), %rax
	0x48, 0x89, 0x01, //0x0000313d movq         %rax, (%rcx)
	0x48, 0xc7, 0x44, 0xd9, 0x08, 0x03, 0x00, 0x00, 0x00, //0x00003140 movq         $3, $8(%rcx,%rbx,8)
	0xe9, 0x27, 0xfe, 0xff, 0xff, //0x00003149 jmp          LBB0_552
	//0x0000314e LBB0_589
	0x40, 0x80, 0xfe, 0x22, //0x0000314e cmpb         $34, %sil
	0x0f, 0x85, 0x84, 0x14, 0x00, 0x00, //0x00003152 jne          LBB0_865
	0x4c, 0x89, 0x7d, 0xc0, //0x00003158 movq         %r15, $-64(%rbp)
	0x48, 0xc7, 0x04, 0xd9, 0x04, 0x00, 0x00, 0x00, //0x0000315c movq         $4, (%rcx,%rbx,8)
	0x48, 0x8b, 0x47, 0x08, //0x00003164 movq         $8(%rdi), %rax
	0x49, 0x89, 0xc7, //0x00003168 movq         %rax, %r15
	0x4d, 0x29, 0xd7, //0x0000316b subq         %r10, %r15
	0x0f, 0x84, 0xb1, 0x21, 0x00, 0x00, //0x0000316e je           LBB0_984
	0x48, 0x89, 0x45, 0x98, //0x00003174 movq         %rax, $-104(%rbp)
	0x49, 0x83, 0xff, 0x40, //0x00003178 cmpq         $64, %r15
	0x0f, 0x82, 0xe2, 0x0e, 0x00, 0x00, //0x0000317c jb           LBB0_799
	0x48, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x00003182 movq         $-2, %rdi
	0x4c, 0x29, 0xdf, //0x00003189 subq         %r11, %rdi
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x0000318c movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc9, //0x00003194 xorl         %r9d, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003197 .p2align 4, 0x90
	//0x000031a0 LBB0_593
	0xf3, 0x43, 0x0f, 0x6f, 0x3c, 0x16, //0x000031a0 movdqu       (%r14,%r10), %xmm7
	0xf3, 0x43, 0x0f, 0x6f, 0x64, 0x16, 0x10, //0x000031a6 movdqu       $16(%r14,%r10), %xmm4
	0xf3, 0x43, 0x0f, 0x6f, 0x6c, 0x16, 0x20, //0x000031ad movdqu       $32(%r14,%r10), %xmm5
	0xf3, 0x43, 0x0f, 0x6f, 0x74, 0x16, 0x30, //0x000031b4 movdqu       $48(%r14,%r10), %xmm6
	0x66, 0x0f, 0x6f, 0xdf, //0x000031bb movdqa       %xmm7, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000031bf pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xeb, //0x000031c3 pmovmskb     %xmm3, %r13d
	0x66, 0x0f, 0x6f, 0xdc, //0x000031c8 movdqa       %xmm4, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000031cc pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x000031d0 pmovmskb     %xmm3, %ecx
	0x66, 0x0f, 0x6f, 0xdd, //0x000031d4 movdqa       %xmm5, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000031d8 pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x000031dc pmovmskb     %xmm3, %edx
	0x66, 0x0f, 0x6f, 0xde, //0x000031e0 movdqa       %xmm6, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x000031e4 pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xc3, //0x000031e8 pmovmskb     %xmm3, %r8d
	0x66, 0x0f, 0x74, 0xf9, //0x000031ed pcmpeqb      %xmm1, %xmm7
	0x66, 0x0f, 0xd7, 0xf7, //0x000031f1 pmovmskb     %xmm7, %esi
	0x66, 0x0f, 0x74, 0xe1, //0x000031f5 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x000031f9 pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x74, 0xe9, //0x000031fd pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xc5, //0x00003201 pmovmskb     %xmm5, %eax
	0x66, 0x0f, 0x74, 0xf1, //0x00003205 pcmpeqb      %xmm1, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xe6, //0x00003209 pmovmskb     %xmm6, %r12d
	0x49, 0xc1, 0xe0, 0x30, //0x0000320e shlq         $48, %r8
	0x48, 0xc1, 0xe2, 0x20, //0x00003212 shlq         $32, %rdx
	0x4c, 0x09, 0xc2, //0x00003216 orq          %r8, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x00003219 shlq         $16, %rcx
	0x48, 0x09, 0xd1, //0x0000321d orq          %rdx, %rcx
	0x49, 0x09, 0xcd, //0x00003220 orq          %rcx, %r13
	0x49, 0xc1, 0xe4, 0x30, //0x00003223 shlq         $48, %r12
	0x48, 0xc1, 0xe0, 0x20, //0x00003227 shlq         $32, %rax
	0x4c, 0x09, 0xe0, //0x0000322b orq          %r12, %rax
	0x48, 0xc1, 0xe3, 0x10, //0x0000322e shlq         $16, %rbx
	0x48, 0x09, 0xc3, //0x00003232 orq          %rax, %rbx
	0x48, 0x09, 0xde, //0x00003235 orq          %rbx, %rsi
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00003238 jne          LBB0_602
	0x4d, 0x85, 0xc9, //0x0000323e testq        %r9, %r9
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00003241 jne          LBB0_604
	0x45, 0x31, 0xc9, //0x00003247 xorl         %r9d, %r9d
	0x4d, 0x85, 0xed, //0x0000324a testq        %r13, %r13
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x0000324d jne          LBB0_605
	//0x00003253 LBB0_596
	0x49, 0x83, 0xc7, 0xc0, //0x00003253 addq         $-64, %r15
	0x48, 0x83, 0xc7, 0xc0, //0x00003257 addq         $-64, %rdi
	0x49, 0x83, 0xc2, 0x40, //0x0000325b addq         $64, %r10
	0x49, 0x83, 0xff, 0x3f, //0x0000325f cmpq         $63, %r15
	0x0f, 0x87, 0x37, 0xff, 0xff, 0xff, //0x00003263 ja           LBB0_593
	0xe9, 0x4f, 0x0b, 0x00, 0x00, //0x00003269 jmp          LBB0_597
	//0x0000326e LBB0_602
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x0000326e cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00003273 jne          LBB0_604
	0x48, 0x0f, 0xbc, 0xc6, //0x00003279 bsfq         %rsi, %rax
	0x4c, 0x01, 0xd0, //0x0000327d addq         %r10, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00003280 movq         %rax, $-56(%rbp)
	//0x00003284 LBB0_604
	0x4c, 0x89, 0xc8, //0x00003284 movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00003287 notq         %rax
	0x48, 0x21, 0xf0, //0x0000328a andq         %rsi, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x0000328d leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xc9, //0x00003291 orq          %r9, %rcx
	0x48, 0x89, 0xca, //0x00003294 movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00003297 notq         %rdx
	0x48, 0x21, 0xf2, //0x0000329a andq         %rsi, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000329d movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x000032a7 andq         %rsi, %rdx
	0x45, 0x31, 0xc9, //0x000032aa xorl         %r9d, %r9d
	0x48, 0x01, 0xc2, //0x000032ad addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc1, //0x000032b0 setb         %r9b
	0x48, 0x01, 0xd2, //0x000032b4 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000032b7 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x000032c1 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x000032c4 andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x000032c7 notq         %rdx
	0x49, 0x21, 0xd5, //0x000032ca andq         %rdx, %r13
	0x4d, 0x85, 0xed, //0x000032cd testq        %r13, %r13
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x000032d0 je           LBB0_596
	//0x000032d6 LBB0_605
	0x4d, 0x0f, 0xbc, 0xd5, //0x000032d6 bsfq         %r13, %r10
	0x49, 0x29, 0xfa, //0x000032da subq         %rdi, %r10
	0x48, 0x8b, 0x7d, 0xb8, //0x000032dd movq         $-72(%rbp), %rdi
	0x48, 0x8b, 0x4d, 0x90, //0x000032e1 movq         $-112(%rbp), %rcx
	0x4c, 0x8b, 0x7d, 0xc0, //0x000032e5 movq         $-64(%rbp), %r15
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000032e9 movabsq      $4294977024, %r13
	0x4c, 0x8d, 0x0d, 0xc2, 0x24, 0x00, 0x00, //0x000032f3 leaq         $9410(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x4d, 0x85, 0xd2, //0x000032fa testq        %r10, %r10
	0x0f, 0x89, 0x55, 0xfc, 0xff, 0xff, //0x000032fd jns          LBB0_551
	0xe9, 0x2c, 0x14, 0x00, 0x00, //0x00003303 jmp          LBB0_608
	//0x00003308 LBB0_611
	0x40, 0x80, 0xfe, 0x3a, //0x00003308 cmpb         $58, %sil
	0x0f, 0x85, 0xca, 0x12, 0x00, 0x00, //0x0000330c jne          LBB0_865
	0x48, 0xc7, 0x04, 0xd9, 0x00, 0x00, 0x00, 0x00, //0x00003312 movq         $0, (%rcx,%rbx,8)
	0xe9, 0x56, 0xfc, 0xff, 0xff, //0x0000331a jmp          LBB0_552
	//0x0000331f LBB0_613
	0x40, 0x80, 0xfe, 0x5d, //0x0000331f cmpb         $93, %sil
	0x0f, 0x84, 0x5a, 0x02, 0x00, 0x00, //0x00003323 je           LBB0_585
	0x48, 0xc7, 0x04, 0xd9, 0x01, 0x00, 0x00, 0x00, //0x00003329 movq         $1, (%rcx,%rbx,8)
	0x83, 0xfe, 0x7b, //0x00003331 cmpl         $123, %esi
	0x0f, 0x87, 0xa2, 0x12, 0x00, 0x00, //0x00003334 ja           LBB0_865
	//0x0000333a LBB0_615
	0x4f, 0x8d, 0x04, 0x1e, //0x0000333a leaq         (%r14,%r11), %r8
	0x89, 0xf0, //0x0000333e movl         %esi, %eax
	0x48, 0x8d, 0x15, 0x8d, 0x24, 0x00, 0x00, //0x00003340 leaq         $9357(%rip), %rdx  /* LJTI0_3+0(%rip) */
	0x48, 0x63, 0x04, 0x82, //0x00003347 movslq       (%rdx,%rax,4), %rax
	0x48, 0x01, 0xd0, //0x0000334b addq         %rdx, %rax
	0xff, 0xe0, //0x0000334e jmpq         *%rax
	//0x00003350 LBB0_618
	0x4c, 0x8b, 0x4f, 0x08, //0x00003350 movq         $8(%rdi), %r9
	0x4d, 0x29, 0xd9, //0x00003354 subq         %r11, %r9
	0x0f, 0x84, 0x0c, 0x14, 0x00, 0x00, //0x00003357 je           LBB0_886
	0x41, 0x80, 0x38, 0x30, //0x0000335d cmpb         $48, (%r8)
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00003361 jne          LBB0_623
	0x49, 0x83, 0xf9, 0x01, //0x00003367 cmpq         $1, %r9
	0x0f, 0x84, 0x52, 0x0b, 0x00, 0x00, //0x0000336b je           LBB0_792
	0x43, 0x8a, 0x04, 0x16, //0x00003371 movb         (%r14,%r10), %al
	0x04, 0xd2, //0x00003375 addb         $-46, %al
	0x3c, 0x37, //0x00003377 cmpb         $55, %al
	0x0f, 0x87, 0x44, 0x0b, 0x00, 0x00, //0x00003379 ja           LBB0_792
	0x0f, 0xb6, 0xc0, //0x0000337f movzbl       %al, %eax
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00003382 movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xc2, //0x0000338c btq          %rax, %rdx
	0x0f, 0x83, 0x2d, 0x0b, 0x00, 0x00, //0x00003390 jae          LBB0_792
	//0x00003396 LBB0_623
	0x4c, 0x89, 0x7d, 0xc0, //0x00003396 movq         %r15, $-64(%rbp)
	0x49, 0x83, 0xf9, 0x10, //0x0000339a cmpq         $16, %r9
	0x0f, 0x82, 0xe7, 0x0c, 0x00, 0x00, //0x0000339e jb           LBB0_801
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x000033a4 movq         $-1, %r13
	0x45, 0x31, 0xd2, //0x000033ab xorl         %r10d, %r10d
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000033ae movq         $-1, %r15
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000033b5 movq         $-1, %r12
	0x4c, 0x89, 0xcb, //0x000033bc movq         %r9, %rbx
	0x90, //0x000033bf .p2align 4, 0x90
	//0x000033c0 LBB0_625
	0xf3, 0x43, 0x0f, 0x6f, 0x1c, 0x10, //0x000033c0 movdqu       (%r8,%r10), %xmm3
	0x66, 0x0f, 0x6f, 0xe3, //0x000033c6 movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe0, //0x000033ca pcmpeqb      %xmm8, %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x000033cf movdqa       %xmm3, %xmm5
	0x66, 0x41, 0x0f, 0x74, 0xe9, //0x000033d3 pcmpeqb      %xmm9, %xmm5
	0x66, 0x0f, 0xeb, 0xec, //0x000033d8 por          %xmm4, %xmm5
	0x66, 0x0f, 0x6f, 0xe3, //0x000033dc movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0xfc, 0xe2, //0x000033e0 paddb        %xmm10, %xmm4
	0x66, 0x0f, 0x6f, 0xf4, //0x000033e5 movdqa       %xmm4, %xmm6
	0x66, 0x41, 0x0f, 0xda, 0xf3, //0x000033e9 pminub       %xmm11, %xmm6
	0x66, 0x0f, 0x74, 0xf4, //0x000033ee pcmpeqb      %xmm4, %xmm6
	0x66, 0x0f, 0x6f, 0xe3, //0x000033f2 movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0xdb, 0xe4, //0x000033f6 pand         %xmm12, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x000033fb pcmpeqb      %xmm13, %xmm3
	0x66, 0x0f, 0x74, 0xe2, //0x00003400 pcmpeqb      %xmm2, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x00003404 pmovmskb     %xmm4, %edi
	0x66, 0x0f, 0xeb, 0xe3, //0x00003408 por          %xmm3, %xmm4
	0x66, 0x0f, 0xeb, 0xe5, //0x0000340c por          %xmm5, %xmm4
	0x66, 0x0f, 0xeb, 0xe6, //0x00003410 por          %xmm6, %xmm4
	0x66, 0x0f, 0xd7, 0xf3, //0x00003414 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0xd7, 0xd5, //0x00003418 pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0xd7, 0xc4, //0x0000341c pmovmskb     %xmm4, %eax
	0xf7, 0xd0, //0x00003420 notl         %eax
	0x0f, 0xbc, 0xc8, //0x00003422 bsfl         %eax, %ecx
	0x83, 0xf9, 0x10, //0x00003425 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00003428 je           LBB0_627
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x0000342e movl         $-1, %eax
	0xd3, 0xe0, //0x00003433 shll         %cl, %eax
	0xf7, 0xd0, //0x00003435 notl         %eax
	0x21, 0xc6, //0x00003437 andl         %eax, %esi
	0x21, 0xc7, //0x00003439 andl         %eax, %edi
	0x21, 0xd0, //0x0000343b andl         %edx, %eax
	0x89, 0xc2, //0x0000343d movl         %eax, %edx
	//0x0000343f LBB0_627
	0x8d, 0x46, 0xff, //0x0000343f leal         $-1(%rsi), %eax
	0x21, 0xf0, //0x00003442 andl         %esi, %eax
	0x0f, 0x85, 0x12, 0x0a, 0x00, 0x00, //0x00003444 jne          LBB0_783
	0x8d, 0x47, 0xff, //0x0000344a leal         $-1(%rdi), %eax
	0x21, 0xf8, //0x0000344d andl         %edi, %eax
	0x0f, 0x85, 0x07, 0x0a, 0x00, 0x00, //0x0000344f jne          LBB0_783
	0x8d, 0x42, 0xff, //0x00003455 leal         $-1(%rdx), %eax
	0x21, 0xd0, //0x00003458 andl         %edx, %eax
	0x0f, 0x85, 0xfc, 0x09, 0x00, 0x00, //0x0000345a jne          LBB0_783
	0x85, 0xf6, //0x00003460 testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00003462 je           LBB0_633
	0x0f, 0xbc, 0xc6, //0x00003468 bsfl         %esi, %eax
	0x49, 0x83, 0xfc, 0xff, //0x0000346b cmpq         $-1, %r12
	0x0f, 0x85, 0x28, 0x0a, 0x00, 0x00, //0x0000346f jne          LBB0_788
	0x4c, 0x01, 0xd0, //0x00003475 addq         %r10, %rax
	0x49, 0x89, 0xc4, //0x00003478 movq         %rax, %r12
	//0x0000347b LBB0_633
	0x85, 0xff, //0x0000347b testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x0000347d je           LBB0_636
	0x0f, 0xbc, 0xc7, //0x00003483 bsfl         %edi, %eax
	0x49, 0x83, 0xff, 0xff, //0x00003486 cmpq         $-1, %r15
	0x0f, 0x85, 0x0d, 0x0a, 0x00, 0x00, //0x0000348a jne          LBB0_788
	0x4c, 0x01, 0xd0, //0x00003490 addq         %r10, %rax
	0x49, 0x89, 0xc7, //0x00003493 movq         %rax, %r15
	//0x00003496 LBB0_636
	0x85, 0xd2, //0x00003496 testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00003498 je           LBB0_639
	0x0f, 0xbc, 0xc2, //0x0000349e bsfl         %edx, %eax
	0x49, 0x83, 0xfd, 0xff, //0x000034a1 cmpq         $-1, %r13
	0x0f, 0x85, 0xf2, 0x09, 0x00, 0x00, //0x000034a5 jne          LBB0_788
	0x4c, 0x01, 0xd0, //0x000034ab addq         %r10, %rax
	0x49, 0x89, 0xc5, //0x000034ae movq         %rax, %r13
	//0x000034b1 LBB0_639
	0x83, 0xf9, 0x10, //0x000034b1 cmpl         $16, %ecx
	0x0f, 0x85, 0xea, 0x02, 0x00, 0x00, //0x000034b4 jne          LBB0_679
	0x48, 0x83, 0xc3, 0xf0, //0x000034ba addq         $-16, %rbx
	0x49, 0x83, 0xc2, 0x10, //0x000034be addq         $16, %r10
	0x48, 0x83, 0xfb, 0x0f, //0x000034c2 cmpq         $15, %rbx
	0x0f, 0x87, 0xf4, 0xfe, 0xff, 0xff, //0x000034c6 ja           LBB0_625
	0x4b, 0x8d, 0x0c, 0x10, //0x000034cc leaq         (%r8,%r10), %rcx
	0x4d, 0x39, 0xd1, //0x000034d0 cmpq         %r10, %r9
	0x49, 0x89, 0xca, //0x000034d3 movq         %rcx, %r10
	0x48, 0x8d, 0x3d, 0x53, 0x25, 0x00, 0x00, //0x000034d6 leaq         $9555(%rip), %rdi  /* LJTI0_5+0(%rip) */
	0x0f, 0x84, 0xd4, 0x02, 0x00, 0x00, //0x000034dd je           LBB0_681
	//0x000034e3 LBB0_642
	0x4c, 0x8d, 0x14, 0x19, //0x000034e3 leaq         (%rcx,%rbx), %r10
	0x49, 0x89, 0xc9, //0x000034e7 movq         %rcx, %r9
	0x4d, 0x29, 0xc1, //0x000034ea subq         %r8, %r9
	0x31, 0xd2, //0x000034ed xorl         %edx, %edx
	0xe9, 0x29, 0x00, 0x00, 0x00, //0x000034ef jmp          LBB0_646
	//0x000034f4 LBB0_643
	0x49, 0x83, 0xfc, 0xff, //0x000034f4 cmpq         $-1, %r12
	0x0f, 0x85, 0x89, 0x09, 0x00, 0x00, //0x000034f8 jne          LBB0_787
	0x4d, 0x8d, 0x24, 0x11, //0x000034fe leaq         (%r9,%rdx), %r12
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003502 .p2align 4, 0x90
	//0x00003510 LBB0_645
	0x48, 0x83, 0xc2, 0x01, //0x00003510 addq         $1, %rdx
	0x48, 0x39, 0xd3, //0x00003514 cmpq         %rdx, %rbx
	0x0f, 0x84, 0x9a, 0x02, 0x00, 0x00, //0x00003517 je           LBB0_681
	//0x0000351d LBB0_646
	0x0f, 0xbe, 0x04, 0x11, //0x0000351d movsbl       (%rcx,%rdx), %eax
	0x8d, 0x70, 0xd0, //0x00003521 leal         $-48(%rax), %esi
	0x83, 0xfe, 0x0a, //0x00003524 cmpl         $10, %esi
	0x0f, 0x82, 0xe3, 0xff, 0xff, 0xff, //0x00003527 jb           LBB0_645
	0x8d, 0x70, 0xd5, //0x0000352d leal         $-43(%rax), %esi
	0x83, 0xfe, 0x1a, //0x00003530 cmpl         $26, %esi
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x00003533 ja           LBB0_651
	0x48, 0x63, 0x04, 0xb7, //0x00003539 movslq       (%rdi,%rsi,4), %rax
	0x48, 0x01, 0xf8, //0x0000353d addq         %rdi, %rax
	0xff, 0xe0, //0x00003540 jmpq         *%rax
	//0x00003542 LBB0_649
	0x49, 0x83, 0xfd, 0xff, //0x00003542 cmpq         $-1, %r13
	0x0f, 0x85, 0x3b, 0x09, 0x00, 0x00, //0x00003546 jne          LBB0_787
	0x4d, 0x8d, 0x2c, 0x11, //0x0000354c leaq         (%r9,%rdx), %r13
	0xe9, 0xbb, 0xff, 0xff, 0xff, //0x00003550 jmp          LBB0_645
	//0x00003555 LBB0_651
	0x83, 0xf8, 0x65, //0x00003555 cmpl         $101, %eax
	0x0f, 0x85, 0x53, 0x02, 0x00, 0x00, //0x00003558 jne          LBB0_680
	//0x0000355e LBB0_652
	0x49, 0x83, 0xff, 0xff, //0x0000355e cmpq         $-1, %r15
	0x0f, 0x85, 0x1f, 0x09, 0x00, 0x00, //0x00003562 jne          LBB0_787
	0x4d, 0x8d, 0x3c, 0x11, //0x00003568 leaq         (%r9,%rdx), %r15
	0xe9, 0x9f, 0xff, 0xff, 0xff, //0x0000356c jmp          LBB0_645
	//0x00003571 LBB0_616
	0x83, 0xfe, 0x22, //0x00003571 cmpl         $34, %esi
	0x0f, 0x84, 0x42, 0x00, 0x00, 0x00, //0x00003574 je           LBB0_658
	//0x0000357a LBB0_588
	0x83, 0xfe, 0x7d, //0x0000357a cmpl         $125, %esi
	0x0f, 0x85, 0x59, 0x10, 0x00, 0x00, //0x0000357d jne          LBB0_865
	//0x00003583 LBB0_585
	0x48, 0x89, 0x11, //0x00003583 movq         %rdx, (%rcx)
	0x48, 0x89, 0xd3, //0x00003586 movq         %rdx, %rbx
	0x4d, 0x89, 0xfc, //0x00003589 movq         %r15, %r12
	0x48, 0x85, 0xd2, //0x0000358c testq        %rdx, %rdx
	0x0f, 0x85, 0xf2, 0xf9, 0xff, 0xff, //0x0000358f jne          LBB0_554
	0xe9, 0x49, 0x10, 0x00, 0x00, //0x00003595 jmp          LBB0_866
	//0x0000359a LBB0_654
	0x48, 0x81, 0xfb, 0xff, 0x0f, 0x00, 0x00, //0x0000359a cmpq         $4095, %rbx
	0x0f, 0x8f, 0x81, 0x11, 0x00, 0x00, //0x000035a1 jg           LBB0_968
	0x48, 0x8d, 0x43, 0x01, //0x000035a7 leaq         $1(%rbx), %rax
	0x48, 0x89, 0x01, //0x000035ab movq         %rax, (%rcx)
	0x48, 0xc7, 0x44, 0xd9, 0x08, 0x00, 0x00, 0x00, 0x00, //0x000035ae movq         $0, $8(%rcx,%rbx,8)
	0xe9, 0xb9, 0xf9, 0xff, 0xff, //0x000035b7 jmp          LBB0_552
	//0x000035bc LBB0_658
	0x4c, 0x89, 0x7d, 0xc0, //0x000035bc movq         %r15, $-64(%rbp)
	0x48, 0xc7, 0x04, 0xd9, 0x02, 0x00, 0x00, 0x00, //0x000035c0 movq         $2, (%rcx,%rbx,8)
	0x48, 0x8b, 0x47, 0x08, //0x000035c8 movq         $8(%rdi), %rax
	0x49, 0x89, 0xc7, //0x000035cc movq         %rax, %r15
	0x4d, 0x29, 0xd7, //0x000035cf subq         %r10, %r15
	0x0f, 0x84, 0x4d, 0x1d, 0x00, 0x00, //0x000035d2 je           LBB0_984
	0x48, 0x89, 0x45, 0x98, //0x000035d8 movq         %rax, $-104(%rbp)
	0x49, 0x83, 0xff, 0x40, //0x000035dc cmpq         $64, %r15
	0x0f, 0x82, 0xcc, 0x0a, 0x00, 0x00, //0x000035e0 jb           LBB0_802
	0x48, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x000035e6 movq         $-2, %rdi
	0x4c, 0x29, 0xdf, //0x000035ed subq         %r11, %rdi
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x000035f0 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc9, //0x000035f8 xorl         %r9d, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000035fb .p2align 4, 0x90
	//0x00003600 LBB0_661
	0xf3, 0x43, 0x0f, 0x6f, 0x1c, 0x16, //0x00003600 movdqu       (%r14,%r10), %xmm3
	0xf3, 0x43, 0x0f, 0x6f, 0x64, 0x16, 0x10, //0x00003606 movdqu       $16(%r14,%r10), %xmm4
	0xf3, 0x43, 0x0f, 0x6f, 0x6c, 0x16, 0x20, //0x0000360d movdqu       $32(%r14,%r10), %xmm5
	0xf3, 0x43, 0x0f, 0x6f, 0x74, 0x16, 0x30, //0x00003614 movdqu       $48(%r14,%r10), %xmm6
	0x66, 0x0f, 0x6f, 0xfb, //0x0000361b movdqa       %xmm3, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x0000361f pcmpeqb      %xmm0, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xef, //0x00003623 pmovmskb     %xmm7, %r13d
	0x66, 0x0f, 0x6f, 0xfc, //0x00003628 movdqa       %xmm4, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x0000362c pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xcf, //0x00003630 pmovmskb     %xmm7, %ecx
	0x66, 0x0f, 0x6f, 0xfd, //0x00003634 movdqa       %xmm5, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00003638 pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xc7, //0x0000363c pmovmskb     %xmm7, %eax
	0x66, 0x0f, 0x6f, 0xfe, //0x00003640 movdqa       %xmm6, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x00003644 pcmpeqb      %xmm0, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xc7, //0x00003648 pmovmskb     %xmm7, %r8d
	0x66, 0x0f, 0x74, 0xd9, //0x0000364d pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00003651 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x74, 0xe1, //0x00003655 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x00003659 pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x74, 0xe9, //0x0000365d pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x00003661 pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x74, 0xf1, //0x00003665 pcmpeqb      %xmm1, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xe6, //0x00003669 pmovmskb     %xmm6, %r12d
	0x49, 0xc1, 0xe0, 0x30, //0x0000366e shlq         $48, %r8
	0x48, 0xc1, 0xe0, 0x20, //0x00003672 shlq         $32, %rax
	0x4c, 0x09, 0xc0, //0x00003676 orq          %r8, %rax
	0x48, 0xc1, 0xe1, 0x10, //0x00003679 shlq         $16, %rcx
	0x48, 0x09, 0xc1, //0x0000367d orq          %rax, %rcx
	0x49, 0x09, 0xcd, //0x00003680 orq          %rcx, %r13
	0x49, 0xc1, 0xe4, 0x30, //0x00003683 shlq         $48, %r12
	0x48, 0xc1, 0xe2, 0x20, //0x00003687 shlq         $32, %rdx
	0x4c, 0x09, 0xe2, //0x0000368b orq          %r12, %rdx
	0x48, 0xc1, 0xe3, 0x10, //0x0000368e shlq         $16, %rbx
	0x48, 0x09, 0xd3, //0x00003692 orq          %rdx, %rbx
	0x48, 0x09, 0xde, //0x00003695 orq          %rbx, %rsi
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00003698 jne          LBB0_670
	0x4d, 0x85, 0xc9, //0x0000369e testq        %r9, %r9
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x000036a1 jne          LBB0_672
	0x45, 0x31, 0xc9, //0x000036a7 xorl         %r9d, %r9d
	0x4d, 0x85, 0xed, //0x000036aa testq        %r13, %r13
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x000036ad jne          LBB0_673
	//0x000036b3 LBB0_664
	0x49, 0x83, 0xc7, 0xc0, //0x000036b3 addq         $-64, %r15
	0x48, 0x83, 0xc7, 0xc0, //0x000036b7 addq         $-64, %rdi
	0x49, 0x83, 0xc2, 0x40, //0x000036bb addq         $64, %r10
	0x49, 0x83, 0xff, 0x3f, //0x000036bf cmpq         $63, %r15
	0x0f, 0x87, 0x37, 0xff, 0xff, 0xff, //0x000036c3 ja           LBB0_661
	0xe9, 0x18, 0x08, 0x00, 0x00, //0x000036c9 jmp          LBB0_665
	//0x000036ce LBB0_670
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x000036ce cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x000036d3 jne          LBB0_672
	0x48, 0x0f, 0xbc, 0xc6, //0x000036d9 bsfq         %rsi, %rax
	0x4c, 0x01, 0xd0, //0x000036dd addq         %r10, %rax
	0x48, 0x89, 0x45, 0xc8, //0x000036e0 movq         %rax, $-56(%rbp)
	//0x000036e4 LBB0_672
	0x4c, 0x89, 0xc8, //0x000036e4 movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x000036e7 notq         %rax
	0x48, 0x21, 0xf0, //0x000036ea andq         %rsi, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x000036ed leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xc9, //0x000036f1 orq          %r9, %rcx
	0x48, 0x89, 0xca, //0x000036f4 movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x000036f7 notq         %rdx
	0x48, 0x21, 0xf2, //0x000036fa andq         %rsi, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000036fd movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x00003707 andq         %rsi, %rdx
	0x45, 0x31, 0xc9, //0x0000370a xorl         %r9d, %r9d
	0x48, 0x01, 0xc2, //0x0000370d addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc1, //0x00003710 setb         %r9b
	0x48, 0x01, 0xd2, //0x00003714 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00003717 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00003721 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x00003724 andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00003727 notq         %rdx
	0x49, 0x21, 0xd5, //0x0000372a andq         %rdx, %r13
	0x4d, 0x85, 0xed, //0x0000372d testq        %r13, %r13
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00003730 je           LBB0_664
	//0x00003736 LBB0_673
	0x4d, 0x0f, 0xbc, 0xd5, //0x00003736 bsfq         %r13, %r10
	0x49, 0x29, 0xfa, //0x0000373a subq         %rdi, %r10
	0x48, 0x8b, 0x7d, 0xb8, //0x0000373d movq         $-72(%rbp), %rdi
	0x48, 0x8b, 0x4d, 0x90, //0x00003741 movq         $-112(%rbp), %rcx
	0x4c, 0x8b, 0x7d, 0xc0, //0x00003745 movq         $-64(%rbp), %r15
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003749 movabsq      $4294977024, %r13
	0x4c, 0x8d, 0x0d, 0x62, 0x20, 0x00, 0x00, //0x00003753 leaq         $8290(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x4d, 0x85, 0xd2, //0x0000375a testq        %r10, %r10
	0x0f, 0x88, 0xd1, 0x0f, 0x00, 0x00, //0x0000375d js           LBB0_608
	//0x00003763 LBB0_676
	0x48, 0x8b, 0x45, 0xd0, //0x00003763 movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x10, //0x00003767 movq         %r10, (%rax)
	0x4d, 0x89, 0xdc, //0x0000376a movq         %r11, %r12
	0x48, 0xb8, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x0000376d movabsq      $9223372036854775806, %rax
	0x49, 0x39, 0xc3, //0x00003777 cmpq         %rax, %r11
	0x0f, 0x87, 0x63, 0x0e, 0x00, 0x00, //0x0000377a ja           LBB0_866
	0x48, 0x8b, 0x01, //0x00003780 movq         (%rcx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00003783 cmpq         $4095, %rax
	0x0f, 0x8f, 0x99, 0x0f, 0x00, 0x00, //0x00003789 jg           LBB0_968
	0x48, 0x8d, 0x50, 0x01, //0x0000378f leaq         $1(%rax), %rdx
	0x48, 0x89, 0x11, //0x00003793 movq         %rdx, (%rcx)
	0x48, 0xc7, 0x44, 0xc1, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00003796 movq         $4, $8(%rcx,%rax,8)
	0xe9, 0xd1, 0xf7, 0xff, 0xff, //0x0000379f jmp          LBB0_552
	//0x000037a4 LBB0_679
	0x89, 0xc8, //0x000037a4 movl         %ecx, %eax
	0x4c, 0x01, 0xc0, //0x000037a6 addq         %r8, %rax
	0x49, 0x01, 0xc2, //0x000037a9 addq         %rax, %r10
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x000037ac jmp          LBB0_681
	//0x000037b1 LBB0_680
	0x48, 0x01, 0xd1, //0x000037b1 addq         %rdx, %rcx
	0x49, 0x89, 0xca, //0x000037b4 movq         %rcx, %r10
	//0x000037b7 LBB0_681
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000037b7 movq         $-1, %rax
	0x4d, 0x85, 0xe4, //0x000037be testq        %r12, %r12
	0x0f, 0x84, 0xb1, 0x0f, 0x00, 0x00, //0x000037c1 je           LBB0_888
	0x4d, 0x85, 0xed, //0x000037c7 testq        %r13, %r13
	0x0f, 0x84, 0xa8, 0x0f, 0x00, 0x00, //0x000037ca je           LBB0_888
	0x4d, 0x85, 0xff, //0x000037d0 testq        %r15, %r15
	0x0f, 0x84, 0x9f, 0x0f, 0x00, 0x00, //0x000037d3 je           LBB0_888
	0x4d, 0x29, 0xc2, //0x000037d9 subq         %r8, %r10
	0x49, 0x8d, 0x42, 0xff, //0x000037dc leaq         $-1(%r10), %rax
	0x49, 0x39, 0xc4, //0x000037e0 cmpq         %rax, %r12
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x000037e3 je           LBB0_690
	0x49, 0x39, 0xc5, //0x000037e9 cmpq         %rax, %r13
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x000037ec je           LBB0_690
	0x49, 0x39, 0xc7, //0x000037f2 cmpq         %rax, %r15
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x000037f5 je           LBB0_690
	0x4d, 0x85, 0xed, //0x000037fb testq        %r13, %r13
	0x0f, 0x8e, 0x20, 0x00, 0x00, 0x00, //0x000037fe jle          LBB0_691
	0x49, 0x8d, 0x45, 0xff, //0x00003804 leaq         $-1(%r13), %rax
	0x49, 0x39, 0xc7, //0x00003808 cmpq         %rax, %r15
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x0000380b je           LBB0_691
	0x49, 0xf7, 0xd5, //0x00003811 notq         %r13
	0x4d, 0x89, 0xea, //0x00003814 movq         %r13, %r10
	0xe9, 0x89, 0x06, 0x00, 0x00, //0x00003817 jmp          LBB0_790
	//0x0000381c LBB0_690
	0x49, 0xf7, 0xda, //0x0000381c negq         %r10
	0xe9, 0x81, 0x06, 0x00, 0x00, //0x0000381f jmp          LBB0_790
	//0x00003824 LBB0_691
	0x4c, 0x89, 0xe0, //0x00003824 movq         %r12, %rax
	0x4c, 0x09, 0xf8, //0x00003827 orq          %r15, %rax
	0x0f, 0x99, 0xc0, //0x0000382a setns        %al
	0x0f, 0x88, 0x95, 0x04, 0x00, 0x00, //0x0000382d js           LBB0_768
	0x4d, 0x39, 0xfc, //0x00003833 cmpq         %r15, %r12
	0x0f, 0x8c, 0x8c, 0x04, 0x00, 0x00, //0x00003836 jl           LBB0_768
	0x49, 0xf7, 0xd4, //0x0000383c notq         %r12
	0x4d, 0x89, 0xe2, //0x0000383f movq         %r12, %r10
	0xe9, 0x5e, 0x06, 0x00, 0x00, //0x00003842 jmp          LBB0_790
	//0x00003847 LBB0_694
	0x4c, 0x89, 0x7d, 0xc0, //0x00003847 movq         %r15, $-64(%rbp)
	0x48, 0x8b, 0x47, 0x08, //0x0000384b movq         $8(%rdi), %rax
	0x49, 0x89, 0xc7, //0x0000384f movq         %rax, %r15
	0x4d, 0x29, 0xd7, //0x00003852 subq         %r10, %r15
	0x0f, 0x84, 0xca, 0x1a, 0x00, 0x00, //0x00003855 je           LBB0_984
	0x48, 0x89, 0x45, 0x98, //0x0000385b movq         %rax, $-104(%rbp)
	0x49, 0x83, 0xff, 0x40, //0x0000385f cmpq         $64, %r15
	0x0f, 0x82, 0x70, 0x08, 0x00, 0x00, //0x00003863 jb           LBB0_804
	0x48, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x00003869 movq         $-2, %rdi
	0x4c, 0x29, 0xdf, //0x00003870 subq         %r11, %rdi
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00003873 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc9, //0x0000387b xorl         %r9d, %r9d
	0x90, 0x90, //0x0000387e .p2align 4, 0x90
	//0x00003880 LBB0_697
	0xf3, 0x43, 0x0f, 0x6f, 0x1c, 0x16, //0x00003880 movdqu       (%r14,%r10), %xmm3
	0xf3, 0x43, 0x0f, 0x6f, 0x64, 0x16, 0x10, //0x00003886 movdqu       $16(%r14,%r10), %xmm4
	0xf3, 0x43, 0x0f, 0x6f, 0x6c, 0x16, 0x20, //0x0000388d movdqu       $32(%r14,%r10), %xmm5
	0xf3, 0x43, 0x0f, 0x6f, 0x74, 0x16, 0x30, //0x00003894 movdqu       $48(%r14,%r10), %xmm6
	0x66, 0x0f, 0x6f, 0xfb, //0x0000389b movdqa       %xmm3, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x0000389f pcmpeqb      %xmm0, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xef, //0x000038a3 pmovmskb     %xmm7, %r13d
	0x66, 0x0f, 0x6f, 0xfc, //0x000038a8 movdqa       %xmm4, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x000038ac pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xcf, //0x000038b0 pmovmskb     %xmm7, %ecx
	0x66, 0x0f, 0x6f, 0xfd, //0x000038b4 movdqa       %xmm5, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x000038b8 pcmpeqb      %xmm0, %xmm7
	0x66, 0x0f, 0xd7, 0xd7, //0x000038bc pmovmskb     %xmm7, %edx
	0x66, 0x0f, 0x6f, 0xfe, //0x000038c0 movdqa       %xmm6, %xmm7
	0x66, 0x0f, 0x74, 0xf8, //0x000038c4 pcmpeqb      %xmm0, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xc7, //0x000038c8 pmovmskb     %xmm7, %r8d
	0x66, 0x0f, 0x74, 0xd9, //0x000038cd pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x000038d1 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x74, 0xe1, //0x000038d5 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x000038d9 pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x74, 0xe9, //0x000038dd pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xc5, //0x000038e1 pmovmskb     %xmm5, %eax
	0x66, 0x0f, 0x74, 0xf1, //0x000038e5 pcmpeqb      %xmm1, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xe6, //0x000038e9 pmovmskb     %xmm6, %r12d
	0x49, 0xc1, 0xe0, 0x30, //0x000038ee shlq         $48, %r8
	0x48, 0xc1, 0xe2, 0x20, //0x000038f2 shlq         $32, %rdx
	0x4c, 0x09, 0xc2, //0x000038f6 orq          %r8, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x000038f9 shlq         $16, %rcx
	0x48, 0x09, 0xd1, //0x000038fd orq          %rdx, %rcx
	0x49, 0x09, 0xcd, //0x00003900 orq          %rcx, %r13
	0x49, 0xc1, 0xe4, 0x30, //0x00003903 shlq         $48, %r12
	0x48, 0xc1, 0xe0, 0x20, //0x00003907 shlq         $32, %rax
	0x4c, 0x09, 0xe0, //0x0000390b orq          %r12, %rax
	0x48, 0xc1, 0xe3, 0x10, //0x0000390e shlq         $16, %rbx
	0x48, 0x09, 0xc3, //0x00003912 orq          %rax, %rbx
	0x48, 0x09, 0xde, //0x00003915 orq          %rbx, %rsi
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00003918 jne          LBB0_706
	0x4d, 0x85, 0xc9, //0x0000391e testq        %r9, %r9
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00003921 jne          LBB0_708
	0x45, 0x31, 0xc9, //0x00003927 xorl         %r9d, %r9d
	0x4d, 0x85, 0xed, //0x0000392a testq        %r13, %r13
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x0000392d jne          LBB0_709
	//0x00003933 LBB0_700
	0x49, 0x83, 0xc7, 0xc0, //0x00003933 addq         $-64, %r15
	0x48, 0x83, 0xc7, 0xc0, //0x00003937 addq         $-64, %rdi
	0x49, 0x83, 0xc2, 0x40, //0x0000393b addq         $64, %r10
	0x49, 0x83, 0xff, 0x3f, //0x0000393f cmpq         $63, %r15
	0x0f, 0x87, 0x37, 0xff, 0xff, 0xff, //0x00003943 ja           LBB0_697
	0xe9, 0x77, 0x06, 0x00, 0x00, //0x00003949 jmp          LBB0_701
	//0x0000394e LBB0_706
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x0000394e cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00003953 jne          LBB0_708
	0x48, 0x0f, 0xbc, 0xc6, //0x00003959 bsfq         %rsi, %rax
	0x4c, 0x01, 0xd0, //0x0000395d addq         %r10, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00003960 movq         %rax, $-56(%rbp)
	//0x00003964 LBB0_708
	0x4c, 0x89, 0xc8, //0x00003964 movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00003967 notq         %rax
	0x48, 0x21, 0xf0, //0x0000396a andq         %rsi, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x0000396d leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xc9, //0x00003971 orq          %r9, %rcx
	0x48, 0x89, 0xca, //0x00003974 movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00003977 notq         %rdx
	0x48, 0x21, 0xf2, //0x0000397a andq         %rsi, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000397d movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x00003987 andq         %rsi, %rdx
	0x45, 0x31, 0xc9, //0x0000398a xorl         %r9d, %r9d
	0x48, 0x01, 0xc2, //0x0000398d addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc1, //0x00003990 setb         %r9b
	0x48, 0x01, 0xd2, //0x00003994 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00003997 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x000039a1 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x000039a4 andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x000039a7 notq         %rdx
	0x49, 0x21, 0xd5, //0x000039aa andq         %rdx, %r13
	0x4d, 0x85, 0xed, //0x000039ad testq        %r13, %r13
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x000039b0 je           LBB0_700
	//0x000039b6 LBB0_709
	0x4d, 0x0f, 0xbc, 0xd5, //0x000039b6 bsfq         %r13, %r10
	0x49, 0x29, 0xfa, //0x000039ba subq         %rdi, %r10
	//0x000039bd LBB0_710
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000039bd movabsq      $4294977024, %r13
	//0x000039c7 LBB0_711
	0x4d, 0x85, 0xd2, //0x000039c7 testq        %r10, %r10
	0x48, 0x8b, 0x7d, 0xb8, //0x000039ca movq         $-72(%rbp), %rdi
	0x48, 0x8b, 0x4d, 0x90, //0x000039ce movq         $-112(%rbp), %rcx
	0x4c, 0x8b, 0x7d, 0xc0, //0x000039d2 movq         $-64(%rbp), %r15
	0x4c, 0x8d, 0x0d, 0xdf, 0x1d, 0x00, 0x00, //0x000039d6 leaq         $7647(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x0f, 0x89, 0x75, 0xf5, 0xff, 0xff, //0x000039dd jns          LBB0_551
	0xe9, 0x4c, 0x0d, 0x00, 0x00, //0x000039e3 jmp          LBB0_608
	//0x000039e8 LBB0_712
	0x4c, 0x8b, 0x47, 0x08, //0x000039e8 movq         $8(%rdi), %r8
	0x4d, 0x29, 0xd0, //0x000039ec subq         %r10, %r8
	0x0f, 0x84, 0x1c, 0x18, 0x00, 0x00, //0x000039ef je           LBB0_966
	0x4c, 0x89, 0x7d, 0xc0, //0x000039f5 movq         %r15, $-64(%rbp)
	0x4b, 0x8d, 0x1c, 0x16, //0x000039f9 leaq         (%r14,%r10), %rbx
	0x80, 0x3b, 0x30, //0x000039fd cmpb         $48, (%rbx)
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x00003a00 jne          LBB0_717
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x00003a06 movl         $1, %r15d
	0x49, 0x83, 0xf8, 0x01, //0x00003a0c cmpq         $1, %r8
	0x0f, 0x84, 0x65, 0x03, 0x00, 0x00, //0x00003a10 je           LBB0_782
	0x8a, 0x43, 0x01, //0x00003a16 movb         $1(%rbx), %al
	0x04, 0xd2, //0x00003a19 addb         $-46, %al
	0x3c, 0x37, //0x00003a1b cmpb         $55, %al
	0x0f, 0x87, 0x58, 0x03, 0x00, 0x00, //0x00003a1d ja           LBB0_782
	0x0f, 0xb6, 0xc0, //0x00003a23 movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00003a26 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00003a30 btq          %rax, %rcx
	0x0f, 0x83, 0x41, 0x03, 0x00, 0x00, //0x00003a34 jae          LBB0_782
	//0x00003a3a LBB0_717
	0x49, 0x83, 0xf8, 0x10, //0x00003a3a cmpq         $16, %r8
	0x0f, 0x82, 0xb2, 0x06, 0x00, 0x00, //0x00003a3e jb           LBB0_805
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00003a44 movq         $-1, %r13
	0x45, 0x31, 0xff, //0x00003a4b xorl         %r15d, %r15d
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003a4e movq         $-1, %r9
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00003a55 movq         $-1, %r12
	0x4c, 0x89, 0x45, 0xc8, //0x00003a5c movq         %r8, $-56(%rbp)
	//0x00003a60 LBB0_719
	0xf3, 0x42, 0x0f, 0x6f, 0x1c, 0x3b, //0x00003a60 movdqu       (%rbx,%r15), %xmm3
	0x66, 0x0f, 0x6f, 0xe3, //0x00003a66 movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe0, //0x00003a6a pcmpeqb      %xmm8, %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x00003a6f movdqa       %xmm3, %xmm5
	0x66, 0x41, 0x0f, 0x74, 0xe9, //0x00003a73 pcmpeqb      %xmm9, %xmm5
	0x66, 0x0f, 0xeb, 0xec, //0x00003a78 por          %xmm4, %xmm5
	0x66, 0x0f, 0x6f, 0xe3, //0x00003a7c movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0xfc, 0xe2, //0x00003a80 paddb        %xmm10, %xmm4
	0x66, 0x0f, 0x6f, 0xf4, //0x00003a85 movdqa       %xmm4, %xmm6
	0x66, 0x41, 0x0f, 0xda, 0xf3, //0x00003a89 pminub       %xmm11, %xmm6
	0x66, 0x0f, 0x74, 0xf4, //0x00003a8e pcmpeqb      %xmm4, %xmm6
	0x66, 0x0f, 0x6f, 0xe3, //0x00003a92 movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0xdb, 0xe4, //0x00003a96 pand         %xmm12, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x00003a9b pcmpeqb      %xmm13, %xmm3
	0x66, 0x0f, 0x74, 0xe2, //0x00003aa0 pcmpeqb      %xmm2, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x00003aa4 pmovmskb     %xmm4, %edi
	0x66, 0x0f, 0xeb, 0xe3, //0x00003aa8 por          %xmm3, %xmm4
	0x66, 0x0f, 0xeb, 0xe5, //0x00003aac por          %xmm5, %xmm4
	0x66, 0x0f, 0xeb, 0xe6, //0x00003ab0 por          %xmm6, %xmm4
	0x66, 0x0f, 0xd7, 0xf3, //0x00003ab4 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0xd7, 0xd5, //0x00003ab8 pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0xd7, 0xc4, //0x00003abc pmovmskb     %xmm4, %eax
	0xf7, 0xd0, //0x00003ac0 notl         %eax
	0x0f, 0xbc, 0xc8, //0x00003ac2 bsfl         %eax, %ecx
	0x83, 0xf9, 0x10, //0x00003ac5 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00003ac8 je           LBB0_721
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00003ace movl         $-1, %eax
	0xd3, 0xe0, //0x00003ad3 shll         %cl, %eax
	0xf7, 0xd0, //0x00003ad5 notl         %eax
	0x21, 0xc6, //0x00003ad7 andl         %eax, %esi
	0x21, 0xc7, //0x00003ad9 andl         %eax, %edi
	0x21, 0xd0, //0x00003adb andl         %edx, %eax
	0x89, 0xc2, //0x00003add movl         %eax, %edx
	//0x00003adf LBB0_721
	0x8d, 0x46, 0xff, //0x00003adf leal         $-1(%rsi), %eax
	0x21, 0xf0, //0x00003ae2 andl         %esi, %eax
	0x0f, 0x85, 0xd3, 0x04, 0x00, 0x00, //0x00003ae4 jne          LBB0_795
	0x8d, 0x47, 0xff, //0x00003aea leal         $-1(%rdi), %eax
	0x21, 0xf8, //0x00003aed andl         %edi, %eax
	0x0f, 0x85, 0xc8, 0x04, 0x00, 0x00, //0x00003aef jne          LBB0_795
	0x8d, 0x42, 0xff, //0x00003af5 leal         $-1(%rdx), %eax
	0x21, 0xd0, //0x00003af8 andl         %edx, %eax
	0x0f, 0x85, 0xbd, 0x04, 0x00, 0x00, //0x00003afa jne          LBB0_795
	0x85, 0xf6, //0x00003b00 testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00003b02 je           LBB0_727
	0x0f, 0xbc, 0xc6, //0x00003b08 bsfl         %esi, %eax
	0x49, 0x83, 0xfc, 0xff, //0x00003b0b cmpq         $-1, %r12
	0x0f, 0x85, 0x42, 0x05, 0x00, 0x00, //0x00003b0f jne          LBB0_797
	0x4c, 0x01, 0xf8, //0x00003b15 addq         %r15, %rax
	0x49, 0x89, 0xc4, //0x00003b18 movq         %rax, %r12
	//0x00003b1b LBB0_727
	0x85, 0xff, //0x00003b1b testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00003b1d je           LBB0_730
	0x0f, 0xbc, 0xc7, //0x00003b23 bsfl         %edi, %eax
	0x49, 0x83, 0xf9, 0xff, //0x00003b26 cmpq         $-1, %r9
	0x0f, 0x85, 0x27, 0x05, 0x00, 0x00, //0x00003b2a jne          LBB0_797
	0x4c, 0x01, 0xf8, //0x00003b30 addq         %r15, %rax
	0x49, 0x89, 0xc1, //0x00003b33 movq         %rax, %r9
	//0x00003b36 LBB0_730
	0x85, 0xd2, //0x00003b36 testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00003b38 je           LBB0_733
	0x0f, 0xbc, 0xc2, //0x00003b3e bsfl         %edx, %eax
	0x49, 0x83, 0xfd, 0xff, //0x00003b41 cmpq         $-1, %r13
	0x0f, 0x85, 0x0c, 0x05, 0x00, 0x00, //0x00003b45 jne          LBB0_797
	0x4c, 0x01, 0xf8, //0x00003b4b addq         %r15, %rax
	0x49, 0x89, 0xc5, //0x00003b4e movq         %rax, %r13
	//0x00003b51 LBB0_733
	0x83, 0xf9, 0x10, //0x00003b51 cmpl         $16, %ecx
	0x0f, 0x85, 0x87, 0x01, 0x00, 0x00, //0x00003b54 jne          LBB0_769
	0x49, 0x83, 0xc0, 0xf0, //0x00003b5a addq         $-16, %r8
	0x49, 0x83, 0xc7, 0x10, //0x00003b5e addq         $16, %r15
	0x49, 0x83, 0xf8, 0x0f, //0x00003b62 cmpq         $15, %r8
	0x0f, 0x87, 0xf4, 0xfe, 0xff, 0xff, //0x00003b66 ja           LBB0_719
	0x4a, 0x8d, 0x14, 0x3b, //0x00003b6c leaq         (%rbx,%r15), %rdx
	0x48, 0x89, 0xd6, //0x00003b70 movq         %rdx, %rsi
	0x4c, 0x39, 0x7d, 0xc8, //0x00003b73 cmpq         %r15, $-56(%rbp)
	0x0f, 0x84, 0x8a, 0x01, 0x00, 0x00, //0x00003b77 je           LBB0_771
	//0x00003b7d LBB0_736
	0x49, 0x89, 0xdf, //0x00003b7d movq         %rbx, %r15
	0x4a, 0x8d, 0x04, 0x02, //0x00003b80 leaq         (%rdx,%r8), %rax
	0x48, 0x89, 0x45, 0xc8, //0x00003b84 movq         %rax, $-56(%rbp)
	0x48, 0x8b, 0x45, 0xa0, //0x00003b88 movq         $-96(%rbp), %rax
	0x48, 0x8d, 0x0c, 0x10, //0x00003b8c leaq         (%rax,%rdx), %rcx
	0x4c, 0x29, 0xd9, //0x00003b90 subq         %r11, %rcx
	0x31, 0xff, //0x00003b93 xorl         %edi, %edi
	0x48, 0x8d, 0x1d, 0x28, 0x1e, 0x00, 0x00, //0x00003b95 leaq         $7720(%rip), %rbx  /* LJTI0_4+0(%rip) */
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00003b9c jmp          LBB0_740
	//0x00003ba1 LBB0_737
	0x49, 0x83, 0xfd, 0xff, //0x00003ba1 cmpq         $-1, %r13
	0x0f, 0x85, 0x9d, 0x04, 0x00, 0x00, //0x00003ba5 jne          LBB0_796
	0x4c, 0x8d, 0x2c, 0x39, //0x00003bab leaq         (%rcx,%rdi), %r13
	0x90, //0x00003baf .p2align 4, 0x90
	//0x00003bb0 LBB0_739
	0x48, 0x83, 0xc7, 0x01, //0x00003bb0 addq         $1, %rdi
	0x49, 0x39, 0xf8, //0x00003bb4 cmpq         %rdi, %r8
	0x0f, 0x84, 0xe4, 0x03, 0x00, 0x00, //0x00003bb7 je           LBB0_794
	//0x00003bbd LBB0_740
	0x0f, 0xbe, 0x04, 0x3a, //0x00003bbd movsbl       (%rdx,%rdi), %eax
	0x8d, 0x70, 0xd0, //0x00003bc1 leal         $-48(%rax), %esi
	0x83, 0xfe, 0x0a, //0x00003bc4 cmpl         $10, %esi
	0x0f, 0x82, 0xe3, 0xff, 0xff, 0xff, //0x00003bc7 jb           LBB0_739
	0x8d, 0x70, 0xd5, //0x00003bcd leal         $-43(%rax), %esi
	0x83, 0xfe, 0x1a, //0x00003bd0 cmpl         $26, %esi
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x00003bd3 ja           LBB0_745
	0x48, 0x63, 0x04, 0xb3, //0x00003bd9 movslq       (%rbx,%rsi,4), %rax
	0x48, 0x01, 0xd8, //0x00003bdd addq         %rbx, %rax
	0xff, 0xe0, //0x00003be0 jmpq         *%rax
	//0x00003be2 LBB0_743
	0x49, 0x83, 0xfc, 0xff, //0x00003be2 cmpq         $-1, %r12
	0x0f, 0x85, 0x5c, 0x04, 0x00, 0x00, //0x00003be6 jne          LBB0_796
	0x4c, 0x8d, 0x24, 0x39, //0x00003bec leaq         (%rcx,%rdi), %r12
	0xe9, 0xbb, 0xff, 0xff, 0xff, //0x00003bf0 jmp          LBB0_739
	//0x00003bf5 LBB0_745
	0x83, 0xf8, 0x65, //0x00003bf5 cmpl         $101, %eax
	0x0f, 0x85, 0x00, 0x01, 0x00, 0x00, //0x00003bf8 jne          LBB0_770
	//0x00003bfe LBB0_746
	0x49, 0x83, 0xf9, 0xff, //0x00003bfe cmpq         $-1, %r9
	0x0f, 0x85, 0x40, 0x04, 0x00, 0x00, //0x00003c02 jne          LBB0_796
	0x4c, 0x8d, 0x0c, 0x39, //0x00003c08 leaq         (%rcx,%rdi), %r9
	0xe9, 0x9f, 0xff, 0xff, 0xff, //0x00003c0c jmp          LBB0_739
	//0x00003c11 LBB0_748
	0x48, 0x8b, 0x01, //0x00003c11 movq         (%rcx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00003c14 cmpq         $4095, %rax
	0x0f, 0x8f, 0x08, 0x0b, 0x00, 0x00, //0x00003c1a jg           LBB0_968
	0x48, 0x8d, 0x50, 0x01, //0x00003c20 leaq         $1(%rax), %rdx
	0x48, 0x89, 0x11, //0x00003c24 movq         %rdx, (%rcx)
	0x48, 0xc7, 0x44, 0xc1, 0x08, 0x05, 0x00, 0x00, 0x00, //0x00003c27 movq         $5, $8(%rcx,%rax,8)
	0xe9, 0x40, 0xf3, 0xff, 0xff, //0x00003c30 jmp          LBB0_552
	//0x00003c35 LBB0_750
	0x48, 0x8b, 0x47, 0x08, //0x00003c35 movq         $8(%rdi), %rax
	0x48, 0x8d, 0x50, 0xfc, //0x00003c39 leaq         $-4(%rax), %rdx
	0x49, 0x39, 0xd3, //0x00003c3d cmpq         %rdx, %r11
	0x0f, 0x83, 0x5d, 0x17, 0x00, 0x00, //0x00003c40 jae          LBB0_987
	0x43, 0x8b, 0x04, 0x16, //0x00003c46 movl         (%r14,%r10), %eax
	0x3d, 0x61, 0x6c, 0x73, 0x65, //0x00003c4a cmpl         $1702063201, %eax
	0x0f, 0x85, 0xcb, 0x15, 0x00, 0x00, //0x00003c4f jne          LBB0_969
	0x4d, 0x8d, 0x53, 0x05, //0x00003c55 leaq         $5(%r11), %r10
	0xe9, 0xfa, 0xf2, 0xff, 0xff, //0x00003c59 jmp          LBB0_551
	//0x00003c5e LBB0_753
	0x48, 0x8b, 0x47, 0x08, //0x00003c5e movq         $8(%rdi), %rax
	0x48, 0x8d, 0x50, 0xfd, //0x00003c62 leaq         $-3(%rax), %rdx
	0x49, 0x39, 0xd3, //0x00003c66 cmpq         %rdx, %r11
	0x0f, 0x83, 0x34, 0x17, 0x00, 0x00, //0x00003c69 jae          LBB0_987
	0x41, 0x81, 0x38, 0x6e, 0x75, 0x6c, 0x6c, //0x00003c6f cmpl         $1819047278, (%r8)
	0x0f, 0x84, 0xd8, 0xf2, 0xff, 0xff, //0x00003c76 je           LBB0_550
	0xe9, 0xf4, 0x15, 0x00, 0x00, //0x00003c7c jmp          LBB0_755
	//0x00003c81 LBB0_760
	0x48, 0x8b, 0x47, 0x08, //0x00003c81 movq         $8(%rdi), %rax
	0x48, 0x8d, 0x50, 0xfd, //0x00003c85 leaq         $-3(%rax), %rdx
	0x49, 0x39, 0xd3, //0x00003c89 cmpq         %rdx, %r11
	0x0f, 0x83, 0x11, 0x17, 0x00, 0x00, //0x00003c8c jae          LBB0_987
	0x41, 0x81, 0x38, 0x74, 0x72, 0x75, 0x65, //0x00003c92 cmpl         $1702195828, (%r8)
	0x0f, 0x84, 0xb5, 0xf2, 0xff, 0xff, //0x00003c99 je           LBB0_550
	0xe9, 0x27, 0x16, 0x00, 0x00, //0x00003c9f jmp          LBB0_762
	//0x00003ca4 LBB0_766
	0x48, 0x8b, 0x01, //0x00003ca4 movq         (%rcx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00003ca7 cmpq         $4095, %rax
	0x0f, 0x8f, 0x75, 0x0a, 0x00, 0x00, //0x00003cad jg           LBB0_968
	0x48, 0x8d, 0x50, 0x01, //0x00003cb3 leaq         $1(%rax), %rdx
	0x48, 0x89, 0x11, //0x00003cb7 movq         %rdx, (%rcx)
	0x48, 0xc7, 0x44, 0xc1, 0x08, 0x06, 0x00, 0x00, 0x00, //0x00003cba movq         $6, $8(%rcx,%rax,8)
	0xe9, 0xad, 0xf2, 0xff, 0xff, //0x00003cc3 jmp          LBB0_552
	//0x00003cc8 LBB0_768
	0x49, 0x8d, 0x4f, 0xff, //0x00003cc8 leaq         $-1(%r15), %rcx
	0x49, 0x39, 0xcc, //0x00003ccc cmpq         %rcx, %r12
	0x49, 0xf7, 0xd7, //0x00003ccf notq         %r15
	0x4d, 0x0f, 0x45, 0xfa, //0x00003cd2 cmovneq      %r10, %r15
	0x84, 0xc0, //0x00003cd6 testb        %al, %al
	0x4d, 0x0f, 0x45, 0xd7, //0x00003cd8 cmovneq      %r15, %r10
	0xe9, 0xc4, 0x01, 0x00, 0x00, //0x00003cdc jmp          LBB0_790
	//0x00003ce1 LBB0_769
	0x89, 0xce, //0x00003ce1 movl         %ecx, %esi
	0x48, 0x01, 0xde, //0x00003ce3 addq         %rbx, %rsi
	0x4c, 0x01, 0xfe, //0x00003ce6 addq         %r15, %rsi
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00003ce9 movq         $-1, %r15
	0x4d, 0x85, 0xe4, //0x00003cf0 testq        %r12, %r12
	0x0f, 0x85, 0x1e, 0x00, 0x00, 0x00, //0x00003cf3 jne          LBB0_772
	0xe9, 0x1a, 0x15, 0x00, 0x00, //0x00003cf9 jmp          LBB0_967
	//0x00003cfe LBB0_770
	0x48, 0x01, 0xfa, //0x00003cfe addq         %rdi, %rdx
	0x48, 0x89, 0xd6, //0x00003d01 movq         %rdx, %rsi
	0x4c, 0x89, 0xfb, //0x00003d04 movq         %r15, %rbx
	//0x00003d07 LBB0_771
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00003d07 movq         $-1, %r15
	0x4d, 0x85, 0xe4, //0x00003d0e testq        %r12, %r12
	0x0f, 0x84, 0x01, 0x15, 0x00, 0x00, //0x00003d11 je           LBB0_967
	//0x00003d17 LBB0_772
	0x4d, 0x85, 0xed, //0x00003d17 testq        %r13, %r13
	0x0f, 0x84, 0xf8, 0x14, 0x00, 0x00, //0x00003d1a je           LBB0_967
	0x4d, 0x85, 0xc9, //0x00003d20 testq        %r9, %r9
	0x0f, 0x84, 0xef, 0x14, 0x00, 0x00, //0x00003d23 je           LBB0_967
	0x48, 0x29, 0xde, //0x00003d29 subq         %rbx, %rsi
	0x48, 0x8d, 0x46, 0xff, //0x00003d2c leaq         $-1(%rsi), %rax
	0x49, 0x39, 0xc4, //0x00003d30 cmpq         %rax, %r12
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00003d33 je           LBB0_780
	0x49, 0x39, 0xc5, //0x00003d39 cmpq         %rax, %r13
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x00003d3c je           LBB0_780
	0x49, 0x39, 0xc1, //0x00003d42 cmpq         %rax, %r9
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00003d45 je           LBB0_780
	0x4d, 0x85, 0xed, //0x00003d4b testq        %r13, %r13
	0x0f, 0x8e, 0x10, 0x01, 0x00, 0x00, //0x00003d4e jle          LBB0_784
	0x49, 0x8d, 0x45, 0xff, //0x00003d54 leaq         $-1(%r13), %rax
	0x49, 0x39, 0xc1, //0x00003d58 cmpq         %rax, %r9
	0x0f, 0x84, 0x03, 0x01, 0x00, 0x00, //0x00003d5b je           LBB0_784
	0x49, 0xf7, 0xd5, //0x00003d61 notq         %r13
	0x4d, 0x89, 0xef, //0x00003d64 movq         %r13, %r15
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00003d67 jmp          LBB0_781
	//0x00003d6c LBB0_780
	0x48, 0xf7, 0xde, //0x00003d6c negq         %rsi
	0x49, 0x89, 0xf7, //0x00003d6f movq         %rsi, %r15
	//0x00003d72 LBB0_781
	0x4d, 0x85, 0xff, //0x00003d72 testq        %r15, %r15
	0x0f, 0x88, 0x9d, 0x14, 0x00, 0x00, //0x00003d75 js           LBB0_967
	//0x00003d7b LBB0_782
	0x4d, 0x01, 0xfa, //0x00003d7b addq         %r15, %r10
	0x48, 0x8b, 0x45, 0xd0, //0x00003d7e movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x10, //0x00003d82 movq         %r10, (%rax)
	0x4d, 0x89, 0xdc, //0x00003d85 movq         %r11, %r12
	0x48, 0xb8, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00003d88 movabsq      $9223372036854775806, %rax
	0x49, 0x39, 0xc3, //0x00003d92 cmpq         %rax, %r11
	0x48, 0x8b, 0x7d, 0xb8, //0x00003d95 movq         $-72(%rbp), %rdi
	0x48, 0x8b, 0x4d, 0x90, //0x00003d99 movq         $-112(%rbp), %rcx
	0x4c, 0x8b, 0x7d, 0xc0, //0x00003d9d movq         $-64(%rbp), %r15
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003da1 movabsq      $4294977024, %r13
	0x4c, 0x8d, 0x0d, 0x0a, 0x1a, 0x00, 0x00, //0x00003dab leaq         $6666(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x0f, 0x86, 0xbd, 0xf1, 0xff, 0xff, //0x00003db2 jbe          LBB0_552
	0xe9, 0x26, 0x08, 0x00, 0x00, //0x00003db8 jmp          LBB0_866
	//0x00003dbd LBB0_597
	0x4d, 0x01, 0xf2, //0x00003dbd addq         %r14, %r10
	0x49, 0x83, 0xff, 0x20, //0x00003dc0 cmpq         $32, %r15
	0x0f, 0x82, 0xb2, 0x02, 0x00, 0x00, //0x00003dc4 jb           LBB0_800
	//0x00003dca LBB0_598
	0xf3, 0x41, 0x0f, 0x6f, 0x1a, //0x00003dca movdqu       (%r10), %xmm3
	0xf3, 0x41, 0x0f, 0x6f, 0x62, 0x10, //0x00003dcf movdqu       $16(%r10), %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x00003dd5 movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00003dd9 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x00003ddd pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x6f, 0xec, //0x00003de1 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00003de5 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xc5, //0x00003de9 pmovmskb     %xmm5, %eax
	0x66, 0x0f, 0x74, 0xd9, //0x00003ded pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00003df1 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x74, 0xe1, //0x00003df5 pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x00003df9 pmovmskb     %xmm4, %ecx
	0x48, 0xc1, 0xe0, 0x10, //0x00003dfd shlq         $16, %rax
	0x48, 0x09, 0xc2, //0x00003e01 orq          %rax, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x00003e04 shlq         $16, %rcx
	0x48, 0x09, 0xce, //0x00003e08 orq          %rcx, %rsi
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003e0b movabsq      $4294977024, %r13
	0x0f, 0x85, 0xf8, 0x02, 0x00, 0x00, //0x00003e15 jne          LBB0_806
	0x4d, 0x85, 0xc9, //0x00003e1b testq        %r9, %r9
	0x0f, 0x85, 0x0b, 0x03, 0x00, 0x00, //0x00003e1e jne          LBB0_808
	0x45, 0x31, 0xc9, //0x00003e24 xorl         %r9d, %r9d
	0x48, 0x85, 0xd2, //0x00003e27 testq        %rdx, %rdx
	0x0f, 0x84, 0x41, 0x03, 0x00, 0x00, //0x00003e2a je           LBB0_809
	//0x00003e30 LBB0_601
	0x48, 0x0f, 0xbc, 0xc2, //0x00003e30 bsfq         %rdx, %rax
	0x4c, 0x03, 0x55, 0xa8, //0x00003e34 addq         $-88(%rbp), %r10
	0x49, 0x01, 0xc2, //0x00003e38 addq         %rax, %r10
	0x48, 0x8b, 0x7d, 0xb8, //0x00003e3b movq         $-72(%rbp), %rdi
	0x48, 0x8b, 0x4d, 0x90, //0x00003e3f movq         $-112(%rbp), %rcx
	0x4c, 0x8b, 0x7d, 0xc0, //0x00003e43 movq         $-64(%rbp), %r15
	0x4c, 0x8d, 0x0d, 0x6e, 0x19, 0x00, 0x00, //0x00003e47 leaq         $6510(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x4d, 0x85, 0xd2, //0x00003e4e testq        %r10, %r10
	0x0f, 0x89, 0x01, 0xf1, 0xff, 0xff, //0x00003e51 jns          LBB0_551
	0xe9, 0xd8, 0x08, 0x00, 0x00, //0x00003e57 jmp          LBB0_608
	//0x00003e5c LBB0_783
	0x0f, 0xbc, 0xc0, //0x00003e5c bsfl         %eax, %eax
	0xe9, 0x3b, 0x00, 0x00, 0x00, //0x00003e5f jmp          LBB0_789
	//0x00003e64 LBB0_784
	0x4c, 0x89, 0xe0, //0x00003e64 movq         %r12, %rax
	0x4c, 0x09, 0xc8, //0x00003e67 orq          %r9, %rax
	0x0f, 0x99, 0xc0, //0x00003e6a setns        %al
	0x0f, 0x88, 0x12, 0x01, 0x00, 0x00, //0x00003e6d js           LBB0_793
	0x4d, 0x39, 0xcc, //0x00003e73 cmpq         %r9, %r12
	0x0f, 0x8c, 0x09, 0x01, 0x00, 0x00, //0x00003e76 jl           LBB0_793
	0x49, 0xf7, 0xd4, //0x00003e7c notq         %r12
	0x4d, 0x89, 0xe7, //0x00003e7f movq         %r12, %r15
	0xe9, 0xeb, 0xfe, 0xff, 0xff, //0x00003e82 jmp          LBB0_781
	//0x00003e87 LBB0_787
	0x48, 0x8b, 0x85, 0x48, 0xff, 0xff, 0xff, //0x00003e87 movq         $-184(%rbp), %rax
	0x4e, 0x8d, 0x14, 0x18, //0x00003e8e leaq         (%rax,%r11), %r10
	0x49, 0x29, 0xca, //0x00003e92 subq         %rcx, %r10
	0x49, 0x29, 0xd2, //0x00003e95 subq         %rdx, %r10
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00003e98 jmp          LBB0_790
	//0x00003e9d LBB0_788
	0x89, 0xc0, //0x00003e9d movl         %eax, %eax
	//0x00003e9f LBB0_789
	0x49, 0xf7, 0xd2, //0x00003e9f notq         %r10
	0x49, 0x29, 0xc2, //0x00003ea2 subq         %rax, %r10
	//0x00003ea5 LBB0_790
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003ea5 movabsq      $4294977024, %r13
	0x4d, 0x85, 0xd2, //0x00003eaf testq        %r10, %r10
	0x48, 0x8b, 0x4d, 0x90, //0x00003eb2 movq         $-112(%rbp), %rcx
	0x4c, 0x8b, 0x7d, 0xc0, //0x00003eb6 movq         $-64(%rbp), %r15
	0x0f, 0x88, 0xb5, 0x08, 0x00, 0x00, //0x00003eba js           LBB0_887
	0x4d, 0x01, 0xda, //0x00003ec0 addq         %r11, %r10
	//0x00003ec3 LBB0_792
	0x48, 0x8b, 0x45, 0xd0, //0x00003ec3 movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x10, //0x00003ec7 movq         %r10, (%rax)
	0x4d, 0x89, 0xdc, //0x00003eca movq         %r11, %r12
	0x4d, 0x85, 0xdb, //0x00003ecd testq        %r11, %r11
	0x48, 0x8b, 0x7d, 0xb8, //0x00003ed0 movq         $-72(%rbp), %rdi
	0x4c, 0x8d, 0x0d, 0xe1, 0x18, 0x00, 0x00, //0x00003ed4 leaq         $6369(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x0f, 0x89, 0x94, 0xf0, 0xff, 0xff, //0x00003edb jns          LBB0_552
	0xe9, 0xfd, 0x06, 0x00, 0x00, //0x00003ee1 jmp          LBB0_866
	//0x00003ee6 LBB0_665
	0x4d, 0x01, 0xf2, //0x00003ee6 addq         %r14, %r10
	0x49, 0x83, 0xff, 0x20, //0x00003ee9 cmpq         $32, %r15
	0x0f, 0x82, 0xd7, 0x01, 0x00, 0x00, //0x00003eed jb           LBB0_803
	//0x00003ef3 LBB0_666
	0xf3, 0x41, 0x0f, 0x6f, 0x1a, //0x00003ef3 movdqu       (%r10), %xmm3
	0xf3, 0x41, 0x0f, 0x6f, 0x62, 0x10, //0x00003ef8 movdqu       $16(%r10), %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x00003efe movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00003f02 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x00003f06 pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x6f, 0xec, //0x00003f0a movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00003f0e pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xc5, //0x00003f12 pmovmskb     %xmm5, %eax
	0x66, 0x0f, 0x74, 0xd9, //0x00003f16 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00003f1a pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x74, 0xe1, //0x00003f1e pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x00003f22 pmovmskb     %xmm4, %ecx
	0x48, 0xc1, 0xe0, 0x10, //0x00003f26 shlq         $16, %rax
	0x48, 0x09, 0xc2, //0x00003f2a orq          %rax, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x00003f2d shlq         $16, %rcx
	0x48, 0x09, 0xce, //0x00003f31 orq          %rcx, %rsi
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003f34 movabsq      $4294977024, %r13
	0x0f, 0x85, 0x2a, 0x03, 0x00, 0x00, //0x00003f3e jne          LBB0_822
	0x4d, 0x85, 0xc9, //0x00003f44 testq        %r9, %r9
	0x0f, 0x85, 0x3d, 0x03, 0x00, 0x00, //0x00003f47 jne          LBB0_824
	0x45, 0x31, 0xc9, //0x00003f4d xorl         %r9d, %r9d
	0x48, 0x85, 0xd2, //0x00003f50 testq        %rdx, %rdx
	0x0f, 0x84, 0x73, 0x03, 0x00, 0x00, //0x00003f53 je           LBB0_825
	//0x00003f59 LBB0_669
	0x48, 0x0f, 0xbc, 0xc2, //0x00003f59 bsfq         %rdx, %rax
	0x4c, 0x03, 0x55, 0xa8, //0x00003f5d addq         $-88(%rbp), %r10
	0x49, 0x01, 0xc2, //0x00003f61 addq         %rax, %r10
	0x48, 0x8b, 0x7d, 0xb8, //0x00003f64 movq         $-72(%rbp), %rdi
	0x48, 0x8b, 0x4d, 0x90, //0x00003f68 movq         $-112(%rbp), %rcx
	0x4c, 0x8b, 0x7d, 0xc0, //0x00003f6c movq         $-64(%rbp), %r15
	0x4c, 0x8d, 0x0d, 0x45, 0x18, 0x00, 0x00, //0x00003f70 leaq         $6213(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x4d, 0x85, 0xd2, //0x00003f77 testq        %r10, %r10
	0x0f, 0x89, 0xe3, 0xf7, 0xff, 0xff, //0x00003f7a jns          LBB0_676
	0xe9, 0xaf, 0x07, 0x00, 0x00, //0x00003f80 jmp          LBB0_608
	//0x00003f85 LBB0_793
	0x49, 0x8d, 0x49, 0xff, //0x00003f85 leaq         $-1(%r9), %rcx
	0x49, 0x39, 0xcc, //0x00003f89 cmpq         %rcx, %r12
	0x49, 0xf7, 0xd1, //0x00003f8c notq         %r9
	0x4c, 0x0f, 0x45, 0xce, //0x00003f8f cmovneq      %rsi, %r9
	0x84, 0xc0, //0x00003f93 testb        %al, %al
	0x4c, 0x0f, 0x44, 0xce, //0x00003f95 cmoveq       %rsi, %r9
	0x4d, 0x89, 0xcf, //0x00003f99 movq         %r9, %r15
	0xe9, 0xd1, 0xfd, 0xff, 0xff, //0x00003f9c jmp          LBB0_781
	//0x00003fa1 LBB0_794
	0x4c, 0x89, 0xfb, //0x00003fa1 movq         %r15, %rbx
	0x48, 0x8b, 0x75, 0xc8, //0x00003fa4 movq         $-56(%rbp), %rsi
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00003fa8 movq         $-1, %r15
	0x4d, 0x85, 0xe4, //0x00003faf testq        %r12, %r12
	0x0f, 0x85, 0x5f, 0xfd, 0xff, 0xff, //0x00003fb2 jne          LBB0_772
	0xe9, 0x5b, 0x12, 0x00, 0x00, //0x00003fb8 jmp          LBB0_967
	//0x00003fbd LBB0_795
	0x0f, 0xbc, 0xc0, //0x00003fbd bsfl         %eax, %eax
	0xe9, 0x94, 0x00, 0x00, 0x00, //0x00003fc0 jmp          LBB0_798
	//0x00003fc5 LBB0_701
	0x4d, 0x01, 0xf2, //0x00003fc5 addq         %r14, %r10
	0x49, 0x83, 0xff, 0x20, //0x00003fc8 cmpq         $32, %r15
	0x0f, 0x82, 0x01, 0x05, 0x00, 0x00, //0x00003fcc jb           LBB0_846
	//0x00003fd2 LBB0_702
	0xf3, 0x41, 0x0f, 0x6f, 0x1a, //0x00003fd2 movdqu       (%r10), %xmm3
	0xf3, 0x41, 0x0f, 0x6f, 0x62, 0x10, //0x00003fd7 movdqu       $16(%r10), %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x00003fdd movdqa       %xmm3, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00003fe1 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x00003fe5 pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x6f, 0xec, //0x00003fe9 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00003fed pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xc5, //0x00003ff1 pmovmskb     %xmm5, %eax
	0x66, 0x0f, 0x74, 0xd9, //0x00003ff5 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00003ff9 pmovmskb     %xmm3, %esi
	0x66, 0x0f, 0x74, 0xe1, //0x00003ffd pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x00004001 pmovmskb     %xmm4, %ecx
	0x48, 0xc1, 0xe0, 0x10, //0x00004005 shlq         $16, %rax
	0x48, 0x09, 0xc2, //0x00004009 orq          %rax, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x0000400c shlq         $16, %rcx
	0x48, 0x09, 0xce, //0x00004010 orq          %rcx, %rsi
	0x0f, 0x85, 0x54, 0x04, 0x00, 0x00, //0x00004013 jne          LBB0_842
	0x4d, 0x85, 0xc9, //0x00004019 testq        %r9, %r9
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000401c movabsq      $4294977024, %r13
	0x0f, 0x85, 0x5d, 0x04, 0x00, 0x00, //0x00004026 jne          LBB0_844
	0x45, 0x31, 0xc9, //0x0000402c xorl         %r9d, %r9d
	0x48, 0x85, 0xd2, //0x0000402f testq        %rdx, %rdx
	0x0f, 0x84, 0x93, 0x04, 0x00, 0x00, //0x00004032 je           LBB0_845
	//0x00004038 LBB0_705
	0x48, 0x0f, 0xbc, 0xc2, //0x00004038 bsfq         %rdx, %rax
	0x4c, 0x03, 0x55, 0xa8, //0x0000403c addq         $-88(%rbp), %r10
	0x49, 0x01, 0xc2, //0x00004040 addq         %rax, %r10
	0xe9, 0x7f, 0xf9, 0xff, 0xff, //0x00004043 jmp          LBB0_711
	//0x00004048 LBB0_796
	0x4f, 0x8d, 0x3c, 0x1e, //0x00004048 leaq         (%r14,%r11), %r15
	0x49, 0x29, 0xd7, //0x0000404c subq         %rdx, %r15
	0x49, 0x29, 0xff, //0x0000404f subq         %rdi, %r15
	0xe9, 0x1b, 0xfd, 0xff, 0xff, //0x00004052 jmp          LBB0_781
	//0x00004057 LBB0_797
	0x89, 0xc0, //0x00004057 movl         %eax, %eax
	//0x00004059 LBB0_798
	0x49, 0xf7, 0xd7, //0x00004059 notq         %r15
	0x49, 0x29, 0xc7, //0x0000405c subq         %rax, %r15
	0xe9, 0x0e, 0xfd, 0xff, 0xff, //0x0000405f jmp          LBB0_781
	//0x00004064 LBB0_799
	0x4d, 0x01, 0xf2, //0x00004064 addq         %r14, %r10
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00004067 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc9, //0x0000406f xorl         %r9d, %r9d
	0x49, 0x83, 0xff, 0x20, //0x00004072 cmpq         $32, %r15
	0x0f, 0x83, 0x4e, 0xfd, 0xff, 0xff, //0x00004076 jae          LBB0_598
	//0x0000407c LBB0_800
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000407c movabsq      $4294977024, %r13
	0xe9, 0xee, 0x00, 0x00, 0x00, //0x00004086 jmp          LBB0_810
	//0x0000408b LBB0_801
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000408b movq         $-1, %r12
	0x4c, 0x89, 0xc1, //0x00004092 movq         %r8, %rcx
	0x4c, 0x89, 0xcb, //0x00004095 movq         %r9, %rbx
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00004098 movq         $-1, %r15
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x0000409f movq         $-1, %r13
	0x48, 0x8d, 0x3d, 0x83, 0x19, 0x00, 0x00, //0x000040a6 leaq         $6531(%rip), %rdi  /* LJTI0_5+0(%rip) */
	0xe9, 0x31, 0xf4, 0xff, 0xff, //0x000040ad jmp          LBB0_642
	//0x000040b2 LBB0_802
	0x4d, 0x01, 0xf2, //0x000040b2 addq         %r14, %r10
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x000040b5 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc9, //0x000040bd xorl         %r9d, %r9d
	0x49, 0x83, 0xff, 0x20, //0x000040c0 cmpq         $32, %r15
	0x0f, 0x83, 0x29, 0xfe, 0xff, 0xff, //0x000040c4 jae          LBB0_666
	//0x000040ca LBB0_803
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000040ca movabsq      $4294977024, %r13
	0xe9, 0xfb, 0x01, 0x00, 0x00, //0x000040d4 jmp          LBB0_826
	//0x000040d9 LBB0_804
	0x4d, 0x01, 0xf2, //0x000040d9 addq         %r14, %r10
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x000040dc movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xc9, //0x000040e4 xorl         %r9d, %r9d
	0x49, 0x83, 0xff, 0x20, //0x000040e7 cmpq         $32, %r15
	0x0f, 0x83, 0xe1, 0xfe, 0xff, 0xff, //0x000040eb jae          LBB0_702
	0xe9, 0xdd, 0x03, 0x00, 0x00, //0x000040f1 jmp          LBB0_846
	//0x000040f6 LBB0_805
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000040f6 movq         $-1, %r12
	0x48, 0x89, 0xda, //0x000040fd movq         %rbx, %rdx
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00004100 movq         $-1, %r9
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00004107 movq         $-1, %r13
	0xe9, 0x6a, 0xfa, 0xff, 0xff, //0x0000410e jmp          LBB0_736
	//0x00004113 LBB0_806
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00004113 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00004118 jne          LBB0_808
	0x4c, 0x89, 0xd0, //0x0000411e movq         %r10, %rax
	0x4c, 0x29, 0xf0, //0x00004121 subq         %r14, %rax
	0x48, 0x0f, 0xbc, 0xce, //0x00004124 bsfq         %rsi, %rcx
	0x48, 0x01, 0xc1, //0x00004128 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x0000412b movq         %rcx, $-56(%rbp)
	//0x0000412f LBB0_808
	0x44, 0x89, 0xc8, //0x0000412f movl         %r9d, %eax
	0xf7, 0xd0, //0x00004132 notl         %eax
	0x21, 0xf0, //0x00004134 andl         %esi, %eax
	0x41, 0x8d, 0x0c, 0x41, //0x00004136 leal         (%r9,%rax,2), %ecx
	0x8d, 0x3c, 0x00, //0x0000413a leal         (%rax,%rax), %edi
	0xf7, 0xd7, //0x0000413d notl         %edi
	0x21, 0xf7, //0x0000413f andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004141 andl         $-1431655766, %edi
	0x45, 0x31, 0xc9, //0x00004147 xorl         %r9d, %r9d
	0x01, 0xc7, //0x0000414a addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc1, //0x0000414c setb         %r9b
	0x01, 0xff, //0x00004150 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00004152 xorl         $1431655765, %edi
	0x21, 0xcf, //0x00004158 andl         %ecx, %edi
	0xf7, 0xd7, //0x0000415a notl         %edi
	0x21, 0xfa, //0x0000415c andl         %edi, %edx
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000415e movabsq      $4294977024, %r13
	0x48, 0x85, 0xd2, //0x00004168 testq        %rdx, %rdx
	0x0f, 0x85, 0xbf, 0xfc, 0xff, 0xff, //0x0000416b jne          LBB0_601
	//0x00004171 LBB0_809
	0x49, 0x83, 0xc2, 0x20, //0x00004171 addq         $32, %r10
	0x49, 0x83, 0xc7, 0xe0, //0x00004175 addq         $-32, %r15
	//0x00004179 LBB0_810
	0x4d, 0x85, 0xc9, //0x00004179 testq        %r9, %r9
	0x0f, 0x85, 0x47, 0x02, 0x00, 0x00, //0x0000417c jne          LBB0_838
	0x48, 0x8b, 0x55, 0xc8, //0x00004182 movq         $-56(%rbp), %rdx
	0x48, 0x8b, 0x7d, 0xb8, //0x00004186 movq         $-72(%rbp), %rdi
	0x4c, 0x8d, 0x0d, 0x2b, 0x16, 0x00, 0x00, //0x0000418a leaq         $5675(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x4d, 0x85, 0xff, //0x00004191 testq        %r15, %r15
	0x0f, 0x84, 0x96, 0x00, 0x00, 0x00, //0x00004194 je           LBB0_821
	//0x0000419a LBB0_812
	0x31, 0xf6, //0x0000419a xorl         %esi, %esi
	//0x0000419c LBB0_813
	0x41, 0x0f, 0xb6, 0x04, 0x32, //0x0000419c movzbl       (%r10,%rsi), %eax
	0x3c, 0x22, //0x000041a1 cmpb         $34, %al
	0x0f, 0x84, 0x80, 0x00, 0x00, 0x00, //0x000041a3 je           LBB0_820
	0x3c, 0x5c, //0x000041a9 cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000041ab je           LBB0_818
	0x48, 0x83, 0xc6, 0x01, //0x000041b1 addq         $1, %rsi
	0x49, 0x39, 0xf7, //0x000041b5 cmpq         %rsi, %r15
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x000041b8 jne          LBB0_813
	0xe9, 0x86, 0x00, 0x00, 0x00, //0x000041be jmp          LBB0_816
	//0x000041c3 LBB0_818
	0x49, 0x8d, 0x47, 0xff, //0x000041c3 leaq         $-1(%r15), %rax
	0x48, 0x39, 0xf0, //0x000041c7 cmpq         %rsi, %rax
	0x0f, 0x84, 0x6e, 0x05, 0x00, 0x00, //0x000041ca je           LBB0_609
	0x48, 0x8b, 0x45, 0xb0, //0x000041d0 movq         $-80(%rbp), %rax
	0x4c, 0x01, 0xd0, //0x000041d4 addq         %r10, %rax
	0x48, 0x01, 0xf0, //0x000041d7 addq         %rsi, %rax
	0x48, 0x83, 0xfa, 0xff, //0x000041da cmpq         $-1, %rdx
	0x48, 0x8b, 0x4d, 0xc8, //0x000041de movq         $-56(%rbp), %rcx
	0x48, 0x0f, 0x44, 0xc8, //0x000041e2 cmoveq       %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x000041e6 movq         %rcx, $-56(%rbp)
	0x48, 0x0f, 0x44, 0xd0, //0x000041ea cmoveq       %rax, %rdx
	0x49, 0x01, 0xf2, //0x000041ee addq         %rsi, %r10
	0x49, 0x83, 0xc2, 0x02, //0x000041f1 addq         $2, %r10
	0x4c, 0x89, 0xf8, //0x000041f5 movq         %r15, %rax
	0x48, 0x29, 0xf0, //0x000041f8 subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000041fb addq         $-2, %rax
	0x49, 0x83, 0xc7, 0xfe, //0x000041ff addq         $-2, %r15
	0x49, 0x39, 0xf7, //0x00004203 cmpq         %rsi, %r15
	0x49, 0x89, 0xc7, //0x00004206 movq         %rax, %r15
	0x48, 0x8b, 0x7d, 0xb8, //0x00004209 movq         $-72(%rbp), %rdi
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000420d movabsq      $4294977024, %r13
	0x4c, 0x8d, 0x0d, 0x9e, 0x15, 0x00, 0x00, //0x00004217 leaq         $5534(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x0f, 0x85, 0x76, 0xff, 0xff, 0xff, //0x0000421e jne          LBB0_812
	0xe9, 0x15, 0x05, 0x00, 0x00, //0x00004224 jmp          LBB0_609
	//0x00004229 LBB0_820
	0x49, 0x01, 0xf2, //0x00004229 addq         %rsi, %r10
	0x49, 0x83, 0xc2, 0x01, //0x0000422c addq         $1, %r10
	//0x00004230 LBB0_821
	0x4d, 0x29, 0xf2, //0x00004230 subq         %r14, %r10
	0x48, 0x8b, 0x4d, 0x90, //0x00004233 movq         $-112(%rbp), %rcx
	0x4c, 0x8b, 0x7d, 0xc0, //0x00004237 movq         $-64(%rbp), %r15
	0x4d, 0x85, 0xd2, //0x0000423b testq        %r10, %r10
	0x0f, 0x89, 0x14, 0xed, 0xff, 0xff, //0x0000423e jns          LBB0_551
	0xe9, 0xeb, 0x04, 0x00, 0x00, //0x00004244 jmp          LBB0_608
	//0x00004249 LBB0_816
	0x3c, 0x22, //0x00004249 cmpb         $34, %al
	0x0f, 0x85, 0xed, 0x04, 0x00, 0x00, //0x0000424b jne          LBB0_609
	0x4d, 0x01, 0xfa, //0x00004251 addq         %r15, %r10
	0x48, 0x8b, 0x7d, 0xb8, //0x00004254 movq         $-72(%rbp), %rdi
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00004258 movabsq      $4294977024, %r13
	0x4c, 0x8d, 0x0d, 0x53, 0x15, 0x00, 0x00, //0x00004262 leaq         $5459(%rip), %r9  /* LJTI0_2+0(%rip) */
	0xe9, 0xc2, 0xff, 0xff, 0xff, //0x00004269 jmp          LBB0_821
	//0x0000426e LBB0_822
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x0000426e cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00004273 jne          LBB0_824
	0x4c, 0x89, 0xd0, //0x00004279 movq         %r10, %rax
	0x4c, 0x29, 0xf0, //0x0000427c subq         %r14, %rax
	0x48, 0x0f, 0xbc, 0xce, //0x0000427f bsfq         %rsi, %rcx
	0x48, 0x01, 0xc1, //0x00004283 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00004286 movq         %rcx, $-56(%rbp)
	//0x0000428a LBB0_824
	0x44, 0x89, 0xc8, //0x0000428a movl         %r9d, %eax
	0xf7, 0xd0, //0x0000428d notl         %eax
	0x21, 0xf0, //0x0000428f andl         %esi, %eax
	0x41, 0x8d, 0x0c, 0x41, //0x00004291 leal         (%r9,%rax,2), %ecx
	0x8d, 0x3c, 0x00, //0x00004295 leal         (%rax,%rax), %edi
	0xf7, 0xd7, //0x00004298 notl         %edi
	0x21, 0xf7, //0x0000429a andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000429c andl         $-1431655766, %edi
	0x45, 0x31, 0xc9, //0x000042a2 xorl         %r9d, %r9d
	0x01, 0xc7, //0x000042a5 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc1, //0x000042a7 setb         %r9b
	0x01, 0xff, //0x000042ab addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000042ad xorl         $1431655765, %edi
	0x21, 0xcf, //0x000042b3 andl         %ecx, %edi
	0xf7, 0xd7, //0x000042b5 notl         %edi
	0x21, 0xfa, //0x000042b7 andl         %edi, %edx
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000042b9 movabsq      $4294977024, %r13
	0x48, 0x85, 0xd2, //0x000042c3 testq        %rdx, %rdx
	0x0f, 0x85, 0x8d, 0xfc, 0xff, 0xff, //0x000042c6 jne          LBB0_669
	//0x000042cc LBB0_825
	0x49, 0x83, 0xc2, 0x20, //0x000042cc addq         $32, %r10
	0x49, 0x83, 0xc7, 0xe0, //0x000042d0 addq         $-32, %r15
	//0x000042d4 LBB0_826
	0x4d, 0x85, 0xc9, //0x000042d4 testq        %r9, %r9
	0x0f, 0x85, 0x3e, 0x01, 0x00, 0x00, //0x000042d7 jne          LBB0_840
	0x48, 0x8b, 0x55, 0xc8, //0x000042dd movq         $-56(%rbp), %rdx
	0x48, 0x8b, 0x7d, 0xb8, //0x000042e1 movq         $-72(%rbp), %rdi
	0x4c, 0x8d, 0x0d, 0xd0, 0x14, 0x00, 0x00, //0x000042e5 leaq         $5328(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x4d, 0x85, 0xff, //0x000042ec testq        %r15, %r15
	0x0f, 0x84, 0x96, 0x00, 0x00, 0x00, //0x000042ef je           LBB0_837
	//0x000042f5 LBB0_828
	0x31, 0xf6, //0x000042f5 xorl         %esi, %esi
	//0x000042f7 LBB0_829
	0x41, 0x0f, 0xb6, 0x04, 0x32, //0x000042f7 movzbl       (%r10,%rsi), %eax
	0x3c, 0x22, //0x000042fc cmpb         $34, %al
	0x0f, 0x84, 0x80, 0x00, 0x00, 0x00, //0x000042fe je           LBB0_836
	0x3c, 0x5c, //0x00004304 cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00004306 je           LBB0_834
	0x48, 0x83, 0xc6, 0x01, //0x0000430c addq         $1, %rsi
	0x49, 0x39, 0xf7, //0x00004310 cmpq         %rsi, %r15
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x00004313 jne          LBB0_829
	0xe9, 0x86, 0x00, 0x00, 0x00, //0x00004319 jmp          LBB0_832
	//0x0000431e LBB0_834
	0x49, 0x8d, 0x47, 0xff, //0x0000431e leaq         $-1(%r15), %rax
	0x48, 0x39, 0xf0, //0x00004322 cmpq         %rsi, %rax
	0x0f, 0x84, 0x13, 0x04, 0x00, 0x00, //0x00004325 je           LBB0_609
	0x48, 0x8b, 0x45, 0xb0, //0x0000432b movq         $-80(%rbp), %rax
	0x4c, 0x01, 0xd0, //0x0000432f addq         %r10, %rax
	0x48, 0x01, 0xf0, //0x00004332 addq         %rsi, %rax
	0x48, 0x83, 0xfa, 0xff, //0x00004335 cmpq         $-1, %rdx
	0x48, 0x8b, 0x4d, 0xc8, //0x00004339 movq         $-56(%rbp), %rcx
	0x48, 0x0f, 0x44, 0xc8, //0x0000433d cmoveq       %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00004341 movq         %rcx, $-56(%rbp)
	0x48, 0x0f, 0x44, 0xd0, //0x00004345 cmoveq       %rax, %rdx
	0x49, 0x01, 0xf2, //0x00004349 addq         %rsi, %r10
	0x49, 0x83, 0xc2, 0x02, //0x0000434c addq         $2, %r10
	0x4c, 0x89, 0xf8, //0x00004350 movq         %r15, %rax
	0x48, 0x29, 0xf0, //0x00004353 subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00004356 addq         $-2, %rax
	0x49, 0x83, 0xc7, 0xfe, //0x0000435a addq         $-2, %r15
	0x49, 0x39, 0xf7, //0x0000435e cmpq         %rsi, %r15
	0x49, 0x89, 0xc7, //0x00004361 movq         %rax, %r15
	0x48, 0x8b, 0x7d, 0xb8, //0x00004364 movq         $-72(%rbp), %rdi
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00004368 movabsq      $4294977024, %r13
	0x4c, 0x8d, 0x0d, 0x43, 0x14, 0x00, 0x00, //0x00004372 leaq         $5187(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x0f, 0x85, 0x76, 0xff, 0xff, 0xff, //0x00004379 jne          LBB0_828
	0xe9, 0xba, 0x03, 0x00, 0x00, //0x0000437f jmp          LBB0_609
	//0x00004384 LBB0_836
	0x49, 0x01, 0xf2, //0x00004384 addq         %rsi, %r10
	0x49, 0x83, 0xc2, 0x01, //0x00004387 addq         $1, %r10
	//0x0000438b LBB0_837
	0x4d, 0x29, 0xf2, //0x0000438b subq         %r14, %r10
	0x48, 0x8b, 0x4d, 0x90, //0x0000438e movq         $-112(%rbp), %rcx
	0x4c, 0x8b, 0x7d, 0xc0, //0x00004392 movq         $-64(%rbp), %r15
	0x4d, 0x85, 0xd2, //0x00004396 testq        %r10, %r10
	0x0f, 0x89, 0xc4, 0xf3, 0xff, 0xff, //0x00004399 jns          LBB0_676
	0xe9, 0x90, 0x03, 0x00, 0x00, //0x0000439f jmp          LBB0_608
	//0x000043a4 LBB0_832
	0x3c, 0x22, //0x000043a4 cmpb         $34, %al
	0x0f, 0x85, 0x92, 0x03, 0x00, 0x00, //0x000043a6 jne          LBB0_609
	0x4d, 0x01, 0xfa, //0x000043ac addq         %r15, %r10
	0x48, 0x8b, 0x7d, 0xb8, //0x000043af movq         $-72(%rbp), %rdi
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000043b3 movabsq      $4294977024, %r13
	0x4c, 0x8d, 0x0d, 0xf8, 0x13, 0x00, 0x00, //0x000043bd leaq         $5112(%rip), %r9  /* LJTI0_2+0(%rip) */
	0xe9, 0xc2, 0xff, 0xff, 0xff, //0x000043c4 jmp          LBB0_837
	//0x000043c9 LBB0_838
	0x4d, 0x85, 0xff, //0x000043c9 testq        %r15, %r15
	0x0f, 0x84, 0x6c, 0x03, 0x00, 0x00, //0x000043cc je           LBB0_609
	0x48, 0x8b, 0x45, 0xa0, //0x000043d2 movq         $-96(%rbp), %rax
	0x4c, 0x01, 0xd0, //0x000043d6 addq         %r10, %rax
	0x48, 0x8b, 0x4d, 0xc8, //0x000043d9 movq         $-56(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x000043dd cmpq         $-1, %rcx
	0x48, 0x89, 0xca, //0x000043e1 movq         %rcx, %rdx
	0x48, 0x0f, 0x44, 0xc8, //0x000043e4 cmoveq       %rax, %rcx
	0x48, 0x0f, 0x44, 0xd0, //0x000043e8 cmoveq       %rax, %rdx
	0x49, 0x83, 0xc2, 0x01, //0x000043ec addq         $1, %r10
	0x49, 0x83, 0xc7, 0xff, //0x000043f0 addq         $-1, %r15
	0x48, 0x89, 0x4d, 0xc8, //0x000043f4 movq         %rcx, $-56(%rbp)
	0x48, 0x8b, 0x7d, 0xb8, //0x000043f8 movq         $-72(%rbp), %rdi
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000043fc movabsq      $4294977024, %r13
	0x4c, 0x8d, 0x0d, 0xaf, 0x13, 0x00, 0x00, //0x00004406 leaq         $5039(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x4d, 0x85, 0xff, //0x0000440d testq        %r15, %r15
	0x0f, 0x85, 0x84, 0xfd, 0xff, 0xff, //0x00004410 jne          LBB0_812
	0xe9, 0x15, 0xfe, 0xff, 0xff, //0x00004416 jmp          LBB0_821
	//0x0000441b LBB0_840
	0x4d, 0x85, 0xff, //0x0000441b testq        %r15, %r15
	0x0f, 0x84, 0x1a, 0x03, 0x00, 0x00, //0x0000441e je           LBB0_609
	0x48, 0x8b, 0x45, 0xa0, //0x00004424 movq         $-96(%rbp), %rax
	0x4c, 0x01, 0xd0, //0x00004428 addq         %r10, %rax
	0x48, 0x8b, 0x4d, 0xc8, //0x0000442b movq         $-56(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x0000442f cmpq         $-1, %rcx
	0x48, 0x89, 0xca, //0x00004433 movq         %rcx, %rdx
	0x48, 0x0f, 0x44, 0xc8, //0x00004436 cmoveq       %rax, %rcx
	0x48, 0x0f, 0x44, 0xd0, //0x0000443a cmoveq       %rax, %rdx
	0x49, 0x83, 0xc2, 0x01, //0x0000443e addq         $1, %r10
	0x49, 0x83, 0xc7, 0xff, //0x00004442 addq         $-1, %r15
	0x48, 0x89, 0x4d, 0xc8, //0x00004446 movq         %rcx, $-56(%rbp)
	0x48, 0x8b, 0x7d, 0xb8, //0x0000444a movq         $-72(%rbp), %rdi
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000444e movabsq      $4294977024, %r13
	0x4c, 0x8d, 0x0d, 0x5d, 0x13, 0x00, 0x00, //0x00004458 leaq         $4957(%rip), %r9  /* LJTI0_2+0(%rip) */
	0x4d, 0x85, 0xff, //0x0000445f testq        %r15, %r15
	0x0f, 0x85, 0x8d, 0xfe, 0xff, 0xff, //0x00004462 jne          LBB0_828
	0xe9, 0x1e, 0xff, 0xff, 0xff, //0x00004468 jmp          LBB0_837
	//0x0000446d LBB0_842
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x0000446d cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00004472 jne          LBB0_844
	0x4c, 0x89, 0xd0, //0x00004478 movq         %r10, %rax
	0x4c, 0x29, 0xf0, //0x0000447b subq         %r14, %rax
	0x48, 0x0f, 0xbc, 0xce, //0x0000447e bsfq         %rsi, %rcx
	0x48, 0x01, 0xc1, //0x00004482 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00004485 movq         %rcx, $-56(%rbp)
	//0x00004489 LBB0_844
	0x44, 0x89, 0xc8, //0x00004489 movl         %r9d, %eax
	0xf7, 0xd0, //0x0000448c notl         %eax
	0x21, 0xf0, //0x0000448e andl         %esi, %eax
	0x41, 0x8d, 0x0c, 0x41, //0x00004490 leal         (%r9,%rax,2), %ecx
	0x8d, 0x3c, 0x00, //0x00004494 leal         (%rax,%rax), %edi
	0xf7, 0xd7, //0x00004497 notl         %edi
	0x21, 0xf7, //0x00004499 andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000449b andl         $-1431655766, %edi
	0x45, 0x31, 0xc9, //0x000044a1 xorl         %r9d, %r9d
	0x01, 0xc7, //0x000044a4 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc1, //0x000044a6 setb         %r9b
	0x01, 0xff, //0x000044aa addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000044ac xorl         $1431655765, %edi
	0x21, 0xcf, //0x000044b2 andl         %ecx, %edi
	0xf7, 0xd7, //0x000044b4 notl         %edi
	0x21, 0xfa, //0x000044b6 andl         %edi, %edx
	0x49, 0xbd, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000044b8 movabsq      $4294977024, %r13
	0x48, 0x85, 0xd2, //0x000044c2 testq        %rdx, %rdx
	0x0f, 0x85, 0x6d, 0xfb, 0xff, 0xff, //0x000044c5 jne          LBB0_705
	//0x000044cb LBB0_845
	0x49, 0x83, 0xc2, 0x20, //0x000044cb addq         $32, %r10
	0x49, 0x83, 0xc7, 0xe0, //0x000044cf addq         $-32, %r15
	//0x000044d3 LBB0_846
	0x4d, 0x85, 0xc9, //0x000044d3 testq        %r9, %r9
	0x0f, 0x85, 0xa6, 0x00, 0x00, 0x00, //0x000044d6 jne          LBB0_858
	0x48, 0x8b, 0x55, 0xc8, //0x000044dc movq         $-56(%rbp), %rdx
	0x4d, 0x85, 0xff, //0x000044e0 testq        %r15, %r15
	0x0f, 0x84, 0x81, 0x00, 0x00, 0x00, //0x000044e3 je           LBB0_857
	//0x000044e9 LBB0_848
	0x31, 0xf6, //0x000044e9 xorl         %esi, %esi
	//0x000044eb LBB0_849
	0x41, 0x0f, 0xb6, 0x04, 0x32, //0x000044eb movzbl       (%r10,%rsi), %eax
	0x3c, 0x22, //0x000044f0 cmpb         $34, %al
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x000044f2 je           LBB0_856
	0x3c, 0x5c, //0x000044f8 cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000044fa je           LBB0_854
	0x48, 0x83, 0xc6, 0x01, //0x00004500 addq         $1, %rsi
	0x49, 0x39, 0xf7, //0x00004504 cmpq         %rsi, %r15
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x00004507 jne          LBB0_849
	0xe9, 0x60, 0x00, 0x00, 0x00, //0x0000450d jmp          LBB0_852
	//0x00004512 LBB0_854
	0x49, 0x8d, 0x47, 0xff, //0x00004512 leaq         $-1(%r15), %rax
	0x48, 0x39, 0xf0, //0x00004516 cmpq         %rsi, %rax
	0x0f, 0x84, 0x1f, 0x02, 0x00, 0x00, //0x00004519 je           LBB0_609
	0x48, 0x8b, 0x45, 0xb0, //0x0000451f movq         $-80(%rbp), %rax
	0x4c, 0x01, 0xd0, //0x00004523 addq         %r10, %rax
	0x48, 0x01, 0xf0, //0x00004526 addq         %rsi, %rax
	0x48, 0x83, 0xfa, 0xff, //0x00004529 cmpq         $-1, %rdx
	0x48, 0x8b, 0x4d, 0xc8, //0x0000452d movq         $-56(%rbp), %rcx
	0x48, 0x0f, 0x44, 0xc8, //0x00004531 cmoveq       %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00004535 movq         %rcx, $-56(%rbp)
	0x48, 0x0f, 0x44, 0xd0, //0x00004539 cmoveq       %rax, %rdx
	0x49, 0x01, 0xf2, //0x0000453d addq         %rsi, %r10
	0x49, 0x83, 0xc2, 0x02, //0x00004540 addq         $2, %r10
	0x4c, 0x89, 0xf8, //0x00004544 movq         %r15, %rax
	0x48, 0x29, 0xf0, //0x00004547 subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x0000454a addq         $-2, %rax
	0x49, 0x83, 0xc7, 0xfe, //0x0000454e addq         $-2, %r15
	0x49, 0x39, 0xf7, //0x00004552 cmpq         %rsi, %r15
	0x49, 0x89, 0xc7, //0x00004555 movq         %rax, %r15
	0x0f, 0x85, 0x8b, 0xff, 0xff, 0xff, //0x00004558 jne          LBB0_848
	0xe9, 0xdb, 0x01, 0x00, 0x00, //0x0000455e jmp          LBB0_609
	//0x00004563 LBB0_856
	0x49, 0x01, 0xf2, //0x00004563 addq         %rsi, %r10
	0x49, 0x83, 0xc2, 0x01, //0x00004566 addq         $1, %r10
	//0x0000456a LBB0_857
	0x4d, 0x29, 0xf2, //0x0000456a subq         %r14, %r10
	0xe9, 0x4b, 0xf4, 0xff, 0xff, //0x0000456d jmp          LBB0_710
	//0x00004572 LBB0_852
	0x3c, 0x22, //0x00004572 cmpb         $34, %al
	0x0f, 0x85, 0xc4, 0x01, 0x00, 0x00, //0x00004574 jne          LBB0_609
	0x4d, 0x01, 0xfa, //0x0000457a addq         %r15, %r10
	0xe9, 0xe8, 0xff, 0xff, 0xff, //0x0000457d jmp          LBB0_857
	//0x00004582 LBB0_858
	0x4d, 0x85, 0xff, //0x00004582 testq        %r15, %r15
	0x0f, 0x84, 0xb3, 0x01, 0x00, 0x00, //0x00004585 je           LBB0_609
	0x48, 0x8b, 0x45, 0xa0, //0x0000458b movq         $-96(%rbp), %rax
	0x4c, 0x01, 0xd0, //0x0000458f addq         %r10, %rax
	0x48, 0x8b, 0x4d, 0xc8, //0x00004592 movq         $-56(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x00004596 cmpq         $-1, %rcx
	0x48, 0x89, 0xca, //0x0000459a movq         %rcx, %rdx
	0x48, 0x0f, 0x44, 0xc8, //0x0000459d cmoveq       %rax, %rcx
	0x48, 0x0f, 0x44, 0xd0, //0x000045a1 cmoveq       %rax, %rdx
	0x49, 0x83, 0xc2, 0x01, //0x000045a5 addq         $1, %r10
	0x49, 0x83, 0xc7, 0xff, //0x000045a9 addq         $-1, %r15
	0x48, 0x89, 0x4d, 0xc8, //0x000045ad movq         %rcx, $-56(%rbp)
	0x4d, 0x85, 0xff, //0x000045b1 testq        %r15, %r15
	0x0f, 0x85, 0x2f, 0xff, 0xff, 0xff, //0x000045b4 jne          LBB0_848
	0xe9, 0xab, 0xff, 0xff, 0xff, //0x000045ba jmp          LBB0_857
	//0x000045bf LBB0_860
	0x48, 0x8b, 0x45, 0xd0, //0x000045bf movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x10, //0x000045c3 movq         %r10, (%rax)
	//0x000045c6 LBB0_861
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000045c6 movq         $-1, %r12
	0xe9, 0x11, 0x00, 0x00, 0x00, //0x000045cd jmp          LBB0_866
	//0x000045d2 LBB0_863
	0x49, 0x89, 0xca, //0x000045d2 movq         %rcx, %r10
	//0x000045d5 LBB0_864
	0x49, 0x83, 0xc2, 0xff, //0x000045d5 addq         $-1, %r10
	0x4d, 0x89, 0x13, //0x000045d9 movq         %r10, (%r11)
	//0x000045dc LBB0_865
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000045dc movq         $-2, %r12
	//0x000045e3 LBB0_866
	0x4c, 0x89, 0xe0, //0x000045e3 movq         %r12, %rax
	0x48, 0x81, 0xc4, 0x98, 0x00, 0x00, 0x00, //0x000045e6 addq         $152, %rsp
	0x5b, //0x000045ed popq         %rbx
	0x41, 0x5c, //0x000045ee popq         %r12
	0x41, 0x5d, //0x000045f0 popq         %r13
	0x41, 0x5e, //0x000045f2 popq         %r14
	0x41, 0x5f, //0x000045f4 popq         %r15
	0x5d, //0x000045f6 popq         %rbp
	0xc3, //0x000045f7 retq         
	//0x000045f8 LBB0_867
	0x4c, 0x01, 0xee, //0x000045f8 addq         %r13, %rsi
	0x48, 0x89, 0xf0, //0x000045fb movq         %rsi, %rax
	//0x000045fe LBB0_868
	0x4c, 0x29, 0xe8, //0x000045fe subq         %r13, %rax
	0x48, 0x89, 0xc6, //0x00004601 movq         %rax, %rsi
	//0x00004604 LBB0_869
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00004604 movq         $-1, %r12
	0x49, 0x89, 0xf2, //0x0000460b movq         %rsi, %r10
	0x48, 0x39, 0xd6, //0x0000460e cmpq         %rdx, %rsi
	0x0f, 0x83, 0xcc, 0xff, 0xff, 0xff, //0x00004611 jae          LBB0_866
	//0x00004617 LBB0_870
	0x49, 0x8d, 0x52, 0x01, //0x00004617 leaq         $1(%r10), %rdx
	0x48, 0x8b, 0x45, 0xd0, //0x0000461b movq         $-48(%rbp), %rax
	0x48, 0x89, 0x10, //0x0000461f movq         %rdx, (%rax)
	0x43, 0x0f, 0xbe, 0x44, 0x15, 0x00, //0x00004622 movsbl       (%r13,%r10), %eax
	0x83, 0xf8, 0x7b, //0x00004628 cmpl         $123, %eax
	0x0f, 0x87, 0x59, 0x01, 0x00, 0x00, //0x0000462b ja           LBB0_890
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00004631 movq         $-1, %r12
	0x48, 0x8d, 0x35, 0x5d, 0x14, 0x00, 0x00, //0x00004638 leaq         $5213(%rip), %rsi  /* LJTI0_6+0(%rip) */
	0x48, 0x63, 0x04, 0x86, //0x0000463f movslq       (%rsi,%rax,4), %rax
	0x48, 0x01, 0xf0, //0x00004643 addq         %rsi, %rax
	0xff, 0xe0, //0x00004646 jmpq         *%rax
	//0x00004648 LBB0_872
	0x48, 0x8b, 0x47, 0x08, //0x00004648 movq         $8(%rdi), %rax
	0x48, 0x89, 0xc6, //0x0000464c movq         %rax, %rsi
	0x48, 0x29, 0xd6, //0x0000464f subq         %rdx, %rsi
	0x48, 0x83, 0xfe, 0x10, //0x00004652 cmpq         $16, %rsi
	0x0f, 0x82, 0x22, 0x0d, 0x00, 0x00, //0x00004656 jb           LBB0_980
	0x4c, 0x89, 0xd6, //0x0000465c movq         %r10, %rsi
	0x48, 0xf7, 0xd6, //0x0000465f notq         %rsi
	0xf3, 0x0f, 0x6f, 0x05, 0x96, 0xb9, 0xff, 0xff, //0x00004662 movdqu       $-18026(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x9e, 0xb9, 0xff, 0xff, //0x0000466a movdqu       $-18018(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x15, 0xa6, 0xb9, 0xff, 0xff, //0x00004672 movdqu       $-18010(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	0x4c, 0x8b, 0x45, 0xd0, //0x0000467a movq         $-48(%rbp), %r8
	0x90, 0x90, //0x0000467e .p2align 4, 0x90
	//0x00004680 LBB0_874
	0xf3, 0x41, 0x0f, 0x6f, 0x5c, 0x15, 0x00, //0x00004680 movdqu       (%r13,%rdx), %xmm3
	0x66, 0x0f, 0x6f, 0xe3, //0x00004687 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x0000468b pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xdb, 0xd9, //0x0000468f pand         %xmm1, %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x00004693 pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xeb, 0xdc, //0x00004697 por          %xmm4, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x0000469b pmovmskb     %xmm3, %edi
	0x85, 0xff, //0x0000469f testl        %edi, %edi
	0x0f, 0x85, 0x6c, 0x00, 0x00, 0x00, //0x000046a1 jne          LBB0_884
	0x48, 0x83, 0xc2, 0x10, //0x000046a7 addq         $16, %rdx
	0x48, 0x8d, 0x0c, 0x30, //0x000046ab leaq         (%rax,%rsi), %rcx
	0x48, 0x83, 0xc1, 0xf0, //0x000046af addq         $-16, %rcx
	0x48, 0x83, 0xc6, 0xf0, //0x000046b3 addq         $-16, %rsi
	0x48, 0x83, 0xf9, 0x0f, //0x000046b7 cmpq         $15, %rcx
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x000046bb ja           LBB0_874
	0x4c, 0x89, 0xea, //0x000046c1 movq         %r13, %rdx
	0x48, 0x29, 0xf2, //0x000046c4 subq         %rsi, %rdx
	0x48, 0x01, 0xf0, //0x000046c7 addq         %rsi, %rax
	0x48, 0x89, 0xc6, //0x000046ca movq         %rax, %rsi
	0x48, 0x85, 0xf6, //0x000046cd testq        %rsi, %rsi
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x000046d0 je           LBB0_883
	//0x000046d6 LBB0_877
	0x48, 0x8d, 0x3c, 0x32, //0x000046d6 leaq         (%rdx,%rsi), %rdi
	0x31, 0xc0, //0x000046da xorl         %eax, %eax
	//0x000046dc LBB0_878
	0x0f, 0xb6, 0x1c, 0x02, //0x000046dc movzbl       (%rdx,%rax), %ebx
	0x80, 0xfb, 0x2c, //0x000046e0 cmpb         $44, %bl
	0x0f, 0x84, 0x17, 0x0b, 0x00, 0x00, //0x000046e3 je           LBB0_965
	0x80, 0xfb, 0x7d, //0x000046e9 cmpb         $125, %bl
	0x0f, 0x84, 0x0e, 0x0b, 0x00, 0x00, //0x000046ec je           LBB0_965
	0x80, 0xfb, 0x5d, //0x000046f2 cmpb         $93, %bl
	0x0f, 0x84, 0x05, 0x0b, 0x00, 0x00, //0x000046f5 je           LBB0_965
	0x48, 0x83, 0xc0, 0x01, //0x000046fb addq         $1, %rax
	0x48, 0x39, 0xc6, //0x000046ff cmpq         %rax, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00004702 jne          LBB0_878
	0x48, 0x89, 0xfa, //0x00004708 movq         %rdi, %rdx
	//0x0000470b LBB0_883
	0x4c, 0x29, 0xea, //0x0000470b subq         %r13, %rdx
	0xe9, 0x2e, 0x0c, 0x00, 0x00, //0x0000470e jmp          LBB0_975
	//0x00004713 LBB0_884
	0x66, 0x0f, 0xbc, 0xc7, //0x00004713 bsfw         %di, %ax
	0x0f, 0xb7, 0xc0, //0x00004717 movzwl       %ax, %eax
	0x48, 0x29, 0xf0, //0x0000471a subq         %rsi, %rax
	0x49, 0x89, 0x00, //0x0000471d movq         %rax, (%r8)
	0x4d, 0x89, 0xd4, //0x00004720 movq         %r10, %r12
	0xe9, 0xbb, 0xfe, 0xff, 0xff, //0x00004723 jmp          LBB0_866
	//0x00004728 LBB0_968
	0x49, 0xc7, 0xc4, 0xf9, 0xff, 0xff, 0xff, //0x00004728 movq         $-7, %r12
	0xe9, 0xaf, 0xfe, 0xff, 0xff, //0x0000472f jmp          LBB0_866
	//0x00004734 LBB0_608
	0x49, 0x83, 0xfa, 0xff, //0x00004734 cmpq         $-1, %r10
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00004738 jne          LBB0_610
	//0x0000473e LBB0_609
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000473e movq         $-1, %r10
	0x48, 0x8b, 0x45, 0x98, //0x00004745 movq         $-104(%rbp), %rax
	0x48, 0x89, 0x45, 0xc8, //0x00004749 movq         %rax, $-56(%rbp)
	//0x0000474d LBB0_610
	0x48, 0x8b, 0x45, 0xd0, //0x0000474d movq         $-48(%rbp), %rax
	0x48, 0x8b, 0x4d, 0xc8, //0x00004751 movq         $-56(%rbp), %rcx
	0x48, 0x89, 0x08, //0x00004755 movq         %rcx, (%rax)
	0x4d, 0x89, 0xd4, //0x00004758 movq         %r10, %r12
	0xe9, 0x83, 0xfe, 0xff, 0xff, //0x0000475b jmp          LBB0_866
	//0x00004760 LBB0_885
	0x49, 0x8d, 0x42, 0x04, //0x00004760 leaq         $4(%r10), %rax
	0xe9, 0x86, 0x05, 0x00, 0x00, //0x00004764 jmp          LBB0_929
	//0x00004769 LBB0_886
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00004769 movq         $-1, %rax
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00004770 jmp          LBB0_888
	//0x00004775 LBB0_887
	0x4c, 0x89, 0xd0, //0x00004775 movq         %r10, %rax
	//0x00004778 LBB0_888
	0x48, 0xf7, 0xd0, //0x00004778 notq         %rax
	0x49, 0x01, 0xc3, //0x0000477b addq         %rax, %r11
	//0x0000477e LBB0_889
	0x48, 0x8b, 0x45, 0xd0, //0x0000477e movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x18, //0x00004782 movq         %r11, (%rax)
	0xe9, 0x52, 0xfe, 0xff, 0xff, //0x00004785 jmp          LBB0_865
	//0x0000478a LBB0_890
	0x48, 0x8b, 0x45, 0xd0, //0x0000478a movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x10, //0x0000478e movq         %r10, (%rax)
	0xe9, 0x46, 0xfe, 0xff, 0xff, //0x00004791 jmp          LBB0_865
	//0x00004796 LBB0_891
	0x4c, 0x8b, 0x47, 0x08, //0x00004796 movq         $8(%rdi), %r8
	0x4d, 0x89, 0xc6, //0x0000479a movq         %r8, %r14
	0x49, 0x29, 0xd6, //0x0000479d subq         %rdx, %r14
	0x49, 0x83, 0xfe, 0x20, //0x000047a0 cmpq         $32, %r14
	0x0f, 0x8c, 0xe9, 0x0b, 0x00, 0x00, //0x000047a4 jl           LBB0_985
	0x4f, 0x8d, 0x0c, 0x2a, //0x000047aa leaq         (%r10,%r13), %r9
	0x4d, 0x29, 0xd0, //0x000047ae subq         %r10, %r8
	0xbe, 0x1f, 0x00, 0x00, 0x00, //0x000047b1 movl         $31, %esi
	0x45, 0x31, 0xf6, //0x000047b6 xorl         %r14d, %r14d
	0xf3, 0x0f, 0x6f, 0x05, 0x6f, 0xb8, 0xff, 0xff, //0x000047b9 movdqu       $-18321(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x77, 0xb8, 0xff, 0xff, //0x000047c1 movdqu       $-18313(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x45, 0x31, 0xdb, //0x000047c9 xorl         %r11d, %r11d
	//0x000047cc LBB0_893
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x31, 0x01, //0x000047cc movdqu       $1(%r9,%r14), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x31, 0x11, //0x000047d3 movdqu       $17(%r9,%r14), %xmm3
	0x66, 0x0f, 0x6f, 0xe2, //0x000047da movdqa       %xmm2, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x000047de pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x000047e2 pmovmskb     %xmm4, %edx
	0x66, 0x0f, 0x6f, 0xe3, //0x000047e6 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x000047ea pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xc4, //0x000047ee pmovmskb     %xmm4, %eax
	0x48, 0xc1, 0xe0, 0x10, //0x000047f2 shlq         $16, %rax
	0x48, 0x09, 0xd0, //0x000047f6 orq          %rdx, %rax
	0x66, 0x0f, 0x74, 0xd1, //0x000047f9 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x000047fd pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x74, 0xd9, //0x00004801 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x00004805 pmovmskb     %xmm3, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x00004809 shlq         $16, %rdx
	0x48, 0x09, 0xca, //0x0000480d orq          %rcx, %rdx
	0x48, 0x89, 0xd1, //0x00004810 movq         %rdx, %rcx
	0x4c, 0x09, 0xd9, //0x00004813 orq          %r11, %rcx
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00004816 je           LBB0_895
	0x44, 0x89, 0xd9, //0x0000481c movl         %r11d, %ecx
	0xf7, 0xd1, //0x0000481f notl         %ecx
	0x21, 0xd1, //0x00004821 andl         %edx, %ecx
	0x8d, 0x3c, 0x09, //0x00004823 leal         (%rcx,%rcx), %edi
	0x44, 0x09, 0xdf, //0x00004826 orl          %r11d, %edi
	0x89, 0xfb, //0x00004829 movl         %edi, %ebx
	0xf7, 0xd3, //0x0000482b notl         %ebx
	0x21, 0xd3, //0x0000482d andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000482f andl         $-1431655766, %ebx
	0x45, 0x31, 0xdb, //0x00004835 xorl         %r11d, %r11d
	0x01, 0xcb, //0x00004838 addl         %ecx, %ebx
	0x41, 0x0f, 0x92, 0xc3, //0x0000483a setb         %r11b
	0x01, 0xdb, //0x0000483e addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00004840 xorl         $1431655765, %ebx
	0x21, 0xfb, //0x00004846 andl         %edi, %ebx
	0xf7, 0xd3, //0x00004848 notl         %ebx
	0x21, 0xd8, //0x0000484a andl         %ebx, %eax
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x0000484c jmp          LBB0_896
	//0x00004851 LBB0_895
	0x45, 0x31, 0xdb, //0x00004851 xorl         %r11d, %r11d
	//0x00004854 LBB0_896
	0x48, 0x85, 0xc0, //0x00004854 testq        %rax, %rax
	0x0f, 0x85, 0x28, 0x09, 0x00, 0x00, //0x00004857 jne          LBB0_957
	0x49, 0x83, 0xc6, 0x20, //0x0000485d addq         $32, %r14
	0x49, 0x8d, 0x04, 0x30, //0x00004861 leaq         (%r8,%rsi), %rax
	0x48, 0x83, 0xc0, 0xe0, //0x00004865 addq         $-32, %rax
	0x48, 0x83, 0xc6, 0xe0, //0x00004869 addq         $-32, %rsi
	0x48, 0x83, 0xf8, 0x3f, //0x0000486d cmpq         $63, %rax
	0x0f, 0x8f, 0x55, 0xff, 0xff, 0xff, //0x00004871 jg           LBB0_893
	0x4d, 0x85, 0xdb, //0x00004877 testq        %r11, %r11
	0x0f, 0x85, 0x38, 0x0b, 0x00, 0x00, //0x0000487a jne          LBB0_989
	0x4b, 0x8d, 0x14, 0x0e, //0x00004880 leaq         (%r14,%r9), %rdx
	0x48, 0x83, 0xc2, 0x01, //0x00004884 addq         $1, %rdx
	0x49, 0xf7, 0xd6, //0x00004888 notq         %r14
	0x4d, 0x01, 0xc6, //0x0000488b addq         %r8, %r14
	//0x0000488e LBB0_900
	0x4d, 0x85, 0xf6, //0x0000488e testq        %r14, %r14
	0x0f, 0x8e, 0x4c, 0xfd, 0xff, 0xff, //0x00004891 jle          LBB0_866
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00004897 movq         $-1, %r12
	0xe9, 0x16, 0x09, 0x00, 0x00, //0x0000489e jmp          LBB0_960
	//0x000048a3 LBB0_902
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000048a3 movabsq      $6148914691236517205, %r15
	0x48, 0x8b, 0x47, 0x08, //0x000048ad movq         $8(%rdi), %rax
	0x48, 0x29, 0xd0, //0x000048b1 subq         %rdx, %rax
	0x49, 0x01, 0xd5, //0x000048b4 addq         %rdx, %r13
	0x31, 0xdb, //0x000048b7 xorl         %ebx, %ebx
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0x7e, 0xb7, 0xff, 0xff, //0x000048b9 movdqu       $-18562(%rip), %xmm10  /* LCPI0_4+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x66, 0xb7, 0xff, 0xff, //0x000048c2 movdqu       $-18586(%rip), %xmm1  /* LCPI0_3+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xc9, //0x000048ca pcmpeqd      %xmm9, %xmm9
	0xf3, 0x0f, 0x6f, 0x1d, 0x99, 0xb7, 0xff, 0xff, //0x000048cf movdqu       $-18535(%rip), %xmm3  /* LCPI0_7+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0x41, 0xb7, 0xff, 0xff, //0x000048d7 movdqu       $-18623(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0x49, 0xb8, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x000048df movabsq      $3689348814741910323, %r8
	0x66, 0x45, 0x0f, 0xef, 0xc0, //0x000048e9 pxor         %xmm8, %xmm8
	0x31, 0xc9, //0x000048ee xorl         %ecx, %ecx
	0x45, 0x31, 0xdb, //0x000048f0 xorl         %r11d, %r11d
	0x45, 0x31, 0xc9, //0x000048f3 xorl         %r9d, %r9d
	0x48, 0x83, 0xf8, 0x40, //0x000048f6 cmpq         $64, %rax
	0x48, 0x89, 0x45, 0xc0, //0x000048fa movq         %rax, $-64(%rbp)
	0x0f, 0x8d, 0x56, 0x01, 0x00, 0x00, //0x000048fe jge          LBB0_903
	//0x00004904 LBB0_912
	0x48, 0x85, 0xc0, //0x00004904 testq        %rax, %rax
	0x0f, 0x8e, 0x8e, 0x0a, 0x00, 0x00, //0x00004907 jle          LBB0_986
	0xf3, 0x44, 0x0f, 0x7f, 0x45, 0x80, //0x0000490d movdqu       %xmm8, $-128(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00004913 movdqu       %xmm8, $-144(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x0000491c movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00004925 movdqu       %xmm8, $-176(%rbp)
	0x44, 0x89, 0xe8, //0x0000492e movl         %r13d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00004931 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00004936 cmpl         $4033, %eax
	0x0f, 0x82, 0x3d, 0x00, 0x00, 0x00, //0x0000493b jb           LBB0_916
	0x48, 0x83, 0x7d, 0xc0, 0x20, //0x00004941 cmpq         $32, $-64(%rbp)
	0x0f, 0x82, 0x41, 0x00, 0x00, 0x00, //0x00004946 jb           LBB0_917
	0x41, 0x0f, 0x10, 0x45, 0x00, //0x0000494c movups       (%r13), %xmm0
	0x0f, 0x11, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00004951 movups       %xmm0, $-176(%rbp)
	0xf3, 0x41, 0x0f, 0x6f, 0x45, 0x10, //0x00004958 movdqu       $16(%r13), %xmm0
	0xf3, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x0000495e movdqu       %xmm0, $-160(%rbp)
	0x49, 0x83, 0xc5, 0x20, //0x00004966 addq         $32, %r13
	0x48, 0x8b, 0x45, 0xc0, //0x0000496a movq         $-64(%rbp), %rax
	0x48, 0x8d, 0x78, 0xe0, //0x0000496e leaq         $-32(%rax), %rdi
	0x48, 0x8d, 0xb5, 0x70, 0xff, 0xff, 0xff, //0x00004972 leaq         $-144(%rbp), %rsi
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00004979 jmp          LBB0_918
	//0x0000497e LBB0_916
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000497e movabsq      $6148914691236517205, %r15
	0xe9, 0xcd, 0x00, 0x00, 0x00, //0x00004988 jmp          LBB0_903
	//0x0000498d LBB0_917
	0x48, 0x8d, 0xb5, 0x50, 0xff, 0xff, 0xff, //0x0000498d leaq         $-176(%rbp), %rsi
	0x48, 0x8b, 0x7d, 0xc0, //0x00004994 movq         $-64(%rbp), %rdi
	//0x00004998 LBB0_918
	0x48, 0x83, 0xff, 0x10, //0x00004998 cmpq         $16, %rdi
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x0000499c jb           LBB0_919
	0xf3, 0x41, 0x0f, 0x6f, 0x45, 0x00, //0x000049a2 movdqu       (%r13), %xmm0
	0xf3, 0x0f, 0x7f, 0x06, //0x000049a8 movdqu       %xmm0, (%rsi)
	0x49, 0x83, 0xc5, 0x10, //0x000049ac addq         $16, %r13
	0x48, 0x83, 0xc6, 0x10, //0x000049b0 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x000049b4 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x000049b8 cmpq         $8, %rdi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x000049bc jae          LBB0_926
	//0x000049c2 LBB0_920
	0x48, 0x83, 0xff, 0x04, //0x000049c2 cmpq         $4, %rdi
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x000049c6 jb           LBB0_921
	//0x000049cc LBB0_927
	0x41, 0x8b, 0x45, 0x00, //0x000049cc movl         (%r13), %eax
	0x89, 0x06, //0x000049d0 movl         %eax, (%rsi)
	0x49, 0x83, 0xc5, 0x04, //0x000049d2 addq         $4, %r13
	0x48, 0x83, 0xc6, 0x04, //0x000049d6 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x000049da addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x000049de cmpq         $2, %rdi
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x000049e2 jae          LBB0_922
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x000049e8 jmp          LBB0_923
	//0x000049ed LBB0_919
	0x48, 0x83, 0xff, 0x08, //0x000049ed cmpq         $8, %rdi
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x000049f1 jb           LBB0_920
	//0x000049f7 LBB0_926
	0x49, 0x8b, 0x45, 0x00, //0x000049f7 movq         (%r13), %rax
	0x48, 0x89, 0x06, //0x000049fb movq         %rax, (%rsi)
	0x49, 0x83, 0xc5, 0x08, //0x000049fe addq         $8, %r13
	0x48, 0x83, 0xc6, 0x08, //0x00004a02 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00004a06 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00004a0a cmpq         $4, %rdi
	0x0f, 0x83, 0xb8, 0xff, 0xff, 0xff, //0x00004a0e jae          LBB0_927
	//0x00004a14 LBB0_921
	0x48, 0x83, 0xff, 0x02, //0x00004a14 cmpq         $2, %rdi
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00004a18 jb           LBB0_923
	//0x00004a1e LBB0_922
	0x41, 0x0f, 0xb7, 0x45, 0x00, //0x00004a1e movzwl       (%r13), %eax
	0x66, 0x89, 0x06, //0x00004a23 movw         %ax, (%rsi)
	0x49, 0x83, 0xc5, 0x02, //0x00004a26 addq         $2, %r13
	0x48, 0x83, 0xc6, 0x02, //0x00004a2a addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00004a2e addq         $-2, %rdi
	//0x00004a32 LBB0_923
	0x4c, 0x89, 0xe8, //0x00004a32 movq         %r13, %rax
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x00004a35 leaq         $-176(%rbp), %r13
	0x48, 0x85, 0xff, //0x00004a3c testq        %rdi, %rdi
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00004a3f movabsq      $6148914691236517205, %r15
	0x0f, 0x84, 0x0b, 0x00, 0x00, 0x00, //0x00004a49 je           LBB0_903
	0x8a, 0x00, //0x00004a4f movb         (%rax), %al
	0x88, 0x06, //0x00004a51 movb         %al, (%rsi)
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x00004a53 leaq         $-176(%rbp), %r13
	//0x00004a5a LBB0_903
	0xf3, 0x41, 0x0f, 0x6f, 0x45, 0x00, //0x00004a5a movdqu       (%r13), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x6d, 0x10, //0x00004a60 movdqu       $16(%r13), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x7d, 0x20, //0x00004a66 movdqu       $32(%r13), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x75, 0x30, //0x00004a6c movdqu       $48(%r13), %xmm6
	0x66, 0x0f, 0x6f, 0xd0, //0x00004a72 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00004a76 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00004a7b pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x6f, 0xd5, //0x00004a7f movdqa       %xmm5, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00004a83 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00004a88 pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x6f, 0xd7, //0x00004a8c movdqa       %xmm7, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00004a90 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00004a95 pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd6, //0x00004a99 movdqa       %xmm6, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00004a9d pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00004aa2 pmovmskb     %xmm2, %edx
	0x48, 0xc1, 0xe2, 0x30, //0x00004aa6 shlq         $48, %rdx
	0x48, 0xc1, 0xe7, 0x20, //0x00004aaa shlq         $32, %rdi
	0x48, 0x09, 0xd7, //0x00004aae orq          %rdx, %rdi
	0x48, 0xc1, 0xe0, 0x10, //0x00004ab1 shlq         $16, %rax
	0x48, 0x09, 0xf8, //0x00004ab5 orq          %rdi, %rax
	0x48, 0x09, 0xc6, //0x00004ab8 orq          %rax, %rsi
	0x48, 0x89, 0xf0, //0x00004abb movq         %rsi, %rax
	0x48, 0x09, 0xc8, //0x00004abe orq          %rcx, %rax
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00004ac1 jne          LBB0_905
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00004ac7 movq         $-1, %rsi
	0x31, 0xc9, //0x00004ace xorl         %ecx, %ecx
	0xe9, 0x40, 0x00, 0x00, 0x00, //0x00004ad0 jmp          LBB0_906
	//0x00004ad5 LBB0_905
	0x48, 0x89, 0xc8, //0x00004ad5 movq         %rcx, %rax
	0x48, 0xf7, 0xd0, //0x00004ad8 notq         %rax
	0x48, 0x21, 0xf0, //0x00004adb andq         %rsi, %rax
	0x48, 0x8d, 0x14, 0x00, //0x00004ade leaq         (%rax,%rax), %rdx
	0x48, 0x09, 0xca, //0x00004ae2 orq          %rcx, %rdx
	0x48, 0x89, 0xd7, //0x00004ae5 movq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x00004ae8 notq         %rdi
	0x48, 0x89, 0xd9, //0x00004aeb movq         %rbx, %rcx
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004aee movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xde, //0x00004af8 andq         %rbx, %rsi
	0x48, 0x89, 0xcb, //0x00004afb movq         %rcx, %rbx
	0x48, 0x21, 0xfe, //0x00004afe andq         %rdi, %rsi
	0x31, 0xc9, //0x00004b01 xorl         %ecx, %ecx
	0x48, 0x01, 0xc6, //0x00004b03 addq         %rax, %rsi
	0x0f, 0x92, 0xc1, //0x00004b06 setb         %cl
	0x48, 0x01, 0xf6, //0x00004b09 addq         %rsi, %rsi
	0x4c, 0x31, 0xfe, //0x00004b0c xorq         %r15, %rsi
	0x48, 0x21, 0xd6, //0x00004b0f andq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00004b12 notq         %rsi
	//0x00004b15 LBB0_906
	0x48, 0x89, 0x4d, 0xa0, //0x00004b15 movq         %rcx, $-96(%rbp)
	0x66, 0x0f, 0x6f, 0xd6, //0x00004b19 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00004b1d pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00004b21 pmovmskb     %xmm2, %eax
	0x48, 0xc1, 0xe0, 0x30, //0x00004b25 shlq         $48, %rax
	0x66, 0x0f, 0x6f, 0xd7, //0x00004b29 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00004b2d pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00004b31 pmovmskb     %xmm2, %edx
	0x48, 0xc1, 0xe2, 0x20, //0x00004b35 shlq         $32, %rdx
	0x48, 0x09, 0xc2, //0x00004b39 orq          %rax, %rdx
	0x66, 0x0f, 0x6f, 0xd5, //0x00004b3c movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00004b40 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00004b44 pmovmskb     %xmm2, %eax
	0x48, 0xc1, 0xe0, 0x10, //0x00004b48 shlq         $16, %rax
	0x48, 0x09, 0xd0, //0x00004b4c orq          %rdx, %rax
	0x66, 0x0f, 0x6f, 0xd0, //0x00004b4f movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00004b53 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00004b57 pmovmskb     %xmm2, %edx
	0x48, 0x09, 0xc2, //0x00004b5b orq          %rax, %rdx
	0x48, 0x21, 0xf2, //0x00004b5e andq         %rsi, %rdx
	0x66, 0x48, 0x0f, 0x6e, 0xd2, //0x00004b61 movq         %rdx, %xmm2
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd1, 0x00, //0x00004b66 pclmulqdq    $0, %xmm9, %xmm2
	0x66, 0x49, 0x0f, 0x7e, 0xd6, //0x00004b6d movq         %xmm2, %r14
	0x49, 0x31, 0xde, //0x00004b72 xorq         %rbx, %r14
	0x66, 0x0f, 0x6f, 0xd0, //0x00004b75 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00004b79 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00004b7d pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd5, //0x00004b81 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00004b85 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00004b89 pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x6f, 0xd7, //0x00004b8d movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00004b91 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00004b95 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x6f, 0xd6, //0x00004b99 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00004b9d pcmpeqb      %xmm3, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xfa, //0x00004ba1 pmovmskb     %xmm2, %r15d
	0x49, 0xc1, 0xe7, 0x30, //0x00004ba6 shlq         $48, %r15
	0x48, 0xc1, 0xe6, 0x20, //0x00004baa shlq         $32, %rsi
	0x4c, 0x09, 0xfe, //0x00004bae orq          %r15, %rsi
	0x48, 0xc1, 0xe0, 0x10, //0x00004bb1 shlq         $16, %rax
	0x48, 0x09, 0xf0, //0x00004bb5 orq          %rsi, %rax
	0x48, 0x09, 0xc2, //0x00004bb8 orq          %rax, %rdx
	0x4d, 0x89, 0xf7, //0x00004bbb movq         %r14, %r15
	0x49, 0xf7, 0xd7, //0x00004bbe notq         %r15
	0x4c, 0x21, 0xfa, //0x00004bc1 andq         %r15, %rdx
	0x66, 0x0f, 0x74, 0xc4, //0x00004bc4 pcmpeqb      %xmm4, %xmm0
	0x66, 0x0f, 0xd7, 0xf0, //0x00004bc8 pmovmskb     %xmm0, %esi
	0x66, 0x0f, 0x74, 0xec, //0x00004bcc pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00004bd0 pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x74, 0xfc, //0x00004bd4 pcmpeqb      %xmm4, %xmm7
	0x66, 0x0f, 0xd7, 0xff, //0x00004bd8 pmovmskb     %xmm7, %edi
	0x66, 0x0f, 0x74, 0xf4, //0x00004bdc pcmpeqb      %xmm4, %xmm6
	0x66, 0x0f, 0xd7, 0xc6, //0x00004be0 pmovmskb     %xmm6, %eax
	0x48, 0xc1, 0xe0, 0x30, //0x00004be4 shlq         $48, %rax
	0x48, 0xc1, 0xe7, 0x20, //0x00004be8 shlq         $32, %rdi
	0x48, 0x09, 0xc7, //0x00004bec orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x10, //0x00004bef shlq         $16, %rbx
	0x48, 0x09, 0xfb, //0x00004bf3 orq          %rdi, %rbx
	0x48, 0x09, 0xde, //0x00004bf6 orq          %rbx, %rsi
	0x48, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00004bf9 movabsq      $1085102592571150095, %rcx
	0x4c, 0x21, 0xfe, //0x00004c03 andq         %r15, %rsi
	0x0f, 0x84, 0x6d, 0x00, 0x00, 0x00, //0x00004c06 je           LBB0_910
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00004c0c movabsq      $6148914691236517205, %r15
	//0x00004c16 LBB0_908
	0x48, 0x8d, 0x46, 0xff, //0x00004c16 leaq         $-1(%rsi), %rax
	0x48, 0x89, 0xc7, //0x00004c1a movq         %rax, %rdi
	0x48, 0x21, 0xd7, //0x00004c1d andq         %rdx, %rdi
	0x48, 0x89, 0xfb, //0x00004c20 movq         %rdi, %rbx
	0x48, 0xd1, 0xeb, //0x00004c23 shrq         %rbx
	0x4c, 0x21, 0xfb, //0x00004c26 andq         %r15, %rbx
	0x48, 0x29, 0xdf, //0x00004c29 subq         %rbx, %rdi
	0x48, 0x89, 0xfb, //0x00004c2c movq         %rdi, %rbx
	0x4c, 0x21, 0xc3, //0x00004c2f andq         %r8, %rbx
	0x48, 0xc1, 0xef, 0x02, //0x00004c32 shrq         $2, %rdi
	0x4c, 0x21, 0xc7, //0x00004c36 andq         %r8, %rdi
	0x48, 0x01, 0xdf, //0x00004c39 addq         %rbx, %rdi
	0x48, 0x89, 0xfb, //0x00004c3c movq         %rdi, %rbx
	0x48, 0xc1, 0xeb, 0x04, //0x00004c3f shrq         $4, %rbx
	0x48, 0x01, 0xfb, //0x00004c43 addq         %rdi, %rbx
	0x48, 0x21, 0xcb, //0x00004c46 andq         %rcx, %rbx
	0x48, 0xbf, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00004c49 movabsq      $72340172838076673, %rdi
	0x48, 0x0f, 0xaf, 0xdf, //0x00004c53 imulq        %rdi, %rbx
	0x48, 0xc1, 0xeb, 0x38, //0x00004c57 shrq         $56, %rbx
	0x4c, 0x01, 0xdb, //0x00004c5b addq         %r11, %rbx
	0x4c, 0x39, 0xcb, //0x00004c5e cmpq         %r9, %rbx
	0x0f, 0x86, 0xdf, 0x04, 0x00, 0x00, //0x00004c61 jbe          LBB0_956
	0x49, 0x83, 0xc1, 0x01, //0x00004c67 addq         $1, %r9
	0x48, 0x21, 0xc6, //0x00004c6b andq         %rax, %rsi
	0x0f, 0x85, 0xa2, 0xff, 0xff, 0xff, //0x00004c6e jne          LBB0_908
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00004c74 jmp          LBB0_911
	//0x00004c79 LBB0_910
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00004c79 movabsq      $6148914691236517205, %r15
	//0x00004c83 LBB0_911
	0x49, 0xc1, 0xfe, 0x3f, //0x00004c83 sarq         $63, %r14
	0x48, 0x89, 0xd0, //0x00004c87 movq         %rdx, %rax
	0x48, 0xd1, 0xe8, //0x00004c8a shrq         %rax
	0x4c, 0x21, 0xf8, //0x00004c8d andq         %r15, %rax
	0x48, 0x29, 0xc2, //0x00004c90 subq         %rax, %rdx
	0x48, 0x89, 0xd0, //0x00004c93 movq         %rdx, %rax
	0x4c, 0x21, 0xc0, //0x00004c96 andq         %r8, %rax
	0x48, 0xc1, 0xea, 0x02, //0x00004c99 shrq         $2, %rdx
	0x4c, 0x21, 0xc2, //0x00004c9d andq         %r8, %rdx
	0x48, 0x01, 0xc2, //0x00004ca0 addq         %rax, %rdx
	0x48, 0x89, 0xd0, //0x00004ca3 movq         %rdx, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x00004ca6 shrq         $4, %rax
	0x48, 0x01, 0xd0, //0x00004caa addq         %rdx, %rax
	0x48, 0x21, 0xc8, //0x00004cad andq         %rcx, %rax
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00004cb0 movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xc1, //0x00004cba imulq        %rcx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x00004cbe shrq         $56, %rax
	0x49, 0x01, 0xc3, //0x00004cc2 addq         %rax, %r11
	0x49, 0x83, 0xc5, 0x40, //0x00004cc5 addq         $64, %r13
	0x48, 0x8b, 0x45, 0xc0, //0x00004cc9 movq         $-64(%rbp), %rax
	0x48, 0x83, 0xc0, 0xc0, //0x00004ccd addq         $-64, %rax
	0x4c, 0x89, 0xf3, //0x00004cd1 movq         %r14, %rbx
	0x48, 0x8b, 0x4d, 0xa0, //0x00004cd4 movq         $-96(%rbp), %rcx
	0x48, 0x83, 0xf8, 0x40, //0x00004cd8 cmpq         $64, %rax
	0x48, 0x89, 0x45, 0xc0, //0x00004cdc movq         %rax, $-64(%rbp)
	0x0f, 0x8d, 0x74, 0xfd, 0xff, 0xff, //0x00004ce0 jge          LBB0_903
	0xe9, 0x19, 0xfc, 0xff, 0xff, //0x00004ce6 jmp          LBB0_912
	//0x00004ceb LBB0_928
	0x49, 0x8d, 0x42, 0x05, //0x00004ceb leaq         $5(%r10), %rax
	//0x00004cef LBB0_929
	0x48, 0x3b, 0x47, 0x08, //0x00004cef cmpq         $8(%rdi), %rax
	0x0f, 0x87, 0xea, 0xf8, 0xff, 0xff, //0x00004cf3 ja           LBB0_866
	0xe9, 0x94, 0x04, 0x00, 0x00, //0x00004cf9 jmp          LBB0_958
	//0x00004cfe LBB0_930
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00004cfe movabsq      $6148914691236517205, %r15
	0x48, 0x8b, 0x47, 0x08, //0x00004d08 movq         $8(%rdi), %rax
	0x48, 0x29, 0xd0, //0x00004d0c subq         %rdx, %rax
	0x49, 0x01, 0xd5, //0x00004d0f addq         %rdx, %r13
	0x31, 0xdb, //0x00004d12 xorl         %ebx, %ebx
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0x23, 0xb3, 0xff, 0xff, //0x00004d14 movdqu       $-19677(%rip), %xmm10  /* LCPI0_4+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x0b, 0xb3, 0xff, 0xff, //0x00004d1d movdqu       $-19701(%rip), %xmm1  /* LCPI0_3+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xc9, //0x00004d25 pcmpeqd      %xmm9, %xmm9
	0xf3, 0x0f, 0x6f, 0x1d, 0x1e, 0xb3, 0xff, 0xff, //0x00004d2a movdqu       $-19682(%rip), %xmm3  /* LCPI0_5+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0x26, 0xb3, 0xff, 0xff, //0x00004d32 movdqu       $-19674(%rip), %xmm4  /* LCPI0_6+0(%rip) */
	0x49, 0xb8, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00004d3a movabsq      $3689348814741910323, %r8
	0x66, 0x45, 0x0f, 0xef, 0xc0, //0x00004d44 pxor         %xmm8, %xmm8
	0x31, 0xc9, //0x00004d49 xorl         %ecx, %ecx
	0x45, 0x31, 0xdb, //0x00004d4b xorl         %r11d, %r11d
	0x45, 0x31, 0xc9, //0x00004d4e xorl         %r9d, %r9d
	0x48, 0x83, 0xf8, 0x40, //0x00004d51 cmpq         $64, %rax
	0x48, 0x89, 0x45, 0xc0, //0x00004d55 movq         %rax, $-64(%rbp)
	0x0f, 0x8d, 0x56, 0x01, 0x00, 0x00, //0x00004d59 jge          LBB0_931
	//0x00004d5f LBB0_940
	0x48, 0x85, 0xc0, //0x00004d5f testq        %rax, %rax
	0x0f, 0x8e, 0x33, 0x06, 0x00, 0x00, //0x00004d62 jle          LBB0_986
	0xf3, 0x44, 0x0f, 0x7f, 0x45, 0x80, //0x00004d68 movdqu       %xmm8, $-128(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00004d6e movdqu       %xmm8, $-144(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00004d77 movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00004d80 movdqu       %xmm8, $-176(%rbp)
	0x44, 0x89, 0xe8, //0x00004d89 movl         %r13d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00004d8c andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00004d91 cmpl         $4033, %eax
	0x0f, 0x82, 0x3d, 0x00, 0x00, 0x00, //0x00004d96 jb           LBB0_944
	0x48, 0x83, 0x7d, 0xc0, 0x20, //0x00004d9c cmpq         $32, $-64(%rbp)
	0x0f, 0x82, 0x41, 0x00, 0x00, 0x00, //0x00004da1 jb           LBB0_945
	0x41, 0x0f, 0x10, 0x45, 0x00, //0x00004da7 movups       (%r13), %xmm0
	0x0f, 0x11, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00004dac movups       %xmm0, $-176(%rbp)
	0xf3, 0x41, 0x0f, 0x6f, 0x45, 0x10, //0x00004db3 movdqu       $16(%r13), %xmm0
	0xf3, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00004db9 movdqu       %xmm0, $-160(%rbp)
	0x49, 0x83, 0xc5, 0x20, //0x00004dc1 addq         $32, %r13
	0x48, 0x8b, 0x45, 0xc0, //0x00004dc5 movq         $-64(%rbp), %rax
	0x48, 0x8d, 0x78, 0xe0, //0x00004dc9 leaq         $-32(%rax), %rdi
	0x48, 0x8d, 0xb5, 0x70, 0xff, 0xff, 0xff, //0x00004dcd leaq         $-144(%rbp), %rsi
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00004dd4 jmp          LBB0_946
	//0x00004dd9 LBB0_944
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00004dd9 movabsq      $6148914691236517205, %r15
	0xe9, 0xcd, 0x00, 0x00, 0x00, //0x00004de3 jmp          LBB0_931
	//0x00004de8 LBB0_945
	0x48, 0x8d, 0xb5, 0x50, 0xff, 0xff, 0xff, //0x00004de8 leaq         $-176(%rbp), %rsi
	0x48, 0x8b, 0x7d, 0xc0, //0x00004def movq         $-64(%rbp), %rdi
	//0x00004df3 LBB0_946
	0x48, 0x83, 0xff, 0x10, //0x00004df3 cmpq         $16, %rdi
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x00004df7 jb           LBB0_947
	0xf3, 0x41, 0x0f, 0x6f, 0x45, 0x00, //0x00004dfd movdqu       (%r13), %xmm0
	0xf3, 0x0f, 0x7f, 0x06, //0x00004e03 movdqu       %xmm0, (%rsi)
	0x49, 0x83, 0xc5, 0x10, //0x00004e07 addq         $16, %r13
	0x48, 0x83, 0xc6, 0x10, //0x00004e0b addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00004e0f addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00004e13 cmpq         $8, %rdi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00004e17 jae          LBB0_954
	//0x00004e1d LBB0_948
	0x48, 0x83, 0xff, 0x04, //0x00004e1d cmpq         $4, %rdi
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x00004e21 jb           LBB0_949
	//0x00004e27 LBB0_955
	0x41, 0x8b, 0x45, 0x00, //0x00004e27 movl         (%r13), %eax
	0x89, 0x06, //0x00004e2b movl         %eax, (%rsi)
	0x49, 0x83, 0xc5, 0x04, //0x00004e2d addq         $4, %r13
	0x48, 0x83, 0xc6, 0x04, //0x00004e31 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00004e35 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00004e39 cmpq         $2, %rdi
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x00004e3d jae          LBB0_950
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x00004e43 jmp          LBB0_951
	//0x00004e48 LBB0_947
	0x48, 0x83, 0xff, 0x08, //0x00004e48 cmpq         $8, %rdi
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x00004e4c jb           LBB0_948
	//0x00004e52 LBB0_954
	0x49, 0x8b, 0x45, 0x00, //0x00004e52 movq         (%r13), %rax
	0x48, 0x89, 0x06, //0x00004e56 movq         %rax, (%rsi)
	0x49, 0x83, 0xc5, 0x08, //0x00004e59 addq         $8, %r13
	0x48, 0x83, 0xc6, 0x08, //0x00004e5d addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00004e61 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00004e65 cmpq         $4, %rdi
	0x0f, 0x83, 0xb8, 0xff, 0xff, 0xff, //0x00004e69 jae          LBB0_955
	//0x00004e6f LBB0_949
	0x48, 0x83, 0xff, 0x02, //0x00004e6f cmpq         $2, %rdi
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00004e73 jb           LBB0_951
	//0x00004e79 LBB0_950
	0x41, 0x0f, 0xb7, 0x45, 0x00, //0x00004e79 movzwl       (%r13), %eax
	0x66, 0x89, 0x06, //0x00004e7e movw         %ax, (%rsi)
	0x49, 0x83, 0xc5, 0x02, //0x00004e81 addq         $2, %r13
	0x48, 0x83, 0xc6, 0x02, //0x00004e85 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00004e89 addq         $-2, %rdi
	//0x00004e8d LBB0_951
	0x4c, 0x89, 0xe8, //0x00004e8d movq         %r13, %rax
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x00004e90 leaq         $-176(%rbp), %r13
	0x48, 0x85, 0xff, //0x00004e97 testq        %rdi, %rdi
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00004e9a movabsq      $6148914691236517205, %r15
	0x0f, 0x84, 0x0b, 0x00, 0x00, 0x00, //0x00004ea4 je           LBB0_931
	0x8a, 0x00, //0x00004eaa movb         (%rax), %al
	0x88, 0x06, //0x00004eac movb         %al, (%rsi)
	0x4c, 0x8d, 0xad, 0x50, 0xff, 0xff, 0xff, //0x00004eae leaq         $-176(%rbp), %r13
	//0x00004eb5 LBB0_931
	0xf3, 0x41, 0x0f, 0x6f, 0x45, 0x00, //0x00004eb5 movdqu       (%r13), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x6d, 0x10, //0x00004ebb movdqu       $16(%r13), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x7d, 0x20, //0x00004ec1 movdqu       $32(%r13), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x75, 0x30, //0x00004ec7 movdqu       $48(%r13), %xmm6
	0x66, 0x0f, 0x6f, 0xd0, //0x00004ecd movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00004ed1 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00004ed6 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x6f, 0xd5, //0x00004eda movdqa       %xmm5, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00004ede pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00004ee3 pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x6f, 0xd7, //0x00004ee7 movdqa       %xmm7, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00004eeb pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00004ef0 pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd6, //0x00004ef4 movdqa       %xmm6, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00004ef8 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00004efd pmovmskb     %xmm2, %edx
	0x48, 0xc1, 0xe2, 0x30, //0x00004f01 shlq         $48, %rdx
	0x48, 0xc1, 0xe7, 0x20, //0x00004f05 shlq         $32, %rdi
	0x48, 0x09, 0xd7, //0x00004f09 orq          %rdx, %rdi
	0x48, 0xc1, 0xe0, 0x10, //0x00004f0c shlq         $16, %rax
	0x48, 0x09, 0xf8, //0x00004f10 orq          %rdi, %rax
	0x48, 0x09, 0xc6, //0x00004f13 orq          %rax, %rsi
	0x48, 0x89, 0xf0, //0x00004f16 movq         %rsi, %rax
	0x48, 0x09, 0xc8, //0x00004f19 orq          %rcx, %rax
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00004f1c jne          LBB0_933
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00004f22 movq         $-1, %rsi
	0x31, 0xc9, //0x00004f29 xorl         %ecx, %ecx
	0xe9, 0x40, 0x00, 0x00, 0x00, //0x00004f2b jmp          LBB0_934
	//0x00004f30 LBB0_933
	0x48, 0x89, 0xc8, //0x00004f30 movq         %rcx, %rax
	0x48, 0xf7, 0xd0, //0x00004f33 notq         %rax
	0x48, 0x21, 0xf0, //0x00004f36 andq         %rsi, %rax
	0x48, 0x8d, 0x14, 0x00, //0x00004f39 leaq         (%rax,%rax), %rdx
	0x48, 0x09, 0xca, //0x00004f3d orq          %rcx, %rdx
	0x48, 0x89, 0xd7, //0x00004f40 movq         %rdx, %rdi
	0x48, 0xf7, 0xd7, //0x00004f43 notq         %rdi
	0x48, 0x89, 0xd9, //0x00004f46 movq         %rbx, %rcx
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004f49 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xde, //0x00004f53 andq         %rbx, %rsi
	0x48, 0x89, 0xcb, //0x00004f56 movq         %rcx, %rbx
	0x48, 0x21, 0xfe, //0x00004f59 andq         %rdi, %rsi
	0x31, 0xc9, //0x00004f5c xorl         %ecx, %ecx
	0x48, 0x01, 0xc6, //0x00004f5e addq         %rax, %rsi
	0x0f, 0x92, 0xc1, //0x00004f61 setb         %cl
	0x48, 0x01, 0xf6, //0x00004f64 addq         %rsi, %rsi
	0x4c, 0x31, 0xfe, //0x00004f67 xorq         %r15, %rsi
	0x48, 0x21, 0xd6, //0x00004f6a andq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00004f6d notq         %rsi
	//0x00004f70 LBB0_934
	0x48, 0x89, 0x4d, 0xa0, //0x00004f70 movq         %rcx, $-96(%rbp)
	0x66, 0x0f, 0x6f, 0xd6, //0x00004f74 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00004f78 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00004f7c pmovmskb     %xmm2, %eax
	0x48, 0xc1, 0xe0, 0x30, //0x00004f80 shlq         $48, %rax
	0x66, 0x0f, 0x6f, 0xd7, //0x00004f84 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00004f88 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00004f8c pmovmskb     %xmm2, %edx
	0x48, 0xc1, 0xe2, 0x20, //0x00004f90 shlq         $32, %rdx
	0x48, 0x09, 0xc2, //0x00004f94 orq          %rax, %rdx
	0x66, 0x0f, 0x6f, 0xd5, //0x00004f97 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00004f9b pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00004f9f pmovmskb     %xmm2, %eax
	0x48, 0xc1, 0xe0, 0x10, //0x00004fa3 shlq         $16, %rax
	0x48, 0x09, 0xd0, //0x00004fa7 orq          %rdx, %rax
	0x66, 0x0f, 0x6f, 0xd0, //0x00004faa movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00004fae pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00004fb2 pmovmskb     %xmm2, %edx
	0x48, 0x09, 0xc2, //0x00004fb6 orq          %rax, %rdx
	0x48, 0x21, 0xf2, //0x00004fb9 andq         %rsi, %rdx
	0x66, 0x48, 0x0f, 0x6e, 0xd2, //0x00004fbc movq         %rdx, %xmm2
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd1, 0x00, //0x00004fc1 pclmulqdq    $0, %xmm9, %xmm2
	0x66, 0x49, 0x0f, 0x7e, 0xd6, //0x00004fc8 movq         %xmm2, %r14
	0x49, 0x31, 0xde, //0x00004fcd xorq         %rbx, %r14
	0x66, 0x0f, 0x6f, 0xd0, //0x00004fd0 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00004fd4 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00004fd8 pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd5, //0x00004fdc movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00004fe0 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00004fe4 pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x6f, 0xd7, //0x00004fe8 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00004fec pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00004ff0 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x6f, 0xd6, //0x00004ff4 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00004ff8 pcmpeqb      %xmm3, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xfa, //0x00004ffc pmovmskb     %xmm2, %r15d
	0x49, 0xc1, 0xe7, 0x30, //0x00005001 shlq         $48, %r15
	0x48, 0xc1, 0xe6, 0x20, //0x00005005 shlq         $32, %rsi
	0x4c, 0x09, 0xfe, //0x00005009 orq          %r15, %rsi
	0x48, 0xc1, 0xe0, 0x10, //0x0000500c shlq         $16, %rax
	0x48, 0x09, 0xf0, //0x00005010 orq          %rsi, %rax
	0x48, 0x09, 0xc2, //0x00005013 orq          %rax, %rdx
	0x4d, 0x89, 0xf7, //0x00005016 movq         %r14, %r15
	0x49, 0xf7, 0xd7, //0x00005019 notq         %r15
	0x4c, 0x21, 0xfa, //0x0000501c andq         %r15, %rdx
	0x66, 0x0f, 0x74, 0xc4, //0x0000501f pcmpeqb      %xmm4, %xmm0
	0x66, 0x0f, 0xd7, 0xf0, //0x00005023 pmovmskb     %xmm0, %esi
	0x66, 0x0f, 0x74, 0xec, //0x00005027 pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x0000502b pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x74, 0xfc, //0x0000502f pcmpeqb      %xmm4, %xmm7
	0x66, 0x0f, 0xd7, 0xff, //0x00005033 pmovmskb     %xmm7, %edi
	0x66, 0x0f, 0x74, 0xf4, //0x00005037 pcmpeqb      %xmm4, %xmm6
	0x66, 0x0f, 0xd7, 0xc6, //0x0000503b pmovmskb     %xmm6, %eax
	0x48, 0xc1, 0xe0, 0x30, //0x0000503f shlq         $48, %rax
	0x48, 0xc1, 0xe7, 0x20, //0x00005043 shlq         $32, %rdi
	0x48, 0x09, 0xc7, //0x00005047 orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x10, //0x0000504a shlq         $16, %rbx
	0x48, 0x09, 0xfb, //0x0000504e orq          %rdi, %rbx
	0x48, 0x09, 0xde, //0x00005051 orq          %rbx, %rsi
	0x48, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00005054 movabsq      $1085102592571150095, %rcx
	0x4c, 0x21, 0xfe, //0x0000505e andq         %r15, %rsi
	0x0f, 0x84, 0x6d, 0x00, 0x00, 0x00, //0x00005061 je           LBB0_938
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00005067 movabsq      $6148914691236517205, %r15
	//0x00005071 LBB0_936
	0x48, 0x8d, 0x46, 0xff, //0x00005071 leaq         $-1(%rsi), %rax
	0x48, 0x89, 0xc7, //0x00005075 movq         %rax, %rdi
	0x48, 0x21, 0xd7, //0x00005078 andq         %rdx, %rdi
	0x48, 0x89, 0xfb, //0x0000507b movq         %rdi, %rbx
	0x48, 0xd1, 0xeb, //0x0000507e shrq         %rbx
	0x4c, 0x21, 0xfb, //0x00005081 andq         %r15, %rbx
	0x48, 0x29, 0xdf, //0x00005084 subq         %rbx, %rdi
	0x48, 0x89, 0xfb, //0x00005087 movq         %rdi, %rbx
	0x4c, 0x21, 0xc3, //0x0000508a andq         %r8, %rbx
	0x48, 0xc1, 0xef, 0x02, //0x0000508d shrq         $2, %rdi
	0x4c, 0x21, 0xc7, //0x00005091 andq         %r8, %rdi
	0x48, 0x01, 0xdf, //0x00005094 addq         %rbx, %rdi
	0x48, 0x89, 0xfb, //0x00005097 movq         %rdi, %rbx
	0x48, 0xc1, 0xeb, 0x04, //0x0000509a shrq         $4, %rbx
	0x48, 0x01, 0xfb, //0x0000509e addq         %rdi, %rbx
	0x48, 0x21, 0xcb, //0x000050a1 andq         %rcx, %rbx
	0x48, 0xbf, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000050a4 movabsq      $72340172838076673, %rdi
	0x48, 0x0f, 0xaf, 0xdf, //0x000050ae imulq        %rdi, %rbx
	0x48, 0xc1, 0xeb, 0x38, //0x000050b2 shrq         $56, %rbx
	0x4c, 0x01, 0xdb, //0x000050b6 addq         %r11, %rbx
	0x4c, 0x39, 0xcb, //0x000050b9 cmpq         %r9, %rbx
	0x0f, 0x86, 0x84, 0x00, 0x00, 0x00, //0x000050bc jbe          LBB0_956
	0x49, 0x83, 0xc1, 0x01, //0x000050c2 addq         $1, %r9
	0x48, 0x21, 0xc6, //0x000050c6 andq         %rax, %rsi
	0x0f, 0x85, 0xa2, 0xff, 0xff, 0xff, //0x000050c9 jne          LBB0_936
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x000050cf jmp          LBB0_939
	//0x000050d4 LBB0_938
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000050d4 movabsq      $6148914691236517205, %r15
	//0x000050de LBB0_939
	0x49, 0xc1, 0xfe, 0x3f, //0x000050de sarq         $63, %r14
	0x48, 0x89, 0xd0, //0x000050e2 movq         %rdx, %rax
	0x48, 0xd1, 0xe8, //0x000050e5 shrq         %rax
	0x4c, 0x21, 0xf8, //0x000050e8 andq         %r15, %rax
	0x48, 0x29, 0xc2, //0x000050eb subq         %rax, %rdx
	0x48, 0x89, 0xd0, //0x000050ee movq         %rdx, %rax
	0x4c, 0x21, 0xc0, //0x000050f1 andq         %r8, %rax
	0x48, 0xc1, 0xea, 0x02, //0x000050f4 shrq         $2, %rdx
	0x4c, 0x21, 0xc2, //0x000050f8 andq         %r8, %rdx
	0x48, 0x01, 0xc2, //0x000050fb addq         %rax, %rdx
	0x48, 0x89, 0xd0, //0x000050fe movq         %rdx, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x00005101 shrq         $4, %rax
	0x48, 0x01, 0xd0, //0x00005105 addq         %rdx, %rax
	0x48, 0x21, 0xc8, //0x00005108 andq         %rcx, %rax
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x0000510b movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xc1, //0x00005115 imulq        %rcx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x00005119 shrq         $56, %rax
	0x49, 0x01, 0xc3, //0x0000511d addq         %rax, %r11
	0x49, 0x83, 0xc5, 0x40, //0x00005120 addq         $64, %r13
	0x48, 0x8b, 0x45, 0xc0, //0x00005124 movq         $-64(%rbp), %rax
	0x48, 0x83, 0xc0, 0xc0, //0x00005128 addq         $-64, %rax
	0x4c, 0x89, 0xf3, //0x0000512c movq         %r14, %rbx
	0x48, 0x8b, 0x4d, 0xa0, //0x0000512f movq         $-96(%rbp), %rcx
	0x48, 0x83, 0xf8, 0x40, //0x00005133 cmpq         $64, %rax
	0x48, 0x89, 0x45, 0xc0, //0x00005137 movq         %rax, $-64(%rbp)
	0x0f, 0x8d, 0x74, 0xfd, 0xff, 0xff, //0x0000513b jge          LBB0_931
	0xe9, 0x19, 0xfc, 0xff, 0xff, //0x00005141 jmp          LBB0_940
	//0x00005146 LBB0_956
	0x48, 0x8b, 0x7d, 0xb8, //0x00005146 movq         $-72(%rbp), %rdi
	0x48, 0x8b, 0x47, 0x08, //0x0000514a movq         $8(%rdi), %rax
	0x48, 0x0f, 0xbc, 0xce, //0x0000514e bsfq         %rsi, %rcx
	0x48, 0x2b, 0x4d, 0xc0, //0x00005152 subq         $-64(%rbp), %rcx
	0x48, 0x01, 0xc8, //0x00005156 addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00005159 addq         $1, %rax
	0x48, 0x8b, 0x55, 0xd0, //0x0000515d movq         $-48(%rbp), %rdx
	0x48, 0x89, 0x02, //0x00005161 movq         %rax, (%rdx)
	0x48, 0x8b, 0x4f, 0x08, //0x00005164 movq         $8(%rdi), %rcx
	0x48, 0x39, 0xc8, //0x00005168 cmpq         %rcx, %rax
	0x48, 0x0f, 0x47, 0xc1, //0x0000516b cmovaq       %rcx, %rax
	0x48, 0x89, 0x02, //0x0000516f movq         %rax, (%rdx)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00005172 movq         $-1, %rax
	0x4c, 0x0f, 0x47, 0xd0, //0x00005179 cmovaq       %rax, %r10
	0x4d, 0x89, 0xd4, //0x0000517d movq         %r10, %r12
	0xe9, 0x5e, 0xf4, 0xff, 0xff, //0x00005180 jmp          LBB0_866
	//0x00005185 LBB0_957
	0x0f, 0xbc, 0xc0, //0x00005185 bsfl         %eax, %eax
	0x4c, 0x01, 0xd0, //0x00005188 addq         %r10, %rax
	0x4c, 0x01, 0xf0, //0x0000518b addq         %r14, %rax
	0x48, 0x83, 0xc0, 0x02, //0x0000518e addq         $2, %rax
	//0x00005192 LBB0_958
	0x48, 0x8b, 0x4d, 0xd0, //0x00005192 movq         $-48(%rbp), %rcx
	0x48, 0x89, 0x01, //0x00005196 movq         %rax, (%rcx)
	0x4d, 0x89, 0xd4, //0x00005199 movq         %r10, %r12
	0xe9, 0x42, 0xf4, 0xff, 0xff, //0x0000519c jmp          LBB0_866
	//0x000051a1 LBB0_959
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000051a1 movq         $-2, %rax
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x000051a8 movl         $2, %esi
	0x48, 0x01, 0xf2, //0x000051ad addq         %rsi, %rdx
	0x49, 0x01, 0xc6, //0x000051b0 addq         %rax, %r14
	0x0f, 0x8e, 0x2a, 0xf4, 0xff, 0xff, //0x000051b3 jle          LBB0_866
	//0x000051b9 LBB0_960
	0x0f, 0xb6, 0x02, //0x000051b9 movzbl       (%rdx), %eax
	0x3c, 0x5c, //0x000051bc cmpb         $92, %al
	0x0f, 0x84, 0xdd, 0xff, 0xff, 0xff, //0x000051be je           LBB0_959
	0x3c, 0x22, //0x000051c4 cmpb         $34, %al
	0x0f, 0x84, 0x6e, 0x01, 0x00, 0x00, //0x000051c6 je           LBB0_974
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000051cc movq         $-1, %rax
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x000051d3 movl         $1, %esi
	0x48, 0x01, 0xf2, //0x000051d8 addq         %rsi, %rdx
	0x49, 0x01, 0xc6, //0x000051db addq         %rax, %r14
	0x0f, 0x8f, 0xd5, 0xff, 0xff, 0xff, //0x000051de jg           LBB0_960
	0xe9, 0xfa, 0xf3, 0xff, 0xff, //0x000051e4 jmp          LBB0_866
	//0x000051e9 LBB0_963
	0x4c, 0x89, 0x55, 0x98, //0x000051e9 movq         %r10, $-104(%rbp)
	//0x000051ed LBB0_964
	0x48, 0x8b, 0x45, 0x98, //0x000051ed movq         $-104(%rbp), %rax
	0x49, 0x89, 0x03, //0x000051f1 movq         %rax, (%r11)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000051f4 movq         $-1, %r12
	0xe9, 0xe3, 0xf3, 0xff, 0xff, //0x000051fb jmp          LBB0_866
	//0x00005200 LBB0_965
	0x4c, 0x29, 0xea, //0x00005200 subq         %r13, %rdx
	0x48, 0x01, 0xc2, //0x00005203 addq         %rax, %rdx
	0x49, 0x89, 0x10, //0x00005206 movq         %rdx, (%r8)
	0x4d, 0x89, 0xd4, //0x00005209 movq         %r10, %r12
	0xe9, 0xd2, 0xf3, 0xff, 0xff, //0x0000520c jmp          LBB0_866
	//0x00005211 LBB0_966
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00005211 movq         $-1, %r15
	//0x00005218 LBB0_967
	0x4d, 0x29, 0xfb, //0x00005218 subq         %r15, %r11
	0xe9, 0x5e, 0xf5, 0xff, 0xff, //0x0000521b jmp          LBB0_889
	//0x00005220 LBB0_969
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x00005220 movq         $-2, %r12
	0x3c, 0x61, //0x00005227 cmpb         $97, %al
	0x0f, 0x85, 0xb4, 0xf3, 0xff, 0xff, //0x00005229 jne          LBB0_866
	0x49, 0x8d, 0x43, 0x02, //0x0000522f leaq         $2(%r11), %rax
	0x48, 0x8b, 0x4d, 0xd0, //0x00005233 movq         $-48(%rbp), %rcx
	0x48, 0x89, 0x01, //0x00005237 movq         %rax, (%rcx)
	0x43, 0x80, 0x7c, 0x1e, 0x02, 0x6c, //0x0000523a cmpb         $108, $2(%r14,%r11)
	0x0f, 0x85, 0x9d, 0xf3, 0xff, 0xff, //0x00005240 jne          LBB0_866
	0x49, 0x8d, 0x43, 0x03, //0x00005246 leaq         $3(%r11), %rax
	0x48, 0x89, 0x01, //0x0000524a movq         %rax, (%rcx)
	0x43, 0x80, 0x7c, 0x1e, 0x03, 0x73, //0x0000524d cmpb         $115, $3(%r14,%r11)
	0x0f, 0x85, 0x8a, 0xf3, 0xff, 0xff, //0x00005253 jne          LBB0_866
	0x49, 0x8d, 0x43, 0x04, //0x00005259 leaq         $4(%r11), %rax
	0x48, 0x89, 0x01, //0x0000525d movq         %rax, (%rcx)
	0x43, 0x80, 0x7c, 0x1e, 0x04, 0x65, //0x00005260 cmpb         $101, $4(%r14,%r11)
	0x0f, 0x85, 0x77, 0xf3, 0xff, 0xff, //0x00005266 jne          LBB0_866
	0x49, 0x83, 0xc3, 0x05, //0x0000526c addq         $5, %r11
	0xe9, 0xfd, 0x00, 0x00, 0x00, //0x00005270 jmp          LBB0_983
	//0x00005275 LBB0_755
	0x48, 0x8b, 0x4d, 0xd0, //0x00005275 movq         $-48(%rbp), %rcx
	0x4c, 0x89, 0x19, //0x00005279 movq         %r11, (%rcx)
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x0000527c movq         $-2, %r12
	0x41, 0x80, 0x38, 0x6e, //0x00005283 cmpb         $110, (%r8)
	0x0f, 0x85, 0x56, 0xf3, 0xff, 0xff, //0x00005287 jne          LBB0_866
	0x49, 0x8d, 0x43, 0x01, //0x0000528d leaq         $1(%r11), %rax
	0x48, 0x89, 0x01, //0x00005291 movq         %rax, (%rcx)
	0x43, 0x80, 0x7c, 0x1e, 0x01, 0x75, //0x00005294 cmpb         $117, $1(%r14,%r11)
	0x0f, 0x85, 0x43, 0xf3, 0xff, 0xff, //0x0000529a jne          LBB0_866
	0x49, 0x8d, 0x43, 0x02, //0x000052a0 leaq         $2(%r11), %rax
	0x48, 0x89, 0x01, //0x000052a4 movq         %rax, (%rcx)
	0x43, 0x80, 0x7c, 0x1e, 0x02, 0x6c, //0x000052a7 cmpb         $108, $2(%r14,%r11)
	0x0f, 0x85, 0x30, 0xf3, 0xff, 0xff, //0x000052ad jne          LBB0_866
	0x49, 0x8d, 0x43, 0x03, //0x000052b3 leaq         $3(%r11), %rax
	0x48, 0x89, 0x01, //0x000052b7 movq         %rax, (%rcx)
	0x43, 0x80, 0x7c, 0x1e, 0x03, 0x6c, //0x000052ba cmpb         $108, $3(%r14,%r11)
	0x0f, 0x85, 0x1d, 0xf3, 0xff, 0xff, //0x000052c0 jne          LBB0_866
	0xe9, 0x51, 0x00, 0x00, 0x00, //0x000052c6 jmp          LBB0_759
	//0x000052cb LBB0_762
	0x48, 0x8b, 0x4d, 0xd0, //0x000052cb movq         $-48(%rbp), %rcx
	0x4c, 0x89, 0x19, //0x000052cf movq         %r11, (%rcx)
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000052d2 movq         $-2, %r12
	0x41, 0x80, 0x38, 0x74, //0x000052d9 cmpb         $116, (%r8)
	0x0f, 0x85, 0x00, 0xf3, 0xff, 0xff, //0x000052dd jne          LBB0_866
	0x49, 0x8d, 0x43, 0x01, //0x000052e3 leaq         $1(%r11), %rax
	0x48, 0x89, 0x01, //0x000052e7 movq         %rax, (%rcx)
	0x43, 0x80, 0x7c, 0x1e, 0x01, 0x72, //0x000052ea cmpb         $114, $1(%r14,%r11)
	0x0f, 0x85, 0xed, 0xf2, 0xff, 0xff, //0x000052f0 jne          LBB0_866
	0x49, 0x8d, 0x43, 0x02, //0x000052f6 leaq         $2(%r11), %rax
	0x48, 0x89, 0x01, //0x000052fa movq         %rax, (%rcx)
	0x43, 0x80, 0x7c, 0x1e, 0x02, 0x75, //0x000052fd cmpb         $117, $2(%r14,%r11)
	0x0f, 0x85, 0xda, 0xf2, 0xff, 0xff, //0x00005303 jne          LBB0_866
	0x49, 0x8d, 0x43, 0x03, //0x00005309 leaq         $3(%r11), %rax
	0x48, 0x89, 0x01, //0x0000530d movq         %rax, (%rcx)
	0x43, 0x80, 0x7c, 0x1e, 0x03, 0x65, //0x00005310 cmpb         $101, $3(%r14,%r11)
	0x0f, 0x85, 0xc7, 0xf2, 0xff, 0xff, //0x00005316 jne          LBB0_866
	//0x0000531c LBB0_759
	0x49, 0x83, 0xc3, 0x04, //0x0000531c addq         $4, %r11
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x00005320 jmp          LBB0_983
	//0x00005325 LBB0_984
	0x4c, 0x89, 0x55, 0x98, //0x00005325 movq         %r10, $-104(%rbp)
	0xe9, 0x10, 0xf4, 0xff, 0xff, //0x00005329 jmp          LBB0_609
	//0x0000532e LBB0_981
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x0000532e movq         $-2, %r12
	0xe9, 0x35, 0x00, 0x00, 0x00, //0x00005335 jmp          LBB0_982
	//0x0000533a LBB0_974
	0x4c, 0x29, 0xea, //0x0000533a subq         %r13, %rdx
	0x48, 0x83, 0xc2, 0x01, //0x0000533d addq         $1, %rdx
	//0x00005341 LBB0_975
	0x48, 0x8b, 0x45, 0xd0, //0x00005341 movq         $-48(%rbp), %rax
	0x48, 0x89, 0x10, //0x00005345 movq         %rdx, (%rax)
	0x4d, 0x89, 0xd4, //0x00005348 movq         %r10, %r12
	0xe9, 0x93, 0xf2, 0xff, 0xff, //0x0000534b jmp          LBB0_866
	//0x00005350 LBB0_976
	0x49, 0x89, 0xd3, //0x00005350 movq         %rdx, %r11
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00005353 jmp          LBB0_982
	//0x00005358 LBB0_977
	0x49, 0x83, 0xc1, 0x01, //0x00005358 addq         $1, %r9
	0x49, 0xc7, 0xc4, 0xfd, 0xff, 0xff, 0xff, //0x0000535c movq         $-3, %r12
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00005363 jmp          LBB0_979
	//0x00005368 LBB0_978
	0x49, 0x83, 0xc1, 0x01, //0x00005368 addq         $1, %r9
	//0x0000536c LBB0_979
	0x4d, 0x89, 0xcb, //0x0000536c movq         %r9, %r11
	//0x0000536f LBB0_982
	0x4d, 0x29, 0xeb, //0x0000536f subq         %r13, %r11
	//0x00005372 LBB0_983
	0x48, 0x8b, 0x45, 0xd0, //0x00005372 movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x18, //0x00005376 movq         %r11, (%rax)
	0xe9, 0x65, 0xf2, 0xff, 0xff, //0x00005379 jmp          LBB0_866
	//0x0000537e LBB0_980
	0x4c, 0x01, 0xea, //0x0000537e addq         %r13, %rdx
	0x4c, 0x8b, 0x45, 0xd0, //0x00005381 movq         $-48(%rbp), %r8
	0x48, 0x85, 0xf6, //0x00005385 testq        %rsi, %rsi
	0x0f, 0x85, 0x48, 0xf3, 0xff, 0xff, //0x00005388 jne          LBB0_877
	0xe9, 0x78, 0xf3, 0xff, 0xff, //0x0000538e jmp          LBB0_883
	//0x00005393 LBB0_985
	0x4c, 0x01, 0xea, //0x00005393 addq         %r13, %rdx
	0xe9, 0xf3, 0xf4, 0xff, 0xff, //0x00005396 jmp          LBB0_900
	//0x0000539b LBB0_986
	0x48, 0x8b, 0x45, 0xb8, //0x0000539b movq         $-72(%rbp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x0000539f movq         $8(%rax), %rax
	//0x000053a3 LBB0_987
	0x48, 0x8b, 0x4d, 0xd0, //0x000053a3 movq         $-48(%rbp), %rcx
	0x48, 0x89, 0x01, //0x000053a7 movq         %rax, (%rcx)
	0xe9, 0x34, 0xf2, 0xff, 0xff, //0x000053aa jmp          LBB0_866
	//0x000053af LBB0_988
	0x4c, 0x8b, 0x5d, 0xd0, //0x000053af movq         $-48(%rbp), %r11
	0xe9, 0x35, 0xfe, 0xff, 0xff, //0x000053b3 jmp          LBB0_964
	//0x000053b8 LBB0_989
	0x49, 0x8d, 0x40, 0xff, //0x000053b8 leaq         $-1(%r8), %rax
	0x4c, 0x39, 0xf0, //0x000053bc cmpq         %r14, %rax
	0x0f, 0x84, 0x1e, 0xf2, 0xff, 0xff, //0x000053bf je           LBB0_866
	0x4b, 0x8d, 0x14, 0x0e, //0x000053c5 leaq         (%r14,%r9), %rdx
	0x48, 0x83, 0xc2, 0x02, //0x000053c9 addq         $2, %rdx
	0x4d, 0x29, 0xf0, //0x000053cd subq         %r14, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x000053d0 addq         $-2, %r8
	0x4d, 0x89, 0xc6, //0x000053d4 movq         %r8, %r14
	0xe9, 0xb2, 0xf4, 0xff, 0xff, //0x000053d7 jmp          LBB0_900
	//0x000053dc .p2align 2, 0x90
	// // .set L0_0_set_452, LBB0_452-LJTI0_0
	// // .set L0_0_set_451, LBB0_451-LJTI0_0
	// // .set L0_0_set_414, LBB0_414-LJTI0_0
	// // .set L0_0_set_399, LBB0_399-LJTI0_0
	// // .set L0_0_set_425, LBB0_425-LJTI0_0
	// // .set L0_0_set_450, LBB0_450-LJTI0_0
	// // .set L0_0_set_413, LBB0_413-LJTI0_0
	// // .set L0_0_set_483, LBB0_483-LJTI0_0
	//0x000053dc LJTI0_0
	0x7a, 0xd2, 0xff, 0xff, //0x000053dc .long L0_0_set_452
	0x74, 0xd2, 0xff, 0xff, //0x000053e0 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000053e4 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000053e8 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000053ec .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000053f0 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000053f4 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000053f8 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000053fc .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005400 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005404 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005408 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000540c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005410 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005414 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005418 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000541c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005420 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005424 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005428 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000542c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005430 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005434 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005438 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000543c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005440 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005444 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005448 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000544c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005450 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005454 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005458 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000545c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005460 .long L0_0_set_451
	0x29, 0xcd, 0xff, 0xff, //0x00005464 .long L0_0_set_414
	0x74, 0xd2, 0xff, 0xff, //0x00005468 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000546c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005470 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005474 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005478 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000547c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005480 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005484 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005488 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000548c .long L0_0_set_451
	0x3a, 0xcc, 0xff, 0xff, //0x00005490 .long L0_0_set_399
	0x74, 0xd2, 0xff, 0xff, //0x00005494 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005498 .long L0_0_set_451
	0x3a, 0xcc, 0xff, 0xff, //0x0000549c .long L0_0_set_399
	0x3a, 0xcc, 0xff, 0xff, //0x000054a0 .long L0_0_set_399
	0x3a, 0xcc, 0xff, 0xff, //0x000054a4 .long L0_0_set_399
	0x3a, 0xcc, 0xff, 0xff, //0x000054a8 .long L0_0_set_399
	0x3a, 0xcc, 0xff, 0xff, //0x000054ac .long L0_0_set_399
	0x3a, 0xcc, 0xff, 0xff, //0x000054b0 .long L0_0_set_399
	0x3a, 0xcc, 0xff, 0xff, //0x000054b4 .long L0_0_set_399
	0x3a, 0xcc, 0xff, 0xff, //0x000054b8 .long L0_0_set_399
	0x3a, 0xcc, 0xff, 0xff, //0x000054bc .long L0_0_set_399
	0x3a, 0xcc, 0xff, 0xff, //0x000054c0 .long L0_0_set_399
	0x74, 0xd2, 0xff, 0xff, //0x000054c4 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054c8 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054cc .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054d0 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054d4 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054d8 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054dc .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054e0 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054e4 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054e8 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054ec .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054f0 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054f4 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054f8 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000054fc .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005500 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005504 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005508 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000550c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005510 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005514 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005518 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000551c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005520 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005524 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005528 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000552c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005530 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005534 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005538 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000553c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005540 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005544 .long L0_0_set_451
	0x46, 0xce, 0xff, 0xff, //0x00005548 .long L0_0_set_425
	0x74, 0xd2, 0xff, 0xff, //0x0000554c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005550 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005554 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005558 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000555c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005560 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005564 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005568 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000556c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005570 .long L0_0_set_451
	0x66, 0xd2, 0xff, 0xff, //0x00005574 .long L0_0_set_450
	0x74, 0xd2, 0xff, 0xff, //0x00005578 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000557c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005580 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005584 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005588 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000558c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x00005590 .long L0_0_set_451
	0x17, 0xcd, 0xff, 0xff, //0x00005594 .long L0_0_set_413
	0x74, 0xd2, 0xff, 0xff, //0x00005598 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x0000559c .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000055a0 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000055a4 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000055a8 .long L0_0_set_451
	0x17, 0xcd, 0xff, 0xff, //0x000055ac .long L0_0_set_413
	0x74, 0xd2, 0xff, 0xff, //0x000055b0 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000055b4 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000055b8 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000055bc .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000055c0 .long L0_0_set_451
	0x74, 0xd2, 0xff, 0xff, //0x000055c4 .long L0_0_set_451
	0xd3, 0xd3, 0xff, 0xff, //0x000055c8 .long L0_0_set_483
	// // .set L0_1_set_160, LBB0_160-LJTI0_1
	// // .set L0_1_set_228, LBB0_228-LJTI0_1
	// // .set L0_1_set_192, LBB0_192-LJTI0_1
	// // .set L0_1_set_146, LBB0_146-LJTI0_1
	// // .set L0_1_set_202, LBB0_202-LJTI0_1
	// // .set L0_1_set_227, LBB0_227-LJTI0_1
	// // .set L0_1_set_191, LBB0_191-LJTI0_1
	// // .set L0_1_set_229, LBB0_229-LJTI0_1
	//0x000055cc LJTI0_1
	0xf2, 0xb4, 0xff, 0xff, //0x000055cc .long L0_1_set_160
	0xb7, 0xbb, 0xff, 0xff, //0x000055d0 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000055d4 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000055d8 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000055dc .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000055e0 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000055e4 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000055e8 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000055ec .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000055f0 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000055f4 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000055f8 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000055fc .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005600 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005604 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005608 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000560c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005610 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005614 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005618 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000561c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005620 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005624 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005628 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000562c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005630 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005634 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005638 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000563c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005640 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005644 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005648 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000564c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005650 .long L0_1_set_228
	0x86, 0xb6, 0xff, 0xff, //0x00005654 .long L0_1_set_192
	0xb7, 0xbb, 0xff, 0xff, //0x00005658 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000565c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005660 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005664 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005668 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000566c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005670 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005674 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005678 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000567c .long L0_1_set_228
	0x11, 0xb4, 0xff, 0xff, //0x00005680 .long L0_1_set_146
	0xb7, 0xbb, 0xff, 0xff, //0x00005684 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005688 .long L0_1_set_228
	0x11, 0xb4, 0xff, 0xff, //0x0000568c .long L0_1_set_146
	0x11, 0xb4, 0xff, 0xff, //0x00005690 .long L0_1_set_146
	0x11, 0xb4, 0xff, 0xff, //0x00005694 .long L0_1_set_146
	0x11, 0xb4, 0xff, 0xff, //0x00005698 .long L0_1_set_146
	0x11, 0xb4, 0xff, 0xff, //0x0000569c .long L0_1_set_146
	0x11, 0xb4, 0xff, 0xff, //0x000056a0 .long L0_1_set_146
	0x11, 0xb4, 0xff, 0xff, //0x000056a4 .long L0_1_set_146
	0x11, 0xb4, 0xff, 0xff, //0x000056a8 .long L0_1_set_146
	0x11, 0xb4, 0xff, 0xff, //0x000056ac .long L0_1_set_146
	0x11, 0xb4, 0xff, 0xff, //0x000056b0 .long L0_1_set_146
	0xb7, 0xbb, 0xff, 0xff, //0x000056b4 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056b8 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056bc .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056c0 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056c4 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056c8 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056cc .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056d0 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056d4 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056d8 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056dc .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056e0 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056e4 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056e8 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056ec .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056f0 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056f4 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056f8 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000056fc .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005700 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005704 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005708 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000570c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005710 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005714 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005718 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000571c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005720 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005724 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005728 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000572c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005730 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005734 .long L0_1_set_228
	0x9b, 0xb7, 0xff, 0xff, //0x00005738 .long L0_1_set_202
	0xb7, 0xbb, 0xff, 0xff, //0x0000573c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005740 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005744 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005748 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000574c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005750 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005754 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005758 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000575c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005760 .long L0_1_set_228
	0xaa, 0xbb, 0xff, 0xff, //0x00005764 .long L0_1_set_227
	0xb7, 0xbb, 0xff, 0xff, //0x00005768 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000576c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005770 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005774 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005778 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000577c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005780 .long L0_1_set_228
	0x74, 0xb6, 0xff, 0xff, //0x00005784 .long L0_1_set_191
	0xb7, 0xbb, 0xff, 0xff, //0x00005788 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x0000578c .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005790 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005794 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x00005798 .long L0_1_set_228
	0x74, 0xb6, 0xff, 0xff, //0x0000579c .long L0_1_set_191
	0xb7, 0xbb, 0xff, 0xff, //0x000057a0 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000057a4 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000057a8 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000057ac .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000057b0 .long L0_1_set_228
	0xb7, 0xbb, 0xff, 0xff, //0x000057b4 .long L0_1_set_228
	0xc2, 0xbb, 0xff, 0xff, //0x000057b8 .long L0_1_set_229
	// // .set L0_2_set_583, LBB0_583-LJTI0_2
	// // .set L0_2_set_587, LBB0_587-LJTI0_2
	// // .set L0_2_set_589, LBB0_589-LJTI0_2
	// // .set L0_2_set_611, LBB0_611-LJTI0_2
	// // .set L0_2_set_613, LBB0_613-LJTI0_2
	// // .set L0_2_set_616, LBB0_616-LJTI0_2
	//0x000057bc LJTI0_2
	0x3f, 0xd9, 0xff, 0xff, //0x000057bc .long L0_2_set_583
	0x67, 0xd9, 0xff, 0xff, //0x000057c0 .long L0_2_set_587
	0x92, 0xd9, 0xff, 0xff, //0x000057c4 .long L0_2_set_589
	0x4c, 0xdb, 0xff, 0xff, //0x000057c8 .long L0_2_set_611
	0x63, 0xdb, 0xff, 0xff, //0x000057cc .long L0_2_set_613
	0xb5, 0xdd, 0xff, 0xff, //0x000057d0 .long L0_2_set_616
	// // .set L0_3_set_866, LBB0_866-LJTI0_3
	// // .set L0_3_set_865, LBB0_865-LJTI0_3
	// // .set L0_3_set_694, LBB0_694-LJTI0_3
	// // .set L0_3_set_712, LBB0_712-LJTI0_3
	// // .set L0_3_set_618, LBB0_618-LJTI0_3
	// // .set L0_3_set_748, LBB0_748-LJTI0_3
	// // .set L0_3_set_750, LBB0_750-LJTI0_3
	// // .set L0_3_set_753, LBB0_753-LJTI0_3
	// // .set L0_3_set_760, LBB0_760-LJTI0_3
	// // .set L0_3_set_766, LBB0_766-LJTI0_3
	//0x000057d4 LJTI0_3
	0x0f, 0xee, 0xff, 0xff, //0x000057d4 .long L0_3_set_866
	0x08, 0xee, 0xff, 0xff, //0x000057d8 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000057dc .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000057e0 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000057e4 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000057e8 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000057ec .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000057f0 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000057f4 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000057f8 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000057fc .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005800 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005804 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005808 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000580c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005810 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005814 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005818 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000581c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005820 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005824 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005828 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000582c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005830 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005834 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005838 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000583c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005840 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005844 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005848 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000584c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005850 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005854 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005858 .long L0_3_set_865
	0x73, 0xe0, 0xff, 0xff, //0x0000585c .long L0_3_set_694
	0x08, 0xee, 0xff, 0xff, //0x00005860 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005864 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005868 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000586c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005870 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005874 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005878 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000587c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005880 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005884 .long L0_3_set_865
	0x14, 0xe2, 0xff, 0xff, //0x00005888 .long L0_3_set_712
	0x08, 0xee, 0xff, 0xff, //0x0000588c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005890 .long L0_3_set_865
	0x7c, 0xdb, 0xff, 0xff, //0x00005894 .long L0_3_set_618
	0x7c, 0xdb, 0xff, 0xff, //0x00005898 .long L0_3_set_618
	0x7c, 0xdb, 0xff, 0xff, //0x0000589c .long L0_3_set_618
	0x7c, 0xdb, 0xff, 0xff, //0x000058a0 .long L0_3_set_618
	0x7c, 0xdb, 0xff, 0xff, //0x000058a4 .long L0_3_set_618
	0x7c, 0xdb, 0xff, 0xff, //0x000058a8 .long L0_3_set_618
	0x7c, 0xdb, 0xff, 0xff, //0x000058ac .long L0_3_set_618
	0x7c, 0xdb, 0xff, 0xff, //0x000058b0 .long L0_3_set_618
	0x7c, 0xdb, 0xff, 0xff, //0x000058b4 .long L0_3_set_618
	0x7c, 0xdb, 0xff, 0xff, //0x000058b8 .long L0_3_set_618
	0x08, 0xee, 0xff, 0xff, //0x000058bc .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058c0 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058c4 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058c8 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058cc .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058d0 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058d4 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058d8 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058dc .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058e0 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058e4 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058e8 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058ec .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058f0 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058f4 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058f8 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000058fc .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005900 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005904 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005908 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000590c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005910 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005914 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005918 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000591c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005920 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005924 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005928 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000592c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005930 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005934 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005938 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000593c .long L0_3_set_865
	0x3d, 0xe4, 0xff, 0xff, //0x00005940 .long L0_3_set_748
	0x08, 0xee, 0xff, 0xff, //0x00005944 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005948 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000594c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005950 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005954 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005958 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000595c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005960 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005964 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005968 .long L0_3_set_865
	0x61, 0xe4, 0xff, 0xff, //0x0000596c .long L0_3_set_750
	0x08, 0xee, 0xff, 0xff, //0x00005970 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005974 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005978 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000597c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005980 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005984 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005988 .long L0_3_set_865
	0x8a, 0xe4, 0xff, 0xff, //0x0000598c .long L0_3_set_753
	0x08, 0xee, 0xff, 0xff, //0x00005990 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005994 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x00005998 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x0000599c .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000059a0 .long L0_3_set_865
	0xad, 0xe4, 0xff, 0xff, //0x000059a4 .long L0_3_set_760
	0x08, 0xee, 0xff, 0xff, //0x000059a8 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000059ac .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000059b0 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000059b4 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000059b8 .long L0_3_set_865
	0x08, 0xee, 0xff, 0xff, //0x000059bc .long L0_3_set_865
	0xd0, 0xe4, 0xff, 0xff, //0x000059c0 .long L0_3_set_766
	// // .set L0_4_set_737, LBB0_737-LJTI0_4
	// // .set L0_4_set_770, LBB0_770-LJTI0_4
	// // .set L0_4_set_743, LBB0_743-LJTI0_4
	// // .set L0_4_set_746, LBB0_746-LJTI0_4
	//0x000059c4 LJTI0_4
	0xdd, 0xe1, 0xff, 0xff, //0x000059c4 .long L0_4_set_737
	0x3a, 0xe3, 0xff, 0xff, //0x000059c8 .long L0_4_set_770
	0xdd, 0xe1, 0xff, 0xff, //0x000059cc .long L0_4_set_737
	0x1e, 0xe2, 0xff, 0xff, //0x000059d0 .long L0_4_set_743
	0x3a, 0xe3, 0xff, 0xff, //0x000059d4 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x000059d8 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x000059dc .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x000059e0 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x000059e4 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x000059e8 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x000059ec .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x000059f0 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x000059f4 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x000059f8 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x000059fc .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x00005a00 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x00005a04 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x00005a08 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x00005a0c .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x00005a10 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x00005a14 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x00005a18 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x00005a1c .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x00005a20 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x00005a24 .long L0_4_set_770
	0x3a, 0xe3, 0xff, 0xff, //0x00005a28 .long L0_4_set_770
	0x3a, 0xe2, 0xff, 0xff, //0x00005a2c .long L0_4_set_746
	// // .set L0_5_set_649, LBB0_649-LJTI0_5
	// // .set L0_5_set_680, LBB0_680-LJTI0_5
	// // .set L0_5_set_643, LBB0_643-LJTI0_5
	// // .set L0_5_set_652, LBB0_652-LJTI0_5
	//0x00005a30 LJTI0_5
	0x12, 0xdb, 0xff, 0xff, //0x00005a30 .long L0_5_set_649
	0x81, 0xdd, 0xff, 0xff, //0x00005a34 .long L0_5_set_680
	0x12, 0xdb, 0xff, 0xff, //0x00005a38 .long L0_5_set_649
	0xc4, 0xda, 0xff, 0xff, //0x00005a3c .long L0_5_set_643
	0x81, 0xdd, 0xff, 0xff, //0x00005a40 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a44 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a48 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a4c .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a50 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a54 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a58 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a5c .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a60 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a64 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a68 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a6c .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a70 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a74 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a78 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a7c .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a80 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a84 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a88 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a8c .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a90 .long L0_5_set_680
	0x81, 0xdd, 0xff, 0xff, //0x00005a94 .long L0_5_set_680
	0x2e, 0xdb, 0xff, 0xff, //0x00005a98 .long L0_5_set_652
	// // .set L0_6_set_866, LBB0_866-LJTI0_6
	// // .set L0_6_set_890, LBB0_890-LJTI0_6
	// // .set L0_6_set_891, LBB0_891-LJTI0_6
	// // .set L0_6_set_872, LBB0_872-LJTI0_6
	// // .set L0_6_set_902, LBB0_902-LJTI0_6
	// // .set L0_6_set_928, LBB0_928-LJTI0_6
	// // .set L0_6_set_885, LBB0_885-LJTI0_6
	// // .set L0_6_set_930, LBB0_930-LJTI0_6
	//0x00005a9c LJTI0_6
	0x47, 0xeb, 0xff, 0xff, //0x00005a9c .long L0_6_set_866
	0xee, 0xec, 0xff, 0xff, //0x00005aa0 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005aa4 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005aa8 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005aac .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ab0 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ab4 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ab8 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005abc .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ac0 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ac4 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ac8 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005acc .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ad0 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ad4 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ad8 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005adc .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ae0 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ae4 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ae8 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005aec .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005af0 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005af4 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005af8 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005afc .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b00 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b04 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b08 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b0c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b10 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b14 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b18 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b1c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b20 .long L0_6_set_890
	0xfa, 0xec, 0xff, 0xff, //0x00005b24 .long L0_6_set_891
	0xee, 0xec, 0xff, 0xff, //0x00005b28 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b2c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b30 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b34 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b38 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b3c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b40 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b44 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b48 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b4c .long L0_6_set_890
	0xac, 0xeb, 0xff, 0xff, //0x00005b50 .long L0_6_set_872
	0xee, 0xec, 0xff, 0xff, //0x00005b54 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b58 .long L0_6_set_890
	0xac, 0xeb, 0xff, 0xff, //0x00005b5c .long L0_6_set_872
	0xac, 0xeb, 0xff, 0xff, //0x00005b60 .long L0_6_set_872
	0xac, 0xeb, 0xff, 0xff, //0x00005b64 .long L0_6_set_872
	0xac, 0xeb, 0xff, 0xff, //0x00005b68 .long L0_6_set_872
	0xac, 0xeb, 0xff, 0xff, //0x00005b6c .long L0_6_set_872
	0xac, 0xeb, 0xff, 0xff, //0x00005b70 .long L0_6_set_872
	0xac, 0xeb, 0xff, 0xff, //0x00005b74 .long L0_6_set_872
	0xac, 0xeb, 0xff, 0xff, //0x00005b78 .long L0_6_set_872
	0xac, 0xeb, 0xff, 0xff, //0x00005b7c .long L0_6_set_872
	0xac, 0xeb, 0xff, 0xff, //0x00005b80 .long L0_6_set_872
	0xee, 0xec, 0xff, 0xff, //0x00005b84 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b88 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b8c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b90 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b94 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b98 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005b9c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ba0 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ba4 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005ba8 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bac .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bb0 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bb4 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bb8 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bbc .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bc0 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bc4 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bc8 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bcc .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bd0 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bd4 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bd8 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bdc .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005be0 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005be4 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005be8 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bec .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bf0 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bf4 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bf8 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005bfc .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c00 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c04 .long L0_6_set_890
	0x07, 0xee, 0xff, 0xff, //0x00005c08 .long L0_6_set_902
	0xee, 0xec, 0xff, 0xff, //0x00005c0c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c10 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c14 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c18 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c1c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c20 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c24 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c28 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c2c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c30 .long L0_6_set_890
	0x4f, 0xf2, 0xff, 0xff, //0x00005c34 .long L0_6_set_928
	0xee, 0xec, 0xff, 0xff, //0x00005c38 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c3c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c40 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c44 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c48 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c4c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c50 .long L0_6_set_890
	0xc4, 0xec, 0xff, 0xff, //0x00005c54 .long L0_6_set_885
	0xee, 0xec, 0xff, 0xff, //0x00005c58 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c5c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c60 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c64 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c68 .long L0_6_set_890
	0xc4, 0xec, 0xff, 0xff, //0x00005c6c .long L0_6_set_885
	0xee, 0xec, 0xff, 0xff, //0x00005c70 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c74 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c78 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c7c .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c80 .long L0_6_set_890
	0xee, 0xec, 0xff, 0xff, //0x00005c84 .long L0_6_set_890
	0x62, 0xf2, 0xff, 0xff, //0x00005c88 .long L0_6_set_930
	//0x00005c8c .p2align 2, 0x00
	//0x00005c8c _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00005c8c .long 2
	//0x00005c90 .p2align 4, 0x00
	//0x00005c90 __UnquoteTab
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c90 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ca0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, //0x00005cb0 QUAD $0x0000000000220000; QUAD $0x2f00000000000000  // .ascii 16, '\x00\x00"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00/'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005cc0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005cd0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5c, 0x00, 0x00, 0x00, //0x00005ce0 QUAD $0x0000000000000000; QUAD $0x0000005c00000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\\\x00\x00\x00'
	0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, //0x00005cf0 QUAD $0x000c000000080000; QUAD $0x000a000000000000  // .ascii 16, '\x00\x00\x08\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\n\x00'
	0x00, 0x00, 0x0d, 0x00, 0x09, 0xff, //0x00005d00 LONG $0x000d0000; WORD $0xff09  // .ascii 6, '\x00\x00\r\x00\t\xff'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d06 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d16 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d26 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d36 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d46 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d56 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d66 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d76 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d86 QUAD $0x0000000000000000; WORD $0x0000  // .space 10, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
}
 
