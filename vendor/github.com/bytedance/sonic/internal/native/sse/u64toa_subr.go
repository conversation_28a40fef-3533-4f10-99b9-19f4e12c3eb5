// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__u64toa = 80
)

const (
    _stack__u64toa = 8
)

const (
    _size__u64toa = 1232
)

var (
    _pcsp__u64toa = [][2]uint32{
        {0x1, 0},
        {0xa5, 8},
        {0xa6, 0},
        {0x1cf, 8},
        {0x1d0, 0},
        {0x307, 8},
        {0x308, 0},
        {0x4cf, 8},
        {0x4d0, 0},
    }
)

var _cfunc_u64toa = []loader.CFunc{
    {"_u64toa_entry", 0,  _entry__u64toa, 0, nil},
    {"_u64toa", _entry__u64toa, _size__u64toa, _stack__u64toa, _pcsp__u64toa},
}
