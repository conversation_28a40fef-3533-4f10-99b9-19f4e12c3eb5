// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_vstring = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, // QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000010 LCPI0_1
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000010 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000020 LCPI0_2
	0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, //0x00000020 QUAD $0x1f1f1f1f1f1f1f1f; QUAD $0x1f1f1f1f1f1f1f1f  // .space 16, '\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f'
	//0x00000030 .p2align 4, 0x90
	//0x00000030 _vstring
	0x55, //0x00000030 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000031 movq         %rsp, %rbp
	0x41, 0x57, //0x00000034 pushq        %r15
	0x41, 0x56, //0x00000036 pushq        %r14
	0x41, 0x55, //0x00000038 pushq        %r13
	0x41, 0x54, //0x0000003a pushq        %r12
	0x53, //0x0000003c pushq        %rbx
	0x48, 0x83, 0xec, 0x38, //0x0000003d subq         $56, %rsp
	0x48, 0x89, 0x55, 0xc0, //0x00000041 movq         %rdx, $-64(%rbp)
	0x48, 0x89, 0x75, 0xc8, //0x00000045 movq         %rsi, $-56(%rbp)
	0x48, 0x8b, 0x06, //0x00000049 movq         (%rsi), %rax
	0xf6, 0xc1, 0x20, //0x0000004c testb        $32, %cl
	0x48, 0x89, 0x45, 0xb0, //0x0000004f movq         %rax, $-80(%rbp)
	0x0f, 0x85, 0xa4, 0x01, 0x00, 0x00, //0x00000053 jne          LBB0_13
	0x4c, 0x8b, 0x6f, 0x08, //0x00000059 movq         $8(%rdi), %r13
	0x4c, 0x89, 0x6d, 0xb8, //0x0000005d movq         %r13, $-72(%rbp)
	0x49, 0x29, 0xc5, //0x00000061 subq         %rax, %r13
	0x0f, 0x84, 0x42, 0x05, 0x00, 0x00, //0x00000064 je           LBB0_42
	0x4c, 0x8b, 0x27, //0x0000006a movq         (%rdi), %r12
	0x49, 0x83, 0xfd, 0x40, //0x0000006d cmpq         $64, %r13
	0x0f, 0x82, 0x41, 0x05, 0x00, 0x00, //0x00000071 jb           LBB0_43
	0x49, 0x89, 0xc0, //0x00000077 movq         %rax, %r8
	0x48, 0x89, 0xc3, //0x0000007a movq         %rax, %rbx
	0x48, 0xf7, 0xd3, //0x0000007d notq         %rbx
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x00000080 movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xdb, //0x00000088 xorl         %r11d, %r11d
	0xf3, 0x0f, 0x6f, 0x05, 0x6d, 0xff, 0xff, 0xff, //0x0000008b movdqu       $-147(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x75, 0xff, 0xff, 0xff, //0x00000093 movdqu       $-139(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000009b movabsq      $6148914691236517205, %r15
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000000a5 .p2align 4, 0x90
	//0x000000b0 LBB0_4
	0xf3, 0x43, 0x0f, 0x6f, 0x14, 0x04, //0x000000b0 movdqu       (%r12,%r8), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x04, 0x10, //0x000000b6 movdqu       $16(%r12,%r8), %xmm3
	0xf3, 0x43, 0x0f, 0x6f, 0x64, 0x04, 0x20, //0x000000bd movdqu       $32(%r12,%r8), %xmm4
	0xf3, 0x43, 0x0f, 0x6f, 0x6c, 0x04, 0x30, //0x000000c4 movdqu       $48(%r12,%r8), %xmm5
	0x66, 0x0f, 0x6f, 0xf2, //0x000000cb movdqa       %xmm2, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000000cf pcmpeqb      %xmm0, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xd6, //0x000000d3 pmovmskb     %xmm6, %r10d
	0x66, 0x0f, 0x6f, 0xf3, //0x000000d8 movdqa       %xmm3, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000000dc pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xce, //0x000000e0 pmovmskb     %xmm6, %ecx
	0x66, 0x0f, 0x6f, 0xf4, //0x000000e4 movdqa       %xmm4, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000000e8 pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xd6, //0x000000ec pmovmskb     %xmm6, %edx
	0x66, 0x0f, 0x6f, 0xf5, //0x000000f0 movdqa       %xmm5, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x000000f4 pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xf6, //0x000000f8 pmovmskb     %xmm6, %esi
	0x66, 0x0f, 0x74, 0xd1, //0x000000fc pcmpeqb      %xmm1, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xca, //0x00000100 pmovmskb     %xmm2, %r9d
	0x66, 0x0f, 0x74, 0xd9, //0x00000105 pcmpeqb      %xmm1, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xf3, //0x00000109 pmovmskb     %xmm3, %r14d
	0x66, 0x0f, 0x74, 0xe1, //0x0000010e pcmpeqb      %xmm1, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x00000112 pmovmskb     %xmm4, %edi
	0x66, 0x0f, 0x74, 0xe9, //0x00000116 pcmpeqb      %xmm1, %xmm5
	0x66, 0x0f, 0xd7, 0xc5, //0x0000011a pmovmskb     %xmm5, %eax
	0x48, 0xc1, 0xe6, 0x30, //0x0000011e shlq         $48, %rsi
	0x48, 0xc1, 0xe2, 0x20, //0x00000122 shlq         $32, %rdx
	0x48, 0x09, 0xf2, //0x00000126 orq          %rsi, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x00000129 shlq         $16, %rcx
	0x48, 0x09, 0xd1, //0x0000012d orq          %rdx, %rcx
	0x49, 0x09, 0xca, //0x00000130 orq          %rcx, %r10
	0x48, 0xc1, 0xe0, 0x30, //0x00000133 shlq         $48, %rax
	0x48, 0xc1, 0xe7, 0x20, //0x00000137 shlq         $32, %rdi
	0x48, 0x09, 0xc7, //0x0000013b orq          %rax, %rdi
	0x49, 0xc1, 0xe6, 0x10, //0x0000013e shlq         $16, %r14
	0x49, 0x09, 0xfe, //0x00000142 orq          %rdi, %r14
	0x4d, 0x09, 0xf1, //0x00000145 orq          %r14, %r9
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00000148 jne          LBB0_8
	0x4d, 0x85, 0xdb, //0x0000014e testq        %r11, %r11
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000151 jne          LBB0_10
	0x45, 0x31, 0xdb, //0x00000157 xorl         %r11d, %r11d
	0x4d, 0x85, 0xd2, //0x0000015a testq        %r10, %r10
	0x0f, 0x85, 0x79, 0x00, 0x00, 0x00, //0x0000015d jne          LBB0_11
	//0x00000163 LBB0_7
	0x49, 0x83, 0xc5, 0xc0, //0x00000163 addq         $-64, %r13
	0x48, 0x83, 0xc3, 0xc0, //0x00000167 addq         $-64, %rbx
	0x49, 0x83, 0xc0, 0x40, //0x0000016b addq         $64, %r8
	0x49, 0x83, 0xfd, 0x3f, //0x0000016f cmpq         $63, %r13
	0x0f, 0x87, 0x37, 0xff, 0xff, 0xff, //0x00000173 ja           LBB0_4
	0xe9, 0xf5, 0x02, 0x00, 0x00, //0x00000179 jmp          LBB0_32
	//0x0000017e LBB0_8
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x0000017e cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00000183 jne          LBB0_10
	0x49, 0x0f, 0xbc, 0xc1, //0x00000189 bsfq         %r9, %rax
	0x4c, 0x01, 0xc0, //0x0000018d addq         %r8, %rax
	0x48, 0x89, 0x45, 0xd0, //0x00000190 movq         %rax, $-48(%rbp)
	//0x00000194 LBB0_10
	0x4c, 0x89, 0xd8, //0x00000194 movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x00000197 notq         %rax
	0x4c, 0x21, 0xc8, //0x0000019a andq         %r9, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x0000019d leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd9, //0x000001a1 orq          %r11, %rcx
	0x48, 0x89, 0xca, //0x000001a4 movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x000001a7 notq         %rdx
	0x4c, 0x21, 0xca, //0x000001aa andq         %r9, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000001ad movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x000001b7 andq         %rsi, %rdx
	0x45, 0x31, 0xdb, //0x000001ba xorl         %r11d, %r11d
	0x48, 0x01, 0xc2, //0x000001bd addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc3, //0x000001c0 setb         %r11b
	0x48, 0x01, 0xd2, //0x000001c4 addq         %rdx, %rdx
	0x4c, 0x31, 0xfa, //0x000001c7 xorq         %r15, %rdx
	0x48, 0x21, 0xca, //0x000001ca andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x000001cd notq         %rdx
	0x49, 0x21, 0xd2, //0x000001d0 andq         %rdx, %r10
	0x4d, 0x85, 0xd2, //0x000001d3 testq        %r10, %r10
	0x0f, 0x84, 0x87, 0xff, 0xff, 0xff, //0x000001d6 je           LBB0_7
	//0x000001dc LBB0_11
	0x4d, 0x0f, 0xbc, 0xda, //0x000001dc bsfq         %r10, %r11
	0x49, 0x29, 0xdb, //0x000001e0 subq         %rbx, %r11
	//0x000001e3 LBB0_12
	0x48, 0x8b, 0x55, 0xc0, //0x000001e3 movq         $-64(%rbp), %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x000001e7 movq         $-56(%rbp), %rsi
	0x48, 0x8b, 0x45, 0xb0, //0x000001eb movq         $-80(%rbp), %rax
	0x4d, 0x85, 0xdb, //0x000001ef testq        %r11, %r11
	0x0f, 0x89, 0x52, 0x02, 0x00, 0x00, //0x000001f2 jns          LBB0_31
	0xe9, 0xb8, 0x06, 0x00, 0x00, //0x000001f8 jmp          LBB0_93
	//0x000001fd LBB0_13
	0x4c, 0x8b, 0x6f, 0x08, //0x000001fd movq         $8(%rdi), %r13
	0x4c, 0x89, 0x6d, 0xb8, //0x00000201 movq         %r13, $-72(%rbp)
	0x49, 0x29, 0xc5, //0x00000205 subq         %rax, %r13
	0x0f, 0x84, 0x9e, 0x03, 0x00, 0x00, //0x00000208 je           LBB0_42
	0x48, 0x8b, 0x0f, //0x0000020e movq         (%rdi), %rcx
	0x48, 0x89, 0x4d, 0xa8, //0x00000211 movq         %rcx, $-88(%rbp)
	0x49, 0x83, 0xfd, 0x40, //0x00000215 cmpq         $64, %r13
	0x0f, 0x82, 0xb7, 0x03, 0x00, 0x00, //0x00000219 jb           LBB0_44
	0x48, 0x89, 0xc3, //0x0000021f movq         %rax, %rbx
	0x48, 0xf7, 0xd3, //0x00000222 notq         %rbx
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x00000225 movq         $-1, $-48(%rbp)
	0x31, 0xf6, //0x0000022d xorl         %esi, %esi
	0xf3, 0x44, 0x0f, 0x6f, 0x05, 0xc8, 0xfd, 0xff, 0xff, //0x0000022f movdqu       $-568(%rip), %xmm8  /* LCPI0_0+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x0d, 0xcf, 0xfd, 0xff, 0xff, //0x00000238 movdqu       $-561(%rip), %xmm9  /* LCPI0_1+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xd6, 0xfd, 0xff, 0xff, //0x00000241 movdqu       $-554(%rip), %xmm10  /* LCPI0_2+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000024a .p2align 4, 0x90
	//0x00000250 LBB0_16
	0x4c, 0x89, 0x6d, 0xa0, //0x00000250 movq         %r13, $-96(%rbp)
	0x49, 0x89, 0xf5, //0x00000254 movq         %rsi, %r13
	0x48, 0x8b, 0x4d, 0xa8, //0x00000257 movq         $-88(%rbp), %rcx
	0xf3, 0x0f, 0x6f, 0x1c, 0x01, //0x0000025b movdqu       (%rcx,%rax), %xmm3
	0xf3, 0x0f, 0x6f, 0x44, 0x01, 0x10, //0x00000260 movdqu       $16(%rcx,%rax), %xmm0
	0xf3, 0x0f, 0x6f, 0x4c, 0x01, 0x20, //0x00000266 movdqu       $32(%rcx,%rax), %xmm1
	0xf3, 0x0f, 0x6f, 0x54, 0x01, 0x30, //0x0000026c movdqu       $48(%rcx,%rax), %xmm2
	0x66, 0x0f, 0x6f, 0xfb, //0x00000272 movdqa       %xmm3, %xmm7
	0x66, 0x41, 0x0f, 0x74, 0xf8, //0x00000276 pcmpeqb      %xmm8, %xmm7
	0x66, 0x0f, 0xd7, 0xff, //0x0000027b pmovmskb     %xmm7, %edi
	0x66, 0x0f, 0x6f, 0xf8, //0x0000027f movdqa       %xmm0, %xmm7
	0x66, 0x0f, 0x6f, 0xf0, //0x00000283 movdqa       %xmm0, %xmm6
	0x66, 0x41, 0x0f, 0xda, 0xf2, //0x00000287 pminub       %xmm10, %xmm6
	0x66, 0x0f, 0x74, 0xf0, //0x0000028c pcmpeqb      %xmm0, %xmm6
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x00000290 pcmpeqb      %xmm8, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xc8, //0x00000295 pmovmskb     %xmm0, %r9d
	0x66, 0x0f, 0x6f, 0xc1, //0x0000029a movdqa       %xmm1, %xmm0
	0x66, 0x0f, 0x6f, 0xe9, //0x0000029e movdqa       %xmm1, %xmm5
	0x66, 0x41, 0x0f, 0xda, 0xea, //0x000002a2 pminub       %xmm10, %xmm5
	0x66, 0x0f, 0x74, 0xe9, //0x000002a7 pcmpeqb      %xmm1, %xmm5
	0x66, 0x41, 0x0f, 0x74, 0xc8, //0x000002ab pcmpeqb      %xmm8, %xmm1
	0x66, 0x0f, 0xd7, 0xf1, //0x000002b0 pmovmskb     %xmm1, %esi
	0x66, 0x0f, 0x6f, 0xca, //0x000002b4 movdqa       %xmm2, %xmm1
	0x66, 0x0f, 0x6f, 0xe2, //0x000002b8 movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0xda, 0xe2, //0x000002bc pminub       %xmm10, %xmm4
	0x66, 0x0f, 0x74, 0xe2, //0x000002c1 pcmpeqb      %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xd0, //0x000002c5 pcmpeqb      %xmm8, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x000002ca pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd3, //0x000002ce movdqa       %xmm3, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd1, //0x000002d2 pcmpeqb      %xmm9, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xf2, //0x000002d7 pmovmskb     %xmm2, %r14d
	0x66, 0x41, 0x0f, 0x74, 0xf9, //0x000002dc pcmpeqb      %xmm9, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xff, //0x000002e1 pmovmskb     %xmm7, %r15d
	0x66, 0x41, 0x0f, 0x74, 0xc1, //0x000002e6 pcmpeqb      %xmm9, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xd8, //0x000002eb pmovmskb     %xmm0, %r11d
	0x66, 0x41, 0x0f, 0x74, 0xc9, //0x000002f0 pcmpeqb      %xmm9, %xmm1
	0x66, 0x44, 0x0f, 0xd7, 0xe1, //0x000002f5 pmovmskb     %xmm1, %r12d
	0x66, 0x0f, 0xd7, 0xce, //0x000002fa pmovmskb     %xmm6, %ecx
	0x66, 0x44, 0x0f, 0xd7, 0xc5, //0x000002fe pmovmskb     %xmm5, %r8d
	0x66, 0x44, 0x0f, 0xd7, 0xd4, //0x00000303 pmovmskb     %xmm4, %r10d
	0x48, 0xc1, 0xe2, 0x30, //0x00000308 shlq         $48, %rdx
	0x48, 0xc1, 0xe6, 0x20, //0x0000030c shlq         $32, %rsi
	0x48, 0x09, 0xd6, //0x00000310 orq          %rdx, %rsi
	0x49, 0xc1, 0xe1, 0x10, //0x00000313 shlq         $16, %r9
	0x49, 0x09, 0xf1, //0x00000317 orq          %rsi, %r9
	0x4c, 0x09, 0xcf, //0x0000031a orq          %r9, %rdi
	0x49, 0xc1, 0xe4, 0x30, //0x0000031d shlq         $48, %r12
	0x49, 0xc1, 0xe3, 0x20, //0x00000321 shlq         $32, %r11
	0x4d, 0x09, 0xe3, //0x00000325 orq          %r12, %r11
	0x49, 0xc1, 0xe7, 0x10, //0x00000328 shlq         $16, %r15
	0x4d, 0x09, 0xdf, //0x0000032c orq          %r11, %r15
	0x49, 0xc1, 0xe2, 0x30, //0x0000032f shlq         $48, %r10
	0x49, 0xc1, 0xe0, 0x20, //0x00000333 shlq         $32, %r8
	0x4d, 0x09, 0xd0, //0x00000337 orq          %r10, %r8
	0x48, 0xc1, 0xe1, 0x10, //0x0000033a shlq         $16, %rcx
	0x4c, 0x09, 0xc1, //0x0000033e orq          %r8, %rcx
	0x4d, 0x09, 0xfe, //0x00000341 orq          %r15, %r14
	0x0f, 0x85, 0x50, 0x00, 0x00, 0x00, //0x00000344 jne          LBB0_22
	0x4d, 0x85, 0xed, //0x0000034a testq        %r13, %r13
	0x0f, 0x85, 0x5d, 0x00, 0x00, 0x00, //0x0000034d jne          LBB0_24
	0x31, 0xf6, //0x00000353 xorl         %esi, %esi
	//0x00000355 LBB0_19
	0x66, 0x0f, 0x6f, 0xc3, //0x00000355 movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc2, //0x00000359 pminub       %xmm10, %xmm0
	0x66, 0x0f, 0x74, 0xc3, //0x0000035e pcmpeqb      %xmm3, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00000362 pmovmskb     %xmm0, %edx
	0x48, 0x09, 0xd1, //0x00000366 orq          %rdx, %rcx
	0x48, 0x85, 0xff, //0x00000369 testq        %rdi, %rdi
	0x0f, 0x85, 0x8b, 0x00, 0x00, 0x00, //0x0000036c jne          LBB0_25
	0x48, 0x85, 0xc9, //0x00000372 testq        %rcx, %rcx
	0x0f, 0x85, 0xf3, 0x04, 0x00, 0x00, //0x00000375 jne          LBB0_85
	0x4c, 0x8b, 0x6d, 0xa0, //0x0000037b movq         $-96(%rbp), %r13
	0x49, 0x83, 0xc5, 0xc0, //0x0000037f addq         $-64, %r13
	0x48, 0x83, 0xc3, 0xc0, //0x00000383 addq         $-64, %rbx
	0x48, 0x83, 0xc0, 0x40, //0x00000387 addq         $64, %rax
	0x49, 0x83, 0xfd, 0x3f, //0x0000038b cmpq         $63, %r13
	0x0f, 0x87, 0xbb, 0xfe, 0xff, 0xff, //0x0000038f ja           LBB0_16
	0xe9, 0x66, 0x01, 0x00, 0x00, //0x00000395 jmp          LBB0_37
	//0x0000039a LBB0_22
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x0000039a cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x0000039f jne          LBB0_24
	0x49, 0x0f, 0xbc, 0xd6, //0x000003a5 bsfq         %r14, %rdx
	0x48, 0x01, 0xc2, //0x000003a9 addq         %rax, %rdx
	0x48, 0x89, 0x55, 0xd0, //0x000003ac movq         %rdx, $-48(%rbp)
	//0x000003b0 LBB0_24
	0x4d, 0x89, 0xe9, //0x000003b0 movq         %r13, %r9
	0x49, 0xf7, 0xd1, //0x000003b3 notq         %r9
	0x4d, 0x21, 0xf1, //0x000003b6 andq         %r14, %r9
	0x4f, 0x8d, 0x04, 0x09, //0x000003b9 leaq         (%r9,%r9), %r8
	0x4d, 0x09, 0xe8, //0x000003bd orq          %r13, %r8
	0x4d, 0x89, 0xc2, //0x000003c0 movq         %r8, %r10
	0x49, 0xf7, 0xd2, //0x000003c3 notq         %r10
	0x4d, 0x21, 0xf2, //0x000003c6 andq         %r14, %r10
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000003c9 movabsq      $-6148914691236517206, %rsi
	0x49, 0x21, 0xf2, //0x000003d3 andq         %rsi, %r10
	0x31, 0xf6, //0x000003d6 xorl         %esi, %esi
	0x4d, 0x01, 0xca, //0x000003d8 addq         %r9, %r10
	0x40, 0x0f, 0x92, 0xc6, //0x000003db setb         %sil
	0x4d, 0x01, 0xd2, //0x000003df addq         %r10, %r10
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000003e2 movabsq      $6148914691236517205, %rdx
	0x49, 0x31, 0xd2, //0x000003ec xorq         %rdx, %r10
	0x4d, 0x21, 0xc2, //0x000003ef andq         %r8, %r10
	0x49, 0xf7, 0xd2, //0x000003f2 notq         %r10
	0x4c, 0x21, 0xd7, //0x000003f5 andq         %r10, %rdi
	0xe9, 0x58, 0xff, 0xff, 0xff, //0x000003f8 jmp          LBB0_19
	//0x000003fd LBB0_25
	0x4c, 0x0f, 0xbc, 0xdf, //0x000003fd bsfq         %rdi, %r11
	0x48, 0x85, 0xc9, //0x00000401 testq        %rcx, %rcx
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00000404 je           LBB0_27
	0x48, 0x0f, 0xbc, 0xc1, //0x0000040a bsfq         %rcx, %rax
	0x48, 0x8b, 0x55, 0xc0, //0x0000040e movq         $-64(%rbp), %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x00000412 movq         $-56(%rbp), %rsi
	0x4c, 0x39, 0xd8, //0x00000416 cmpq         %r11, %rax
	0x0f, 0x83, 0x1b, 0x00, 0x00, 0x00, //0x00000419 jae          LBB0_28
	0xe9, 0xba, 0x04, 0x00, 0x00, //0x0000041f jmp          LBB0_92
	//0x00000424 LBB0_27
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00000424 movl         $64, %eax
	0x48, 0x8b, 0x55, 0xc0, //0x00000429 movq         $-64(%rbp), %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x0000042d movq         $-56(%rbp), %rsi
	0x4c, 0x39, 0xd8, //0x00000431 cmpq         %r11, %rax
	0x0f, 0x82, 0xa4, 0x04, 0x00, 0x00, //0x00000434 jb           LBB0_92
	//0x0000043a LBB0_28
	0x49, 0x29, 0xdb, //0x0000043a subq         %rbx, %r11
	0x48, 0x8b, 0x45, 0xb0, //0x0000043d movq         $-80(%rbp), %rax
	0x4d, 0x85, 0xdb, //0x00000441 testq        %r11, %r11
	0x0f, 0x88, 0x6b, 0x04, 0x00, 0x00, //0x00000444 js           LBB0_93
	//0x0000044a LBB0_31
	0x4c, 0x89, 0x1e, //0x0000044a movq         %r11, (%rsi)
	0x48, 0x89, 0x42, 0x10, //0x0000044d movq         %rax, $16(%rdx)
	0x48, 0xc7, 0x02, 0x07, 0x00, 0x00, 0x00, //0x00000451 movq         $7, (%rdx)
	0x48, 0x8b, 0x4d, 0xd0, //0x00000458 movq         $-48(%rbp), %rcx
	0x4c, 0x39, 0xd9, //0x0000045c cmpq         %r11, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000045f movq         $-1, %rax
	0x48, 0x0f, 0x4c, 0xc1, //0x00000466 cmovlq       %rcx, %rax
	0x48, 0x89, 0x42, 0x18, //0x0000046a movq         %rax, $24(%rdx)
	0xe9, 0x4c, 0x04, 0x00, 0x00, //0x0000046e jmp          LBB0_95
	//0x00000473 LBB0_32
	0x4d, 0x01, 0xe0, //0x00000473 addq         %r12, %r8
	0x48, 0x8b, 0x45, 0xb0, //0x00000476 movq         $-80(%rbp), %rax
	0x49, 0x83, 0xfd, 0x20, //0x0000047a cmpq         $32, %r13
	0x0f, 0x82, 0xd8, 0x01, 0x00, 0x00, //0x0000047e jb           LBB0_50
	//0x00000484 LBB0_33
	0xf3, 0x41, 0x0f, 0x6f, 0x00, //0x00000484 movdqu       (%r8), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x48, 0x10, //0x00000489 movdqu       $16(%r8), %xmm1
	0xf3, 0x0f, 0x6f, 0x15, 0x69, 0xfb, 0xff, 0xff, //0x0000048f movdqu       $-1175(%rip), %xmm2  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x1d, 0x71, 0xfb, 0xff, 0xff, //0x00000497 movdqu       $-1167(%rip), %xmm3  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0x6f, 0xe0, //0x0000049f movdqa       %xmm0, %xmm4
	0x66, 0x0f, 0x74, 0xe2, //0x000004a3 pcmpeqb      %xmm2, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x000004a7 pmovmskb     %xmm4, %edi
	0x66, 0x0f, 0x74, 0xd1, //0x000004ab pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x000004af pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x74, 0xc3, //0x000004b3 pcmpeqb      %xmm3, %xmm0
	0x66, 0x0f, 0xd7, 0xd8, //0x000004b7 pmovmskb     %xmm0, %ebx
	0x66, 0x0f, 0x74, 0xcb, //0x000004bb pcmpeqb      %xmm3, %xmm1
	0x66, 0x0f, 0xd7, 0xd1, //0x000004bf pmovmskb     %xmm1, %edx
	0x48, 0xc1, 0xe1, 0x10, //0x000004c3 shlq         $16, %rcx
	0x48, 0x09, 0xcf, //0x000004c7 orq          %rcx, %rdi
	0x48, 0xc1, 0xe2, 0x10, //0x000004ca shlq         $16, %rdx
	0x48, 0x09, 0xd3, //0x000004ce orq          %rdx, %rbx
	0x0f, 0x85, 0x25, 0x01, 0x00, 0x00, //0x000004d1 jne          LBB0_46
	0x4d, 0x85, 0xdb, //0x000004d7 testq        %r11, %r11
	0x0f, 0x85, 0x38, 0x01, 0x00, 0x00, //0x000004da jne          LBB0_48
	0x45, 0x31, 0xdb, //0x000004e0 xorl         %r11d, %r11d
	0x48, 0x85, 0xff, //0x000004e3 testq        %rdi, %rdi
	0x0f, 0x84, 0x68, 0x01, 0x00, 0x00, //0x000004e6 je           LBB0_49
	//0x000004ec LBB0_36
	0x48, 0x0f, 0xbc, 0xcf, //0x000004ec bsfq         %rdi, %rcx
	0x4d, 0x29, 0xe0, //0x000004f0 subq         %r12, %r8
	0x4d, 0x8d, 0x1c, 0x08, //0x000004f3 leaq         (%r8,%rcx), %r11
	0x49, 0x83, 0xc3, 0x01, //0x000004f7 addq         $1, %r11
	0xe9, 0x09, 0x02, 0x00, 0x00, //0x000004fb jmp          LBB0_62
	//0x00000500 LBB0_37
	0x48, 0x03, 0x45, 0xa8, //0x00000500 addq         $-88(%rbp), %rax
	0x4d, 0x89, 0xea, //0x00000504 movq         %r13, %r10
	0x49, 0x83, 0xfd, 0x20, //0x00000507 cmpq         $32, %r13
	0x0f, 0x82, 0xe3, 0x00, 0x00, 0x00, //0x0000050b jb           LBB0_45
	//0x00000511 LBB0_38
	0xf3, 0x0f, 0x6f, 0x00, //0x00000511 movdqu       (%rax), %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0xe3, 0xfa, 0xff, 0xff, //0x00000515 movdqu       $-1309(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd0, //0x0000051d movdqa       %xmm0, %xmm2
	0xf3, 0x0f, 0x6f, 0x1d, 0xf7, 0xfa, 0xff, 0xff, //0x00000521 movdqu       $-1289(%rip), %xmm3  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0x6f, 0xe0, //0x00000529 movdqa       %xmm0, %xmm4
	0x66, 0x0f, 0xda, 0xe3, //0x0000052d pminub       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x00000531 pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0x74, 0xc1, //0x00000535 pcmpeqb      %xmm1, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00000539 pmovmskb     %xmm0, %edi
	0xf3, 0x0f, 0x6f, 0x40, 0x10, //0x0000053d movdqu       $16(%rax), %xmm0
	0x66, 0x0f, 0x74, 0xc8, //0x00000542 pcmpeqb      %xmm0, %xmm1
	0x66, 0x0f, 0xd7, 0xd1, //0x00000546 pmovmskb     %xmm1, %edx
	0xf3, 0x0f, 0x6f, 0x0d, 0xbe, 0xfa, 0xff, 0xff, //0x0000054a movdqu       $-1346(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0x74, 0xd1, //0x00000552 pcmpeqb      %xmm1, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xca, //0x00000556 pmovmskb     %xmm2, %r9d
	0x66, 0x0f, 0x74, 0xc8, //0x0000055b pcmpeqb      %xmm0, %xmm1
	0x66, 0x0f, 0xd7, 0xd9, //0x0000055f pmovmskb     %xmm1, %ebx
	0x66, 0x44, 0x0f, 0xd7, 0xf4, //0x00000563 pmovmskb     %xmm4, %r14d
	0x66, 0x0f, 0xda, 0xd8, //0x00000568 pminub       %xmm0, %xmm3
	0x66, 0x0f, 0x74, 0xd8, //0x0000056c pcmpeqb      %xmm0, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00000570 pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe2, 0x10, //0x00000574 shlq         $16, %rdx
	0x48, 0x09, 0xd7, //0x00000578 orq          %rdx, %rdi
	0x48, 0xc1, 0xe3, 0x10, //0x0000057b shlq         $16, %rbx
	0x49, 0x09, 0xd9, //0x0000057f orq          %rbx, %r9
	0x0f, 0x85, 0xaf, 0x01, 0x00, 0x00, //0x00000582 jne          LBB0_65
	0x48, 0x85, 0xf6, //0x00000588 testq        %rsi, %rsi
	0x0f, 0x85, 0xcb, 0x01, 0x00, 0x00, //0x0000058b jne          LBB0_67
	0x4c, 0x89, 0xd2, //0x00000591 movq         %r10, %rdx
	0x31, 0xf6, //0x00000594 xorl         %esi, %esi
	0x48, 0xc1, 0xe1, 0x10, //0x00000596 shlq         $16, %rcx
	0x48, 0x85, 0xff, //0x0000059a testq        %rdi, %rdi
	0x0f, 0x84, 0xfd, 0x01, 0x00, 0x00, //0x0000059d je           LBB0_69
	//0x000005a3 LBB0_41
	0x48, 0x0f, 0xbc, 0xdf, //0x000005a3 bsfq         %rdi, %rbx
	0xe9, 0xf9, 0x01, 0x00, 0x00, //0x000005a7 jmp          LBB0_70
	//0x000005ac LBB0_42
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000005ac movq         $-1, %r11
	0xe9, 0xc1, 0x02, 0x00, 0x00, //0x000005b3 jmp          LBB0_87
	//0x000005b8 LBB0_43
	0x4d, 0x8d, 0x04, 0x04, //0x000005b8 leaq         (%r12,%rax), %r8
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x000005bc movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xdb, //0x000005c4 xorl         %r11d, %r11d
	0x49, 0x83, 0xfd, 0x20, //0x000005c7 cmpq         $32, %r13
	0x0f, 0x83, 0xb3, 0xfe, 0xff, 0xff, //0x000005cb jae          LBB0_33
	0xe9, 0x86, 0x00, 0x00, 0x00, //0x000005d1 jmp          LBB0_50
	//0x000005d6 LBB0_44
	0x48, 0x8b, 0x4d, 0xa8, //0x000005d6 movq         $-88(%rbp), %rcx
	0x48, 0x01, 0xc8, //0x000005da addq         %rcx, %rax
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x000005dd movq         $-1, $-48(%rbp)
	0x31, 0xf6, //0x000005e5 xorl         %esi, %esi
	0x4d, 0x89, 0xea, //0x000005e7 movq         %r13, %r10
	0x49, 0x83, 0xfd, 0x20, //0x000005ea cmpq         $32, %r13
	0x0f, 0x83, 0x1d, 0xff, 0xff, 0xff, //0x000005ee jae          LBB0_38
	//0x000005f4 LBB0_45
	0x4c, 0x89, 0xd2, //0x000005f4 movq         %r10, %rdx
	0xe9, 0xd8, 0x01, 0x00, 0x00, //0x000005f7 jmp          LBB0_75
	//0x000005fc LBB0_46
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x000005fc cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00000601 jne          LBB0_48
	0x4c, 0x89, 0xc1, //0x00000607 movq         %r8, %rcx
	0x4c, 0x29, 0xe1, //0x0000060a subq         %r12, %rcx
	0x48, 0x0f, 0xbc, 0xc3, //0x0000060d bsfq         %rbx, %rax
	0x48, 0x01, 0xc8, //0x00000611 addq         %rcx, %rax
	0x48, 0x89, 0x45, 0xd0, //0x00000614 movq         %rax, $-48(%rbp)
	//0x00000618 LBB0_48
	0x44, 0x89, 0xd9, //0x00000618 movl         %r11d, %ecx
	0xf7, 0xd1, //0x0000061b notl         %ecx
	0x21, 0xd9, //0x0000061d andl         %ebx, %ecx
	0x41, 0x8d, 0x14, 0x4b, //0x0000061f leal         (%r11,%rcx,2), %edx
	0x8d, 0x34, 0x09, //0x00000623 leal         (%rcx,%rcx), %esi
	0xf7, 0xd6, //0x00000626 notl         %esi
	0x21, 0xde, //0x00000628 andl         %ebx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000062a andl         $-1431655766, %esi
	0x45, 0x31, 0xdb, //0x00000630 xorl         %r11d, %r11d
	0x01, 0xce, //0x00000633 addl         %ecx, %esi
	0x41, 0x0f, 0x92, 0xc3, //0x00000635 setb         %r11b
	0x01, 0xf6, //0x00000639 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x0000063b xorl         $1431655765, %esi
	0x21, 0xd6, //0x00000641 andl         %edx, %esi
	0xf7, 0xd6, //0x00000643 notl         %esi
	0x21, 0xf7, //0x00000645 andl         %esi, %edi
	0x48, 0x8b, 0x45, 0xb0, //0x00000647 movq         $-80(%rbp), %rax
	0x48, 0x85, 0xff, //0x0000064b testq        %rdi, %rdi
	0x0f, 0x85, 0x98, 0xfe, 0xff, 0xff, //0x0000064e jne          LBB0_36
	//0x00000654 LBB0_49
	0x49, 0x83, 0xc0, 0x20, //0x00000654 addq         $32, %r8
	0x49, 0x83, 0xc5, 0xe0, //0x00000658 addq         $-32, %r13
	//0x0000065c LBB0_50
	0x4d, 0x85, 0xdb, //0x0000065c testq        %r11, %r11
	0x0f, 0x85, 0x85, 0x02, 0x00, 0x00, //0x0000065f jne          LBB0_96
	0x48, 0x8b, 0x7d, 0xd0, //0x00000665 movq         $-48(%rbp), %rdi
	0x4d, 0x85, 0xed, //0x00000669 testq        %r13, %r13
	0x0f, 0x84, 0x91, 0x00, 0x00, 0x00, //0x0000066c je           LBB0_61
	//0x00000672 LBB0_52
	0x4c, 0x89, 0xe0, //0x00000672 movq         %r12, %rax
	0x48, 0xf7, 0xd8, //0x00000675 negq         %rax
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000678 movq         $-1, %r11
	//0x0000067f LBB0_53
	0x31, 0xdb, //0x0000067f xorl         %ebx, %ebx
	//0x00000681 LBB0_54
	0x41, 0x0f, 0xb6, 0x0c, 0x18, //0x00000681 movzbl       (%r8,%rbx), %ecx
	0x80, 0xf9, 0x22, //0x00000686 cmpb         $34, %cl
	0x0f, 0x84, 0x69, 0x00, 0x00, 0x00, //0x00000689 je           LBB0_59
	0x80, 0xf9, 0x5c, //0x0000068f cmpb         $92, %cl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00000692 je           LBB0_57
	0x48, 0x83, 0xc3, 0x01, //0x00000698 addq         $1, %rbx
	0x49, 0x39, 0xdd, //0x0000069c cmpq         %rbx, %r13
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x0000069f jne          LBB0_54
	0xe9, 0x75, 0x00, 0x00, 0x00, //0x000006a5 jmp          LBB0_63
	//0x000006aa LBB0_57
	0x49, 0x8d, 0x4d, 0xff, //0x000006aa leaq         $-1(%r13), %rcx
	0x48, 0x39, 0xd9, //0x000006ae cmpq         %rbx, %rcx
	0x0f, 0x84, 0xbe, 0x01, 0x00, 0x00, //0x000006b1 je           LBB0_86
	0x4a, 0x8d, 0x0c, 0x00, //0x000006b7 leaq         (%rax,%r8), %rcx
	0x48, 0x01, 0xd9, //0x000006bb addq         %rbx, %rcx
	0x48, 0x83, 0xff, 0xff, //0x000006be cmpq         $-1, %rdi
	0x48, 0x8b, 0x55, 0xd0, //0x000006c2 movq         $-48(%rbp), %rdx
	0x48, 0x0f, 0x44, 0xd1, //0x000006c6 cmoveq       %rcx, %rdx
	0x48, 0x89, 0x55, 0xd0, //0x000006ca movq         %rdx, $-48(%rbp)
	0x48, 0x0f, 0x44, 0xf9, //0x000006ce cmoveq       %rcx, %rdi
	0x49, 0x01, 0xd8, //0x000006d2 addq         %rbx, %r8
	0x49, 0x83, 0xc0, 0x02, //0x000006d5 addq         $2, %r8
	0x4c, 0x89, 0xe9, //0x000006d9 movq         %r13, %rcx
	0x48, 0x29, 0xd9, //0x000006dc subq         %rbx, %rcx
	0x48, 0x83, 0xc1, 0xfe, //0x000006df addq         $-2, %rcx
	0x49, 0x83, 0xc5, 0xfe, //0x000006e3 addq         $-2, %r13
	0x49, 0x39, 0xdd, //0x000006e7 cmpq         %rbx, %r13
	0x49, 0x89, 0xcd, //0x000006ea movq         %rcx, %r13
	0x0f, 0x85, 0x8c, 0xff, 0xff, 0xff, //0x000006ed jne          LBB0_53
	0xe9, 0x7d, 0x01, 0x00, 0x00, //0x000006f3 jmp          LBB0_86
	//0x000006f8 LBB0_59
	0x49, 0x01, 0xd8, //0x000006f8 addq         %rbx, %r8
	0x49, 0x83, 0xc0, 0x01, //0x000006fb addq         $1, %r8
	//0x000006ff LBB0_60
	0x48, 0x8b, 0x45, 0xb0, //0x000006ff movq         $-80(%rbp), %rax
	//0x00000703 LBB0_61
	0x4d, 0x29, 0xe0, //0x00000703 subq         %r12, %r8
	0x4d, 0x89, 0xc3, //0x00000706 movq         %r8, %r11
	//0x00000709 LBB0_62
	0x48, 0x8b, 0x55, 0xc0, //0x00000709 movq         $-64(%rbp), %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x0000070d movq         $-56(%rbp), %rsi
	0x4d, 0x85, 0xdb, //0x00000711 testq        %r11, %r11
	0x0f, 0x89, 0x30, 0xfd, 0xff, 0xff, //0x00000714 jns          LBB0_31
	0xe9, 0x96, 0x01, 0x00, 0x00, //0x0000071a jmp          LBB0_93
	//0x0000071f LBB0_63
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000071f movq         $-1, %r11
	0x80, 0xf9, 0x22, //0x00000726 cmpb         $34, %cl
	0x0f, 0x85, 0x46, 0x01, 0x00, 0x00, //0x00000729 jne          LBB0_86
	0x4d, 0x01, 0xe8, //0x0000072f addq         %r13, %r8
	0xe9, 0xc8, 0xff, 0xff, 0xff, //0x00000732 jmp          LBB0_60
	//0x00000737 LBB0_65
	0x48, 0x89, 0xf2, //0x00000737 movq         %rsi, %rdx
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x0000073a cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x1a, 0x00, 0x00, 0x00, //0x0000073f jne          LBB0_68
	0x49, 0x89, 0xc0, //0x00000745 movq         %rax, %r8
	0x4c, 0x2b, 0x45, 0xa8, //0x00000748 subq         $-88(%rbp), %r8
	0x49, 0x0f, 0xbc, 0xf1, //0x0000074c bsfq         %r9, %rsi
	0x4c, 0x01, 0xc6, //0x00000750 addq         %r8, %rsi
	0x48, 0x89, 0x75, 0xd0, //0x00000753 movq         %rsi, $-48(%rbp)
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00000757 jmp          LBB0_68
	//0x0000075c LBB0_67
	0x48, 0x89, 0xf2, //0x0000075c movq         %rsi, %rdx
	//0x0000075f LBB0_68
	0x48, 0x89, 0xd6, //0x0000075f movq         %rdx, %rsi
	0xf7, 0xd2, //0x00000762 notl         %edx
	0x44, 0x21, 0xca, //0x00000764 andl         %r9d, %edx
	0x44, 0x8d, 0x04, 0x56, //0x00000767 leal         (%rsi,%rdx,2), %r8d
	0x8d, 0x1c, 0x12, //0x0000076b leal         (%rdx,%rdx), %ebx
	0xf7, 0xd3, //0x0000076e notl         %ebx
	0x44, 0x21, 0xcb, //0x00000770 andl         %r9d, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000773 andl         $-1431655766, %ebx
	0x31, 0xf6, //0x00000779 xorl         %esi, %esi
	0x01, 0xd3, //0x0000077b addl         %edx, %ebx
	0x40, 0x0f, 0x92, 0xc6, //0x0000077d setb         %sil
	0x01, 0xdb, //0x00000781 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00000783 xorl         $1431655765, %ebx
	0x44, 0x21, 0xc3, //0x00000789 andl         %r8d, %ebx
	0xf7, 0xd3, //0x0000078c notl         %ebx
	0x21, 0xdf, //0x0000078e andl         %ebx, %edi
	0x4c, 0x89, 0xd2, //0x00000790 movq         %r10, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x00000793 shlq         $16, %rcx
	0x48, 0x85, 0xff, //0x00000797 testq        %rdi, %rdi
	0x0f, 0x85, 0x03, 0xfe, 0xff, 0xff, //0x0000079a jne          LBB0_41
	//0x000007a0 LBB0_69
	0xbb, 0x40, 0x00, 0x00, 0x00, //0x000007a0 movl         $64, %ebx
	//0x000007a5 LBB0_70
	0x4c, 0x09, 0xf1, //0x000007a5 orq          %r14, %rcx
	0x48, 0x85, 0xff, //0x000007a8 testq        %rdi, %rdi
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000007ab je           LBB0_73
	0x48, 0x85, 0xc9, //0x000007b1 testq        %rcx, %rcx
	0x0f, 0x84, 0xcc, 0x00, 0x00, 0x00, //0x000007b4 je           LBB0_88
	0x48, 0x0f, 0xbc, 0xc9, //0x000007ba bsfq         %rcx, %rcx
	0xe9, 0xc8, 0x00, 0x00, 0x00, //0x000007be jmp          LBB0_89
	//0x000007c3 LBB0_73
	0x48, 0x85, 0xc9, //0x000007c3 testq        %rcx, %rcx
	0x0f, 0x85, 0xa2, 0x00, 0x00, 0x00, //0x000007c6 jne          LBB0_85
	0x48, 0x83, 0xc0, 0x20, //0x000007cc addq         $32, %rax
	0x48, 0x83, 0xc2, 0xe0, //0x000007d0 addq         $-32, %rdx
	//0x000007d4 LBB0_75
	0x48, 0x85, 0xf6, //0x000007d4 testq        %rsi, %rsi
	0x0f, 0x85, 0x50, 0x01, 0x00, 0x00, //0x000007d7 jne          LBB0_98
	0x48, 0x8b, 0x4d, 0xd0, //0x000007dd movq         $-48(%rbp), %rcx
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000007e1 movq         $-1, %r11
	0x48, 0x85, 0xd2, //0x000007e8 testq        %rdx, %rdx
	0x0f, 0x84, 0x84, 0x00, 0x00, 0x00, //0x000007eb je           LBB0_86
	//0x000007f1 LBB0_77
	0x0f, 0xb6, 0x18, //0x000007f1 movzbl       (%rax), %ebx
	0x80, 0xfb, 0x22, //0x000007f4 cmpb         $34, %bl
	0x0f, 0x84, 0xd1, 0x00, 0x00, 0x00, //0x000007f7 je           LBB0_91
	0x80, 0xfb, 0x5c, //0x000007fd cmpb         $92, %bl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00000800 je           LBB0_81
	0x80, 0xfb, 0x20, //0x00000806 cmpb         $32, %bl
	0x0f, 0x82, 0x5f, 0x00, 0x00, 0x00, //0x00000809 jb           LBB0_85
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000080f movq         $-1, %rdi
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00000816 movl         $1, %ebx
	0x48, 0x01, 0xd8, //0x0000081b addq         %rbx, %rax
	0x48, 0x01, 0xfa, //0x0000081e addq         %rdi, %rdx
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x00000821 jne          LBB0_77
	0xe9, 0x49, 0x00, 0x00, 0x00, //0x00000827 jmp          LBB0_86
	//0x0000082c LBB0_81
	0x48, 0x83, 0xfa, 0x01, //0x0000082c cmpq         $1, %rdx
	0x0f, 0x84, 0x3f, 0x00, 0x00, 0x00, //0x00000830 je           LBB0_86
	0x48, 0x89, 0xd6, //0x00000836 movq         %rdx, %rsi
	0x48, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x00000839 movq         $-2, %rdi
	0xbb, 0x02, 0x00, 0x00, 0x00, //0x00000840 movl         $2, %ebx
	0x48, 0x83, 0xf9, 0xff, //0x00000845 cmpq         $-1, %rcx
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00000849 jne          LBB0_84
	0x48, 0x89, 0xc1, //0x0000084f movq         %rax, %rcx
	0x48, 0x2b, 0x4d, 0xa8, //0x00000852 subq         $-88(%rbp), %rcx
	0x48, 0x89, 0x4d, 0xd0, //0x00000856 movq         %rcx, $-48(%rbp)
	//0x0000085a LBB0_84
	0x48, 0x89, 0xf2, //0x0000085a movq         %rsi, %rdx
	0x48, 0x01, 0xd8, //0x0000085d addq         %rbx, %rax
	0x48, 0x01, 0xfa, //0x00000860 addq         %rdi, %rdx
	0x0f, 0x85, 0x88, 0xff, 0xff, 0xff, //0x00000863 jne          LBB0_77
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00000869 jmp          LBB0_86
	//0x0000086e LBB0_85
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x0000086e movq         $-2, %r11
	//0x00000875 LBB0_86
	0x48, 0x8b, 0x45, 0xb8, //0x00000875 movq         $-72(%rbp), %rax
	//0x00000879 LBB0_87
	0x48, 0x8b, 0x55, 0xc0, //0x00000879 movq         $-64(%rbp), %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x0000087d movq         $-56(%rbp), %rsi
	0xe9, 0x33, 0x00, 0x00, 0x00, //0x00000881 jmp          LBB0_94
	//0x00000886 LBB0_88
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000886 movl         $64, %ecx
	//0x0000088b LBB0_89
	0x48, 0x8b, 0x55, 0xc0, //0x0000088b movq         $-64(%rbp), %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x0000088f movq         $-56(%rbp), %rsi
	0x48, 0x39, 0xd9, //0x00000893 cmpq         %rbx, %rcx
	0x0f, 0x82, 0x42, 0x00, 0x00, 0x00, //0x00000896 jb           LBB0_92
	0x48, 0x2b, 0x45, 0xa8, //0x0000089c subq         $-88(%rbp), %rax
	0x4c, 0x8d, 0x1c, 0x18, //0x000008a0 leaq         (%rax,%rbx), %r11
	0x49, 0x83, 0xc3, 0x01, //0x000008a4 addq         $1, %r11
	0x48, 0x8b, 0x45, 0xb0, //0x000008a8 movq         $-80(%rbp), %rax
	0x4d, 0x85, 0xdb, //0x000008ac testq        %r11, %r11
	0x0f, 0x89, 0x95, 0xfb, 0xff, 0xff, //0x000008af jns          LBB0_31
	//0x000008b5 LBB0_93
	0x48, 0x8b, 0x45, 0xb8, //0x000008b5 movq         $-72(%rbp), %rax
	//0x000008b9 LBB0_94
	0x48, 0x89, 0x06, //0x000008b9 movq         %rax, (%rsi)
	0x4c, 0x89, 0x1a, //0x000008bc movq         %r11, (%rdx)
	//0x000008bf LBB0_95
	0x48, 0x83, 0xc4, 0x38, //0x000008bf addq         $56, %rsp
	0x5b, //0x000008c3 popq         %rbx
	0x41, 0x5c, //0x000008c4 popq         %r12
	0x41, 0x5d, //0x000008c6 popq         %r13
	0x41, 0x5e, //0x000008c8 popq         %r14
	0x41, 0x5f, //0x000008ca popq         %r15
	0x5d, //0x000008cc popq         %rbp
	0xc3, //0x000008cd retq         
	//0x000008ce LBB0_91
	0x48, 0x2b, 0x45, 0xa8, //0x000008ce subq         $-88(%rbp), %rax
	0x48, 0x83, 0xc0, 0x01, //0x000008d2 addq         $1, %rax
	0x49, 0x89, 0xc3, //0x000008d6 movq         %rax, %r11
	0xe9, 0x05, 0xf9, 0xff, 0xff, //0x000008d9 jmp          LBB0_12
	//0x000008de LBB0_92
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x000008de movq         $-2, %r11
	0xe9, 0xcb, 0xff, 0xff, 0xff, //0x000008e5 jmp          LBB0_93
	//0x000008ea LBB0_96
	0x4d, 0x85, 0xed, //0x000008ea testq        %r13, %r13
	0x0f, 0x84, 0x87, 0x00, 0x00, 0x00, //0x000008ed je           LBB0_101
	0x4c, 0x89, 0xe7, //0x000008f3 movq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x000008f6 notq         %rdi
	0x4c, 0x01, 0xc7, //0x000008f9 addq         %r8, %rdi
	0x48, 0x8b, 0x4d, 0xd0, //0x000008fc movq         $-48(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x00000900 cmpq         $-1, %rcx
	0x48, 0x89, 0xc8, //0x00000904 movq         %rcx, %rax
	0x48, 0x0f, 0x44, 0xc7, //0x00000907 cmoveq       %rdi, %rax
	0x48, 0x0f, 0x45, 0xf9, //0x0000090b cmovneq      %rcx, %rdi
	0x49, 0x83, 0xc0, 0x01, //0x0000090f addq         $1, %r8
	0x49, 0x83, 0xc5, 0xff, //0x00000913 addq         $-1, %r13
	0x48, 0x89, 0x45, 0xd0, //0x00000917 movq         %rax, $-48(%rbp)
	0x48, 0x8b, 0x45, 0xb0, //0x0000091b movq         $-80(%rbp), %rax
	0x4d, 0x85, 0xed, //0x0000091f testq        %r13, %r13
	0x0f, 0x85, 0x4a, 0xfd, 0xff, 0xff, //0x00000922 jne          LBB0_52
	0xe9, 0xd6, 0xfd, 0xff, 0xff, //0x00000928 jmp          LBB0_61
	//0x0000092d LBB0_98
	0x48, 0x85, 0xd2, //0x0000092d testq        %rdx, %rdx
	0x0f, 0x84, 0x44, 0x00, 0x00, 0x00, //0x00000930 je           LBB0_101
	0x48, 0x89, 0xd7, //0x00000936 movq         %rdx, %rdi
	0x48, 0x8b, 0x4d, 0xa8, //0x00000939 movq         $-88(%rbp), %rcx
	0x48, 0xf7, 0xd1, //0x0000093d notq         %rcx
	0x48, 0x01, 0xc1, //0x00000940 addq         %rax, %rcx
	0x48, 0x8b, 0x75, 0xd0, //0x00000943 movq         $-48(%rbp), %rsi
	0x48, 0x83, 0xfe, 0xff, //0x00000947 cmpq         $-1, %rsi
	0x48, 0x89, 0xf2, //0x0000094b movq         %rsi, %rdx
	0x48, 0x0f, 0x44, 0xd1, //0x0000094e cmoveq       %rcx, %rdx
	0x48, 0x0f, 0x45, 0xce, //0x00000952 cmovneq      %rsi, %rcx
	0x48, 0x83, 0xc0, 0x01, //0x00000956 addq         $1, %rax
	0x48, 0x83, 0xc7, 0xff, //0x0000095a addq         $-1, %rdi
	0x48, 0x89, 0x55, 0xd0, //0x0000095e movq         %rdx, $-48(%rbp)
	0x48, 0x89, 0xfa, //0x00000962 movq         %rdi, %rdx
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000965 movq         $-1, %r11
	0x48, 0x85, 0xd2, //0x0000096c testq        %rdx, %rdx
	0x0f, 0x85, 0x7c, 0xfe, 0xff, 0xff, //0x0000096f jne          LBB0_77
	0xe9, 0xfb, 0xfe, 0xff, 0xff, //0x00000975 jmp          LBB0_86
	//0x0000097a LBB0_101
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000097a movq         $-1, %r11
	0xe9, 0xef, 0xfe, 0xff, 0xff, //0x00000981 jmp          LBB0_86
	0x00, 0x00, //0x00000986 .p2align 2, 0x00
	//0x00000988 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000988 .long 2
}
 
