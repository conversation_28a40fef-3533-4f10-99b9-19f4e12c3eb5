// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__vnumber = 48
)

const (
    _stack__vnumber = 136
)

const (
    _size__vnumber = 8184
)

var (
    _pcsp__vnumber = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0xc29, 136},
        {0xc2a, 48},
        {0xc2c, 40},
        {0xc2e, 32},
        {0xc30, 24},
        {0xc32, 16},
        {0xc33, 8},
        {0xc34, 0},
        {0x1ff8, 136},
    }
)

var _cfunc_vnumber = []loader.CFunc{
    {"_vnumber_entry", 0,  _entry__vnumber, 0, nil},
    {"_vnumber", _entry__vnumber, _size__vnumber, _stack__vnumber, _pcsp__vnumber},
}
