// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__parse_with_padding = 336
)

const (
    _stack__parse_with_padding = 192
)

const (
    _size__parse_with_padding = 48420
)

var (
    _pcsp__parse_with_padding = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x14, 48},
        {0xf31, 192},
        {0xf32, 48},
        {0xf34, 40},
        {0xf36, 32},
        {0xf38, 24},
        {0xf3a, 16},
        {0xf3b, 8},
        {0xf3c, 0},
        {0xbd24, 192},
    }
)

var _cfunc_parse_with_padding = []loader.CFunc{
    {"_parse_with_padding_entry", 0,  _entry__parse_with_padding, 0, nil},
    {"_parse_with_padding", _entry__parse_with_padding, _size__parse_with_padding, _stack__parse_with_padding, _pcsp__parse_with_padding},
}
