// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_validate_one = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // .quad 1
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000008 .quad 0
	//0x00000010 LCPI0_1
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000010 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000020 LCPI0_2
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000020 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000030 LCPI0_3
	0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, //0x00000030 QUAD $0x1f1f1f1f1f1f1f1f; QUAD $0x1f1f1f1f1f1f1f1f  // .space 16, '\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f'
	//0x00000040 LCPI0_4
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000040 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x00000050 LCPI0_5
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000050 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000060 LCPI0_6
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000060 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000070 LCPI0_7
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000070 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000080 LCPI0_8
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000080 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000090 LCPI0_9
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000090 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x000000a0 LCPI0_10
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000a0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000000b0 LCPI0_11
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000b0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x000000c0 LCPI0_12
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x000000c0 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x000000d0 LCPI0_13
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x000000d0 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x000000e0 LCPI0_14
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000000e0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000000f0 LCPI0_15
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x000000f0 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x00000100 LCPI0_16
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000100 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .space 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	//0x00000110 .p2align 4, 0x90
	//0x00000110 _validate_one
	0x55, //0x00000110 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000111 movq         %rsp, %rbp
	0x41, 0x57, //0x00000114 pushq        %r15
	0x41, 0x56, //0x00000116 pushq        %r14
	0x41, 0x55, //0x00000118 pushq        %r13
	0x41, 0x54, //0x0000011a pushq        %r12
	0x53, //0x0000011c pushq        %rbx
	0x48, 0x81, 0xec, 0x88, 0x00, 0x00, 0x00, //0x0000011d subq         $136, %rsp
	0x48, 0x89, 0x4d, 0x98, //0x00000124 movq         %rcx, $-104(%rbp)
	0x49, 0x89, 0xd5, //0x00000128 movq         %rdx, %r13
	0x48, 0x89, 0x7d, 0xa0, //0x0000012b movq         %rdi, $-96(%rbp)
	0xf3, 0x0f, 0x6f, 0x05, 0xc9, 0xfe, 0xff, 0xff, //0x0000012f movdqu       $-311(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x7f, 0x02, //0x00000137 movdqu       %xmm0, (%rdx)
	0x48, 0x89, 0x75, 0xa8, //0x0000013b movq         %rsi, $-88(%rbp)
	0x4c, 0x8b, 0x1e, //0x0000013f movq         (%rsi), %r11
	0x48, 0xc7, 0x45, 0x90, 0xff, 0xff, 0xff, 0xff, //0x00000142 movq         $-1, $-112(%rbp)
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000014a movl         $1, %r8d
	0xf3, 0x44, 0x0f, 0x6f, 0x1d, 0xb7, 0xfe, 0xff, 0xff, //0x00000150 movdqu       $-329(%rip), %xmm11  /* LCPI0_1+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0xbe, 0xfe, 0xff, 0xff, //0x00000159 movdqu       $-322(%rip), %xmm13  /* LCPI0_2+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xc5, 0xfe, 0xff, 0xff, //0x00000162 movdqu       $-315(%rip), %xmm10  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x3d, 0x2d, 0xff, 0xff, 0xff, //0x0000016b movdqu       $-211(%rip), %xmm7  /* LCPI0_10+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x0d, 0x14, 0xff, 0xff, 0xff, //0x00000173 movdqu       $-236(%rip), %xmm9  /* LCPI0_9+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x3d, 0x6b, 0xff, 0xff, 0xff, //0x0000017c movdqu       $-149(%rip), %xmm15  /* LCPI0_15+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x35, 0xf2, 0xfe, 0xff, 0xff, //0x00000185 movdqu       $-270(%rip), %xmm14  /* LCPI0_8+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x05, 0xd9, 0xfe, 0xff, 0xff, //0x0000018e movdqu       $-295(%rip), %xmm8  /* LCPI0_7+0(%rip) */
	0x48, 0x89, 0x55, 0xc0, //0x00000197 movq         %rdx, $-64(%rbp)
	0xe9, 0x44, 0x00, 0x00, 0x00, //0x0000019b jmp          LBB0_5
	//0x000001a0 LBB0_1
	0x4c, 0x8d, 0x5e, 0x04, //0x000001a0 leaq         $4(%rsi), %r11
	//0x000001a4 LBB0_2
	0x48, 0x8b, 0x45, 0xa8, //0x000001a4 movq         $-88(%rbp), %rax
	0x4c, 0x89, 0x18, //0x000001a8 movq         %r11, (%rax)
	0x48, 0x89, 0xf0, //0x000001ab movq         %rsi, %rax
	0x48, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x000001ae movabsq      $9223372036854775806, %rcx
	0x48, 0x39, 0xce, //0x000001b8 cmpq         %rcx, %rsi
	0x0f, 0x87, 0x32, 0x35, 0x00, 0x00, //0x000001bb ja           LBB0_638
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000001c1 .p2align 4, 0x90
	//0x000001d0 LBB0_3
	0x49, 0x8b, 0x4d, 0x00, //0x000001d0 movq         (%r13), %rcx
	0x49, 0x89, 0xc8, //0x000001d4 movq         %rcx, %r8
	0x48, 0x8b, 0x45, 0x90, //0x000001d7 movq         $-112(%rbp), %rax
	0x48, 0x85, 0xc9, //0x000001db testq        %rcx, %rcx
	0x0f, 0x84, 0x0f, 0x35, 0x00, 0x00, //0x000001de je           LBB0_638
	//0x000001e4 LBB0_5
	0x48, 0x8b, 0x45, 0xa0, //0x000001e4 movq         $-96(%rbp), %rax
	0x4c, 0x8b, 0x08, //0x000001e8 movq         (%rax), %r9
	0x48, 0x8b, 0x40, 0x08, //0x000001eb movq         $8(%rax), %rax
	0x49, 0x39, 0xc3, //0x000001ef cmpq         %rax, %r11
	0x0f, 0x83, 0x38, 0x00, 0x00, 0x00, //0x000001f2 jae          LBB0_10
	0x43, 0x8a, 0x14, 0x19, //0x000001f8 movb         (%r9,%r11), %dl
	0x80, 0xfa, 0x0d, //0x000001fc cmpb         $13, %dl
	0x0f, 0x84, 0x2b, 0x00, 0x00, 0x00, //0x000001ff je           LBB0_10
	0x80, 0xfa, 0x20, //0x00000205 cmpb         $32, %dl
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x00000208 je           LBB0_10
	0x80, 0xc2, 0xf5, //0x0000020e addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00000211 cmpb         $-2, %dl
	0x0f, 0x83, 0x16, 0x00, 0x00, 0x00, //0x00000214 jae          LBB0_10
	0x4c, 0x89, 0xde, //0x0000021a movq         %r11, %rsi
	0xe9, 0x16, 0x01, 0x00, 0x00, //0x0000021d jmp          LBB0_31
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000222 .p2align 4, 0x90
	//0x00000230 LBB0_10
	0x49, 0x8d, 0x73, 0x01, //0x00000230 leaq         $1(%r11), %rsi
	0x48, 0x39, 0xc6, //0x00000234 cmpq         %rax, %rsi
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000237 jae          LBB0_14
	0x41, 0x8a, 0x14, 0x31, //0x0000023d movb         (%r9,%rsi), %dl
	0x80, 0xfa, 0x0d, //0x00000241 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000244 je           LBB0_14
	0x80, 0xfa, 0x20, //0x0000024a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000024d je           LBB0_14
	0x80, 0xc2, 0xf5, //0x00000253 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00000256 cmpb         $-2, %dl
	0x0f, 0x82, 0xd9, 0x00, 0x00, 0x00, //0x00000259 jb           LBB0_31
	0x90, //0x0000025f .p2align 4, 0x90
	//0x00000260 LBB0_14
	0x49, 0x8d, 0x73, 0x02, //0x00000260 leaq         $2(%r11), %rsi
	0x48, 0x39, 0xc6, //0x00000264 cmpq         %rax, %rsi
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000267 jae          LBB0_18
	0x41, 0x8a, 0x14, 0x31, //0x0000026d movb         (%r9,%rsi), %dl
	0x80, 0xfa, 0x0d, //0x00000271 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000274 je           LBB0_18
	0x80, 0xfa, 0x20, //0x0000027a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000027d je           LBB0_18
	0x80, 0xc2, 0xf5, //0x00000283 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00000286 cmpb         $-2, %dl
	0x0f, 0x82, 0xa9, 0x00, 0x00, 0x00, //0x00000289 jb           LBB0_31
	0x90, //0x0000028f .p2align 4, 0x90
	//0x00000290 LBB0_18
	0x49, 0x8d, 0x73, 0x03, //0x00000290 leaq         $3(%r11), %rsi
	0x48, 0x39, 0xc6, //0x00000294 cmpq         %rax, %rsi
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000297 jae          LBB0_22
	0x41, 0x8a, 0x14, 0x31, //0x0000029d movb         (%r9,%rsi), %dl
	0x80, 0xfa, 0x0d, //0x000002a1 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000002a4 je           LBB0_22
	0x80, 0xfa, 0x20, //0x000002aa cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000002ad je           LBB0_22
	0x80, 0xc2, 0xf5, //0x000002b3 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x000002b6 cmpb         $-2, %dl
	0x0f, 0x82, 0x79, 0x00, 0x00, 0x00, //0x000002b9 jb           LBB0_31
	0x90, //0x000002bf .p2align 4, 0x90
	//0x000002c0 LBB0_22
	0x49, 0x83, 0xc3, 0x04, //0x000002c0 addq         $4, %r11
	0x4c, 0x39, 0xd8, //0x000002c4 cmpq         %r11, %rax
	0x0f, 0x86, 0xc7, 0x33, 0x00, 0x00, //0x000002c7 jbe          LBB0_617
	0x0f, 0x84, 0x4d, 0x00, 0x00, 0x00, //0x000002cd je           LBB0_28
	0x49, 0x8d, 0x14, 0x01, //0x000002d3 leaq         (%r9,%rax), %rdx
	0x48, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000002d7 movabsq      $4294977024, %rdi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002e1 .p2align 4, 0x90
	//0x000002f0 LBB0_25
	0x43, 0x0f, 0xbe, 0x34, 0x19, //0x000002f0 movsbl       (%r9,%r11), %esi
	0x83, 0xfe, 0x20, //0x000002f5 cmpl         $32, %esi
	0x0f, 0x87, 0x2e, 0x00, 0x00, 0x00, //0x000002f8 ja           LBB0_30
	0x48, 0x0f, 0xa3, 0xf7, //0x000002fe btq          %rsi, %rdi
	0x0f, 0x83, 0x24, 0x00, 0x00, 0x00, //0x00000302 jae          LBB0_30
	0x49, 0x83, 0xc3, 0x01, //0x00000308 addq         $1, %r11
	0x4c, 0x39, 0xd8, //0x0000030c cmpq         %r11, %rax
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x0000030f jne          LBB0_25
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x00000315 jmp          LBB0_29
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000031a .p2align 4, 0x90
	//0x00000320 LBB0_28
	0x4d, 0x01, 0xcb, //0x00000320 addq         %r9, %r11
	0x4c, 0x89, 0xda, //0x00000323 movq         %r11, %rdx
	//0x00000326 LBB0_29
	0x4c, 0x29, 0xca, //0x00000326 subq         %r9, %rdx
	0x49, 0x89, 0xd3, //0x00000329 movq         %rdx, %r11
	//0x0000032c LBB0_30
	0x4c, 0x89, 0xde, //0x0000032c movq         %r11, %rsi
	0x49, 0x39, 0xc3, //0x0000032f cmpq         %rax, %r11
	0x0f, 0x83, 0x63, 0x33, 0x00, 0x00, //0x00000332 jae          LBB0_618
	//0x00000338 LBB0_31
	0x4c, 0x8d, 0x5e, 0x01, //0x00000338 leaq         $1(%rsi), %r11
	0x48, 0x8b, 0x45, 0xa8, //0x0000033c movq         $-88(%rbp), %rax
	0x4c, 0x89, 0x18, //0x00000340 movq         %r11, (%rax)
	0x41, 0x0f, 0xbe, 0x14, 0x31, //0x00000343 movsbl       (%r9,%rsi), %edx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000348 movq         $-1, %rax
	0x85, 0xd2, //0x0000034f testl        %edx, %edx
	0x0f, 0x84, 0x9c, 0x33, 0x00, 0x00, //0x00000351 je           LBB0_638
	0x49, 0x8d, 0x48, 0xff, //0x00000357 leaq         $-1(%r8), %rcx
	0x43, 0x8b, 0x7c, 0xc5, 0x00, //0x0000035b movl         (%r13,%r8,8), %edi
	0x48, 0x8b, 0x5d, 0x90, //0x00000360 movq         $-112(%rbp), %rbx
	0x48, 0x83, 0xfb, 0xff, //0x00000364 cmpq         $-1, %rbx
	0x48, 0x0f, 0x44, 0xde, //0x00000368 cmoveq       %rsi, %rbx
	0x48, 0x89, 0x5d, 0x90, //0x0000036c movq         %rbx, $-112(%rbp)
	0x83, 0xc7, 0xff, //0x00000370 addl         $-1, %edi
	0x83, 0xff, 0x05, //0x00000373 cmpl         $5, %edi
	0x0f, 0x87, 0x7c, 0x02, 0x00, 0x00, //0x00000376 ja           LBB0_66
	0x48, 0x8d, 0x1d, 0x11, 0x36, 0x00, 0x00, //0x0000037c leaq         $13841(%rip), %rbx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x3c, 0xbb, //0x00000383 movslq       (%rbx,%rdi,4), %rdi
	0x48, 0x01, 0xdf, //0x00000387 addq         %rbx, %rdi
	0xff, 0xe7, //0x0000038a jmpq         *%rdi
	//0x0000038c LBB0_34
	0x83, 0xfa, 0x2c, //0x0000038c cmpl         $44, %edx
	0x0f, 0x84, 0xdc, 0x04, 0x00, 0x00, //0x0000038f je           LBB0_105
	0x83, 0xfa, 0x5d, //0x00000395 cmpl         $93, %edx
	0x0f, 0x84, 0x41, 0x02, 0x00, 0x00, //0x00000398 je           LBB0_36
	0xe9, 0x49, 0x33, 0x00, 0x00, //0x0000039e jmp          LBB0_637
	//0x000003a3 LBB0_37
	0x80, 0xfa, 0x5d, //0x000003a3 cmpb         $93, %dl
	0x0f, 0x84, 0x33, 0x02, 0x00, 0x00, //0x000003a6 je           LBB0_36
	0x48, 0x89, 0x75, 0xc8, //0x000003ac movq         %rsi, $-56(%rbp)
	0x4b, 0xc7, 0x44, 0xc5, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000003b0 movq         $1, (%r13,%r8,8)
	0x83, 0xfa, 0x7b, //0x000003b9 cmpl         $123, %edx
	0x0f, 0x86, 0x47, 0x02, 0x00, 0x00, //0x000003bc jbe          LBB0_67
	0xe9, 0x25, 0x33, 0x00, 0x00, //0x000003c2 jmp          LBB0_637
	//0x000003c7 LBB0_39
	0x80, 0xfa, 0x22, //0x000003c7 cmpb         $34, %dl
	0x0f, 0x85, 0x1c, 0x33, 0x00, 0x00, //0x000003ca jne          LBB0_637
	0x4b, 0xc7, 0x44, 0xc5, 0x00, 0x04, 0x00, 0x00, 0x00, //0x000003d0 movq         $4, (%r13,%r8,8)
	0x48, 0x8b, 0x4d, 0xa0, //0x000003d9 movq         $-96(%rbp), %rcx
	0x4c, 0x8b, 0x61, 0x08, //0x000003dd movq         $8(%rcx), %r12
	0x48, 0x8b, 0x4d, 0x98, //0x000003e1 movq         $-104(%rbp), %rcx
	0xf6, 0xc1, 0x40, //0x000003e5 testb        $64, %cl
	0x0f, 0x85, 0x5c, 0x06, 0x00, 0x00, //0x000003e8 jne          LBB0_129
	0x48, 0x89, 0x75, 0xc8, //0x000003ee movq         %rsi, $-56(%rbp)
	0xf6, 0xc1, 0x20, //0x000003f2 testb        $32, %cl
	0x4c, 0x89, 0x65, 0xb8, //0x000003f5 movq         %r12, $-72(%rbp)
	0x4d, 0x89, 0xe6, //0x000003f9 movq         %r12, %r14
	0x0f, 0x85, 0x4a, 0x09, 0x00, 0x00, //0x000003fc jne          LBB0_164
	0x4d, 0x29, 0xde, //0x00000402 subq         %r11, %r14
	0x0f, 0x84, 0xbe, 0x34, 0x00, 0x00, //0x00000405 je           LBB0_639
	0x49, 0x83, 0xfe, 0x40, //0x0000040b cmpq         $64, %r14
	0x0f, 0x82, 0x8b, 0x2a, 0x00, 0x00, //0x0000040f jb           LBB0_523
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x00000415 movq         $-2, %r12
	0x4c, 0x2b, 0x65, 0xc8, //0x0000041c subq         $-56(%rbp), %r12
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x00000420 movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xd2, //0x00000428 xorl         %r10d, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000042b .p2align 4, 0x90
	//0x00000430 LBB0_45
	0xf3, 0x43, 0x0f, 0x6f, 0x04, 0x19, //0x00000430 movdqu       (%r9,%r11), %xmm0
	0xf3, 0x43, 0x0f, 0x6f, 0x4c, 0x19, 0x10, //0x00000436 movdqu       $16(%r9,%r11), %xmm1
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x19, 0x20, //0x0000043d movdqu       $32(%r9,%r11), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x19, 0x30, //0x00000444 movdqu       $48(%r9,%r11), %xmm3
	0x66, 0x0f, 0x6f, 0xe0, //0x0000044b movdqa       %xmm0, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x0000044f pcmpeqb      %xmm11, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xc4, //0x00000454 pmovmskb     %xmm4, %r8d
	0x66, 0x0f, 0x6f, 0xe1, //0x00000459 movdqa       %xmm1, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x0000045d pcmpeqb      %xmm11, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x00000462 pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x6f, 0xe2, //0x00000466 movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x0000046a pcmpeqb      %xmm11, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x0000046f pmovmskb     %xmm4, %ecx
	0x66, 0x0f, 0x6f, 0xe3, //0x00000473 movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x00000477 pcmpeqb      %xmm11, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x0000047c pmovmskb     %xmm4, %edi
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00000480 pcmpeqb      %xmm13, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xf8, //0x00000485 pmovmskb     %xmm0, %r15d
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x0000048a pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xd1, //0x0000048f pmovmskb     %xmm1, %edx
	0x66, 0x41, 0x0f, 0x74, 0xd5, //0x00000493 pcmpeqb      %xmm13, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00000498 pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x0000049c pcmpeqb      %xmm13, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x000004a1 pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe7, 0x30, //0x000004a5 shlq         $48, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x000004a9 shlq         $32, %rcx
	0x48, 0x09, 0xf9, //0x000004ad orq          %rdi, %rcx
	0x48, 0xc1, 0xe3, 0x10, //0x000004b0 shlq         $16, %rbx
	0x48, 0x09, 0xcb, //0x000004b4 orq          %rcx, %rbx
	0x49, 0x09, 0xd8, //0x000004b7 orq          %rbx, %r8
	0x48, 0xc1, 0xe6, 0x30, //0x000004ba shlq         $48, %rsi
	0x48, 0xc1, 0xe0, 0x20, //0x000004be shlq         $32, %rax
	0x48, 0x09, 0xf0, //0x000004c2 orq          %rsi, %rax
	0x48, 0xc1, 0xe2, 0x10, //0x000004c5 shlq         $16, %rdx
	0x48, 0x09, 0xc2, //0x000004c9 orq          %rax, %rdx
	0x49, 0x09, 0xd7, //0x000004cc orq          %rdx, %r15
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x000004cf jne          LBB0_54
	0x4d, 0x85, 0xd2, //0x000004d5 testq        %r10, %r10
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x000004d8 jne          LBB0_56
	0x45, 0x31, 0xd2, //0x000004de xorl         %r10d, %r10d
	0x4d, 0x85, 0xc0, //0x000004e1 testq        %r8, %r8
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x000004e4 jne          LBB0_57
	//0x000004ea LBB0_48
	0x49, 0x83, 0xc6, 0xc0, //0x000004ea addq         $-64, %r14
	0x49, 0x83, 0xc4, 0xc0, //0x000004ee addq         $-64, %r12
	0x49, 0x83, 0xc3, 0x40, //0x000004f2 addq         $64, %r11
	0x49, 0x83, 0xfe, 0x3f, //0x000004f6 cmpq         $63, %r14
	0x0f, 0x87, 0x30, 0xff, 0xff, 0xff, //0x000004fa ja           LBB0_45
	0xe9, 0x9b, 0x22, 0x00, 0x00, //0x00000500 jmp          LBB0_49
	//0x00000505 LBB0_54
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x00000505 cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x0000050a jne          LBB0_56
	0x49, 0x0f, 0xbc, 0xc7, //0x00000510 bsfq         %r15, %rax
	0x4c, 0x01, 0xd8, //0x00000514 addq         %r11, %rax
	0x48, 0x89, 0x45, 0xd0, //0x00000517 movq         %rax, $-48(%rbp)
	//0x0000051b LBB0_56
	0x4c, 0x89, 0xd0, //0x0000051b movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x0000051e notq         %rax
	0x4c, 0x21, 0xf8, //0x00000521 andq         %r15, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00000524 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd1, //0x00000528 orq          %r10, %rcx
	0x48, 0x89, 0xca, //0x0000052b movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x0000052e notq         %rdx
	0x4c, 0x21, 0xfa, //0x00000531 andq         %r15, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000534 movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x0000053e andq         %rsi, %rdx
	0x45, 0x31, 0xd2, //0x00000541 xorl         %r10d, %r10d
	0x48, 0x01, 0xc2, //0x00000544 addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc2, //0x00000547 setb         %r10b
	0x48, 0x01, 0xd2, //0x0000054b addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000054e movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00000558 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x0000055b andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x0000055e notq         %rdx
	0x49, 0x21, 0xd0, //0x00000561 andq         %rdx, %r8
	0x4d, 0x85, 0xc0, //0x00000564 testq        %r8, %r8
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00000567 je           LBB0_48
	//0x0000056d LBB0_57
	0x4d, 0x0f, 0xbc, 0xd8, //0x0000056d bsfq         %r8, %r11
	0x4d, 0x29, 0xe3, //0x00000571 subq         %r12, %r11
	0x48, 0x8b, 0x55, 0xc8, //0x00000574 movq         $-56(%rbp), %rdx
	0x4c, 0x8b, 0x65, 0xb8, //0x00000578 movq         $-72(%rbp), %r12
	0x4d, 0x85, 0xdb, //0x0000057c testq        %r11, %r11
	0x0f, 0x89, 0xd2, 0x0c, 0x00, 0x00, //0x0000057f jns          LBB0_246
	0xe9, 0x29, 0x31, 0x00, 0x00, //0x00000585 jmp          LBB0_58
	//0x0000058a LBB0_60
	0x80, 0xfa, 0x3a, //0x0000058a cmpb         $58, %dl
	0x0f, 0x85, 0x59, 0x31, 0x00, 0x00, //0x0000058d jne          LBB0_637
	0x4b, 0xc7, 0x44, 0xc5, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000593 movq         $0, (%r13,%r8,8)
	0xe9, 0x2f, 0xfc, 0xff, 0xff, //0x0000059c jmp          LBB0_3
	//0x000005a1 LBB0_62
	0x83, 0xfa, 0x2c, //0x000005a1 cmpl         $44, %edx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x000005a4 jne          LBB0_63
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x000005aa cmpq         $4095, %r8
	0x0f, 0x8f, 0xf0, 0x30, 0x00, 0x00, //0x000005b1 jg           LBB0_634
	0x49, 0x8d, 0x40, 0x01, //0x000005b7 leaq         $1(%r8), %rax
	0x49, 0x89, 0x45, 0x00, //0x000005bb movq         %rax, (%r13)
	0x4b, 0xc7, 0x44, 0xc5, 0x08, 0x03, 0x00, 0x00, 0x00, //0x000005bf movq         $3, $8(%r13,%r8,8)
	0xe9, 0x03, 0xfc, 0xff, 0xff, //0x000005c8 jmp          LBB0_3
	//0x000005cd LBB0_64
	0x83, 0xfa, 0x22, //0x000005cd cmpl         $34, %edx
	0x0f, 0x84, 0xbe, 0x02, 0x00, 0x00, //0x000005d0 je           LBB0_109
	//0x000005d6 LBB0_63
	0x83, 0xfa, 0x7d, //0x000005d6 cmpl         $125, %edx
	0x0f, 0x85, 0x0d, 0x31, 0x00, 0x00, //0x000005d9 jne          LBB0_637
	//0x000005df LBB0_36
	0x49, 0x89, 0x4d, 0x00, //0x000005df movq         %rcx, (%r13)
	0x49, 0x89, 0xc8, //0x000005e3 movq         %rcx, %r8
	0x48, 0x8b, 0x45, 0x90, //0x000005e6 movq         $-112(%rbp), %rax
	0x48, 0x85, 0xc9, //0x000005ea testq        %rcx, %rcx
	0x0f, 0x85, 0xf1, 0xfb, 0xff, 0xff, //0x000005ed jne          LBB0_5
	0xe9, 0xfb, 0x30, 0x00, 0x00, //0x000005f3 jmp          LBB0_638
	//0x000005f8 LBB0_66
	0x48, 0x89, 0x75, 0xc8, //0x000005f8 movq         %rsi, $-56(%rbp)
	0x49, 0x89, 0x4d, 0x00, //0x000005fc movq         %rcx, (%r13)
	0x83, 0xfa, 0x7b, //0x00000600 cmpl         $123, %edx
	0x0f, 0x87, 0xe3, 0x30, 0x00, 0x00, //0x00000603 ja           LBB0_637
	//0x00000609 LBB0_67
	0x48, 0x8b, 0x4d, 0xc8, //0x00000609 movq         $-56(%rbp), %rcx
	0x4d, 0x8d, 0x04, 0x09, //0x0000060d leaq         (%r9,%rcx), %r8
	0x89, 0xd1, //0x00000611 movl         %edx, %ecx
	0x48, 0x8d, 0x15, 0x92, 0x33, 0x00, 0x00, //0x00000613 leaq         $13202(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x0c, 0x8a, //0x0000061a movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x0000061e addq         %rdx, %rcx
	0xff, 0xe1, //0x00000621 jmpq         *%rcx
	//0x00000623 LBB0_68
	0x48, 0x8b, 0x45, 0xa0, //0x00000623 movq         $-96(%rbp), %rax
	0x48, 0x8b, 0x70, 0x08, //0x00000627 movq         $8(%rax), %rsi
	0xf6, 0x45, 0x98, 0x40, //0x0000062b testb        $64, $-104(%rbp)
	0x0f, 0x85, 0x31, 0x05, 0x00, 0x00, //0x0000062f jne          LBB0_140
	0x48, 0x8b, 0x55, 0xc8, //0x00000635 movq         $-56(%rbp), %rdx
	0x48, 0x29, 0xd6, //0x00000639 subq         %rdx, %rsi
	0x0f, 0x84, 0x8e, 0x30, 0x00, 0x00, //0x0000063c je           LBB0_619
	0x41, 0x80, 0x38, 0x30, //0x00000642 cmpb         $48, (%r8)
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x00000646 jne          LBB0_74
	0x48, 0x83, 0xfe, 0x01, //0x0000064c cmpq         $1, %rsi
	0x0f, 0x84, 0xc7, 0x0b, 0x00, 0x00, //0x00000650 je           LBB0_243
	0x43, 0x8a, 0x04, 0x19, //0x00000656 movb         (%r9,%r11), %al
	0x04, 0xd2, //0x0000065a addb         $-46, %al
	0x3c, 0x37, //0x0000065c cmpb         $55, %al
	0x0f, 0x87, 0xb9, 0x0b, 0x00, 0x00, //0x0000065e ja           LBB0_243
	0x0f, 0xb6, 0xc0, //0x00000664 movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000667 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00000671 btq          %rax, %rcx
	0x0f, 0x83, 0xa2, 0x0b, 0x00, 0x00, //0x00000675 jae          LBB0_243
	//0x0000067b LBB0_74
	0x48, 0x89, 0x55, 0xc8, //0x0000067b movq         %rdx, $-56(%rbp)
	0x48, 0x83, 0xfe, 0x10, //0x0000067f cmpq         $16, %rsi
	0x0f, 0x82, 0x56, 0x27, 0x00, 0x00, //0x00000683 jb           LBB0_510
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00000689 movq         $-1, %r13
	0x45, 0x31, 0xdb, //0x00000690 xorl         %r11d, %r11d
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00000693 movq         $-1, %r10
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000069a movq         $-1, %r12
	0x48, 0x89, 0xf0, //0x000006a1 movq         %rsi, %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000006a4 .p2align 4, 0x90
	//0x000006b0 LBB0_76
	0xf3, 0x43, 0x0f, 0x6f, 0x04, 0x18, //0x000006b0 movdqu       (%r8,%r11), %xmm0
	0x66, 0x0f, 0x6f, 0xc8, //0x000006b6 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x74, 0xcf, //0x000006ba pcmpeqb      %xmm7, %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x000006be movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0x15, 0xe6, 0xf9, 0xff, 0xff, //0x000006c2 pcmpeqb      $-1562(%rip), %xmm2  /* LCPI0_11+0(%rip) */
	0x66, 0x0f, 0xeb, 0xd1, //0x000006ca por          %xmm1, %xmm2
	0x66, 0x0f, 0x6f, 0xc8, //0x000006ce movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0xfc, 0x0d, 0xe6, 0xf9, 0xff, 0xff, //0x000006d2 paddb        $-1562(%rip), %xmm1  /* LCPI0_12+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd9, //0x000006da movdqa       %xmm1, %xmm3
	0x66, 0x0f, 0xda, 0x1d, 0xea, 0xf9, 0xff, 0xff, //0x000006de pminub       $-1558(%rip), %xmm3  /* LCPI0_13+0(%rip) */
	0x66, 0x0f, 0x74, 0xd9, //0x000006e6 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0x6f, 0xc8, //0x000006ea movdqa       %xmm0, %xmm1
	0x66, 0x41, 0x0f, 0xdb, 0xc9, //0x000006ee pand         %xmm9, %xmm1
	0x66, 0x0f, 0x74, 0x05, 0xe5, 0xf9, 0xff, 0xff, //0x000006f3 pcmpeqb      $-1563(%rip), %xmm0  /* LCPI0_14+0(%rip) */
	0x66, 0x41, 0x0f, 0x74, 0xcf, //0x000006fb pcmpeqb      %xmm15, %xmm1
	0x66, 0x0f, 0xd7, 0xf9, //0x00000700 pmovmskb     %xmm1, %edi
	0x66, 0x0f, 0xeb, 0xc8, //0x00000704 por          %xmm0, %xmm1
	0x66, 0x0f, 0xeb, 0xca, //0x00000708 por          %xmm2, %xmm1
	0x66, 0x0f, 0xeb, 0xcb, //0x0000070c por          %xmm3, %xmm1
	0x66, 0x0f, 0xd7, 0xd0, //0x00000710 pmovmskb     %xmm0, %edx
	0x66, 0x44, 0x0f, 0xd7, 0xf2, //0x00000714 pmovmskb     %xmm2, %r14d
	0x66, 0x0f, 0xd7, 0xc9, //0x00000719 pmovmskb     %xmm1, %ecx
	0xf7, 0xd1, //0x0000071d notl         %ecx
	0x0f, 0xbc, 0xc9, //0x0000071f bsfl         %ecx, %ecx
	0x83, 0xf9, 0x10, //0x00000722 cmpl         $16, %ecx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000725 je           LBB0_78
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x0000072b movl         $-1, %ebx
	0xd3, 0xe3, //0x00000730 shll         %cl, %ebx
	0xf7, 0xd3, //0x00000732 notl         %ebx
	0x21, 0xda, //0x00000734 andl         %ebx, %edx
	0x21, 0xdf, //0x00000736 andl         %ebx, %edi
	0x44, 0x21, 0xf3, //0x00000738 andl         %r14d, %ebx
	0x41, 0x89, 0xde, //0x0000073b movl         %ebx, %r14d
	//0x0000073e LBB0_78
	0x44, 0x8d, 0x7a, 0xff, //0x0000073e leal         $-1(%rdx), %r15d
	0x41, 0x21, 0xd7, //0x00000742 andl         %edx, %r15d
	0x0f, 0x85, 0xcd, 0x21, 0x00, 0x00, //0x00000745 jne          LBB0_474
	0x8d, 0x5f, 0xff, //0x0000074b leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x0000074e andl         %edi, %ebx
	0x0f, 0x85, 0xb0, 0x1f, 0x00, 0x00, //0x00000750 jne          LBB0_470
	0x41, 0x8d, 0x5e, 0xff, //0x00000756 leal         $-1(%r14), %ebx
	0x44, 0x21, 0xf3, //0x0000075a andl         %r14d, %ebx
	0x0f, 0x85, 0xa3, 0x1f, 0x00, 0x00, //0x0000075d jne          LBB0_470
	0x85, 0xd2, //0x00000763 testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000765 je           LBB0_84
	0x0f, 0xbc, 0xd2, //0x0000076b bsfl         %edx, %edx
	0x49, 0x83, 0xfc, 0xff, //0x0000076e cmpq         $-1, %r12
	0x0f, 0x85, 0x96, 0x1f, 0x00, 0x00, //0x00000772 jne          LBB0_471
	0x4c, 0x01, 0xda, //0x00000778 addq         %r11, %rdx
	0x49, 0x89, 0xd4, //0x0000077b movq         %rdx, %r12
	//0x0000077e LBB0_84
	0x85, 0xff, //0x0000077e testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000780 je           LBB0_87
	0x0f, 0xbc, 0xd7, //0x00000786 bsfl         %edi, %edx
	0x49, 0x83, 0xfa, 0xff, //0x00000789 cmpq         $-1, %r10
	0x0f, 0x85, 0x7b, 0x1f, 0x00, 0x00, //0x0000078d jne          LBB0_471
	0x4c, 0x01, 0xda, //0x00000793 addq         %r11, %rdx
	0x49, 0x89, 0xd2, //0x00000796 movq         %rdx, %r10
	//0x00000799 LBB0_87
	0x45, 0x85, 0xf6, //0x00000799 testl        %r14d, %r14d
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000079c je           LBB0_90
	0x41, 0x0f, 0xbc, 0xd6, //0x000007a2 bsfl         %r14d, %edx
	0x49, 0x83, 0xfd, 0xff, //0x000007a6 cmpq         $-1, %r13
	0x0f, 0x85, 0x5e, 0x1f, 0x00, 0x00, //0x000007aa jne          LBB0_471
	0x4c, 0x01, 0xda, //0x000007b0 addq         %r11, %rdx
	0x49, 0x89, 0xd5, //0x000007b3 movq         %rdx, %r13
	//0x000007b6 LBB0_90
	0x83, 0xf9, 0x10, //0x000007b6 cmpl         $16, %ecx
	0x0f, 0x85, 0xa0, 0x07, 0x00, 0x00, //0x000007b9 jne          LBB0_196
	0x48, 0x83, 0xc0, 0xf0, //0x000007bf addq         $-16, %rax
	0x49, 0x83, 0xc3, 0x10, //0x000007c3 addq         $16, %r11
	0x48, 0x83, 0xf8, 0x0f, //0x000007c7 cmpq         $15, %rax
	0x0f, 0x87, 0xdf, 0xfe, 0xff, 0xff, //0x000007cb ja           LBB0_76
	0x4b, 0x8d, 0x0c, 0x18, //0x000007d1 leaq         (%r8,%r11), %rcx
	0x4c, 0x39, 0xde, //0x000007d5 cmpq         %r11, %rsi
	0x49, 0x89, 0xcb, //0x000007d8 movq         %rcx, %r11
	0x48, 0x8d, 0x3d, 0x26, 0x34, 0x00, 0x00, //0x000007db leaq         $13350(%rip), %rdi  /* LJTI0_3+0(%rip) */
	0x0f, 0x84, 0x7f, 0x07, 0x00, 0x00, //0x000007e2 je           LBB0_197
	//0x000007e8 LBB0_93
	0x4c, 0x8d, 0x1c, 0x01, //0x000007e8 leaq         (%rcx,%rax), %r11
	0x49, 0x89, 0xce, //0x000007ec movq         %rcx, %r14
	0x4d, 0x29, 0xc6, //0x000007ef subq         %r8, %r14
	0x31, 0xf6, //0x000007f2 xorl         %esi, %esi
	0xe9, 0x24, 0x00, 0x00, 0x00, //0x000007f4 jmp          LBB0_97
	//0x000007f9 LBB0_94
	0x49, 0x83, 0xfd, 0xff, //0x000007f9 cmpq         $-1, %r13
	0x0f, 0x85, 0xde, 0x1e, 0x00, 0x00, //0x000007fd jne          LBB0_469
	0x4d, 0x8d, 0x2c, 0x36, //0x00000803 leaq         (%r14,%rsi), %r13
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000807 .p2align 4, 0x90
	//0x00000810 LBB0_96
	0x48, 0x83, 0xc6, 0x01, //0x00000810 addq         $1, %rsi
	0x48, 0x39, 0xf0, //0x00000814 cmpq         %rsi, %rax
	0x0f, 0x84, 0x4a, 0x07, 0x00, 0x00, //0x00000817 je           LBB0_197
	//0x0000081d LBB0_97
	0x0f, 0xbe, 0x14, 0x31, //0x0000081d movsbl       (%rcx,%rsi), %edx
	0x8d, 0x5a, 0xd0, //0x00000821 leal         $-48(%rdx), %ebx
	0x83, 0xfb, 0x0a, //0x00000824 cmpl         $10, %ebx
	0x0f, 0x82, 0xe3, 0xff, 0xff, 0xff, //0x00000827 jb           LBB0_96
	0x8d, 0x5a, 0xd5, //0x0000082d leal         $-43(%rdx), %ebx
	0x83, 0xfb, 0x1a, //0x00000830 cmpl         $26, %ebx
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x00000833 ja           LBB0_102
	0x48, 0x63, 0x14, 0x9f, //0x00000839 movslq       (%rdi,%rbx,4), %rdx
	0x48, 0x01, 0xfa, //0x0000083d addq         %rdi, %rdx
	0xff, 0xe2, //0x00000840 jmpq         *%rdx
	//0x00000842 LBB0_100
	0x49, 0x83, 0xfc, 0xff, //0x00000842 cmpq         $-1, %r12
	0x0f, 0x85, 0x95, 0x1e, 0x00, 0x00, //0x00000846 jne          LBB0_469
	0x4d, 0x8d, 0x24, 0x36, //0x0000084c leaq         (%r14,%rsi), %r12
	0xe9, 0xbb, 0xff, 0xff, 0xff, //0x00000850 jmp          LBB0_96
	//0x00000855 LBB0_102
	0x83, 0xfa, 0x65, //0x00000855 cmpl         $101, %edx
	0x0f, 0x85, 0x30, 0x09, 0x00, 0x00, //0x00000858 jne          LBB0_232
	//0x0000085e LBB0_103
	0x49, 0x83, 0xfa, 0xff, //0x0000085e cmpq         $-1, %r10
	0x0f, 0x85, 0x79, 0x1e, 0x00, 0x00, //0x00000862 jne          LBB0_469
	0x4d, 0x8d, 0x14, 0x36, //0x00000868 leaq         (%r14,%rsi), %r10
	0xe9, 0x9f, 0xff, 0xff, 0xff, //0x0000086c jmp          LBB0_96
	//0x00000871 LBB0_105
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x00000871 cmpq         $4095, %r8
	0x0f, 0x8f, 0x29, 0x2e, 0x00, 0x00, //0x00000878 jg           LBB0_634
	0x49, 0x8d, 0x40, 0x01, //0x0000087e leaq         $1(%r8), %rax
	0x49, 0x89, 0x45, 0x00, //0x00000882 movq         %rax, (%r13)
	0x4b, 0xc7, 0x44, 0xc5, 0x08, 0x00, 0x00, 0x00, 0x00, //0x00000886 movq         $0, $8(%r13,%r8,8)
	0xe9, 0x3c, 0xf9, 0xff, 0xff, //0x0000088f jmp          LBB0_3
	//0x00000894 LBB0_109
	0x4b, 0xc7, 0x44, 0xc5, 0x00, 0x02, 0x00, 0x00, 0x00, //0x00000894 movq         $2, (%r13,%r8,8)
	0x48, 0x8b, 0x4d, 0xa0, //0x0000089d movq         $-96(%rbp), %rcx
	0x4c, 0x8b, 0x61, 0x08, //0x000008a1 movq         $8(%rcx), %r12
	0x48, 0x8b, 0x4d, 0x98, //0x000008a5 movq         $-104(%rbp), %rcx
	0xf6, 0xc1, 0x40, //0x000008a9 testb        $64, %cl
	0x0f, 0x85, 0x95, 0x03, 0x00, 0x00, //0x000008ac jne          LBB0_154
	0x48, 0x89, 0x75, 0xc8, //0x000008b2 movq         %rsi, $-56(%rbp)
	0xf6, 0xc1, 0x20, //0x000008b6 testb        $32, %cl
	0x4c, 0x89, 0x65, 0xb8, //0x000008b9 movq         %r12, $-72(%rbp)
	0x0f, 0x85, 0xb9, 0x06, 0x00, 0x00, //0x000008bd jne          LBB0_199
	0x4d, 0x89, 0xe6, //0x000008c3 movq         %r12, %r14
	0x4d, 0x29, 0xde, //0x000008c6 subq         %r11, %r14
	0x0f, 0x84, 0x02, 0x30, 0x00, 0x00, //0x000008c9 je           LBB0_640
	0x49, 0x83, 0xfe, 0x40, //0x000008cf cmpq         $64, %r14
	0x0f, 0x82, 0x6e, 0x26, 0x00, 0x00, //0x000008d3 jb           LBB0_529
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000008d9 movq         $-2, %r12
	0x4c, 0x2b, 0x65, 0xc8, //0x000008e0 subq         $-56(%rbp), %r12
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x000008e4 movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xd2, //0x000008ec xorl         %r10d, %r10d
	0x90, //0x000008ef .p2align 4, 0x90
	//0x000008f0 LBB0_114
	0xf3, 0x43, 0x0f, 0x6f, 0x04, 0x19, //0x000008f0 movdqu       (%r9,%r11), %xmm0
	0xf3, 0x43, 0x0f, 0x6f, 0x4c, 0x19, 0x10, //0x000008f6 movdqu       $16(%r9,%r11), %xmm1
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x19, 0x20, //0x000008fd movdqu       $32(%r9,%r11), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x19, 0x30, //0x00000904 movdqu       $48(%r9,%r11), %xmm3
	0x66, 0x0f, 0x6f, 0xe0, //0x0000090b movdqa       %xmm0, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x0000090f pcmpeqb      %xmm11, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xc4, //0x00000914 pmovmskb     %xmm4, %r8d
	0x66, 0x0f, 0x6f, 0xe1, //0x00000919 movdqa       %xmm1, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x0000091d pcmpeqb      %xmm11, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x00000922 pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x6f, 0xe2, //0x00000926 movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x0000092a pcmpeqb      %xmm11, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x0000092f pmovmskb     %xmm4, %ecx
	0x66, 0x0f, 0x6f, 0xe3, //0x00000933 movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x00000937 pcmpeqb      %xmm11, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x0000093c pmovmskb     %xmm4, %edi
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00000940 pcmpeqb      %xmm13, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xf8, //0x00000945 pmovmskb     %xmm0, %r15d
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x0000094a pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xd1, //0x0000094f pmovmskb     %xmm1, %edx
	0x66, 0x41, 0x0f, 0x74, 0xd5, //0x00000953 pcmpeqb      %xmm13, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00000958 pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x0000095c pcmpeqb      %xmm13, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00000961 pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe7, 0x30, //0x00000965 shlq         $48, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00000969 shlq         $32, %rcx
	0x48, 0x09, 0xf9, //0x0000096d orq          %rdi, %rcx
	0x48, 0xc1, 0xe3, 0x10, //0x00000970 shlq         $16, %rbx
	0x48, 0x09, 0xcb, //0x00000974 orq          %rcx, %rbx
	0x49, 0x09, 0xd8, //0x00000977 orq          %rbx, %r8
	0x48, 0xc1, 0xe6, 0x30, //0x0000097a shlq         $48, %rsi
	0x48, 0xc1, 0xe0, 0x20, //0x0000097e shlq         $32, %rax
	0x48, 0x09, 0xf0, //0x00000982 orq          %rsi, %rax
	0x48, 0xc1, 0xe2, 0x10, //0x00000985 shlq         $16, %rdx
	0x48, 0x09, 0xc2, //0x00000989 orq          %rax, %rdx
	0x49, 0x09, 0xd7, //0x0000098c orq          %rdx, %r15
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x0000098f jne          LBB0_123
	0x4d, 0x85, 0xd2, //0x00000995 testq        %r10, %r10
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000998 jne          LBB0_125
	0x45, 0x31, 0xd2, //0x0000099e xorl         %r10d, %r10d
	0x4d, 0x85, 0xc0, //0x000009a1 testq        %r8, %r8
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x000009a4 jne          LBB0_126
	//0x000009aa LBB0_117
	0x49, 0x83, 0xc6, 0xc0, //0x000009aa addq         $-64, %r14
	0x49, 0x83, 0xc4, 0xc0, //0x000009ae addq         $-64, %r12
	0x49, 0x83, 0xc3, 0x40, //0x000009b2 addq         $64, %r11
	0x49, 0x83, 0xfe, 0x3f, //0x000009b6 cmpq         $63, %r14
	0x0f, 0x87, 0x30, 0xff, 0xff, 0xff, //0x000009ba ja           LBB0_114
	0xe9, 0x8b, 0x1f, 0x00, 0x00, //0x000009c0 jmp          LBB0_118
	//0x000009c5 LBB0_123
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x000009c5 cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x000009ca jne          LBB0_125
	0x49, 0x0f, 0xbc, 0xc7, //0x000009d0 bsfq         %r15, %rax
	0x4c, 0x01, 0xd8, //0x000009d4 addq         %r11, %rax
	0x48, 0x89, 0x45, 0xd0, //0x000009d7 movq         %rax, $-48(%rbp)
	//0x000009db LBB0_125
	0x4c, 0x89, 0xd0, //0x000009db movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x000009de notq         %rax
	0x4c, 0x21, 0xf8, //0x000009e1 andq         %r15, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x000009e4 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd1, //0x000009e8 orq          %r10, %rcx
	0x48, 0x89, 0xca, //0x000009eb movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x000009ee notq         %rdx
	0x4c, 0x21, 0xfa, //0x000009f1 andq         %r15, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000009f4 movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x000009fe andq         %rsi, %rdx
	0x45, 0x31, 0xd2, //0x00000a01 xorl         %r10d, %r10d
	0x48, 0x01, 0xc2, //0x00000a04 addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc2, //0x00000a07 setb         %r10b
	0x48, 0x01, 0xd2, //0x00000a0b addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000a0e movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00000a18 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x00000a1b andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00000a1e notq         %rdx
	0x49, 0x21, 0xd0, //0x00000a21 andq         %rdx, %r8
	0x4d, 0x85, 0xc0, //0x00000a24 testq        %r8, %r8
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00000a27 je           LBB0_117
	//0x00000a2d LBB0_126
	0x4d, 0x0f, 0xbc, 0xd8, //0x00000a2d bsfq         %r8, %r11
	0x4d, 0x29, 0xe3, //0x00000a31 subq         %r12, %r11
	0x48, 0x8b, 0x55, 0xc8, //0x00000a34 movq         $-56(%rbp), %rdx
	0x4c, 0x8b, 0x65, 0xb8, //0x00000a38 movq         $-72(%rbp), %r12
	0x4d, 0x85, 0xdb, //0x00000a3c testq        %r11, %r11
	0x0f, 0x89, 0x7b, 0x0d, 0x00, 0x00, //0x00000a3f jns          LBB0_331
	0xe9, 0xbb, 0x2c, 0x00, 0x00, //0x00000a45 jmp          LBB0_127
	//0x00000a4a LBB0_129
	0x49, 0x89, 0xf0, //0x00000a4a movq         %rsi, %r8
	0x4d, 0x89, 0xe6, //0x00000a4d movq         %r12, %r14
	0x4d, 0x29, 0xde, //0x00000a50 subq         %r11, %r14
	0x49, 0x83, 0xfe, 0x20, //0x00000a53 cmpq         $32, %r14
	0x0f, 0x8c, 0x69, 0x23, 0x00, 0x00, //0x00000a57 jl           LBB0_508
	0x4c, 0x89, 0xc1, //0x00000a5d movq         %r8, %rcx
	0x4f, 0x8d, 0x14, 0x01, //0x00000a60 leaq         (%r9,%r8), %r10
	0x4d, 0x29, 0xc4, //0x00000a64 subq         %r8, %r12
	0xbb, 0x1f, 0x00, 0x00, 0x00, //0x00000a67 movl         $31, %ebx
	0x45, 0x31, 0xf6, //0x00000a6c xorl         %r14d, %r14d
	0x45, 0x31, 0xdb, //0x00000a6f xorl         %r11d, %r11d
	0xe9, 0x2f, 0x00, 0x00, 0x00, //0x00000a72 jmp          LBB0_131
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a77 .p2align 4, 0x90
	//0x00000a80 LBB0_135
	0x45, 0x31, 0xdb, //0x00000a80 xorl         %r11d, %r11d
	0x48, 0x85, 0xd2, //0x00000a83 testq        %rdx, %rdx
	0x0f, 0x85, 0xaa, 0x00, 0x00, 0x00, //0x00000a86 jne          LBB0_133
	//0x00000a8c LBB0_136
	0x49, 0x83, 0xc6, 0x20, //0x00000a8c addq         $32, %r14
	0x49, 0x8d, 0x0c, 0x1c, //0x00000a90 leaq         (%r12,%rbx), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00000a94 addq         $-32, %rcx
	0x48, 0x83, 0xc3, 0xe0, //0x00000a98 addq         $-32, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x00000a9c cmpq         $63, %rcx
	0x0f, 0x8e, 0x84, 0x1b, 0x00, 0x00, //0x00000aa0 jle          LBB0_137
	//0x00000aa6 LBB0_131
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x32, 0x01, //0x00000aa6 movdqu       $1(%r10,%r14), %xmm3
	0xf3, 0x43, 0x0f, 0x6f, 0x64, 0x32, 0x11, //0x00000aad movdqu       $17(%r10,%r14), %xmm4
	0x66, 0x0f, 0x6f, 0xeb, //0x00000ab4 movdqa       %xmm3, %xmm5
	0x66, 0x41, 0x0f, 0x74, 0xeb, //0x00000ab8 pcmpeqb      %xmm11, %xmm5
	0x66, 0x0f, 0xd7, 0xcd, //0x00000abd pmovmskb     %xmm5, %ecx
	0x66, 0x0f, 0x6f, 0xec, //0x00000ac1 movdqa       %xmm4, %xmm5
	0x66, 0x41, 0x0f, 0x74, 0xeb, //0x00000ac5 pcmpeqb      %xmm11, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x00000aca pmovmskb     %xmm5, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x00000ace shlq         $16, %rdx
	0x48, 0x09, 0xca, //0x00000ad2 orq          %rcx, %rdx
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x00000ad5 pcmpeqb      %xmm13, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00000ada pmovmskb     %xmm3, %edi
	0x66, 0x41, 0x0f, 0x74, 0xe5, //0x00000ade pcmpeqb      %xmm13, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x00000ae3 pmovmskb     %xmm4, %ecx
	0x48, 0xc1, 0xe1, 0x10, //0x00000ae7 shlq         $16, %rcx
	0x48, 0x09, 0xf9, //0x00000aeb orq          %rdi, %rcx
	0x48, 0x89, 0xcf, //0x00000aee movq         %rcx, %rdi
	0x4c, 0x09, 0xdf, //0x00000af1 orq          %r11, %rdi
	0x0f, 0x84, 0x86, 0xff, 0xff, 0xff, //0x00000af4 je           LBB0_135
	0x44, 0x89, 0xdf, //0x00000afa movl         %r11d, %edi
	0xf7, 0xd7, //0x00000afd notl         %edi
	0x21, 0xcf, //0x00000aff andl         %ecx, %edi
	0x44, 0x8d, 0x3c, 0x3f, //0x00000b01 leal         (%rdi,%rdi), %r15d
	0x45, 0x09, 0xdf, //0x00000b05 orl          %r11d, %r15d
	0x44, 0x89, 0xfe, //0x00000b08 movl         %r15d, %esi
	0xf7, 0xd6, //0x00000b0b notl         %esi
	0x21, 0xce, //0x00000b0d andl         %ecx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000b0f andl         $-1431655766, %esi
	0x45, 0x31, 0xdb, //0x00000b15 xorl         %r11d, %r11d
	0x01, 0xfe, //0x00000b18 addl         %edi, %esi
	0x41, 0x0f, 0x92, 0xc3, //0x00000b1a setb         %r11b
	0x01, 0xf6, //0x00000b1e addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x00000b20 xorl         $1431655765, %esi
	0x44, 0x21, 0xfe, //0x00000b26 andl         %r15d, %esi
	0xf7, 0xd6, //0x00000b29 notl         %esi
	0x21, 0xf2, //0x00000b2b andl         %esi, %edx
	0x48, 0x85, 0xd2, //0x00000b2d testq        %rdx, %rdx
	0x0f, 0x84, 0x56, 0xff, 0xff, 0xff, //0x00000b30 je           LBB0_136
	//0x00000b36 LBB0_133
	0x0f, 0xbc, 0xc2, //0x00000b36 bsfl         %edx, %eax
	0x4c, 0x01, 0xc0, //0x00000b39 addq         %r8, %rax
	0x4d, 0x8d, 0x1c, 0x06, //0x00000b3c leaq         (%r14,%rax), %r11
	0x49, 0x83, 0xc3, 0x02, //0x00000b40 addq         $2, %r11
	//0x00000b44 LBB0_134
	0x48, 0x8b, 0x45, 0xa8, //0x00000b44 movq         $-88(%rbp), %rax
	0x4c, 0x89, 0x18, //0x00000b48 movq         %r11, (%rax)
	0x4c, 0x89, 0xc0, //0x00000b4b movq         %r8, %rax
	0x48, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00000b4e movabsq      $9223372036854775806, %rcx
	0x49, 0x39, 0xc8, //0x00000b58 cmpq         %rcx, %r8
	0x0f, 0x86, 0x6f, 0xf6, 0xff, 0xff, //0x00000b5b jbe          LBB0_3
	0xe9, 0x8d, 0x2b, 0x00, 0x00, //0x00000b61 jmp          LBB0_638
	//0x00000b66 LBB0_140
	0x48, 0x89, 0xf0, //0x00000b66 movq         %rsi, %rax
	0x4c, 0x29, 0xd8, //0x00000b69 subq         %r11, %rax
	0x48, 0x83, 0xf8, 0x10, //0x00000b6c cmpq         $16, %rax
	0x48, 0x8b, 0x7d, 0xc8, //0x00000b70 movq         $-56(%rbp), %rdi
	0x0f, 0x82, 0x54, 0x22, 0x00, 0x00, //0x00000b74 jb           LBB0_509
	0x48, 0x89, 0xf8, //0x00000b7a movq         %rdi, %rax
	0x48, 0xf7, 0xd0, //0x00000b7d notq         %rax
	//0x00000b80 .p2align 4, 0x90
	//0x00000b80 LBB0_142
	0xf3, 0x43, 0x0f, 0x6f, 0x04, 0x19, //0x00000b80 movdqu       (%r9,%r11), %xmm0
	0x66, 0x0f, 0x6f, 0xc8, //0x00000b86 movdqa       %xmm0, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xce, //0x00000b8a pcmpeqb      %xmm14, %xmm1
	0x66, 0x41, 0x0f, 0xdb, 0xc1, //0x00000b8f pand         %xmm9, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x00000b94 pcmpeqb      %xmm8, %xmm0
	0x66, 0x0f, 0xeb, 0xc1, //0x00000b99 por          %xmm1, %xmm0
	0x66, 0x0f, 0xd7, 0xc8, //0x00000b9d pmovmskb     %xmm0, %ecx
	0x85, 0xc9, //0x00000ba1 testl        %ecx, %ecx
	0x0f, 0x85, 0x71, 0x00, 0x00, 0x00, //0x00000ba3 jne          LBB0_152
	0x49, 0x83, 0xc3, 0x10, //0x00000ba9 addq         $16, %r11
	0x48, 0x8d, 0x0c, 0x06, //0x00000bad leaq         (%rsi,%rax), %rcx
	0x48, 0x83, 0xc1, 0xf0, //0x00000bb1 addq         $-16, %rcx
	0x48, 0x83, 0xc0, 0xf0, //0x00000bb5 addq         $-16, %rax
	0x48, 0x83, 0xf9, 0x0f, //0x00000bb9 cmpq         $15, %rcx
	0x0f, 0x87, 0xbd, 0xff, 0xff, 0xff, //0x00000bbd ja           LBB0_142
	0x4d, 0x89, 0xcb, //0x00000bc3 movq         %r9, %r11
	0x49, 0x29, 0xc3, //0x00000bc6 subq         %rax, %r11
	0x48, 0x01, 0xc6, //0x00000bc9 addq         %rax, %rsi
	0x48, 0x89, 0xf0, //0x00000bcc movq         %rsi, %rax
	0x48, 0x85, 0xc0, //0x00000bcf testq        %rax, %rax
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00000bd2 je           LBB0_151
	//0x00000bd8 LBB0_145
	0x49, 0x8d, 0x14, 0x03, //0x00000bd8 leaq         (%r11,%rax), %rdx
	0x31, 0xc9, //0x00000bdc xorl         %ecx, %ecx
	//0x00000bde LBB0_146
	0x41, 0x0f, 0xb6, 0x1c, 0x0b, //0x00000bde movzbl       (%r11,%rcx), %ebx
	0x80, 0xfb, 0x2c, //0x00000be3 cmpb         $44, %bl
	0x0f, 0x84, 0xc7, 0x1a, 0x00, 0x00, //0x00000be6 je           LBB0_465
	0x80, 0xfb, 0x7d, //0x00000bec cmpb         $125, %bl
	0x0f, 0x84, 0xbe, 0x1a, 0x00, 0x00, //0x00000bef je           LBB0_465
	0x80, 0xfb, 0x5d, //0x00000bf5 cmpb         $93, %bl
	0x0f, 0x84, 0xb5, 0x1a, 0x00, 0x00, //0x00000bf8 je           LBB0_465
	0x48, 0x83, 0xc1, 0x01, //0x00000bfe addq         $1, %rcx
	0x48, 0x39, 0xc8, //0x00000c02 cmpq         %rcx, %rax
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x00000c05 jne          LBB0_146
	0x49, 0x89, 0xd3, //0x00000c0b movq         %rdx, %r11
	//0x00000c0e LBB0_151
	0x4d, 0x29, 0xcb, //0x00000c0e subq         %r9, %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x00000c11 movq         $-64(%rbp), %r13
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00000c15 jmp          LBB0_153
	//0x00000c1a LBB0_152
	0x66, 0x0f, 0xbc, 0xc9, //0x00000c1a bsfw         %cx, %cx
	0x44, 0x0f, 0xb7, 0xd9, //0x00000c1e movzwl       %cx, %r11d
	0x49, 0x29, 0xc3, //0x00000c22 subq         %rax, %r11
	//0x00000c25 LBB0_153
	0x48, 0x8b, 0x45, 0xa8, //0x00000c25 movq         $-88(%rbp), %rax
	0x4c, 0x89, 0x18, //0x00000c29 movq         %r11, (%rax)
	0x48, 0x89, 0xf8, //0x00000c2c movq         %rdi, %rax
	0x48, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00000c2f movabsq      $9223372036854775806, %rcx
	0x48, 0x39, 0xcf, //0x00000c39 cmpq         %rcx, %rdi
	0x0f, 0x86, 0x8e, 0xf5, 0xff, 0xff, //0x00000c3c jbe          LBB0_3
	0xe9, 0xac, 0x2a, 0x00, 0x00, //0x00000c42 jmp          LBB0_638
	//0x00000c47 LBB0_154
	0x49, 0x89, 0xf0, //0x00000c47 movq         %rsi, %r8
	0x4d, 0x89, 0xe6, //0x00000c4a movq         %r12, %r14
	0x4d, 0x29, 0xde, //0x00000c4d subq         %r11, %r14
	0x49, 0x83, 0xfe, 0x20, //0x00000c50 cmpq         $32, %r14
	0x0f, 0x8c, 0x3e, 0x22, 0x00, 0x00, //0x00000c54 jl           LBB0_522
	0x4c, 0x89, 0xc1, //0x00000c5a movq         %r8, %rcx
	0x4f, 0x8d, 0x14, 0x01, //0x00000c5d leaq         (%r9,%r8), %r10
	0x4d, 0x29, 0xc4, //0x00000c61 subq         %r8, %r12
	0xbb, 0x1f, 0x00, 0x00, 0x00, //0x00000c64 movl         $31, %ebx
	0x45, 0x31, 0xf6, //0x00000c69 xorl         %r14d, %r14d
	0x45, 0x31, 0xdb, //0x00000c6c xorl         %r11d, %r11d
	0xe9, 0x32, 0x00, 0x00, 0x00, //0x00000c6f jmp          LBB0_156
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000c74 .p2align 4, 0x90
	//0x00000c80 LBB0_159
	0x45, 0x31, 0xdb, //0x00000c80 xorl         %r11d, %r11d
	0x48, 0x85, 0xd2, //0x00000c83 testq        %rdx, %rdx
	0x0f, 0x85, 0xaa, 0x00, 0x00, 0x00, //0x00000c86 jne          LBB0_158
	//0x00000c8c LBB0_160
	0x49, 0x83, 0xc6, 0x20, //0x00000c8c addq         $32, %r14
	0x49, 0x8d, 0x0c, 0x1c, //0x00000c90 leaq         (%r12,%rbx), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00000c94 addq         $-32, %rcx
	0x48, 0x83, 0xc3, 0xe0, //0x00000c98 addq         $-32, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x00000c9c cmpq         $63, %rcx
	0x0f, 0x8e, 0x6f, 0x1a, 0x00, 0x00, //0x00000ca0 jle          LBB0_161
	//0x00000ca6 LBB0_156
	0xf3, 0x43, 0x0f, 0x6f, 0x44, 0x32, 0x01, //0x00000ca6 movdqu       $1(%r10,%r14), %xmm0
	0xf3, 0x43, 0x0f, 0x6f, 0x4c, 0x32, 0x11, //0x00000cad movdqu       $17(%r10,%r14), %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00000cb4 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x00000cb8 pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00000cbd pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd1, //0x00000cc1 movdqa       %xmm1, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x00000cc5 pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00000cca pmovmskb     %xmm2, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x00000cce shlq         $16, %rdx
	0x48, 0x09, 0xca, //0x00000cd2 orq          %rcx, %rdx
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00000cd5 pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00000cda pmovmskb     %xmm0, %edi
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x00000cde pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x00000ce3 pmovmskb     %xmm1, %ecx
	0x48, 0xc1, 0xe1, 0x10, //0x00000ce7 shlq         $16, %rcx
	0x48, 0x09, 0xf9, //0x00000ceb orq          %rdi, %rcx
	0x48, 0x89, 0xcf, //0x00000cee movq         %rcx, %rdi
	0x4c, 0x09, 0xdf, //0x00000cf1 orq          %r11, %rdi
	0x0f, 0x84, 0x86, 0xff, 0xff, 0xff, //0x00000cf4 je           LBB0_159
	0x44, 0x89, 0xdf, //0x00000cfa movl         %r11d, %edi
	0xf7, 0xd7, //0x00000cfd notl         %edi
	0x21, 0xcf, //0x00000cff andl         %ecx, %edi
	0x44, 0x8d, 0x3c, 0x3f, //0x00000d01 leal         (%rdi,%rdi), %r15d
	0x45, 0x09, 0xdf, //0x00000d05 orl          %r11d, %r15d
	0x44, 0x89, 0xfe, //0x00000d08 movl         %r15d, %esi
	0xf7, 0xd6, //0x00000d0b notl         %esi
	0x21, 0xce, //0x00000d0d andl         %ecx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000d0f andl         $-1431655766, %esi
	0x45, 0x31, 0xdb, //0x00000d15 xorl         %r11d, %r11d
	0x01, 0xfe, //0x00000d18 addl         %edi, %esi
	0x41, 0x0f, 0x92, 0xc3, //0x00000d1a setb         %r11b
	0x01, 0xf6, //0x00000d1e addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x00000d20 xorl         $1431655765, %esi
	0x44, 0x21, 0xfe, //0x00000d26 andl         %r15d, %esi
	0xf7, 0xd6, //0x00000d29 notl         %esi
	0x21, 0xf2, //0x00000d2b andl         %esi, %edx
	0x48, 0x85, 0xd2, //0x00000d2d testq        %rdx, %rdx
	0x0f, 0x84, 0x56, 0xff, 0xff, 0xff, //0x00000d30 je           LBB0_160
	//0x00000d36 LBB0_158
	0x0f, 0xbc, 0xc2, //0x00000d36 bsfl         %edx, %eax
	0x4c, 0x89, 0xc2, //0x00000d39 movq         %r8, %rdx
	0x4c, 0x01, 0xc0, //0x00000d3c addq         %r8, %rax
	0x4d, 0x8d, 0x1c, 0x06, //0x00000d3f leaq         (%r14,%rax), %r11
	0x49, 0x83, 0xc3, 0x02, //0x00000d43 addq         $2, %r11
	0xe9, 0x74, 0x0a, 0x00, 0x00, //0x00000d47 jmp          LBB0_331
	//0x00000d4c LBB0_164
	0x4d, 0x29, 0xde, //0x00000d4c subq         %r11, %r14
	0x0f, 0x84, 0x74, 0x2b, 0x00, 0x00, //0x00000d4f je           LBB0_639
	0x66, 0x44, 0x0f, 0x6f, 0xe7, //0x00000d55 movdqa       %xmm7, %xmm12
	0x49, 0x83, 0xfe, 0x40, //0x00000d5a cmpq         $64, %r14
	0x0f, 0x82, 0x59, 0x21, 0x00, 0x00, //0x00000d5e jb           LBB0_524
	0x4c, 0x89, 0x4d, 0xb0, //0x00000d64 movq         %r9, $-80(%rbp)
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x00000d68 movq         $-1, $-48(%rbp)
	0x31, 0xdb, //0x00000d70 xorl         %ebx, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000d72 .p2align 4, 0x90
	//0x00000d80 LBB0_167
	0x49, 0x89, 0xd9, //0x00000d80 movq         %rbx, %r9
	0x48, 0x8b, 0x45, 0xb0, //0x00000d83 movq         $-80(%rbp), %rax
	0xf3, 0x42, 0x0f, 0x6f, 0x3c, 0x18, //0x00000d87 movdqu       (%rax,%r11), %xmm7
	0xf3, 0x42, 0x0f, 0x6f, 0x54, 0x18, 0x10, //0x00000d8d movdqu       $16(%rax,%r11), %xmm2
	0xf3, 0x42, 0x0f, 0x6f, 0x44, 0x18, 0x20, //0x00000d94 movdqu       $32(%rax,%r11), %xmm0
	0xf3, 0x42, 0x0f, 0x6f, 0x4c, 0x18, 0x30, //0x00000d9b movdqu       $48(%rax,%r11), %xmm1
	0x66, 0x0f, 0x6f, 0xdf, //0x00000da2 movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x00000da6 pcmpeqb      %xmm11, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xfb, //0x00000dab pmovmskb     %xmm3, %r15d
	0x66, 0x0f, 0x6f, 0xda, //0x00000db0 movdqa       %xmm2, %xmm3
	0x66, 0x0f, 0x6f, 0xf2, //0x00000db4 movdqa       %xmm2, %xmm6
	0x66, 0x41, 0x0f, 0xda, 0xf2, //0x00000db8 pminub       %xmm10, %xmm6
	0x66, 0x0f, 0x74, 0xf2, //0x00000dbd pcmpeqb      %xmm2, %xmm6
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x00000dc1 pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00000dc6 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x6f, 0xd0, //0x00000dca movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x6f, 0xe8, //0x00000dce movdqa       %xmm0, %xmm5
	0x66, 0x41, 0x0f, 0xda, 0xea, //0x00000dd2 pminub       %xmm10, %xmm5
	0x66, 0x0f, 0x74, 0xe8, //0x00000dd7 pcmpeqb      %xmm0, %xmm5
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x00000ddb pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00000de0 pmovmskb     %xmm0, %edi
	0x66, 0x0f, 0x6f, 0xc1, //0x00000de4 movdqa       %xmm1, %xmm0
	0x66, 0x0f, 0x6f, 0xe1, //0x00000de8 movdqa       %xmm1, %xmm4
	0x66, 0x41, 0x0f, 0xda, 0xe2, //0x00000dec pminub       %xmm10, %xmm4
	0x66, 0x0f, 0x74, 0xe1, //0x00000df1 pcmpeqb      %xmm1, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xcb, //0x00000df5 pcmpeqb      %xmm11, %xmm1
	0x66, 0x0f, 0xd7, 0xc1, //0x00000dfa pmovmskb     %xmm1, %eax
	0x66, 0x0f, 0x6f, 0xcf, //0x00000dfe movdqa       %xmm7, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x00000e02 pcmpeqb      %xmm13, %xmm1
	0x66, 0x44, 0x0f, 0xd7, 0xe1, //0x00000e07 pmovmskb     %xmm1, %r12d
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x00000e0c pcmpeqb      %xmm13, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x00000e11 pmovmskb     %xmm3, %edx
	0x66, 0x41, 0x0f, 0x74, 0xd5, //0x00000e15 pcmpeqb      %xmm13, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xc2, //0x00000e1a pmovmskb     %xmm2, %r8d
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00000e1f pcmpeqb      %xmm13, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xd0, //0x00000e24 pmovmskb     %xmm0, %r10d
	0x66, 0x0f, 0xd7, 0xce, //0x00000e29 pmovmskb     %xmm6, %ecx
	0x66, 0x0f, 0xd7, 0xdd, //0x00000e2d pmovmskb     %xmm5, %ebx
	0x66, 0x44, 0x0f, 0xd7, 0xec, //0x00000e31 pmovmskb     %xmm4, %r13d
	0x48, 0xc1, 0xe0, 0x30, //0x00000e36 shlq         $48, %rax
	0x48, 0xc1, 0xe7, 0x20, //0x00000e3a shlq         $32, %rdi
	0x48, 0x09, 0xc7, //0x00000e3e orq          %rax, %rdi
	0x48, 0xc1, 0xe6, 0x10, //0x00000e41 shlq         $16, %rsi
	0x48, 0x09, 0xfe, //0x00000e45 orq          %rdi, %rsi
	0x49, 0x09, 0xf7, //0x00000e48 orq          %rsi, %r15
	0x49, 0xc1, 0xe2, 0x30, //0x00000e4b shlq         $48, %r10
	0x49, 0xc1, 0xe0, 0x20, //0x00000e4f shlq         $32, %r8
	0x4d, 0x09, 0xd0, //0x00000e53 orq          %r10, %r8
	0x48, 0xc1, 0xe2, 0x10, //0x00000e56 shlq         $16, %rdx
	0x4c, 0x09, 0xc2, //0x00000e5a orq          %r8, %rdx
	0x49, 0xc1, 0xe5, 0x30, //0x00000e5d shlq         $48, %r13
	0x48, 0xc1, 0xe3, 0x20, //0x00000e61 shlq         $32, %rbx
	0x4c, 0x09, 0xeb, //0x00000e65 orq          %r13, %rbx
	0x48, 0xc1, 0xe1, 0x10, //0x00000e68 shlq         $16, %rcx
	0x48, 0x09, 0xd9, //0x00000e6c orq          %rbx, %rcx
	0x49, 0x09, 0xd4, //0x00000e6f orq          %rdx, %r12
	0x0f, 0x85, 0x50, 0x00, 0x00, 0x00, //0x00000e72 jne          LBB0_184
	0x4d, 0x85, 0xc9, //0x00000e78 testq        %r9, %r9
	0x0f, 0x85, 0x66, 0x00, 0x00, 0x00, //0x00000e7b jne          LBB0_186
	0x31, 0xdb, //0x00000e81 xorl         %ebx, %ebx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00000e83 movq         $-64(%rbp), %r13
	//0x00000e87 LBB0_170
	0x66, 0x0f, 0x6f, 0xc7, //0x00000e87 movdqa       %xmm7, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc2, //0x00000e8b pminub       %xmm10, %xmm0
	0x66, 0x0f, 0x74, 0xc7, //0x00000e90 pcmpeqb      %xmm7, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00000e94 pmovmskb     %xmm0, %eax
	0x48, 0x09, 0xc1, //0x00000e98 orq          %rax, %rcx
	0x4d, 0x85, 0xff, //0x00000e9b testq        %r15, %r15
	0x4c, 0x8b, 0x65, 0xb8, //0x00000e9e movq         $-72(%rbp), %r12
	0x0f, 0x85, 0x8f, 0x00, 0x00, 0x00, //0x00000ea2 jne          LBB0_188
	0x48, 0x85, 0xc9, //0x00000ea8 testq        %rcx, %rcx
	0x0f, 0x85, 0x74, 0x28, 0x00, 0x00, //0x00000eab jne          LBB0_623
	0x49, 0x83, 0xc6, 0xc0, //0x00000eb1 addq         $-64, %r14
	0x49, 0x83, 0xc3, 0x40, //0x00000eb5 addq         $64, %r11
	0x49, 0x83, 0xfe, 0x3f, //0x00000eb9 cmpq         $63, %r14
	0x0f, 0x87, 0xbd, 0xfe, 0xff, 0xff, //0x00000ebd ja           LBB0_167
	0xe9, 0x52, 0x19, 0x00, 0x00, //0x00000ec3 jmp          LBB0_173
	//0x00000ec8 LBB0_184
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x00000ec8 cmpq         $-1, $-48(%rbp)
	0x4c, 0x8b, 0x6d, 0xc0, //0x00000ecd movq         $-64(%rbp), %r13
	0x0f, 0x85, 0x14, 0x00, 0x00, 0x00, //0x00000ed1 jne          LBB0_187
	0x49, 0x0f, 0xbc, 0xc4, //0x00000ed7 bsfq         %r12, %rax
	0x4c, 0x01, 0xd8, //0x00000edb addq         %r11, %rax
	0x48, 0x89, 0x45, 0xd0, //0x00000ede movq         %rax, $-48(%rbp)
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00000ee2 jmp          LBB0_187
	//0x00000ee7 LBB0_186
	0x4c, 0x8b, 0x6d, 0xc0, //0x00000ee7 movq         $-64(%rbp), %r13
	//0x00000eeb LBB0_187
	0x4c, 0x89, 0xc8, //0x00000eeb movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00000eee notq         %rax
	0x4c, 0x21, 0xe0, //0x00000ef1 andq         %r12, %rax
	0x48, 0x8d, 0x14, 0x00, //0x00000ef4 leaq         (%rax,%rax), %rdx
	0x4c, 0x09, 0xca, //0x00000ef8 orq          %r9, %rdx
	0x48, 0x89, 0xd6, //0x00000efb movq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00000efe notq         %rsi
	0x4c, 0x21, 0xe6, //0x00000f01 andq         %r12, %rsi
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000f04 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x00000f0e andq         %rdi, %rsi
	0x31, 0xdb, //0x00000f11 xorl         %ebx, %ebx
	0x48, 0x01, 0xc6, //0x00000f13 addq         %rax, %rsi
	0x0f, 0x92, 0xc3, //0x00000f16 setb         %bl
	0x48, 0x01, 0xf6, //0x00000f19 addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000f1c movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x00000f26 xorq         %rax, %rsi
	0x48, 0x21, 0xd6, //0x00000f29 andq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00000f2c notq         %rsi
	0x49, 0x21, 0xf7, //0x00000f2f andq         %rsi, %r15
	0xe9, 0x50, 0xff, 0xff, 0xff, //0x00000f32 jmp          LBB0_170
	//0x00000f37 LBB0_188
	0x49, 0x0f, 0xbc, 0xc7, //0x00000f37 bsfq         %r15, %rax
	0x48, 0x85, 0xc9, //0x00000f3b testq        %rcx, %rcx
	0x66, 0x41, 0x0f, 0x6f, 0xfc, //0x00000f3e movdqa       %xmm12, %xmm7
	0x0f, 0x84, 0xec, 0x02, 0x00, 0x00, //0x00000f43 je           LBB0_244
	0x48, 0x0f, 0xbc, 0xc9, //0x00000f49 bsfq         %rcx, %rcx
	0x48, 0x8b, 0x55, 0xc8, //0x00000f4d movq         $-56(%rbp), %rdx
	0x48, 0x39, 0xc1, //0x00000f51 cmpq         %rax, %rcx
	0x0f, 0x83, 0xed, 0x02, 0x00, 0x00, //0x00000f54 jae          LBB0_245
	0xe9, 0x7a, 0x29, 0x00, 0x00, //0x00000f5a jmp          LBB0_190
	//0x00000f5f LBB0_196
	0x89, 0xc8, //0x00000f5f movl         %ecx, %eax
	0x4c, 0x01, 0xc0, //0x00000f61 addq         %r8, %rax
	0x49, 0x01, 0xc3, //0x00000f64 addq         %rax, %r11
	//0x00000f67 LBB0_197
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000f67 movq         $-1, %rax
	0x4d, 0x85, 0xe4, //0x00000f6e testq        %r12, %r12
	0x0f, 0x85, 0x2d, 0x02, 0x00, 0x00, //0x00000f71 jne          LBB0_233
	0xe9, 0x03, 0x29, 0x00, 0x00, //0x00000f77 jmp          LBB0_198
	//0x00000f7c LBB0_199
	0x4d, 0x89, 0xe6, //0x00000f7c movq         %r12, %r14
	0x4d, 0x29, 0xde, //0x00000f7f subq         %r11, %r14
	0x0f, 0x84, 0x49, 0x29, 0x00, 0x00, //0x00000f82 je           LBB0_640
	0x4c, 0x89, 0x4d, 0xb0, //0x00000f88 movq         %r9, $-80(%rbp)
	0x49, 0x83, 0xfe, 0x40, //0x00000f8c cmpq         $64, %r14
	0x0f, 0x82, 0xd2, 0x1f, 0x00, 0x00, //0x00000f90 jb           LBB0_530
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x00000f96 movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xc9, //0x00000f9e xorl         %r9d, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000fa1 .p2align 4, 0x90
	//0x00000fb0 LBB0_202
	0x48, 0x8b, 0x45, 0xb0, //0x00000fb0 movq         $-80(%rbp), %rax
	0xf3, 0x46, 0x0f, 0x6f, 0x24, 0x18, //0x00000fb4 movdqu       (%rax,%r11), %xmm12
	0xf3, 0x42, 0x0f, 0x6f, 0x44, 0x18, 0x10, //0x00000fba movdqu       $16(%rax,%r11), %xmm0
	0xf3, 0x42, 0x0f, 0x6f, 0x64, 0x18, 0x20, //0x00000fc1 movdqu       $32(%rax,%r11), %xmm4
	0xf3, 0x42, 0x0f, 0x6f, 0x5c, 0x18, 0x30, //0x00000fc8 movdqu       $48(%rax,%r11), %xmm3
	0x66, 0x41, 0x0f, 0x6f, 0xcc, //0x00000fcf movdqa       %xmm12, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xcb, //0x00000fd4 pcmpeqb      %xmm11, %xmm1
	0x66, 0x44, 0x0f, 0xd7, 0xf9, //0x00000fd9 pmovmskb     %xmm1, %r15d
	0x66, 0x0f, 0x6f, 0xc8, //0x00000fde movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00000fe2 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0xda, 0xd2, //0x00000fe6 pminub       %xmm10, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00000feb pcmpeqb      %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x00000fef pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xf0, //0x00000ff4 pmovmskb     %xmm0, %esi
	0x66, 0x0f, 0x6f, 0xc4, //0x00000ff8 movdqa       %xmm4, %xmm0
	0x66, 0x0f, 0x6f, 0xec, //0x00000ffc movdqa       %xmm4, %xmm5
	0x66, 0x41, 0x0f, 0xda, 0xea, //0x00001000 pminub       %xmm10, %xmm5
	0x66, 0x0f, 0x74, 0xec, //0x00001005 pcmpeqb      %xmm4, %xmm5
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x00001009 pcmpeqb      %xmm11, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x0000100e pmovmskb     %xmm4, %edi
	0x66, 0x0f, 0x6f, 0xe3, //0x00001012 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x6f, 0xf3, //0x00001016 movdqa       %xmm3, %xmm6
	0x66, 0x41, 0x0f, 0xda, 0xf2, //0x0000101a pminub       %xmm10, %xmm6
	0x66, 0x0f, 0x74, 0xf3, //0x0000101f pcmpeqb      %xmm3, %xmm6
	0x66, 0x41, 0x0f, 0x74, 0xdb, //0x00001023 pcmpeqb      %xmm11, %xmm3
	0x66, 0x0f, 0xd7, 0xc3, //0x00001028 pmovmskb     %xmm3, %eax
	0x66, 0x41, 0x0f, 0x6f, 0xdc, //0x0000102c movdqa       %xmm12, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x00001031 pcmpeqb      %xmm13, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xe3, //0x00001036 pmovmskb     %xmm3, %r12d
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x0000103b pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xd1, //0x00001040 pmovmskb     %xmm1, %edx
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00001044 pcmpeqb      %xmm13, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xc0, //0x00001049 pmovmskb     %xmm0, %r8d
	0x66, 0x41, 0x0f, 0x74, 0xe5, //0x0000104e pcmpeqb      %xmm13, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xd4, //0x00001053 pmovmskb     %xmm4, %r10d
	0x66, 0x0f, 0xd7, 0xca, //0x00001058 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0xd7, 0xdd, //0x0000105c pmovmskb     %xmm5, %ebx
	0x66, 0x44, 0x0f, 0xd7, 0xee, //0x00001060 pmovmskb     %xmm6, %r13d
	0x48, 0xc1, 0xe0, 0x30, //0x00001065 shlq         $48, %rax
	0x48, 0xc1, 0xe7, 0x20, //0x00001069 shlq         $32, %rdi
	0x48, 0x09, 0xc7, //0x0000106d orq          %rax, %rdi
	0x48, 0xc1, 0xe6, 0x10, //0x00001070 shlq         $16, %rsi
	0x48, 0x09, 0xfe, //0x00001074 orq          %rdi, %rsi
	0x49, 0x09, 0xf7, //0x00001077 orq          %rsi, %r15
	0x49, 0xc1, 0xe2, 0x30, //0x0000107a shlq         $48, %r10
	0x49, 0xc1, 0xe0, 0x20, //0x0000107e shlq         $32, %r8
	0x4d, 0x09, 0xd0, //0x00001082 orq          %r10, %r8
	0x48, 0xc1, 0xe2, 0x10, //0x00001085 shlq         $16, %rdx
	0x4c, 0x09, 0xc2, //0x00001089 orq          %r8, %rdx
	0x49, 0xc1, 0xe5, 0x30, //0x0000108c shlq         $48, %r13
	0x48, 0xc1, 0xe3, 0x20, //0x00001090 shlq         $32, %rbx
	0x4c, 0x09, 0xeb, //0x00001094 orq          %r13, %rbx
	0x48, 0xc1, 0xe1, 0x10, //0x00001097 shlq         $16, %rcx
	0x48, 0x09, 0xd9, //0x0000109b orq          %rbx, %rcx
	0x49, 0x09, 0xd4, //0x0000109e orq          %rdx, %r12
	0x0f, 0x85, 0x53, 0x00, 0x00, 0x00, //0x000010a1 jne          LBB0_219
	0x4d, 0x85, 0xc9, //0x000010a7 testq        %r9, %r9
	0x0f, 0x85, 0x69, 0x00, 0x00, 0x00, //0x000010aa jne          LBB0_221
	0x45, 0x31, 0xc9, //0x000010b0 xorl         %r9d, %r9d
	0x4c, 0x8b, 0x6d, 0xc0, //0x000010b3 movq         $-64(%rbp), %r13
	//0x000010b7 LBB0_205
	0x66, 0x41, 0x0f, 0x6f, 0xc4, //0x000010b7 movdqa       %xmm12, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc2, //0x000010bc pminub       %xmm10, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc4, //0x000010c1 pcmpeqb      %xmm12, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x000010c6 pmovmskb     %xmm0, %eax
	0x48, 0x09, 0xc1, //0x000010ca orq          %rax, %rcx
	0x4d, 0x85, 0xff, //0x000010cd testq        %r15, %r15
	0x4c, 0x8b, 0x65, 0xb8, //0x000010d0 movq         $-72(%rbp), %r12
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x000010d4 jne          LBB0_223
	0x48, 0x85, 0xc9, //0x000010da testq        %rcx, %rcx
	0x0f, 0x85, 0x7b, 0x27, 0x00, 0x00, //0x000010dd jne          LBB0_632
	0x49, 0x83, 0xc6, 0xc0, //0x000010e3 addq         $-64, %r14
	0x49, 0x83, 0xc3, 0x40, //0x000010e7 addq         $64, %r11
	0x49, 0x83, 0xfe, 0x3f, //0x000010eb cmpq         $63, %r14
	0x0f, 0x87, 0xbb, 0xfe, 0xff, 0xff, //0x000010ef ja           LBB0_202
	0xe9, 0xda, 0x18, 0x00, 0x00, //0x000010f5 jmp          LBB0_208
	//0x000010fa LBB0_219
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x000010fa cmpq         $-1, $-48(%rbp)
	0x4c, 0x8b, 0x6d, 0xc0, //0x000010ff movq         $-64(%rbp), %r13
	0x0f, 0x85, 0x14, 0x00, 0x00, 0x00, //0x00001103 jne          LBB0_222
	0x49, 0x0f, 0xbc, 0xc4, //0x00001109 bsfq         %r12, %rax
	0x4c, 0x01, 0xd8, //0x0000110d addq         %r11, %rax
	0x48, 0x89, 0x45, 0xd0, //0x00001110 movq         %rax, $-48(%rbp)
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00001114 jmp          LBB0_222
	//0x00001119 LBB0_221
	0x4c, 0x8b, 0x6d, 0xc0, //0x00001119 movq         $-64(%rbp), %r13
	//0x0000111d LBB0_222
	0x4c, 0x89, 0xc8, //0x0000111d movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00001120 notq         %rax
	0x4c, 0x21, 0xe0, //0x00001123 andq         %r12, %rax
	0x48, 0x8d, 0x14, 0x00, //0x00001126 leaq         (%rax,%rax), %rdx
	0x4c, 0x09, 0xca, //0x0000112a orq          %r9, %rdx
	0x48, 0x89, 0xd6, //0x0000112d movq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00001130 notq         %rsi
	0x4c, 0x21, 0xe6, //0x00001133 andq         %r12, %rsi
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001136 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x00001140 andq         %rdi, %rsi
	0x45, 0x31, 0xc9, //0x00001143 xorl         %r9d, %r9d
	0x48, 0x01, 0xc6, //0x00001146 addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc1, //0x00001149 setb         %r9b
	0x48, 0x01, 0xf6, //0x0000114d addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001150 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x0000115a xorq         %rax, %rsi
	0x48, 0x21, 0xd6, //0x0000115d andq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x00001160 notq         %rsi
	0x49, 0x21, 0xf7, //0x00001163 andq         %rsi, %r15
	0xe9, 0x4c, 0xff, 0xff, 0xff, //0x00001166 jmp          LBB0_205
	//0x0000116b LBB0_223
	0x49, 0x0f, 0xbc, 0xc7, //0x0000116b bsfq         %r15, %rax
	0x48, 0x85, 0xc9, //0x0000116f testq        %rcx, %rcx
	0x0f, 0x84, 0x26, 0x06, 0x00, 0x00, //0x00001172 je           LBB0_329
	0x48, 0x0f, 0xbc, 0xc9, //0x00001178 bsfq         %rcx, %rcx
	0x48, 0x8b, 0x55, 0xc8, //0x0000117c movq         $-56(%rbp), %rdx
	0x48, 0x39, 0xc1, //0x00001180 cmpq         %rax, %rcx
	0x0f, 0x83, 0x27, 0x06, 0x00, 0x00, //0x00001183 jae          LBB0_330
	0xe9, 0x5a, 0x27, 0x00, 0x00, //0x00001189 jmp          LBB0_225
	//0x0000118e LBB0_232
	0x48, 0x01, 0xf1, //0x0000118e addq         %rsi, %rcx
	0x49, 0x89, 0xcb, //0x00001191 movq         %rcx, %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001194 movq         $-1, %rax
	0x4d, 0x85, 0xe4, //0x0000119b testq        %r12, %r12
	0x0f, 0x84, 0xdb, 0x26, 0x00, 0x00, //0x0000119e je           LBB0_198
	//0x000011a4 LBB0_233
	0x4d, 0x85, 0xed, //0x000011a4 testq        %r13, %r13
	0x48, 0x8b, 0x55, 0xc8, //0x000011a7 movq         $-56(%rbp), %rdx
	0x0f, 0x84, 0x2e, 0x25, 0x00, 0x00, //0x000011ab je           LBB0_622
	0x4d, 0x85, 0xd2, //0x000011b1 testq        %r10, %r10
	0x0f, 0x84, 0x25, 0x25, 0x00, 0x00, //0x000011b4 je           LBB0_622
	0x4d, 0x29, 0xc3, //0x000011ba subq         %r8, %r11
	0x49, 0x8d, 0x43, 0xff, //0x000011bd leaq         $-1(%r11), %rax
	0x49, 0x39, 0xc4, //0x000011c1 cmpq         %rax, %r12
	0x0f, 0x84, 0x40, 0x00, 0x00, 0x00, //0x000011c4 je           LBB0_241
	0x49, 0x39, 0xc5, //0x000011ca cmpq         %rax, %r13
	0x0f, 0x84, 0x37, 0x00, 0x00, 0x00, //0x000011cd je           LBB0_241
	0x49, 0x39, 0xc2, //0x000011d3 cmpq         %rax, %r10
	0x0f, 0x84, 0x2e, 0x00, 0x00, 0x00, //0x000011d6 je           LBB0_241
	0x4d, 0x85, 0xed, //0x000011dc testq        %r13, %r13
	0x0f, 0x8e, 0x89, 0x05, 0x00, 0x00, //0x000011df jle          LBB0_326
	0x49, 0x8d, 0x45, 0xff, //0x000011e5 leaq         $-1(%r13), %rax
	0x49, 0x39, 0xc2, //0x000011e9 cmpq         %rax, %r10
	0x0f, 0x84, 0x7c, 0x05, 0x00, 0x00, //0x000011ec je           LBB0_326
	0x49, 0xf7, 0xd5, //0x000011f2 notq         %r13
	0x4d, 0x89, 0xeb, //0x000011f5 movq         %r13, %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x000011f8 movq         $-64(%rbp), %r13
	0x4d, 0x85, 0xdb, //0x000011fc testq        %r11, %r11
	0x0f, 0x89, 0x15, 0x00, 0x00, 0x00, //0x000011ff jns          LBB0_242
	0xe9, 0xd2, 0x24, 0x00, 0x00, //0x00001205 jmp          LBB0_621
	//0x0000120a LBB0_241
	0x49, 0xf7, 0xdb, //0x0000120a negq         %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x0000120d movq         $-64(%rbp), %r13
	0x4d, 0x85, 0xdb, //0x00001211 testq        %r11, %r11
	0x0f, 0x88, 0xc2, 0x24, 0x00, 0x00, //0x00001214 js           LBB0_621
	//0x0000121a LBB0_242
	0x49, 0x01, 0xd3, //0x0000121a addq         %rdx, %r11
	//0x0000121d LBB0_243
	0x48, 0x8b, 0x45, 0xa8, //0x0000121d movq         $-88(%rbp), %rax
	0x4c, 0x89, 0x18, //0x00001221 movq         %r11, (%rax)
	0x48, 0x89, 0xd0, //0x00001224 movq         %rdx, %rax
	0x48, 0x85, 0xd2, //0x00001227 testq        %rdx, %rdx
	0x0f, 0x89, 0xa0, 0xef, 0xff, 0xff, //0x0000122a jns          LBB0_3
	0xe9, 0xbe, 0x24, 0x00, 0x00, //0x00001230 jmp          LBB0_638
	//0x00001235 LBB0_244
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001235 movl         $64, %ecx
	0x48, 0x8b, 0x55, 0xc8, //0x0000123a movq         $-56(%rbp), %rdx
	0x48, 0x39, 0xc1, //0x0000123e cmpq         %rax, %rcx
	0x0f, 0x82, 0x92, 0x26, 0x00, 0x00, //0x00001241 jb           LBB0_190
	//0x00001247 LBB0_245
	0x49, 0x01, 0xc3, //0x00001247 addq         %rax, %r11
	0x49, 0x83, 0xc3, 0x01, //0x0000124a addq         $1, %r11
	0x4d, 0x85, 0xdb, //0x0000124e testq        %r11, %r11
	0x0f, 0x88, 0x5c, 0x24, 0x00, 0x00, //0x00001251 js           LBB0_58
	//0x00001257 LBB0_246
	0x48, 0x8b, 0x45, 0xa8, //0x00001257 movq         $-88(%rbp), %rax
	0x4c, 0x89, 0x18, //0x0000125b movq         %r11, (%rax)
	//0x0000125e LBB0_247
	0x48, 0x89, 0xd0, //0x0000125e movq         %rdx, %rax
	0x48, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00001261 movabsq      $9223372036854775806, %rcx
	0x48, 0x39, 0xca, //0x0000126b cmpq         %rcx, %rdx
	0x0f, 0x86, 0x5c, 0xef, 0xff, 0xff, //0x0000126e jbe          LBB0_3
	0xe9, 0x7a, 0x24, 0x00, 0x00, //0x00001274 jmp          LBB0_638
	//0x00001279 LBB0_248
	0x48, 0x8b, 0x4d, 0xa0, //0x00001279 movq         $-96(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x0000127d movq         $8(%rcx), %rcx
	0x48, 0x8d, 0x51, 0xfc, //0x00001281 leaq         $-4(%rcx), %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x00001285 movq         $-56(%rbp), %rsi
	0x48, 0x39, 0xd6, //0x00001289 cmpq         %rdx, %rsi
	0x0f, 0x83, 0x81, 0x26, 0x00, 0x00, //0x0000128c jae          LBB0_643
	0x43, 0x8b, 0x0c, 0x19, //0x00001292 movl         (%r9,%r11), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x00001296 cmpl         $1702063201, %ecx
	0x0f, 0x85, 0x06, 0x25, 0x00, 0x00, //0x0000129c jne          LBB0_627
	0x4c, 0x8d, 0x5e, 0x05, //0x000012a2 leaq         $5(%rsi), %r11
	0xe9, 0xf9, 0xee, 0xff, 0xff, //0x000012a6 jmp          LBB0_2
	//0x000012ab LBB0_251
	0x48, 0x8b, 0x4d, 0xa0, //0x000012ab movq         $-96(%rbp), %rcx
	0x4c, 0x8b, 0x41, 0x08, //0x000012af movq         $8(%rcx), %r8
	0x48, 0x8b, 0x4d, 0x98, //0x000012b3 movq         $-104(%rbp), %rcx
	0xf6, 0xc1, 0x40, //0x000012b7 testb        $64, %cl
	0x0f, 0x85, 0x43, 0x05, 0x00, 0x00, //0x000012ba jne          LBB0_334
	0xf6, 0xc1, 0x20, //0x000012c0 testb        $32, %cl
	0x4c, 0x89, 0x45, 0xb8, //0x000012c3 movq         %r8, $-72(%rbp)
	0x0f, 0x85, 0x1f, 0x10, 0x00, 0x00, //0x000012c7 jne          LBB0_414
	0x4d, 0x89, 0xc6, //0x000012cd movq         %r8, %r14
	0x4d, 0x29, 0xde, //0x000012d0 subq         %r11, %r14
	0x0f, 0x84, 0x21, 0x26, 0x00, 0x00, //0x000012d3 je           LBB0_641
	0x49, 0x83, 0xfe, 0x40, //0x000012d9 cmpq         $64, %r14
	0x0f, 0x82, 0x37, 0x1d, 0x00, 0x00, //0x000012dd jb           LBB0_537
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000012e3 movq         $-2, %r12
	0x4c, 0x2b, 0x65, 0xc8, //0x000012ea subq         $-56(%rbp), %r12
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x000012ee movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xd2, //0x000012f6 xorl         %r10d, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000012f9 .p2align 4, 0x90
	//0x00001300 LBB0_256
	0xf3, 0x43, 0x0f, 0x6f, 0x04, 0x19, //0x00001300 movdqu       (%r9,%r11), %xmm0
	0xf3, 0x43, 0x0f, 0x6f, 0x4c, 0x19, 0x10, //0x00001306 movdqu       $16(%r9,%r11), %xmm1
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x19, 0x20, //0x0000130d movdqu       $32(%r9,%r11), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x19, 0x30, //0x00001314 movdqu       $48(%r9,%r11), %xmm3
	0x66, 0x0f, 0x6f, 0xe0, //0x0000131b movdqa       %xmm0, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x0000131f pcmpeqb      %xmm11, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xc4, //0x00001324 pmovmskb     %xmm4, %r8d
	0x66, 0x0f, 0x6f, 0xe1, //0x00001329 movdqa       %xmm1, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x0000132d pcmpeqb      %xmm11, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x00001332 pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x6f, 0xe2, //0x00001336 movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x0000133a pcmpeqb      %xmm11, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x0000133f pmovmskb     %xmm4, %ecx
	0x66, 0x0f, 0x6f, 0xe3, //0x00001343 movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x00001347 pcmpeqb      %xmm11, %xmm4
	0x66, 0x0f, 0xd7, 0xfc, //0x0000134c pmovmskb     %xmm4, %edi
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00001350 pcmpeqb      %xmm13, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xf8, //0x00001355 pmovmskb     %xmm0, %r15d
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x0000135a pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xd1, //0x0000135f pmovmskb     %xmm1, %edx
	0x66, 0x41, 0x0f, 0x74, 0xd5, //0x00001363 pcmpeqb      %xmm13, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00001368 pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xdd, //0x0000136c pcmpeqb      %xmm13, %xmm3
	0x66, 0x0f, 0xd7, 0xf3, //0x00001371 pmovmskb     %xmm3, %esi
	0x48, 0xc1, 0xe7, 0x30, //0x00001375 shlq         $48, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00001379 shlq         $32, %rcx
	0x48, 0x09, 0xf9, //0x0000137d orq          %rdi, %rcx
	0x48, 0xc1, 0xe3, 0x10, //0x00001380 shlq         $16, %rbx
	0x48, 0x09, 0xcb, //0x00001384 orq          %rcx, %rbx
	0x49, 0x09, 0xd8, //0x00001387 orq          %rbx, %r8
	0x48, 0xc1, 0xe6, 0x30, //0x0000138a shlq         $48, %rsi
	0x48, 0xc1, 0xe0, 0x20, //0x0000138e shlq         $32, %rax
	0x48, 0x09, 0xf0, //0x00001392 orq          %rsi, %rax
	0x48, 0xc1, 0xe2, 0x10, //0x00001395 shlq         $16, %rdx
	0x48, 0x09, 0xc2, //0x00001399 orq          %rax, %rdx
	0x49, 0x09, 0xd7, //0x0000139c orq          %rdx, %r15
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x0000139f jne          LBB0_265
	0x4d, 0x85, 0xd2, //0x000013a5 testq        %r10, %r10
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x000013a8 jne          LBB0_267
	0x45, 0x31, 0xd2, //0x000013ae xorl         %r10d, %r10d
	0x4d, 0x85, 0xc0, //0x000013b1 testq        %r8, %r8
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x000013b4 jne          LBB0_268
	//0x000013ba LBB0_259
	0x49, 0x83, 0xc6, 0xc0, //0x000013ba addq         $-64, %r14
	0x49, 0x83, 0xc4, 0xc0, //0x000013be addq         $-64, %r12
	0x49, 0x83, 0xc3, 0x40, //0x000013c2 addq         $64, %r11
	0x49, 0x83, 0xfe, 0x3f, //0x000013c6 cmpq         $63, %r14
	0x0f, 0x87, 0x30, 0xff, 0xff, 0xff, //0x000013ca ja           LBB0_256
	0xe9, 0x8b, 0x18, 0x00, 0x00, //0x000013d0 jmp          LBB0_260
	//0x000013d5 LBB0_265
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x000013d5 cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x000013da jne          LBB0_267
	0x49, 0x0f, 0xbc, 0xc7, //0x000013e0 bsfq         %r15, %rax
	0x4c, 0x01, 0xd8, //0x000013e4 addq         %r11, %rax
	0x48, 0x89, 0x45, 0xd0, //0x000013e7 movq         %rax, $-48(%rbp)
	//0x000013eb LBB0_267
	0x4c, 0x89, 0xd0, //0x000013eb movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x000013ee notq         %rax
	0x4c, 0x21, 0xf8, //0x000013f1 andq         %r15, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x000013f4 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd1, //0x000013f8 orq          %r10, %rcx
	0x48, 0x89, 0xca, //0x000013fb movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x000013fe notq         %rdx
	0x4c, 0x21, 0xfa, //0x00001401 andq         %r15, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001404 movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x0000140e andq         %rsi, %rdx
	0x45, 0x31, 0xd2, //0x00001411 xorl         %r10d, %r10d
	0x48, 0x01, 0xc2, //0x00001414 addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc2, //0x00001417 setb         %r10b
	0x48, 0x01, 0xd2, //0x0000141b addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000141e movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00001428 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x0000142b andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x0000142e notq         %rdx
	0x49, 0x21, 0xd0, //0x00001431 andq         %rdx, %r8
	0x4d, 0x85, 0xc0, //0x00001434 testq        %r8, %r8
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00001437 je           LBB0_259
	//0x0000143d LBB0_268
	0x4d, 0x0f, 0xbc, 0xd8, //0x0000143d bsfq         %r8, %r11
	0x4d, 0x29, 0xe3, //0x00001441 subq         %r12, %r11
	//0x00001444 LBB0_269
	0x48, 0x8b, 0x55, 0xc8, //0x00001444 movq         $-56(%rbp), %rdx
	0x4c, 0x8b, 0x45, 0xb8, //0x00001448 movq         $-72(%rbp), %r8
	0x4d, 0x85, 0xdb, //0x0000144c testq        %r11, %r11
	0x0f, 0x89, 0x02, 0xfe, 0xff, 0xff, //0x0000144f jns          LBB0_246
	0xe9, 0x48, 0x24, 0x00, 0x00, //0x00001455 jmp          LBB0_434
	//0x0000145a LBB0_270
	0xf6, 0x45, 0x98, 0x40, //0x0000145a testb        $64, $-104(%rbp)
	0x0f, 0x85, 0xa7, 0x04, 0x00, 0x00, //0x0000145e jne          LBB0_344
	0x49, 0x8b, 0x45, 0x00, //0x00001464 movq         (%r13), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001468 cmpq         $4095, %rax
	0x0f, 0x8f, 0x33, 0x22, 0x00, 0x00, //0x0000146e jg           LBB0_634
	0x48, 0x8d, 0x48, 0x01, //0x00001474 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x00001478 movq         %rcx, (%r13)
	0x49, 0xc7, 0x44, 0xc5, 0x08, 0x05, 0x00, 0x00, 0x00, //0x0000147c movq         $5, $8(%r13,%rax,8)
	0xe9, 0x46, 0xed, 0xff, 0xff, //0x00001485 jmp          LBB0_3
	//0x0000148a LBB0_273
	0x48, 0x8b, 0x45, 0xa0, //0x0000148a movq         $-96(%rbp), %rax
	0x4c, 0x8b, 0x60, 0x08, //0x0000148e movq         $8(%rax), %r12
	0xf6, 0x45, 0x98, 0x40, //0x00001492 testb        $64, $-104(%rbp)
	0x0f, 0x85, 0xad, 0x08, 0x00, 0x00, //0x00001496 jne          LBB0_371
	0x4d, 0x29, 0xdc, //0x0000149c subq         %r11, %r12
	0x0f, 0x84, 0xe3, 0x23, 0x00, 0x00, //0x0000149f je           LBB0_635
	0x4b, 0x8d, 0x04, 0x19, //0x000014a5 leaq         (%r9,%r11), %rax
	0x48, 0x89, 0x45, 0xb0, //0x000014a9 movq         %rax, $-80(%rbp)
	0x80, 0x38, 0x30, //0x000014ad cmpb         $48, (%rax)
	0x0f, 0x85, 0x39, 0x00, 0x00, 0x00, //0x000014b0 jne          LBB0_279
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000014b6 movl         $1, %eax
	0x49, 0x83, 0xfc, 0x01, //0x000014bb cmpq         $1, %r12
	0x0f, 0x84, 0x2b, 0x11, 0x00, 0x00, //0x000014bf je           LBB0_460
	0x48, 0x8b, 0x4d, 0xb0, //0x000014c5 movq         $-80(%rbp), %rcx
	0x8a, 0x49, 0x01, //0x000014c9 movb         $1(%rcx), %cl
	0x80, 0xc1, 0xd2, //0x000014cc addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x000014cf cmpb         $55, %cl
	0x0f, 0x87, 0x18, 0x11, 0x00, 0x00, //0x000014d2 ja           LBB0_460
	0x0f, 0xb6, 0xc9, //0x000014d8 movzbl       %cl, %ecx
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000014db movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xca, //0x000014e5 btq          %rcx, %rdx
	0x0f, 0x83, 0x01, 0x11, 0x00, 0x00, //0x000014e9 jae          LBB0_460
	//0x000014ef LBB0_279
	0x49, 0x83, 0xfc, 0x10, //0x000014ef cmpq         $16, %r12
	0x0f, 0x82, 0x00, 0x1b, 0x00, 0x00, //0x000014f3 jb           LBB0_536
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000014f9 movq         $-1, %r8
	0x31, 0xc0, //0x00001500 xorl         %eax, %eax
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00001502 movq         $-1, %r15
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00001509 movq         $-1, %r14
	0x4d, 0x89, 0xe2, //0x00001510 movq         %r12, %r10
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001513 .p2align 4, 0x90
	//0x00001520 LBB0_281
	0x48, 0x8b, 0x4d, 0xb0, //0x00001520 movq         $-80(%rbp), %rcx
	0xf3, 0x0f, 0x6f, 0x04, 0x01, //0x00001524 movdqu       (%rcx,%rax), %xmm0
	0x66, 0x0f, 0x6f, 0xc8, //0x00001529 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x74, 0xcf, //0x0000152d pcmpeqb      %xmm7, %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00001531 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0x15, 0x73, 0xeb, 0xff, 0xff, //0x00001535 pcmpeqb      $-5261(%rip), %xmm2  /* LCPI0_11+0(%rip) */
	0x66, 0x0f, 0xeb, 0xd1, //0x0000153d por          %xmm1, %xmm2
	0x66, 0x0f, 0x6f, 0xc8, //0x00001541 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0xfc, 0x0d, 0x73, 0xeb, 0xff, 0xff, //0x00001545 paddb        $-5261(%rip), %xmm1  /* LCPI0_12+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd9, //0x0000154d movdqa       %xmm1, %xmm3
	0x66, 0x0f, 0xda, 0x1d, 0x77, 0xeb, 0xff, 0xff, //0x00001551 pminub       $-5257(%rip), %xmm3  /* LCPI0_13+0(%rip) */
	0x66, 0x0f, 0x74, 0xd9, //0x00001559 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0x6f, 0xc8, //0x0000155d movdqa       %xmm0, %xmm1
	0x66, 0x41, 0x0f, 0xdb, 0xc9, //0x00001561 pand         %xmm9, %xmm1
	0x66, 0x0f, 0x74, 0x05, 0x72, 0xeb, 0xff, 0xff, //0x00001566 pcmpeqb      $-5262(%rip), %xmm0  /* LCPI0_14+0(%rip) */
	0x66, 0x41, 0x0f, 0x74, 0xcf, //0x0000156e pcmpeqb      %xmm15, %xmm1
	0x66, 0x0f, 0xd7, 0xf9, //0x00001573 pmovmskb     %xmm1, %edi
	0x66, 0x0f, 0xeb, 0xc8, //0x00001577 por          %xmm0, %xmm1
	0x66, 0x0f, 0xeb, 0xca, //0x0000157b por          %xmm2, %xmm1
	0x66, 0x0f, 0xeb, 0xcb, //0x0000157f por          %xmm3, %xmm1
	0x66, 0x0f, 0xd7, 0xd0, //0x00001583 pmovmskb     %xmm0, %edx
	0x66, 0x0f, 0xd7, 0xf2, //0x00001587 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0xd7, 0xc9, //0x0000158b pmovmskb     %xmm1, %ecx
	0xf7, 0xd1, //0x0000158f notl         %ecx
	0x0f, 0xbc, 0xc9, //0x00001591 bsfl         %ecx, %ecx
	0x83, 0xf9, 0x10, //0x00001594 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00001597 je           LBB0_283
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x0000159d movl         $-1, %ebx
	0xd3, 0xe3, //0x000015a2 shll         %cl, %ebx
	0xf7, 0xd3, //0x000015a4 notl         %ebx
	0x21, 0xda, //0x000015a6 andl         %ebx, %edx
	0x21, 0xdf, //0x000015a8 andl         %ebx, %edi
	0x21, 0xf3, //0x000015aa andl         %esi, %ebx
	0x89, 0xde, //0x000015ac movl         %ebx, %esi
	//0x000015ae LBB0_283
	0x8d, 0x5a, 0xff, //0x000015ae leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x000015b1 andl         %edx, %ebx
	0x0f, 0x85, 0x64, 0x16, 0x00, 0x00, //0x000015b3 jne          LBB0_502
	0x8d, 0x5f, 0xff, //0x000015b9 leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x000015bc andl         %edi, %ebx
	0x0f, 0x85, 0x59, 0x16, 0x00, 0x00, //0x000015be jne          LBB0_502
	0x8d, 0x5e, 0xff, //0x000015c4 leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x000015c7 andl         %esi, %ebx
	0x0f, 0x85, 0x4e, 0x16, 0x00, 0x00, //0x000015c9 jne          LBB0_502
	0x85, 0xd2, //0x000015cf testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000015d1 je           LBB0_289
	0x0f, 0xbc, 0xd2, //0x000015d7 bsfl         %edx, %edx
	0x49, 0x83, 0xfe, 0xff, //0x000015da cmpq         $-1, %r14
	0x0f, 0x85, 0x6f, 0x16, 0x00, 0x00, //0x000015de jne          LBB0_506
	0x48, 0x01, 0xc2, //0x000015e4 addq         %rax, %rdx
	0x49, 0x89, 0xd6, //0x000015e7 movq         %rdx, %r14
	//0x000015ea LBB0_289
	0x85, 0xff, //0x000015ea testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000015ec je           LBB0_292
	0x0f, 0xbc, 0xd7, //0x000015f2 bsfl         %edi, %edx
	0x49, 0x83, 0xff, 0xff, //0x000015f5 cmpq         $-1, %r15
	0x0f, 0x85, 0x54, 0x16, 0x00, 0x00, //0x000015f9 jne          LBB0_506
	0x48, 0x01, 0xc2, //0x000015ff addq         %rax, %rdx
	0x49, 0x89, 0xd7, //0x00001602 movq         %rdx, %r15
	//0x00001605 LBB0_292
	0x85, 0xf6, //0x00001605 testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00001607 je           LBB0_295
	0x0f, 0xbc, 0xd6, //0x0000160d bsfl         %esi, %edx
	0x49, 0x83, 0xf8, 0xff, //0x00001610 cmpq         $-1, %r8
	0x0f, 0x85, 0x39, 0x16, 0x00, 0x00, //0x00001614 jne          LBB0_506
	0x48, 0x01, 0xc2, //0x0000161a addq         %rax, %rdx
	0x49, 0x89, 0xd0, //0x0000161d movq         %rdx, %r8
	//0x00001620 LBB0_295
	0x83, 0xf9, 0x10, //0x00001620 cmpl         $16, %ecx
	0x0f, 0x85, 0x2e, 0x0f, 0x00, 0x00, //0x00001623 jne          LBB0_447
	0x49, 0x83, 0xc2, 0xf0, //0x00001629 addq         $-16, %r10
	0x48, 0x83, 0xc0, 0x10, //0x0000162d addq         $16, %rax
	0x49, 0x83, 0xfa, 0x0f, //0x00001631 cmpq         $15, %r10
	0x0f, 0x87, 0xe5, 0xfe, 0xff, 0xff, //0x00001635 ja           LBB0_281
	0x48, 0x8b, 0x4d, 0xb0, //0x0000163b movq         $-80(%rbp), %rcx
	0x48, 0x01, 0xc1, //0x0000163f addq         %rax, %rcx
	0x48, 0x89, 0xca, //0x00001642 movq         %rcx, %rdx
	0x49, 0x39, 0xc4, //0x00001645 cmpq         %rax, %r12
	0x0f, 0x84, 0x2d, 0x0f, 0x00, 0x00, //0x00001648 je           LBB0_449
	//0x0000164e LBB0_298
	0x4e, 0x8d, 0x24, 0x11, //0x0000164e leaq         (%rcx,%r10), %r12
	0x48, 0x89, 0xc8, //0x00001652 movq         %rcx, %rax
	0x48, 0x2b, 0x45, 0xc8, //0x00001655 subq         $-56(%rbp), %rax
	0x4c, 0x89, 0xce, //0x00001659 movq         %r9, %rsi
	0x48, 0xf7, 0xd6, //0x0000165c notq         %rsi
	0x48, 0x01, 0xc6, //0x0000165f addq         %rax, %rsi
	0x31, 0xc0, //0x00001662 xorl         %eax, %eax
	0xe9, 0x24, 0x00, 0x00, 0x00, //0x00001664 jmp          LBB0_302
	//0x00001669 LBB0_299
	0x49, 0x83, 0xf8, 0xff, //0x00001669 cmpq         $-1, %r8
	0x0f, 0x85, 0xce, 0x15, 0x00, 0x00, //0x0000166d jne          LBB0_505
	0x4c, 0x8d, 0x04, 0x06, //0x00001673 leaq         (%rsi,%rax), %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001677 .p2align 4, 0x90
	//0x00001680 LBB0_301
	0x48, 0x83, 0xc0, 0x01, //0x00001680 addq         $1, %rax
	0x49, 0x39, 0xc2, //0x00001684 cmpq         %rax, %r10
	0x0f, 0x84, 0xab, 0x12, 0x00, 0x00, //0x00001687 je           LBB0_476
	//0x0000168d LBB0_302
	0x0f, 0xbe, 0x14, 0x01, //0x0000168d movsbl       (%rcx,%rax), %edx
	0x8d, 0x7a, 0xd0, //0x00001691 leal         $-48(%rdx), %edi
	0x83, 0xff, 0x0a, //0x00001694 cmpl         $10, %edi
	0x0f, 0x82, 0xe3, 0xff, 0xff, 0xff, //0x00001697 jb           LBB0_301
	0x8d, 0x7a, 0xd5, //0x0000169d leal         $-43(%rdx), %edi
	0x83, 0xff, 0x1a, //0x000016a0 cmpl         $26, %edi
	0x0f, 0x87, 0x23, 0x00, 0x00, 0x00, //0x000016a3 ja           LBB0_307
	0x48, 0x8d, 0x1d, 0xec, 0x24, 0x00, 0x00, //0x000016a9 leaq         $9452(%rip), %rbx  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x14, 0xbb, //0x000016b0 movslq       (%rbx,%rdi,4), %rdx
	0x48, 0x01, 0xda, //0x000016b4 addq         %rbx, %rdx
	0xff, 0xe2, //0x000016b7 jmpq         *%rdx
	//0x000016b9 LBB0_305
	0x49, 0x83, 0xfe, 0xff, //0x000016b9 cmpq         $-1, %r14
	0x0f, 0x85, 0x7e, 0x15, 0x00, 0x00, //0x000016bd jne          LBB0_505
	0x4c, 0x8d, 0x34, 0x06, //0x000016c3 leaq         (%rsi,%rax), %r14
	0xe9, 0xb4, 0xff, 0xff, 0xff, //0x000016c7 jmp          LBB0_301
	//0x000016cc LBB0_307
	0x83, 0xfa, 0x65, //0x000016cc cmpl         $101, %edx
	0x0f, 0x85, 0xa0, 0x0e, 0x00, 0x00, //0x000016cf jne          LBB0_448
	//0x000016d5 LBB0_308
	0x49, 0x83, 0xff, 0xff, //0x000016d5 cmpq         $-1, %r15
	0x0f, 0x85, 0x62, 0x15, 0x00, 0x00, //0x000016d9 jne          LBB0_505
	0x4c, 0x8d, 0x3c, 0x06, //0x000016df leaq         (%rsi,%rax), %r15
	0xe9, 0x98, 0xff, 0xff, 0xff, //0x000016e3 jmp          LBB0_301
	//0x000016e8 LBB0_310
	0x48, 0x8b, 0x4d, 0xa0, //0x000016e8 movq         $-96(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x000016ec movq         $8(%rcx), %rcx
	0x48, 0x8d, 0x51, 0xfd, //0x000016f0 leaq         $-3(%rcx), %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x000016f4 movq         $-56(%rbp), %rsi
	0x48, 0x39, 0xd6, //0x000016f8 cmpq         %rdx, %rsi
	0x0f, 0x83, 0x12, 0x22, 0x00, 0x00, //0x000016fb jae          LBB0_643
	0x41, 0x81, 0x38, 0x6e, 0x75, 0x6c, 0x6c, //0x00001701 cmpl         $1819047278, (%r8)
	0x0f, 0x84, 0x92, 0xea, 0xff, 0xff, //0x00001708 je           LBB0_1
	0xe9, 0x3f, 0x20, 0x00, 0x00, //0x0000170e jmp          LBB0_312
	//0x00001713 LBB0_317
	0xf6, 0x45, 0x98, 0x40, //0x00001713 testb        $64, $-104(%rbp)
	0x0f, 0x85, 0xed, 0x06, 0x00, 0x00, //0x00001717 jne          LBB0_383
	0x49, 0x8b, 0x45, 0x00, //0x0000171d movq         (%r13), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001721 cmpq         $4095, %rax
	0x0f, 0x8f, 0x7a, 0x1f, 0x00, 0x00, //0x00001727 jg           LBB0_634
	0x48, 0x8d, 0x48, 0x01, //0x0000172d leaq         $1(%rax), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x00001731 movq         %rcx, (%r13)
	0x49, 0xc7, 0x44, 0xc5, 0x08, 0x06, 0x00, 0x00, 0x00, //0x00001735 movq         $6, $8(%r13,%rax,8)
	0xe9, 0x8d, 0xea, 0xff, 0xff, //0x0000173e jmp          LBB0_3
	//0x00001743 LBB0_320
	0x48, 0x8b, 0x4d, 0xa0, //0x00001743 movq         $-96(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x00001747 movq         $8(%rcx), %rcx
	0x48, 0x8d, 0x51, 0xfd, //0x0000174b leaq         $-3(%rcx), %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x0000174f movq         $-56(%rbp), %rsi
	0x48, 0x39, 0xd6, //0x00001753 cmpq         %rdx, %rsi
	0x0f, 0x83, 0xb7, 0x21, 0x00, 0x00, //0x00001756 jae          LBB0_643
	0x41, 0x81, 0x38, 0x74, 0x72, 0x75, 0x65, //0x0000175c cmpl         $1702195828, (%r8)
	0x0f, 0x84, 0x37, 0xea, 0xff, 0xff, //0x00001763 je           LBB0_1
	0xe9, 0x93, 0x20, 0x00, 0x00, //0x00001769 jmp          LBB0_322
	//0x0000176e LBB0_326
	0x4c, 0x89, 0xe0, //0x0000176e movq         %r12, %rax
	0x4c, 0x09, 0xd0, //0x00001771 orq          %r10, %rax
	0x0f, 0x99, 0xc0, //0x00001774 setns        %al
	0x0f, 0x88, 0x49, 0x0b, 0x00, 0x00, //0x00001777 js           LBB0_620
	0x4d, 0x39, 0xd4, //0x0000177d cmpq         %r10, %r12
	0x0f, 0x8c, 0x40, 0x0b, 0x00, 0x00, //0x00001780 jl           LBB0_620
	0x49, 0xf7, 0xd4, //0x00001786 notq         %r12
	0x4d, 0x89, 0xe3, //0x00001789 movq         %r12, %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x0000178c movq         $-64(%rbp), %r13
	0x4d, 0x85, 0xdb, //0x00001790 testq        %r11, %r11
	0x0f, 0x89, 0x81, 0xfa, 0xff, 0xff, //0x00001793 jns          LBB0_242
	0xe9, 0x3e, 0x1f, 0x00, 0x00, //0x00001799 jmp          LBB0_621
	//0x0000179e LBB0_329
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x0000179e movl         $64, %ecx
	0x48, 0x8b, 0x55, 0xc8, //0x000017a3 movq         $-56(%rbp), %rdx
	0x48, 0x39, 0xc1, //0x000017a7 cmpq         %rax, %rcx
	0x0f, 0x82, 0x38, 0x21, 0x00, 0x00, //0x000017aa jb           LBB0_225
	//0x000017b0 LBB0_330
	0x49, 0x01, 0xc3, //0x000017b0 addq         %rax, %r11
	0x49, 0x83, 0xc3, 0x01, //0x000017b3 addq         $1, %r11
	0x4d, 0x85, 0xdb, //0x000017b7 testq        %r11, %r11
	0x0f, 0x88, 0x45, 0x1f, 0x00, 0x00, //0x000017ba js           LBB0_127
	//0x000017c0 LBB0_331
	0x48, 0x8b, 0x45, 0xa8, //0x000017c0 movq         $-88(%rbp), %rax
	0x4c, 0x89, 0x18, //0x000017c4 movq         %r11, (%rax)
	0x48, 0x89, 0xd0, //0x000017c7 movq         %rdx, %rax
	0x48, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x000017ca movabsq      $9223372036854775806, %rcx
	0x48, 0x39, 0xca, //0x000017d4 cmpq         %rcx, %rdx
	0x0f, 0x87, 0x16, 0x1f, 0x00, 0x00, //0x000017d7 ja           LBB0_638
	0x49, 0x8b, 0x45, 0x00, //0x000017dd movq         (%r13), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x000017e1 cmpq         $4095, %rax
	0x0f, 0x8f, 0xba, 0x1e, 0x00, 0x00, //0x000017e7 jg           LBB0_634
	0x48, 0x8d, 0x48, 0x01, //0x000017ed leaq         $1(%rax), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x000017f1 movq         %rcx, (%r13)
	0x49, 0xc7, 0x44, 0xc5, 0x08, 0x04, 0x00, 0x00, 0x00, //0x000017f5 movq         $4, $8(%r13,%rax,8)
	0xe9, 0xcd, 0xe9, 0xff, 0xff, //0x000017fe jmp          LBB0_3
	//0x00001803 LBB0_334
	0x4d, 0x89, 0xc6, //0x00001803 movq         %r8, %r14
	0x4d, 0x29, 0xde, //0x00001806 subq         %r11, %r14
	0x49, 0x83, 0xfe, 0x20, //0x00001809 cmpq         $32, %r14
	0x0f, 0x8c, 0xc9, 0x17, 0x00, 0x00, //0x0000180d jl           LBB0_534
	0x48, 0x8b, 0x4d, 0xc8, //0x00001813 movq         $-56(%rbp), %rcx
	0x4d, 0x8d, 0x14, 0x09, //0x00001817 leaq         (%r9,%rcx), %r10
	0x49, 0x29, 0xc8, //0x0000181b subq         %rcx, %r8
	0xbb, 0x1f, 0x00, 0x00, 0x00, //0x0000181e movl         $31, %ebx
	0x45, 0x31, 0xf6, //0x00001823 xorl         %r14d, %r14d
	0x45, 0x31, 0xdb, //0x00001826 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001829 .p2align 4, 0x90
	//0x00001830 LBB0_336
	0xf3, 0x43, 0x0f, 0x6f, 0x44, 0x32, 0x01, //0x00001830 movdqu       $1(%r10,%r14), %xmm0
	0xf3, 0x43, 0x0f, 0x6f, 0x4c, 0x32, 0x11, //0x00001837 movdqu       $17(%r10,%r14), %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x0000183e movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x00001842 pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00001847 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd1, //0x0000184b movdqa       %xmm1, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x0000184f pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00001854 pmovmskb     %xmm2, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x00001858 shlq         $16, %rdx
	0x48, 0x09, 0xca, //0x0000185c orq          %rcx, %rdx
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x0000185f pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00001864 pmovmskb     %xmm0, %edi
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x00001868 pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x0000186d pmovmskb     %xmm1, %ecx
	0x48, 0xc1, 0xe1, 0x10, //0x00001871 shlq         $16, %rcx
	0x48, 0x09, 0xf9, //0x00001875 orq          %rdi, %rcx
	0x48, 0x89, 0xcf, //0x00001878 movq         %rcx, %rdi
	0x4c, 0x09, 0xdf, //0x0000187b orq          %r11, %rdi
	0x0f, 0x84, 0x3c, 0x00, 0x00, 0x00, //0x0000187e je           LBB0_338
	0x44, 0x89, 0xdf, //0x00001884 movl         %r11d, %edi
	0xf7, 0xd7, //0x00001887 notl         %edi
	0x21, 0xcf, //0x00001889 andl         %ecx, %edi
	0x44, 0x8d, 0x3c, 0x3f, //0x0000188b leal         (%rdi,%rdi), %r15d
	0x45, 0x09, 0xdf, //0x0000188f orl          %r11d, %r15d
	0x44, 0x89, 0xfe, //0x00001892 movl         %r15d, %esi
	0xf7, 0xd6, //0x00001895 notl         %esi
	0x21, 0xce, //0x00001897 andl         %ecx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001899 andl         $-1431655766, %esi
	0x45, 0x31, 0xdb, //0x0000189f xorl         %r11d, %r11d
	0x01, 0xfe, //0x000018a2 addl         %edi, %esi
	0x41, 0x0f, 0x92, 0xc3, //0x000018a4 setb         %r11b
	0x01, 0xf6, //0x000018a8 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x000018aa xorl         $1431655765, %esi
	0x44, 0x21, 0xfe, //0x000018b0 andl         %r15d, %esi
	0xf7, 0xd6, //0x000018b3 notl         %esi
	0x21, 0xf2, //0x000018b5 andl         %esi, %edx
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x000018b7 jmp          LBB0_339
	0x90, 0x90, 0x90, 0x90, //0x000018bc .p2align 4, 0x90
	//0x000018c0 LBB0_338
	0x45, 0x31, 0xdb, //0x000018c0 xorl         %r11d, %r11d
	//0x000018c3 LBB0_339
	0x48, 0x85, 0xd2, //0x000018c3 testq        %rdx, %rdx
	0x0f, 0x85, 0xd3, 0x09, 0x00, 0x00, //0x000018c6 jne          LBB0_412
	0x49, 0x83, 0xc6, 0x20, //0x000018cc addq         $32, %r14
	0x49, 0x8d, 0x0c, 0x18, //0x000018d0 leaq         (%r8,%rbx), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x000018d4 addq         $-32, %rcx
	0x48, 0x83, 0xc3, 0xe0, //0x000018d8 addq         $-32, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x000018dc cmpq         $63, %rcx
	0x0f, 0x8f, 0x4a, 0xff, 0xff, 0xff, //0x000018e0 jg           LBB0_336
	0x4d, 0x85, 0xdb, //0x000018e6 testq        %r11, %r11
	0x0f, 0x85, 0xc0, 0x1b, 0x00, 0x00, //0x000018e9 jne          LBB0_593
	0x4f, 0x8d, 0x1c, 0x16, //0x000018ef leaq         (%r14,%r10), %r11
	0x49, 0x83, 0xc3, 0x01, //0x000018f3 addq         $1, %r11
	0x49, 0xf7, 0xd6, //0x000018f7 notq         %r14
	0x4d, 0x01, 0xc6, //0x000018fa addq         %r8, %r14
	//0x000018fd LBB0_343
	0x4d, 0x85, 0xf6, //0x000018fd testq        %r14, %r14
	0x0f, 0x8f, 0x19, 0x0c, 0x00, 0x00, //0x00001900 jg           LBB0_444
	0xe9, 0xe8, 0x1d, 0x00, 0x00, //0x00001906 jmp          LBB0_638
	//0x0000190b LBB0_344
	0x48, 0x8b, 0x4d, 0xa0, //0x0000190b movq         $-96(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x0000190f movq         $8(%rcx), %rcx
	0x4c, 0x29, 0xd9, //0x00001913 subq         %r11, %rcx
	0x4d, 0x01, 0xd9, //0x00001916 addq         %r11, %r9
	0x45, 0x31, 0xf6, //0x00001919 xorl         %r14d, %r14d
	0x45, 0x31, 0xc0, //0x0000191c xorl         %r8d, %r8d
	0x45, 0x31, 0xdb, //0x0000191f xorl         %r11d, %r11d
	0x45, 0x31, 0xe4, //0x00001922 xorl         %r12d, %r12d
	0x48, 0x83, 0xf9, 0x40, //0x00001925 cmpq         $64, %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x00001929 movq         %rcx, $-80(%rbp)
	0x0f, 0x8d, 0x4a, 0x01, 0x00, 0x00, //0x0000192d jge          LBB0_345
	//0x00001933 LBB0_354
	0x48, 0x85, 0xc9, //0x00001933 testq        %rcx, %rcx
	0x0f, 0x8e, 0xcf, 0x1f, 0x00, 0x00, //0x00001936 jle          LBB0_642
	0x66, 0x0f, 0xef, 0xc0, //0x0000193c pxor         %xmm0, %xmm0
	0xf3, 0x0f, 0x7f, 0x45, 0x80, //0x00001940 movdqu       %xmm0, $-128(%rbp)
	0xf3, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00001945 movdqu       %xmm0, $-144(%rbp)
	0xf3, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x0000194d movdqu       %xmm0, $-160(%rbp)
	0xf3, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00001955 movdqu       %xmm0, $-176(%rbp)
	0x4c, 0x89, 0xcf, //0x0000195d movq         %r9, %rdi
	0x44, 0x89, 0xca, //0x00001960 movl         %r9d, %edx
	0x81, 0xe2, 0xff, 0x0f, 0x00, 0x00, //0x00001963 andl         $4095, %edx
	0x81, 0xfa, 0xc1, 0x0f, 0x00, 0x00, //0x00001969 cmpl         $4033, %edx
	0x0f, 0x82, 0x3a, 0x00, 0x00, 0x00, //0x0000196f jb           LBB0_358
	0x48, 0x83, 0x7d, 0xb0, 0x20, //0x00001975 cmpq         $32, $-80(%rbp)
	0x0f, 0x82, 0x37, 0x00, 0x00, 0x00, //0x0000197a jb           LBB0_359
	0x0f, 0x10, 0x07, //0x00001980 movups       (%rdi), %xmm0
	0x0f, 0x11, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00001983 movups       %xmm0, $-176(%rbp)
	0xf3, 0x0f, 0x6f, 0x47, 0x10, //0x0000198a movdqu       $16(%rdi), %xmm0
	0xf3, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x0000198f movdqu       %xmm0, $-160(%rbp)
	0x48, 0x83, 0xc7, 0x20, //0x00001997 addq         $32, %rdi
	0x48, 0x8b, 0x4d, 0xb0, //0x0000199b movq         $-80(%rbp), %rcx
	0x48, 0x8d, 0x71, 0xe0, //0x0000199f leaq         $-32(%rcx), %rsi
	0x4c, 0x8d, 0x95, 0x70, 0xff, 0xff, 0xff, //0x000019a3 leaq         $-144(%rbp), %r10
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x000019aa jmp          LBB0_360
	//0x000019af LBB0_358
	0x49, 0x89, 0xf9, //0x000019af movq         %rdi, %r9
	0xe9, 0xc6, 0x00, 0x00, 0x00, //0x000019b2 jmp          LBB0_345
	//0x000019b7 LBB0_359
	0x4c, 0x8d, 0x95, 0x50, 0xff, 0xff, 0xff, //0x000019b7 leaq         $-176(%rbp), %r10
	0x48, 0x8b, 0x75, 0xb0, //0x000019be movq         $-80(%rbp), %rsi
	//0x000019c2 LBB0_360
	0x48, 0x83, 0xfe, 0x10, //0x000019c2 cmpq         $16, %rsi
	0x0f, 0x82, 0x4c, 0x00, 0x00, 0x00, //0x000019c6 jb           LBB0_361
	0xf3, 0x0f, 0x6f, 0x07, //0x000019cc movdqu       (%rdi), %xmm0
	0xf3, 0x41, 0x0f, 0x7f, 0x02, //0x000019d0 movdqu       %xmm0, (%r10)
	0x48, 0x83, 0xc7, 0x10, //0x000019d5 addq         $16, %rdi
	0x49, 0x83, 0xc2, 0x10, //0x000019d9 addq         $16, %r10
	0x48, 0x83, 0xc6, 0xf0, //0x000019dd addq         $-16, %rsi
	0x48, 0x83, 0xfe, 0x08, //0x000019e1 cmpq         $8, %rsi
	0x0f, 0x83, 0x37, 0x00, 0x00, 0x00, //0x000019e5 jae          LBB0_366
	//0x000019eb LBB0_362
	0x48, 0x83, 0xfe, 0x04, //0x000019eb cmpq         $4, %rsi
	0x0f, 0x82, 0x49, 0x00, 0x00, 0x00, //0x000019ef jb           LBB0_363
	//0x000019f5 LBB0_367
	0x8b, 0x17, //0x000019f5 movl         (%rdi), %edx
	0x41, 0x89, 0x12, //0x000019f7 movl         %edx, (%r10)
	0x48, 0x83, 0xc7, 0x04, //0x000019fa addq         $4, %rdi
	0x49, 0x83, 0xc2, 0x04, //0x000019fe addq         $4, %r10
	0x48, 0x83, 0xc6, 0xfc, //0x00001a02 addq         $-4, %rsi
	0x48, 0x83, 0xfe, 0x02, //0x00001a06 cmpq         $2, %rsi
	0x0f, 0x83, 0x38, 0x00, 0x00, 0x00, //0x00001a0a jae          LBB0_364
	//0x00001a10 LBB0_368
	0x48, 0x89, 0xfa, //0x00001a10 movq         %rdi, %rdx
	0xe9, 0x49, 0x00, 0x00, 0x00, //0x00001a13 jmp          LBB0_369
	//0x00001a18 LBB0_361
	0x48, 0x83, 0xfe, 0x08, //0x00001a18 cmpq         $8, %rsi
	0x0f, 0x82, 0xc9, 0xff, 0xff, 0xff, //0x00001a1c jb           LBB0_362
	//0x00001a22 LBB0_366
	0x48, 0x8b, 0x17, //0x00001a22 movq         (%rdi), %rdx
	0x49, 0x89, 0x12, //0x00001a25 movq         %rdx, (%r10)
	0x48, 0x83, 0xc7, 0x08, //0x00001a28 addq         $8, %rdi
	0x49, 0x83, 0xc2, 0x08, //0x00001a2c addq         $8, %r10
	0x48, 0x83, 0xc6, 0xf8, //0x00001a30 addq         $-8, %rsi
	0x48, 0x83, 0xfe, 0x04, //0x00001a34 cmpq         $4, %rsi
	0x0f, 0x83, 0xb7, 0xff, 0xff, 0xff, //0x00001a38 jae          LBB0_367
	//0x00001a3e LBB0_363
	0x48, 0x83, 0xfe, 0x02, //0x00001a3e cmpq         $2, %rsi
	0x0f, 0x82, 0xc8, 0xff, 0xff, 0xff, //0x00001a42 jb           LBB0_368
	//0x00001a48 LBB0_364
	0x48, 0x89, 0xf9, //0x00001a48 movq         %rdi, %rcx
	0x0f, 0xb7, 0x17, //0x00001a4b movzwl       (%rdi), %edx
	0x66, 0x41, 0x89, 0x12, //0x00001a4e movw         %dx, (%r10)
	0x48, 0x83, 0xc1, 0x02, //0x00001a52 addq         $2, %rcx
	0x49, 0x83, 0xc2, 0x02, //0x00001a56 addq         $2, %r10
	0x48, 0x83, 0xc6, 0xfe, //0x00001a5a addq         $-2, %rsi
	0x48, 0x89, 0xca, //0x00001a5e movq         %rcx, %rdx
	//0x00001a61 LBB0_369
	0x4c, 0x8d, 0x8d, 0x50, 0xff, 0xff, 0xff, //0x00001a61 leaq         $-176(%rbp), %r9
	0x48, 0x85, 0xf6, //0x00001a68 testq        %rsi, %rsi
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00001a6b je           LBB0_345
	0x8a, 0x12, //0x00001a71 movb         (%rdx), %dl
	0x41, 0x88, 0x12, //0x00001a73 movb         %dl, (%r10)
	0x4c, 0x8d, 0x8d, 0x50, 0xff, 0xff, 0xff, //0x00001a76 leaq         $-176(%rbp), %r9
	//0x00001a7d LBB0_345
	0xf3, 0x41, 0x0f, 0x6f, 0x31, //0x00001a7d movdqu       (%r9), %xmm6
	0xf3, 0x41, 0x0f, 0x6f, 0x69, 0x10, //0x00001a82 movdqu       $16(%r9), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x61, 0x20, //0x00001a88 movdqu       $32(%r9), %xmm4
	0xf3, 0x41, 0x0f, 0x6f, 0x59, 0x30, //0x00001a8e movdqu       $48(%r9), %xmm3
	0x66, 0x0f, 0x6f, 0xc6, //0x00001a94 movdqa       %xmm6, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00001a98 pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xf0, //0x00001a9d pmovmskb     %xmm0, %esi
	0x66, 0x0f, 0x6f, 0xc5, //0x00001aa1 movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00001aa5 pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00001aaa pmovmskb     %xmm0, %edx
	0x66, 0x0f, 0x6f, 0xc4, //0x00001aae movdqa       %xmm4, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00001ab2 pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00001ab7 pmovmskb     %xmm0, %edi
	0x66, 0x0f, 0x6f, 0xc3, //0x00001abb movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00001abf pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xd8, //0x00001ac4 pmovmskb     %xmm0, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00001ac8 shlq         $48, %rbx
	0x48, 0xc1, 0xe7, 0x20, //0x00001acc shlq         $32, %rdi
	0x48, 0x09, 0xdf, //0x00001ad0 orq          %rbx, %rdi
	0x48, 0xc1, 0xe2, 0x10, //0x00001ad3 shlq         $16, %rdx
	0x48, 0x09, 0xfa, //0x00001ad7 orq          %rdi, %rdx
	0x48, 0x09, 0xd6, //0x00001ada orq          %rdx, %rsi
	0x48, 0x89, 0xf2, //0x00001add movq         %rsi, %rdx
	0x4c, 0x09, 0xc2, //0x00001ae0 orq          %r8, %rdx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00001ae3 jne          LBB0_347
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00001ae9 movq         $-1, %rsi
	0x45, 0x31, 0xc0, //0x00001af0 xorl         %r8d, %r8d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00001af3 jmp          LBB0_348
	//0x00001af8 LBB0_347
	0x4c, 0x89, 0xc2, //0x00001af8 movq         %r8, %rdx
	0x48, 0xf7, 0xd2, //0x00001afb notq         %rdx
	0x48, 0x21, 0xf2, //0x00001afe andq         %rsi, %rdx
	0x4c, 0x8d, 0x14, 0x12, //0x00001b01 leaq         (%rdx,%rdx), %r10
	0x4d, 0x09, 0xc2, //0x00001b05 orq          %r8, %r10
	0x4c, 0x89, 0xd7, //0x00001b08 movq         %r10, %rdi
	0x48, 0xf7, 0xd7, //0x00001b0b notq         %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001b0e movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xde, //0x00001b18 andq         %rbx, %rsi
	0x48, 0x21, 0xfe, //0x00001b1b andq         %rdi, %rsi
	0x45, 0x31, 0xc0, //0x00001b1e xorl         %r8d, %r8d
	0x48, 0x01, 0xd6, //0x00001b21 addq         %rdx, %rsi
	0x41, 0x0f, 0x92, 0xc0, //0x00001b24 setb         %r8b
	0x48, 0x01, 0xf6, //0x00001b28 addq         %rsi, %rsi
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001b2b movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd6, //0x00001b35 xorq         %rdx, %rsi
	0x4c, 0x21, 0xd6, //0x00001b38 andq         %r10, %rsi
	0x48, 0xf7, 0xd6, //0x00001b3b notq         %rsi
	//0x00001b3e LBB0_348
	0x66, 0x0f, 0x6f, 0xc3, //0x00001b3e movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x00001b42 pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00001b47 pmovmskb     %xmm0, %edx
	0x48, 0xc1, 0xe2, 0x30, //0x00001b4b shlq         $48, %rdx
	0x66, 0x0f, 0x6f, 0xc4, //0x00001b4f movdqa       %xmm4, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x00001b53 pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00001b58 pmovmskb     %xmm0, %edi
	0x48, 0xc1, 0xe7, 0x20, //0x00001b5c shlq         $32, %rdi
	0x48, 0x09, 0xd7, //0x00001b60 orq          %rdx, %rdi
	0x66, 0x0f, 0x6f, 0xc5, //0x00001b63 movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x00001b67 pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00001b6c pmovmskb     %xmm0, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x00001b70 shlq         $16, %rdx
	0x48, 0x09, 0xfa, //0x00001b74 orq          %rdi, %rdx
	0x66, 0x0f, 0x6f, 0xc6, //0x00001b77 movdqa       %xmm6, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x00001b7b pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00001b80 pmovmskb     %xmm0, %edi
	0x48, 0x09, 0xd7, //0x00001b84 orq          %rdx, %rdi
	0x48, 0x21, 0xf7, //0x00001b87 andq         %rsi, %rdi
	0x66, 0x48, 0x0f, 0x6e, 0xc7, //0x00001b8a movq         %rdi, %xmm0
	0x66, 0x0f, 0x3a, 0x44, 0x05, 0x67, 0xe5, 0xff, 0xff, 0x00, //0x00001b8f pclmulqdq    $0, $-6809(%rip), %xmm0  /* LCPI0_16+0(%rip) */
	0x66, 0x49, 0x0f, 0x7e, 0xc2, //0x00001b99 movq         %xmm0, %r10
	0x4d, 0x31, 0xf2, //0x00001b9e xorq         %r14, %r10
	0x66, 0x0f, 0x6f, 0xc6, //0x00001ba1 movdqa       %xmm6, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0xb3, 0xe4, 0xff, 0xff, //0x00001ba5 movdqu       $-6989(%rip), %xmm1  /* LCPI0_6+0(%rip) */
	0x66, 0x0f, 0x74, 0xc1, //0x00001bad pcmpeqb      %xmm1, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xe8, //0x00001bb1 pmovmskb     %xmm0, %r13d
	0x66, 0x0f, 0x6f, 0xc5, //0x00001bb6 movdqa       %xmm5, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x00001bba pcmpeqb      %xmm1, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00001bbe pmovmskb     %xmm0, %edx
	0x66, 0x0f, 0x6f, 0xc4, //0x00001bc2 movdqa       %xmm4, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x00001bc6 pcmpeqb      %xmm1, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00001bca pmovmskb     %xmm0, %edi
	0x66, 0x0f, 0x6f, 0xc3, //0x00001bce movdqa       %xmm3, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x00001bd2 pcmpeqb      %xmm1, %xmm0
	0x66, 0x0f, 0xd7, 0xf0, //0x00001bd6 pmovmskb     %xmm0, %esi
	0x48, 0xc1, 0xe6, 0x30, //0x00001bda shlq         $48, %rsi
	0x48, 0xc1, 0xe7, 0x20, //0x00001bde shlq         $32, %rdi
	0x48, 0x09, 0xf7, //0x00001be2 orq          %rsi, %rdi
	0x48, 0xc1, 0xe2, 0x10, //0x00001be5 shlq         $16, %rdx
	0x48, 0x09, 0xfa, //0x00001be9 orq          %rdi, %rdx
	0x49, 0x09, 0xd5, //0x00001bec orq          %rdx, %r13
	0x4d, 0x89, 0xd6, //0x00001bef movq         %r10, %r14
	0x49, 0xf7, 0xd6, //0x00001bf2 notq         %r14
	0x4d, 0x21, 0xf5, //0x00001bf5 andq         %r14, %r13
	0x66, 0x41, 0x0f, 0x74, 0xf0, //0x00001bf8 pcmpeqb      %xmm8, %xmm6
	0x66, 0x0f, 0xd7, 0xfe, //0x00001bfd pmovmskb     %xmm6, %edi
	0x66, 0x41, 0x0f, 0x74, 0xe8, //0x00001c01 pcmpeqb      %xmm8, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x00001c06 pmovmskb     %xmm5, %esi
	0x66, 0x41, 0x0f, 0x74, 0xe0, //0x00001c0a pcmpeqb      %xmm8, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x00001c0f pmovmskb     %xmm4, %edx
	0x66, 0x41, 0x0f, 0x74, 0xd8, //0x00001c13 pcmpeqb      %xmm8, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xfb, //0x00001c18 pmovmskb     %xmm3, %r15d
	0x49, 0xc1, 0xe7, 0x30, //0x00001c1d shlq         $48, %r15
	0x48, 0xc1, 0xe2, 0x20, //0x00001c21 shlq         $32, %rdx
	0x4c, 0x09, 0xfa, //0x00001c25 orq          %r15, %rdx
	0x48, 0xc1, 0xe6, 0x10, //0x00001c28 shlq         $16, %rsi
	0x48, 0x09, 0xd6, //0x00001c2c orq          %rdx, %rsi
	0x48, 0x09, 0xf7, //0x00001c2f orq          %rsi, %rdi
	0x4c, 0x21, 0xf7, //0x00001c32 andq         %r14, %rdi
	0x0f, 0x84, 0x8c, 0x00, 0x00, 0x00, //0x00001c35 je           LBB0_352
	0x49, 0xbe, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001c3b movabsq      $6148914691236517205, %r14
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001c45 .p2align 4, 0x90
	//0x00001c50 LBB0_350
	0x48, 0x8d, 0x5f, 0xff, //0x00001c50 leaq         $-1(%rdi), %rbx
	0x48, 0x89, 0xda, //0x00001c54 movq         %rbx, %rdx
	0x4c, 0x21, 0xea, //0x00001c57 andq         %r13, %rdx
	0x48, 0x89, 0xd6, //0x00001c5a movq         %rdx, %rsi
	0x48, 0xd1, 0xee, //0x00001c5d shrq         %rsi
	0x4c, 0x21, 0xf6, //0x00001c60 andq         %r14, %rsi
	0x48, 0x29, 0xf2, //0x00001c63 subq         %rsi, %rdx
	0x48, 0x89, 0xd6, //0x00001c66 movq         %rdx, %rsi
	0x48, 0xb9, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00001c69 movabsq      $3689348814741910323, %rcx
	0x48, 0x21, 0xce, //0x00001c73 andq         %rcx, %rsi
	0x48, 0xc1, 0xea, 0x02, //0x00001c76 shrq         $2, %rdx
	0x48, 0x21, 0xca, //0x00001c7a andq         %rcx, %rdx
	0x48, 0x01, 0xf2, //0x00001c7d addq         %rsi, %rdx
	0x48, 0x89, 0xd6, //0x00001c80 movq         %rdx, %rsi
	0x48, 0xc1, 0xee, 0x04, //0x00001c83 shrq         $4, %rsi
	0x48, 0x01, 0xd6, //0x00001c87 addq         %rdx, %rsi
	0x48, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001c8a movabsq      $1085102592571150095, %rcx
	0x48, 0x21, 0xce, //0x00001c94 andq         %rcx, %rsi
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00001c97 movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xf1, //0x00001ca1 imulq        %rcx, %rsi
	0x48, 0xc1, 0xee, 0x38, //0x00001ca5 shrq         $56, %rsi
	0x4c, 0x01, 0xde, //0x00001ca9 addq         %r11, %rsi
	0x4c, 0x39, 0xe6, //0x00001cac cmpq         %r12, %rsi
	0x0f, 0x86, 0x94, 0x05, 0x00, 0x00, //0x00001caf jbe          LBB0_410
	0x49, 0x83, 0xc4, 0x01, //0x00001cb5 addq         $1, %r12
	0x48, 0x21, 0xdf, //0x00001cb9 andq         %rbx, %rdi
	0x0f, 0x85, 0x8e, 0xff, 0xff, 0xff, //0x00001cbc jne          LBB0_350
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00001cc2 jmp          LBB0_353
	//0x00001cc7 LBB0_352
	0x49, 0xbe, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001cc7 movabsq      $6148914691236517205, %r14
	//0x00001cd1 LBB0_353
	0x49, 0xc1, 0xfa, 0x3f, //0x00001cd1 sarq         $63, %r10
	0x4c, 0x89, 0xea, //0x00001cd5 movq         %r13, %rdx
	0x48, 0xd1, 0xea, //0x00001cd8 shrq         %rdx
	0x4c, 0x21, 0xf2, //0x00001cdb andq         %r14, %rdx
	0x49, 0x29, 0xd5, //0x00001cde subq         %rdx, %r13
	0x4c, 0x89, 0xea, //0x00001ce1 movq         %r13, %rdx
	0x48, 0xb9, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00001ce4 movabsq      $3689348814741910323, %rcx
	0x48, 0x21, 0xca, //0x00001cee andq         %rcx, %rdx
	0x49, 0xc1, 0xed, 0x02, //0x00001cf1 shrq         $2, %r13
	0x49, 0x21, 0xcd, //0x00001cf5 andq         %rcx, %r13
	0x49, 0x01, 0xd5, //0x00001cf8 addq         %rdx, %r13
	0x4c, 0x89, 0xea, //0x00001cfb movq         %r13, %rdx
	0x48, 0xc1, 0xea, 0x04, //0x00001cfe shrq         $4, %rdx
	0x4c, 0x01, 0xea, //0x00001d02 addq         %r13, %rdx
	0x48, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00001d05 movabsq      $1085102592571150095, %rcx
	0x48, 0x21, 0xca, //0x00001d0f andq         %rcx, %rdx
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00001d12 movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xd1, //0x00001d1c imulq        %rcx, %rdx
	0x48, 0xc1, 0xea, 0x38, //0x00001d20 shrq         $56, %rdx
	0x49, 0x01, 0xd3, //0x00001d24 addq         %rdx, %r11
	0x49, 0x83, 0xc1, 0x40, //0x00001d27 addq         $64, %r9
	0x48, 0x8b, 0x4d, 0xb0, //0x00001d2b movq         $-80(%rbp), %rcx
	0x48, 0x83, 0xc1, 0xc0, //0x00001d2f addq         $-64, %rcx
	0x4d, 0x89, 0xd6, //0x00001d33 movq         %r10, %r14
	0x48, 0x83, 0xf9, 0x40, //0x00001d36 cmpq         $64, %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x00001d3a movq         %rcx, $-80(%rbp)
	0x0f, 0x8d, 0x39, 0xfd, 0xff, 0xff, //0x00001d3e jge          LBB0_345
	0xe9, 0xea, 0xfb, 0xff, 0xff, //0x00001d44 jmp          LBB0_354
	//0x00001d49 LBB0_371
	0x4c, 0x89, 0xe0, //0x00001d49 movq         %r12, %rax
	0x4c, 0x29, 0xd8, //0x00001d4c subq         %r11, %rax
	0x48, 0x83, 0xf8, 0x10, //0x00001d4f cmpq         $16, %rax
	0x0f, 0x82, 0x8b, 0x12, 0x00, 0x00, //0x00001d53 jb           LBB0_535
	0x48, 0x8b, 0x75, 0xc8, //0x00001d59 movq         $-56(%rbp), %rsi
	0x48, 0x89, 0xf0, //0x00001d5d movq         %rsi, %rax
	0x48, 0xf7, 0xd0, //0x00001d60 notq         %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001d63 .p2align 4, 0x90
	//0x00001d70 LBB0_373
	0xf3, 0x43, 0x0f, 0x6f, 0x04, 0x19, //0x00001d70 movdqu       (%r9,%r11), %xmm0
	0x66, 0x0f, 0x6f, 0xc8, //0x00001d76 movdqa       %xmm0, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xce, //0x00001d7a pcmpeqb      %xmm14, %xmm1
	0x66, 0x41, 0x0f, 0xdb, 0xc1, //0x00001d7f pand         %xmm9, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc0, //0x00001d84 pcmpeqb      %xmm8, %xmm0
	0x66, 0x0f, 0xeb, 0xc1, //0x00001d89 por          %xmm1, %xmm0
	0x66, 0x0f, 0xd7, 0xc8, //0x00001d8d pmovmskb     %xmm0, %ecx
	0x85, 0xc9, //0x00001d91 testl        %ecx, %ecx
	0x0f, 0x85, 0x1d, 0x05, 0x00, 0x00, //0x00001d93 jne          LBB0_413
	0x49, 0x83, 0xc3, 0x10, //0x00001d99 addq         $16, %r11
	0x49, 0x8d, 0x0c, 0x04, //0x00001d9d leaq         (%r12,%rax), %rcx
	0x48, 0x83, 0xc1, 0xf0, //0x00001da1 addq         $-16, %rcx
	0x48, 0x83, 0xc0, 0xf0, //0x00001da5 addq         $-16, %rax
	0x48, 0x83, 0xf9, 0x0f, //0x00001da9 cmpq         $15, %rcx
	0x0f, 0x87, 0xbd, 0xff, 0xff, 0xff, //0x00001dad ja           LBB0_373
	0x4d, 0x89, 0xcb, //0x00001db3 movq         %r9, %r11
	0x49, 0x29, 0xc3, //0x00001db6 subq         %rax, %r11
	0x49, 0x01, 0xc4, //0x00001db9 addq         %rax, %r12
	0x4c, 0x89, 0xe0, //0x00001dbc movq         %r12, %rax
	0x48, 0x85, 0xc0, //0x00001dbf testq        %rax, %rax
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00001dc2 je           LBB0_382
	//0x00001dc8 LBB0_376
	0x49, 0x8d, 0x14, 0x03, //0x00001dc8 leaq         (%r11,%rax), %rdx
	0x31, 0xc9, //0x00001dcc xorl         %ecx, %ecx
	//0x00001dce LBB0_377
	0x41, 0x0f, 0xb6, 0x1c, 0x0b, //0x00001dce movzbl       (%r11,%rcx), %ebx
	0x80, 0xfb, 0x2c, //0x00001dd3 cmpb         $44, %bl
	0x0f, 0x84, 0x36, 0x0e, 0x00, 0x00, //0x00001dd6 je           LBB0_501
	0x80, 0xfb, 0x7d, //0x00001ddc cmpb         $125, %bl
	0x0f, 0x84, 0x2d, 0x0e, 0x00, 0x00, //0x00001ddf je           LBB0_501
	0x80, 0xfb, 0x5d, //0x00001de5 cmpb         $93, %bl
	0x0f, 0x84, 0x24, 0x0e, 0x00, 0x00, //0x00001de8 je           LBB0_501
	0x48, 0x83, 0xc1, 0x01, //0x00001dee addq         $1, %rcx
	0x48, 0x39, 0xc8, //0x00001df2 cmpq         %rcx, %rax
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x00001df5 jne          LBB0_377
	0x49, 0x89, 0xd3, //0x00001dfb movq         %rdx, %r11
	//0x00001dfe LBB0_382
	0x4d, 0x29, 0xcb, //0x00001dfe subq         %r9, %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x00001e01 movq         $-64(%rbp), %r13
	0xe9, 0x9a, 0xe3, 0xff, 0xff, //0x00001e05 jmp          LBB0_2
	//0x00001e0a LBB0_383
	0x48, 0x8b, 0x4d, 0xa0, //0x00001e0a movq         $-96(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x00001e0e movq         $8(%rcx), %rcx
	0x4c, 0x29, 0xd9, //0x00001e12 subq         %r11, %rcx
	0x4d, 0x01, 0xd9, //0x00001e15 addq         %r11, %r9
	0x45, 0x31, 0xf6, //0x00001e18 xorl         %r14d, %r14d
	0x45, 0x31, 0xc0, //0x00001e1b xorl         %r8d, %r8d
	0x45, 0x31, 0xdb, //0x00001e1e xorl         %r11d, %r11d
	0x45, 0x31, 0xe4, //0x00001e21 xorl         %r12d, %r12d
	0x48, 0x83, 0xf9, 0x40, //0x00001e24 cmpq         $64, %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x00001e28 movq         %rcx, $-80(%rbp)
	0x0f, 0x8d, 0x4a, 0x01, 0x00, 0x00, //0x00001e2c jge          LBB0_384
	//0x00001e32 LBB0_393
	0x48, 0x85, 0xc9, //0x00001e32 testq        %rcx, %rcx
	0x0f, 0x8e, 0xd0, 0x1a, 0x00, 0x00, //0x00001e35 jle          LBB0_642
	0x66, 0x0f, 0xef, 0xc0, //0x00001e3b pxor         %xmm0, %xmm0
	0xf3, 0x0f, 0x7f, 0x45, 0x80, //0x00001e3f movdqu       %xmm0, $-128(%rbp)
	0xf3, 0x0f, 0x7f, 0x85, 0x70, 0xff, 0xff, 0xff, //0x00001e44 movdqu       %xmm0, $-144(%rbp)
	0xf3, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00001e4c movdqu       %xmm0, $-160(%rbp)
	0xf3, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00001e54 movdqu       %xmm0, $-176(%rbp)
	0x4c, 0x89, 0xcf, //0x00001e5c movq         %r9, %rdi
	0x44, 0x89, 0xca, //0x00001e5f movl         %r9d, %edx
	0x81, 0xe2, 0xff, 0x0f, 0x00, 0x00, //0x00001e62 andl         $4095, %edx
	0x81, 0xfa, 0xc1, 0x0f, 0x00, 0x00, //0x00001e68 cmpl         $4033, %edx
	0x0f, 0x82, 0x3a, 0x00, 0x00, 0x00, //0x00001e6e jb           LBB0_397
	0x48, 0x83, 0x7d, 0xb0, 0x20, //0x00001e74 cmpq         $32, $-80(%rbp)
	0x0f, 0x82, 0x37, 0x00, 0x00, 0x00, //0x00001e79 jb           LBB0_398
	0x0f, 0x10, 0x07, //0x00001e7f movups       (%rdi), %xmm0
	0x0f, 0x11, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00001e82 movups       %xmm0, $-176(%rbp)
	0xf3, 0x0f, 0x6f, 0x47, 0x10, //0x00001e89 movdqu       $16(%rdi), %xmm0
	0xf3, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00001e8e movdqu       %xmm0, $-160(%rbp)
	0x48, 0x83, 0xc7, 0x20, //0x00001e96 addq         $32, %rdi
	0x48, 0x8b, 0x4d, 0xb0, //0x00001e9a movq         $-80(%rbp), %rcx
	0x48, 0x8d, 0x71, 0xe0, //0x00001e9e leaq         $-32(%rcx), %rsi
	0x4c, 0x8d, 0x95, 0x70, 0xff, 0xff, 0xff, //0x00001ea2 leaq         $-144(%rbp), %r10
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00001ea9 jmp          LBB0_399
	//0x00001eae LBB0_397
	0x49, 0x89, 0xf9, //0x00001eae movq         %rdi, %r9
	0xe9, 0xc6, 0x00, 0x00, 0x00, //0x00001eb1 jmp          LBB0_384
	//0x00001eb6 LBB0_398
	0x4c, 0x8d, 0x95, 0x50, 0xff, 0xff, 0xff, //0x00001eb6 leaq         $-176(%rbp), %r10
	0x48, 0x8b, 0x75, 0xb0, //0x00001ebd movq         $-80(%rbp), %rsi
	//0x00001ec1 LBB0_399
	0x48, 0x83, 0xfe, 0x10, //0x00001ec1 cmpq         $16, %rsi
	0x0f, 0x82, 0x4c, 0x00, 0x00, 0x00, //0x00001ec5 jb           LBB0_400
	0xf3, 0x0f, 0x6f, 0x07, //0x00001ecb movdqu       (%rdi), %xmm0
	0xf3, 0x41, 0x0f, 0x7f, 0x02, //0x00001ecf movdqu       %xmm0, (%r10)
	0x48, 0x83, 0xc7, 0x10, //0x00001ed4 addq         $16, %rdi
	0x49, 0x83, 0xc2, 0x10, //0x00001ed8 addq         $16, %r10
	0x48, 0x83, 0xc6, 0xf0, //0x00001edc addq         $-16, %rsi
	0x48, 0x83, 0xfe, 0x08, //0x00001ee0 cmpq         $8, %rsi
	0x0f, 0x83, 0x37, 0x00, 0x00, 0x00, //0x00001ee4 jae          LBB0_405
	//0x00001eea LBB0_401
	0x48, 0x83, 0xfe, 0x04, //0x00001eea cmpq         $4, %rsi
	0x0f, 0x82, 0x49, 0x00, 0x00, 0x00, //0x00001eee jb           LBB0_402
	//0x00001ef4 LBB0_406
	0x8b, 0x17, //0x00001ef4 movl         (%rdi), %edx
	0x41, 0x89, 0x12, //0x00001ef6 movl         %edx, (%r10)
	0x48, 0x83, 0xc7, 0x04, //0x00001ef9 addq         $4, %rdi
	0x49, 0x83, 0xc2, 0x04, //0x00001efd addq         $4, %r10
	0x48, 0x83, 0xc6, 0xfc, //0x00001f01 addq         $-4, %rsi
	0x48, 0x83, 0xfe, 0x02, //0x00001f05 cmpq         $2, %rsi
	0x0f, 0x83, 0x38, 0x00, 0x00, 0x00, //0x00001f09 jae          LBB0_403
	//0x00001f0f LBB0_407
	0x48, 0x89, 0xfa, //0x00001f0f movq         %rdi, %rdx
	0xe9, 0x49, 0x00, 0x00, 0x00, //0x00001f12 jmp          LBB0_408
	//0x00001f17 LBB0_400
	0x48, 0x83, 0xfe, 0x08, //0x00001f17 cmpq         $8, %rsi
	0x0f, 0x82, 0xc9, 0xff, 0xff, 0xff, //0x00001f1b jb           LBB0_401
	//0x00001f21 LBB0_405
	0x48, 0x8b, 0x17, //0x00001f21 movq         (%rdi), %rdx
	0x49, 0x89, 0x12, //0x00001f24 movq         %rdx, (%r10)
	0x48, 0x83, 0xc7, 0x08, //0x00001f27 addq         $8, %rdi
	0x49, 0x83, 0xc2, 0x08, //0x00001f2b addq         $8, %r10
	0x48, 0x83, 0xc6, 0xf8, //0x00001f2f addq         $-8, %rsi
	0x48, 0x83, 0xfe, 0x04, //0x00001f33 cmpq         $4, %rsi
	0x0f, 0x83, 0xb7, 0xff, 0xff, 0xff, //0x00001f37 jae          LBB0_406
	//0x00001f3d LBB0_402
	0x48, 0x83, 0xfe, 0x02, //0x00001f3d cmpq         $2, %rsi
	0x0f, 0x82, 0xc8, 0xff, 0xff, 0xff, //0x00001f41 jb           LBB0_407
	//0x00001f47 LBB0_403
	0x48, 0x89, 0xf9, //0x00001f47 movq         %rdi, %rcx
	0x0f, 0xb7, 0x17, //0x00001f4a movzwl       (%rdi), %edx
	0x66, 0x41, 0x89, 0x12, //0x00001f4d movw         %dx, (%r10)
	0x48, 0x83, 0xc1, 0x02, //0x00001f51 addq         $2, %rcx
	0x49, 0x83, 0xc2, 0x02, //0x00001f55 addq         $2, %r10
	0x48, 0x83, 0xc6, 0xfe, //0x00001f59 addq         $-2, %rsi
	0x48, 0x89, 0xca, //0x00001f5d movq         %rcx, %rdx
	//0x00001f60 LBB0_408
	0x4c, 0x8d, 0x8d, 0x50, 0xff, 0xff, 0xff, //0x00001f60 leaq         $-176(%rbp), %r9
	0x48, 0x85, 0xf6, //0x00001f67 testq        %rsi, %rsi
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00001f6a je           LBB0_384
	0x8a, 0x12, //0x00001f70 movb         (%rdx), %dl
	0x41, 0x88, 0x12, //0x00001f72 movb         %dl, (%r10)
	0x4c, 0x8d, 0x8d, 0x50, 0xff, 0xff, 0xff, //0x00001f75 leaq         $-176(%rbp), %r9
	//0x00001f7c LBB0_384
	0xf3, 0x41, 0x0f, 0x6f, 0x31, //0x00001f7c movdqu       (%r9), %xmm6
	0xf3, 0x41, 0x0f, 0x6f, 0x69, 0x10, //0x00001f81 movdqu       $16(%r9), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x61, 0x20, //0x00001f87 movdqu       $32(%r9), %xmm4
	0xf3, 0x41, 0x0f, 0x6f, 0x59, 0x30, //0x00001f8d movdqu       $48(%r9), %xmm3
	0x66, 0x0f, 0x6f, 0xc6, //0x00001f93 movdqa       %xmm6, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00001f97 pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xf0, //0x00001f9c pmovmskb     %xmm0, %esi
	0x66, 0x0f, 0x6f, 0xc5, //0x00001fa0 movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00001fa4 pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00001fa9 pmovmskb     %xmm0, %edx
	0x66, 0x0f, 0x6f, 0xc4, //0x00001fad movdqa       %xmm4, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00001fb1 pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00001fb6 pmovmskb     %xmm0, %edi
	0x66, 0x0f, 0x6f, 0xc3, //0x00001fba movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00001fbe pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xd8, //0x00001fc3 pmovmskb     %xmm0, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00001fc7 shlq         $48, %rbx
	0x48, 0xc1, 0xe7, 0x20, //0x00001fcb shlq         $32, %rdi
	0x48, 0x09, 0xdf, //0x00001fcf orq          %rbx, %rdi
	0x48, 0xc1, 0xe2, 0x10, //0x00001fd2 shlq         $16, %rdx
	0x48, 0x09, 0xfa, //0x00001fd6 orq          %rdi, %rdx
	0x48, 0x09, 0xd6, //0x00001fd9 orq          %rdx, %rsi
	0x48, 0x89, 0xf2, //0x00001fdc movq         %rsi, %rdx
	0x4c, 0x09, 0xc2, //0x00001fdf orq          %r8, %rdx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00001fe2 jne          LBB0_386
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00001fe8 movq         $-1, %rsi
	0x45, 0x31, 0xc0, //0x00001fef xorl         %r8d, %r8d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00001ff2 jmp          LBB0_387
	//0x00001ff7 LBB0_386
	0x4c, 0x89, 0xc2, //0x00001ff7 movq         %r8, %rdx
	0x48, 0xf7, 0xd2, //0x00001ffa notq         %rdx
	0x48, 0x21, 0xf2, //0x00001ffd andq         %rsi, %rdx
	0x4c, 0x8d, 0x14, 0x12, //0x00002000 leaq         (%rdx,%rdx), %r10
	0x4d, 0x09, 0xc2, //0x00002004 orq          %r8, %r10
	0x4c, 0x89, 0xd7, //0x00002007 movq         %r10, %rdi
	0x48, 0xf7, 0xd7, //0x0000200a notq         %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000200d movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xde, //0x00002017 andq         %rbx, %rsi
	0x48, 0x21, 0xfe, //0x0000201a andq         %rdi, %rsi
	0x45, 0x31, 0xc0, //0x0000201d xorl         %r8d, %r8d
	0x48, 0x01, 0xd6, //0x00002020 addq         %rdx, %rsi
	0x41, 0x0f, 0x92, 0xc0, //0x00002023 setb         %r8b
	0x48, 0x01, 0xf6, //0x00002027 addq         %rsi, %rsi
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000202a movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd6, //0x00002034 xorq         %rdx, %rsi
	0x4c, 0x21, 0xd6, //0x00002037 andq         %r10, %rsi
	0x48, 0xf7, 0xd6, //0x0000203a notq         %rsi
	//0x0000203d LBB0_387
	0x66, 0x0f, 0x6f, 0xc3, //0x0000203d movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x00002041 pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00002046 pmovmskb     %xmm0, %edx
	0x48, 0xc1, 0xe2, 0x30, //0x0000204a shlq         $48, %rdx
	0x66, 0x0f, 0x6f, 0xc4, //0x0000204e movdqa       %xmm4, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x00002052 pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00002057 pmovmskb     %xmm0, %edi
	0x48, 0xc1, 0xe7, 0x20, //0x0000205b shlq         $32, %rdi
	0x48, 0x09, 0xd7, //0x0000205f orq          %rdx, %rdi
	0x66, 0x0f, 0x6f, 0xc5, //0x00002062 movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x00002066 pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x0000206b pmovmskb     %xmm0, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x0000206f shlq         $16, %rdx
	0x48, 0x09, 0xfa, //0x00002073 orq          %rdi, %rdx
	0x66, 0x0f, 0x6f, 0xc6, //0x00002076 movdqa       %xmm6, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x0000207a pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x0000207f pmovmskb     %xmm0, %edi
	0x48, 0x09, 0xd7, //0x00002083 orq          %rdx, %rdi
	0x48, 0x21, 0xf7, //0x00002086 andq         %rsi, %rdi
	0x66, 0x48, 0x0f, 0x6e, 0xc7, //0x00002089 movq         %rdi, %xmm0
	0x66, 0x0f, 0x3a, 0x44, 0x05, 0x68, 0xe0, 0xff, 0xff, 0x00, //0x0000208e pclmulqdq    $0, $-8088(%rip), %xmm0  /* LCPI0_16+0(%rip) */
	0x66, 0x49, 0x0f, 0x7e, 0xc2, //0x00002098 movq         %xmm0, %r10
	0x4d, 0x31, 0xf2, //0x0000209d xorq         %r14, %r10
	0x66, 0x0f, 0x6f, 0xc6, //0x000020a0 movdqa       %xmm6, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0x94, 0xdf, 0xff, 0xff, //0x000020a4 movdqu       $-8300(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x66, 0x0f, 0x74, 0xc1, //0x000020ac pcmpeqb      %xmm1, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xe8, //0x000020b0 pmovmskb     %xmm0, %r13d
	0x66, 0x0f, 0x6f, 0xc5, //0x000020b5 movdqa       %xmm5, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x000020b9 pcmpeqb      %xmm1, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x000020bd pmovmskb     %xmm0, %edx
	0x66, 0x0f, 0x6f, 0xc4, //0x000020c1 movdqa       %xmm4, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x000020c5 pcmpeqb      %xmm1, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x000020c9 pmovmskb     %xmm0, %edi
	0x66, 0x0f, 0x6f, 0xc3, //0x000020cd movdqa       %xmm3, %xmm0
	0x66, 0x0f, 0x74, 0xc1, //0x000020d1 pcmpeqb      %xmm1, %xmm0
	0x66, 0x0f, 0xd7, 0xf0, //0x000020d5 pmovmskb     %xmm0, %esi
	0x48, 0xc1, 0xe6, 0x30, //0x000020d9 shlq         $48, %rsi
	0x48, 0xc1, 0xe7, 0x20, //0x000020dd shlq         $32, %rdi
	0x48, 0x09, 0xf7, //0x000020e1 orq          %rsi, %rdi
	0x48, 0xc1, 0xe2, 0x10, //0x000020e4 shlq         $16, %rdx
	0x48, 0x09, 0xfa, //0x000020e8 orq          %rdi, %rdx
	0x49, 0x09, 0xd5, //0x000020eb orq          %rdx, %r13
	0x4d, 0x89, 0xd6, //0x000020ee movq         %r10, %r14
	0x49, 0xf7, 0xd6, //0x000020f1 notq         %r14
	0x4d, 0x21, 0xf5, //0x000020f4 andq         %r14, %r13
	0xf3, 0x0f, 0x6f, 0x05, 0x51, 0xdf, 0xff, 0xff, //0x000020f7 movdqu       $-8367(%rip), %xmm0  /* LCPI0_5+0(%rip) */
	0x66, 0x0f, 0x74, 0xf0, //0x000020ff pcmpeqb      %xmm0, %xmm6
	0x66, 0x0f, 0xd7, 0xfe, //0x00002103 pmovmskb     %xmm6, %edi
	0x66, 0x0f, 0x74, 0xe8, //0x00002107 pcmpeqb      %xmm0, %xmm5
	0x66, 0x0f, 0xd7, 0xf5, //0x0000210b pmovmskb     %xmm5, %esi
	0x66, 0x0f, 0x74, 0xe0, //0x0000210f pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x00002113 pmovmskb     %xmm4, %edx
	0x66, 0x0f, 0x74, 0xd8, //0x00002117 pcmpeqb      %xmm0, %xmm3
	0x66, 0x44, 0x0f, 0xd7, 0xfb, //0x0000211b pmovmskb     %xmm3, %r15d
	0x49, 0xc1, 0xe7, 0x30, //0x00002120 shlq         $48, %r15
	0x48, 0xc1, 0xe2, 0x20, //0x00002124 shlq         $32, %rdx
	0x4c, 0x09, 0xfa, //0x00002128 orq          %r15, %rdx
	0x48, 0xc1, 0xe6, 0x10, //0x0000212b shlq         $16, %rsi
	0x48, 0x09, 0xd6, //0x0000212f orq          %rdx, %rsi
	0x48, 0x09, 0xf7, //0x00002132 orq          %rsi, %rdi
	0x4c, 0x21, 0xf7, //0x00002135 andq         %r14, %rdi
	0x0f, 0x84, 0x89, 0x00, 0x00, 0x00, //0x00002138 je           LBB0_391
	0x49, 0xbe, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000213e movabsq      $6148914691236517205, %r14
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002148 .p2align 4, 0x90
	//0x00002150 LBB0_389
	0x48, 0x8d, 0x5f, 0xff, //0x00002150 leaq         $-1(%rdi), %rbx
	0x48, 0x89, 0xda, //0x00002154 movq         %rbx, %rdx
	0x4c, 0x21, 0xea, //0x00002157 andq         %r13, %rdx
	0x48, 0x89, 0xd6, //0x0000215a movq         %rdx, %rsi
	0x48, 0xd1, 0xee, //0x0000215d shrq         %rsi
	0x4c, 0x21, 0xf6, //0x00002160 andq         %r14, %rsi
	0x48, 0x29, 0xf2, //0x00002163 subq         %rsi, %rdx
	0x48, 0x89, 0xd6, //0x00002166 movq         %rdx, %rsi
	0x48, 0xb9, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00002169 movabsq      $3689348814741910323, %rcx
	0x48, 0x21, 0xce, //0x00002173 andq         %rcx, %rsi
	0x48, 0xc1, 0xea, 0x02, //0x00002176 shrq         $2, %rdx
	0x48, 0x21, 0xca, //0x0000217a andq         %rcx, %rdx
	0x48, 0x01, 0xf2, //0x0000217d addq         %rsi, %rdx
	0x48, 0x89, 0xd6, //0x00002180 movq         %rdx, %rsi
	0x48, 0xc1, 0xee, 0x04, //0x00002183 shrq         $4, %rsi
	0x48, 0x01, 0xd6, //0x00002187 addq         %rdx, %rsi
	0x48, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x0000218a movabsq      $1085102592571150095, %rcx
	0x48, 0x21, 0xce, //0x00002194 andq         %rcx, %rsi
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00002197 movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xf1, //0x000021a1 imulq        %rcx, %rsi
	0x48, 0xc1, 0xee, 0x38, //0x000021a5 shrq         $56, %rsi
	0x4c, 0x01, 0xde, //0x000021a9 addq         %r11, %rsi
	0x4c, 0x39, 0xe6, //0x000021ac cmpq         %r12, %rsi
	0x0f, 0x86, 0x94, 0x00, 0x00, 0x00, //0x000021af jbe          LBB0_410
	0x49, 0x83, 0xc4, 0x01, //0x000021b5 addq         $1, %r12
	0x48, 0x21, 0xdf, //0x000021b9 andq         %rbx, %rdi
	0x0f, 0x85, 0x8e, 0xff, 0xff, 0xff, //0x000021bc jne          LBB0_389
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x000021c2 jmp          LBB0_392
	//0x000021c7 LBB0_391
	0x49, 0xbe, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000021c7 movabsq      $6148914691236517205, %r14
	//0x000021d1 LBB0_392
	0x49, 0xc1, 0xfa, 0x3f, //0x000021d1 sarq         $63, %r10
	0x4c, 0x89, 0xea, //0x000021d5 movq         %r13, %rdx
	0x48, 0xd1, 0xea, //0x000021d8 shrq         %rdx
	0x4c, 0x21, 0xf2, //0x000021db andq         %r14, %rdx
	0x49, 0x29, 0xd5, //0x000021de subq         %rdx, %r13
	0x4c, 0x89, 0xea, //0x000021e1 movq         %r13, %rdx
	0x48, 0xb9, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x000021e4 movabsq      $3689348814741910323, %rcx
	0x48, 0x21, 0xca, //0x000021ee andq         %rcx, %rdx
	0x49, 0xc1, 0xed, 0x02, //0x000021f1 shrq         $2, %r13
	0x49, 0x21, 0xcd, //0x000021f5 andq         %rcx, %r13
	0x49, 0x01, 0xd5, //0x000021f8 addq         %rdx, %r13
	0x4c, 0x89, 0xea, //0x000021fb movq         %r13, %rdx
	0x48, 0xc1, 0xea, 0x04, //0x000021fe shrq         $4, %rdx
	0x4c, 0x01, 0xea, //0x00002202 addq         %r13, %rdx
	0x48, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002205 movabsq      $1085102592571150095, %rcx
	0x48, 0x21, 0xca, //0x0000220f andq         %rcx, %rdx
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00002212 movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xd1, //0x0000221c imulq        %rcx, %rdx
	0x48, 0xc1, 0xea, 0x38, //0x00002220 shrq         $56, %rdx
	0x49, 0x01, 0xd3, //0x00002224 addq         %rdx, %r11
	0x49, 0x83, 0xc1, 0x40, //0x00002227 addq         $64, %r9
	0x48, 0x8b, 0x4d, 0xb0, //0x0000222b movq         $-80(%rbp), %rcx
	0x48, 0x83, 0xc1, 0xc0, //0x0000222f addq         $-64, %rcx
	0x4d, 0x89, 0xd6, //0x00002233 movq         %r10, %r14
	0x48, 0x83, 0xf9, 0x40, //0x00002236 cmpq         $64, %rcx
	0x48, 0x89, 0x4d, 0xb0, //0x0000223a movq         %rcx, $-80(%rbp)
	0x0f, 0x8d, 0x38, 0xfd, 0xff, 0xff, //0x0000223e jge          LBB0_384
	0xe9, 0xe9, 0xfb, 0xff, 0xff, //0x00002244 jmp          LBB0_393
	//0x00002249 LBB0_410
	0x48, 0x8b, 0x75, 0xa0, //0x00002249 movq         $-96(%rbp), %rsi
	0x48, 0x8b, 0x4e, 0x08, //0x0000224d movq         $8(%rsi), %rcx
	0x48, 0x0f, 0xbc, 0xd7, //0x00002251 bsfq         %rdi, %rdx
	0x48, 0x2b, 0x55, 0xb0, //0x00002255 subq         $-80(%rbp), %rdx
	0x4c, 0x8d, 0x1c, 0x0a, //0x00002259 leaq         (%rdx,%rcx), %r11
	0x49, 0x83, 0xc3, 0x01, //0x0000225d addq         $1, %r11
	0x48, 0x8b, 0x55, 0xa8, //0x00002261 movq         $-88(%rbp), %rdx
	0x4c, 0x89, 0x1a, //0x00002265 movq         %r11, (%rdx)
	0x48, 0x8b, 0x4e, 0x08, //0x00002268 movq         $8(%rsi), %rcx
	0x49, 0x39, 0xcb, //0x0000226c cmpq         %rcx, %r11
	0x49, 0x0f, 0x46, 0xcb, //0x0000226f cmovbeq      %r11, %rcx
	0x48, 0x89, 0x0a, //0x00002273 movq         %rcx, (%rdx)
	0x0f, 0x87, 0x77, 0x14, 0x00, 0x00, //0x00002276 ja           LBB0_638
	0x48, 0x8b, 0x55, 0xc8, //0x0000227c movq         $-56(%rbp), %rdx
	0x48, 0x89, 0xd0, //0x00002280 movq         %rdx, %rax
	0x48, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00002283 movabsq      $9223372036854775806, %rcx
	0x48, 0x39, 0xca, //0x0000228d cmpq         %rcx, %rdx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002290 movq         $-64(%rbp), %r13
	0x0f, 0x86, 0x36, 0xdf, 0xff, 0xff, //0x00002294 jbe          LBB0_3
	0xe9, 0x54, 0x14, 0x00, 0x00, //0x0000229a jmp          LBB0_638
	//0x0000229f LBB0_412
	0x0f, 0xbc, 0xc2, //0x0000229f bsfl         %edx, %eax
	0x48, 0x8b, 0x55, 0xc8, //0x000022a2 movq         $-56(%rbp), %rdx
	0x48, 0x01, 0xd0, //0x000022a6 addq         %rdx, %rax
	0x4d, 0x8d, 0x1c, 0x06, //0x000022a9 leaq         (%r14,%rax), %r11
	0x49, 0x83, 0xc3, 0x02, //0x000022ad addq         $2, %r11
	0xe9, 0xa1, 0xef, 0xff, 0xff, //0x000022b1 jmp          LBB0_246
	//0x000022b6 LBB0_413
	0x66, 0x0f, 0xbc, 0xc9, //0x000022b6 bsfw         %cx, %cx
	0x44, 0x0f, 0xb7, 0xd9, //0x000022ba movzwl       %cx, %r11d
	0x49, 0x29, 0xc3, //0x000022be subq         %rax, %r11
	0xe9, 0xde, 0xde, 0xff, 0xff, //0x000022c1 jmp          LBB0_2
	//0x000022c6 LBB0_620
	0x49, 0x8d, 0x4a, 0xff, //0x000022c6 leaq         $-1(%r10), %rcx
	0x49, 0x39, 0xcc, //0x000022ca cmpq         %rcx, %r12
	0x49, 0xf7, 0xd2, //0x000022cd notq         %r10
	0x4d, 0x0f, 0x45, 0xd3, //0x000022d0 cmovneq      %r11, %r10
	0x84, 0xc0, //0x000022d4 testb        %al, %al
	0x4d, 0x0f, 0x45, 0xda, //0x000022d6 cmovneq      %r10, %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x000022da movq         $-64(%rbp), %r13
	0x4d, 0x85, 0xdb, //0x000022de testq        %r11, %r11
	0x0f, 0x89, 0x33, 0xef, 0xff, 0xff, //0x000022e1 jns          LBB0_242
	0xe9, 0xf0, 0x13, 0x00, 0x00, //0x000022e7 jmp          LBB0_621
	//0x000022ec LBB0_414
	0x4c, 0x89, 0xc1, //0x000022ec movq         %r8, %rcx
	0x4c, 0x29, 0xd9, //0x000022ef subq         %r11, %rcx
	0x0f, 0x84, 0x02, 0x16, 0x00, 0x00, //0x000022f2 je           LBB0_641
	0x66, 0x44, 0x0f, 0x6f, 0xe7, //0x000022f8 movdqa       %xmm7, %xmm12
	0x48, 0x83, 0xf9, 0x40, //0x000022fd cmpq         $64, %rcx
	0x0f, 0x82, 0x34, 0x0d, 0x00, 0x00, //0x00002301 jb           LBB0_538
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x00002307 movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xf6, //0x0000230f xorl         %r14d, %r14d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002312 .p2align 4, 0x90
	//0x00002320 LBB0_417
	0x48, 0x89, 0x4d, 0xb0, //0x00002320 movq         %rcx, $-80(%rbp)
	0xf3, 0x43, 0x0f, 0x6f, 0x1c, 0x19, //0x00002324 movdqu       (%r9,%r11), %xmm3
	0xf3, 0x43, 0x0f, 0x6f, 0x44, 0x19, 0x10, //0x0000232a movdqu       $16(%r9,%r11), %xmm0
	0xf3, 0x43, 0x0f, 0x6f, 0x6c, 0x19, 0x20, //0x00002331 movdqu       $32(%r9,%r11), %xmm5
	0xf3, 0x43, 0x0f, 0x6f, 0x64, 0x19, 0x30, //0x00002338 movdqu       $48(%r9,%r11), %xmm4
	0x66, 0x0f, 0x6f, 0xcb, //0x0000233f movdqa       %xmm3, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xcb, //0x00002343 pcmpeqb      %xmm11, %xmm1
	0x66, 0x44, 0x0f, 0xd7, 0xf9, //0x00002348 pmovmskb     %xmm1, %r15d
	0x66, 0x0f, 0x6f, 0xc8, //0x0000234d movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00002351 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0xda, 0xd2, //0x00002355 pminub       %xmm10, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x0000235a pcmpeqb      %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x0000235e pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xf0, //0x00002363 pmovmskb     %xmm0, %esi
	0x66, 0x0f, 0x6f, 0xc5, //0x00002367 movdqa       %xmm5, %xmm0
	0x66, 0x0f, 0x6f, 0xf5, //0x0000236b movdqa       %xmm5, %xmm6
	0x66, 0x41, 0x0f, 0xda, 0xf2, //0x0000236f pminub       %xmm10, %xmm6
	0x66, 0x0f, 0x74, 0xf5, //0x00002374 pcmpeqb      %xmm5, %xmm6
	0x66, 0x41, 0x0f, 0x74, 0xeb, //0x00002378 pcmpeqb      %xmm11, %xmm5
	0x66, 0x0f, 0xd7, 0xfd, //0x0000237d pmovmskb     %xmm5, %edi
	0x66, 0x0f, 0x6f, 0xec, //0x00002381 movdqa       %xmm4, %xmm5
	0x66, 0x0f, 0x6f, 0xfc, //0x00002385 movdqa       %xmm4, %xmm7
	0x66, 0x41, 0x0f, 0xda, 0xfa, //0x00002389 pminub       %xmm10, %xmm7
	0x66, 0x0f, 0x74, 0xfc, //0x0000238e pcmpeqb      %xmm4, %xmm7
	0x66, 0x41, 0x0f, 0x74, 0xe3, //0x00002392 pcmpeqb      %xmm11, %xmm4
	0x66, 0x0f, 0xd7, 0xc4, //0x00002397 pmovmskb     %xmm4, %eax
	0x66, 0x0f, 0x6f, 0xe3, //0x0000239b movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe5, //0x0000239f pcmpeqb      %xmm13, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xe4, //0x000023a4 pmovmskb     %xmm4, %r12d
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x000023a9 pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xd1, //0x000023ae pmovmskb     %xmm1, %edx
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x000023b2 pcmpeqb      %xmm13, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xc0, //0x000023b7 pmovmskb     %xmm0, %r8d
	0x66, 0x41, 0x0f, 0x74, 0xed, //0x000023bc pcmpeqb      %xmm13, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xd5, //0x000023c1 pmovmskb     %xmm5, %r10d
	0x66, 0x0f, 0xd7, 0xca, //0x000023c6 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0xd7, 0xde, //0x000023ca pmovmskb     %xmm6, %ebx
	0x66, 0x44, 0x0f, 0xd7, 0xef, //0x000023ce pmovmskb     %xmm7, %r13d
	0x48, 0xc1, 0xe0, 0x30, //0x000023d3 shlq         $48, %rax
	0x48, 0xc1, 0xe7, 0x20, //0x000023d7 shlq         $32, %rdi
	0x48, 0x09, 0xc7, //0x000023db orq          %rax, %rdi
	0x48, 0xc1, 0xe6, 0x10, //0x000023de shlq         $16, %rsi
	0x48, 0x09, 0xfe, //0x000023e2 orq          %rdi, %rsi
	0x49, 0x09, 0xf7, //0x000023e5 orq          %rsi, %r15
	0x49, 0xc1, 0xe2, 0x30, //0x000023e8 shlq         $48, %r10
	0x49, 0xc1, 0xe0, 0x20, //0x000023ec shlq         $32, %r8
	0x4d, 0x09, 0xd0, //0x000023f0 orq          %r10, %r8
	0x48, 0xc1, 0xe2, 0x10, //0x000023f3 shlq         $16, %rdx
	0x4c, 0x09, 0xc2, //0x000023f7 orq          %r8, %rdx
	0x49, 0xc1, 0xe5, 0x30, //0x000023fa shlq         $48, %r13
	0x48, 0xc1, 0xe3, 0x20, //0x000023fe shlq         $32, %rbx
	0x4c, 0x09, 0xeb, //0x00002402 orq          %r13, %rbx
	0x48, 0xc1, 0xe1, 0x10, //0x00002405 shlq         $16, %rcx
	0x48, 0x09, 0xd9, //0x00002409 orq          %rbx, %rcx
	0x49, 0x09, 0xd4, //0x0000240c orq          %rdx, %r12
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x0000240f jne          LBB0_436
	0x4d, 0x85, 0xf6, //0x00002415 testq        %r14, %r14
	0x0f, 0x85, 0x75, 0x00, 0x00, 0x00, //0x00002418 jne          LBB0_438
	0x45, 0x31, 0xf6, //0x0000241e xorl         %r14d, %r14d
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002421 movq         $-64(%rbp), %r13
	0x66, 0x41, 0x0f, 0x6f, 0xfc, //0x00002425 movdqa       %xmm12, %xmm7
	//0x0000242a LBB0_420
	0x66, 0x0f, 0x6f, 0xc3, //0x0000242a movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc2, //0x0000242e pminub       %xmm10, %xmm0
	0x66, 0x0f, 0x74, 0xc3, //0x00002433 pcmpeqb      %xmm3, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00002437 pmovmskb     %xmm0, %eax
	0x48, 0x09, 0xc1, //0x0000243b orq          %rax, %rcx
	0x4d, 0x85, 0xff, //0x0000243e testq        %r15, %r15
	0x4c, 0x8b, 0x45, 0xb8, //0x00002441 movq         $-72(%rbp), %r8
	0x0f, 0x85, 0x9f, 0x00, 0x00, 0x00, //0x00002445 jne          LBB0_440
	0x48, 0x85, 0xc9, //0x0000244b testq        %rcx, %rcx
	0x0f, 0x85, 0x0a, 0x14, 0x00, 0x00, //0x0000244e jne          LBB0_632
	0x48, 0x8b, 0x4d, 0xb0, //0x00002454 movq         $-80(%rbp), %rcx
	0x48, 0x83, 0xc1, 0xc0, //0x00002458 addq         $-64, %rcx
	0x49, 0x83, 0xc3, 0x40, //0x0000245c addq         $64, %r11
	0x48, 0x83, 0xf9, 0x3f, //0x00002460 cmpq         $63, %rcx
	0x0f, 0x87, 0xb6, 0xfe, 0xff, 0xff, //0x00002464 ja           LBB0_417
	0xe9, 0x6b, 0x08, 0x00, 0x00, //0x0000246a jmp          LBB0_423
	//0x0000246f LBB0_436
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x0000246f cmpq         $-1, $-48(%rbp)
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002474 movq         $-64(%rbp), %r13
	0x66, 0x41, 0x0f, 0x6f, 0xfc, //0x00002478 movdqa       %xmm12, %xmm7
	0x0f, 0x85, 0x19, 0x00, 0x00, 0x00, //0x0000247d jne          LBB0_439
	0x49, 0x0f, 0xbc, 0xc4, //0x00002483 bsfq         %r12, %rax
	0x4c, 0x01, 0xd8, //0x00002487 addq         %r11, %rax
	0x48, 0x89, 0x45, 0xd0, //0x0000248a movq         %rax, $-48(%rbp)
	0xe9, 0x09, 0x00, 0x00, 0x00, //0x0000248e jmp          LBB0_439
	//0x00002493 LBB0_438
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002493 movq         $-64(%rbp), %r13
	0x66, 0x41, 0x0f, 0x6f, 0xfc, //0x00002497 movdqa       %xmm12, %xmm7
	//0x0000249c LBB0_439
	0x4c, 0x89, 0xf0, //0x0000249c movq         %r14, %rax
	0x48, 0xf7, 0xd0, //0x0000249f notq         %rax
	0x4c, 0x21, 0xe0, //0x000024a2 andq         %r12, %rax
	0x48, 0x8d, 0x14, 0x00, //0x000024a5 leaq         (%rax,%rax), %rdx
	0x4c, 0x09, 0xf2, //0x000024a9 orq          %r14, %rdx
	0x48, 0x89, 0xd6, //0x000024ac movq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x000024af notq         %rsi
	0x4c, 0x21, 0xe6, //0x000024b2 andq         %r12, %rsi
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000024b5 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x000024bf andq         %rdi, %rsi
	0x45, 0x31, 0xf6, //0x000024c2 xorl         %r14d, %r14d
	0x48, 0x01, 0xc6, //0x000024c5 addq         %rax, %rsi
	0x41, 0x0f, 0x92, 0xc6, //0x000024c8 setb         %r14b
	0x48, 0x01, 0xf6, //0x000024cc addq         %rsi, %rsi
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000024cf movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc6, //0x000024d9 xorq         %rax, %rsi
	0x48, 0x21, 0xd6, //0x000024dc andq         %rdx, %rsi
	0x48, 0xf7, 0xd6, //0x000024df notq         %rsi
	0x49, 0x21, 0xf7, //0x000024e2 andq         %rsi, %r15
	0xe9, 0x40, 0xff, 0xff, 0xff, //0x000024e5 jmp          LBB0_420
	//0x000024ea LBB0_440
	0x49, 0x0f, 0xbc, 0xc7, //0x000024ea bsfq         %r15, %rax
	0x48, 0x85, 0xc9, //0x000024ee testq        %rcx, %rcx
	0x0f, 0x84, 0x0c, 0x01, 0x00, 0x00, //0x000024f1 je           LBB0_461
	0x48, 0x0f, 0xbc, 0xc9, //0x000024f7 bsfq         %rcx, %rcx
	0xe9, 0x08, 0x01, 0x00, 0x00, //0x000024fb jmp          LBB0_462
	//0x00002500 LBB0_442
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002500 movq         $-2, %rcx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00002507 movl         $2, %eax
	0x49, 0x01, 0xc3, //0x0000250c addq         %rax, %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000250f movq         $-1, %rax
	0x49, 0x01, 0xce, //0x00002516 addq         %rcx, %r14
	0x0f, 0x8e, 0xd4, 0x11, 0x00, 0x00, //0x00002519 jle          LBB0_638
	//0x0000251f LBB0_444
	0x41, 0x0f, 0xb6, 0x03, //0x0000251f movzbl       (%r11), %eax
	0x3c, 0x5c, //0x00002523 cmpb         $92, %al
	0x0f, 0x84, 0xd5, 0xff, 0xff, 0xff, //0x00002525 je           LBB0_442
	0x3c, 0x22, //0x0000252b cmpb         $34, %al
	0x0f, 0x84, 0xfe, 0x06, 0x00, 0x00, //0x0000252d je           LBB0_504
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002533 movq         $-1, %rcx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000253a movl         $1, %eax
	0x49, 0x01, 0xc3, //0x0000253f addq         %rax, %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002542 movq         $-1, %rax
	0x49, 0x01, 0xce, //0x00002549 addq         %rcx, %r14
	0x0f, 0x8f, 0xcd, 0xff, 0xff, 0xff, //0x0000254c jg           LBB0_444
	0xe9, 0x9c, 0x11, 0x00, 0x00, //0x00002552 jmp          LBB0_638
	//0x00002557 LBB0_447
	0x89, 0xca, //0x00002557 movl         %ecx, %edx
	0x48, 0x03, 0x55, 0xb0, //0x00002559 addq         $-80(%rbp), %rdx
	0x48, 0x01, 0xc2, //0x0000255d addq         %rax, %rdx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002560 movq         $-1, %rax
	0x4d, 0x85, 0xf6, //0x00002567 testq        %r14, %r14
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x0000256a jne          LBB0_450
	0xe9, 0x1a, 0x13, 0x00, 0x00, //0x00002570 jmp          LBB0_636
	//0x00002575 LBB0_448
	0x48, 0x01, 0xc1, //0x00002575 addq         %rax, %rcx
	0x48, 0x89, 0xca, //0x00002578 movq         %rcx, %rdx
	//0x0000257b LBB0_449
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000257b movq         $-1, %rax
	0x4d, 0x85, 0xf6, //0x00002582 testq        %r14, %r14
	0x0f, 0x84, 0x04, 0x13, 0x00, 0x00, //0x00002585 je           LBB0_636
	//0x0000258b LBB0_450
	0x4d, 0x85, 0xc0, //0x0000258b testq        %r8, %r8
	0x0f, 0x84, 0xfb, 0x12, 0x00, 0x00, //0x0000258e je           LBB0_636
	0x4d, 0x85, 0xff, //0x00002594 testq        %r15, %r15
	0x0f, 0x84, 0xf2, 0x12, 0x00, 0x00, //0x00002597 je           LBB0_636
	0x48, 0x2b, 0x55, 0xb0, //0x0000259d subq         $-80(%rbp), %rdx
	0x48, 0x8d, 0x42, 0xff, //0x000025a1 leaq         $-1(%rdx), %rax
	0x49, 0x39, 0xc6, //0x000025a5 cmpq         %rax, %r14
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x000025a8 je           LBB0_458
	0x49, 0x39, 0xc0, //0x000025ae cmpq         %rax, %r8
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x000025b1 je           LBB0_458
	0x49, 0x39, 0xc7, //0x000025b7 cmpq         %rax, %r15
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x000025ba je           LBB0_458
	0x4d, 0x85, 0xc0, //0x000025c0 testq        %r8, %r8
	0x0f, 0x8e, 0xf5, 0x00, 0x00, 0x00, //0x000025c3 jle          LBB0_466
	0x49, 0x8d, 0x40, 0xff, //0x000025c9 leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc7, //0x000025cd cmpq         %rax, %r15
	0x0f, 0x84, 0xe8, 0x00, 0x00, 0x00, //0x000025d0 je           LBB0_466
	0x49, 0xf7, 0xd0, //0x000025d6 notq         %r8
	0x4c, 0x89, 0xc0, //0x000025d9 movq         %r8, %rax
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x000025dc jmp          LBB0_459
	//0x000025e1 LBB0_458
	0x48, 0xf7, 0xda, //0x000025e1 negq         %rdx
	0x48, 0x89, 0xd0, //0x000025e4 movq         %rdx, %rax
	//0x000025e7 LBB0_459
	0x48, 0x85, 0xc0, //0x000025e7 testq        %rax, %rax
	0x0f, 0x88, 0x9f, 0x12, 0x00, 0x00, //0x000025ea js           LBB0_636
	//0x000025f0 LBB0_460
	0x49, 0x01, 0xc3, //0x000025f0 addq         %rax, %r11
	0x48, 0x8b, 0x45, 0xa8, //0x000025f3 movq         $-88(%rbp), %rax
	0x4c, 0x89, 0x18, //0x000025f7 movq         %r11, (%rax)
	0x48, 0x8b, 0x55, 0xc8, //0x000025fa movq         $-56(%rbp), %rdx
	0xe9, 0x5b, 0xec, 0xff, 0xff, //0x000025fe jmp          LBB0_247
	//0x00002603 LBB0_461
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00002603 movl         $64, %ecx
	//0x00002608 LBB0_462
	0x48, 0x8b, 0x55, 0xc8, //0x00002608 movq         $-56(%rbp), %rdx
	0x48, 0x39, 0xc1, //0x0000260c cmpq         %rax, %rcx
	0x0f, 0x82, 0xd3, 0x12, 0x00, 0x00, //0x0000260f jb           LBB0_225
	0x49, 0x01, 0xc3, //0x00002615 addq         %rax, %r11
	0x49, 0x83, 0xc3, 0x01, //0x00002618 addq         $1, %r11
	0x4d, 0x85, 0xdb, //0x0000261c testq        %r11, %r11
	0x0f, 0x89, 0x32, 0xec, 0xff, 0xff, //0x0000261f jns          LBB0_246
	0xe9, 0x78, 0x12, 0x00, 0x00, //0x00002625 jmp          LBB0_434
	//0x0000262a LBB0_137
	0x4d, 0x85, 0xdb, //0x0000262a testq        %r11, %r11
	0x0f, 0x85, 0x6e, 0x0a, 0x00, 0x00, //0x0000262d jne          LBB0_543
	0x4f, 0x8d, 0x1c, 0x16, //0x00002633 leaq         (%r14,%r10), %r11
	0x49, 0x83, 0xc3, 0x01, //0x00002637 addq         $1, %r11
	0x49, 0xf7, 0xd6, //0x0000263b notq         %r14
	0x4d, 0x01, 0xe6, //0x0000263e addq         %r12, %r14
	//0x00002641 LBB0_139
	0x4d, 0x85, 0xf6, //0x00002641 testq        %r14, %r14
	0x0f, 0x8f, 0x25, 0x00, 0x00, 0x00, //0x00002644 jg           LBB0_193
	0xe9, 0xa4, 0x10, 0x00, 0x00, //0x0000264a jmp          LBB0_638
	0x90, //0x0000264f .p2align 4, 0x90
	//0x00002650 LBB0_191
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002650 movq         $-2, %rcx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00002657 movl         $2, %eax
	0x49, 0x01, 0xc3, //0x0000265c addq         %rax, %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000265f movq         $-1, %rax
	0x49, 0x01, 0xce, //0x00002666 addq         %rcx, %r14
	0x0f, 0x8e, 0x84, 0x10, 0x00, 0x00, //0x00002669 jle          LBB0_638
	//0x0000266f LBB0_193
	0x41, 0x0f, 0xb6, 0x03, //0x0000266f movzbl       (%r11), %eax
	0x3c, 0x5c, //0x00002673 cmpb         $92, %al
	0x0f, 0x84, 0xd5, 0xff, 0xff, 0xff, //0x00002675 je           LBB0_191
	0x3c, 0x22, //0x0000267b cmpb         $34, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x0000267d je           LBB0_464
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002683 movq         $-1, %rcx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000268a movl         $1, %eax
	0x49, 0x01, 0xc3, //0x0000268f addq         %rax, %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002692 movq         $-1, %rax
	0x49, 0x01, 0xce, //0x00002699 addq         %rcx, %r14
	0x0f, 0x8f, 0xcd, 0xff, 0xff, 0xff, //0x0000269c jg           LBB0_193
	0xe9, 0x4c, 0x10, 0x00, 0x00, //0x000026a2 jmp          LBB0_638
	//0x000026a7 LBB0_464
	0x4d, 0x29, 0xcb, //0x000026a7 subq         %r9, %r11
	0x49, 0x83, 0xc3, 0x01, //0x000026aa addq         $1, %r11
	0xe9, 0x91, 0xe4, 0xff, 0xff, //0x000026ae jmp          LBB0_134
	//0x000026b3 LBB0_465
	0x4d, 0x29, 0xcb, //0x000026b3 subq         %r9, %r11
	0x49, 0x01, 0xcb, //0x000026b6 addq         %rcx, %r11
	0xe9, 0x67, 0xe5, 0xff, 0xff, //0x000026b9 jmp          LBB0_153
	//0x000026be LBB0_466
	0x4c, 0x89, 0xf0, //0x000026be movq         %r14, %rax
	0x4c, 0x09, 0xf8, //0x000026c1 orq          %r15, %rax
	0x0f, 0x99, 0xc0, //0x000026c4 setns        %al
	0x0f, 0x88, 0x2f, 0x02, 0x00, 0x00, //0x000026c7 js           LBB0_473
	0x4d, 0x39, 0xfe, //0x000026cd cmpq         %r15, %r14
	0x0f, 0x8c, 0x26, 0x02, 0x00, 0x00, //0x000026d0 jl           LBB0_473
	0x49, 0xf7, 0xd6, //0x000026d6 notq         %r14
	0x4c, 0x89, 0xf0, //0x000026d9 movq         %r14, %rax
	0xe9, 0x06, 0xff, 0xff, 0xff, //0x000026dc jmp          LBB0_459
	//0x000026e1 LBB0_469
	0x48, 0x8b, 0x55, 0xc8, //0x000026e1 movq         $-56(%rbp), %rdx
	0x49, 0x01, 0xd1, //0x000026e5 addq         %rdx, %r9
	0x49, 0x29, 0xc9, //0x000026e8 subq         %rcx, %r9
	0x48, 0xf7, 0xd6, //0x000026eb notq         %rsi
	0x4c, 0x01, 0xce, //0x000026ee addq         %r9, %rsi
	0x49, 0x89, 0xf3, //0x000026f1 movq         %rsi, %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x000026f4 movq         $-64(%rbp), %r13
	0x4d, 0x85, 0xdb, //0x000026f8 testq        %r11, %r11
	0x0f, 0x89, 0x19, 0xeb, 0xff, 0xff, //0x000026fb jns          LBB0_242
	0xe9, 0xd6, 0x0f, 0x00, 0x00, //0x00002701 jmp          LBB0_621
	//0x00002706 LBB0_470
	0x0f, 0xbc, 0xc3, //0x00002706 bsfl         %ebx, %eax
	0xe9, 0x0e, 0x02, 0x00, 0x00, //0x00002709 jmp          LBB0_475
	//0x0000270e LBB0_471
	0x89, 0xd0, //0x0000270e movl         %edx, %eax
	0xe9, 0x07, 0x02, 0x00, 0x00, //0x00002710 jmp          LBB0_475
	//0x00002715 LBB0_161
	0x4d, 0x85, 0xdb, //0x00002715 testq        %r11, %r11
	0x0f, 0x85, 0x43, 0x0b, 0x00, 0x00, //0x00002718 jne          LBB0_566
	0x4f, 0x8d, 0x1c, 0x16, //0x0000271e leaq         (%r14,%r10), %r11
	0x49, 0x83, 0xc3, 0x01, //0x00002722 addq         $1, %r11
	0x49, 0xf7, 0xd6, //0x00002726 notq         %r14
	0x4d, 0x01, 0xe6, //0x00002729 addq         %r12, %r14
	//0x0000272c LBB0_163
	0x4d, 0x85, 0xf6, //0x0000272c testq        %r14, %r14
	0x0f, 0x8f, 0x24, 0x00, 0x00, 0x00, //0x0000272f jg           LBB0_229
	0xe9, 0xb9, 0x0f, 0x00, 0x00, //0x00002735 jmp          LBB0_638
	//0x0000273a LBB0_227
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000273a movq         $-2, %rcx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00002741 movl         $2, %eax
	0x49, 0x01, 0xc3, //0x00002746 addq         %rax, %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002749 movq         $-1, %rax
	0x49, 0x01, 0xce, //0x00002750 addq         %rcx, %r14
	0x0f, 0x8e, 0x9a, 0x0f, 0x00, 0x00, //0x00002753 jle          LBB0_638
	//0x00002759 LBB0_229
	0x41, 0x0f, 0xb6, 0x03, //0x00002759 movzbl       (%r11), %eax
	0x3c, 0x5c, //0x0000275d cmpb         $92, %al
	0x0f, 0x84, 0xd5, 0xff, 0xff, 0xff, //0x0000275f je           LBB0_227
	0x3c, 0x22, //0x00002765 cmpb         $34, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00002767 je           LBB0_472
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000276d movq         $-1, %rcx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00002774 movl         $1, %eax
	0x49, 0x01, 0xc3, //0x00002779 addq         %rax, %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000277c movq         $-1, %rax
	0x49, 0x01, 0xce, //0x00002783 addq         %rcx, %r14
	0x0f, 0x8f, 0xcd, 0xff, 0xff, 0xff, //0x00002786 jg           LBB0_229
	0xe9, 0x62, 0x0f, 0x00, 0x00, //0x0000278c jmp          LBB0_638
	//0x00002791 LBB0_472
	0x4d, 0x29, 0xcb, //0x00002791 subq         %r9, %r11
	0x49, 0x83, 0xc3, 0x01, //0x00002794 addq         $1, %r11
	0x4c, 0x89, 0xc2, //0x00002798 movq         %r8, %rdx
	0xe9, 0x20, 0xf0, 0xff, 0xff, //0x0000279b jmp          LBB0_331
	//0x000027a0 LBB0_49
	0x4d, 0x01, 0xcb, //0x000027a0 addq         %r9, %r11
	0x4c, 0x8b, 0x65, 0xb8, //0x000027a3 movq         $-72(%rbp), %r12
	0x49, 0x83, 0xfe, 0x20, //0x000027a7 cmpq         $32, %r14
	0x0f, 0x82, 0x83, 0x09, 0x00, 0x00, //0x000027ab jb           LBB0_549
	//0x000027b1 LBB0_50
	0xf3, 0x41, 0x0f, 0x6f, 0x03, //0x000027b1 movdqu       (%r11), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x4b, 0x10, //0x000027b6 movdqu       $16(%r11), %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x000027bc movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x000027c0 pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x000027c5 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd1, //0x000027c9 movdqa       %xmm1, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x000027cd pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x000027d2 pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x000027d6 pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x000027db pmovmskb     %xmm0, %edx
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x000027df pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xf1, //0x000027e4 pmovmskb     %xmm1, %esi
	0x48, 0xc1, 0xe0, 0x10, //0x000027e8 shlq         $16, %rax
	0x48, 0x09, 0xc1, //0x000027ec orq          %rax, %rcx
	0x48, 0xc1, 0xe6, 0x10, //0x000027ef shlq         $16, %rsi
	0x48, 0x09, 0xf2, //0x000027f3 orq          %rsi, %rdx
	0x0f, 0x85, 0xd4, 0x08, 0x00, 0x00, //0x000027f6 jne          LBB0_545
	0x4d, 0x85, 0xd2, //0x000027fc testq        %r10, %r10
	0x0f, 0x85, 0xe7, 0x08, 0x00, 0x00, //0x000027ff jne          LBB0_547
	0x45, 0x31, 0xd2, //0x00002805 xorl         %r10d, %r10d
	0x48, 0x85, 0xc9, //0x00002808 testq        %rcx, %rcx
	0x0f, 0x84, 0x1b, 0x09, 0x00, 0x00, //0x0000280b je           LBB0_548
	//0x00002811 LBB0_53
	0x48, 0x0f, 0xbc, 0xc1, //0x00002811 bsfq         %rcx, %rax
	0xe9, 0xd3, 0x00, 0x00, 0x00, //0x00002815 jmp          LBB0_183
	//0x0000281a LBB0_173
	0x4c, 0x8b, 0x4d, 0xb0, //0x0000281a movq         $-80(%rbp), %r9
	0x4d, 0x01, 0xcb, //0x0000281e addq         %r9, %r11
	0x49, 0x83, 0xfe, 0x20, //0x00002821 cmpq         $32, %r14
	0x0f, 0x82, 0xa9, 0x06, 0x00, 0x00, //0x00002825 jb           LBB0_525
	//0x0000282b LBB0_174
	0xf3, 0x41, 0x0f, 0x6f, 0x1b, //0x0000282b movdqu       (%r11), %xmm3
	0xf3, 0x41, 0x0f, 0x6f, 0x43, 0x10, //0x00002830 movdqu       $16(%r11), %xmm0
	0x66, 0x0f, 0x6f, 0xcb, //0x00002836 movdqa       %xmm3, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xcb, //0x0000283a pcmpeqb      %xmm11, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x0000283f pmovmskb     %xmm1, %ecx
	0x66, 0x0f, 0x6f, 0xc8, //0x00002843 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00002847 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0xda, 0xd2, //0x0000284b pminub       %xmm10, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00002850 pcmpeqb      %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x00002854 pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00002859 pmovmskb     %xmm0, %eax
	0x66, 0x0f, 0x6f, 0xc3, //0x0000285d movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00002861 pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00002866 pmovmskb     %xmm0, %edx
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x0000286a pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xf9, //0x0000286f pmovmskb     %xmm1, %edi
	0x66, 0x0f, 0xd7, 0xf2, //0x00002873 pmovmskb     %xmm2, %esi
	0x48, 0xc1, 0xe0, 0x10, //0x00002877 shlq         $16, %rax
	0x48, 0x09, 0xc1, //0x0000287b orq          %rax, %rcx
	0x48, 0xc1, 0xe7, 0x10, //0x0000287e shlq         $16, %rdi
	0x48, 0xc1, 0xe6, 0x10, //0x00002882 shlq         $16, %rsi
	0x48, 0x09, 0xfa, //0x00002886 orq          %rdi, %rdx
	0x0f, 0x85, 0x74, 0x09, 0x00, 0x00, //0x00002889 jne          LBB0_563
	0x48, 0x85, 0xdb, //0x0000288f testq        %rbx, %rbx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002892 movq         $-64(%rbp), %r13
	0x0f, 0x85, 0x83, 0x09, 0x00, 0x00, //0x00002896 jne          LBB0_565
	0x66, 0x41, 0x0f, 0x6f, 0xfc, //0x0000289c movdqa       %xmm12, %xmm7
	0x31, 0xdb, //0x000028a1 xorl         %ebx, %ebx
	//0x000028a3 LBB0_177
	0x66, 0x0f, 0x6f, 0xc3, //0x000028a3 movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc2, //0x000028a7 pminub       %xmm10, %xmm0
	0x66, 0x0f, 0x74, 0xc3, //0x000028ac pcmpeqb      %xmm3, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x000028b0 pmovmskb     %xmm0, %eax
	0x48, 0x09, 0xc6, //0x000028b4 orq          %rax, %rsi
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000028b7 movl         $64, %edx
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x000028bc movl         $64, %eax
	0x48, 0x85, 0xc9, //0x000028c1 testq        %rcx, %rcx
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x000028c4 je           LBB0_179
	0x48, 0x0f, 0xbc, 0xc1, //0x000028ca bsfq         %rcx, %rax
	//0x000028ce LBB0_179
	0x48, 0x85, 0xf6, //0x000028ce testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x000028d1 je           LBB0_181
	0x48, 0x0f, 0xbc, 0xd6, //0x000028d7 bsfq         %rsi, %rdx
	//0x000028db LBB0_181
	0x48, 0x85, 0xc9, //0x000028db testq        %rcx, %rcx
	0x0f, 0x84, 0xce, 0x01, 0x00, 0x00, //0x000028de je           LBB0_477
	0x48, 0x39, 0xc2, //0x000028e4 cmpq         %rax, %rdx
	0x0f, 0x82, 0x32, 0x10, 0x00, 0x00, //0x000028e7 jb           LBB0_644
	//0x000028ed LBB0_183
	0x4d, 0x29, 0xcb, //0x000028ed subq         %r9, %r11
	0x49, 0x01, 0xc3, //0x000028f0 addq         %rax, %r11
	0x49, 0x83, 0xc3, 0x01, //0x000028f3 addq         $1, %r11
	0xe9, 0xdd, 0x08, 0x00, 0x00, //0x000028f7 jmp          LBB0_562
	//0x000028fc LBB0_473
	0x49, 0x8d, 0x4f, 0xff, //0x000028fc leaq         $-1(%r15), %rcx
	0x49, 0x39, 0xce, //0x00002900 cmpq         %rcx, %r14
	0x49, 0xf7, 0xd7, //0x00002903 notq         %r15
	0x4c, 0x0f, 0x45, 0xfa, //0x00002906 cmovneq      %rdx, %r15
	0x84, 0xc0, //0x0000290a testb        %al, %al
	0x4c, 0x0f, 0x44, 0xfa, //0x0000290c cmoveq       %rdx, %r15
	0x4c, 0x89, 0xf8, //0x00002910 movq         %r15, %rax
	0xe9, 0xcf, 0xfc, 0xff, 0xff, //0x00002913 jmp          LBB0_459
	//0x00002918 LBB0_474
	0x41, 0x0f, 0xbc, 0xc7, //0x00002918 bsfl         %r15d, %eax
	//0x0000291c LBB0_475
	0x49, 0xf7, 0xd3, //0x0000291c notq         %r11
	0x49, 0x29, 0xc3, //0x0000291f subq         %rax, %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002922 movq         $-64(%rbp), %r13
	0x48, 0x8b, 0x55, 0xc8, //0x00002926 movq         $-56(%rbp), %rdx
	0x4d, 0x85, 0xdb, //0x0000292a testq        %r11, %r11
	0x0f, 0x89, 0xe7, 0xe8, 0xff, 0xff, //0x0000292d jns          LBB0_242
	0xe9, 0xa4, 0x0d, 0x00, 0x00, //0x00002933 jmp          LBB0_621
	//0x00002938 LBB0_476
	0x4c, 0x89, 0xe2, //0x00002938 movq         %r12, %rdx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000293b movq         $-1, %rax
	0x4d, 0x85, 0xf6, //0x00002942 testq        %r14, %r14
	0x0f, 0x85, 0x40, 0xfc, 0xff, 0xff, //0x00002945 jne          LBB0_450
	0xe9, 0x3f, 0x0f, 0x00, 0x00, //0x0000294b jmp          LBB0_636
	//0x00002950 LBB0_118
	0x4d, 0x01, 0xcb, //0x00002950 addq         %r9, %r11
	0x49, 0x83, 0xfe, 0x20, //0x00002953 cmpq         $32, %r14
	0x4c, 0x8b, 0x65, 0xb8, //0x00002957 movq         $-72(%rbp), %r12
	0x0f, 0x82, 0x93, 0x09, 0x00, 0x00, //0x0000295b jb           LBB0_572
	//0x00002961 LBB0_119
	0xf3, 0x41, 0x0f, 0x6f, 0x03, //0x00002961 movdqu       (%r11), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x4b, 0x10, //0x00002966 movdqu       $16(%r11), %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x0000296c movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x00002970 pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00002975 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd1, //0x00002979 movdqa       %xmm1, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x0000297d pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00002982 pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00002986 pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x0000298b pmovmskb     %xmm0, %edx
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x0000298f pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xf1, //0x00002994 pmovmskb     %xmm1, %esi
	0x48, 0xc1, 0xe0, 0x10, //0x00002998 shlq         $16, %rax
	0x48, 0x09, 0xc1, //0x0000299c orq          %rax, %rcx
	0x48, 0xc1, 0xe6, 0x10, //0x0000299f shlq         $16, %rsi
	0x48, 0x09, 0xf2, //0x000029a3 orq          %rsi, %rdx
	0x0f, 0x85, 0xe4, 0x08, 0x00, 0x00, //0x000029a6 jne          LBB0_568
	0x4d, 0x85, 0xd2, //0x000029ac testq        %r10, %r10
	0x0f, 0x85, 0xf7, 0x08, 0x00, 0x00, //0x000029af jne          LBB0_570
	0x45, 0x31, 0xd2, //0x000029b5 xorl         %r10d, %r10d
	0x48, 0x85, 0xc9, //0x000029b8 testq        %rcx, %rcx
	0x0f, 0x84, 0x2b, 0x09, 0x00, 0x00, //0x000029bb je           LBB0_571
	//0x000029c1 LBB0_122
	0x48, 0x0f, 0xbc, 0xc1, //0x000029c1 bsfq         %rcx, %rax
	0x4d, 0x29, 0xcb, //0x000029c5 subq         %r9, %r11
	0x49, 0x01, 0xc3, //0x000029c8 addq         %rax, %r11
	0x49, 0x83, 0xc3, 0x01, //0x000029cb addq         $1, %r11
	0xe9, 0xc5, 0x09, 0x00, 0x00, //0x000029cf jmp          LBB0_585
	//0x000029d4 LBB0_208
	0x4c, 0x8b, 0x45, 0xb0, //0x000029d4 movq         $-80(%rbp), %r8
	0x4d, 0x01, 0xc3, //0x000029d8 addq         %r8, %r11
	0x49, 0x83, 0xfe, 0x20, //0x000029db cmpq         $32, %r14
	0x0f, 0x82, 0x9f, 0x05, 0x00, 0x00, //0x000029df jb           LBB0_531
	//0x000029e5 LBB0_209
	0xf3, 0x41, 0x0f, 0x6f, 0x1b, //0x000029e5 movdqu       (%r11), %xmm3
	0xf3, 0x41, 0x0f, 0x6f, 0x43, 0x10, //0x000029ea movdqu       $16(%r11), %xmm0
	0x66, 0x0f, 0x6f, 0xcb, //0x000029f0 movdqa       %xmm3, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xcb, //0x000029f4 pcmpeqb      %xmm11, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x000029f9 pmovmskb     %xmm1, %ecx
	0x66, 0x0f, 0x6f, 0xc8, //0x000029fd movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00002a01 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0xda, 0xd2, //0x00002a05 pminub       %xmm10, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00002a0a pcmpeqb      %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x00002a0e pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00002a13 pmovmskb     %xmm0, %eax
	0x66, 0x0f, 0x6f, 0xc3, //0x00002a17 movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00002a1b pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00002a20 pmovmskb     %xmm0, %edx
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x00002a24 pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xf9, //0x00002a29 pmovmskb     %xmm1, %edi
	0x66, 0x0f, 0xd7, 0xf2, //0x00002a2d pmovmskb     %xmm2, %esi
	0x48, 0xc1, 0xe0, 0x10, //0x00002a31 shlq         $16, %rax
	0x48, 0x09, 0xc1, //0x00002a35 orq          %rax, %rcx
	0x48, 0xc1, 0xe7, 0x10, //0x00002a38 shlq         $16, %rdi
	0x48, 0xc1, 0xe6, 0x10, //0x00002a3c shlq         $16, %rsi
	0x48, 0x09, 0xfa, //0x00002a40 orq          %rdi, %rdx
	0x0f, 0x85, 0x7a, 0x09, 0x00, 0x00, //0x00002a43 jne          LBB0_586
	0x4d, 0x85, 0xc9, //0x00002a49 testq        %r9, %r9
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002a4c movq         $-64(%rbp), %r13
	0x0f, 0x85, 0x8a, 0x09, 0x00, 0x00, //0x00002a50 jne          LBB0_588
	0x45, 0x31, 0xc9, //0x00002a56 xorl         %r9d, %r9d
	//0x00002a59 LBB0_212
	0x66, 0x0f, 0x6f, 0xc3, //0x00002a59 movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc2, //0x00002a5d pminub       %xmm10, %xmm0
	0x66, 0x0f, 0x74, 0xc3, //0x00002a62 pcmpeqb      %xmm3, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00002a66 pmovmskb     %xmm0, %eax
	0x48, 0x09, 0xc6, //0x00002a6a orq          %rax, %rsi
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00002a6d movl         $64, %edx
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00002a72 movl         $64, %eax
	0x48, 0x85, 0xc9, //0x00002a77 testq        %rcx, %rcx
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00002a7a je           LBB0_214
	0x48, 0x0f, 0xbc, 0xc1, //0x00002a80 bsfq         %rcx, %rax
	//0x00002a84 LBB0_214
	0x48, 0x85, 0xf6, //0x00002a84 testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00002a87 je           LBB0_216
	0x48, 0x0f, 0xbc, 0xd6, //0x00002a8d bsfq         %rsi, %rdx
	//0x00002a91 LBB0_216
	0x48, 0x85, 0xc9, //0x00002a91 testq        %rcx, %rcx
	0x0f, 0x84, 0xcc, 0x00, 0x00, 0x00, //0x00002a94 je           LBB0_489
	0x48, 0x39, 0xc2, //0x00002a9a cmpq         %rax, %rdx
	0x0f, 0x82, 0x87, 0x0e, 0x00, 0x00, //0x00002a9d jb           LBB0_645
	0x4d, 0x29, 0xc3, //0x00002aa3 subq         %r8, %r11
	0x49, 0x01, 0xc3, //0x00002aa6 addq         %rax, %r11
	0x49, 0x83, 0xc3, 0x01, //0x00002aa9 addq         $1, %r11
	0xe9, 0xe7, 0x08, 0x00, 0x00, //0x00002aad jmp          LBB0_585
	//0x00002ab2 LBB0_477
	0x48, 0x85, 0xf6, //0x00002ab2 testq        %rsi, %rsi
	0x0f, 0x85, 0x64, 0x0e, 0x00, 0x00, //0x00002ab5 jne          LBB0_644
	0x49, 0x83, 0xc3, 0x20, //0x00002abb addq         $32, %r11
	0x49, 0x83, 0xc6, 0xe0, //0x00002abf addq         $-32, %r14
	0x48, 0x85, 0xdb, //0x00002ac3 testq        %rbx, %rbx
	0x0f, 0x85, 0x1a, 0x04, 0x00, 0x00, //0x00002ac6 jne          LBB0_526
	//0x00002acc LBB0_479
	0x48, 0x8b, 0x4d, 0xd0, //0x00002acc movq         $-48(%rbp), %rcx
	0x4d, 0x85, 0xf6, //0x00002ad0 testq        %r14, %r14
	0x0f, 0x84, 0xe7, 0x0b, 0x00, 0x00, //0x00002ad3 je           LBB0_59
	//0x00002ad9 LBB0_480
	0x41, 0x0f, 0xb6, 0x03, //0x00002ad9 movzbl       (%r11), %eax
	0x3c, 0x22, //0x00002add cmpb         $34, %al
	0x0f, 0x84, 0x75, 0x00, 0x00, 0x00, //0x00002adf je           LBB0_488
	0x3c, 0x5c, //0x00002ae5 cmpb         $92, %al
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00002ae7 je           LBB0_484
	0x3c, 0x1f, //0x00002aed cmpb         $31, %al
	0x0f, 0x86, 0x53, 0x0e, 0x00, 0x00, //0x00002aef jbe          LBB0_647
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002af5 movq         $-1, %rax
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00002afc movl         $1, %edx
	0x49, 0x01, 0xd3, //0x00002b01 addq         %rdx, %r11
	0x49, 0x01, 0xc6, //0x00002b04 addq         %rax, %r14
	0x0f, 0x85, 0xcc, 0xff, 0xff, 0xff, //0x00002b07 jne          LBB0_480
	0xe9, 0xae, 0x0b, 0x00, 0x00, //0x00002b0d jmp          LBB0_59
	//0x00002b12 LBB0_484
	0x49, 0x83, 0xfe, 0x01, //0x00002b12 cmpq         $1, %r14
	0x0f, 0x84, 0x23, 0x0e, 0x00, 0x00, //0x00002b16 je           LBB0_652
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00002b1c movq         $-2, %rax
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00002b23 movl         $2, %edx
	0x48, 0x83, 0xf9, 0xff, //0x00002b28 cmpq         $-1, %rcx
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x00002b2c jne          LBB0_487
	0x4c, 0x89, 0xd9, //0x00002b32 movq         %r11, %rcx
	0x4c, 0x29, 0xc9, //0x00002b35 subq         %r9, %rcx
	0x48, 0x89, 0x4d, 0xd0, //0x00002b38 movq         %rcx, $-48(%rbp)
	//0x00002b3c LBB0_487
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002b3c movq         $-64(%rbp), %r13
	0x66, 0x41, 0x0f, 0x6f, 0xfc, //0x00002b40 movdqa       %xmm12, %xmm7
	0x4c, 0x8b, 0x65, 0xb8, //0x00002b45 movq         $-72(%rbp), %r12
	0x49, 0x01, 0xd3, //0x00002b49 addq         %rdx, %r11
	0x49, 0x01, 0xc6, //0x00002b4c addq         %rax, %r14
	0x0f, 0x85, 0x84, 0xff, 0xff, 0xff, //0x00002b4f jne          LBB0_480
	0xe9, 0x66, 0x0b, 0x00, 0x00, //0x00002b55 jmp          LBB0_59
	//0x00002b5a LBB0_488
	0x4d, 0x29, 0xcb, //0x00002b5a subq         %r9, %r11
	0x49, 0x83, 0xc3, 0x01, //0x00002b5d addq         $1, %r11
	0xe9, 0x73, 0x06, 0x00, 0x00, //0x00002b61 jmp          LBB0_562
	//0x00002b66 LBB0_489
	0x48, 0x85, 0xf6, //0x00002b66 testq        %rsi, %rsi
	0x0f, 0x85, 0xbb, 0x0d, 0x00, 0x00, //0x00002b69 jne          LBB0_645
	0x49, 0x83, 0xc3, 0x20, //0x00002b6f addq         $32, %r11
	0x49, 0x83, 0xc6, 0xe0, //0x00002b73 addq         $-32, %r14
	0x4d, 0x85, 0xc9, //0x00002b77 testq        %r9, %r9
	0x0f, 0x85, 0x11, 0x04, 0x00, 0x00, //0x00002b7a jne          LBB0_532
	//0x00002b80 LBB0_491
	0x48, 0x8b, 0x4d, 0xd0, //0x00002b80 movq         $-48(%rbp), %rcx
	0x4d, 0x85, 0xf6, //0x00002b84 testq        %r14, %r14
	0x0f, 0x84, 0x89, 0x0b, 0x00, 0x00, //0x00002b87 je           LBB0_128
	//0x00002b8d LBB0_492
	0x41, 0x0f, 0xb6, 0x03, //0x00002b8d movzbl       (%r11), %eax
	0x3c, 0x22, //0x00002b91 cmpb         $34, %al
	0x0f, 0x84, 0x8c, 0x00, 0x00, 0x00, //0x00002b93 je           LBB0_503
	0x3c, 0x5c, //0x00002b99 cmpb         $92, %al
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00002b9b je           LBB0_497
	0x3c, 0x1f, //0x00002ba1 cmpb         $31, %al
	0x0f, 0x86, 0xbb, 0x0d, 0x00, 0x00, //0x00002ba3 jbe          LBB0_649
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002ba9 movq         $-1, %rax
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00002bb0 movl         $1, %edx
	//0x00002bb5 LBB0_496
	0x49, 0x01, 0xd3, //0x00002bb5 addq         %rdx, %r11
	0x49, 0x01, 0xc6, //0x00002bb8 addq         %rax, %r14
	0x0f, 0x85, 0xcc, 0xff, 0xff, 0xff, //0x00002bbb jne          LBB0_492
	0xe9, 0x50, 0x0b, 0x00, 0x00, //0x00002bc1 jmp          LBB0_128
	//0x00002bc6 LBB0_497
	0x49, 0x83, 0xfe, 0x01, //0x00002bc6 cmpq         $1, %r14
	0x0f, 0x84, 0x8b, 0x0d, 0x00, 0x00, //0x00002bca je           LBB0_653
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00002bd0 movq         $-2, %rax
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00002bd7 movl         $2, %edx
	0x48, 0x83, 0xf9, 0xff, //0x00002bdc cmpq         $-1, %rcx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00002be0 je           LBB0_500
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002be6 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x00002bea movq         $-72(%rbp), %r12
	0x4c, 0x8b, 0x45, 0xb0, //0x00002bee movq         $-80(%rbp), %r8
	0xe9, 0xbe, 0xff, 0xff, 0xff, //0x00002bf2 jmp          LBB0_496
	//0x00002bf7 LBB0_500
	0x4c, 0x89, 0xd9, //0x00002bf7 movq         %r11, %rcx
	0x4c, 0x8b, 0x45, 0xb0, //0x00002bfa movq         $-80(%rbp), %r8
	0x4c, 0x29, 0xc1, //0x00002bfe subq         %r8, %rcx
	0x48, 0x89, 0x4d, 0xd0, //0x00002c01 movq         %rcx, $-48(%rbp)
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002c05 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x00002c09 movq         $-72(%rbp), %r12
	0xe9, 0xa3, 0xff, 0xff, 0xff, //0x00002c0d jmp          LBB0_496
	//0x00002c12 LBB0_501
	0x4d, 0x29, 0xcb, //0x00002c12 subq         %r9, %r11
	0x49, 0x01, 0xcb, //0x00002c15 addq         %rcx, %r11
	0xe9, 0x87, 0xd5, 0xff, 0xff, //0x00002c18 jmp          LBB0_2
	//0x00002c1d LBB0_502
	0x0f, 0xbc, 0xcb, //0x00002c1d bsfl         %ebx, %ecx
	0xe9, 0x30, 0x00, 0x00, 0x00, //0x00002c20 jmp          LBB0_507
	//0x00002c25 LBB0_503
	0x4d, 0x29, 0xc3, //0x00002c25 subq         %r8, %r11
	0x49, 0x83, 0xc3, 0x01, //0x00002c28 addq         $1, %r11
	0xe9, 0x68, 0x07, 0x00, 0x00, //0x00002c2c jmp          LBB0_585
	//0x00002c31 LBB0_504
	0x4d, 0x29, 0xcb, //0x00002c31 subq         %r9, %r11
	0x49, 0x83, 0xc3, 0x01, //0x00002c34 addq         $1, %r11
	0x48, 0x8b, 0x55, 0xc8, //0x00002c38 movq         $-56(%rbp), %rdx
	0xe9, 0x16, 0xe6, 0xff, 0xff, //0x00002c3c jmp          LBB0_246
	//0x00002c41 LBB0_505
	0x4c, 0x03, 0x4d, 0xc8, //0x00002c41 addq         $-56(%rbp), %r9
	0x49, 0x29, 0xc9, //0x00002c45 subq         %rcx, %r9
	0x49, 0x29, 0xc1, //0x00002c48 subq         %rax, %r9
	0x4c, 0x89, 0xc8, //0x00002c4b movq         %r9, %rax
	0xe9, 0x94, 0xf9, 0xff, 0xff, //0x00002c4e jmp          LBB0_459
	//0x00002c53 LBB0_506
	0x89, 0xd1, //0x00002c53 movl         %edx, %ecx
	//0x00002c55 LBB0_507
	0x48, 0xf7, 0xd0, //0x00002c55 notq         %rax
	0x48, 0x29, 0xc8, //0x00002c58 subq         %rcx, %rax
	0xe9, 0x87, 0xf9, 0xff, 0xff, //0x00002c5b jmp          LBB0_459
	//0x00002c60 LBB0_260
	0x4d, 0x01, 0xcb, //0x00002c60 addq         %r9, %r11
	0x49, 0x83, 0xfe, 0x20, //0x00002c63 cmpq         $32, %r14
	0x4c, 0x8b, 0x45, 0xb8, //0x00002c67 movq         $-72(%rbp), %r8
	0x0f, 0x82, 0xd0, 0x08, 0x00, 0x00, //0x00002c6b jb           LBB0_599
	//0x00002c71 LBB0_261
	0xf3, 0x41, 0x0f, 0x6f, 0x03, //0x00002c71 movdqu       (%r11), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x4b, 0x10, //0x00002c76 movdqu       $16(%r11), %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00002c7c movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x00002c80 pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00002c85 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd1, //0x00002c89 movdqa       %xmm1, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd3, //0x00002c8d pcmpeqb      %xmm11, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00002c92 pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00002c96 pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00002c9b pmovmskb     %xmm0, %edx
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x00002c9f pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xf1, //0x00002ca4 pmovmskb     %xmm1, %esi
	0x48, 0xc1, 0xe0, 0x10, //0x00002ca8 shlq         $16, %rax
	0x48, 0x09, 0xc1, //0x00002cac orq          %rax, %rcx
	0x48, 0xc1, 0xe6, 0x10, //0x00002caf shlq         $16, %rsi
	0x48, 0x09, 0xf2, //0x00002cb3 orq          %rsi, %rdx
	0x0f, 0x85, 0x21, 0x08, 0x00, 0x00, //0x00002cb6 jne          LBB0_595
	0x4d, 0x85, 0xd2, //0x00002cbc testq        %r10, %r10
	0x0f, 0x85, 0x34, 0x08, 0x00, 0x00, //0x00002cbf jne          LBB0_597
	0x45, 0x31, 0xd2, //0x00002cc5 xorl         %r10d, %r10d
	0x48, 0x85, 0xc9, //0x00002cc8 testq        %rcx, %rcx
	0x0f, 0x84, 0x68, 0x08, 0x00, 0x00, //0x00002ccb je           LBB0_598
	//0x00002cd1 LBB0_264
	0x48, 0x0f, 0xbc, 0xc1, //0x00002cd1 bsfq         %rcx, %rax
	0xe9, 0xd0, 0x00, 0x00, 0x00, //0x00002cd5 jmp          LBB0_433
	//0x00002cda LBB0_423
	0x4d, 0x01, 0xcb, //0x00002cda addq         %r9, %r11
	0x48, 0x83, 0xf9, 0x20, //0x00002cdd cmpq         $32, %rcx
	0x0f, 0x82, 0x6c, 0x03, 0x00, 0x00, //0x00002ce1 jb           LBB0_539
	//0x00002ce7 LBB0_424
	0xf3, 0x41, 0x0f, 0x6f, 0x1b, //0x00002ce7 movdqu       (%r11), %xmm3
	0xf3, 0x41, 0x0f, 0x6f, 0x43, 0x10, //0x00002cec movdqu       $16(%r11), %xmm0
	0x66, 0x0f, 0x6f, 0xcb, //0x00002cf2 movdqa       %xmm3, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xcb, //0x00002cf6 pcmpeqb      %xmm11, %xmm1
	0x66, 0x44, 0x0f, 0xd7, 0xd1, //0x00002cfb pmovmskb     %xmm1, %r10d
	0x66, 0x0f, 0x6f, 0xc8, //0x00002d00 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00002d04 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0xda, 0xd2, //0x00002d08 pminub       %xmm10, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00002d0d pcmpeqb      %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xc3, //0x00002d11 pcmpeqb      %xmm11, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00002d16 pmovmskb     %xmm0, %eax
	0x66, 0x0f, 0x6f, 0xc3, //0x00002d1a movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00002d1e pcmpeqb      %xmm13, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00002d23 pmovmskb     %xmm0, %edx
	0x66, 0x41, 0x0f, 0x74, 0xcd, //0x00002d27 pcmpeqb      %xmm13, %xmm1
	0x66, 0x0f, 0xd7, 0xf9, //0x00002d2c pmovmskb     %xmm1, %edi
	0x66, 0x0f, 0xd7, 0xf2, //0x00002d30 pmovmskb     %xmm2, %esi
	0x48, 0xc1, 0xe0, 0x10, //0x00002d34 shlq         $16, %rax
	0x49, 0x09, 0xc2, //0x00002d38 orq          %rax, %r10
	0x48, 0xc1, 0xe7, 0x10, //0x00002d3b shlq         $16, %rdi
	0x48, 0xc1, 0xe6, 0x10, //0x00002d3f shlq         $16, %rsi
	0x48, 0x09, 0xfa, //0x00002d43 orq          %rdi, %rdx
	0x0f, 0x85, 0xab, 0x08, 0x00, 0x00, //0x00002d46 jne          LBB0_612
	0x4d, 0x85, 0xf6, //0x00002d4c testq        %r14, %r14
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002d4f movq         $-64(%rbp), %r13
	0x0f, 0x85, 0xba, 0x08, 0x00, 0x00, //0x00002d53 jne          LBB0_614
	0x66, 0x41, 0x0f, 0x6f, 0xfc, //0x00002d59 movdqa       %xmm12, %xmm7
	0x31, 0xff, //0x00002d5e xorl         %edi, %edi
	//0x00002d60 LBB0_427
	0x66, 0x0f, 0x6f, 0xc3, //0x00002d60 movdqa       %xmm3, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc2, //0x00002d64 pminub       %xmm10, %xmm0
	0x66, 0x0f, 0x74, 0xc3, //0x00002d69 pcmpeqb      %xmm3, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00002d6d pmovmskb     %xmm0, %eax
	0x48, 0x09, 0xc6, //0x00002d71 orq          %rax, %rsi
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00002d74 movl         $64, %edx
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00002d79 movl         $64, %eax
	0x4d, 0x85, 0xd2, //0x00002d7e testq        %r10, %r10
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00002d81 je           LBB0_429
	0x49, 0x0f, 0xbc, 0xc2, //0x00002d87 bsfq         %r10, %rax
	//0x00002d8b LBB0_429
	0x48, 0x85, 0xf6, //0x00002d8b testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00002d8e je           LBB0_431
	0x48, 0x0f, 0xbc, 0xd6, //0x00002d94 bsfq         %rsi, %rdx
	//0x00002d98 LBB0_431
	0x4d, 0x85, 0xd2, //0x00002d98 testq        %r10, %r10
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x00002d9b je           LBB0_511
	0x48, 0x39, 0xc2, //0x00002da1 cmpq         %rax, %rdx
	0x0f, 0x82, 0xcc, 0x0b, 0x00, 0x00, //0x00002da4 jb           LBB0_650
	//0x00002daa LBB0_433
	0x4d, 0x29, 0xcb, //0x00002daa subq         %r9, %r11
	0x49, 0x01, 0xc3, //0x00002dad addq         %rax, %r11
	0x49, 0x83, 0xc3, 0x01, //0x00002db0 addq         $1, %r11
	0x48, 0x8b, 0x55, 0xc8, //0x00002db4 movq         $-56(%rbp), %rdx
	0x4d, 0x85, 0xdb, //0x00002db8 testq        %r11, %r11
	0x0f, 0x89, 0x96, 0xe4, 0xff, 0xff, //0x00002dbb jns          LBB0_246
	0xe9, 0xdc, 0x0a, 0x00, 0x00, //0x00002dc1 jmp          LBB0_434
	//0x00002dc6 LBB0_508
	0x4d, 0x01, 0xcb, //0x00002dc6 addq         %r9, %r11
	0xe9, 0x73, 0xf8, 0xff, 0xff, //0x00002dc9 jmp          LBB0_139
	//0x00002dce LBB0_509
	0x4d, 0x01, 0xcb, //0x00002dce addq         %r9, %r11
	0x48, 0x85, 0xc0, //0x00002dd1 testq        %rax, %rax
	0x0f, 0x85, 0xfe, 0xdd, 0xff, 0xff, //0x00002dd4 jne          LBB0_145
	0xe9, 0x2f, 0xde, 0xff, 0xff, //0x00002dda jmp          LBB0_151
	//0x00002ddf LBB0_510
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00002ddf movq         $-1, %r12
	0x4c, 0x89, 0xc1, //0x00002de6 movq         %r8, %rcx
	0x48, 0x89, 0xf0, //0x00002de9 movq         %rsi, %rax
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002dec movq         $-1, %r10
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00002df3 movq         $-1, %r13
	0x48, 0x8d, 0x3d, 0x07, 0x0e, 0x00, 0x00, //0x00002dfa leaq         $3591(%rip), %rdi  /* LJTI0_3+0(%rip) */
	0xe9, 0xe2, 0xd9, 0xff, 0xff, //0x00002e01 jmp          LBB0_93
	//0x00002e06 LBB0_511
	0x48, 0x85, 0xf6, //0x00002e06 testq        %rsi, %rsi
	0x0f, 0x85, 0x67, 0x0b, 0x00, 0x00, //0x00002e09 jne          LBB0_650
	0x49, 0x83, 0xc3, 0x20, //0x00002e0f addq         $32, %r11
	0x48, 0x83, 0xc1, 0xe0, //0x00002e13 addq         $-32, %rcx
	0x48, 0x89, 0xce, //0x00002e17 movq         %rcx, %rsi
	0x48, 0x85, 0xff, //0x00002e1a testq        %rdi, %rdi
	0x0f, 0x85, 0x3f, 0x02, 0x00, 0x00, //0x00002e1d jne          LBB0_540
	//0x00002e23 LBB0_513
	0x48, 0x8b, 0x4d, 0xd0, //0x00002e23 movq         $-48(%rbp), %rcx
	0x48, 0x85, 0xf6, //0x00002e27 testq        %rsi, %rsi
	0x0f, 0x84, 0xd2, 0x0a, 0x00, 0x00, //0x00002e2a je           LBB0_542
	//0x00002e30 LBB0_514
	0x41, 0x0f, 0xb6, 0x03, //0x00002e30 movzbl       (%r11), %eax
	0x3c, 0x22, //0x00002e34 cmpb         $34, %al
	0x0f, 0x84, 0xf6, 0x00, 0x00, 0x00, //0x00002e36 je           LBB0_528
	0x3c, 0x5c, //0x00002e3c cmpb         $92, %al
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00002e3e je           LBB0_519
	0x3c, 0x1f, //0x00002e44 cmpb         $31, %al
	0x0f, 0x86, 0x35, 0x0b, 0x00, 0x00, //0x00002e46 jbe          LBB0_651
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002e4c movq         $-1, %rax
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00002e53 movl         $1, %edx
	//0x00002e58 LBB0_518
	0x49, 0x01, 0xd3, //0x00002e58 addq         %rdx, %r11
	0x48, 0x01, 0xc6, //0x00002e5b addq         %rax, %rsi
	0x0f, 0x85, 0xcc, 0xff, 0xff, 0xff, //0x00002e5e jne          LBB0_514
	0xe9, 0x99, 0x0a, 0x00, 0x00, //0x00002e64 jmp          LBB0_542
	//0x00002e69 LBB0_519
	0x48, 0x83, 0xfe, 0x01, //0x00002e69 cmpq         $1, %rsi
	0x0f, 0x84, 0x8f, 0x0a, 0x00, 0x00, //0x00002e6d je           LBB0_542
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00002e73 movq         $-2, %rax
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00002e7a movl         $2, %edx
	0x48, 0x83, 0xf9, 0xff, //0x00002e7f cmpq         $-1, %rcx
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00002e83 jne          LBB0_518
	0x4c, 0x89, 0xd9, //0x00002e89 movq         %r11, %rcx
	0x4c, 0x29, 0xc9, //0x00002e8c subq         %r9, %rcx
	0x48, 0x89, 0x4d, 0xd0, //0x00002e8f movq         %rcx, $-48(%rbp)
	0xe9, 0xc0, 0xff, 0xff, 0xff, //0x00002e93 jmp          LBB0_518
	//0x00002e98 LBB0_522
	0x4d, 0x01, 0xcb, //0x00002e98 addq         %r9, %r11
	0xe9, 0x8c, 0xf8, 0xff, 0xff, //0x00002e9b jmp          LBB0_163
	//0x00002ea0 LBB0_523
	0x4d, 0x01, 0xcb, //0x00002ea0 addq         %r9, %r11
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x00002ea3 movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xd2, //0x00002eab xorl         %r10d, %r10d
	0x49, 0x83, 0xfe, 0x20, //0x00002eae cmpq         $32, %r14
	0x0f, 0x83, 0xf9, 0xf8, 0xff, 0xff, //0x00002eb2 jae          LBB0_50
	0xe9, 0x77, 0x02, 0x00, 0x00, //0x00002eb8 jmp          LBB0_549
	//0x00002ebd LBB0_524
	0x4d, 0x01, 0xcb, //0x00002ebd addq         %r9, %r11
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x00002ec0 movq         $-1, $-48(%rbp)
	0x31, 0xdb, //0x00002ec8 xorl         %ebx, %ebx
	0x49, 0x83, 0xfe, 0x20, //0x00002eca cmpq         $32, %r14
	0x0f, 0x83, 0x57, 0xf9, 0xff, 0xff, //0x00002ece jae          LBB0_174
	//0x00002ed4 LBB0_525
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002ed4 movq         $-64(%rbp), %r13
	0x66, 0x41, 0x0f, 0x6f, 0xfc, //0x00002ed8 movdqa       %xmm12, %xmm7
	0x48, 0x85, 0xdb, //0x00002edd testq        %rbx, %rbx
	0x0f, 0x84, 0xe6, 0xfb, 0xff, 0xff, //0x00002ee0 je           LBB0_479
	//0x00002ee6 LBB0_526
	0x4d, 0x85, 0xf6, //0x00002ee6 testq        %r14, %r14
	0x0f, 0x84, 0x50, 0x0a, 0x00, 0x00, //0x00002ee9 je           LBB0_652
	0x4c, 0x89, 0xc9, //0x00002eef movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x00002ef2 notq         %rcx
	0x4c, 0x01, 0xd9, //0x00002ef5 addq         %r11, %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x00002ef8 movq         $-48(%rbp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x00002efc cmpq         $-1, %rdx
	0x48, 0x89, 0xd0, //0x00002f00 movq         %rdx, %rax
	0x48, 0x0f, 0x44, 0xc1, //0x00002f03 cmoveq       %rcx, %rax
	0x48, 0x0f, 0x45, 0xca, //0x00002f07 cmovneq      %rdx, %rcx
	0x49, 0x83, 0xc3, 0x01, //0x00002f0b addq         $1, %r11
	0x49, 0x83, 0xc6, 0xff, //0x00002f0f addq         $-1, %r14
	0x48, 0x89, 0x45, 0xd0, //0x00002f13 movq         %rax, $-48(%rbp)
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002f17 movq         $-64(%rbp), %r13
	0x66, 0x41, 0x0f, 0x6f, 0xfc, //0x00002f1b movdqa       %xmm12, %xmm7
	0x4c, 0x8b, 0x65, 0xb8, //0x00002f20 movq         $-72(%rbp), %r12
	0x4d, 0x85, 0xf6, //0x00002f24 testq        %r14, %r14
	0x0f, 0x85, 0xac, 0xfb, 0xff, 0xff, //0x00002f27 jne          LBB0_480
	0xe9, 0x8e, 0x07, 0x00, 0x00, //0x00002f2d jmp          LBB0_59
	//0x00002f32 LBB0_528
	0x4d, 0x29, 0xcb, //0x00002f32 subq         %r9, %r11
	0x49, 0x83, 0xc3, 0x01, //0x00002f35 addq         $1, %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002f39 movq         $-64(%rbp), %r13
	0x66, 0x41, 0x0f, 0x6f, 0xfc, //0x00002f3d movdqa       %xmm12, %xmm7
	0xe9, 0xfd, 0xe4, 0xff, 0xff, //0x00002f42 jmp          LBB0_269
	//0x00002f47 LBB0_529
	0x4d, 0x01, 0xcb, //0x00002f47 addq         %r9, %r11
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x00002f4a movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xd2, //0x00002f52 xorl         %r10d, %r10d
	0x49, 0x83, 0xfe, 0x20, //0x00002f55 cmpq         $32, %r14
	0x4c, 0x8b, 0x65, 0xb8, //0x00002f59 movq         $-72(%rbp), %r12
	0x0f, 0x83, 0xfe, 0xf9, 0xff, 0xff, //0x00002f5d jae          LBB0_119
	0xe9, 0x8c, 0x03, 0x00, 0x00, //0x00002f63 jmp          LBB0_572
	//0x00002f68 LBB0_530
	0x4c, 0x8b, 0x45, 0xb0, //0x00002f68 movq         $-80(%rbp), %r8
	0x4d, 0x01, 0xc3, //0x00002f6c addq         %r8, %r11
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x00002f6f movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xc9, //0x00002f77 xorl         %r9d, %r9d
	0x49, 0x83, 0xfe, 0x20, //0x00002f7a cmpq         $32, %r14
	0x0f, 0x83, 0x61, 0xfa, 0xff, 0xff, //0x00002f7e jae          LBB0_209
	//0x00002f84 LBB0_531
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002f84 movq         $-64(%rbp), %r13
	0x4d, 0x85, 0xc9, //0x00002f88 testq        %r9, %r9
	0x0f, 0x84, 0xef, 0xfb, 0xff, 0xff, //0x00002f8b je           LBB0_491
	//0x00002f91 LBB0_532
	0x4d, 0x85, 0xf6, //0x00002f91 testq        %r14, %r14
	0x0f, 0x84, 0xc1, 0x09, 0x00, 0x00, //0x00002f94 je           LBB0_653
	0x4c, 0x8b, 0x45, 0xb0, //0x00002f9a movq         $-80(%rbp), %r8
	0x4c, 0x89, 0xc1, //0x00002f9e movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x00002fa1 notq         %rcx
	0x4c, 0x01, 0xd9, //0x00002fa4 addq         %r11, %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x00002fa7 movq         $-48(%rbp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x00002fab cmpq         $-1, %rdx
	0x48, 0x89, 0xd0, //0x00002faf movq         %rdx, %rax
	0x48, 0x0f, 0x44, 0xc1, //0x00002fb2 cmoveq       %rcx, %rax
	0x48, 0x0f, 0x45, 0xca, //0x00002fb6 cmovneq      %rdx, %rcx
	0x49, 0x83, 0xc3, 0x01, //0x00002fba addq         $1, %r11
	0x49, 0x83, 0xc6, 0xff, //0x00002fbe addq         $-1, %r14
	0x48, 0x89, 0x45, 0xd0, //0x00002fc2 movq         %rax, $-48(%rbp)
	0x4c, 0x8b, 0x6d, 0xc0, //0x00002fc6 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x00002fca movq         $-72(%rbp), %r12
	0x4d, 0x85, 0xf6, //0x00002fce testq        %r14, %r14
	0x0f, 0x85, 0xb6, 0xfb, 0xff, 0xff, //0x00002fd1 jne          LBB0_492
	0xe9, 0x3a, 0x07, 0x00, 0x00, //0x00002fd7 jmp          LBB0_128
	//0x00002fdc LBB0_534
	0x4d, 0x01, 0xcb, //0x00002fdc addq         %r9, %r11
	0xe9, 0x19, 0xe9, 0xff, 0xff, //0x00002fdf jmp          LBB0_343
	//0x00002fe4 LBB0_535
	0x4d, 0x01, 0xcb, //0x00002fe4 addq         %r9, %r11
	0x48, 0x8b, 0x75, 0xc8, //0x00002fe7 movq         $-56(%rbp), %rsi
	0x48, 0x85, 0xc0, //0x00002feb testq        %rax, %rax
	0x0f, 0x85, 0xd4, 0xed, 0xff, 0xff, //0x00002fee jne          LBB0_376
	0xe9, 0x05, 0xee, 0xff, 0xff, //0x00002ff4 jmp          LBB0_382
	//0x00002ff9 LBB0_536
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002ff9 movq         $-1, %r14
	0x48, 0x8b, 0x4d, 0xb0, //0x00003000 movq         $-80(%rbp), %rcx
	0x4d, 0x89, 0xe2, //0x00003004 movq         %r12, %r10
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00003007 movq         $-1, %r15
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000300e movq         $-1, %r8
	0xe9, 0x34, 0xe6, 0xff, 0xff, //0x00003015 jmp          LBB0_298
	//0x0000301a LBB0_537
	0x4d, 0x01, 0xcb, //0x0000301a addq         %r9, %r11
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x0000301d movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xd2, //0x00003025 xorl         %r10d, %r10d
	0x49, 0x83, 0xfe, 0x20, //0x00003028 cmpq         $32, %r14
	0x4c, 0x8b, 0x45, 0xb8, //0x0000302c movq         $-72(%rbp), %r8
	0x0f, 0x83, 0x3b, 0xfc, 0xff, 0xff, //0x00003030 jae          LBB0_261
	0xe9, 0x06, 0x05, 0x00, 0x00, //0x00003036 jmp          LBB0_599
	//0x0000303b LBB0_538
	0x4d, 0x01, 0xcb, //0x0000303b addq         %r9, %r11
	0x48, 0xc7, 0x45, 0xd0, 0xff, 0xff, 0xff, 0xff, //0x0000303e movq         $-1, $-48(%rbp)
	0x45, 0x31, 0xf6, //0x00003046 xorl         %r14d, %r14d
	0x48, 0x83, 0xf9, 0x20, //0x00003049 cmpq         $32, %rcx
	0x0f, 0x83, 0x94, 0xfc, 0xff, 0xff, //0x0000304d jae          LBB0_424
	//0x00003053 LBB0_539
	0x48, 0x89, 0xce, //0x00003053 movq         %rcx, %rsi
	0x4c, 0x89, 0xf7, //0x00003056 movq         %r14, %rdi
	0x48, 0x85, 0xff, //0x00003059 testq        %rdi, %rdi
	0x0f, 0x84, 0xc1, 0xfd, 0xff, 0xff, //0x0000305c je           LBB0_513
	//0x00003062 LBB0_540
	0x48, 0x85, 0xf6, //0x00003062 testq        %rsi, %rsi
	0x0f, 0x84, 0x97, 0x08, 0x00, 0x00, //0x00003065 je           LBB0_542
	0x4c, 0x89, 0xc9, //0x0000306b movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x0000306e notq         %rcx
	0x4c, 0x01, 0xd9, //0x00003071 addq         %r11, %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x00003074 movq         $-48(%rbp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x00003078 cmpq         $-1, %rdx
	0x48, 0x89, 0xd0, //0x0000307c movq         %rdx, %rax
	0x48, 0x0f, 0x44, 0xc1, //0x0000307f cmoveq       %rcx, %rax
	0x48, 0x0f, 0x45, 0xca, //0x00003083 cmovneq      %rdx, %rcx
	0x49, 0x83, 0xc3, 0x01, //0x00003087 addq         $1, %r11
	0x48, 0x83, 0xc6, 0xff, //0x0000308b addq         $-1, %rsi
	0x48, 0x89, 0x45, 0xd0, //0x0000308f movq         %rax, $-48(%rbp)
	0x48, 0x85, 0xf6, //0x00003093 testq        %rsi, %rsi
	0x0f, 0x85, 0x94, 0xfd, 0xff, 0xff, //0x00003096 jne          LBB0_514
	0xe9, 0x61, 0x08, 0x00, 0x00, //0x0000309c jmp          LBB0_542
	//0x000030a1 LBB0_543
	0x49, 0x8d, 0x4c, 0x24, 0xff, //0x000030a1 leaq         $-1(%r12), %rcx
	0x4c, 0x39, 0xf1, //0x000030a6 cmpq         %r14, %rcx
	0x0f, 0x84, 0x44, 0x06, 0x00, 0x00, //0x000030a9 je           LBB0_638
	0x4c, 0x89, 0xe2, //0x000030af movq         %r12, %rdx
	0x4f, 0x8d, 0x1c, 0x16, //0x000030b2 leaq         (%r14,%r10), %r11
	0x49, 0x83, 0xc3, 0x02, //0x000030b6 addq         $2, %r11
	0x4c, 0x89, 0xe1, //0x000030ba movq         %r12, %rcx
	0x4c, 0x29, 0xf1, //0x000030bd subq         %r14, %rcx
	0x48, 0x83, 0xc1, 0xfe, //0x000030c0 addq         $-2, %rcx
	0x49, 0x89, 0xce, //0x000030c4 movq         %rcx, %r14
	0x4c, 0x8b, 0x6d, 0xc0, //0x000030c7 movq         $-64(%rbp), %r13
	0xe9, 0x71, 0xf5, 0xff, 0xff, //0x000030cb jmp          LBB0_139
	//0x000030d0 LBB0_545
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x000030d0 cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x000030d5 jne          LBB0_547
	0x4c, 0x89, 0xd8, //0x000030db movq         %r11, %rax
	0x4c, 0x29, 0xc8, //0x000030de subq         %r9, %rax
	0x48, 0x0f, 0xbc, 0xf2, //0x000030e1 bsfq         %rdx, %rsi
	0x48, 0x01, 0xc6, //0x000030e5 addq         %rax, %rsi
	0x48, 0x89, 0x75, 0xd0, //0x000030e8 movq         %rsi, $-48(%rbp)
	//0x000030ec LBB0_547
	0x44, 0x89, 0xd0, //0x000030ec movl         %r10d, %eax
	0xf7, 0xd0, //0x000030ef notl         %eax
	0x21, 0xd0, //0x000030f1 andl         %edx, %eax
	0x41, 0x8d, 0x34, 0x42, //0x000030f3 leal         (%r10,%rax,2), %esi
	0x8d, 0x3c, 0x00, //0x000030f7 leal         (%rax,%rax), %edi
	0xf7, 0xd7, //0x000030fa notl         %edi
	0x21, 0xd7, //0x000030fc andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000030fe andl         $-1431655766, %edi
	0x45, 0x31, 0xd2, //0x00003104 xorl         %r10d, %r10d
	0x01, 0xc7, //0x00003107 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc2, //0x00003109 setb         %r10b
	0x01, 0xff, //0x0000310d addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x0000310f xorl         $1431655765, %edi
	0x21, 0xf7, //0x00003115 andl         %esi, %edi
	0xf7, 0xd7, //0x00003117 notl         %edi
	0x21, 0xf9, //0x00003119 andl         %edi, %ecx
	0x4c, 0x8b, 0x6d, 0xc0, //0x0000311b movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x0000311f movq         $-72(%rbp), %r12
	0x48, 0x85, 0xc9, //0x00003123 testq        %rcx, %rcx
	0x0f, 0x85, 0xe5, 0xf6, 0xff, 0xff, //0x00003126 jne          LBB0_53
	//0x0000312c LBB0_548
	0x49, 0x83, 0xc3, 0x20, //0x0000312c addq         $32, %r11
	0x49, 0x83, 0xc6, 0xe0, //0x00003130 addq         $-32, %r14
	//0x00003134 LBB0_549
	0x4d, 0x85, 0xd2, //0x00003134 testq        %r10, %r10
	0x0f, 0x85, 0xe4, 0x02, 0x00, 0x00, //0x00003137 jne          LBB0_589
	0x48, 0x8b, 0x4d, 0xd0, //0x0000313d movq         $-48(%rbp), %rcx
	0x4d, 0x85, 0xf6, //0x00003141 testq        %r14, %r14
	0x0f, 0x84, 0x8c, 0x00, 0x00, 0x00, //0x00003144 je           LBB0_561
	//0x0000314a LBB0_551
	0x4c, 0x89, 0xca, //0x0000314a movq         %r9, %rdx
	0x48, 0xf7, 0xda, //0x0000314d negq         %rdx
	//0x00003150 LBB0_552
	0x31, 0xf6, //0x00003150 xorl         %esi, %esi
	//0x00003152 LBB0_553
	0x41, 0x0f, 0xb6, 0x04, 0x33, //0x00003152 movzbl       (%r11,%rsi), %eax
	0x3c, 0x22, //0x00003157 cmpb         $34, %al
	0x0f, 0x84, 0x70, 0x00, 0x00, 0x00, //0x00003159 je           LBB0_560
	0x3c, 0x5c, //0x0000315f cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00003161 je           LBB0_558
	0x48, 0x83, 0xc6, 0x01, //0x00003167 addq         $1, %rsi
	0x49, 0x39, 0xf6, //0x0000316b cmpq         %rsi, %r14
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x0000316e jne          LBB0_553
	0xe9, 0x72, 0x00, 0x00, 0x00, //0x00003174 jmp          LBB0_556
	//0x00003179 LBB0_558
	0x49, 0x8d, 0x46, 0xff, //0x00003179 leaq         $-1(%r14), %rax
	0x48, 0x39, 0xf0, //0x0000317d cmpq         %rsi, %rax
	0x0f, 0x84, 0xb9, 0x07, 0x00, 0x00, //0x00003180 je           LBB0_652
	0x4a, 0x8d, 0x04, 0x1a, //0x00003186 leaq         (%rdx,%r11), %rax
	0x48, 0x01, 0xf0, //0x0000318a addq         %rsi, %rax
	0x48, 0x83, 0xf9, 0xff, //0x0000318d cmpq         $-1, %rcx
	0x48, 0x8b, 0x7d, 0xd0, //0x00003191 movq         $-48(%rbp), %rdi
	0x48, 0x0f, 0x44, 0xf8, //0x00003195 cmoveq       %rax, %rdi
	0x48, 0x89, 0x7d, 0xd0, //0x00003199 movq         %rdi, $-48(%rbp)
	0x48, 0x0f, 0x44, 0xc8, //0x0000319d cmoveq       %rax, %rcx
	0x49, 0x01, 0xf3, //0x000031a1 addq         %rsi, %r11
	0x49, 0x83, 0xc3, 0x02, //0x000031a4 addq         $2, %r11
	0x4c, 0x89, 0xf0, //0x000031a8 movq         %r14, %rax
	0x48, 0x29, 0xf0, //0x000031ab subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000031ae addq         $-2, %rax
	0x49, 0x83, 0xc6, 0xfe, //0x000031b2 addq         $-2, %r14
	0x49, 0x39, 0xf6, //0x000031b6 cmpq         %rsi, %r14
	0x49, 0x89, 0xc6, //0x000031b9 movq         %rax, %r14
	0x4c, 0x8b, 0x6d, 0xc0, //0x000031bc movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x000031c0 movq         $-72(%rbp), %r12
	0x0f, 0x85, 0x86, 0xff, 0xff, 0xff, //0x000031c4 jne          LBB0_552
	0xe9, 0xf1, 0x04, 0x00, 0x00, //0x000031ca jmp          LBB0_59
	//0x000031cf LBB0_560
	0x49, 0x01, 0xf3, //0x000031cf addq         %rsi, %r11
	0x49, 0x83, 0xc3, 0x01, //0x000031d2 addq         $1, %r11
	//0x000031d6 LBB0_561
	0x4d, 0x29, 0xcb, //0x000031d6 subq         %r9, %r11
	//0x000031d9 LBB0_562
	0x48, 0x8b, 0x55, 0xc8, //0x000031d9 movq         $-56(%rbp), %rdx
	0x4d, 0x85, 0xdb, //0x000031dd testq        %r11, %r11
	0x0f, 0x89, 0x71, 0xe0, 0xff, 0xff, //0x000031e0 jns          LBB0_246
	0xe9, 0xc8, 0x04, 0x00, 0x00, //0x000031e6 jmp          LBB0_58
	//0x000031eb LBB0_556
	0x3c, 0x22, //0x000031eb cmpb         $34, %al
	0x0f, 0x85, 0x4c, 0x07, 0x00, 0x00, //0x000031ed jne          LBB0_652
	0x4d, 0x01, 0xf3, //0x000031f3 addq         %r14, %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x000031f6 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x000031fa movq         $-72(%rbp), %r12
	0xe9, 0xd3, 0xff, 0xff, 0xff, //0x000031fe jmp          LBB0_561
	//0x00003203 LBB0_563
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x00003203 cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00003208 jne          LBB0_565
	0x4c, 0x89, 0xd8, //0x0000320e movq         %r11, %rax
	0x4c, 0x29, 0xc8, //0x00003211 subq         %r9, %rax
	0x48, 0x0f, 0xbc, 0xfa, //0x00003214 bsfq         %rdx, %rdi
	0x48, 0x01, 0xc7, //0x00003218 addq         %rax, %rdi
	0x48, 0x89, 0x7d, 0xd0, //0x0000321b movq         %rdi, $-48(%rbp)
	//0x0000321f LBB0_565
	0x48, 0x89, 0xdf, //0x0000321f movq         %rbx, %rdi
	0x89, 0xf8, //0x00003222 movl         %edi, %eax
	0xf7, 0xd0, //0x00003224 notl         %eax
	0x21, 0xd0, //0x00003226 andl         %edx, %eax
	0x44, 0x8d, 0x04, 0x43, //0x00003228 leal         (%rbx,%rax,2), %r8d
	0x8d, 0x3c, 0x00, //0x0000322c leal         (%rax,%rax), %edi
	0xf7, 0xd7, //0x0000322f notl         %edi
	0x21, 0xd7, //0x00003231 andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003233 andl         $-1431655766, %edi
	0x31, 0xdb, //0x00003239 xorl         %ebx, %ebx
	0x01, 0xc7, //0x0000323b addl         %eax, %edi
	0x0f, 0x92, 0xc3, //0x0000323d setb         %bl
	0x01, 0xff, //0x00003240 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00003242 xorl         $1431655765, %edi
	0x44, 0x21, 0xc7, //0x00003248 andl         %r8d, %edi
	0xf7, 0xd7, //0x0000324b notl         %edi
	0x21, 0xf9, //0x0000324d andl         %edi, %ecx
	0x4c, 0x8b, 0x6d, 0xc0, //0x0000324f movq         $-64(%rbp), %r13
	0x66, 0x41, 0x0f, 0x6f, 0xfc, //0x00003253 movdqa       %xmm12, %xmm7
	0x4c, 0x8b, 0x65, 0xb8, //0x00003258 movq         $-72(%rbp), %r12
	0xe9, 0x42, 0xf6, 0xff, 0xff, //0x0000325c jmp          LBB0_177
	//0x00003261 LBB0_566
	0x49, 0x8d, 0x4c, 0x24, 0xff, //0x00003261 leaq         $-1(%r12), %rcx
	0x4c, 0x39, 0xf1, //0x00003266 cmpq         %r14, %rcx
	0x0f, 0x84, 0x84, 0x04, 0x00, 0x00, //0x00003269 je           LBB0_638
	0x4c, 0x89, 0xe2, //0x0000326f movq         %r12, %rdx
	0x4f, 0x8d, 0x1c, 0x16, //0x00003272 leaq         (%r14,%r10), %r11
	0x49, 0x83, 0xc3, 0x02, //0x00003276 addq         $2, %r11
	0x4c, 0x89, 0xe1, //0x0000327a movq         %r12, %rcx
	0x4c, 0x29, 0xf1, //0x0000327d subq         %r14, %rcx
	0x48, 0x83, 0xc1, 0xfe, //0x00003280 addq         $-2, %rcx
	0x49, 0x89, 0xce, //0x00003284 movq         %rcx, %r14
	0x4c, 0x8b, 0x6d, 0xc0, //0x00003287 movq         $-64(%rbp), %r13
	0xe9, 0x9c, 0xf4, 0xff, 0xff, //0x0000328b jmp          LBB0_163
	//0x00003290 LBB0_568
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x00003290 cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00003295 jne          LBB0_570
	0x4c, 0x89, 0xd8, //0x0000329b movq         %r11, %rax
	0x4c, 0x29, 0xc8, //0x0000329e subq         %r9, %rax
	0x48, 0x0f, 0xbc, 0xf2, //0x000032a1 bsfq         %rdx, %rsi
	0x48, 0x01, 0xc6, //0x000032a5 addq         %rax, %rsi
	0x48, 0x89, 0x75, 0xd0, //0x000032a8 movq         %rsi, $-48(%rbp)
	//0x000032ac LBB0_570
	0x44, 0x89, 0xd0, //0x000032ac movl         %r10d, %eax
	0xf7, 0xd0, //0x000032af notl         %eax
	0x21, 0xd0, //0x000032b1 andl         %edx, %eax
	0x41, 0x8d, 0x34, 0x42, //0x000032b3 leal         (%r10,%rax,2), %esi
	0x8d, 0x3c, 0x00, //0x000032b7 leal         (%rax,%rax), %edi
	0xf7, 0xd7, //0x000032ba notl         %edi
	0x21, 0xd7, //0x000032bc andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000032be andl         $-1431655766, %edi
	0x45, 0x31, 0xd2, //0x000032c4 xorl         %r10d, %r10d
	0x01, 0xc7, //0x000032c7 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc2, //0x000032c9 setb         %r10b
	0x01, 0xff, //0x000032cd addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000032cf xorl         $1431655765, %edi
	0x21, 0xf7, //0x000032d5 andl         %esi, %edi
	0xf7, 0xd7, //0x000032d7 notl         %edi
	0x21, 0xf9, //0x000032d9 andl         %edi, %ecx
	0x4c, 0x8b, 0x6d, 0xc0, //0x000032db movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x000032df movq         $-72(%rbp), %r12
	0x48, 0x85, 0xc9, //0x000032e3 testq        %rcx, %rcx
	0x0f, 0x85, 0xd5, 0xf6, 0xff, 0xff, //0x000032e6 jne          LBB0_122
	//0x000032ec LBB0_571
	0x49, 0x83, 0xc3, 0x20, //0x000032ec addq         $32, %r11
	0x49, 0x83, 0xc6, 0xe0, //0x000032f0 addq         $-32, %r14
	//0x000032f4 LBB0_572
	0x4d, 0x85, 0xd2, //0x000032f4 testq        %r10, %r10
	0x0f, 0x85, 0x6b, 0x01, 0x00, 0x00, //0x000032f7 jne          LBB0_591
	0x48, 0x8b, 0x4d, 0xd0, //0x000032fd movq         $-48(%rbp), %rcx
	0x4d, 0x85, 0xf6, //0x00003301 testq        %r14, %r14
	0x0f, 0x84, 0x8c, 0x00, 0x00, 0x00, //0x00003304 je           LBB0_584
	//0x0000330a LBB0_574
	0x4c, 0x89, 0xca, //0x0000330a movq         %r9, %rdx
	0x48, 0xf7, 0xda, //0x0000330d negq         %rdx
	//0x00003310 LBB0_575
	0x31, 0xf6, //0x00003310 xorl         %esi, %esi
	//0x00003312 LBB0_576
	0x41, 0x0f, 0xb6, 0x04, 0x33, //0x00003312 movzbl       (%r11,%rsi), %eax
	0x3c, 0x22, //0x00003317 cmpb         $34, %al
	0x0f, 0x84, 0x70, 0x00, 0x00, 0x00, //0x00003319 je           LBB0_583
	0x3c, 0x5c, //0x0000331f cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00003321 je           LBB0_581
	0x48, 0x83, 0xc6, 0x01, //0x00003327 addq         $1, %rsi
	0x49, 0x39, 0xf6, //0x0000332b cmpq         %rsi, %r14
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x0000332e jne          LBB0_576
	0xe9, 0x72, 0x00, 0x00, 0x00, //0x00003334 jmp          LBB0_579
	//0x00003339 LBB0_581
	0x49, 0x8d, 0x46, 0xff, //0x00003339 leaq         $-1(%r14), %rax
	0x48, 0x39, 0xf0, //0x0000333d cmpq         %rsi, %rax
	0x0f, 0x84, 0x15, 0x06, 0x00, 0x00, //0x00003340 je           LBB0_653
	0x4a, 0x8d, 0x04, 0x1a, //0x00003346 leaq         (%rdx,%r11), %rax
	0x48, 0x01, 0xf0, //0x0000334a addq         %rsi, %rax
	0x48, 0x83, 0xf9, 0xff, //0x0000334d cmpq         $-1, %rcx
	0x48, 0x8b, 0x7d, 0xd0, //0x00003351 movq         $-48(%rbp), %rdi
	0x48, 0x0f, 0x44, 0xf8, //0x00003355 cmoveq       %rax, %rdi
	0x48, 0x89, 0x7d, 0xd0, //0x00003359 movq         %rdi, $-48(%rbp)
	0x48, 0x0f, 0x44, 0xc8, //0x0000335d cmoveq       %rax, %rcx
	0x49, 0x01, 0xf3, //0x00003361 addq         %rsi, %r11
	0x49, 0x83, 0xc3, 0x02, //0x00003364 addq         $2, %r11
	0x4c, 0x89, 0xf0, //0x00003368 movq         %r14, %rax
	0x48, 0x29, 0xf0, //0x0000336b subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x0000336e addq         $-2, %rax
	0x49, 0x83, 0xc6, 0xfe, //0x00003372 addq         $-2, %r14
	0x49, 0x39, 0xf6, //0x00003376 cmpq         %rsi, %r14
	0x49, 0x89, 0xc6, //0x00003379 movq         %rax, %r14
	0x4c, 0x8b, 0x6d, 0xc0, //0x0000337c movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x00003380 movq         $-72(%rbp), %r12
	0x0f, 0x85, 0x86, 0xff, 0xff, 0xff, //0x00003384 jne          LBB0_575
	0xe9, 0x87, 0x03, 0x00, 0x00, //0x0000338a jmp          LBB0_128
	//0x0000338f LBB0_583
	0x49, 0x01, 0xf3, //0x0000338f addq         %rsi, %r11
	0x49, 0x83, 0xc3, 0x01, //0x00003392 addq         $1, %r11
	//0x00003396 LBB0_584
	0x4d, 0x29, 0xcb, //0x00003396 subq         %r9, %r11
	//0x00003399 LBB0_585
	0x48, 0x8b, 0x55, 0xc8, //0x00003399 movq         $-56(%rbp), %rdx
	0x4d, 0x85, 0xdb, //0x0000339d testq        %r11, %r11
	0x0f, 0x89, 0x1a, 0xe4, 0xff, 0xff, //0x000033a0 jns          LBB0_331
	0xe9, 0x5a, 0x03, 0x00, 0x00, //0x000033a6 jmp          LBB0_127
	//0x000033ab LBB0_579
	0x3c, 0x22, //0x000033ab cmpb         $34, %al
	0x0f, 0x85, 0xa8, 0x05, 0x00, 0x00, //0x000033ad jne          LBB0_653
	0x4d, 0x01, 0xf3, //0x000033b3 addq         %r14, %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x000033b6 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x000033ba movq         $-72(%rbp), %r12
	0xe9, 0xd3, 0xff, 0xff, 0xff, //0x000033be jmp          LBB0_584
	//0x000033c3 LBB0_586
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x000033c3 cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x000033c8 jne          LBB0_588
	0x4c, 0x89, 0xd8, //0x000033ce movq         %r11, %rax
	0x48, 0x2b, 0x45, 0xb0, //0x000033d1 subq         $-80(%rbp), %rax
	0x48, 0x0f, 0xbc, 0xfa, //0x000033d5 bsfq         %rdx, %rdi
	0x48, 0x01, 0xc7, //0x000033d9 addq         %rax, %rdi
	0x48, 0x89, 0x7d, 0xd0, //0x000033dc movq         %rdi, $-48(%rbp)
	//0x000033e0 LBB0_588
	0x44, 0x89, 0xc8, //0x000033e0 movl         %r9d, %eax
	0xf7, 0xd0, //0x000033e3 notl         %eax
	0x21, 0xd0, //0x000033e5 andl         %edx, %eax
	0x45, 0x8d, 0x04, 0x41, //0x000033e7 leal         (%r9,%rax,2), %r8d
	0x8d, 0x3c, 0x00, //0x000033eb leal         (%rax,%rax), %edi
	0xf7, 0xd7, //0x000033ee notl         %edi
	0x21, 0xd7, //0x000033f0 andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000033f2 andl         $-1431655766, %edi
	0x45, 0x31, 0xc9, //0x000033f8 xorl         %r9d, %r9d
	0x01, 0xc7, //0x000033fb addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc1, //0x000033fd setb         %r9b
	0x01, 0xff, //0x00003401 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00003403 xorl         $1431655765, %edi
	0x44, 0x21, 0xc7, //0x00003409 andl         %r8d, %edi
	0xf7, 0xd7, //0x0000340c notl         %edi
	0x21, 0xf9, //0x0000340e andl         %edi, %ecx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00003410 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x00003414 movq         $-72(%rbp), %r12
	0x4c, 0x8b, 0x45, 0xb0, //0x00003418 movq         $-80(%rbp), %r8
	0xe9, 0x38, 0xf6, 0xff, 0xff, //0x0000341c jmp          LBB0_212
	//0x00003421 LBB0_589
	0x4d, 0x85, 0xf6, //0x00003421 testq        %r14, %r14
	0x0f, 0x84, 0x15, 0x05, 0x00, 0x00, //0x00003424 je           LBB0_652
	0x4c, 0x89, 0xc9, //0x0000342a movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x0000342d notq         %rcx
	0x4c, 0x01, 0xd9, //0x00003430 addq         %r11, %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x00003433 movq         $-48(%rbp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x00003437 cmpq         $-1, %rdx
	0x48, 0x89, 0xd0, //0x0000343b movq         %rdx, %rax
	0x48, 0x0f, 0x44, 0xc1, //0x0000343e cmoveq       %rcx, %rax
	0x48, 0x0f, 0x45, 0xca, //0x00003442 cmovneq      %rdx, %rcx
	0x49, 0x83, 0xc3, 0x01, //0x00003446 addq         $1, %r11
	0x49, 0x83, 0xc6, 0xff, //0x0000344a addq         $-1, %r14
	0x48, 0x89, 0x45, 0xd0, //0x0000344e movq         %rax, $-48(%rbp)
	0x4c, 0x8b, 0x6d, 0xc0, //0x00003452 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x00003456 movq         $-72(%rbp), %r12
	0x4d, 0x85, 0xf6, //0x0000345a testq        %r14, %r14
	0x0f, 0x85, 0xe7, 0xfc, 0xff, 0xff, //0x0000345d jne          LBB0_551
	0xe9, 0x6e, 0xfd, 0xff, 0xff, //0x00003463 jmp          LBB0_561
	//0x00003468 LBB0_591
	0x4d, 0x85, 0xf6, //0x00003468 testq        %r14, %r14
	0x0f, 0x84, 0xea, 0x04, 0x00, 0x00, //0x0000346b je           LBB0_653
	0x4c, 0x89, 0xc9, //0x00003471 movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x00003474 notq         %rcx
	0x4c, 0x01, 0xd9, //0x00003477 addq         %r11, %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x0000347a movq         $-48(%rbp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x0000347e cmpq         $-1, %rdx
	0x48, 0x89, 0xd0, //0x00003482 movq         %rdx, %rax
	0x48, 0x0f, 0x44, 0xc1, //0x00003485 cmoveq       %rcx, %rax
	0x48, 0x0f, 0x45, 0xca, //0x00003489 cmovneq      %rdx, %rcx
	0x49, 0x83, 0xc3, 0x01, //0x0000348d addq         $1, %r11
	0x49, 0x83, 0xc6, 0xff, //0x00003491 addq         $-1, %r14
	0x48, 0x89, 0x45, 0xd0, //0x00003495 movq         %rax, $-48(%rbp)
	0x4c, 0x8b, 0x6d, 0xc0, //0x00003499 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x0000349d movq         $-72(%rbp), %r12
	0x4d, 0x85, 0xf6, //0x000034a1 testq        %r14, %r14
	0x0f, 0x85, 0x60, 0xfe, 0xff, 0xff, //0x000034a4 jne          LBB0_574
	0xe9, 0xe7, 0xfe, 0xff, 0xff, //0x000034aa jmp          LBB0_584
	//0x000034af LBB0_593
	0x49, 0x8d, 0x48, 0xff, //0x000034af leaq         $-1(%r8), %rcx
	0x4c, 0x39, 0xf1, //0x000034b3 cmpq         %r14, %rcx
	0x0f, 0x84, 0x37, 0x02, 0x00, 0x00, //0x000034b6 je           LBB0_638
	0x4c, 0x89, 0xc2, //0x000034bc movq         %r8, %rdx
	0x4f, 0x8d, 0x1c, 0x16, //0x000034bf leaq         (%r14,%r10), %r11
	0x49, 0x83, 0xc3, 0x02, //0x000034c3 addq         $2, %r11
	0x4c, 0x89, 0xc1, //0x000034c7 movq         %r8, %rcx
	0x4c, 0x29, 0xf1, //0x000034ca subq         %r14, %rcx
	0x48, 0x83, 0xc1, 0xfe, //0x000034cd addq         $-2, %rcx
	0x49, 0x89, 0xce, //0x000034d1 movq         %rcx, %r14
	0x4c, 0x8b, 0x6d, 0xc0, //0x000034d4 movq         $-64(%rbp), %r13
	0xe9, 0x20, 0xe4, 0xff, 0xff, //0x000034d8 jmp          LBB0_343
	//0x000034dd LBB0_595
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x000034dd cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x000034e2 jne          LBB0_597
	0x4c, 0x89, 0xd8, //0x000034e8 movq         %r11, %rax
	0x4c, 0x29, 0xc8, //0x000034eb subq         %r9, %rax
	0x48, 0x0f, 0xbc, 0xf2, //0x000034ee bsfq         %rdx, %rsi
	0x48, 0x01, 0xc6, //0x000034f2 addq         %rax, %rsi
	0x48, 0x89, 0x75, 0xd0, //0x000034f5 movq         %rsi, $-48(%rbp)
	//0x000034f9 LBB0_597
	0x44, 0x89, 0xd0, //0x000034f9 movl         %r10d, %eax
	0xf7, 0xd0, //0x000034fc notl         %eax
	0x21, 0xd0, //0x000034fe andl         %edx, %eax
	0x41, 0x8d, 0x34, 0x42, //0x00003500 leal         (%r10,%rax,2), %esi
	0x8d, 0x3c, 0x00, //0x00003504 leal         (%rax,%rax), %edi
	0xf7, 0xd7, //0x00003507 notl         %edi
	0x21, 0xd7, //0x00003509 andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000350b andl         $-1431655766, %edi
	0x45, 0x31, 0xd2, //0x00003511 xorl         %r10d, %r10d
	0x01, 0xc7, //0x00003514 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc2, //0x00003516 setb         %r10b
	0x01, 0xff, //0x0000351a addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x0000351c xorl         $1431655765, %edi
	0x21, 0xf7, //0x00003522 andl         %esi, %edi
	0xf7, 0xd7, //0x00003524 notl         %edi
	0x21, 0xf9, //0x00003526 andl         %edi, %ecx
	0x4c, 0x8b, 0x6d, 0xc0, //0x00003528 movq         $-64(%rbp), %r13
	0x4c, 0x8b, 0x45, 0xb8, //0x0000352c movq         $-72(%rbp), %r8
	0x48, 0x85, 0xc9, //0x00003530 testq        %rcx, %rcx
	0x0f, 0x85, 0x98, 0xf7, 0xff, 0xff, //0x00003533 jne          LBB0_264
	//0x00003539 LBB0_598
	0x49, 0x83, 0xc3, 0x20, //0x00003539 addq         $32, %r11
	0x49, 0x83, 0xc6, 0xe0, //0x0000353d addq         $-32, %r14
	//0x00003541 LBB0_599
	0x4d, 0x85, 0xd2, //0x00003541 testq        %r10, %r10
	0x0f, 0x85, 0x0b, 0x01, 0x00, 0x00, //0x00003544 jne          LBB0_615
	0x48, 0x8b, 0x4d, 0xd0, //0x0000354a movq         $-48(%rbp), %rcx
	0x4d, 0x85, 0xf6, //0x0000354e testq        %r14, %r14
	0x0f, 0x84, 0x84, 0x00, 0x00, 0x00, //0x00003551 je           LBB0_611
	//0x00003557 LBB0_601
	0x4c, 0x89, 0xca, //0x00003557 movq         %r9, %rdx
	0x48, 0xf7, 0xda, //0x0000355a negq         %rdx
	//0x0000355d LBB0_602
	0x31, 0xf6, //0x0000355d xorl         %esi, %esi
	//0x0000355f LBB0_603
	0x41, 0x0f, 0xb6, 0x04, 0x33, //0x0000355f movzbl       (%r11,%rsi), %eax
	0x3c, 0x22, //0x00003564 cmpb         $34, %al
	0x0f, 0x84, 0x68, 0x00, 0x00, 0x00, //0x00003566 je           LBB0_610
	0x3c, 0x5c, //0x0000356c cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000356e je           LBB0_608
	0x48, 0x83, 0xc6, 0x01, //0x00003574 addq         $1, %rsi
	0x49, 0x39, 0xf6, //0x00003578 cmpq         %rsi, %r14
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x0000357b jne          LBB0_603
	0xe9, 0x61, 0x00, 0x00, 0x00, //0x00003581 jmp          LBB0_606
	//0x00003586 LBB0_608
	0x49, 0x8d, 0x46, 0xff, //0x00003586 leaq         $-1(%r14), %rax
	0x48, 0x39, 0xf0, //0x0000358a cmpq         %rsi, %rax
	0x0f, 0x84, 0x6f, 0x03, 0x00, 0x00, //0x0000358d je           LBB0_542
	0x4a, 0x8d, 0x04, 0x1a, //0x00003593 leaq         (%rdx,%r11), %rax
	0x48, 0x01, 0xf0, //0x00003597 addq         %rsi, %rax
	0x48, 0x83, 0xf9, 0xff, //0x0000359a cmpq         $-1, %rcx
	0x48, 0x8b, 0x7d, 0xd0, //0x0000359e movq         $-48(%rbp), %rdi
	0x48, 0x0f, 0x44, 0xf8, //0x000035a2 cmoveq       %rax, %rdi
	0x48, 0x89, 0x7d, 0xd0, //0x000035a6 movq         %rdi, $-48(%rbp)
	0x48, 0x0f, 0x44, 0xc8, //0x000035aa cmoveq       %rax, %rcx
	0x49, 0x01, 0xf3, //0x000035ae addq         %rsi, %r11
	0x49, 0x83, 0xc3, 0x02, //0x000035b1 addq         $2, %r11
	0x4c, 0x89, 0xf0, //0x000035b5 movq         %r14, %rax
	0x48, 0x29, 0xf0, //0x000035b8 subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000035bb addq         $-2, %rax
	0x49, 0x83, 0xc6, 0xfe, //0x000035bf addq         $-2, %r14
	0x49, 0x39, 0xf6, //0x000035c3 cmpq         %rsi, %r14
	0x49, 0x89, 0xc6, //0x000035c6 movq         %rax, %r14
	0x0f, 0x85, 0x8e, 0xff, 0xff, 0xff, //0x000035c9 jne          LBB0_602
	0xe9, 0x2e, 0x03, 0x00, 0x00, //0x000035cf jmp          LBB0_542
	//0x000035d4 LBB0_610
	0x49, 0x01, 0xf3, //0x000035d4 addq         %rsi, %r11
	0x49, 0x83, 0xc3, 0x01, //0x000035d7 addq         $1, %r11
	//0x000035db LBB0_611
	0x4d, 0x29, 0xcb, //0x000035db subq         %r9, %r11
	0x4c, 0x8b, 0x6d, 0xc0, //0x000035de movq         $-64(%rbp), %r13
	0xe9, 0x5d, 0xde, 0xff, 0xff, //0x000035e2 jmp          LBB0_269
	//0x000035e7 LBB0_606
	0x3c, 0x22, //0x000035e7 cmpb         $34, %al
	0x0f, 0x85, 0x13, 0x03, 0x00, 0x00, //0x000035e9 jne          LBB0_542
	0x4d, 0x01, 0xf3, //0x000035ef addq         %r14, %r11
	0xe9, 0xe4, 0xff, 0xff, 0xff, //0x000035f2 jmp          LBB0_611
	//0x000035f7 LBB0_612
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x000035f7 cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x000035fc jne          LBB0_614
	0x4c, 0x89, 0xd8, //0x00003602 movq         %r11, %rax
	0x4c, 0x29, 0xc8, //0x00003605 subq         %r9, %rax
	0x48, 0x0f, 0xbc, 0xfa, //0x00003608 bsfq         %rdx, %rdi
	0x48, 0x01, 0xc7, //0x0000360c addq         %rax, %rdi
	0x48, 0x89, 0x7d, 0xd0, //0x0000360f movq         %rdi, $-48(%rbp)
	//0x00003613 LBB0_614
	0x44, 0x89, 0xf0, //0x00003613 movl         %r14d, %eax
	0xf7, 0xd0, //0x00003616 notl         %eax
	0x21, 0xd0, //0x00003618 andl         %edx, %eax
	0x45, 0x8d, 0x04, 0x46, //0x0000361a leal         (%r14,%rax,2), %r8d
	0x8d, 0x1c, 0x00, //0x0000361e leal         (%rax,%rax), %ebx
	0xf7, 0xd3, //0x00003621 notl         %ebx
	0x21, 0xd3, //0x00003623 andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003625 andl         $-1431655766, %ebx
	0x31, 0xff, //0x0000362b xorl         %edi, %edi
	0x01, 0xc3, //0x0000362d addl         %eax, %ebx
	0x40, 0x0f, 0x92, 0xc7, //0x0000362f setb         %dil
	0x01, 0xdb, //0x00003633 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00003635 xorl         $1431655765, %ebx
	0x44, 0x21, 0xc3, //0x0000363b andl         %r8d, %ebx
	0xf7, 0xd3, //0x0000363e notl         %ebx
	0x41, 0x21, 0xda, //0x00003640 andl         %ebx, %r10d
	0x4c, 0x8b, 0x6d, 0xc0, //0x00003643 movq         $-64(%rbp), %r13
	0x66, 0x41, 0x0f, 0x6f, 0xfc, //0x00003647 movdqa       %xmm12, %xmm7
	0x4c, 0x8b, 0x45, 0xb8, //0x0000364c movq         $-72(%rbp), %r8
	0xe9, 0x0b, 0xf7, 0xff, 0xff, //0x00003650 jmp          LBB0_427
	//0x00003655 LBB0_615
	0x4d, 0x85, 0xf6, //0x00003655 testq        %r14, %r14
	0x0f, 0x84, 0xa4, 0x02, 0x00, 0x00, //0x00003658 je           LBB0_542
	0x4c, 0x89, 0xc9, //0x0000365e movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x00003661 notq         %rcx
	0x4c, 0x01, 0xd9, //0x00003664 addq         %r11, %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x00003667 movq         $-48(%rbp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x0000366b cmpq         $-1, %rdx
	0x48, 0x89, 0xd0, //0x0000366f movq         %rdx, %rax
	0x48, 0x0f, 0x44, 0xc1, //0x00003672 cmoveq       %rcx, %rax
	0x48, 0x0f, 0x45, 0xca, //0x00003676 cmovneq      %rdx, %rcx
	0x49, 0x83, 0xc3, 0x01, //0x0000367a addq         $1, %r11
	0x49, 0x83, 0xc6, 0xff, //0x0000367e addq         $-1, %r14
	0x48, 0x89, 0x45, 0xd0, //0x00003682 movq         %rax, $-48(%rbp)
	0x4d, 0x85, 0xf6, //0x00003686 testq        %r14, %r14
	0x0f, 0x85, 0xc8, 0xfe, 0xff, 0xff, //0x00003689 jne          LBB0_601
	0xe9, 0x47, 0xff, 0xff, 0xff, //0x0000368f jmp          LBB0_611
	//0x00003694 LBB0_617
	0x48, 0x8b, 0x45, 0xa8, //0x00003694 movq         $-88(%rbp), %rax
	0x4c, 0x89, 0x18, //0x00003698 movq         %r11, (%rax)
	//0x0000369b LBB0_618
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000369b movq         $-1, %rax
	0xe9, 0x4c, 0x00, 0x00, 0x00, //0x000036a2 jmp          LBB0_638
	//0x000036a7 LBB0_634
	0x48, 0xc7, 0xc0, 0xf9, 0xff, 0xff, 0xff, //0x000036a7 movq         $-7, %rax
	0xe9, 0x40, 0x00, 0x00, 0x00, //0x000036ae jmp          LBB0_638
	//0x000036b3 LBB0_58
	0x4c, 0x89, 0xd8, //0x000036b3 movq         %r11, %rax
	0x49, 0x83, 0xfb, 0xff, //0x000036b6 cmpq         $-1, %r11
	0x0f, 0x85, 0x82, 0x00, 0x00, 0x00, //0x000036ba jne          LBB0_626
	//0x000036c0 LBB0_59
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000036c0 movq         $-1, %rax
	0x4c, 0x89, 0x65, 0xd0, //0x000036c7 movq         %r12, $-48(%rbp)
	0xe9, 0x72, 0x00, 0x00, 0x00, //0x000036cb jmp          LBB0_626
	//0x000036d0 LBB0_619
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000036d0 movq         $-1, %rax
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000036d7 jmp          LBB0_622
	//0x000036dc LBB0_621
	0x4c, 0x89, 0xd8, //0x000036dc movq         %r11, %rax
	//0x000036df LBB0_622
	0x48, 0xf7, 0xd0, //0x000036df notq         %rax
	0x48, 0x01, 0xc2, //0x000036e2 addq         %rax, %rdx
	0x48, 0x8b, 0x45, 0xa8, //0x000036e5 movq         $-88(%rbp), %rax
	0x48, 0x89, 0x10, //0x000036e9 movq         %rdx, (%rax)
	//0x000036ec LBB0_637
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000036ec movq         $-2, %rax
	//0x000036f3 LBB0_638
	0x48, 0x81, 0xc4, 0x88, 0x00, 0x00, 0x00, //0x000036f3 addq         $136, %rsp
	0x5b, //0x000036fa popq         %rbx
	0x41, 0x5c, //0x000036fb popq         %r12
	0x41, 0x5d, //0x000036fd popq         %r13
	0x41, 0x5e, //0x000036ff popq         %r14
	0x41, 0x5f, //0x00003701 popq         %r15
	0x5d, //0x00003703 popq         %rbp
	0xc3, //0x00003704 retq         
	//0x00003705 LBB0_127
	0x4c, 0x89, 0xd8, //0x00003705 movq         %r11, %rax
	0x49, 0x83, 0xfb, 0xff, //0x00003708 cmpq         $-1, %r11
	0x48, 0x8b, 0x75, 0xd0, //0x0000370c movq         $-48(%rbp), %rsi
	0x0f, 0x85, 0xa7, 0x01, 0x00, 0x00, //0x00003710 jne          LBB0_226
	//0x00003716 LBB0_128
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003716 movq         $-1, %rax
	0x4c, 0x89, 0xe6, //0x0000371d movq         %r12, %rsi
	0xe9, 0x98, 0x01, 0x00, 0x00, //0x00003720 jmp          LBB0_226
	//0x00003725 LBB0_623
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003725 movq         $-2, %rax
	0x48, 0x83, 0x7d, 0xd0, 0xff, //0x0000372c cmpq         $-1, $-48(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00003731 jne          LBB0_626
	0x48, 0x0f, 0xbc, 0xc9, //0x00003737 bsfq         %rcx, %rcx
	0x4c, 0x01, 0xd9, //0x0000373b addq         %r11, %rcx
	//0x0000373e LBB0_625
	0x48, 0x89, 0x4d, 0xd0, //0x0000373e movq         %rcx, $-48(%rbp)
	//0x00003742 LBB0_626
	0x48, 0x8b, 0x4d, 0xa8, //0x00003742 movq         $-88(%rbp), %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x00003746 movq         $-48(%rbp), %rdx
	0x48, 0x89, 0x11, //0x0000374a movq         %rdx, (%rcx)
	0xe9, 0xa1, 0xff, 0xff, 0xff, //0x0000374d jmp          LBB0_638
	//0x00003752 LBB0_312
	0x48, 0x8b, 0x55, 0xa8, //0x00003752 movq         $-88(%rbp), %rdx
	0x48, 0x89, 0x32, //0x00003756 movq         %rsi, (%rdx)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003759 movq         $-2, %rax
	0x41, 0x80, 0x38, 0x6e, //0x00003760 cmpb         $110, (%r8)
	0x0f, 0x85, 0x89, 0xff, 0xff, 0xff, //0x00003764 jne          LBB0_638
	0x48, 0x8d, 0x4e, 0x01, //0x0000376a leaq         $1(%rsi), %rcx
	0x48, 0x89, 0x0a, //0x0000376e movq         %rcx, (%rdx)
	0x41, 0x80, 0x7c, 0x31, 0x01, 0x75, //0x00003771 cmpb         $117, $1(%r9,%rsi)
	0x0f, 0x85, 0x76, 0xff, 0xff, 0xff, //0x00003777 jne          LBB0_638
	0x48, 0x8d, 0x4e, 0x02, //0x0000377d leaq         $2(%rsi), %rcx
	0x48, 0x89, 0x0a, //0x00003781 movq         %rcx, (%rdx)
	0x41, 0x80, 0x7c, 0x31, 0x02, 0x6c, //0x00003784 cmpb         $108, $2(%r9,%rsi)
	0x0f, 0x85, 0x63, 0xff, 0xff, 0xff, //0x0000378a jne          LBB0_638
	0x48, 0x8d, 0x4e, 0x03, //0x00003790 leaq         $3(%rsi), %rcx
	0x48, 0x89, 0x0a, //0x00003794 movq         %rcx, (%rdx)
	0x41, 0x80, 0x7c, 0x31, 0x03, 0x6c, //0x00003797 cmpb         $108, $3(%r9,%rsi)
	0x0f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x0000379d jne          LBB0_638
	0xe9, 0xaa, 0x00, 0x00, 0x00, //0x000037a3 jmp          LBB0_316
	//0x000037a8 LBB0_627
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000037a8 movq         $-2, %rax
	0x80, 0xf9, 0x61, //0x000037af cmpb         $97, %cl
	0x0f, 0x85, 0x3b, 0xff, 0xff, 0xff, //0x000037b2 jne          LBB0_638
	0x48, 0x8d, 0x4e, 0x02, //0x000037b8 leaq         $2(%rsi), %rcx
	0x48, 0x8b, 0x55, 0xa8, //0x000037bc movq         $-88(%rbp), %rdx
	0x48, 0x89, 0x0a, //0x000037c0 movq         %rcx, (%rdx)
	0x41, 0x80, 0x7c, 0x31, 0x02, 0x6c, //0x000037c3 cmpb         $108, $2(%r9,%rsi)
	0x0f, 0x85, 0x24, 0xff, 0xff, 0xff, //0x000037c9 jne          LBB0_638
	0x48, 0x8d, 0x4e, 0x03, //0x000037cf leaq         $3(%rsi), %rcx
	0x48, 0x89, 0x0a, //0x000037d3 movq         %rcx, (%rdx)
	0x41, 0x80, 0x7c, 0x31, 0x03, 0x73, //0x000037d6 cmpb         $115, $3(%r9,%rsi)
	0x0f, 0x85, 0x11, 0xff, 0xff, 0xff, //0x000037dc jne          LBB0_638
	0x48, 0x8d, 0x4e, 0x04, //0x000037e2 leaq         $4(%rsi), %rcx
	0x48, 0x89, 0x0a, //0x000037e6 movq         %rcx, (%rdx)
	0x41, 0x80, 0x7c, 0x31, 0x04, 0x65, //0x000037e9 cmpb         $101, $4(%r9,%rsi)
	0x0f, 0x85, 0xfe, 0xfe, 0xff, 0xff, //0x000037ef jne          LBB0_638
	0x48, 0x83, 0xc6, 0x05, //0x000037f5 addq         $5, %rsi
	0x48, 0x89, 0x32, //0x000037f9 movq         %rsi, (%rdx)
	0xe9, 0xf2, 0xfe, 0xff, 0xff, //0x000037fc jmp          LBB0_638
	//0x00003801 LBB0_322
	0x48, 0x8b, 0x55, 0xa8, //0x00003801 movq         $-88(%rbp), %rdx
	0x48, 0x89, 0x32, //0x00003805 movq         %rsi, (%rdx)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003808 movq         $-2, %rax
	0x41, 0x80, 0x38, 0x74, //0x0000380f cmpb         $116, (%r8)
	0x0f, 0x85, 0xda, 0xfe, 0xff, 0xff, //0x00003813 jne          LBB0_638
	0x48, 0x8d, 0x4e, 0x01, //0x00003819 leaq         $1(%rsi), %rcx
	0x48, 0x89, 0x0a, //0x0000381d movq         %rcx, (%rdx)
	0x41, 0x80, 0x7c, 0x31, 0x01, 0x72, //0x00003820 cmpb         $114, $1(%r9,%rsi)
	0x0f, 0x85, 0xc7, 0xfe, 0xff, 0xff, //0x00003826 jne          LBB0_638
	0x48, 0x8d, 0x4e, 0x02, //0x0000382c leaq         $2(%rsi), %rcx
	0x48, 0x89, 0x0a, //0x00003830 movq         %rcx, (%rdx)
	0x41, 0x80, 0x7c, 0x31, 0x02, 0x75, //0x00003833 cmpb         $117, $2(%r9,%rsi)
	0x0f, 0x85, 0xb4, 0xfe, 0xff, 0xff, //0x00003839 jne          LBB0_638
	0x48, 0x8d, 0x4e, 0x03, //0x0000383f leaq         $3(%rsi), %rcx
	0x48, 0x89, 0x0a, //0x00003843 movq         %rcx, (%rdx)
	0x41, 0x80, 0x7c, 0x31, 0x03, 0x65, //0x00003846 cmpb         $101, $3(%r9,%rsi)
	0x0f, 0x85, 0xa1, 0xfe, 0xff, 0xff, //0x0000384c jne          LBB0_638
	//0x00003852 LBB0_316
	0x48, 0x83, 0xc6, 0x04, //0x00003852 addq         $4, %rsi
	0x48, 0x89, 0x32, //0x00003856 movq         %rsi, (%rdx)
	0xe9, 0x95, 0xfe, 0xff, 0xff, //0x00003859 jmp          LBB0_638
	//0x0000385e LBB0_632
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000385e movq         $-2, %rax
	0x48, 0x8b, 0x75, 0xd0, //0x00003865 movq         $-48(%rbp), %rsi
	0x48, 0x83, 0xfe, 0xff, //0x00003869 cmpq         $-1, %rsi
	0x0f, 0x85, 0x4a, 0x00, 0x00, 0x00, //0x0000386d jne          LBB0_226
	0x48, 0x0f, 0xbc, 0xf1, //0x00003873 bsfq         %rcx, %rsi
	0x4c, 0x01, 0xde, //0x00003877 addq         %r11, %rsi
	0xe9, 0x3e, 0x00, 0x00, 0x00, //0x0000387a jmp          LBB0_226
	//0x0000387f LBB0_198
	0x48, 0x8b, 0x55, 0xc8, //0x0000387f movq         $-56(%rbp), %rdx
	0xe9, 0x57, 0xfe, 0xff, 0xff, //0x00003883 jmp          LBB0_622
	//0x00003888 LBB0_635
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003888 movq         $-1, %rax
	//0x0000388f LBB0_636
	0x48, 0x8b, 0x4d, 0xc8, //0x0000388f movq         $-56(%rbp), %rcx
	0x48, 0x29, 0xc1, //0x00003893 subq         %rax, %rcx
	0x48, 0x8b, 0x45, 0xa8, //0x00003896 movq         $-88(%rbp), %rax
	0x48, 0x89, 0x08, //0x0000389a movq         %rcx, (%rax)
	0xe9, 0x4a, 0xfe, 0xff, 0xff, //0x0000389d jmp          LBB0_637
	//0x000038a2 LBB0_434
	0x4c, 0x89, 0xd8, //0x000038a2 movq         %r11, %rax
	0x49, 0x83, 0xfb, 0xff, //0x000038a5 cmpq         $-1, %r11
	0x48, 0x8b, 0x75, 0xd0, //0x000038a9 movq         $-48(%rbp), %rsi
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x000038ad jne          LBB0_226
	//0x000038b3 LBB0_435
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000038b3 movq         $-1, %rax
	0x4c, 0x89, 0xc6, //0x000038ba movq         %r8, %rsi
	//0x000038bd LBB0_226
	0x48, 0x8b, 0x4d, 0xa8, //0x000038bd movq         $-88(%rbp), %rcx
	0x48, 0x89, 0x31, //0x000038c1 movq         %rsi, (%rcx)
	0xe9, 0x2a, 0xfe, 0xff, 0xff, //0x000038c4 jmp          LBB0_638
	//0x000038c9 LBB0_639
	0x4d, 0x89, 0xdc, //0x000038c9 movq         %r11, %r12
	0xe9, 0xef, 0xfd, 0xff, 0xff, //0x000038cc jmp          LBB0_59
	//0x000038d1 LBB0_640
	0x4d, 0x89, 0xdc, //0x000038d1 movq         %r11, %r12
	0xe9, 0x3d, 0xfe, 0xff, 0xff, //0x000038d4 jmp          LBB0_128
	//0x000038d9 LBB0_190
	0x4c, 0x01, 0xd9, //0x000038d9 addq         %r11, %rcx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000038dc movq         $-2, %rax
	0xe9, 0x56, 0xfe, 0xff, 0xff, //0x000038e3 jmp          LBB0_625
	//0x000038e8 LBB0_225
	0x4c, 0x01, 0xd9, //0x000038e8 addq         %r11, %rcx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000038eb movq         $-2, %rax
	0x48, 0x89, 0xce, //0x000038f2 movq         %rcx, %rsi
	0xe9, 0xc3, 0xff, 0xff, 0xff, //0x000038f5 jmp          LBB0_226
	//0x000038fa LBB0_641
	0x4d, 0x89, 0xd8, //0x000038fa movq         %r11, %r8
	0xe9, 0xb1, 0xff, 0xff, 0xff, //0x000038fd jmp          LBB0_435
	//0x00003902 LBB0_542
	0x4c, 0x8b, 0x45, 0xb8, //0x00003902 movq         $-72(%rbp), %r8
	0xe9, 0xa8, 0xff, 0xff, 0xff, //0x00003906 jmp          LBB0_435
	//0x0000390b LBB0_642
	0x48, 0x8b, 0x4d, 0xa0, //0x0000390b movq         $-96(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x0000390f movq         $8(%rcx), %rcx
	//0x00003913 LBB0_643
	0x48, 0x8b, 0x55, 0xa8, //0x00003913 movq         $-88(%rbp), %rdx
	0x48, 0x89, 0x0a, //0x00003917 movq         %rcx, (%rdx)
	0xe9, 0xd4, 0xfd, 0xff, 0xff, //0x0000391a jmp          LBB0_638
	//0x0000391f LBB0_644
	0x4d, 0x29, 0xcb, //0x0000391f subq         %r9, %r11
	0x49, 0x01, 0xd3, //0x00003922 addq         %rdx, %r11
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x00003925 jmp          LBB0_648
	//0x0000392a LBB0_645
	0x4c, 0x89, 0xde, //0x0000392a movq         %r11, %rsi
	0x4c, 0x29, 0xc6, //0x0000392d subq         %r8, %rsi
	//0x00003930 LBB0_646
	0x48, 0x01, 0xd6, //0x00003930 addq         %rdx, %rsi
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003933 movq         $-2, %rax
	0xe9, 0x7e, 0xff, 0xff, 0xff, //0x0000393a jmp          LBB0_226
	//0x0000393f LBB0_652
	0x4c, 0x8b, 0x65, 0xb8, //0x0000393f movq         $-72(%rbp), %r12
	0xe9, 0x78, 0xfd, 0xff, 0xff, //0x00003943 jmp          LBB0_59
	//0x00003948 LBB0_647
	0x4d, 0x29, 0xcb, //0x00003948 subq         %r9, %r11
	//0x0000394b LBB0_648
	0x4c, 0x89, 0x5d, 0xd0, //0x0000394b movq         %r11, $-48(%rbp)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000394f movq         $-2, %rax
	0xe9, 0xe7, 0xfd, 0xff, 0xff, //0x00003956 jmp          LBB0_626
	//0x0000395b LBB0_653
	0x4c, 0x8b, 0x65, 0xb8, //0x0000395b movq         $-72(%rbp), %r12
	0xe9, 0xb2, 0xfd, 0xff, 0xff, //0x0000395f jmp          LBB0_128
	//0x00003964 LBB0_649
	0x4c, 0x89, 0xde, //0x00003964 movq         %r11, %rsi
	0x4c, 0x29, 0xc6, //0x00003967 subq         %r8, %rsi
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000396a movq         $-2, %rax
	0xe9, 0x47, 0xff, 0xff, 0xff, //0x00003971 jmp          LBB0_226
	//0x00003976 LBB0_650
	0x4c, 0x89, 0xde, //0x00003976 movq         %r11, %rsi
	0x4c, 0x29, 0xce, //0x00003979 subq         %r9, %rsi
	0xe9, 0xaf, 0xff, 0xff, 0xff, //0x0000397c jmp          LBB0_646
	//0x00003981 LBB0_651
	0x4c, 0x89, 0xde, //0x00003981 movq         %r11, %rsi
	0x4c, 0x29, 0xce, //0x00003984 subq         %r9, %rsi
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003987 movq         $-2, %rax
	0xe9, 0x2a, 0xff, 0xff, 0xff, //0x0000398e jmp          LBB0_226
	0x90, //0x00003993 .p2align 2, 0x90
	// // .set L0_0_set_34, LBB0_34-LJTI0_0
	// // .set L0_0_set_62, LBB0_62-LJTI0_0
	// // .set L0_0_set_39, LBB0_39-LJTI0_0
	// // .set L0_0_set_60, LBB0_60-LJTI0_0
	// // .set L0_0_set_37, LBB0_37-LJTI0_0
	// // .set L0_0_set_64, LBB0_64-LJTI0_0
	//0x00003994 LJTI0_0
	0xf8, 0xc9, 0xff, 0xff, //0x00003994 .long L0_0_set_34
	0x0d, 0xcc, 0xff, 0xff, //0x00003998 .long L0_0_set_62
	0x33, 0xca, 0xff, 0xff, //0x0000399c .long L0_0_set_39
	0xf6, 0xcb, 0xff, 0xff, //0x000039a0 .long L0_0_set_60
	0x0f, 0xca, 0xff, 0xff, //0x000039a4 .long L0_0_set_37
	0x39, 0xcc, 0xff, 0xff, //0x000039a8 .long L0_0_set_64
	// // .set L0_1_set_638, LBB0_638-LJTI0_1
	// // .set L0_1_set_637, LBB0_637-LJTI0_1
	// // .set L0_1_set_251, LBB0_251-LJTI0_1
	// // .set L0_1_set_273, LBB0_273-LJTI0_1
	// // .set L0_1_set_68, LBB0_68-LJTI0_1
	// // .set L0_1_set_270, LBB0_270-LJTI0_1
	// // .set L0_1_set_248, LBB0_248-LJTI0_1
	// // .set L0_1_set_310, LBB0_310-LJTI0_1
	// // .set L0_1_set_320, LBB0_320-LJTI0_1
	// // .set L0_1_set_317, LBB0_317-LJTI0_1
	//0x000039ac LJTI0_1
	0x47, 0xfd, 0xff, 0xff, //0x000039ac .long L0_1_set_638
	0x40, 0xfd, 0xff, 0xff, //0x000039b0 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039b4 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039b8 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039bc .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039c0 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039c4 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039c8 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039cc .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039d0 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039d4 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039d8 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039dc .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039e0 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039e4 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039e8 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039ec .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039f0 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039f4 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039f8 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x000039fc .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a00 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a04 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a08 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a0c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a10 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a14 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a18 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a1c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a20 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a24 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a28 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a2c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a30 .long L0_1_set_637
	0xff, 0xd8, 0xff, 0xff, //0x00003a34 .long L0_1_set_251
	0x40, 0xfd, 0xff, 0xff, //0x00003a38 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a3c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a40 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a44 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a48 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a4c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a50 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a54 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a58 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a5c .long L0_1_set_637
	0xde, 0xda, 0xff, 0xff, //0x00003a60 .long L0_1_set_273
	0x40, 0xfd, 0xff, 0xff, //0x00003a64 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a68 .long L0_1_set_637
	0x77, 0xcc, 0xff, 0xff, //0x00003a6c .long L0_1_set_68
	0x77, 0xcc, 0xff, 0xff, //0x00003a70 .long L0_1_set_68
	0x77, 0xcc, 0xff, 0xff, //0x00003a74 .long L0_1_set_68
	0x77, 0xcc, 0xff, 0xff, //0x00003a78 .long L0_1_set_68
	0x77, 0xcc, 0xff, 0xff, //0x00003a7c .long L0_1_set_68
	0x77, 0xcc, 0xff, 0xff, //0x00003a80 .long L0_1_set_68
	0x77, 0xcc, 0xff, 0xff, //0x00003a84 .long L0_1_set_68
	0x77, 0xcc, 0xff, 0xff, //0x00003a88 .long L0_1_set_68
	0x77, 0xcc, 0xff, 0xff, //0x00003a8c .long L0_1_set_68
	0x77, 0xcc, 0xff, 0xff, //0x00003a90 .long L0_1_set_68
	0x40, 0xfd, 0xff, 0xff, //0x00003a94 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a98 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003a9c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003aa0 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003aa4 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003aa8 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003aac .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003ab0 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003ab4 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003ab8 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003abc .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003ac0 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003ac4 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003ac8 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003acc .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003ad0 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003ad4 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003ad8 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003adc .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003ae0 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003ae4 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003ae8 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003aec .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003af0 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003af4 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003af8 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003afc .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b00 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b04 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b08 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b0c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b10 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b14 .long L0_1_set_637
	0xae, 0xda, 0xff, 0xff, //0x00003b18 .long L0_1_set_270
	0x40, 0xfd, 0xff, 0xff, //0x00003b1c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b20 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b24 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b28 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b2c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b30 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b34 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b38 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b3c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b40 .long L0_1_set_637
	0xcd, 0xd8, 0xff, 0xff, //0x00003b44 .long L0_1_set_248
	0x40, 0xfd, 0xff, 0xff, //0x00003b48 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b4c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b50 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b54 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b58 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b5c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b60 .long L0_1_set_637
	0x3c, 0xdd, 0xff, 0xff, //0x00003b64 .long L0_1_set_310
	0x40, 0xfd, 0xff, 0xff, //0x00003b68 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b6c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b70 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b74 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b78 .long L0_1_set_637
	0x97, 0xdd, 0xff, 0xff, //0x00003b7c .long L0_1_set_320
	0x40, 0xfd, 0xff, 0xff, //0x00003b80 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b84 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b88 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b8c .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b90 .long L0_1_set_637
	0x40, 0xfd, 0xff, 0xff, //0x00003b94 .long L0_1_set_637
	0x67, 0xdd, 0xff, 0xff, //0x00003b98 .long L0_1_set_317
	// // .set L0_2_set_299, LBB0_299-LJTI0_2
	// // .set L0_2_set_448, LBB0_448-LJTI0_2
	// // .set L0_2_set_305, LBB0_305-LJTI0_2
	// // .set L0_2_set_308, LBB0_308-LJTI0_2
	//0x00003b9c LJTI0_2
	0xcd, 0xda, 0xff, 0xff, //0x00003b9c .long L0_2_set_299
	0xd9, 0xe9, 0xff, 0xff, //0x00003ba0 .long L0_2_set_448
	0xcd, 0xda, 0xff, 0xff, //0x00003ba4 .long L0_2_set_299
	0x1d, 0xdb, 0xff, 0xff, //0x00003ba8 .long L0_2_set_305
	0xd9, 0xe9, 0xff, 0xff, //0x00003bac .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bb0 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bb4 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bb8 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bbc .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bc0 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bc4 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bc8 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bcc .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bd0 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bd4 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bd8 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bdc .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003be0 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003be4 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003be8 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bec .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bf0 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bf4 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bf8 .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003bfc .long L0_2_set_448
	0xd9, 0xe9, 0xff, 0xff, //0x00003c00 .long L0_2_set_448
	0x39, 0xdb, 0xff, 0xff, //0x00003c04 .long L0_2_set_308
	// // .set L0_3_set_94, LBB0_94-LJTI0_3
	// // .set L0_3_set_232, LBB0_232-LJTI0_3
	// // .set L0_3_set_100, LBB0_100-LJTI0_3
	// // .set L0_3_set_103, LBB0_103-LJTI0_3
	//0x00003c08 LJTI0_3
	0xf1, 0xcb, 0xff, 0xff, //0x00003c08 .long L0_3_set_94
	0x86, 0xd5, 0xff, 0xff, //0x00003c0c .long L0_3_set_232
	0xf1, 0xcb, 0xff, 0xff, //0x00003c10 .long L0_3_set_94
	0x3a, 0xcc, 0xff, 0xff, //0x00003c14 .long L0_3_set_100
	0x86, 0xd5, 0xff, 0xff, //0x00003c18 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c1c .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c20 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c24 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c28 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c2c .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c30 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c34 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c38 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c3c .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c40 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c44 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c48 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c4c .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c50 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c54 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c58 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c5c .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c60 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c64 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c68 .long L0_3_set_232
	0x86, 0xd5, 0xff, 0xff, //0x00003c6c .long L0_3_set_232
	0x56, 0xcc, 0xff, 0xff, //0x00003c70 .long L0_3_set_103
	//0x00003c74 .p2align 2, 0x00
	//0x00003c74 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00003c74 .long 2
}
 
