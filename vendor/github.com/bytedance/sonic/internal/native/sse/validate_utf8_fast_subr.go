// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__validate_utf8_fast = 0
)

const (
    _stack__validate_utf8_fast = 24
)

const (
    _size__validate_utf8_fast = 536
)

var (
    _pcsp__validate_utf8_fast = [][2]uint32{
        {0x1, 0},
        {0x5, 8},
        {0x6, 16},
        {0xfb, 24},
        {0xfc, 16},
        {0xfd, 8},
        {0xfe, 0},
        {0x213, 24},
        {0x214, 16},
        {0x215, 8},
        {0x218, 0},
    }
)

var _cfunc_validate_utf8_fast = []loader.CFunc{
    {"_validate_utf8_fast_entry", 0,  _entry__validate_utf8_fast, 0, nil},
    {"_validate_utf8_fast", _entry__validate_utf8_fast, _size__validate_utf8_fast, _stack__validate_utf8_fast, _pcsp__validate_utf8_fast},
}
