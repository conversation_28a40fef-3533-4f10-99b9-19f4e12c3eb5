// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_i64toa = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x59, 0x17, 0xb7, 0xd1, 0x00, 0x00, 0x00, 0x00, // .quad 3518437209
	0x59, 0x17, 0xb7, 0xd1, 0x00, 0x00, 0x00, 0x00, //0x00000008 .quad 3518437209
	//0x00000010 LCPI0_1
	0xc5, 0x20, //0x00000010 .word 8389
	0x7b, 0x14, //0x00000012 .word 5243
	0x34, 0x33, //0x00000014 .word 13108
	0x00, 0x80, //0x00000016 .word 32768
	0xc5, 0x20, //0x00000018 .word 8389
	0x7b, 0x14, //0x0000001a .word 5243
	0x34, 0x33, //0x0000001c .word 13108
	0x00, 0x80, //0x0000001e .word 32768
	//0x00000020 LCPI0_2
	0x80, 0x00, //0x00000020 .word 128
	0x00, 0x08, //0x00000022 .word 2048
	0x00, 0x20, //0x00000024 .word 8192
	0x00, 0x80, //0x00000026 .word 32768
	0x80, 0x00, //0x00000028 .word 128
	0x00, 0x08, //0x0000002a .word 2048
	0x00, 0x20, //0x0000002c .word 8192
	0x00, 0x80, //0x0000002e .word 32768
	//0x00000030 LCPI0_3
	0x0a, 0x00, //0x00000030 .word 10
	0x0a, 0x00, //0x00000032 .word 10
	0x0a, 0x00, //0x00000034 .word 10
	0x0a, 0x00, //0x00000036 .word 10
	0x0a, 0x00, //0x00000038 .word 10
	0x0a, 0x00, //0x0000003a .word 10
	0x0a, 0x00, //0x0000003c .word 10
	0x0a, 0x00, //0x0000003e .word 10
	//0x00000040 LCPI0_4
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, //0x00000040 QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	//0x00000050 .p2align 4, 0x90
	//0x00000050 _i64toa
	0x55, //0x00000050 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000051 movq         %rsp, %rbp
	0x48, 0x85, 0xf6, //0x00000054 testq        %rsi, %rsi
	0x0f, 0x88, 0xb2, 0x00, 0x00, 0x00, //0x00000057 js           LBB0_25
	0x48, 0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x0000005d cmpq         $9999, %rsi
	0x0f, 0x87, 0xfb, 0x00, 0x00, 0x00, //0x00000064 ja           LBB0_9
	0x0f, 0xb7, 0xc6, //0x0000006a movzwl       %si, %eax
	0xc1, 0xe8, 0x02, //0x0000006d shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000070 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000076 shrl         $17, %eax
	0x48, 0x8d, 0x14, 0x00, //0x00000079 leaq         (%rax,%rax), %rdx
	0x6b, 0xc0, 0x64, //0x0000007d imull        $100, %eax, %eax
	0x89, 0xf1, //0x00000080 movl         %esi, %ecx
	0x29, 0xc1, //0x00000082 subl         %eax, %ecx
	0x0f, 0xb7, 0xc1, //0x00000084 movzwl       %cx, %eax
	0x48, 0x01, 0xc0, //0x00000087 addq         %rax, %rax
	0x81, 0xfe, 0xe8, 0x03, 0x00, 0x00, //0x0000008a cmpl         $1000, %esi
	0x0f, 0x82, 0x16, 0x00, 0x00, 0x00, //0x00000090 jb           LBB0_4
	0x48, 0x8d, 0x0d, 0xc3, 0x08, 0x00, 0x00, //0x00000096 leaq         $2243(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x0c, 0x0a, //0x0000009d movb         (%rdx,%rcx), %cl
	0x88, 0x0f, //0x000000a0 movb         %cl, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000000a2 movl         $1, %ecx
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x000000a7 jmp          LBB0_5
	//0x000000ac LBB0_4
	0x31, 0xc9, //0x000000ac xorl         %ecx, %ecx
	0x83, 0xfe, 0x64, //0x000000ae cmpl         $100, %esi
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x000000b1 jb           LBB0_6
	//0x000000b7 LBB0_5
	0x0f, 0xb7, 0xd2, //0x000000b7 movzwl       %dx, %edx
	0x48, 0x83, 0xca, 0x01, //0x000000ba orq          $1, %rdx
	0x48, 0x8d, 0x35, 0x9b, 0x08, 0x00, 0x00, //0x000000be leaq         $2203(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x32, //0x000000c5 movb         (%rdx,%rsi), %dl
	0x89, 0xce, //0x000000c8 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x000000ca addl         $1, %ecx
	0x88, 0x14, 0x37, //0x000000cd movb         %dl, (%rdi,%rsi)
	//0x000000d0 LBB0_7
	0x48, 0x8d, 0x15, 0x89, 0x08, 0x00, 0x00, //0x000000d0 leaq         $2185(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x10, //0x000000d7 movb         (%rax,%rdx), %dl
	0x89, 0xce, //0x000000da movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x000000dc addl         $1, %ecx
	0x88, 0x14, 0x37, //0x000000df movb         %dl, (%rdi,%rsi)
	//0x000000e2 LBB0_8
	0x0f, 0xb7, 0xc0, //0x000000e2 movzwl       %ax, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000000e5 orq          $1, %rax
	0x48, 0x8d, 0x15, 0x70, 0x08, 0x00, 0x00, //0x000000e9 leaq         $2160(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x10, //0x000000f0 movb         (%rax,%rdx), %al
	0x89, 0xca, //0x000000f3 movl         %ecx, %edx
	0x83, 0xc1, 0x01, //0x000000f5 addl         $1, %ecx
	0x88, 0x04, 0x17, //0x000000f8 movb         %al, (%rdi,%rdx)
	0x89, 0xc8, //0x000000fb movl         %ecx, %eax
	0x5d, //0x000000fd popq         %rbp
	0xc3, //0x000000fe retq         
	//0x000000ff LBB0_6
	0x31, 0xc9, //0x000000ff xorl         %ecx, %ecx
	0x83, 0xfe, 0x0a, //0x00000101 cmpl         $10, %esi
	0x0f, 0x83, 0xc6, 0xff, 0xff, 0xff, //0x00000104 jae          LBB0_7
	0xe9, 0xd3, 0xff, 0xff, 0xff, //0x0000010a jmp          LBB0_8
	//0x0000010f LBB0_25
	0xc6, 0x07, 0x2d, //0x0000010f movb         $45, (%rdi)
	0x48, 0xf7, 0xde, //0x00000112 negq         %rsi
	0x48, 0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x00000115 cmpq         $9999, %rsi
	0x0f, 0x87, 0xd9, 0x01, 0x00, 0x00, //0x0000011c ja           LBB0_33
	0x0f, 0xb7, 0xc6, //0x00000122 movzwl       %si, %eax
	0xc1, 0xe8, 0x02, //0x00000125 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000128 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x0000012e shrl         $17, %eax
	0x48, 0x8d, 0x14, 0x00, //0x00000131 leaq         (%rax,%rax), %rdx
	0x6b, 0xc0, 0x64, //0x00000135 imull        $100, %eax, %eax
	0x89, 0xf1, //0x00000138 movl         %esi, %ecx
	0x29, 0xc1, //0x0000013a subl         %eax, %ecx
	0x0f, 0xb7, 0xc1, //0x0000013c movzwl       %cx, %eax
	0x48, 0x01, 0xc0, //0x0000013f addq         %rax, %rax
	0x81, 0xfe, 0xe8, 0x03, 0x00, 0x00, //0x00000142 cmpl         $1000, %esi
	0x0f, 0x82, 0xab, 0x00, 0x00, 0x00, //0x00000148 jb           LBB0_28
	0x48, 0x8d, 0x0d, 0x0b, 0x08, 0x00, 0x00, //0x0000014e leaq         $2059(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x0c, 0x0a, //0x00000155 movb         (%rdx,%rcx), %cl
	0x88, 0x4f, 0x01, //0x00000158 movb         %cl, $1(%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000015b movl         $1, %ecx
	0xe9, 0x9f, 0x00, 0x00, 0x00, //0x00000160 jmp          LBB0_29
	//0x00000165 LBB0_9
	0x48, 0x81, 0xfe, 0xff, 0xe0, 0xf5, 0x05, //0x00000165 cmpq         $99999999, %rsi
	0x0f, 0x87, 0x1e, 0x02, 0x00, 0x00, //0x0000016c ja           LBB0_17
	0x89, 0xf0, //0x00000172 movl         %esi, %eax
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x00000174 movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x00000179 imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x0000017d shrq         $45, %rdx
	0x44, 0x69, 0xc2, 0x10, 0x27, 0x00, 0x00, //0x00000181 imull        $10000, %edx, %r8d
	0x89, 0xf1, //0x00000188 movl         %esi, %ecx
	0x44, 0x29, 0xc1, //0x0000018a subl         %r8d, %ecx
	0x4c, 0x69, 0xd0, 0x83, 0xde, 0x1b, 0x43, //0x0000018d imulq        $1125899907, %rax, %r10
	0x49, 0xc1, 0xea, 0x31, //0x00000194 shrq         $49, %r10
	0x41, 0x83, 0xe2, 0xfe, //0x00000198 andl         $-2, %r10d
	0x0f, 0xb7, 0xc2, //0x0000019c movzwl       %dx, %eax
	0xc1, 0xe8, 0x02, //0x0000019f shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x000001a2 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x000001a8 shrl         $17, %eax
	0x6b, 0xc0, 0x64, //0x000001ab imull        $100, %eax, %eax
	0x29, 0xc2, //0x000001ae subl         %eax, %edx
	0x44, 0x0f, 0xb7, 0xca, //0x000001b0 movzwl       %dx, %r9d
	0x4d, 0x01, 0xc9, //0x000001b4 addq         %r9, %r9
	0x0f, 0xb7, 0xc1, //0x000001b7 movzwl       %cx, %eax
	0xc1, 0xe8, 0x02, //0x000001ba shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x000001bd imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x000001c3 shrl         $17, %eax
	0x4c, 0x8d, 0x04, 0x00, //0x000001c6 leaq         (%rax,%rax), %r8
	0x6b, 0xc0, 0x64, //0x000001ca imull        $100, %eax, %eax
	0x29, 0xc1, //0x000001cd subl         %eax, %ecx
	0x44, 0x0f, 0xb7, 0xd9, //0x000001cf movzwl       %cx, %r11d
	0x4d, 0x01, 0xdb, //0x000001d3 addq         %r11, %r11
	0x81, 0xfe, 0x80, 0x96, 0x98, 0x00, //0x000001d6 cmpl         $10000000, %esi
	0x0f, 0x82, 0x70, 0x00, 0x00, 0x00, //0x000001dc jb           LBB0_12
	0x48, 0x8d, 0x05, 0x77, 0x07, 0x00, 0x00, //0x000001e2 leaq         $1911(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x02, //0x000001e9 movb         (%r10,%rax), %al
	0x88, 0x07, //0x000001ed movb         %al, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000001ef movl         $1, %ecx
	0xe9, 0x67, 0x00, 0x00, 0x00, //0x000001f4 jmp          LBB0_13
	//0x000001f9 LBB0_28
	0x31, 0xc9, //0x000001f9 xorl         %ecx, %ecx
	0x83, 0xfe, 0x64, //0x000001fb cmpl         $100, %esi
	0x0f, 0x82, 0xd4, 0x00, 0x00, 0x00, //0x000001fe jb           LBB0_30
	//0x00000204 LBB0_29
	0x0f, 0xb7, 0xd2, //0x00000204 movzwl       %dx, %edx
	0x48, 0x83, 0xca, 0x01, //0x00000207 orq          $1, %rdx
	0x48, 0x8d, 0x35, 0x4e, 0x07, 0x00, 0x00, //0x0000020b leaq         $1870(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x32, //0x00000212 movb         (%rdx,%rsi), %dl
	0x89, 0xce, //0x00000215 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x00000217 addl         $1, %ecx
	0x88, 0x54, 0x37, 0x01, //0x0000021a movb         %dl, $1(%rdi,%rsi)
	//0x0000021e LBB0_31
	0x48, 0x8d, 0x15, 0x3b, 0x07, 0x00, 0x00, //0x0000021e leaq         $1851(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x10, //0x00000225 movb         (%rax,%rdx), %dl
	0x89, 0xce, //0x00000228 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x0000022a addl         $1, %ecx
	0x88, 0x54, 0x37, 0x01, //0x0000022d movb         %dl, $1(%rdi,%rsi)
	//0x00000231 LBB0_32
	0x0f, 0xb7, 0xc0, //0x00000231 movzwl       %ax, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000234 orq          $1, %rax
	0x48, 0x8d, 0x15, 0x21, 0x07, 0x00, 0x00, //0x00000238 leaq         $1825(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x10, //0x0000023f movb         (%rax,%rdx), %al
	0x89, 0xca, //0x00000242 movl         %ecx, %edx
	0x83, 0xc1, 0x01, //0x00000244 addl         $1, %ecx
	0x88, 0x44, 0x17, 0x01, //0x00000247 movb         %al, $1(%rdi,%rdx)
	0x83, 0xc1, 0x01, //0x0000024b addl         $1, %ecx
	0x89, 0xc8, //0x0000024e movl         %ecx, %eax
	0x5d, //0x00000250 popq         %rbp
	0xc3, //0x00000251 retq         
	//0x00000252 LBB0_12
	0x31, 0xc9, //0x00000252 xorl         %ecx, %ecx
	0x81, 0xfe, 0x40, 0x42, 0x0f, 0x00, //0x00000254 cmpl         $1000000, %esi
	0x0f, 0x82, 0x88, 0x00, 0x00, 0x00, //0x0000025a jb           LBB0_14
	//0x00000260 LBB0_13
	0x44, 0x89, 0xd0, //0x00000260 movl         %r10d, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000263 orq          $1, %rax
	0x48, 0x8d, 0x35, 0xf2, 0x06, 0x00, 0x00, //0x00000267 leaq         $1778(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x0000026e movb         (%rax,%rsi), %al
	0x89, 0xce, //0x00000271 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x00000273 addl         $1, %ecx
	0x88, 0x04, 0x37, //0x00000276 movb         %al, (%rdi,%rsi)
	//0x00000279 LBB0_15
	0x48, 0x8d, 0x05, 0xe0, 0x06, 0x00, 0x00, //0x00000279 leaq         $1760(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x01, //0x00000280 movb         (%r9,%rax), %al
	0x89, 0xce, //0x00000284 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x00000286 addl         $1, %ecx
	0x88, 0x04, 0x37, //0x00000289 movb         %al, (%rdi,%rsi)
	//0x0000028c LBB0_16
	0x41, 0x0f, 0xb7, 0xc1, //0x0000028c movzwl       %r9w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000290 orq          $1, %rax
	0x48, 0x8d, 0x35, 0xc5, 0x06, 0x00, 0x00, //0x00000294 leaq         $1733(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x0000029b movb         (%rax,%rsi), %al
	0x89, 0xca, //0x0000029e movl         %ecx, %edx
	0x88, 0x04, 0x17, //0x000002a0 movb         %al, (%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x30, //0x000002a3 movb         (%r8,%rsi), %al
	0x88, 0x44, 0x17, 0x01, //0x000002a7 movb         %al, $1(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc0, //0x000002ab movzwl       %r8w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000002af orq          $1, %rax
	0x8a, 0x04, 0x30, //0x000002b3 movb         (%rax,%rsi), %al
	0x88, 0x44, 0x17, 0x02, //0x000002b6 movb         %al, $2(%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x33, //0x000002ba movb         (%r11,%rsi), %al
	0x88, 0x44, 0x17, 0x03, //0x000002be movb         %al, $3(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc3, //0x000002c2 movzwl       %r11w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000002c6 orq          $1, %rax
	0x8a, 0x04, 0x30, //0x000002ca movb         (%rax,%rsi), %al
	0x83, 0xc1, 0x05, //0x000002cd addl         $5, %ecx
	0x88, 0x44, 0x17, 0x04, //0x000002d0 movb         %al, $4(%rdi,%rdx)
	0x89, 0xc8, //0x000002d4 movl         %ecx, %eax
	0x5d, //0x000002d6 popq         %rbp
	0xc3, //0x000002d7 retq         
	//0x000002d8 LBB0_30
	0x31, 0xc9, //0x000002d8 xorl         %ecx, %ecx
	0x83, 0xfe, 0x0a, //0x000002da cmpl         $10, %esi
	0x0f, 0x83, 0x3b, 0xff, 0xff, 0xff, //0x000002dd jae          LBB0_31
	0xe9, 0x49, 0xff, 0xff, 0xff, //0x000002e3 jmp          LBB0_32
	//0x000002e8 LBB0_14
	0x31, 0xc9, //0x000002e8 xorl         %ecx, %ecx
	0x81, 0xfe, 0xa0, 0x86, 0x01, 0x00, //0x000002ea cmpl         $100000, %esi
	0x0f, 0x83, 0x83, 0xff, 0xff, 0xff, //0x000002f0 jae          LBB0_15
	0xe9, 0x91, 0xff, 0xff, 0xff, //0x000002f6 jmp          LBB0_16
	//0x000002fb LBB0_33
	0x48, 0x81, 0xfe, 0xff, 0xe0, 0xf5, 0x05, //0x000002fb cmpq         $99999999, %rsi
	0x0f, 0x87, 0x4c, 0x02, 0x00, 0x00, //0x00000302 ja           LBB0_41
	0x89, 0xf0, //0x00000308 movl         %esi, %eax
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x0000030a movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x0000030f imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x00000313 shrq         $45, %rdx
	0x44, 0x69, 0xc2, 0x10, 0x27, 0x00, 0x00, //0x00000317 imull        $10000, %edx, %r8d
	0x89, 0xf1, //0x0000031e movl         %esi, %ecx
	0x44, 0x29, 0xc1, //0x00000320 subl         %r8d, %ecx
	0x4c, 0x69, 0xd0, 0x83, 0xde, 0x1b, 0x43, //0x00000323 imulq        $1125899907, %rax, %r10
	0x49, 0xc1, 0xea, 0x31, //0x0000032a shrq         $49, %r10
	0x41, 0x83, 0xe2, 0xfe, //0x0000032e andl         $-2, %r10d
	0x0f, 0xb7, 0xc2, //0x00000332 movzwl       %dx, %eax
	0xc1, 0xe8, 0x02, //0x00000335 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000338 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x0000033e shrl         $17, %eax
	0x6b, 0xc0, 0x64, //0x00000341 imull        $100, %eax, %eax
	0x29, 0xc2, //0x00000344 subl         %eax, %edx
	0x44, 0x0f, 0xb7, 0xca, //0x00000346 movzwl       %dx, %r9d
	0x4d, 0x01, 0xc9, //0x0000034a addq         %r9, %r9
	0x0f, 0xb7, 0xc1, //0x0000034d movzwl       %cx, %eax
	0xc1, 0xe8, 0x02, //0x00000350 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000353 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000359 shrl         $17, %eax
	0x4c, 0x8d, 0x04, 0x00, //0x0000035c leaq         (%rax,%rax), %r8
	0x6b, 0xc0, 0x64, //0x00000360 imull        $100, %eax, %eax
	0x29, 0xc1, //0x00000363 subl         %eax, %ecx
	0x44, 0x0f, 0xb7, 0xd9, //0x00000365 movzwl       %cx, %r11d
	0x4d, 0x01, 0xdb, //0x00000369 addq         %r11, %r11
	0x81, 0xfe, 0x80, 0x96, 0x98, 0x00, //0x0000036c cmpl         $10000000, %esi
	0x0f, 0x82, 0x3d, 0x01, 0x00, 0x00, //0x00000372 jb           LBB0_36
	0x48, 0x8d, 0x05, 0xe1, 0x05, 0x00, 0x00, //0x00000378 leaq         $1505(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x02, //0x0000037f movb         (%r10,%rax), %al
	0x88, 0x47, 0x01, //0x00000383 movb         %al, $1(%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000386 movl         $1, %ecx
	0xe9, 0x33, 0x01, 0x00, 0x00, //0x0000038b jmp          LBB0_37
	//0x00000390 LBB0_17
	0x48, 0xb9, 0x00, 0x00, 0xc1, 0x6f, 0xf2, 0x86, 0x23, 0x00, //0x00000390 movabsq      $10000000000000000, %rcx
	0x48, 0x39, 0xce, //0x0000039a cmpq         %rcx, %rsi
	0x0f, 0x83, 0xda, 0x02, 0x00, 0x00, //0x0000039d jae          LBB0_19
	0x48, 0xb9, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x000003a3 movabsq      $-6067343680855748867, %rcx
	0x48, 0x89, 0xf0, //0x000003ad movq         %rsi, %rax
	0x48, 0xf7, 0xe1, //0x000003b0 mulq         %rcx
	0x48, 0xc1, 0xea, 0x1a, //0x000003b3 shrq         $26, %rdx
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x000003b7 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x000003bd subl         %eax, %esi
	0x66, 0x0f, 0x6e, 0xc2, //0x000003bf movd         %edx, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0x35, 0xfc, 0xff, 0xff, //0x000003c3 movdqu       $-971(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd0, //0x000003cb movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0xf4, 0xd1, //0x000003cf pmuludq      %xmm1, %xmm2
	0x66, 0x0f, 0x73, 0xd2, 0x2d, //0x000003d3 psrlq        $45, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x000003d8 movl         $10000, %eax
	0x66, 0x48, 0x0f, 0x6e, 0xd8, //0x000003dd movq         %rax, %xmm3
	0x66, 0x0f, 0x6f, 0xe2, //0x000003e2 movdqa       %xmm2, %xmm4
	0x66, 0x0f, 0xf4, 0xe3, //0x000003e6 pmuludq      %xmm3, %xmm4
	0x66, 0x0f, 0xfa, 0xc4, //0x000003ea psubd        %xmm4, %xmm0
	0x66, 0x0f, 0x61, 0xd0, //0x000003ee punpcklwd    %xmm0, %xmm2
	0x66, 0x0f, 0x73, 0xf2, 0x02, //0x000003f2 psllq        $2, %xmm2
	0xf2, 0x0f, 0x70, 0xc2, 0x50, //0x000003f7 pshuflw      $80, %xmm2, %xmm0
	0x66, 0x0f, 0x70, 0xc0, 0x50, //0x000003fc pshufd       $80, %xmm0, %xmm0
	0xf3, 0x0f, 0x6f, 0x15, 0x07, 0xfc, 0xff, 0xff, //0x00000401 movdqu       $-1017(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc2, //0x00000409 pmulhuw      %xmm2, %xmm0
	0xf3, 0x0f, 0x6f, 0x25, 0x0b, 0xfc, 0xff, 0xff, //0x0000040d movdqu       $-1013(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc4, //0x00000415 pmulhuw      %xmm4, %xmm0
	0xf3, 0x0f, 0x6f, 0x2d, 0x0f, 0xfc, 0xff, 0xff, //0x00000419 movdqu       $-1009(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0x66, 0x0f, 0x6f, 0xf0, //0x00000421 movdqa       %xmm0, %xmm6
	0x66, 0x0f, 0xd5, 0xf5, //0x00000425 pmullw       %xmm5, %xmm6
	0x66, 0x0f, 0x73, 0xf6, 0x10, //0x00000429 psllq        $16, %xmm6
	0x66, 0x0f, 0xf9, 0xc6, //0x0000042e psubw        %xmm6, %xmm0
	0x66, 0x0f, 0x6e, 0xf6, //0x00000432 movd         %esi, %xmm6
	0x66, 0x0f, 0xf4, 0xce, //0x00000436 pmuludq      %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xd1, 0x2d, //0x0000043a psrlq        $45, %xmm1
	0x66, 0x0f, 0xf4, 0xd9, //0x0000043f pmuludq      %xmm1, %xmm3
	0x66, 0x0f, 0xfa, 0xf3, //0x00000443 psubd        %xmm3, %xmm6
	0x66, 0x0f, 0x61, 0xce, //0x00000447 punpcklwd    %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xf1, 0x02, //0x0000044b psllq        $2, %xmm1
	0xf2, 0x0f, 0x70, 0xc9, 0x50, //0x00000450 pshuflw      $80, %xmm1, %xmm1
	0x66, 0x0f, 0x70, 0xc9, 0x50, //0x00000455 pshufd       $80, %xmm1, %xmm1
	0x66, 0x0f, 0xe4, 0xca, //0x0000045a pmulhuw      %xmm2, %xmm1
	0x66, 0x0f, 0xe4, 0xcc, //0x0000045e pmulhuw      %xmm4, %xmm1
	0x66, 0x0f, 0xd5, 0xe9, //0x00000462 pmullw       %xmm1, %xmm5
	0x66, 0x0f, 0x73, 0xf5, 0x10, //0x00000466 psllq        $16, %xmm5
	0x66, 0x0f, 0xf9, 0xcd, //0x0000046b psubw        %xmm5, %xmm1
	0x66, 0x0f, 0x67, 0xc1, //0x0000046f packuswb     %xmm1, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0xc5, 0xfb, 0xff, 0xff, //0x00000473 movdqu       $-1083(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x66, 0x0f, 0xfc, 0xc8, //0x0000047b paddb        %xmm0, %xmm1
	0x66, 0x0f, 0xef, 0xd2, //0x0000047f pxor         %xmm2, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00000483 pcmpeqb      %xmm0, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00000487 pmovmskb     %xmm2, %eax
	0xf7, 0xd0, //0x0000048b notl         %eax
	0x0d, 0x00, 0x80, 0x00, 0x00, //0x0000048d orl          $32768, %eax
	0x0f, 0xbc, 0xc0, //0x00000492 bsfl         %eax, %eax
	0xb9, 0x10, 0x00, 0x00, 0x00, //0x00000495 movl         $16, %ecx
	0x29, 0xc1, //0x0000049a subl         %eax, %ecx
	0x48, 0xc1, 0xe0, 0x04, //0x0000049c shlq         $4, %rax
	0x48, 0x8d, 0x15, 0x89, 0x05, 0x00, 0x00, //0x000004a0 leaq         $1417(%rip), %rdx  /* _VecShiftShuffles+0(%rip) */
	0x66, 0x0f, 0x38, 0x00, 0x0c, 0x10, //0x000004a7 pshufb       (%rax,%rdx), %xmm1
	0xf3, 0x0f, 0x7f, 0x0f, //0x000004ad movdqu       %xmm1, (%rdi)
	0x89, 0xc8, //0x000004b1 movl         %ecx, %eax
	0x5d, //0x000004b3 popq         %rbp
	0xc3, //0x000004b4 retq         
	//0x000004b5 LBB0_36
	0x31, 0xc9, //0x000004b5 xorl         %ecx, %ecx
	0x81, 0xfe, 0x40, 0x42, 0x0f, 0x00, //0x000004b7 cmpl         $1000000, %esi
	0x0f, 0x82, 0x7e, 0x00, 0x00, 0x00, //0x000004bd jb           LBB0_38
	//0x000004c3 LBB0_37
	0x44, 0x89, 0xd0, //0x000004c3 movl         %r10d, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000004c6 orq          $1, %rax
	0x48, 0x8d, 0x35, 0x8f, 0x04, 0x00, 0x00, //0x000004ca leaq         $1167(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x000004d1 movb         (%rax,%rsi), %al
	0x89, 0xce, //0x000004d4 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x000004d6 addl         $1, %ecx
	0x88, 0x44, 0x37, 0x01, //0x000004d9 movb         %al, $1(%rdi,%rsi)
	//0x000004dd LBB0_39
	0x48, 0x8d, 0x05, 0x7c, 0x04, 0x00, 0x00, //0x000004dd leaq         $1148(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x01, //0x000004e4 movb         (%r9,%rax), %al
	0x89, 0xce, //0x000004e8 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x000004ea addl         $1, %ecx
	0x88, 0x44, 0x37, 0x01, //0x000004ed movb         %al, $1(%rdi,%rsi)
	//0x000004f1 LBB0_40
	0x41, 0x0f, 0xb7, 0xc1, //0x000004f1 movzwl       %r9w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000004f5 orq          $1, %rax
	0x48, 0x8d, 0x35, 0x60, 0x04, 0x00, 0x00, //0x000004f9 leaq         $1120(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x00000500 movb         (%rax,%rsi), %al
	0x89, 0xca, //0x00000503 movl         %ecx, %edx
	0x88, 0x44, 0x17, 0x01, //0x00000505 movb         %al, $1(%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x30, //0x00000509 movb         (%r8,%rsi), %al
	0x88, 0x44, 0x17, 0x02, //0x0000050d movb         %al, $2(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc0, //0x00000511 movzwl       %r8w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000515 orq          $1, %rax
	0x8a, 0x04, 0x30, //0x00000519 movb         (%rax,%rsi), %al
	0x88, 0x44, 0x17, 0x03, //0x0000051c movb         %al, $3(%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x33, //0x00000520 movb         (%r11,%rsi), %al
	0x88, 0x44, 0x17, 0x04, //0x00000524 movb         %al, $4(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc3, //0x00000528 movzwl       %r11w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x0000052c orq          $1, %rax
	0x8a, 0x04, 0x30, //0x00000530 movb         (%rax,%rsi), %al
	0x83, 0xc1, 0x05, //0x00000533 addl         $5, %ecx
	0x88, 0x44, 0x17, 0x05, //0x00000536 movb         %al, $5(%rdi,%rdx)
	0x83, 0xc1, 0x01, //0x0000053a addl         $1, %ecx
	0x89, 0xc8, //0x0000053d movl         %ecx, %eax
	0x5d, //0x0000053f popq         %rbp
	0xc3, //0x00000540 retq         
	//0x00000541 LBB0_38
	0x31, 0xc9, //0x00000541 xorl         %ecx, %ecx
	0x81, 0xfe, 0xa0, 0x86, 0x01, 0x00, //0x00000543 cmpl         $100000, %esi
	0x0f, 0x83, 0x8e, 0xff, 0xff, 0xff, //0x00000549 jae          LBB0_39
	0xe9, 0x9d, 0xff, 0xff, 0xff, //0x0000054f jmp          LBB0_40
	//0x00000554 LBB0_41
	0x48, 0xb9, 0x00, 0x00, 0xc1, 0x6f, 0xf2, 0x86, 0x23, 0x00, //0x00000554 movabsq      $10000000000000000, %rcx
	0x48, 0x39, 0xce, //0x0000055e cmpq         %rcx, %rsi
	0x0f, 0x83, 0x81, 0x02, 0x00, 0x00, //0x00000561 jae          LBB0_43
	0x48, 0xb9, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00000567 movabsq      $-6067343680855748867, %rcx
	0x48, 0x89, 0xf0, //0x00000571 movq         %rsi, %rax
	0x48, 0xf7, 0xe1, //0x00000574 mulq         %rcx
	0x48, 0xc1, 0xea, 0x1a, //0x00000577 shrq         $26, %rdx
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x0000057b imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x00000581 subl         %eax, %esi
	0x66, 0x0f, 0x6e, 0xc2, //0x00000583 movd         %edx, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0x71, 0xfa, 0xff, 0xff, //0x00000587 movdqu       $-1423(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd0, //0x0000058f movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0xf4, 0xd1, //0x00000593 pmuludq      %xmm1, %xmm2
	0x66, 0x0f, 0x73, 0xd2, 0x2d, //0x00000597 psrlq        $45, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x0000059c movl         $10000, %eax
	0x66, 0x48, 0x0f, 0x6e, 0xd8, //0x000005a1 movq         %rax, %xmm3
	0x66, 0x0f, 0x6f, 0xe2, //0x000005a6 movdqa       %xmm2, %xmm4
	0x66, 0x0f, 0xf4, 0xe3, //0x000005aa pmuludq      %xmm3, %xmm4
	0x66, 0x0f, 0xfa, 0xc4, //0x000005ae psubd        %xmm4, %xmm0
	0x66, 0x0f, 0x61, 0xd0, //0x000005b2 punpcklwd    %xmm0, %xmm2
	0x66, 0x0f, 0x73, 0xf2, 0x02, //0x000005b6 psllq        $2, %xmm2
	0xf2, 0x0f, 0x70, 0xc2, 0x50, //0x000005bb pshuflw      $80, %xmm2, %xmm0
	0x66, 0x0f, 0x70, 0xc0, 0x50, //0x000005c0 pshufd       $80, %xmm0, %xmm0
	0xf3, 0x0f, 0x6f, 0x15, 0x43, 0xfa, 0xff, 0xff, //0x000005c5 movdqu       $-1469(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc2, //0x000005cd pmulhuw      %xmm2, %xmm0
	0xf3, 0x0f, 0x6f, 0x25, 0x47, 0xfa, 0xff, 0xff, //0x000005d1 movdqu       $-1465(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc4, //0x000005d9 pmulhuw      %xmm4, %xmm0
	0xf3, 0x0f, 0x6f, 0x2d, 0x4b, 0xfa, 0xff, 0xff, //0x000005dd movdqu       $-1461(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0x66, 0x0f, 0x6f, 0xf0, //0x000005e5 movdqa       %xmm0, %xmm6
	0x66, 0x0f, 0xd5, 0xf5, //0x000005e9 pmullw       %xmm5, %xmm6
	0x66, 0x0f, 0x73, 0xf6, 0x10, //0x000005ed psllq        $16, %xmm6
	0x66, 0x0f, 0xf9, 0xc6, //0x000005f2 psubw        %xmm6, %xmm0
	0x66, 0x0f, 0x6e, 0xf6, //0x000005f6 movd         %esi, %xmm6
	0x66, 0x0f, 0xf4, 0xce, //0x000005fa pmuludq      %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xd1, 0x2d, //0x000005fe psrlq        $45, %xmm1
	0x66, 0x0f, 0xf4, 0xd9, //0x00000603 pmuludq      %xmm1, %xmm3
	0x66, 0x0f, 0xfa, 0xf3, //0x00000607 psubd        %xmm3, %xmm6
	0x66, 0x0f, 0x61, 0xce, //0x0000060b punpcklwd    %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xf1, 0x02, //0x0000060f psllq        $2, %xmm1
	0xf2, 0x0f, 0x70, 0xc9, 0x50, //0x00000614 pshuflw      $80, %xmm1, %xmm1
	0x66, 0x0f, 0x70, 0xc9, 0x50, //0x00000619 pshufd       $80, %xmm1, %xmm1
	0x66, 0x0f, 0xe4, 0xca, //0x0000061e pmulhuw      %xmm2, %xmm1
	0x66, 0x0f, 0xe4, 0xcc, //0x00000622 pmulhuw      %xmm4, %xmm1
	0x66, 0x0f, 0xd5, 0xe9, //0x00000626 pmullw       %xmm1, %xmm5
	0x66, 0x0f, 0x73, 0xf5, 0x10, //0x0000062a psllq        $16, %xmm5
	0x66, 0x0f, 0xf9, 0xcd, //0x0000062f psubw        %xmm5, %xmm1
	0x66, 0x0f, 0x67, 0xc1, //0x00000633 packuswb     %xmm1, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0x01, 0xfa, 0xff, 0xff, //0x00000637 movdqu       $-1535(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x66, 0x0f, 0xfc, 0xc8, //0x0000063f paddb        %xmm0, %xmm1
	0x66, 0x0f, 0xef, 0xd2, //0x00000643 pxor         %xmm2, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00000647 pcmpeqb      %xmm0, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x0000064b pmovmskb     %xmm2, %eax
	0xf7, 0xd0, //0x0000064f notl         %eax
	0x0d, 0x00, 0x80, 0x00, 0x00, //0x00000651 orl          $32768, %eax
	0x0f, 0xbc, 0xc0, //0x00000656 bsfl         %eax, %eax
	0xb9, 0x10, 0x00, 0x00, 0x00, //0x00000659 movl         $16, %ecx
	0x29, 0xc1, //0x0000065e subl         %eax, %ecx
	0x48, 0xc1, 0xe0, 0x04, //0x00000660 shlq         $4, %rax
	0x48, 0x8d, 0x15, 0xc5, 0x03, 0x00, 0x00, //0x00000664 leaq         $965(%rip), %rdx  /* _VecShiftShuffles+0(%rip) */
	0x66, 0x0f, 0x38, 0x00, 0x0c, 0x10, //0x0000066b pshufb       (%rax,%rdx), %xmm1
	0xf3, 0x0f, 0x7f, 0x4f, 0x01, //0x00000671 movdqu       %xmm1, $1(%rdi)
	0x83, 0xc1, 0x01, //0x00000676 addl         $1, %ecx
	0x89, 0xc8, //0x00000679 movl         %ecx, %eax
	0x5d, //0x0000067b popq         %rbp
	0xc3, //0x0000067c retq         
	//0x0000067d LBB0_19
	0x48, 0xba, 0x57, 0x78, 0x13, 0xb1, 0x2f, 0x65, 0xa5, 0x39, //0x0000067d movabsq      $4153837486827862103, %rdx
	0x48, 0x89, 0xf0, //0x00000687 movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x0000068a mulq         %rdx
	0x48, 0xc1, 0xea, 0x33, //0x0000068d shrq         $51, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x00000691 imulq        %rdx, %rcx
	0x48, 0x29, 0xce, //0x00000695 subq         %rcx, %rsi
	0x83, 0xfa, 0x09, //0x00000698 cmpl         $9, %edx
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x0000069b ja           LBB0_21
	0x80, 0xc2, 0x30, //0x000006a1 addb         $48, %dl
	0x88, 0x17, //0x000006a4 movb         %dl, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000006a6 movl         $1, %ecx
	0xe9, 0x52, 0x00, 0x00, 0x00, //0x000006ab jmp          LBB0_24
	//0x000006b0 LBB0_21
	0x83, 0xfa, 0x63, //0x000006b0 cmpl         $99, %edx
	0x0f, 0x87, 0x1a, 0x00, 0x00, 0x00, //0x000006b3 ja           LBB0_23
	0x89, 0xd0, //0x000006b9 movl         %edx, %eax
	0x48, 0x8d, 0x0d, 0x9e, 0x02, 0x00, 0x00, //0x000006bb leaq         $670(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x000006c2 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x07, //0x000006c6 movw         %ax, (%rdi)
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x000006c9 movl         $2, %ecx
	0xe9, 0x2f, 0x00, 0x00, 0x00, //0x000006ce jmp          LBB0_24
	//0x000006d3 LBB0_23
	0x89, 0xd0, //0x000006d3 movl         %edx, %eax
	0xc1, 0xe8, 0x02, //0x000006d5 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x000006d8 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x000006de shrl         $17, %eax
	0x8d, 0x48, 0x30, //0x000006e1 leal         $48(%rax), %ecx
	0x88, 0x0f, //0x000006e4 movb         %cl, (%rdi)
	0x6b, 0xc0, 0x64, //0x000006e6 imull        $100, %eax, %eax
	0x29, 0xc2, //0x000006e9 subl         %eax, %edx
	0x0f, 0xb7, 0xc2, //0x000006eb movzwl       %dx, %eax
	0x48, 0x8d, 0x0d, 0x6b, 0x02, 0x00, 0x00, //0x000006ee leaq         $619(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x000006f5 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x47, 0x01, //0x000006f9 movw         %ax, $1(%rdi)
	0xb9, 0x03, 0x00, 0x00, 0x00, //0x000006fd movl         $3, %ecx
	//0x00000702 LBB0_24
	0x48, 0xba, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00000702 movabsq      $-6067343680855748867, %rdx
	0x48, 0x89, 0xf0, //0x0000070c movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x0000070f mulq         %rdx
	0x48, 0xc1, 0xea, 0x1a, //0x00000712 shrq         $26, %rdx
	0x66, 0x0f, 0x6e, 0xc2, //0x00000716 movd         %edx, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0xde, 0xf8, 0xff, 0xff, //0x0000071a movdqu       $-1826(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd8, //0x00000722 movdqa       %xmm0, %xmm3
	0x66, 0x0f, 0xf4, 0xd9, //0x00000726 pmuludq      %xmm1, %xmm3
	0x66, 0x0f, 0x73, 0xd3, 0x2d, //0x0000072a psrlq        $45, %xmm3
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x0000072f movl         $10000, %eax
	0x66, 0x48, 0x0f, 0x6e, 0xd0, //0x00000734 movq         %rax, %xmm2
	0x66, 0x0f, 0x6f, 0xe3, //0x00000739 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0xf4, 0xe2, //0x0000073d pmuludq      %xmm2, %xmm4
	0x66, 0x0f, 0xfa, 0xc4, //0x00000741 psubd        %xmm4, %xmm0
	0x66, 0x0f, 0x61, 0xd8, //0x00000745 punpcklwd    %xmm0, %xmm3
	0x66, 0x0f, 0x73, 0xf3, 0x02, //0x00000749 psllq        $2, %xmm3
	0xf2, 0x0f, 0x70, 0xc3, 0x50, //0x0000074e pshuflw      $80, %xmm3, %xmm0
	0x66, 0x0f, 0x70, 0xc0, 0x50, //0x00000753 pshufd       $80, %xmm0, %xmm0
	0xf3, 0x0f, 0x6f, 0x25, 0xb0, 0xf8, 0xff, 0xff, //0x00000758 movdqu       $-1872(%rip), %xmm4  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc4, //0x00000760 pmulhuw      %xmm4, %xmm0
	0xf3, 0x0f, 0x6f, 0x2d, 0xb4, 0xf8, 0xff, 0xff, //0x00000764 movdqu       $-1868(%rip), %xmm5  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc5, //0x0000076c pmulhuw      %xmm5, %xmm0
	0xf3, 0x0f, 0x6f, 0x1d, 0xb8, 0xf8, 0xff, 0xff, //0x00000770 movdqu       $-1864(%rip), %xmm3  /* LCPI0_3+0(%rip) */
	0x66, 0x0f, 0x6f, 0xf0, //0x00000778 movdqa       %xmm0, %xmm6
	0x66, 0x0f, 0xd5, 0xf3, //0x0000077c pmullw       %xmm3, %xmm6
	0x66, 0x0f, 0x73, 0xf6, 0x10, //0x00000780 psllq        $16, %xmm6
	0x66, 0x0f, 0xf9, 0xc6, //0x00000785 psubw        %xmm6, %xmm0
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x00000789 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x0000078f subl         %eax, %esi
	0x66, 0x0f, 0x6e, 0xf6, //0x00000791 movd         %esi, %xmm6
	0x66, 0x0f, 0xf4, 0xce, //0x00000795 pmuludq      %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xd1, 0x2d, //0x00000799 psrlq        $45, %xmm1
	0x66, 0x0f, 0xf4, 0xd1, //0x0000079e pmuludq      %xmm1, %xmm2
	0x66, 0x0f, 0xfa, 0xf2, //0x000007a2 psubd        %xmm2, %xmm6
	0x66, 0x0f, 0x61, 0xce, //0x000007a6 punpcklwd    %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xf1, 0x02, //0x000007aa psllq        $2, %xmm1
	0xf2, 0x0f, 0x70, 0xc9, 0x50, //0x000007af pshuflw      $80, %xmm1, %xmm1
	0x66, 0x0f, 0x70, 0xc9, 0x50, //0x000007b4 pshufd       $80, %xmm1, %xmm1
	0x66, 0x0f, 0xe4, 0xcc, //0x000007b9 pmulhuw      %xmm4, %xmm1
	0x66, 0x0f, 0xe4, 0xcd, //0x000007bd pmulhuw      %xmm5, %xmm1
	0x66, 0x0f, 0xd5, 0xd9, //0x000007c1 pmullw       %xmm1, %xmm3
	0x66, 0x0f, 0x73, 0xf3, 0x10, //0x000007c5 psllq        $16, %xmm3
	0x66, 0x0f, 0xf9, 0xcb, //0x000007ca psubw        %xmm3, %xmm1
	0x66, 0x0f, 0x67, 0xc1, //0x000007ce packuswb     %xmm1, %xmm0
	0x66, 0x0f, 0xfc, 0x05, 0x66, 0xf8, 0xff, 0xff, //0x000007d2 paddb        $-1946(%rip), %xmm0  /* LCPI0_4+0(%rip) */
	0x89, 0xc8, //0x000007da movl         %ecx, %eax
	0xf3, 0x0f, 0x7f, 0x04, 0x07, //0x000007dc movdqu       %xmm0, (%rdi,%rax)
	0x83, 0xc9, 0x10, //0x000007e1 orl          $16, %ecx
	0x89, 0xc8, //0x000007e4 movl         %ecx, %eax
	0x5d, //0x000007e6 popq         %rbp
	0xc3, //0x000007e7 retq         
	//0x000007e8 LBB0_43
	0x48, 0xba, 0x57, 0x78, 0x13, 0xb1, 0x2f, 0x65, 0xa5, 0x39, //0x000007e8 movabsq      $4153837486827862103, %rdx
	0x48, 0x89, 0xf0, //0x000007f2 movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x000007f5 mulq         %rdx
	0x48, 0xc1, 0xea, 0x33, //0x000007f8 shrq         $51, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x000007fc imulq        %rdx, %rcx
	0x48, 0x29, 0xce, //0x00000800 subq         %rcx, %rsi
	0x83, 0xfa, 0x09, //0x00000803 cmpl         $9, %edx
	0x0f, 0x87, 0x10, 0x00, 0x00, 0x00, //0x00000806 ja           LBB0_45
	0x80, 0xc2, 0x30, //0x0000080c addb         $48, %dl
	0x88, 0x57, 0x01, //0x0000080f movb         %dl, $1(%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000812 movl         $1, %ecx
	0xe9, 0x54, 0x00, 0x00, 0x00, //0x00000817 jmp          LBB0_48
	//0x0000081c LBB0_45
	0x83, 0xfa, 0x63, //0x0000081c cmpl         $99, %edx
	0x0f, 0x87, 0x1b, 0x00, 0x00, 0x00, //0x0000081f ja           LBB0_47
	0x89, 0xd0, //0x00000825 movl         %edx, %eax
	0x48, 0x8d, 0x0d, 0x32, 0x01, 0x00, 0x00, //0x00000827 leaq         $306(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x0000082e movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x47, 0x01, //0x00000832 movw         %ax, $1(%rdi)
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00000836 movl         $2, %ecx
	0xe9, 0x30, 0x00, 0x00, 0x00, //0x0000083b jmp          LBB0_48
	//0x00000840 LBB0_47
	0x89, 0xd0, //0x00000840 movl         %edx, %eax
	0xc1, 0xe8, 0x02, //0x00000842 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000845 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x0000084b shrl         $17, %eax
	0x8d, 0x48, 0x30, //0x0000084e leal         $48(%rax), %ecx
	0x88, 0x4f, 0x01, //0x00000851 movb         %cl, $1(%rdi)
	0x6b, 0xc0, 0x64, //0x00000854 imull        $100, %eax, %eax
	0x29, 0xc2, //0x00000857 subl         %eax, %edx
	0x0f, 0xb7, 0xc2, //0x00000859 movzwl       %dx, %eax
	0x48, 0x8d, 0x0d, 0xfd, 0x00, 0x00, 0x00, //0x0000085c leaq         $253(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x00000863 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x47, 0x02, //0x00000867 movw         %ax, $2(%rdi)
	0xb9, 0x03, 0x00, 0x00, 0x00, //0x0000086b movl         $3, %ecx
	//0x00000870 LBB0_48
	0x48, 0xba, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00000870 movabsq      $-6067343680855748867, %rdx
	0x48, 0x89, 0xf0, //0x0000087a movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x0000087d mulq         %rdx
	0x48, 0xc1, 0xea, 0x1a, //0x00000880 shrq         $26, %rdx
	0x66, 0x0f, 0x6e, 0xc2, //0x00000884 movd         %edx, %xmm0
	0xf3, 0x0f, 0x6f, 0x0d, 0x70, 0xf7, 0xff, 0xff, //0x00000888 movdqu       $-2192(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd8, //0x00000890 movdqa       %xmm0, %xmm3
	0x66, 0x0f, 0xf4, 0xd9, //0x00000894 pmuludq      %xmm1, %xmm3
	0x66, 0x0f, 0x73, 0xd3, 0x2d, //0x00000898 psrlq        $45, %xmm3
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x0000089d movl         $10000, %eax
	0x66, 0x48, 0x0f, 0x6e, 0xd0, //0x000008a2 movq         %rax, %xmm2
	0x66, 0x0f, 0x6f, 0xe3, //0x000008a7 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0xf4, 0xe2, //0x000008ab pmuludq      %xmm2, %xmm4
	0x66, 0x0f, 0xfa, 0xc4, //0x000008af psubd        %xmm4, %xmm0
	0x66, 0x0f, 0x61, 0xd8, //0x000008b3 punpcklwd    %xmm0, %xmm3
	0x66, 0x0f, 0x73, 0xf3, 0x02, //0x000008b7 psllq        $2, %xmm3
	0xf2, 0x0f, 0x70, 0xc3, 0x50, //0x000008bc pshuflw      $80, %xmm3, %xmm0
	0x66, 0x0f, 0x70, 0xc0, 0x50, //0x000008c1 pshufd       $80, %xmm0, %xmm0
	0xf3, 0x0f, 0x6f, 0x25, 0x42, 0xf7, 0xff, 0xff, //0x000008c6 movdqu       $-2238(%rip), %xmm4  /* LCPI0_1+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc4, //0x000008ce pmulhuw      %xmm4, %xmm0
	0xf3, 0x0f, 0x6f, 0x2d, 0x46, 0xf7, 0xff, 0xff, //0x000008d2 movdqu       $-2234(%rip), %xmm5  /* LCPI0_2+0(%rip) */
	0x66, 0x0f, 0xe4, 0xc5, //0x000008da pmulhuw      %xmm5, %xmm0
	0xf3, 0x0f, 0x6f, 0x1d, 0x4a, 0xf7, 0xff, 0xff, //0x000008de movdqu       $-2230(%rip), %xmm3  /* LCPI0_3+0(%rip) */
	0x66, 0x0f, 0x6f, 0xf0, //0x000008e6 movdqa       %xmm0, %xmm6
	0x66, 0x0f, 0xd5, 0xf3, //0x000008ea pmullw       %xmm3, %xmm6
	0x66, 0x0f, 0x73, 0xf6, 0x10, //0x000008ee psllq        $16, %xmm6
	0x66, 0x0f, 0xf9, 0xc6, //0x000008f3 psubw        %xmm6, %xmm0
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x000008f7 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x000008fd subl         %eax, %esi
	0x66, 0x0f, 0x6e, 0xf6, //0x000008ff movd         %esi, %xmm6
	0x66, 0x0f, 0xf4, 0xce, //0x00000903 pmuludq      %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xd1, 0x2d, //0x00000907 psrlq        $45, %xmm1
	0x66, 0x0f, 0xf4, 0xd1, //0x0000090c pmuludq      %xmm1, %xmm2
	0x66, 0x0f, 0xfa, 0xf2, //0x00000910 psubd        %xmm2, %xmm6
	0x66, 0x0f, 0x61, 0xce, //0x00000914 punpcklwd    %xmm6, %xmm1
	0x66, 0x0f, 0x73, 0xf1, 0x02, //0x00000918 psllq        $2, %xmm1
	0xf2, 0x0f, 0x70, 0xc9, 0x50, //0x0000091d pshuflw      $80, %xmm1, %xmm1
	0x66, 0x0f, 0x70, 0xc9, 0x50, //0x00000922 pshufd       $80, %xmm1, %xmm1
	0x66, 0x0f, 0xe4, 0xcc, //0x00000927 pmulhuw      %xmm4, %xmm1
	0x66, 0x0f, 0xe4, 0xcd, //0x0000092b pmulhuw      %xmm5, %xmm1
	0x66, 0x0f, 0xd5, 0xd9, //0x0000092f pmullw       %xmm1, %xmm3
	0x66, 0x0f, 0x73, 0xf3, 0x10, //0x00000933 psllq        $16, %xmm3
	0x66, 0x0f, 0xf9, 0xcb, //0x00000938 psubw        %xmm3, %xmm1
	0x66, 0x0f, 0x67, 0xc1, //0x0000093c packuswb     %xmm1, %xmm0
	0x66, 0x0f, 0xfc, 0x05, 0xf8, 0xf6, 0xff, 0xff, //0x00000940 paddb        $-2312(%rip), %xmm0  /* LCPI0_4+0(%rip) */
	0x89, 0xc8, //0x00000948 movl         %ecx, %eax
	0xf3, 0x0f, 0x7f, 0x44, 0x07, 0x01, //0x0000094a movdqu       %xmm0, $1(%rdi,%rax)
	0x83, 0xc9, 0x10, //0x00000950 orl          $16, %ecx
	0x83, 0xc1, 0x01, //0x00000953 addl         $1, %ecx
	0x89, 0xc8, //0x00000956 movl         %ecx, %eax
	0x5d, //0x00000958 popq         %rbp
	0xc3, //0x00000959 retq         
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000095a .p2align 4, 0x00
	//0x00000960 _Digits
	0x30, 0x30, 0x30, 0x31, 0x30, 0x32, 0x30, 0x33, 0x30, 0x34, 0x30, 0x35, 0x30, 0x36, 0x30, 0x37, //0x00000960 QUAD $0x3330323031303030; QUAD $0x3730363035303430  // .ascii 16, '0001020304050607'
	0x30, 0x38, 0x30, 0x39, 0x31, 0x30, 0x31, 0x31, 0x31, 0x32, 0x31, 0x33, 0x31, 0x34, 0x31, 0x35, //0x00000970 QUAD $0x3131303139303830; QUAD $0x3531343133313231  // .ascii 16, '0809101112131415'
	0x31, 0x36, 0x31, 0x37, 0x31, 0x38, 0x31, 0x39, 0x32, 0x30, 0x32, 0x31, 0x32, 0x32, 0x32, 0x33, //0x00000980 QUAD $0x3931383137313631; QUAD $0x3332323231323032  // .ascii 16, '1617181920212223'
	0x32, 0x34, 0x32, 0x35, 0x32, 0x36, 0x32, 0x37, 0x32, 0x38, 0x32, 0x39, 0x33, 0x30, 0x33, 0x31, //0x00000990 QUAD $0x3732363235323432; QUAD $0x3133303339323832  // .ascii 16, '2425262728293031'
	0x33, 0x32, 0x33, 0x33, 0x33, 0x34, 0x33, 0x35, 0x33, 0x36, 0x33, 0x37, 0x33, 0x38, 0x33, 0x39, //0x000009a0 QUAD $0x3533343333333233; QUAD $0x3933383337333633  // .ascii 16, '3233343536373839'
	0x34, 0x30, 0x34, 0x31, 0x34, 0x32, 0x34, 0x33, 0x34, 0x34, 0x34, 0x35, 0x34, 0x36, 0x34, 0x37, //0x000009b0 QUAD $0x3334323431343034; QUAD $0x3734363435343434  // .ascii 16, '4041424344454647'
	0x34, 0x38, 0x34, 0x39, 0x35, 0x30, 0x35, 0x31, 0x35, 0x32, 0x35, 0x33, 0x35, 0x34, 0x35, 0x35, //0x000009c0 QUAD $0x3135303539343834; QUAD $0x3535343533353235  // .ascii 16, '4849505152535455'
	0x35, 0x36, 0x35, 0x37, 0x35, 0x38, 0x35, 0x39, 0x36, 0x30, 0x36, 0x31, 0x36, 0x32, 0x36, 0x33, //0x000009d0 QUAD $0x3935383537353635; QUAD $0x3336323631363036  // .ascii 16, '5657585960616263'
	0x36, 0x34, 0x36, 0x35, 0x36, 0x36, 0x36, 0x37, 0x36, 0x38, 0x36, 0x39, 0x37, 0x30, 0x37, 0x31, //0x000009e0 QUAD $0x3736363635363436; QUAD $0x3137303739363836  // .ascii 16, '6465666768697071'
	0x37, 0x32, 0x37, 0x33, 0x37, 0x34, 0x37, 0x35, 0x37, 0x36, 0x37, 0x37, 0x37, 0x38, 0x37, 0x39, //0x000009f0 QUAD $0x3537343733373237; QUAD $0x3937383737373637  // .ascii 16, '7273747576777879'
	0x38, 0x30, 0x38, 0x31, 0x38, 0x32, 0x38, 0x33, 0x38, 0x34, 0x38, 0x35, 0x38, 0x36, 0x38, 0x37, //0x00000a00 QUAD $0x3338323831383038; QUAD $0x3738363835383438  // .ascii 16, '8081828384858687'
	0x38, 0x38, 0x38, 0x39, 0x39, 0x30, 0x39, 0x31, 0x39, 0x32, 0x39, 0x33, 0x39, 0x34, 0x39, 0x35, //0x00000a10 QUAD $0x3139303939383838; QUAD $0x3539343933393239  // .ascii 16, '8889909192939495'
	0x39, 0x36, 0x39, 0x37, 0x39, 0x38, 0x39, 0x39, //0x00000a20 QUAD $0x3939383937393639  // .ascii 8, '96979899'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000a28 .p2align 4, 0x00
	//0x00000a30 _VecShiftShuffles
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, //0x00000a30 QUAD $0x0706050403020100; QUAD $0x0f0e0d0c0b0a0908  // .ascii 16, '\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f'
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, //0x00000a40 QUAD $0x0807060504030201; QUAD $0xff0f0e0d0c0b0a09  // .ascii 16, '\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff'
	0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, //0x00000a50 QUAD $0x0908070605040302; QUAD $0xffff0f0e0d0c0b0a  // .ascii 16, '\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff'
	0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, //0x00000a60 QUAD $0x0a09080706050403; QUAD $0xffffff0f0e0d0c0b  // .ascii 16, '\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff'
	0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, //0x00000a70 QUAD $0x0b0a090807060504; QUAD $0xffffffff0f0e0d0c  // .ascii 16, '\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff'
	0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000a80 QUAD $0x0c0b0a0908070605; QUAD $0xffffffffff0f0e0d  // .ascii 16, '\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff'
	0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000a90 QUAD $0x0d0c0b0a09080706; QUAD $0xffffffffffff0f0e  // .ascii 16, '\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff'
	0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000aa0 QUAD $0x0e0d0c0b0a090807; QUAD $0xffffffffffffff0f  // .ascii 16, '\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff\xff'
	0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000ab0 QUAD $0x0f0e0d0c0b0a0908; QUAD $0xffffffffffffffff  // .ascii 16, '\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff\xff\xff'
}
 
