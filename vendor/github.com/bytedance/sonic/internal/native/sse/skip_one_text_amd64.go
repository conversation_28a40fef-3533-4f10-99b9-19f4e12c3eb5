// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_skip_one = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, // QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000010 LCPI0_1
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000010 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000020 LCPI0_2
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000020 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000030 LCPI0_3
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000030 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000040 LCPI0_4
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000040 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000050 LCPI0_5
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000050 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x00000060 LCPI0_6
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000060 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000070 LCPI0_7
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000070 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000080 LCPI0_8
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000080 .quad 1
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000088 .quad 0
	//0x00000090 LCPI0_9
	0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, //0x00000090 QUAD $0x1f1f1f1f1f1f1f1f; QUAD $0x1f1f1f1f1f1f1f1f  // .space 16, '\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f'
	//0x000000a0 LCPI0_10
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000a0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000000b0 LCPI0_11
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000b0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x000000c0 LCPI0_12
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x000000c0 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x000000d0 LCPI0_13
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x000000d0 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x000000e0 LCPI0_14
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000000e0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000000f0 LCPI0_15
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x000000f0 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x00000100 .p2align 4, 0x90
	//0x00000100 _skip_one
	0x55, //0x00000100 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000101 movq         %rsp, %rbp
	0x41, 0x57, //0x00000104 pushq        %r15
	0x41, 0x56, //0x00000106 pushq        %r14
	0x41, 0x55, //0x00000108 pushq        %r13
	0x41, 0x54, //0x0000010a pushq        %r12
	0x53, //0x0000010c pushq        %rbx
	0x48, 0x81, 0xec, 0xa8, 0x00, 0x00, 0x00, //0x0000010d subq         $168, %rsp
	0x49, 0x89, 0xf5, //0x00000114 movq         %rsi, %r13
	0x48, 0x89, 0x8d, 0x70, 0xff, 0xff, 0xff, //0x00000117 movq         %rcx, $-144(%rbp)
	0xf6, 0xc1, 0x40, //0x0000011e testb        $64, %cl
	0x48, 0x89, 0x75, 0xd0, //0x00000121 movq         %rsi, $-48(%rbp)
	0x0f, 0x85, 0xa6, 0x00, 0x00, 0x00, //0x00000125 jne          LBB0_2
	0xf3, 0x0f, 0x6f, 0x05, 0x4d, 0xff, 0xff, 0xff, //0x0000012b movdqu       $-179(%rip), %xmm0  /* LCPI0_8+0(%rip) */
	0xf3, 0x0f, 0x7f, 0x02, //0x00000133 movdqu       %xmm0, (%rdx)
	0x4d, 0x8b, 0x4d, 0x00, //0x00000137 movq         (%r13), %r9
	0x4c, 0x8b, 0x27, //0x0000013b movq         (%rdi), %r12
	0x4c, 0x89, 0xe0, //0x0000013e movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x00000141 notq         %rax
	0x48, 0x89, 0x45, 0x88, //0x00000144 movq         %rax, $-120(%rbp)
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000148 movl         $1, %r8d
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000014e movl         $1, %eax
	0x4c, 0x29, 0xe0, //0x00000153 subq         %r12, %rax
	0x48, 0x89, 0x45, 0x90, //0x00000156 movq         %rax, $-112(%rbp)
	0x4c, 0x89, 0xe0, //0x0000015a movq         %r12, %rax
	0x48, 0xf7, 0xd8, //0x0000015d negq         %rax
	0x48, 0x89, 0x45, 0x80, //0x00000160 movq         %rax, $-128(%rbp)
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x00000164 leaq         $-1(%r12), %rax
	0x48, 0x89, 0x85, 0x78, 0xff, 0xff, 0xff, //0x00000169 movq         %rax, $-136(%rbp)
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00000170 movq         $-1, %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000177 movabsq      $4294977024, %r11
	0xf3, 0x44, 0x0f, 0x6f, 0x0d, 0xa6, 0xfe, 0xff, 0xff, //0x00000181 movdqu       $-346(%rip), %xmm9  /* LCPI0_3+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xad, 0xfe, 0xff, 0xff, //0x0000018a movdqu       $-339(%rip), %xmm10  /* LCPI0_4+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x3d, 0xf4, 0xfe, 0xff, 0xff, //0x00000193 movdqu       $-268(%rip), %xmm15  /* LCPI0_9+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x1d, 0x2b, 0xff, 0xff, 0xff, //0x0000019c movdqu       $-213(%rip), %xmm11  /* LCPI0_13+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x25, 0x62, 0xfe, 0xff, 0xff, //0x000001a5 movdqu       $-414(%rip), %xmm12  /* LCPI0_1+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x2d, 0x29, 0xff, 0xff, 0xff, //0x000001ae movdqu       $-215(%rip), %xmm13  /* LCPI0_14+0(%rip) */
	0xf3, 0x44, 0x0f, 0x6f, 0x35, 0x30, 0xff, 0xff, 0xff, //0x000001b7 movdqu       $-208(%rip), %xmm14  /* LCPI0_15+0(%rip) */
	0x4c, 0x89, 0x65, 0xb8, //0x000001c0 movq         %r12, $-72(%rbp)
	0x48, 0x89, 0x55, 0xa0, //0x000001c4 movq         %rdx, $-96(%rbp)
	0x48, 0x89, 0x7d, 0xb0, //0x000001c8 movq         %rdi, $-80(%rbp)
	0xe9, 0x31, 0x01, 0x00, 0x00, //0x000001cc jmp          LBB0_28
	//0x000001d1 LBB0_2
	0x4c, 0x8b, 0x27, //0x000001d1 movq         (%rdi), %r12
	0x48, 0x8b, 0x5f, 0x08, //0x000001d4 movq         $8(%rdi), %rbx
	0x49, 0x8b, 0x4d, 0x00, //0x000001d8 movq         (%r13), %rcx
	0x48, 0x39, 0xd9, //0x000001dc cmpq         %rbx, %rcx
	0x0f, 0x83, 0x26, 0x00, 0x00, 0x00, //0x000001df jae          LBB0_7
	0x41, 0x8a, 0x04, 0x0c, //0x000001e5 movb         (%r12,%rcx), %al
	0x3c, 0x0d, //0x000001e9 cmpb         $13, %al
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x000001eb je           LBB0_7
	0x3c, 0x20, //0x000001f1 cmpb         $32, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000001f3 je           LBB0_7
	0x04, 0xf5, //0x000001f9 addb         $-11, %al
	0x3c, 0xfe, //0x000001fb cmpb         $-2, %al
	0x0f, 0x83, 0x08, 0x00, 0x00, 0x00, //0x000001fd jae          LBB0_7
	0x49, 0x89, 0xc9, //0x00000203 movq         %rcx, %r9
	0xe9, 0x9d, 0x25, 0x00, 0x00, //0x00000206 jmp          LBB0_481
	//0x0000020b LBB0_7
	0x4c, 0x8d, 0x49, 0x01, //0x0000020b leaq         $1(%rcx), %r9
	0x49, 0x39, 0xd9, //0x0000020f cmpq         %rbx, %r9
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x00000212 jae          LBB0_11
	0x43, 0x8a, 0x04, 0x0c, //0x00000218 movb         (%r12,%r9), %al
	0x3c, 0x0d, //0x0000021c cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000021e je           LBB0_11
	0x3c, 0x20, //0x00000224 cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00000226 je           LBB0_11
	0x04, 0xf5, //0x0000022c addb         $-11, %al
	0x3c, 0xfe, //0x0000022e cmpb         $-2, %al
	0x0f, 0x82, 0x72, 0x25, 0x00, 0x00, //0x00000230 jb           LBB0_481
	//0x00000236 LBB0_11
	0x4c, 0x8d, 0x49, 0x02, //0x00000236 leaq         $2(%rcx), %r9
	0x49, 0x39, 0xd9, //0x0000023a cmpq         %rbx, %r9
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x0000023d jae          LBB0_15
	0x43, 0x8a, 0x04, 0x0c, //0x00000243 movb         (%r12,%r9), %al
	0x3c, 0x0d, //0x00000247 cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00000249 je           LBB0_15
	0x3c, 0x20, //0x0000024f cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00000251 je           LBB0_15
	0x04, 0xf5, //0x00000257 addb         $-11, %al
	0x3c, 0xfe, //0x00000259 cmpb         $-2, %al
	0x0f, 0x82, 0x47, 0x25, 0x00, 0x00, //0x0000025b jb           LBB0_481
	//0x00000261 LBB0_15
	0x4c, 0x8d, 0x49, 0x03, //0x00000261 leaq         $3(%rcx), %r9
	0x49, 0x39, 0xd9, //0x00000265 cmpq         %rbx, %r9
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x00000268 jae          LBB0_19
	0x43, 0x8a, 0x04, 0x0c, //0x0000026e movb         (%r12,%r9), %al
	0x3c, 0x0d, //0x00000272 cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00000274 je           LBB0_19
	0x3c, 0x20, //0x0000027a cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x0000027c je           LBB0_19
	0x04, 0xf5, //0x00000282 addb         $-11, %al
	0x3c, 0xfe, //0x00000284 cmpb         $-2, %al
	0x0f, 0x82, 0x1c, 0x25, 0x00, 0x00, //0x00000286 jb           LBB0_481
	//0x0000028c LBB0_19
	0x48, 0x83, 0xc1, 0x04, //0x0000028c addq         $4, %rcx
	0x48, 0x39, 0xcb, //0x00000290 cmpq         %rcx, %rbx
	0x0f, 0x86, 0xd0, 0x24, 0x00, 0x00, //0x00000293 jbe          LBB0_475
	0x0f, 0x84, 0xea, 0x24, 0x00, 0x00, //0x00000299 je           LBB0_478
	0x4d, 0x8d, 0x04, 0x1c, //0x0000029f leaq         (%r12,%rbx), %r8
	0x48, 0xb8, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000002a3 movabsq      $4294977024, %rax
	0x90, 0x90, 0x90, //0x000002ad .p2align 4, 0x90
	//0x000002b0 LBB0_22
	0x41, 0x0f, 0xbe, 0x14, 0x0c, //0x000002b0 movsbl       (%r12,%rcx), %edx
	0x83, 0xfa, 0x20, //0x000002b5 cmpl         $32, %edx
	0x0f, 0x87, 0xd7, 0x24, 0x00, 0x00, //0x000002b8 ja           LBB0_480
	0x48, 0x0f, 0xa3, 0xd0, //0x000002be btq          %rdx, %rax
	0x0f, 0x83, 0xcd, 0x24, 0x00, 0x00, //0x000002c2 jae          LBB0_480
	0x48, 0x83, 0xc1, 0x01, //0x000002c8 addq         $1, %rcx
	0x48, 0x39, 0xcb, //0x000002cc cmpq         %rcx, %rbx
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x000002cf jne          LBB0_22
	0xe9, 0xb5, 0x24, 0x00, 0x00, //0x000002d5 jmp          LBB0_479
	//0x000002da LBB0_25
	0x4d, 0x89, 0x4d, 0x00, //0x000002da movq         %r9, (%r13)
	0x48, 0x89, 0xd8, //0x000002de movq         %rbx, %rax
	0x48, 0x85, 0xdb, //0x000002e1 testq        %rbx, %rbx
	0x0f, 0x88, 0xf2, 0x30, 0x00, 0x00, //0x000002e4 js           LBB0_582
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002ea .p2align 4, 0x90
	//0x000002f0 LBB0_26
	0x4c, 0x8b, 0x12, //0x000002f0 movq         (%rdx), %r10
	0x4d, 0x89, 0xd0, //0x000002f3 movq         %r10, %r8
	0x4c, 0x89, 0xf0, //0x000002f6 movq         %r14, %rax
	0x4d, 0x85, 0xd2, //0x000002f9 testq        %r10, %r10
	0x0f, 0x84, 0xda, 0x30, 0x00, 0x00, //0x000002fc je           LBB0_582
	//0x00000302 LBB0_28
	0x48, 0x8b, 0x47, 0x08, //0x00000302 movq         $8(%rdi), %rax
	0x49, 0x39, 0xc1, //0x00000306 cmpq         %rax, %r9
	0x0f, 0x83, 0x31, 0x00, 0x00, 0x00, //0x00000309 jae          LBB0_33
	0x43, 0x8a, 0x0c, 0x0c, //0x0000030f movb         (%r12,%r9), %cl
	0x80, 0xf9, 0x0d, //0x00000313 cmpb         $13, %cl
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00000316 je           LBB0_33
	0x80, 0xf9, 0x20, //0x0000031c cmpb         $32, %cl
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x0000031f je           LBB0_33
	0x80, 0xc1, 0xf5, //0x00000325 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000328 cmpb         $-2, %cl
	0x0f, 0x83, 0x0f, 0x00, 0x00, 0x00, //0x0000032b jae          LBB0_33
	0x4c, 0x89, 0xcb, //0x00000331 movq         %r9, %rbx
	0xe9, 0xff, 0x00, 0x00, 0x00, //0x00000334 jmp          LBB0_54
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000339 .p2align 4, 0x90
	//0x00000340 LBB0_33
	0x49, 0x8d, 0x59, 0x01, //0x00000340 leaq         $1(%r9), %rbx
	0x48, 0x39, 0xc3, //0x00000344 cmpq         %rax, %rbx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000347 jae          LBB0_37
	0x41, 0x8a, 0x0c, 0x1c, //0x0000034d movb         (%r12,%rbx), %cl
	0x80, 0xf9, 0x0d, //0x00000351 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000354 je           LBB0_37
	0x80, 0xf9, 0x20, //0x0000035a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000035d je           LBB0_37
	0x80, 0xc1, 0xf5, //0x00000363 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000366 cmpb         $-2, %cl
	0x0f, 0x82, 0xc9, 0x00, 0x00, 0x00, //0x00000369 jb           LBB0_54
	0x90, //0x0000036f .p2align 4, 0x90
	//0x00000370 LBB0_37
	0x49, 0x8d, 0x59, 0x02, //0x00000370 leaq         $2(%r9), %rbx
	0x48, 0x39, 0xc3, //0x00000374 cmpq         %rax, %rbx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000377 jae          LBB0_41
	0x41, 0x8a, 0x0c, 0x1c, //0x0000037d movb         (%r12,%rbx), %cl
	0x80, 0xf9, 0x0d, //0x00000381 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000384 je           LBB0_41
	0x80, 0xf9, 0x20, //0x0000038a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000038d je           LBB0_41
	0x80, 0xc1, 0xf5, //0x00000393 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000396 cmpb         $-2, %cl
	0x0f, 0x82, 0x99, 0x00, 0x00, 0x00, //0x00000399 jb           LBB0_54
	0x90, //0x0000039f .p2align 4, 0x90
	//0x000003a0 LBB0_41
	0x49, 0x8d, 0x59, 0x03, //0x000003a0 leaq         $3(%r9), %rbx
	0x48, 0x39, 0xc3, //0x000003a4 cmpq         %rax, %rbx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000003a7 jae          LBB0_45
	0x41, 0x8a, 0x0c, 0x1c, //0x000003ad movb         (%r12,%rbx), %cl
	0x80, 0xf9, 0x0d, //0x000003b1 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000003b4 je           LBB0_45
	0x80, 0xf9, 0x20, //0x000003ba cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000003bd je           LBB0_45
	0x80, 0xc1, 0xf5, //0x000003c3 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x000003c6 cmpb         $-2, %cl
	0x0f, 0x82, 0x69, 0x00, 0x00, 0x00, //0x000003c9 jb           LBB0_54
	0x90, //0x000003cf .p2align 4, 0x90
	//0x000003d0 LBB0_45
	0x49, 0x83, 0xc1, 0x04, //0x000003d0 addq         $4, %r9
	0x4c, 0x39, 0xc8, //0x000003d4 cmpq         %r9, %rax
	0x0f, 0x86, 0x9c, 0x23, 0x00, 0x00, //0x000003d7 jbe          LBB0_476
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x000003dd je           LBB0_51
	0x49, 0x8d, 0x1c, 0x04, //0x000003e3 leaq         (%r12,%rax), %rbx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003e7 .p2align 4, 0x90
	//0x000003f0 LBB0_48
	0x43, 0x0f, 0xbe, 0x0c, 0x0c, //0x000003f0 movsbl       (%r12,%r9), %ecx
	0x83, 0xf9, 0x20, //0x000003f5 cmpl         $32, %ecx
	0x0f, 0x87, 0x2e, 0x00, 0x00, 0x00, //0x000003f8 ja           LBB0_53
	0x49, 0x0f, 0xa3, 0xcb, //0x000003fe btq          %rcx, %r11
	0x0f, 0x83, 0x24, 0x00, 0x00, 0x00, //0x00000402 jae          LBB0_53
	0x49, 0x83, 0xc1, 0x01, //0x00000408 addq         $1, %r9
	0x4c, 0x39, 0xc8, //0x0000040c cmpq         %r9, %rax
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x0000040f jne          LBB0_48
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x00000415 jmp          LBB0_52
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000041a .p2align 4, 0x90
	//0x00000420 LBB0_51
	0x4d, 0x01, 0xe1, //0x00000420 addq         %r12, %r9
	0x4c, 0x89, 0xcb, //0x00000423 movq         %r9, %rbx
	//0x00000426 LBB0_52
	0x4c, 0x29, 0xe3, //0x00000426 subq         %r12, %rbx
	0x49, 0x89, 0xd9, //0x00000429 movq         %rbx, %r9
	//0x0000042c LBB0_53
	0x4c, 0x89, 0xcb, //0x0000042c movq         %r9, %rbx
	0x49, 0x39, 0xc1, //0x0000042f cmpq         %rax, %r9
	0x0f, 0x83, 0x45, 0x23, 0x00, 0x00, //0x00000432 jae          LBB0_477
	//0x00000438 LBB0_54
	0x4c, 0x8d, 0x4b, 0x01, //0x00000438 leaq         $1(%rbx), %r9
	0x4d, 0x89, 0x4d, 0x00, //0x0000043c movq         %r9, (%r13)
	0x45, 0x0f, 0xbe, 0x3c, 0x1c, //0x00000440 movsbl       (%r12,%rbx), %r15d
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000445 movq         $-1, %rax
	0x45, 0x85, 0xff, //0x0000044c testl        %r15d, %r15d
	0x0f, 0x84, 0x87, 0x2f, 0x00, 0x00, //0x0000044f je           LBB0_582
	0x4d, 0x8d, 0x50, 0xff, //0x00000455 leaq         $-1(%r8), %r10
	0x42, 0x8b, 0x0c, 0xc2, //0x00000459 movl         (%rdx,%r8,8), %ecx
	0x49, 0x83, 0xfe, 0xff, //0x0000045d cmpq         $-1, %r14
	0x48, 0x89, 0x5d, 0xc0, //0x00000461 movq         %rbx, $-64(%rbp)
	0x4c, 0x0f, 0x44, 0xf3, //0x00000465 cmoveq       %rbx, %r14
	0x83, 0xc1, 0xff, //0x00000469 addl         $-1, %ecx
	0x83, 0xf9, 0x05, //0x0000046c cmpl         $5, %ecx
	0x0f, 0x87, 0x55, 0x02, 0x00, 0x00, //0x0000046f ja           LBB0_87
	0x48, 0x8d, 0x1d, 0x44, 0x33, 0x00, 0x00, //0x00000475 leaq         $13124(%rip), %rbx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x0c, 0x8b, //0x0000047c movslq       (%rbx,%rcx,4), %rcx
	0x48, 0x01, 0xd9, //0x00000480 addq         %rbx, %rcx
	0xff, 0xe1, //0x00000483 jmpq         *%rcx
	//0x00000485 LBB0_57
	0x41, 0x83, 0xff, 0x2c, //0x00000485 cmpl         $44, %r15d
	0x0f, 0x84, 0xdb, 0x04, 0x00, 0x00, //0x00000489 je           LBB0_126
	0x41, 0x83, 0xff, 0x5d, //0x0000048f cmpl         $93, %r15d
	0x0f, 0x84, 0xba, 0x04, 0x00, 0x00, //0x00000493 je           LBB0_59
	0xe9, 0x86, 0x2e, 0x00, 0x00, //0x00000499 jmp          LBB0_567
	//0x0000049e LBB0_60
	0x41, 0x80, 0xff, 0x5d, //0x0000049e cmpb         $93, %r15b
	0x0f, 0x84, 0xab, 0x04, 0x00, 0x00, //0x000004a2 je           LBB0_59
	0x4a, 0xc7, 0x04, 0xc2, 0x01, 0x00, 0x00, 0x00, //0x000004a8 movq         $1, (%rdx,%r8,8)
	0x41, 0x83, 0xff, 0x7b, //0x000004b0 cmpl         $123, %r15d
	0x48, 0x8b, 0x75, 0xc0, //0x000004b4 movq         $-64(%rbp), %rsi
	0x0f, 0x86, 0x1d, 0x02, 0x00, 0x00, //0x000004b8 jbe          LBB0_62
	0xe9, 0x61, 0x2e, 0x00, 0x00, //0x000004be jmp          LBB0_567
	//0x000004c3 LBB0_63
	0x41, 0x80, 0xff, 0x22, //0x000004c3 cmpb         $34, %r15b
	0x0f, 0x85, 0x57, 0x2e, 0x00, 0x00, //0x000004c7 jne          LBB0_567
	0x4c, 0x89, 0x75, 0xa8, //0x000004cd movq         %r14, $-88(%rbp)
	0x4a, 0xc7, 0x04, 0xc2, 0x04, 0x00, 0x00, 0x00, //0x000004d1 movq         $4, (%rdx,%r8,8)
	0x48, 0x8b, 0x47, 0x08, //0x000004d9 movq         $8(%rdi), %rax
	0xf6, 0x85, 0x70, 0xff, 0xff, 0xff, 0x20, //0x000004dd testb        $32, $-144(%rbp)
	0x48, 0x89, 0x45, 0x98, //0x000004e4 movq         %rax, $-104(%rbp)
	0x49, 0x89, 0xc6, //0x000004e8 movq         %rax, %r14
	0x0f, 0x85, 0x56, 0x06, 0x00, 0x00, //0x000004eb jne          LBB0_147
	0x4d, 0x29, 0xce, //0x000004f1 subq         %r9, %r14
	0x0f, 0x84, 0x19, 0x30, 0x00, 0x00, //0x000004f4 je           LBB0_599
	0x49, 0x83, 0xfe, 0x40, //0x000004fa cmpq         $64, %r14
	0x0f, 0x82, 0x60, 0x1b, 0x00, 0x00, //0x000004fe jb           LBB0_405
	0x49, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x00000504 movq         $-2, %r15
	0x4c, 0x2b, 0x7d, 0xc0, //0x0000050b subq         $-64(%rbp), %r15
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x0000050f movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x00000517 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000051a .p2align 4, 0x90
	//0x00000520 LBB0_68
	0xf3, 0x43, 0x0f, 0x6f, 0x04, 0x0c, //0x00000520 movdqu       (%r12,%r9), %xmm0
	0xf3, 0x43, 0x0f, 0x6f, 0x4c, 0x0c, 0x10, //0x00000526 movdqu       $16(%r12,%r9), %xmm1
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x0c, 0x20, //0x0000052d movdqu       $32(%r12,%r9), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x0c, 0x30, //0x00000534 movdqu       $48(%r12,%r9), %xmm3
	0x66, 0x0f, 0x6f, 0xe0, //0x0000053b movdqa       %xmm0, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x0000053f pcmpeqb      %xmm9, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xc4, //0x00000544 pmovmskb     %xmm4, %r8d
	0x66, 0x0f, 0x6f, 0xe1, //0x00000549 movdqa       %xmm1, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x0000054d pcmpeqb      %xmm9, %xmm4
	0x66, 0x0f, 0xd7, 0xf4, //0x00000552 pmovmskb     %xmm4, %esi
	0x66, 0x0f, 0x6f, 0xe2, //0x00000556 movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x0000055a pcmpeqb      %xmm9, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x0000055f pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x6f, 0xe3, //0x00000563 movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x00000567 pcmpeqb      %xmm9, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x0000056c pmovmskb     %xmm4, %edx
	0x66, 0x41, 0x0f, 0x74, 0xc2, //0x00000570 pcmpeqb      %xmm10, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xd0, //0x00000575 pmovmskb     %xmm0, %r10d
	0x66, 0x41, 0x0f, 0x74, 0xca, //0x0000057a pcmpeqb      %xmm10, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x0000057f pmovmskb     %xmm1, %ecx
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00000583 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00000588 pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x0000058c pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00000591 pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe2, 0x30, //0x00000595 shlq         $48, %rdx
	0x48, 0xc1, 0xe3, 0x20, //0x00000599 shlq         $32, %rbx
	0x48, 0x09, 0xd3, //0x0000059d orq          %rdx, %rbx
	0x48, 0xc1, 0xe6, 0x10, //0x000005a0 shlq         $16, %rsi
	0x48, 0x09, 0xde, //0x000005a4 orq          %rbx, %rsi
	0x49, 0x09, 0xf0, //0x000005a7 orq          %rsi, %r8
	0x48, 0xc1, 0xe7, 0x30, //0x000005aa shlq         $48, %rdi
	0x48, 0xc1, 0xe0, 0x20, //0x000005ae shlq         $32, %rax
	0x48, 0x09, 0xf8, //0x000005b2 orq          %rdi, %rax
	0x48, 0xc1, 0xe1, 0x10, //0x000005b5 shlq         $16, %rcx
	0x48, 0x09, 0xc1, //0x000005b9 orq          %rax, %rcx
	0x49, 0x09, 0xca, //0x000005bc orq          %rcx, %r10
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x000005bf jne          LBB0_77
	0x4d, 0x85, 0xdb, //0x000005c5 testq        %r11, %r11
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x000005c8 jne          LBB0_79
	0x45, 0x31, 0xdb, //0x000005ce xorl         %r11d, %r11d
	0x4d, 0x85, 0xc0, //0x000005d1 testq        %r8, %r8
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x000005d4 jne          LBB0_80
	//0x000005da LBB0_71
	0x49, 0x83, 0xc6, 0xc0, //0x000005da addq         $-64, %r14
	0x49, 0x83, 0xc7, 0xc0, //0x000005de addq         $-64, %r15
	0x49, 0x83, 0xc1, 0x40, //0x000005e2 addq         $64, %r9
	0x49, 0x83, 0xfe, 0x3f, //0x000005e6 cmpq         $63, %r14
	0x0f, 0x87, 0x30, 0xff, 0xff, 0xff, //0x000005ea ja           LBB0_68
	0xe9, 0xff, 0x12, 0x00, 0x00, //0x000005f0 jmp          LBB0_72
	//0x000005f5 LBB0_77
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x000005f5 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x000005fa jne          LBB0_79
	0x49, 0x0f, 0xbc, 0xc2, //0x00000600 bsfq         %r10, %rax
	0x4c, 0x01, 0xc8, //0x00000604 addq         %r9, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00000607 movq         %rax, $-56(%rbp)
	//0x0000060b LBB0_79
	0x4c, 0x89, 0xd8, //0x0000060b movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x0000060e notq         %rax
	0x4c, 0x21, 0xd0, //0x00000611 andq         %r10, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00000614 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd9, //0x00000618 orq          %r11, %rcx
	0x48, 0x89, 0xca, //0x0000061b movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x0000061e notq         %rdx
	0x4c, 0x21, 0xd2, //0x00000621 andq         %r10, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000624 movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x0000062e andq         %rsi, %rdx
	0x45, 0x31, 0xdb, //0x00000631 xorl         %r11d, %r11d
	0x48, 0x01, 0xc2, //0x00000634 addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc3, //0x00000637 setb         %r11b
	0x48, 0x01, 0xd2, //0x0000063b addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000063e movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00000648 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x0000064b andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x0000064e notq         %rdx
	0x49, 0x21, 0xd0, //0x00000651 andq         %rdx, %r8
	0x4d, 0x85, 0xc0, //0x00000654 testq        %r8, %r8
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00000657 je           LBB0_71
	//0x0000065d LBB0_80
	0x4d, 0x0f, 0xbc, 0xc8, //0x0000065d bsfq         %r8, %r9
	0x4d, 0x29, 0xf9, //0x00000661 subq         %r15, %r9
	//0x00000664 LBB0_81
	0x4c, 0x8b, 0x6d, 0xd0, //0x00000664 movq         $-48(%rbp), %r13
	0x48, 0x8b, 0x7d, 0xb0, //0x00000668 movq         $-80(%rbp), %rdi
	//0x0000066c LBB0_82
	0x48, 0x8b, 0x55, 0xa0, //0x0000066c movq         $-96(%rbp), %rdx
	0x4c, 0x8b, 0x75, 0xa8, //0x00000670 movq         $-88(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000674 movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x0000067e movq         $-64(%rbp), %rbx
	0xe9, 0x5f, 0x09, 0x00, 0x00, //0x00000682 jmp          LBB0_214
	//0x00000687 LBB0_83
	0x41, 0x80, 0xff, 0x3a, //0x00000687 cmpb         $58, %r15b
	0x0f, 0x85, 0x93, 0x2c, 0x00, 0x00, //0x0000068b jne          LBB0_567
	0x4a, 0xc7, 0x04, 0xc2, 0x00, 0x00, 0x00, 0x00, //0x00000691 movq         $0, (%rdx,%r8,8)
	0xe9, 0x52, 0xfc, 0xff, 0xff, //0x00000699 jmp          LBB0_26
	//0x0000069e LBB0_85
	0x41, 0x83, 0xff, 0x2c, //0x0000069e cmpl         $44, %r15d
	0x0f, 0x85, 0xa1, 0x02, 0x00, 0x00, //0x000006a2 jne          LBB0_86
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x000006a8 cmpq         $4095, %r8
	0x0f, 0x8f, 0x10, 0x22, 0x00, 0x00, //0x000006af jg           LBB0_585
	0x49, 0x8d, 0x40, 0x01, //0x000006b5 leaq         $1(%r8), %rax
	0x48, 0x89, 0x02, //0x000006b9 movq         %rax, (%rdx)
	0x4a, 0xc7, 0x44, 0xc2, 0x08, 0x03, 0x00, 0x00, 0x00, //0x000006bc movq         $3, $8(%rdx,%r8,8)
	0xe9, 0x26, 0xfc, 0xff, 0xff, //0x000006c5 jmp          LBB0_26
	//0x000006ca LBB0_87
	0x4c, 0x89, 0x12, //0x000006ca movq         %r10, (%rdx)
	0x41, 0x83, 0xff, 0x7b, //0x000006cd cmpl         $123, %r15d
	0x48, 0x8b, 0x75, 0xc0, //0x000006d1 movq         $-64(%rbp), %rsi
	0x0f, 0x87, 0x49, 0x2c, 0x00, 0x00, //0x000006d5 ja           LBB0_567
	//0x000006db LBB0_62
	0x49, 0x8d, 0x0c, 0x34, //0x000006db leaq         (%r12,%rsi), %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x000006df movq         %rcx, $-56(%rbp)
	0x44, 0x89, 0xf9, //0x000006e3 movl         %r15d, %ecx
	0x48, 0x8d, 0x1d, 0xeb, 0x30, 0x00, 0x00, //0x000006e6 leaq         $12523(%rip), %rbx  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x0c, 0x8b, //0x000006ed movslq       (%rbx,%rcx,4), %rcx
	0x48, 0x01, 0xd9, //0x000006f1 addq         %rbx, %rcx
	0xff, 0xe1, //0x000006f4 jmpq         *%rcx
	//0x000006f6 LBB0_90
	0x4c, 0x8b, 0x47, 0x08, //0x000006f6 movq         $8(%rdi), %r8
	0x49, 0x29, 0xf0, //0x000006fa subq         %rsi, %r8
	0x0f, 0x84, 0x04, 0x2c, 0x00, 0x00, //0x000006fd je           LBB0_564
	0x48, 0x89, 0xf3, //0x00000703 movq         %rsi, %rbx
	0x48, 0x8b, 0x75, 0xc8, //0x00000706 movq         $-56(%rbp), %rsi
	0x80, 0x3e, 0x30, //0x0000070a cmpb         $48, (%rsi)
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x0000070d jne          LBB0_95
	0x49, 0x83, 0xf8, 0x01, //0x00000713 cmpq         $1, %r8
	0x0f, 0x84, 0xbd, 0xfb, 0xff, 0xff, //0x00000717 je           LBB0_25
	0x43, 0x8a, 0x04, 0x0c, //0x0000071d movb         (%r12,%r9), %al
	0x04, 0xd2, //0x00000721 addb         $-46, %al
	0x3c, 0x37, //0x00000723 cmpb         $55, %al
	0x0f, 0x87, 0xaf, 0xfb, 0xff, 0xff, //0x00000725 ja           LBB0_25
	0x0f, 0xb6, 0xc0, //0x0000072b movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x0000072e movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00000738 btq          %rax, %rcx
	0x0f, 0x83, 0x98, 0xfb, 0xff, 0xff, //0x0000073c jae          LBB0_25
	//0x00000742 LBB0_95
	0x4c, 0x89, 0x75, 0xa8, //0x00000742 movq         %r14, $-88(%rbp)
	0x49, 0x83, 0xf8, 0x10, //0x00000746 cmpq         $16, %r8
	0x0f, 0x82, 0x57, 0x18, 0x00, 0x00, //0x0000074a jb           LBB0_390
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000750 movq         $-1, %r15
	0x45, 0x31, 0xc9, //0x00000757 xorl         %r9d, %r9d
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000075a movq         $-1, %r12
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00000761 movq         $-1, %r14
	0x4d, 0x89, 0xc2, //0x00000768 movq         %r8, %r10
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000076b .p2align 4, 0x90
	//0x00000770 LBB0_97
	0xf3, 0x42, 0x0f, 0x6f, 0x04, 0x0e, //0x00000770 movdqu       (%rsi,%r9), %xmm0
	0x66, 0x0f, 0x6f, 0xc8, //0x00000776 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x74, 0x0d, 0x1e, 0xf9, 0xff, 0xff, //0x0000077a pcmpeqb      $-1762(%rip), %xmm1  /* LCPI0_10+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd0, //0x00000782 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0x15, 0x22, 0xf9, 0xff, 0xff, //0x00000786 pcmpeqb      $-1758(%rip), %xmm2  /* LCPI0_11+0(%rip) */
	0x66, 0x0f, 0xeb, 0xd1, //0x0000078e por          %xmm1, %xmm2
	0x66, 0x0f, 0x6f, 0xc8, //0x00000792 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0xfc, 0x0d, 0x22, 0xf9, 0xff, 0xff, //0x00000796 paddb        $-1758(%rip), %xmm1  /* LCPI0_12+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd9, //0x0000079e movdqa       %xmm1, %xmm3
	0x66, 0x41, 0x0f, 0xda, 0xdb, //0x000007a2 pminub       %xmm11, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x000007a7 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0x6f, 0xc8, //0x000007ab movdqa       %xmm0, %xmm1
	0x66, 0x41, 0x0f, 0xdb, 0xcc, //0x000007af pand         %xmm12, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x000007b4 pcmpeqb      %xmm13, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xce, //0x000007b9 pcmpeqb      %xmm14, %xmm1
	0x66, 0x44, 0x0f, 0xd7, 0xe9, //0x000007be pmovmskb     %xmm1, %r13d
	0x66, 0x0f, 0xeb, 0xc8, //0x000007c3 por          %xmm0, %xmm1
	0x66, 0x0f, 0xeb, 0xca, //0x000007c7 por          %xmm2, %xmm1
	0x66, 0x0f, 0xeb, 0xcb, //0x000007cb por          %xmm3, %xmm1
	0x66, 0x0f, 0xd7, 0xd0, //0x000007cf pmovmskb     %xmm0, %edx
	0x66, 0x44, 0x0f, 0xd7, 0xda, //0x000007d3 pmovmskb     %xmm2, %r11d
	0x66, 0x0f, 0xd7, 0xc1, //0x000007d8 pmovmskb     %xmm1, %eax
	0xf7, 0xd0, //0x000007dc notl         %eax
	0x0f, 0xbc, 0xc8, //0x000007de bsfl         %eax, %ecx
	0x83, 0xf9, 0x10, //0x000007e1 cmpl         $16, %ecx
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000007e4 je           LBB0_99
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000007ea movl         $-1, %eax
	0xd3, 0xe0, //0x000007ef shll         %cl, %eax
	0xf7, 0xd0, //0x000007f1 notl         %eax
	0x21, 0xc2, //0x000007f3 andl         %eax, %edx
	0x41, 0x21, 0xc5, //0x000007f5 andl         %eax, %r13d
	0x44, 0x21, 0xd8, //0x000007f8 andl         %r11d, %eax
	0x41, 0x89, 0xc3, //0x000007fb movl         %eax, %r11d
	//0x000007fe LBB0_99
	0x8d, 0x42, 0xff, //0x000007fe leal         $-1(%rdx), %eax
	0x21, 0xd0, //0x00000801 andl         %edx, %eax
	0x0f, 0x85, 0x68, 0x10, 0x00, 0x00, //0x00000803 jne          LBB0_346
	0x41, 0x8d, 0x45, 0xff, //0x00000809 leal         $-1(%r13), %eax
	0x44, 0x21, 0xe8, //0x0000080d andl         %r13d, %eax
	0x0f, 0x85, 0x5b, 0x10, 0x00, 0x00, //0x00000810 jne          LBB0_346
	0x41, 0x8d, 0x43, 0xff, //0x00000816 leal         $-1(%r11), %eax
	0x44, 0x21, 0xd8, //0x0000081a andl         %r11d, %eax
	0x0f, 0x85, 0x4e, 0x10, 0x00, 0x00, //0x0000081d jne          LBB0_346
	0x85, 0xd2, //0x00000823 testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000825 je           LBB0_105
	0x0f, 0xbc, 0xc2, //0x0000082b bsfl         %edx, %eax
	0x49, 0x83, 0xfe, 0xff, //0x0000082e cmpq         $-1, %r14
	0x0f, 0x85, 0x81, 0x10, 0x00, 0x00, //0x00000832 jne          LBB0_351
	0x4c, 0x01, 0xc8, //0x00000838 addq         %r9, %rax
	0x49, 0x89, 0xc6, //0x0000083b movq         %rax, %r14
	//0x0000083e LBB0_105
	0x45, 0x85, 0xed, //0x0000083e testl        %r13d, %r13d
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00000841 je           LBB0_108
	0x41, 0x0f, 0xbc, 0xc5, //0x00000847 bsfl         %r13d, %eax
	0x49, 0x83, 0xfc, 0xff, //0x0000084b cmpq         $-1, %r12
	0x0f, 0x85, 0x64, 0x10, 0x00, 0x00, //0x0000084f jne          LBB0_351
	0x4c, 0x01, 0xc8, //0x00000855 addq         %r9, %rax
	0x49, 0x89, 0xc4, //0x00000858 movq         %rax, %r12
	//0x0000085b LBB0_108
	0x45, 0x85, 0xdb, //0x0000085b testl        %r11d, %r11d
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000085e je           LBB0_111
	0x41, 0x0f, 0xbc, 0xc3, //0x00000864 bsfl         %r11d, %eax
	0x49, 0x83, 0xff, 0xff, //0x00000868 cmpq         $-1, %r15
	0x0f, 0x85, 0x47, 0x10, 0x00, 0x00, //0x0000086c jne          LBB0_351
	0x4c, 0x01, 0xc8, //0x00000872 addq         %r9, %rax
	0x49, 0x89, 0xc7, //0x00000875 movq         %rax, %r15
	//0x00000878 LBB0_111
	0x4c, 0x8b, 0x6d, 0xd0, //0x00000878 movq         $-48(%rbp), %r13
	0x83, 0xf9, 0x10, //0x0000087c cmpl         $16, %ecx
	0x0f, 0x85, 0xa7, 0x04, 0x00, 0x00, //0x0000087f jne          LBB0_172
	0x49, 0x83, 0xc2, 0xf0, //0x00000885 addq         $-16, %r10
	0x49, 0x83, 0xc1, 0x10, //0x00000889 addq         $16, %r9
	0x49, 0x83, 0xfa, 0x0f, //0x0000088d cmpq         $15, %r10
	0x0f, 0x87, 0xd9, 0xfe, 0xff, 0xff, //0x00000891 ja           LBB0_97
	0x4a, 0x8d, 0x0c, 0x0e, //0x00000897 leaq         (%rsi,%r9), %rcx
	0x4d, 0x39, 0xc8, //0x0000089b cmpq         %r9, %r8
	0x49, 0x89, 0xc9, //0x0000089e movq         %rcx, %r9
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000008a1 movabsq      $4294977024, %r11
	0x0f, 0x84, 0x95, 0x06, 0x00, 0x00, //0x000008ab je           LBB0_201
	//0x000008b1 LBB0_114
	0x48, 0x89, 0xf0, //0x000008b1 movq         %rsi, %rax
	0x4c, 0x89, 0xee, //0x000008b4 movq         %r13, %rsi
	0x4e, 0x8d, 0x0c, 0x11, //0x000008b7 leaq         (%rcx,%r10), %r9
	0x49, 0x89, 0xcd, //0x000008bb movq         %rcx, %r13
	0x49, 0x29, 0xc5, //0x000008be subq         %rax, %r13
	0x31, 0xff, //0x000008c1 xorl         %edi, %edi
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x000008c3 jmp          LBB0_119
	//0x000008c8 LBB0_115
	0x83, 0xf8, 0x65, //0x000008c8 cmpl         $101, %eax
	0x0f, 0x85, 0x68, 0x06, 0x00, 0x00, //0x000008cb jne          LBB0_199
	//0x000008d1 LBB0_116
	0x49, 0x83, 0xfc, 0xff, //0x000008d1 cmpq         $-1, %r12
	0x0f, 0x85, 0xc1, 0x0f, 0x00, 0x00, //0x000008d5 jne          LBB0_350
	0x4e, 0x8d, 0x24, 0x2f, //0x000008db leaq         (%rdi,%r13), %r12
	0x90, //0x000008df .p2align 4, 0x90
	//0x000008e0 LBB0_118
	0x48, 0x83, 0xc7, 0x01, //0x000008e0 addq         $1, %rdi
	0x49, 0x39, 0xfa, //0x000008e4 cmpq         %rdi, %r10
	0x0f, 0x84, 0x52, 0x06, 0x00, 0x00, //0x000008e7 je           LBB0_200
	//0x000008ed LBB0_119
	0x0f, 0xbe, 0x04, 0x39, //0x000008ed movsbl       (%rcx,%rdi), %eax
	0x8d, 0x58, 0xd0, //0x000008f1 leal         $-48(%rax), %ebx
	0x83, 0xfb, 0x0a, //0x000008f4 cmpl         $10, %ebx
	0x0f, 0x82, 0xe3, 0xff, 0xff, 0xff, //0x000008f7 jb           LBB0_118
	0x8d, 0x58, 0xd5, //0x000008fd leal         $-43(%rax), %ebx
	0x83, 0xfb, 0x1a, //0x00000900 cmpl         $26, %ebx
	0x0f, 0x87, 0xbf, 0xff, 0xff, 0xff, //0x00000903 ja           LBB0_115
	0x48, 0x8d, 0x15, 0x24, 0x31, 0x00, 0x00, //0x00000909 leaq         $12580(%rip), %rdx  /* LJTI0_4+0(%rip) */
	0x48, 0x63, 0x04, 0x9a, //0x00000910 movslq       (%rdx,%rbx,4), %rax
	0x48, 0x01, 0xd0, //0x00000914 addq         %rdx, %rax
	0xff, 0xe0, //0x00000917 jmpq         *%rax
	//0x00000919 LBB0_122
	0x49, 0x83, 0xff, 0xff, //0x00000919 cmpq         $-1, %r15
	0x0f, 0x85, 0x79, 0x0f, 0x00, 0x00, //0x0000091d jne          LBB0_350
	0x4e, 0x8d, 0x3c, 0x2f, //0x00000923 leaq         (%rdi,%r13), %r15
	0xe9, 0xb4, 0xff, 0xff, 0xff, //0x00000927 jmp          LBB0_118
	//0x0000092c LBB0_124
	0x49, 0x83, 0xfe, 0xff, //0x0000092c cmpq         $-1, %r14
	0x0f, 0x85, 0x66, 0x0f, 0x00, 0x00, //0x00000930 jne          LBB0_350
	0x4e, 0x8d, 0x34, 0x2f, //0x00000936 leaq         (%rdi,%r13), %r14
	0xe9, 0xa1, 0xff, 0xff, 0xff, //0x0000093a jmp          LBB0_118
	//0x0000093f LBB0_88
	0x41, 0x83, 0xff, 0x22, //0x0000093f cmpl         $34, %r15d
	0x0f, 0x84, 0x43, 0x00, 0x00, 0x00, //0x00000943 je           LBB0_130
	//0x00000949 LBB0_86
	0x41, 0x83, 0xff, 0x7d, //0x00000949 cmpl         $125, %r15d
	0x0f, 0x85, 0xd1, 0x29, 0x00, 0x00, //0x0000094d jne          LBB0_567
	//0x00000953 LBB0_59
	0x4c, 0x89, 0x12, //0x00000953 movq         %r10, (%rdx)
	0x4d, 0x89, 0xd0, //0x00000956 movq         %r10, %r8
	0x4c, 0x89, 0xf0, //0x00000959 movq         %r14, %rax
	0x4d, 0x85, 0xd2, //0x0000095c testq        %r10, %r10
	0x0f, 0x85, 0x9d, 0xf9, 0xff, 0xff, //0x0000095f jne          LBB0_28
	0xe9, 0x72, 0x2a, 0x00, 0x00, //0x00000965 jmp          LBB0_582
	//0x0000096a LBB0_126
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x0000096a cmpq         $4095, %r8
	0x0f, 0x8f, 0x4e, 0x1f, 0x00, 0x00, //0x00000971 jg           LBB0_585
	0x49, 0x8d, 0x40, 0x01, //0x00000977 leaq         $1(%r8), %rax
	0x48, 0x89, 0x02, //0x0000097b movq         %rax, (%rdx)
	0x4a, 0xc7, 0x44, 0xc2, 0x08, 0x00, 0x00, 0x00, 0x00, //0x0000097e movq         $0, $8(%rdx,%r8,8)
	0xe9, 0x64, 0xf9, 0xff, 0xff, //0x00000987 jmp          LBB0_26
	//0x0000098c LBB0_130
	0x4c, 0x89, 0x75, 0xa8, //0x0000098c movq         %r14, $-88(%rbp)
	0x4a, 0xc7, 0x04, 0xc2, 0x02, 0x00, 0x00, 0x00, //0x00000990 movq         $2, (%rdx,%r8,8)
	0x48, 0x8b, 0x47, 0x08, //0x00000998 movq         $8(%rdi), %rax
	0xf6, 0x85, 0x70, 0xff, 0xff, 0xff, 0x20, //0x0000099c testb        $32, $-144(%rbp)
	0x48, 0x89, 0x45, 0x98, //0x000009a3 movq         %rax, $-104(%rbp)
	0x49, 0x89, 0xc6, //0x000009a7 movq         %rax, %r14
	0x0f, 0x85, 0x93, 0x03, 0x00, 0x00, //0x000009aa jne          LBB0_173
	0x4d, 0x29, 0xce, //0x000009b0 subq         %r9, %r14
	0x0f, 0x84, 0x5a, 0x2b, 0x00, 0x00, //0x000009b3 je           LBB0_599
	0x49, 0x83, 0xfe, 0x40, //0x000009b9 cmpq         $64, %r14
	0x0f, 0x82, 0x07, 0x17, 0x00, 0x00, //0x000009bd jb           LBB0_410
	0x49, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x000009c3 movq         $-2, %r15
	0x4c, 0x2b, 0x7d, 0xc0, //0x000009ca subq         $-64(%rbp), %r15
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x000009ce movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x000009d6 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000009d9 .p2align 4, 0x90
	//0x000009e0 LBB0_134
	0xf3, 0x43, 0x0f, 0x6f, 0x04, 0x0c, //0x000009e0 movdqu       (%r12,%r9), %xmm0
	0xf3, 0x43, 0x0f, 0x6f, 0x4c, 0x0c, 0x10, //0x000009e6 movdqu       $16(%r12,%r9), %xmm1
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x0c, 0x20, //0x000009ed movdqu       $32(%r12,%r9), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x0c, 0x30, //0x000009f4 movdqu       $48(%r12,%r9), %xmm3
	0x66, 0x0f, 0x6f, 0xe0, //0x000009fb movdqa       %xmm0, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x000009ff pcmpeqb      %xmm9, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xc4, //0x00000a04 pmovmskb     %xmm4, %r8d
	0x66, 0x0f, 0x6f, 0xe1, //0x00000a09 movdqa       %xmm1, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x00000a0d pcmpeqb      %xmm9, %xmm4
	0x66, 0x0f, 0xd7, 0xf4, //0x00000a12 pmovmskb     %xmm4, %esi
	0x66, 0x0f, 0x6f, 0xe2, //0x00000a16 movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x00000a1a pcmpeqb      %xmm9, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x00000a1f pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x6f, 0xe3, //0x00000a23 movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x00000a27 pcmpeqb      %xmm9, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x00000a2c pmovmskb     %xmm4, %edx
	0x66, 0x41, 0x0f, 0x74, 0xc2, //0x00000a30 pcmpeqb      %xmm10, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xd0, //0x00000a35 pmovmskb     %xmm0, %r10d
	0x66, 0x41, 0x0f, 0x74, 0xca, //0x00000a3a pcmpeqb      %xmm10, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x00000a3f pmovmskb     %xmm1, %ecx
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00000a43 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00000a48 pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x00000a4c pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x00000a51 pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe2, 0x30, //0x00000a55 shlq         $48, %rdx
	0x48, 0xc1, 0xe3, 0x20, //0x00000a59 shlq         $32, %rbx
	0x48, 0x09, 0xd3, //0x00000a5d orq          %rdx, %rbx
	0x48, 0xc1, 0xe6, 0x10, //0x00000a60 shlq         $16, %rsi
	0x48, 0x09, 0xde, //0x00000a64 orq          %rbx, %rsi
	0x49, 0x09, 0xf0, //0x00000a67 orq          %rsi, %r8
	0x48, 0xc1, 0xe7, 0x30, //0x00000a6a shlq         $48, %rdi
	0x48, 0xc1, 0xe0, 0x20, //0x00000a6e shlq         $32, %rax
	0x48, 0x09, 0xf8, //0x00000a72 orq          %rdi, %rax
	0x48, 0xc1, 0xe1, 0x10, //0x00000a75 shlq         $16, %rcx
	0x48, 0x09, 0xc1, //0x00000a79 orq          %rax, %rcx
	0x49, 0x09, 0xca, //0x00000a7c orq          %rcx, %r10
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00000a7f jne          LBB0_143
	0x4d, 0x85, 0xdb, //0x00000a85 testq        %r11, %r11
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000a88 jne          LBB0_145
	0x45, 0x31, 0xdb, //0x00000a8e xorl         %r11d, %r11d
	0x4d, 0x85, 0xc0, //0x00000a91 testq        %r8, %r8
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x00000a94 jne          LBB0_146
	//0x00000a9a LBB0_137
	0x49, 0x83, 0xc6, 0xc0, //0x00000a9a addq         $-64, %r14
	0x49, 0x83, 0xc7, 0xc0, //0x00000a9e addq         $-64, %r15
	0x49, 0x83, 0xc1, 0x40, //0x00000aa2 addq         $64, %r9
	0x49, 0x83, 0xfe, 0x3f, //0x00000aa6 cmpq         $63, %r14
	0x0f, 0x87, 0x30, 0xff, 0xff, 0xff, //0x00000aaa ja           LBB0_134
	0xe9, 0xde, 0x0f, 0x00, 0x00, //0x00000ab0 jmp          LBB0_138
	//0x00000ab5 LBB0_143
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00000ab5 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00000aba jne          LBB0_145
	0x49, 0x0f, 0xbc, 0xc2, //0x00000ac0 bsfq         %r10, %rax
	0x4c, 0x01, 0xc8, //0x00000ac4 addq         %r9, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00000ac7 movq         %rax, $-56(%rbp)
	//0x00000acb LBB0_145
	0x4c, 0x89, 0xd8, //0x00000acb movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x00000ace notq         %rax
	0x4c, 0x21, 0xd0, //0x00000ad1 andq         %r10, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00000ad4 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd9, //0x00000ad8 orq          %r11, %rcx
	0x48, 0x89, 0xca, //0x00000adb movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00000ade notq         %rdx
	0x4c, 0x21, 0xd2, //0x00000ae1 andq         %r10, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000ae4 movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x00000aee andq         %rsi, %rdx
	0x45, 0x31, 0xdb, //0x00000af1 xorl         %r11d, %r11d
	0x48, 0x01, 0xc2, //0x00000af4 addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc3, //0x00000af7 setb         %r11b
	0x48, 0x01, 0xd2, //0x00000afb addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000afe movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00000b08 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x00000b0b andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00000b0e notq         %rdx
	0x49, 0x21, 0xd0, //0x00000b11 andq         %rdx, %r8
	0x4d, 0x85, 0xc0, //0x00000b14 testq        %r8, %r8
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00000b17 je           LBB0_137
	//0x00000b1d LBB0_146
	0x4d, 0x0f, 0xbc, 0xc8, //0x00000b1d bsfq         %r8, %r9
	0x4d, 0x29, 0xf9, //0x00000b21 subq         %r15, %r9
	0x4c, 0x8b, 0x6d, 0xd0, //0x00000b24 movq         $-48(%rbp), %r13
	0x48, 0x8b, 0x7d, 0xb0, //0x00000b28 movq         $-80(%rbp), %rdi
	0x48, 0x8b, 0x55, 0xa0, //0x00000b2c movq         $-96(%rbp), %rdx
	0x4c, 0x8b, 0x75, 0xa8, //0x00000b30 movq         $-88(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000b34 movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x00000b3e movq         $-64(%rbp), %rbx
	0xe9, 0x19, 0x05, 0x00, 0x00, //0x00000b42 jmp          LBB0_222
	//0x00000b47 LBB0_147
	0x4d, 0x29, 0xce, //0x00000b47 subq         %r9, %r14
	0x48, 0x8b, 0x5d, 0xc0, //0x00000b4a movq         $-64(%rbp), %rbx
	0x0f, 0x84, 0xbf, 0x29, 0x00, 0x00, //0x00000b4e je           LBB0_599
	0x49, 0x83, 0xfe, 0x40, //0x00000b54 cmpq         $64, %r14
	0x0f, 0x82, 0x27, 0x15, 0x00, 0x00, //0x00000b58 jb           LBB0_406
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00000b5e movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x00000b66 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000b69 .p2align 4, 0x90
	//0x00000b70 LBB0_150
	0xf3, 0x43, 0x0f, 0x6f, 0x24, 0x0c, //0x00000b70 movdqu       (%r12,%r9), %xmm4
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x0c, 0x10, //0x00000b76 movdqu       $16(%r12,%r9), %xmm2
	0xf3, 0x47, 0x0f, 0x6f, 0x44, 0x0c, 0x20, //0x00000b7d movdqu       $32(%r12,%r9), %xmm8
	0xf3, 0x43, 0x0f, 0x6f, 0x74, 0x0c, 0x30, //0x00000b84 movdqu       $48(%r12,%r9), %xmm6
	0x66, 0x0f, 0x6f, 0xec, //0x00000b8b movdqa       %xmm4, %xmm5
	0x66, 0x41, 0x0f, 0x74, 0xe9, //0x00000b8f pcmpeqb      %xmm9, %xmm5
	0x66, 0x44, 0x0f, 0xd7, 0xfd, //0x00000b94 pmovmskb     %xmm5, %r15d
	0x66, 0x0f, 0x6f, 0xea, //0x00000b99 movdqa       %xmm2, %xmm5
	0x66, 0x0f, 0x6f, 0xda, //0x00000b9d movdqa       %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0xda, 0xdf, //0x00000ba1 pminub       %xmm15, %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x00000ba6 pcmpeqb      %xmm2, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xd1, //0x00000baa pcmpeqb      %xmm9, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00000baf pmovmskb     %xmm2, %esi
	0x66, 0x41, 0x0f, 0x6f, 0xd0, //0x00000bb3 movdqa       %xmm8, %xmm2
	0x66, 0x41, 0x0f, 0x6f, 0xf8, //0x00000bb8 movdqa       %xmm8, %xmm7
	0x66, 0x41, 0x0f, 0xda, 0xff, //0x00000bbd pminub       %xmm15, %xmm7
	0x66, 0x41, 0x0f, 0x74, 0xf8, //0x00000bc2 pcmpeqb      %xmm8, %xmm7
	0x66, 0x45, 0x0f, 0x74, 0xc1, //0x00000bc7 pcmpeqb      %xmm9, %xmm8
	0x66, 0x41, 0x0f, 0xd7, 0xd0, //0x00000bcc pmovmskb     %xmm8, %edx
	0x66, 0x0f, 0x6f, 0xc6, //0x00000bd1 movdqa       %xmm6, %xmm0
	0x66, 0x0f, 0x6f, 0xce, //0x00000bd5 movdqa       %xmm6, %xmm1
	0x66, 0x41, 0x0f, 0xda, 0xcf, //0x00000bd9 pminub       %xmm15, %xmm1
	0x66, 0x0f, 0x74, 0xce, //0x00000bde pcmpeqb      %xmm6, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xf1, //0x00000be2 pcmpeqb      %xmm9, %xmm6
	0x66, 0x0f, 0xd7, 0xc6, //0x00000be7 pmovmskb     %xmm6, %eax
	0x66, 0x0f, 0x6f, 0xf4, //0x00000beb movdqa       %xmm4, %xmm6
	0x66, 0x41, 0x0f, 0x74, 0xf2, //0x00000bef pcmpeqb      %xmm10, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xe6, //0x00000bf4 pmovmskb     %xmm6, %r12d
	0x66, 0x41, 0x0f, 0x74, 0xea, //0x00000bf9 pcmpeqb      %xmm10, %xmm5
	0x66, 0x0f, 0xd7, 0xcd, //0x00000bfe pmovmskb     %xmm5, %ecx
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00000c02 pcmpeqb      %xmm10, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xc2, //0x00000c07 pmovmskb     %xmm2, %r8d
	0x66, 0x41, 0x0f, 0x74, 0xc2, //0x00000c0c pcmpeqb      %xmm10, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xd0, //0x00000c11 pmovmskb     %xmm0, %r10d
	0x66, 0x0f, 0xd7, 0xfb, //0x00000c16 pmovmskb     %xmm3, %edi
	0x66, 0x0f, 0xd7, 0xdf, //0x00000c1a pmovmskb     %xmm7, %ebx
	0x66, 0x44, 0x0f, 0xd7, 0xe9, //0x00000c1e pmovmskb     %xmm1, %r13d
	0x48, 0xc1, 0xe0, 0x30, //0x00000c23 shlq         $48, %rax
	0x48, 0xc1, 0xe2, 0x20, //0x00000c27 shlq         $32, %rdx
	0x48, 0x09, 0xc2, //0x00000c2b orq          %rax, %rdx
	0x48, 0xc1, 0xe6, 0x10, //0x00000c2e shlq         $16, %rsi
	0x48, 0x09, 0xd6, //0x00000c32 orq          %rdx, %rsi
	0x49, 0x09, 0xf7, //0x00000c35 orq          %rsi, %r15
	0x49, 0xc1, 0xe2, 0x30, //0x00000c38 shlq         $48, %r10
	0x49, 0xc1, 0xe0, 0x20, //0x00000c3c shlq         $32, %r8
	0x4d, 0x09, 0xd0, //0x00000c40 orq          %r10, %r8
	0x48, 0xc1, 0xe1, 0x10, //0x00000c43 shlq         $16, %rcx
	0x4c, 0x09, 0xc1, //0x00000c47 orq          %r8, %rcx
	0x49, 0xc1, 0xe5, 0x30, //0x00000c4a shlq         $48, %r13
	0x48, 0xc1, 0xe3, 0x20, //0x00000c4e shlq         $32, %rbx
	0x4c, 0x09, 0xeb, //0x00000c52 orq          %r13, %rbx
	0x48, 0xc1, 0xe7, 0x10, //0x00000c55 shlq         $16, %rdi
	0x48, 0x09, 0xdf, //0x00000c59 orq          %rbx, %rdi
	0x49, 0x09, 0xcc, //0x00000c5c orq          %rcx, %r12
	0x0f, 0x85, 0x4d, 0x00, 0x00, 0x00, //0x00000c5f jne          LBB0_167
	0x4d, 0x85, 0xdb, //0x00000c65 testq        %r11, %r11
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x00000c68 jne          LBB0_169
	0x45, 0x31, 0xdb, //0x00000c6e xorl         %r11d, %r11d
	//0x00000c71 LBB0_153
	0x66, 0x0f, 0x6f, 0xc4, //0x00000c71 movdqa       %xmm4, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc7, //0x00000c75 pminub       %xmm15, %xmm0
	0x66, 0x0f, 0x74, 0xc4, //0x00000c7a pcmpeqb      %xmm4, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00000c7e pmovmskb     %xmm0, %eax
	0x48, 0x09, 0xc7, //0x00000c82 orq          %rax, %rdi
	0x4d, 0x85, 0xff, //0x00000c85 testq        %r15, %r15
	0x4c, 0x8b, 0x65, 0xb8, //0x00000c88 movq         $-72(%rbp), %r12
	0x0f, 0x85, 0x84, 0x00, 0x00, 0x00, //0x00000c8c jne          LBB0_170
	0x48, 0x85, 0xff, //0x00000c92 testq        %rdi, %rdi
	0x0f, 0x85, 0xf4, 0x26, 0x00, 0x00, //0x00000c95 jne          LBB0_573
	0x49, 0x83, 0xc6, 0xc0, //0x00000c9b addq         $-64, %r14
	0x49, 0x83, 0xc1, 0x40, //0x00000c9f addq         $64, %r9
	0x49, 0x83, 0xfe, 0x3f, //0x00000ca3 cmpq         $63, %r14
	0x0f, 0x87, 0xc3, 0xfe, 0xff, 0xff, //0x00000ca7 ja           LBB0_150
	0xe9, 0xbc, 0x0c, 0x00, 0x00, //0x00000cad jmp          LBB0_156
	//0x00000cb2 LBB0_167
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00000cb2 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00000cb7 jne          LBB0_169
	0x49, 0x0f, 0xbc, 0xc4, //0x00000cbd bsfq         %r12, %rax
	0x4c, 0x01, 0xc8, //0x00000cc1 addq         %r9, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00000cc4 movq         %rax, $-56(%rbp)
	//0x00000cc8 LBB0_169
	0x4c, 0x89, 0xd8, //0x00000cc8 movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x00000ccb notq         %rax
	0x4c, 0x21, 0xe0, //0x00000cce andq         %r12, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00000cd1 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd9, //0x00000cd5 orq          %r11, %rcx
	0x48, 0x89, 0xca, //0x00000cd8 movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00000cdb notq         %rdx
	0x4c, 0x21, 0xe2, //0x00000cde andq         %r12, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000ce1 movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x00000ceb andq         %rsi, %rdx
	0x45, 0x31, 0xdb, //0x00000cee xorl         %r11d, %r11d
	0x48, 0x01, 0xc2, //0x00000cf1 addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc3, //0x00000cf4 setb         %r11b
	0x48, 0x01, 0xd2, //0x00000cf8 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000cfb movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00000d05 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x00000d08 andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00000d0b notq         %rdx
	0x49, 0x21, 0xd7, //0x00000d0e andq         %rdx, %r15
	0xe9, 0x5b, 0xff, 0xff, 0xff, //0x00000d11 jmp          LBB0_153
	//0x00000d16 LBB0_170
	0x49, 0x0f, 0xbc, 0xc7, //0x00000d16 bsfq         %r15, %rax
	0x48, 0x85, 0xff, //0x00000d1a testq        %rdi, %rdi
	0x0f, 0x84, 0x90, 0x02, 0x00, 0x00, //0x00000d1d je           LBB0_211
	0x48, 0x0f, 0xbc, 0xcf, //0x00000d23 bsfq         %rdi, %rcx
	0xe9, 0x8c, 0x02, 0x00, 0x00, //0x00000d27 jmp          LBB0_212
	//0x00000d2c LBB0_172
	0x89, 0xc8, //0x00000d2c movl         %ecx, %eax
	0x48, 0x01, 0xf0, //0x00000d2e addq         %rsi, %rax
	0x49, 0x01, 0xc1, //0x00000d31 addq         %rax, %r9
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000d34 movabsq      $4294977024, %r11
	0xe9, 0x03, 0x02, 0x00, 0x00, //0x00000d3e jmp          LBB0_201
	//0x00000d43 LBB0_173
	0x4d, 0x29, 0xce, //0x00000d43 subq         %r9, %r14
	0x0f, 0x84, 0xc7, 0x27, 0x00, 0x00, //0x00000d46 je           LBB0_599
	0x49, 0x83, 0xfe, 0x40, //0x00000d4c cmpq         $64, %r14
	0x0f, 0x82, 0x95, 0x13, 0x00, 0x00, //0x00000d50 jb           LBB0_411
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00000d56 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x00000d5e xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000d61 .p2align 4, 0x90
	//0x00000d70 LBB0_176
	0xf3, 0x43, 0x0f, 0x6f, 0x2c, 0x0c, //0x00000d70 movdqu       (%r12,%r9), %xmm5
	0xf3, 0x43, 0x0f, 0x6f, 0x44, 0x0c, 0x10, //0x00000d76 movdqu       $16(%r12,%r9), %xmm0
	0xf3, 0x43, 0x0f, 0x6f, 0x7c, 0x0c, 0x20, //0x00000d7d movdqu       $32(%r12,%r9), %xmm7
	0xf3, 0x43, 0x0f, 0x6f, 0x74, 0x0c, 0x30, //0x00000d84 movdqu       $48(%r12,%r9), %xmm6
	0x66, 0x0f, 0x6f, 0xcd, //0x00000d8b movdqa       %xmm5, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xc9, //0x00000d8f pcmpeqb      %xmm9, %xmm1
	0x66, 0x44, 0x0f, 0xd7, 0xd1, //0x00000d94 pmovmskb     %xmm1, %r10d
	0x66, 0x0f, 0x6f, 0xc8, //0x00000d99 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00000d9d movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0xda, 0xd7, //0x00000da1 pminub       %xmm15, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00000da6 pcmpeqb      %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xc1, //0x00000daa pcmpeqb      %xmm9, %xmm0
	0x66, 0x0f, 0xd7, 0xf0, //0x00000daf pmovmskb     %xmm0, %esi
	0x66, 0x0f, 0x6f, 0xc7, //0x00000db3 movdqa       %xmm7, %xmm0
	0x66, 0x0f, 0x6f, 0xdf, //0x00000db7 movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0xda, 0xdf, //0x00000dbb pminub       %xmm15, %xmm3
	0x66, 0x0f, 0x74, 0xdf, //0x00000dc0 pcmpeqb      %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xf9, //0x00000dc4 pcmpeqb      %xmm9, %xmm7
	0x66, 0x0f, 0xd7, 0xd7, //0x00000dc9 pmovmskb     %xmm7, %edx
	0x66, 0x0f, 0x6f, 0xe6, //0x00000dcd movdqa       %xmm6, %xmm4
	0x66, 0x0f, 0x6f, 0xfe, //0x00000dd1 movdqa       %xmm6, %xmm7
	0x66, 0x41, 0x0f, 0xda, 0xff, //0x00000dd5 pminub       %xmm15, %xmm7
	0x66, 0x0f, 0x74, 0xfe, //0x00000dda pcmpeqb      %xmm6, %xmm7
	0x66, 0x41, 0x0f, 0x74, 0xf1, //0x00000dde pcmpeqb      %xmm9, %xmm6
	0x66, 0x0f, 0xd7, 0xc6, //0x00000de3 pmovmskb     %xmm6, %eax
	0x66, 0x0f, 0x6f, 0xf5, //0x00000de7 movdqa       %xmm5, %xmm6
	0x66, 0x41, 0x0f, 0x74, 0xf2, //0x00000deb pcmpeqb      %xmm10, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xfe, //0x00000df0 pmovmskb     %xmm6, %r15d
	0x66, 0x41, 0x0f, 0x74, 0xca, //0x00000df5 pcmpeqb      %xmm10, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x00000dfa pmovmskb     %xmm1, %ecx
	0x66, 0x41, 0x0f, 0x74, 0xc2, //0x00000dfe pcmpeqb      %xmm10, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00000e03 pmovmskb     %xmm0, %edi
	0x66, 0x41, 0x0f, 0x74, 0xe2, //0x00000e07 pcmpeqb      %xmm10, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xc4, //0x00000e0c pmovmskb     %xmm4, %r8d
	0x66, 0x44, 0x0f, 0xd7, 0xea, //0x00000e11 pmovmskb     %xmm2, %r13d
	0x66, 0x0f, 0xd7, 0xdb, //0x00000e16 pmovmskb     %xmm3, %ebx
	0x66, 0x44, 0x0f, 0xd7, 0xe7, //0x00000e1a pmovmskb     %xmm7, %r12d
	0x48, 0xc1, 0xe0, 0x30, //0x00000e1f shlq         $48, %rax
	0x48, 0xc1, 0xe2, 0x20, //0x00000e23 shlq         $32, %rdx
	0x48, 0x09, 0xc2, //0x00000e27 orq          %rax, %rdx
	0x48, 0xc1, 0xe6, 0x10, //0x00000e2a shlq         $16, %rsi
	0x48, 0x09, 0xd6, //0x00000e2e orq          %rdx, %rsi
	0x49, 0x09, 0xf2, //0x00000e31 orq          %rsi, %r10
	0x49, 0xc1, 0xe0, 0x30, //0x00000e34 shlq         $48, %r8
	0x48, 0xc1, 0xe7, 0x20, //0x00000e38 shlq         $32, %rdi
	0x4c, 0x09, 0xc7, //0x00000e3c orq          %r8, %rdi
	0x48, 0xc1, 0xe1, 0x10, //0x00000e3f shlq         $16, %rcx
	0x48, 0x09, 0xf9, //0x00000e43 orq          %rdi, %rcx
	0x49, 0xc1, 0xe4, 0x30, //0x00000e46 shlq         $48, %r12
	0x48, 0xc1, 0xe3, 0x20, //0x00000e4a shlq         $32, %rbx
	0x4c, 0x09, 0xe3, //0x00000e4e orq          %r12, %rbx
	0x49, 0xc1, 0xe5, 0x10, //0x00000e51 shlq         $16, %r13
	0x49, 0x09, 0xdd, //0x00000e55 orq          %rbx, %r13
	0x49, 0x09, 0xcf, //0x00000e58 orq          %rcx, %r15
	0x0f, 0x85, 0x51, 0x00, 0x00, 0x00, //0x00000e5b jne          LBB0_193
	0x4d, 0x85, 0xdb, //0x00000e61 testq        %r11, %r11
	0x0f, 0x85, 0x67, 0x00, 0x00, 0x00, //0x00000e64 jne          LBB0_195
	0x45, 0x31, 0xdb, //0x00000e6a xorl         %r11d, %r11d
	0x4c, 0x8b, 0x65, 0xb8, //0x00000e6d movq         $-72(%rbp), %r12
	//0x00000e71 LBB0_179
	0x66, 0x0f, 0x6f, 0xc5, //0x00000e71 movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc7, //0x00000e75 pminub       %xmm15, %xmm0
	0x66, 0x0f, 0x74, 0xc5, //0x00000e7a pcmpeqb      %xmm5, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00000e7e pmovmskb     %xmm0, %eax
	0x49, 0x09, 0xc5, //0x00000e82 orq          %rax, %r13
	0x4d, 0x85, 0xd2, //0x00000e85 testq        %r10, %r10
	0x48, 0x8b, 0x5d, 0xc0, //0x00000e88 movq         $-64(%rbp), %rbx
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x00000e8c jne          LBB0_197
	0x4d, 0x85, 0xed, //0x00000e92 testq        %r13, %r13
	0x0f, 0x85, 0x1a, 0x25, 0x00, 0x00, //0x00000e95 jne          LBB0_576
	0x49, 0x83, 0xc6, 0xc0, //0x00000e9b addq         $-64, %r14
	0x49, 0x83, 0xc1, 0x40, //0x00000e9f addq         $64, %r9
	0x49, 0x83, 0xfe, 0x3f, //0x00000ea3 cmpq         $63, %r14
	0x0f, 0x87, 0xc3, 0xfe, 0xff, 0xff, //0x00000ea7 ja           LBB0_176
	0xe9, 0x5b, 0x0c, 0x00, 0x00, //0x00000ead jmp          LBB0_182
	//0x00000eb2 LBB0_193
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00000eb2 cmpq         $-1, $-56(%rbp)
	0x4c, 0x8b, 0x65, 0xb8, //0x00000eb7 movq         $-72(%rbp), %r12
	0x0f, 0x85, 0x14, 0x00, 0x00, 0x00, //0x00000ebb jne          LBB0_196
	0x49, 0x0f, 0xbc, 0xc7, //0x00000ec1 bsfq         %r15, %rax
	0x4c, 0x01, 0xc8, //0x00000ec5 addq         %r9, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00000ec8 movq         %rax, $-56(%rbp)
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00000ecc jmp          LBB0_196
	//0x00000ed1 LBB0_195
	0x4c, 0x8b, 0x65, 0xb8, //0x00000ed1 movq         $-72(%rbp), %r12
	//0x00000ed5 LBB0_196
	0x4c, 0x89, 0xd8, //0x00000ed5 movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x00000ed8 notq         %rax
	0x4c, 0x21, 0xf8, //0x00000edb andq         %r15, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00000ede leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd9, //0x00000ee2 orq          %r11, %rcx
	0x48, 0x89, 0xca, //0x00000ee5 movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00000ee8 notq         %rdx
	0x4c, 0x21, 0xfa, //0x00000eeb andq         %r15, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000eee movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x00000ef8 andq         %rsi, %rdx
	0x45, 0x31, 0xdb, //0x00000efb xorl         %r11d, %r11d
	0x48, 0x01, 0xc2, //0x00000efe addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc3, //0x00000f01 setb         %r11b
	0x48, 0x01, 0xd2, //0x00000f05 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000f08 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00000f12 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x00000f15 andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00000f18 notq         %rdx
	0x49, 0x21, 0xd2, //0x00000f1b andq         %rdx, %r10
	0xe9, 0x4e, 0xff, 0xff, 0xff, //0x00000f1e jmp          LBB0_179
	//0x00000f23 LBB0_197
	0x49, 0x0f, 0xbc, 0xc2, //0x00000f23 bsfq         %r10, %rax
	0x4d, 0x85, 0xed, //0x00000f27 testq        %r13, %r13
	0x0f, 0x84, 0x01, 0x01, 0x00, 0x00, //0x00000f2a je           LBB0_219
	0x49, 0x0f, 0xbc, 0xcd, //0x00000f30 bsfq         %r13, %rcx
	0xe9, 0xfd, 0x00, 0x00, 0x00, //0x00000f34 jmp          LBB0_220
	//0x00000f39 LBB0_199
	0x48, 0x01, 0xf9, //0x00000f39 addq         %rdi, %rcx
	0x49, 0x89, 0xc9, //0x00000f3c movq         %rcx, %r9
	//0x00000f3f LBB0_200
	0x49, 0x89, 0xf5, //0x00000f3f movq         %rsi, %r13
	0x48, 0x8b, 0x75, 0xc8, //0x00000f42 movq         $-56(%rbp), %rsi
	//0x00000f46 LBB0_201
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00000f46 movq         $-1, %rcx
	0x4d, 0x85, 0xf6, //0x00000f4d testq        %r14, %r14
	0x0f, 0x84, 0xc0, 0x23, 0x00, 0x00, //0x00000f50 je           LBB0_566
	0x4d, 0x85, 0xff, //0x00000f56 testq        %r15, %r15
	0x0f, 0x84, 0xb7, 0x23, 0x00, 0x00, //0x00000f59 je           LBB0_566
	0x4d, 0x85, 0xe4, //0x00000f5f testq        %r12, %r12
	0x0f, 0x84, 0xae, 0x23, 0x00, 0x00, //0x00000f62 je           LBB0_566
	0x49, 0x29, 0xf1, //0x00000f68 subq         %rsi, %r9
	0x49, 0x8d, 0x41, 0xff, //0x00000f6b leaq         $-1(%r9), %rax
	0x49, 0x39, 0xc6, //0x00000f6f cmpq         %rax, %r14
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00000f72 je           LBB0_210
	0x49, 0x39, 0xc7, //0x00000f78 cmpq         %rax, %r15
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x00000f7b je           LBB0_210
	0x49, 0x39, 0xc4, //0x00000f81 cmpq         %rax, %r12
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00000f84 je           LBB0_210
	0x4d, 0x85, 0xff, //0x00000f8a testq        %r15, %r15
	0x0f, 0x8e, 0x7b, 0x00, 0x00, 0x00, //0x00000f8d jle          LBB0_216
	0x49, 0x8d, 0x47, 0xff, //0x00000f93 leaq         $-1(%r15), %rax
	0x49, 0x39, 0xc4, //0x00000f97 cmpq         %rax, %r12
	0x0f, 0x84, 0x6e, 0x00, 0x00, 0x00, //0x00000f9a je           LBB0_216
	0x49, 0xf7, 0xd7, //0x00000fa0 notq         %r15
	0x4d, 0x89, 0xf9, //0x00000fa3 movq         %r15, %r9
	0xe9, 0x24, 0x09, 0x00, 0x00, //0x00000fa6 jmp          LBB0_353
	//0x00000fab LBB0_210
	0x49, 0xf7, 0xd9, //0x00000fab negq         %r9
	0xe9, 0x1c, 0x09, 0x00, 0x00, //0x00000fae jmp          LBB0_353
	//0x00000fb3 LBB0_211
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000fb3 movl         $64, %ecx
	//0x00000fb8 LBB0_212
	0x4c, 0x8b, 0x6d, 0xd0, //0x00000fb8 movq         $-48(%rbp), %r13
	0x48, 0x8b, 0x7d, 0xb0, //0x00000fbc movq         $-80(%rbp), %rdi
	0x48, 0x8b, 0x55, 0xa0, //0x00000fc0 movq         $-96(%rbp), %rdx
	0x4c, 0x8b, 0x75, 0xa8, //0x00000fc4 movq         $-88(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000fc8 movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x00000fd2 movq         $-64(%rbp), %rbx
	0x48, 0x39, 0xc1, //0x00000fd6 cmpq         %rax, %rcx
	0x0f, 0x82, 0x4c, 0x25, 0x00, 0x00, //0x00000fd9 jb           LBB0_345
	//0x00000fdf LBB0_213
	0x49, 0x01, 0xc1, //0x00000fdf addq         %rax, %r9
	0x49, 0x83, 0xc1, 0x01, //0x00000fe2 addq         $1, %r9
	//0x00000fe6 LBB0_214
	0x4d, 0x85, 0xc9, //0x00000fe6 testq        %r9, %r9
	0x0f, 0x88, 0xe2, 0x18, 0x00, 0x00, //0x00000fe9 js           LBB0_497
	//0x00000fef LBB0_215
	0x4d, 0x89, 0x4d, 0x00, //0x00000fef movq         %r9, (%r13)
	0x48, 0x89, 0xd8, //0x00000ff3 movq         %rbx, %rax
	0x48, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00000ff6 movabsq      $9223372036854775806, %rcx
	0x48, 0x39, 0xcb, //0x00001000 cmpq         %rcx, %rbx
	0x0f, 0x86, 0xe7, 0xf2, 0xff, 0xff, //0x00001003 jbe          LBB0_26
	0xe9, 0xce, 0x23, 0x00, 0x00, //0x00001009 jmp          LBB0_582
	//0x0000100e LBB0_216
	0x4c, 0x89, 0xf0, //0x0000100e movq         %r14, %rax
	0x4c, 0x09, 0xe0, //0x00001011 orq          %r12, %rax
	0x0f, 0x99, 0xc1, //0x00001014 setns        %cl
	0x0f, 0x88, 0x3e, 0x05, 0x00, 0x00, //0x00001017 js           LBB0_302
	0x4d, 0x39, 0xe6, //0x0000101d cmpq         %r12, %r14
	0x0f, 0x8c, 0x35, 0x05, 0x00, 0x00, //0x00001020 jl           LBB0_302
	0x49, 0xf7, 0xd6, //0x00001026 notq         %r14
	0x4d, 0x89, 0xf1, //0x00001029 movq         %r14, %r9
	0xe9, 0x9e, 0x08, 0x00, 0x00, //0x0000102c jmp          LBB0_353
	//0x00001031 LBB0_219
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001031 movl         $64, %ecx
	//0x00001036 LBB0_220
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001036 movq         $-48(%rbp), %r13
	0x48, 0x8b, 0x7d, 0xb0, //0x0000103a movq         $-80(%rbp), %rdi
	0x48, 0x8b, 0x55, 0xa0, //0x0000103e movq         $-96(%rbp), %rdx
	0x4c, 0x8b, 0x75, 0xa8, //0x00001042 movq         $-88(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001046 movabsq      $4294977024, %r11
	0x48, 0x39, 0xc1, //0x00001050 cmpq         %rax, %rcx
	0x0f, 0x82, 0xd2, 0x24, 0x00, 0x00, //0x00001053 jb           LBB0_345
	0x49, 0x01, 0xc1, //0x00001059 addq         %rax, %r9
	0x49, 0x83, 0xc1, 0x01, //0x0000105c addq         $1, %r9
	//0x00001060 LBB0_222
	0x4d, 0x85, 0xc9, //0x00001060 testq        %r9, %r9
	0x0f, 0x88, 0x68, 0x18, 0x00, 0x00, //0x00001063 js           LBB0_497
	0x4d, 0x89, 0x4d, 0x00, //0x00001069 movq         %r9, (%r13)
	0x48, 0x89, 0xd8, //0x0000106d movq         %rbx, %rax
	0x48, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00001070 movabsq      $9223372036854775806, %rcx
	0x48, 0x39, 0xcb, //0x0000107a cmpq         %rcx, %rbx
	0x0f, 0x87, 0x59, 0x23, 0x00, 0x00, //0x0000107d ja           LBB0_582
	0x48, 0x8b, 0x02, //0x00001083 movq         (%rdx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001086 cmpq         $4095, %rax
	0x0f, 0x8f, 0x33, 0x18, 0x00, 0x00, //0x0000108c jg           LBB0_585
	0x48, 0x8d, 0x48, 0x01, //0x00001092 leaq         $1(%rax), %rcx
	0x48, 0x89, 0x0a, //0x00001096 movq         %rcx, (%rdx)
	0x48, 0xc7, 0x44, 0xc2, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00001099 movq         $4, $8(%rdx,%rax,8)
	0xe9, 0x49, 0xf2, 0xff, 0xff, //0x000010a2 jmp          LBB0_26
	//0x000010a7 LBB0_226
	0x48, 0x8b, 0x02, //0x000010a7 movq         (%rdx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x000010aa cmpq         $4095, %rax
	0x0f, 0x8f, 0x0f, 0x18, 0x00, 0x00, //0x000010b0 jg           LBB0_585
	0x48, 0x8d, 0x48, 0x01, //0x000010b6 leaq         $1(%rax), %rcx
	0x48, 0x89, 0x0a, //0x000010ba movq         %rcx, (%rdx)
	0x48, 0xc7, 0x44, 0xc2, 0x08, 0x05, 0x00, 0x00, 0x00, //0x000010bd movq         $5, $8(%rdx,%rax,8)
	0xe9, 0x25, 0xf2, 0xff, 0xff, //0x000010c6 jmp          LBB0_26
	//0x000010cb LBB0_228
	0x48, 0x8b, 0x4f, 0x08, //0x000010cb movq         $8(%rdi), %rcx
	0x48, 0x8d, 0x59, 0xfc, //0x000010cf leaq         $-4(%rcx), %rbx
	0x48, 0x39, 0xde, //0x000010d3 cmpq         %rbx, %rsi
	0x0f, 0x83, 0xaa, 0x22, 0x00, 0x00, //0x000010d6 jae          LBB0_586
	0x43, 0x8b, 0x0c, 0x0c, //0x000010dc movl         (%r12,%r9), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x000010e0 cmpl         $1702063201, %ecx
	0x0f, 0x85, 0x1c, 0x23, 0x00, 0x00, //0x000010e6 jne          LBB0_587
	0x4c, 0x8d, 0x4e, 0x05, //0x000010ec leaq         $5(%rsi), %r9
	0x4d, 0x89, 0x4d, 0x00, //0x000010f0 movq         %r9, (%r13)
	0x48, 0x89, 0xf0, //0x000010f4 movq         %rsi, %rax
	0x48, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x000010f7 movabsq      $9223372036854775806, %rcx
	0x48, 0x39, 0xce, //0x00001101 cmpq         %rcx, %rsi
	0x0f, 0x86, 0xe6, 0xf1, 0xff, 0xff, //0x00001104 jbe          LBB0_26
	0xe9, 0xcd, 0x22, 0x00, 0x00, //0x0000110a jmp          LBB0_582
	//0x0000110f LBB0_231
	0x4c, 0x89, 0x75, 0xa8, //0x0000110f movq         %r14, $-88(%rbp)
	0x48, 0x8b, 0x47, 0x08, //0x00001113 movq         $8(%rdi), %rax
	0xf6, 0x85, 0x70, 0xff, 0xff, 0xff, 0x20, //0x00001117 testb        $32, $-144(%rbp)
	0x48, 0x89, 0x45, 0x98, //0x0000111e movq         %rax, $-104(%rbp)
	0x49, 0x89, 0xc6, //0x00001122 movq         %rax, %r14
	0x0f, 0x85, 0x4a, 0x04, 0x00, 0x00, //0x00001125 jne          LBB0_303
	0x4d, 0x29, 0xce, //0x0000112b subq         %r9, %r14
	0x0f, 0x84, 0xdf, 0x23, 0x00, 0x00, //0x0000112e je           LBB0_599
	0x49, 0x83, 0xfe, 0x40, //0x00001134 cmpq         $64, %r14
	0x0f, 0x82, 0x45, 0x10, 0x00, 0x00, //0x00001138 jb           LBB0_416
	0x49, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x0000113e movq         $-2, %r15
	0x4c, 0x2b, 0x7d, 0xc0, //0x00001145 subq         $-64(%rbp), %r15
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00001149 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x00001151 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001154 .p2align 4, 0x90
	//0x00001160 LBB0_235
	0xf3, 0x43, 0x0f, 0x6f, 0x04, 0x0c, //0x00001160 movdqu       (%r12,%r9), %xmm0
	0xf3, 0x43, 0x0f, 0x6f, 0x4c, 0x0c, 0x10, //0x00001166 movdqu       $16(%r12,%r9), %xmm1
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x0c, 0x20, //0x0000116d movdqu       $32(%r12,%r9), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x0c, 0x30, //0x00001174 movdqu       $48(%r12,%r9), %xmm3
	0x66, 0x0f, 0x6f, 0xe0, //0x0000117b movdqa       %xmm0, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x0000117f pcmpeqb      %xmm9, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xc4, //0x00001184 pmovmskb     %xmm4, %r8d
	0x66, 0x0f, 0x6f, 0xe1, //0x00001189 movdqa       %xmm1, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x0000118d pcmpeqb      %xmm9, %xmm4
	0x66, 0x0f, 0xd7, 0xf4, //0x00001192 pmovmskb     %xmm4, %esi
	0x66, 0x0f, 0x6f, 0xe2, //0x00001196 movdqa       %xmm2, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x0000119a pcmpeqb      %xmm9, %xmm4
	0x66, 0x0f, 0xd7, 0xdc, //0x0000119f pmovmskb     %xmm4, %ebx
	0x66, 0x0f, 0x6f, 0xe3, //0x000011a3 movdqa       %xmm3, %xmm4
	0x66, 0x41, 0x0f, 0x74, 0xe1, //0x000011a7 pcmpeqb      %xmm9, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x000011ac pmovmskb     %xmm4, %edx
	0x66, 0x41, 0x0f, 0x74, 0xc2, //0x000011b0 pcmpeqb      %xmm10, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xd0, //0x000011b5 pmovmskb     %xmm0, %r10d
	0x66, 0x41, 0x0f, 0x74, 0xca, //0x000011ba pcmpeqb      %xmm10, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x000011bf pmovmskb     %xmm1, %ecx
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x000011c3 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x000011c8 pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xda, //0x000011cc pcmpeqb      %xmm10, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x000011d1 pmovmskb     %xmm3, %edi
	0x48, 0xc1, 0xe2, 0x30, //0x000011d5 shlq         $48, %rdx
	0x48, 0xc1, 0xe3, 0x20, //0x000011d9 shlq         $32, %rbx
	0x48, 0x09, 0xd3, //0x000011dd orq          %rdx, %rbx
	0x48, 0xc1, 0xe6, 0x10, //0x000011e0 shlq         $16, %rsi
	0x48, 0x09, 0xde, //0x000011e4 orq          %rbx, %rsi
	0x49, 0x09, 0xf0, //0x000011e7 orq          %rsi, %r8
	0x48, 0xc1, 0xe7, 0x30, //0x000011ea shlq         $48, %rdi
	0x48, 0xc1, 0xe0, 0x20, //0x000011ee shlq         $32, %rax
	0x48, 0x09, 0xf8, //0x000011f2 orq          %rdi, %rax
	0x48, 0xc1, 0xe1, 0x10, //0x000011f5 shlq         $16, %rcx
	0x48, 0x09, 0xc1, //0x000011f9 orq          %rax, %rcx
	0x49, 0x09, 0xca, //0x000011fc orq          %rcx, %r10
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x000011ff jne          LBB0_252
	0x4d, 0x85, 0xdb, //0x00001205 testq        %r11, %r11
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00001208 jne          LBB0_254
	0x45, 0x31, 0xdb, //0x0000120e xorl         %r11d, %r11d
	0x4d, 0x85, 0xc0, //0x00001211 testq        %r8, %r8
	0x0f, 0x85, 0x43, 0xf4, 0xff, 0xff, //0x00001214 jne          LBB0_80
	//0x0000121a LBB0_238
	0x49, 0x83, 0xc6, 0xc0, //0x0000121a addq         $-64, %r14
	0x49, 0x83, 0xc7, 0xc0, //0x0000121e addq         $-64, %r15
	0x49, 0x83, 0xc1, 0x40, //0x00001222 addq         $64, %r9
	0x49, 0x83, 0xfe, 0x3f, //0x00001226 cmpq         $63, %r14
	0x0f, 0x87, 0x30, 0xff, 0xff, 0xff, //0x0000122a ja           LBB0_235
	0xe9, 0x92, 0x0b, 0x00, 0x00, //0x00001230 jmp          LBB0_239
	//0x00001235 LBB0_252
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00001235 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x0000123a jne          LBB0_254
	0x49, 0x0f, 0xbc, 0xc2, //0x00001240 bsfq         %r10, %rax
	0x4c, 0x01, 0xc8, //0x00001244 addq         %r9, %rax
	0x48, 0x89, 0x45, 0xc8, //0x00001247 movq         %rax, $-56(%rbp)
	//0x0000124b LBB0_254
	0x4c, 0x89, 0xd8, //0x0000124b movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x0000124e notq         %rax
	0x4c, 0x21, 0xd0, //0x00001251 andq         %r10, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x00001254 leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd9, //0x00001258 orq          %r11, %rcx
	0x48, 0x89, 0xca, //0x0000125b movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x0000125e notq         %rdx
	0x4c, 0x21, 0xd2, //0x00001261 andq         %r10, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001264 movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x0000126e andq         %rsi, %rdx
	0x45, 0x31, 0xdb, //0x00001271 xorl         %r11d, %r11d
	0x48, 0x01, 0xc2, //0x00001274 addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc3, //0x00001277 setb         %r11b
	0x48, 0x01, 0xd2, //0x0000127b addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000127e movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00001288 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x0000128b andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x0000128e notq         %rdx
	0x49, 0x21, 0xd0, //0x00001291 andq         %rdx, %r8
	0x4d, 0x85, 0xc0, //0x00001294 testq        %r8, %r8
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00001297 je           LBB0_238
	0xe9, 0xbb, 0xf3, 0xff, 0xff, //0x0000129d jmp          LBB0_80
	//0x000012a2 LBB0_255
	0x4c, 0x8b, 0x5f, 0x08, //0x000012a2 movq         $8(%rdi), %r11
	0x4d, 0x29, 0xcb, //0x000012a6 subq         %r9, %r11
	0x0f, 0x84, 0x3f, 0x21, 0x00, 0x00, //0x000012a9 je           LBB0_583
	0x4c, 0x89, 0x75, 0xa8, //0x000012af movq         %r14, $-88(%rbp)
	0x49, 0x89, 0xfa, //0x000012b3 movq         %rdi, %r10
	0x4b, 0x8d, 0x04, 0x0c, //0x000012b6 leaq         (%r12,%r9), %rax
	0x49, 0x89, 0xc7, //0x000012ba movq         %rax, %r15
	0x80, 0x38, 0x30, //0x000012bd cmpb         $48, (%rax)
	0x0f, 0x85, 0x35, 0x00, 0x00, 0x00, //0x000012c0 jne          LBB0_260
	0x41, 0xbe, 0x01, 0x00, 0x00, 0x00, //0x000012c6 movl         $1, %r14d
	0x49, 0x83, 0xfb, 0x01, //0x000012cc cmpq         $1, %r11
	0x0f, 0x84, 0x2b, 0x05, 0x00, 0x00, //0x000012d0 je           LBB0_342
	0x41, 0x8a, 0x47, 0x01, //0x000012d6 movb         $1(%r15), %al
	0x04, 0xd2, //0x000012da addb         $-46, %al
	0x3c, 0x37, //0x000012dc cmpb         $55, %al
	0x0f, 0x87, 0x1d, 0x05, 0x00, 0x00, //0x000012de ja           LBB0_342
	0x0f, 0xb6, 0xc0, //0x000012e4 movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000012e7 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x000012f1 btq          %rax, %rcx
	0x0f, 0x83, 0x06, 0x05, 0x00, 0x00, //0x000012f5 jae          LBB0_342
	//0x000012fb LBB0_260
	0x49, 0x83, 0xfb, 0x10, //0x000012fb cmpq         $16, %r11
	0x0f, 0x82, 0x5e, 0x0e, 0x00, 0x00, //0x000012ff jb           LBB0_415
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001305 movq         $-1, %r12
	0x45, 0x31, 0xf6, //0x0000130c xorl         %r14d, %r14d
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x0000130f movq         $-1, %r13
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001316 movq         $-1, %r8
	0x4c, 0x89, 0xde, //0x0000131d movq         %r11, %rsi
	//0x00001320 .p2align 4, 0x90
	//0x00001320 LBB0_262
	0xf3, 0x43, 0x0f, 0x6f, 0x04, 0x37, //0x00001320 movdqu       (%r15,%r14), %xmm0
	0x66, 0x0f, 0x6f, 0xc8, //0x00001326 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x74, 0x0d, 0x6e, 0xed, 0xff, 0xff, //0x0000132a pcmpeqb      $-4754(%rip), %xmm1  /* LCPI0_10+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd0, //0x00001332 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0x15, 0x72, 0xed, 0xff, 0xff, //0x00001336 pcmpeqb      $-4750(%rip), %xmm2  /* LCPI0_11+0(%rip) */
	0x66, 0x0f, 0xeb, 0xd1, //0x0000133e por          %xmm1, %xmm2
	0x66, 0x0f, 0x6f, 0xc8, //0x00001342 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0xfc, 0x0d, 0x72, 0xed, 0xff, 0xff, //0x00001346 paddb        $-4750(%rip), %xmm1  /* LCPI0_12+0(%rip) */
	0x66, 0x0f, 0x6f, 0xd9, //0x0000134e movdqa       %xmm1, %xmm3
	0x66, 0x41, 0x0f, 0xda, 0xdb, //0x00001352 pminub       %xmm11, %xmm3
	0x66, 0x0f, 0x74, 0xd9, //0x00001357 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0x6f, 0xc8, //0x0000135b movdqa       %xmm0, %xmm1
	0x66, 0x41, 0x0f, 0xdb, 0xcc, //0x0000135f pand         %xmm12, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xc5, //0x00001364 pcmpeqb      %xmm13, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xce, //0x00001369 pcmpeqb      %xmm14, %xmm1
	0x66, 0x0f, 0xd7, 0xd1, //0x0000136e pmovmskb     %xmm1, %edx
	0x66, 0x0f, 0xeb, 0xc8, //0x00001372 por          %xmm0, %xmm1
	0x66, 0x0f, 0xeb, 0xca, //0x00001376 por          %xmm2, %xmm1
	0x66, 0x0f, 0xeb, 0xcb, //0x0000137a por          %xmm3, %xmm1
	0x66, 0x0f, 0xd7, 0xd8, //0x0000137e pmovmskb     %xmm0, %ebx
	0x66, 0x0f, 0xd7, 0xfa, //0x00001382 pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0xd7, 0xc1, //0x00001386 pmovmskb     %xmm1, %eax
	0xf7, 0xd0, //0x0000138a notl         %eax
	0x0f, 0xbc, 0xc8, //0x0000138c bsfl         %eax, %ecx
	0x83, 0xf9, 0x10, //0x0000138f cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00001392 je           LBB0_264
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001398 movl         $-1, %eax
	0xd3, 0xe0, //0x0000139d shll         %cl, %eax
	0xf7, 0xd0, //0x0000139f notl         %eax
	0x21, 0xc3, //0x000013a1 andl         %eax, %ebx
	0x21, 0xc2, //0x000013a3 andl         %eax, %edx
	0x21, 0xf8, //0x000013a5 andl         %edi, %eax
	0x89, 0xc7, //0x000013a7 movl         %eax, %edi
	//0x000013a9 LBB0_264
	0x8d, 0x43, 0xff, //0x000013a9 leal         $-1(%rbx), %eax
	0x21, 0xd8, //0x000013ac andl         %ebx, %eax
	0x0f, 0x85, 0xeb, 0x09, 0x00, 0x00, //0x000013ae jne          LBB0_384
	0x8d, 0x42, 0xff, //0x000013b4 leal         $-1(%rdx), %eax
	0x21, 0xd0, //0x000013b7 andl         %edx, %eax
	0x0f, 0x85, 0xe0, 0x09, 0x00, 0x00, //0x000013b9 jne          LBB0_384
	0x8d, 0x47, 0xff, //0x000013bf leal         $-1(%rdi), %eax
	0x21, 0xf8, //0x000013c2 andl         %edi, %eax
	0x0f, 0x85, 0xd5, 0x09, 0x00, 0x00, //0x000013c4 jne          LBB0_384
	0x85, 0xdb, //0x000013ca testl        %ebx, %ebx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000013cc je           LBB0_270
	0x0f, 0xbc, 0xdb, //0x000013d2 bsfl         %ebx, %ebx
	0x49, 0x83, 0xf8, 0xff, //0x000013d5 cmpq         $-1, %r8
	0x0f, 0x85, 0xe8, 0x0b, 0x00, 0x00, //0x000013d9 jne          LBB0_391
	0x4c, 0x01, 0xf3, //0x000013df addq         %r14, %rbx
	0x49, 0x89, 0xd8, //0x000013e2 movq         %rbx, %r8
	//0x000013e5 LBB0_270
	0x85, 0xd2, //0x000013e5 testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000013e7 je           LBB0_273
	0x0f, 0xbc, 0xd2, //0x000013ed bsfl         %edx, %edx
	0x49, 0x83, 0xfd, 0xff, //0x000013f0 cmpq         $-1, %r13
	0x0f, 0x85, 0xa6, 0x0b, 0x00, 0x00, //0x000013f4 jne          LBB0_389
	0x4c, 0x01, 0xf2, //0x000013fa addq         %r14, %rdx
	0x49, 0x89, 0xd5, //0x000013fd movq         %rdx, %r13
	//0x00001400 LBB0_273
	0x85, 0xff, //0x00001400 testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00001402 je           LBB0_276
	0x0f, 0xbc, 0xd7, //0x00001408 bsfl         %edi, %edx
	0x49, 0x83, 0xfc, 0xff, //0x0000140b cmpq         $-1, %r12
	0x0f, 0x85, 0x8b, 0x0b, 0x00, 0x00, //0x0000140f jne          LBB0_389
	0x4c, 0x01, 0xf2, //0x00001415 addq         %r14, %rdx
	0x49, 0x89, 0xd4, //0x00001418 movq         %rdx, %r12
	//0x0000141b LBB0_276
	0x83, 0xf9, 0x10, //0x0000141b cmpl         $16, %ecx
	0x0f, 0x85, 0x45, 0x03, 0x00, 0x00, //0x0000141e jne          LBB0_329
	0x48, 0x83, 0xc6, 0xf0, //0x00001424 addq         $-16, %rsi
	0x49, 0x83, 0xc6, 0x10, //0x00001428 addq         $16, %r14
	0x48, 0x83, 0xfe, 0x0f, //0x0000142c cmpq         $15, %rsi
	0x0f, 0x87, 0xea, 0xfe, 0xff, 0xff, //0x00001430 ja           LBB0_262
	0x4b, 0x8d, 0x0c, 0x37, //0x00001436 leaq         (%r15,%r14), %rcx
	0x48, 0x89, 0xca, //0x0000143a movq         %rcx, %rdx
	0x4d, 0x39, 0xf3, //0x0000143d cmpq         %r14, %r11
	0x0f, 0x84, 0x46, 0x03, 0x00, 0x00, //0x00001440 je           LBB0_331
	//0x00001446 LBB0_279
	0x4c, 0x8d, 0x34, 0x31, //0x00001446 leaq         (%rcx,%rsi), %r14
	0x48, 0x8b, 0x45, 0x88, //0x0000144a movq         $-120(%rbp), %rax
	0x48, 0x8d, 0x14, 0x08, //0x0000144e leaq         (%rax,%rcx), %rdx
	0x48, 0x2b, 0x55, 0xc0, //0x00001452 subq         $-64(%rbp), %rdx
	0x31, 0xff, //0x00001456 xorl         %edi, %edi
	0x4c, 0x8d, 0x1d, 0x69, 0x25, 0x00, 0x00, //0x00001458 leaq         $9577(%rip), %r11  /* LJTI0_3+0(%rip) */
	0xe9, 0x29, 0x00, 0x00, 0x00, //0x0000145f jmp          LBB0_283
	//0x00001464 LBB0_280
	0x49, 0x83, 0xfc, 0xff, //0x00001464 cmpq         $-1, %r12
	0x0f, 0x85, 0x42, 0x09, 0x00, 0x00, //0x00001468 jne          LBB0_386
	0x4c, 0x8d, 0x24, 0x3a, //0x0000146e leaq         (%rdx,%rdi), %r12
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001472 .p2align 4, 0x90
	//0x00001480 LBB0_282
	0x48, 0x83, 0xc7, 0x01, //0x00001480 addq         $1, %rdi
	0x48, 0x39, 0xfe, //0x00001484 cmpq         %rdi, %rsi
	0x0f, 0x84, 0xee, 0x05, 0x00, 0x00, //0x00001487 je           LBB0_357
	//0x0000148d LBB0_283
	0x0f, 0xbe, 0x1c, 0x39, //0x0000148d movsbl       (%rcx,%rdi), %ebx
	0x8d, 0x43, 0xd0, //0x00001491 leal         $-48(%rbx), %eax
	0x83, 0xf8, 0x0a, //0x00001494 cmpl         $10, %eax
	0x0f, 0x82, 0xe3, 0xff, 0xff, 0xff, //0x00001497 jb           LBB0_282
	0x8d, 0x43, 0xd5, //0x0000149d leal         $-43(%rbx), %eax
	0x83, 0xf8, 0x1a, //0x000014a0 cmpl         $26, %eax
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x000014a3 ja           LBB0_288
	0x49, 0x63, 0x04, 0x83, //0x000014a9 movslq       (%r11,%rax,4), %rax
	0x4c, 0x01, 0xd8, //0x000014ad addq         %r11, %rax
	0xff, 0xe0, //0x000014b0 jmpq         *%rax
	//0x000014b2 LBB0_286
	0x49, 0x83, 0xf8, 0xff, //0x000014b2 cmpq         $-1, %r8
	0x0f, 0x85, 0xf4, 0x08, 0x00, 0x00, //0x000014b6 jne          LBB0_386
	0x4c, 0x8d, 0x04, 0x3a, //0x000014bc leaq         (%rdx,%rdi), %r8
	0xe9, 0xbb, 0xff, 0xff, 0xff, //0x000014c0 jmp          LBB0_282
	//0x000014c5 LBB0_288
	0x83, 0xfb, 0x65, //0x000014c5 cmpl         $101, %ebx
	0x0f, 0x85, 0xb8, 0x02, 0x00, 0x00, //0x000014c8 jne          LBB0_330
	//0x000014ce LBB0_289
	0x49, 0x83, 0xfd, 0xff, //0x000014ce cmpq         $-1, %r13
	0x0f, 0x85, 0xd8, 0x08, 0x00, 0x00, //0x000014d2 jne          LBB0_386
	0x4c, 0x8d, 0x2c, 0x3a, //0x000014d8 leaq         (%rdx,%rdi), %r13
	0xe9, 0x9f, 0xff, 0xff, 0xff, //0x000014dc jmp          LBB0_282
	//0x000014e1 LBB0_291
	0x48, 0x8b, 0x4f, 0x08, //0x000014e1 movq         $8(%rdi), %rcx
	0x48, 0x8d, 0x59, 0xfd, //0x000014e5 leaq         $-3(%rcx), %rbx
	0x48, 0x39, 0xde, //0x000014e9 cmpq         %rbx, %rsi
	0x0f, 0x83, 0x94, 0x1e, 0x00, 0x00, //0x000014ec jae          LBB0_586
	0x48, 0x89, 0xf3, //0x000014f2 movq         %rsi, %rbx
	0x48, 0x8b, 0x4d, 0xc8, //0x000014f5 movq         $-56(%rbp), %rcx
	0x81, 0x39, 0x6e, 0x75, 0x6c, 0x6c, //0x000014f9 cmpl         $1819047278, (%rcx)
	0x0f, 0x84, 0x4d, 0x00, 0x00, 0x00, //0x000014ff je           LBB0_301
	0xe9, 0x57, 0x1f, 0x00, 0x00, //0x00001505 jmp          LBB0_293
	//0x0000150a LBB0_297
	0x48, 0x8b, 0x02, //0x0000150a movq         (%rdx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x0000150d cmpq         $4095, %rax
	0x0f, 0x8f, 0xac, 0x13, 0x00, 0x00, //0x00001513 jg           LBB0_585
	0x48, 0x8d, 0x48, 0x01, //0x00001519 leaq         $1(%rax), %rcx
	0x48, 0x89, 0x0a, //0x0000151d movq         %rcx, (%rdx)
	0x48, 0xc7, 0x44, 0xc2, 0x08, 0x06, 0x00, 0x00, 0x00, //0x00001520 movq         $6, $8(%rdx,%rax,8)
	0xe9, 0xc2, 0xed, 0xff, 0xff, //0x00001529 jmp          LBB0_26
	//0x0000152e LBB0_299
	0x48, 0x8b, 0x4f, 0x08, //0x0000152e movq         $8(%rdi), %rcx
	0x48, 0x8d, 0x59, 0xfd, //0x00001532 leaq         $-3(%rcx), %rbx
	0x48, 0x39, 0xde, //0x00001536 cmpq         %rbx, %rsi
	0x0f, 0x83, 0x47, 0x1e, 0x00, 0x00, //0x00001539 jae          LBB0_586
	0x48, 0x89, 0xf3, //0x0000153f movq         %rsi, %rbx
	0x48, 0x8b, 0x4d, 0xc8, //0x00001542 movq         $-56(%rbp), %rcx
	0x81, 0x39, 0x74, 0x72, 0x75, 0x65, //0x00001546 cmpl         $1702195828, (%rcx)
	0x0f, 0x85, 0x64, 0x1f, 0x00, 0x00, //0x0000154c jne          LBB0_592
	//0x00001552 LBB0_301
	0x4c, 0x8d, 0x4b, 0x04, //0x00001552 leaq         $4(%rbx), %r9
	0xe9, 0x94, 0xfa, 0xff, 0xff, //0x00001556 jmp          LBB0_215
	//0x0000155b LBB0_302
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x0000155b leaq         $-1(%r12), %rax
	0x49, 0x39, 0xc6, //0x00001560 cmpq         %rax, %r14
	0x49, 0xf7, 0xd4, //0x00001563 notq         %r12
	0x4d, 0x0f, 0x45, 0xe1, //0x00001566 cmovneq      %r9, %r12
	0x84, 0xc9, //0x0000156a testb        %cl, %cl
	0x4d, 0x0f, 0x45, 0xcc, //0x0000156c cmovneq      %r12, %r9
	0xe9, 0x5a, 0x03, 0x00, 0x00, //0x00001570 jmp          LBB0_353
	//0x00001575 LBB0_303
	0x4d, 0x29, 0xce, //0x00001575 subq         %r9, %r14
	0x0f, 0x84, 0x95, 0x1f, 0x00, 0x00, //0x00001578 je           LBB0_599
	0x49, 0x83, 0xfe, 0x40, //0x0000157e cmpq         $64, %r14
	0x0f, 0x82, 0x1c, 0x0c, 0x00, 0x00, //0x00001582 jb           LBB0_417
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00001588 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x00001590 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001593 .p2align 4, 0x90
	//0x000015a0 LBB0_306
	0xf3, 0x43, 0x0f, 0x6f, 0x2c, 0x0c, //0x000015a0 movdqu       (%r12,%r9), %xmm5
	0xf3, 0x43, 0x0f, 0x6f, 0x44, 0x0c, 0x10, //0x000015a6 movdqu       $16(%r12,%r9), %xmm0
	0xf3, 0x43, 0x0f, 0x6f, 0x7c, 0x0c, 0x20, //0x000015ad movdqu       $32(%r12,%r9), %xmm7
	0xf3, 0x43, 0x0f, 0x6f, 0x74, 0x0c, 0x30, //0x000015b4 movdqu       $48(%r12,%r9), %xmm6
	0x66, 0x0f, 0x6f, 0xcd, //0x000015bb movdqa       %xmm5, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xc9, //0x000015bf pcmpeqb      %xmm9, %xmm1
	0x66, 0x44, 0x0f, 0xd7, 0xd1, //0x000015c4 pmovmskb     %xmm1, %r10d
	0x66, 0x0f, 0x6f, 0xc8, //0x000015c9 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x000015cd movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0xda, 0xd7, //0x000015d1 pminub       %xmm15, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x000015d6 pcmpeqb      %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xc1, //0x000015da pcmpeqb      %xmm9, %xmm0
	0x66, 0x0f, 0xd7, 0xf0, //0x000015df pmovmskb     %xmm0, %esi
	0x66, 0x0f, 0x6f, 0xc7, //0x000015e3 movdqa       %xmm7, %xmm0
	0x66, 0x0f, 0x6f, 0xdf, //0x000015e7 movdqa       %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0xda, 0xdf, //0x000015eb pminub       %xmm15, %xmm3
	0x66, 0x0f, 0x74, 0xdf, //0x000015f0 pcmpeqb      %xmm7, %xmm3
	0x66, 0x41, 0x0f, 0x74, 0xf9, //0x000015f4 pcmpeqb      %xmm9, %xmm7
	0x66, 0x0f, 0xd7, 0xd7, //0x000015f9 pmovmskb     %xmm7, %edx
	0x66, 0x0f, 0x6f, 0xe6, //0x000015fd movdqa       %xmm6, %xmm4
	0x66, 0x0f, 0x6f, 0xfe, //0x00001601 movdqa       %xmm6, %xmm7
	0x66, 0x41, 0x0f, 0xda, 0xff, //0x00001605 pminub       %xmm15, %xmm7
	0x66, 0x0f, 0x74, 0xfe, //0x0000160a pcmpeqb      %xmm6, %xmm7
	0x66, 0x41, 0x0f, 0x74, 0xf1, //0x0000160e pcmpeqb      %xmm9, %xmm6
	0x66, 0x0f, 0xd7, 0xc6, //0x00001613 pmovmskb     %xmm6, %eax
	0x66, 0x0f, 0x6f, 0xf5, //0x00001617 movdqa       %xmm5, %xmm6
	0x66, 0x41, 0x0f, 0x74, 0xf2, //0x0000161b pcmpeqb      %xmm10, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xfe, //0x00001620 pmovmskb     %xmm6, %r15d
	0x66, 0x41, 0x0f, 0x74, 0xca, //0x00001625 pcmpeqb      %xmm10, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x0000162a pmovmskb     %xmm1, %ecx
	0x66, 0x41, 0x0f, 0x74, 0xc2, //0x0000162e pcmpeqb      %xmm10, %xmm0
	0x66, 0x0f, 0xd7, 0xf8, //0x00001633 pmovmskb     %xmm0, %edi
	0x66, 0x41, 0x0f, 0x74, 0xe2, //0x00001637 pcmpeqb      %xmm10, %xmm4
	0x66, 0x44, 0x0f, 0xd7, 0xc4, //0x0000163c pmovmskb     %xmm4, %r8d
	0x66, 0x44, 0x0f, 0xd7, 0xea, //0x00001641 pmovmskb     %xmm2, %r13d
	0x66, 0x0f, 0xd7, 0xdb, //0x00001646 pmovmskb     %xmm3, %ebx
	0x66, 0x44, 0x0f, 0xd7, 0xe7, //0x0000164a pmovmskb     %xmm7, %r12d
	0x48, 0xc1, 0xe0, 0x30, //0x0000164f shlq         $48, %rax
	0x48, 0xc1, 0xe2, 0x20, //0x00001653 shlq         $32, %rdx
	0x48, 0x09, 0xc2, //0x00001657 orq          %rax, %rdx
	0x48, 0xc1, 0xe6, 0x10, //0x0000165a shlq         $16, %rsi
	0x48, 0x09, 0xd6, //0x0000165e orq          %rdx, %rsi
	0x49, 0x09, 0xf2, //0x00001661 orq          %rsi, %r10
	0x49, 0xc1, 0xe0, 0x30, //0x00001664 shlq         $48, %r8
	0x48, 0xc1, 0xe7, 0x20, //0x00001668 shlq         $32, %rdi
	0x4c, 0x09, 0xc7, //0x0000166c orq          %r8, %rdi
	0x48, 0xc1, 0xe1, 0x10, //0x0000166f shlq         $16, %rcx
	0x48, 0x09, 0xf9, //0x00001673 orq          %rdi, %rcx
	0x49, 0xc1, 0xe4, 0x30, //0x00001676 shlq         $48, %r12
	0x48, 0xc1, 0xe3, 0x20, //0x0000167a shlq         $32, %rbx
	0x4c, 0x09, 0xe3, //0x0000167e orq          %r12, %rbx
	0x49, 0xc1, 0xe5, 0x10, //0x00001681 shlq         $16, %r13
	0x49, 0x09, 0xdd, //0x00001685 orq          %rbx, %r13
	0x49, 0x09, 0xcf, //0x00001688 orq          %rcx, %r15
	0x0f, 0x85, 0x51, 0x00, 0x00, 0x00, //0x0000168b jne          LBB0_323
	0x4d, 0x85, 0xdb, //0x00001691 testq        %r11, %r11
	0x0f, 0x85, 0x67, 0x00, 0x00, 0x00, //0x00001694 jne          LBB0_325
	0x45, 0x31, 0xdb, //0x0000169a xorl         %r11d, %r11d
	0x4c, 0x8b, 0x65, 0xb8, //0x0000169d movq         $-72(%rbp), %r12
	//0x000016a1 LBB0_309
	0x66, 0x0f, 0x6f, 0xc5, //0x000016a1 movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc7, //0x000016a5 pminub       %xmm15, %xmm0
	0x66, 0x0f, 0x74, 0xc5, //0x000016aa pcmpeqb      %xmm5, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x000016ae pmovmskb     %xmm0, %eax
	0x49, 0x09, 0xc5, //0x000016b2 orq          %rax, %r13
	0x4d, 0x85, 0xd2, //0x000016b5 testq        %r10, %r10
	0x48, 0x8b, 0x5d, 0xc0, //0x000016b8 movq         $-64(%rbp), %rbx
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x000016bc jne          LBB0_327
	0x4d, 0x85, 0xed, //0x000016c2 testq        %r13, %r13
	0x0f, 0x85, 0xea, 0x1c, 0x00, 0x00, //0x000016c5 jne          LBB0_576
	0x49, 0x83, 0xc6, 0xc0, //0x000016cb addq         $-64, %r14
	0x49, 0x83, 0xc1, 0x40, //0x000016cf addq         $64, %r9
	0x49, 0x83, 0xfe, 0x3f, //0x000016d3 cmpq         $63, %r14
	0x0f, 0x87, 0xc3, 0xfe, 0xff, 0xff, //0x000016d7 ja           LBB0_306
	0xe9, 0xee, 0x07, 0x00, 0x00, //0x000016dd jmp          LBB0_312
	//0x000016e2 LBB0_323
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x000016e2 cmpq         $-1, $-56(%rbp)
	0x4c, 0x8b, 0x65, 0xb8, //0x000016e7 movq         $-72(%rbp), %r12
	0x0f, 0x85, 0x14, 0x00, 0x00, 0x00, //0x000016eb jne          LBB0_326
	0x49, 0x0f, 0xbc, 0xc7, //0x000016f1 bsfq         %r15, %rax
	0x4c, 0x01, 0xc8, //0x000016f5 addq         %r9, %rax
	0x48, 0x89, 0x45, 0xc8, //0x000016f8 movq         %rax, $-56(%rbp)
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x000016fc jmp          LBB0_326
	//0x00001701 LBB0_325
	0x4c, 0x8b, 0x65, 0xb8, //0x00001701 movq         $-72(%rbp), %r12
	//0x00001705 LBB0_326
	0x4c, 0x89, 0xd8, //0x00001705 movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x00001708 notq         %rax
	0x4c, 0x21, 0xf8, //0x0000170b andq         %r15, %rax
	0x48, 0x8d, 0x0c, 0x00, //0x0000170e leaq         (%rax,%rax), %rcx
	0x4c, 0x09, 0xd9, //0x00001712 orq          %r11, %rcx
	0x48, 0x89, 0xca, //0x00001715 movq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00001718 notq         %rdx
	0x4c, 0x21, 0xfa, //0x0000171b andq         %r15, %rdx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000171e movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf2, //0x00001728 andq         %rsi, %rdx
	0x45, 0x31, 0xdb, //0x0000172b xorl         %r11d, %r11d
	0x48, 0x01, 0xc2, //0x0000172e addq         %rax, %rdx
	0x41, 0x0f, 0x92, 0xc3, //0x00001731 setb         %r11b
	0x48, 0x01, 0xd2, //0x00001735 addq         %rdx, %rdx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001738 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc2, //0x00001742 xorq         %rax, %rdx
	0x48, 0x21, 0xca, //0x00001745 andq         %rcx, %rdx
	0x48, 0xf7, 0xd2, //0x00001748 notq         %rdx
	0x49, 0x21, 0xd2, //0x0000174b andq         %rdx, %r10
	0xe9, 0x4e, 0xff, 0xff, 0xff, //0x0000174e jmp          LBB0_309
	//0x00001753 LBB0_327
	0x49, 0x0f, 0xbc, 0xc2, //0x00001753 bsfq         %r10, %rax
	0x4d, 0x85, 0xed, //0x00001757 testq        %r13, %r13
	0x0f, 0x84, 0xe4, 0x00, 0x00, 0x00, //0x0000175a je           LBB0_343
	0x49, 0x0f, 0xbc, 0xcd, //0x00001760 bsfq         %r13, %rcx
	0xe9, 0xe0, 0x00, 0x00, 0x00, //0x00001764 jmp          LBB0_344
	//0x00001769 LBB0_329
	0x89, 0xca, //0x00001769 movl         %ecx, %edx
	0x4c, 0x01, 0xfa, //0x0000176b addq         %r15, %rdx
	0x4c, 0x01, 0xf2, //0x0000176e addq         %r14, %rdx
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00001771 movq         $-1, %r14
	0x4d, 0x85, 0xc0, //0x00001778 testq        %r8, %r8
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x0000177b jne          LBB0_332
	0xe9, 0x6f, 0x1c, 0x00, 0x00, //0x00001781 jmp          LBB0_584
	//0x00001786 LBB0_330
	0x48, 0x01, 0xf9, //0x00001786 addq         %rdi, %rcx
	0x48, 0x89, 0xca, //0x00001789 movq         %rcx, %rdx
	//0x0000178c LBB0_331
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x0000178c movq         $-1, %r14
	0x4d, 0x85, 0xc0, //0x00001793 testq        %r8, %r8
	0x0f, 0x84, 0x59, 0x1c, 0x00, 0x00, //0x00001796 je           LBB0_584
	//0x0000179c LBB0_332
	0x4d, 0x85, 0xe4, //0x0000179c testq        %r12, %r12
	0x0f, 0x84, 0x50, 0x1c, 0x00, 0x00, //0x0000179f je           LBB0_584
	0x4d, 0x85, 0xed, //0x000017a5 testq        %r13, %r13
	0x0f, 0x84, 0x47, 0x1c, 0x00, 0x00, //0x000017a8 je           LBB0_584
	0x4c, 0x29, 0xfa, //0x000017ae subq         %r15, %rdx
	0x48, 0x8d, 0x4a, 0xff, //0x000017b1 leaq         $-1(%rdx), %rcx
	0x49, 0x39, 0xc8, //0x000017b5 cmpq         %rcx, %r8
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x000017b8 je           LBB0_340
	0x49, 0x39, 0xcc, //0x000017be cmpq         %rcx, %r12
	0x0f, 0x84, 0x2b, 0x00, 0x00, 0x00, //0x000017c1 je           LBB0_340
	0x49, 0x39, 0xcd, //0x000017c7 cmpq         %rcx, %r13
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x000017ca je           LBB0_340
	0x4d, 0x85, 0xe4, //0x000017d0 testq        %r12, %r12
	0x0f, 0x8e, 0xa0, 0x00, 0x00, 0x00, //0x000017d3 jle          LBB0_347
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x000017d9 leaq         $-1(%r12), %rax
	0x49, 0x39, 0xc5, //0x000017de cmpq         %rax, %r13
	0x0f, 0x84, 0x92, 0x00, 0x00, 0x00, //0x000017e1 je           LBB0_347
	0x49, 0xf7, 0xd4, //0x000017e7 notq         %r12
	0x4d, 0x89, 0xe6, //0x000017ea movq         %r12, %r14
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x000017ed jmp          LBB0_341
	//0x000017f2 LBB0_340
	0x48, 0xf7, 0xda, //0x000017f2 negq         %rdx
	0x49, 0x89, 0xd6, //0x000017f5 movq         %rdx, %r14
	//0x000017f8 LBB0_341
	0x4d, 0x85, 0xf6, //0x000017f8 testq        %r14, %r14
	0x0f, 0x88, 0xf4, 0x1b, 0x00, 0x00, //0x000017fb js           LBB0_584
	//0x00001801 LBB0_342
	0x4d, 0x01, 0xf1, //0x00001801 addq         %r14, %r9
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001804 movq         $-48(%rbp), %r13
	0x4d, 0x89, 0x4d, 0x00, //0x00001808 movq         %r9, (%r13)
	0x48, 0x8b, 0x55, 0xc0, //0x0000180c movq         $-64(%rbp), %rdx
	0x48, 0x89, 0xd0, //0x00001810 movq         %rdx, %rax
	0x48, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00001813 movabsq      $9223372036854775806, %rcx
	0x48, 0x39, 0xca, //0x0000181d cmpq         %rcx, %rdx
	0x4c, 0x89, 0xd7, //0x00001820 movq         %r10, %rdi
	0x4c, 0x8b, 0x65, 0xb8, //0x00001823 movq         $-72(%rbp), %r12
	0x48, 0x8b, 0x55, 0xa0, //0x00001827 movq         $-96(%rbp), %rdx
	0x4c, 0x8b, 0x75, 0xa8, //0x0000182b movq         $-88(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000182f movabsq      $4294977024, %r11
	0x0f, 0x86, 0xb1, 0xea, 0xff, 0xff, //0x00001839 jbe          LBB0_26
	0xe9, 0x98, 0x1b, 0x00, 0x00, //0x0000183f jmp          LBB0_582
	//0x00001844 LBB0_343
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001844 movl         $64, %ecx
	//0x00001849 LBB0_344
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001849 movq         $-48(%rbp), %r13
	0x48, 0x8b, 0x7d, 0xb0, //0x0000184d movq         $-80(%rbp), %rdi
	0x48, 0x8b, 0x55, 0xa0, //0x00001851 movq         $-96(%rbp), %rdx
	0x4c, 0x8b, 0x75, 0xa8, //0x00001855 movq         $-88(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001859 movabsq      $4294977024, %r11
	0x48, 0x39, 0xc1, //0x00001863 cmpq         %rax, %rcx
	0x0f, 0x83, 0x73, 0xf7, 0xff, 0xff, //0x00001866 jae          LBB0_213
	0xe9, 0xba, 0x1c, 0x00, 0x00, //0x0000186c jmp          LBB0_345
	//0x00001871 LBB0_346
	0x0f, 0xbc, 0xc0, //0x00001871 bsfl         %eax, %eax
	0xe9, 0x42, 0x00, 0x00, 0x00, //0x00001874 jmp          LBB0_352
	//0x00001879 LBB0_347
	0x4c, 0x89, 0xc0, //0x00001879 movq         %r8, %rax
	0x4c, 0x09, 0xe8, //0x0000187c orq          %r13, %rax
	0x0f, 0x99, 0xc0, //0x0000187f setns        %al
	0x0f, 0x88, 0xd7, 0x01, 0x00, 0x00, //0x00001882 js           LBB0_356
	0x4d, 0x39, 0xe8, //0x00001888 cmpq         %r13, %r8
	0x0f, 0x8c, 0xce, 0x01, 0x00, 0x00, //0x0000188b jl           LBB0_356
	0x49, 0xf7, 0xd0, //0x00001891 notq         %r8
	0x4d, 0x89, 0xc6, //0x00001894 movq         %r8, %r14
	0xe9, 0x5c, 0xff, 0xff, 0xff, //0x00001897 jmp          LBB0_341
	//0x0000189c LBB0_350
	0x48, 0x8b, 0x85, 0x78, 0xff, 0xff, 0xff, //0x0000189c movq         $-136(%rbp), %rax
	0x48, 0x8b, 0x5d, 0xc0, //0x000018a3 movq         $-64(%rbp), %rbx
	0x4c, 0x8d, 0x0c, 0x18, //0x000018a7 leaq         (%rax,%rbx), %r9
	0x49, 0x29, 0xc9, //0x000018ab subq         %rcx, %r9
	0x49, 0x29, 0xf9, //0x000018ae subq         %rdi, %r9
	0x49, 0x89, 0xf5, //0x000018b1 movq         %rsi, %r13
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x000018b4 jmp          LBB0_354
	//0x000018b9 LBB0_351
	0x89, 0xc0, //0x000018b9 movl         %eax, %eax
	//0x000018bb LBB0_352
	0x49, 0xf7, 0xd1, //0x000018bb notq         %r9
	0x49, 0x29, 0xc1, //0x000018be subq         %rax, %r9
	0x4c, 0x8b, 0x6d, 0xd0, //0x000018c1 movq         $-48(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000018c5 movabsq      $4294977024, %r11
	//0x000018cf LBB0_353
	0x48, 0x8b, 0x5d, 0xc0, //0x000018cf movq         $-64(%rbp), %rbx
	//0x000018d3 LBB0_354
	0x4d, 0x85, 0xc9, //0x000018d3 testq        %r9, %r9
	0x48, 0x8b, 0x7d, 0xb0, //0x000018d6 movq         $-80(%rbp), %rdi
	0x4c, 0x8b, 0x65, 0xb8, //0x000018da movq         $-72(%rbp), %r12
	0x48, 0x8b, 0x55, 0xa0, //0x000018de movq         $-96(%rbp), %rdx
	0x4c, 0x8b, 0x75, 0xa8, //0x000018e2 movq         $-88(%rbp), %r14
	0x0f, 0x88, 0x27, 0x1a, 0x00, 0x00, //0x000018e6 js           LBB0_565
	0x49, 0x01, 0xd9, //0x000018ec addq         %rbx, %r9
	0xe9, 0xe6, 0xe9, 0xff, 0xff, //0x000018ef jmp          LBB0_25
	//0x000018f4 LBB0_72
	0x4d, 0x01, 0xe1, //0x000018f4 addq         %r12, %r9
	0x49, 0x83, 0xfe, 0x20, //0x000018f7 cmpq         $32, %r14
	0x48, 0x8b, 0x5d, 0xc0, //0x000018fb movq         $-64(%rbp), %rbx
	0x0f, 0x82, 0x31, 0x09, 0x00, 0x00, //0x000018ff jb           LBB0_422
	//0x00001905 LBB0_73
	0xf3, 0x41, 0x0f, 0x6f, 0x01, //0x00001905 movdqu       (%r9), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x49, 0x10, //0x0000190a movdqu       $16(%r9), %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00001910 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd1, //0x00001914 pcmpeqb      %xmm9, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00001919 pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd1, //0x0000191d movdqa       %xmm1, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd1, //0x00001921 pcmpeqb      %xmm9, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00001926 pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xc2, //0x0000192a pcmpeqb      %xmm10, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x0000192f pmovmskb     %xmm0, %edx
	0x66, 0x41, 0x0f, 0x74, 0xca, //0x00001933 pcmpeqb      %xmm10, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x00001938 pmovmskb     %xmm1, %ecx
	0x48, 0xc1, 0xe0, 0x10, //0x0000193c shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001940 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x10, //0x00001943 shlq         $16, %rcx
	0x48, 0x09, 0xca, //0x00001947 orq          %rcx, %rdx
	0x0f, 0x85, 0x81, 0x08, 0x00, 0x00, //0x0000194a jne          LBB0_418
	0x4d, 0x85, 0xdb, //0x00001950 testq        %r11, %r11
	0x0f, 0x85, 0x95, 0x08, 0x00, 0x00, //0x00001953 jne          LBB0_420
	0x45, 0x31, 0xdb, //0x00001959 xorl         %r11d, %r11d
	0x48, 0x85, 0xff, //0x0000195c testq        %rdi, %rdi
	0x0f, 0x84, 0xc9, 0x08, 0x00, 0x00, //0x0000195f je           LBB0_421
	//0x00001965 LBB0_76
	0x48, 0x0f, 0xbc, 0xc7, //0x00001965 bsfq         %rdi, %rax
	0xe9, 0xcb, 0x00, 0x00, 0x00, //0x00001969 jmp          LBB0_166
	//0x0000196e LBB0_156
	0x4d, 0x01, 0xe1, //0x0000196e addq         %r12, %r9
	0x48, 0x8b, 0x5d, 0xc0, //0x00001971 movq         $-64(%rbp), %rbx
	0x49, 0x83, 0xfe, 0x20, //0x00001975 cmpq         $32, %r14
	0x0f, 0x82, 0x90, 0x02, 0x00, 0x00, //0x00001979 jb           LBB0_360
	//0x0000197f LBB0_157
	0xf3, 0x41, 0x0f, 0x6f, 0x29, //0x0000197f movdqu       (%r9), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x41, 0x10, //0x00001984 movdqu       $16(%r9), %xmm0
	0x66, 0x0f, 0x6f, 0xcd, //0x0000198a movdqa       %xmm5, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xc9, //0x0000198e pcmpeqb      %xmm9, %xmm1
	0x66, 0x0f, 0xd7, 0xf1, //0x00001993 pmovmskb     %xmm1, %esi
	0x66, 0x0f, 0x6f, 0xc8, //0x00001997 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x0000199b movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0xda, 0xd7, //0x0000199f pminub       %xmm15, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x000019a4 pcmpeqb      %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xc1, //0x000019a8 pcmpeqb      %xmm9, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x000019ad pmovmskb     %xmm0, %eax
	0x66, 0x0f, 0x6f, 0xc5, //0x000019b1 movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc2, //0x000019b5 pcmpeqb      %xmm10, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x000019ba pmovmskb     %xmm0, %edx
	0x66, 0x41, 0x0f, 0x74, 0xca, //0x000019be pcmpeqb      %xmm10, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x000019c3 pmovmskb     %xmm1, %ecx
	0x66, 0x0f, 0xd7, 0xfa, //0x000019c7 pmovmskb     %xmm2, %edi
	0x48, 0xc1, 0xe0, 0x10, //0x000019cb shlq         $16, %rax
	0x48, 0x09, 0xc6, //0x000019cf orq          %rax, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x000019d2 shlq         $16, %rcx
	0x48, 0xc1, 0xe7, 0x10, //0x000019d6 shlq         $16, %rdi
	0x48, 0x09, 0xca, //0x000019da orq          %rcx, %rdx
	0x0f, 0x85, 0x48, 0x09, 0x00, 0x00, //0x000019dd jne          LBB0_435
	0x4d, 0x85, 0xdb, //0x000019e3 testq        %r11, %r11
	0x0f, 0x85, 0x5c, 0x09, 0x00, 0x00, //0x000019e6 jne          LBB0_437
	0x45, 0x31, 0xdb, //0x000019ec xorl         %r11d, %r11d
	//0x000019ef LBB0_160
	0x66, 0x0f, 0x6f, 0xc5, //0x000019ef movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc7, //0x000019f3 pminub       %xmm15, %xmm0
	0x66, 0x0f, 0x74, 0xc5, //0x000019f8 pcmpeqb      %xmm5, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x000019fc pmovmskb     %xmm0, %eax
	0x48, 0x09, 0xc7, //0x00001a00 orq          %rax, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001a03 movl         $64, %ecx
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00001a08 movl         $64, %eax
	0x48, 0x85, 0xf6, //0x00001a0d testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001a10 je           LBB0_162
	0x48, 0x0f, 0xbc, 0xc6, //0x00001a16 bsfq         %rsi, %rax
	//0x00001a1a LBB0_162
	0x48, 0x85, 0xff, //0x00001a1a testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001a1d je           LBB0_164
	0x48, 0x0f, 0xbc, 0xcf, //0x00001a23 bsfq         %rdi, %rcx
	//0x00001a27 LBB0_164
	0x48, 0x85, 0xf6, //0x00001a27 testq        %rsi, %rsi
	0x0f, 0x84, 0xce, 0x01, 0x00, 0x00, //0x00001a2a je           LBB0_358
	0x48, 0x39, 0xc1, //0x00001a30 cmpq         %rax, %rcx
	0x0f, 0x82, 0x1a, 0x1b, 0x00, 0x00, //0x00001a33 jb           LBB0_601
	//0x00001a39 LBB0_166
	0x4c, 0x03, 0x4d, 0x90, //0x00001a39 addq         $-112(%rbp), %r9
	0x49, 0x01, 0xc1, //0x00001a3d addq         %rax, %r9
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001a40 movq         $-48(%rbp), %r13
	0x48, 0x8b, 0x7d, 0xb0, //0x00001a44 movq         $-80(%rbp), %rdi
	0x48, 0x8b, 0x55, 0xa0, //0x00001a48 movq         $-96(%rbp), %rdx
	0x4c, 0x8b, 0x75, 0xa8, //0x00001a4c movq         $-88(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001a50 movabsq      $4294977024, %r11
	0xe9, 0x87, 0xf5, 0xff, 0xff, //0x00001a5a jmp          LBB0_214
	//0x00001a5f LBB0_356
	0x49, 0x8d, 0x4d, 0xff, //0x00001a5f leaq         $-1(%r13), %rcx
	0x49, 0x39, 0xc8, //0x00001a63 cmpq         %rcx, %r8
	0x49, 0xf7, 0xd5, //0x00001a66 notq         %r13
	0x4c, 0x0f, 0x45, 0xea, //0x00001a69 cmovneq      %rdx, %r13
	0x84, 0xc0, //0x00001a6d testb        %al, %al
	0x4c, 0x0f, 0x44, 0xea, //0x00001a6f cmoveq       %rdx, %r13
	0x4d, 0x89, 0xee, //0x00001a73 movq         %r13, %r14
	0xe9, 0x7d, 0xfd, 0xff, 0xff, //0x00001a76 jmp          LBB0_341
	//0x00001a7b LBB0_357
	0x4c, 0x89, 0xf2, //0x00001a7b movq         %r14, %rdx
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00001a7e movq         $-1, %r14
	0x4d, 0x85, 0xc0, //0x00001a85 testq        %r8, %r8
	0x0f, 0x85, 0x0e, 0xfd, 0xff, 0xff, //0x00001a88 jne          LBB0_332
	0xe9, 0x62, 0x19, 0x00, 0x00, //0x00001a8e jmp          LBB0_584
	//0x00001a93 LBB0_138
	0x4d, 0x01, 0xe1, //0x00001a93 addq         %r12, %r9
	0x49, 0x83, 0xfe, 0x20, //0x00001a96 cmpq         $32, %r14
	0x48, 0x8b, 0x5d, 0xc0, //0x00001a9a movq         $-64(%rbp), %rbx
	0x0f, 0x82, 0x45, 0x09, 0x00, 0x00, //0x00001a9e jb           LBB0_442
	//0x00001aa4 LBB0_139
	0xf3, 0x41, 0x0f, 0x6f, 0x01, //0x00001aa4 movdqu       (%r9), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x49, 0x10, //0x00001aa9 movdqu       $16(%r9), %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00001aaf movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd1, //0x00001ab3 pcmpeqb      %xmm9, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00001ab8 pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd1, //0x00001abc movdqa       %xmm1, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd1, //0x00001ac0 pcmpeqb      %xmm9, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00001ac5 pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xc2, //0x00001ac9 pcmpeqb      %xmm10, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00001ace pmovmskb     %xmm0, %edx
	0x66, 0x41, 0x0f, 0x74, 0xca, //0x00001ad2 pcmpeqb      %xmm10, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x00001ad7 pmovmskb     %xmm1, %ecx
	0x48, 0xc1, 0xe0, 0x10, //0x00001adb shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001adf orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x10, //0x00001ae2 shlq         $16, %rcx
	0x48, 0x09, 0xca, //0x00001ae6 orq          %rcx, %rdx
	0x0f, 0x85, 0x95, 0x08, 0x00, 0x00, //0x00001ae9 jne          LBB0_438
	0x4d, 0x85, 0xdb, //0x00001aef testq        %r11, %r11
	0x0f, 0x85, 0xa9, 0x08, 0x00, 0x00, //0x00001af2 jne          LBB0_440
	0x45, 0x31, 0xdb, //0x00001af8 xorl         %r11d, %r11d
	0x48, 0x85, 0xff, //0x00001afb testq        %rdi, %rdi
	0x0f, 0x84, 0xdd, 0x08, 0x00, 0x00, //0x00001afe je           LBB0_441
	//0x00001b04 LBB0_142
	0x48, 0x0f, 0xbc, 0xc7, //0x00001b04 bsfq         %rdi, %rax
	0xe9, 0xcb, 0x00, 0x00, 0x00, //0x00001b08 jmp          LBB0_192
	//0x00001b0d LBB0_182
	0x4d, 0x01, 0xe1, //0x00001b0d addq         %r12, %r9
	0x49, 0x83, 0xfe, 0x20, //0x00001b10 cmpq         $32, %r14
	0x0f, 0x82, 0xe9, 0x05, 0x00, 0x00, //0x00001b14 jb           LBB0_412
	//0x00001b1a LBB0_183
	0xf3, 0x41, 0x0f, 0x6f, 0x29, //0x00001b1a movdqu       (%r9), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x41, 0x10, //0x00001b1f movdqu       $16(%r9), %xmm0
	0x66, 0x0f, 0x6f, 0xcd, //0x00001b25 movdqa       %xmm5, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xc9, //0x00001b29 pcmpeqb      %xmm9, %xmm1
	0x66, 0x0f, 0xd7, 0xf1, //0x00001b2e pmovmskb     %xmm1, %esi
	0x66, 0x0f, 0x6f, 0xc8, //0x00001b32 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00001b36 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0xda, 0xd7, //0x00001b3a pminub       %xmm15, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00001b3f pcmpeqb      %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xc1, //0x00001b43 pcmpeqb      %xmm9, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00001b48 pmovmskb     %xmm0, %eax
	0x66, 0x0f, 0x6f, 0xc5, //0x00001b4c movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc2, //0x00001b50 pcmpeqb      %xmm10, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00001b55 pmovmskb     %xmm0, %edx
	0x66, 0x41, 0x0f, 0x74, 0xca, //0x00001b59 pcmpeqb      %xmm10, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x00001b5e pmovmskb     %xmm1, %ecx
	0x66, 0x0f, 0xd7, 0xfa, //0x00001b62 pmovmskb     %xmm2, %edi
	0x48, 0xc1, 0xe0, 0x10, //0x00001b66 shlq         $16, %rax
	0x48, 0x09, 0xc6, //0x00001b6a orq          %rax, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x00001b6d shlq         $16, %rcx
	0x48, 0xc1, 0xe7, 0x10, //0x00001b71 shlq         $16, %rdi
	0x48, 0x09, 0xca, //0x00001b75 orq          %rcx, %rdx
	0x48, 0x8b, 0x5d, 0xc0, //0x00001b78 movq         $-64(%rbp), %rbx
	0x0f, 0x85, 0x5c, 0x09, 0x00, 0x00, //0x00001b7c jne          LBB0_455
	0x4d, 0x85, 0xdb, //0x00001b82 testq        %r11, %r11
	0x0f, 0x85, 0x70, 0x09, 0x00, 0x00, //0x00001b85 jne          LBB0_457
	0x45, 0x31, 0xdb, //0x00001b8b xorl         %r11d, %r11d
	//0x00001b8e LBB0_186
	0x66, 0x0f, 0x6f, 0xc5, //0x00001b8e movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc7, //0x00001b92 pminub       %xmm15, %xmm0
	0x66, 0x0f, 0x74, 0xc5, //0x00001b97 pcmpeqb      %xmm5, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00001b9b pmovmskb     %xmm0, %eax
	0x48, 0x09, 0xc7, //0x00001b9f orq          %rax, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001ba2 movl         $64, %ecx
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00001ba7 movl         $64, %eax
	0x48, 0x85, 0xf6, //0x00001bac testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001baf je           LBB0_188
	0x48, 0x0f, 0xbc, 0xc6, //0x00001bb5 bsfq         %rsi, %rax
	//0x00001bb9 LBB0_188
	0x48, 0x85, 0xff, //0x00001bb9 testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001bbc je           LBB0_190
	0x48, 0x0f, 0xbc, 0xcf, //0x00001bc2 bsfq         %rdi, %rcx
	//0x00001bc6 LBB0_190
	0x48, 0x85, 0xf6, //0x00001bc6 testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x01, 0x00, 0x00, //0x00001bc9 je           LBB0_372
	0x48, 0x39, 0xc1, //0x00001bcf cmpq         %rax, %rcx
	0x0f, 0x82, 0x7b, 0x19, 0x00, 0x00, //0x00001bd2 jb           LBB0_601
	//0x00001bd8 LBB0_192
	0x4c, 0x03, 0x4d, 0x90, //0x00001bd8 addq         $-112(%rbp), %r9
	0x49, 0x01, 0xc1, //0x00001bdc addq         %rax, %r9
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001bdf movq         $-48(%rbp), %r13
	0x48, 0x8b, 0x7d, 0xb0, //0x00001be3 movq         $-80(%rbp), %rdi
	0x48, 0x8b, 0x55, 0xa0, //0x00001be7 movq         $-96(%rbp), %rdx
	0x4c, 0x8b, 0x75, 0xa8, //0x00001beb movq         $-88(%rbp), %r14
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001bef movabsq      $4294977024, %r11
	0xe9, 0x62, 0xf4, 0xff, 0xff, //0x00001bf9 jmp          LBB0_222
	//0x00001bfe LBB0_358
	0x48, 0x85, 0xff, //0x00001bfe testq        %rdi, %rdi
	0x0f, 0x85, 0x4c, 0x19, 0x00, 0x00, //0x00001c01 jne          LBB0_601
	0x49, 0x83, 0xc1, 0x20, //0x00001c07 addq         $32, %r9
	0x49, 0x83, 0xc6, 0xe0, //0x00001c0b addq         $-32, %r14
	//0x00001c0f LBB0_360
	0x4d, 0x85, 0xdb, //0x00001c0f testq        %r11, %r11
	0x0f, 0x85, 0x72, 0x09, 0x00, 0x00, //0x00001c12 jne          LBB0_460
	0x48, 0x8b, 0x55, 0xc8, //0x00001c18 movq         $-56(%rbp), %rdx
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001c1c movq         $-48(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001c20 movabsq      $4294977024, %r11
	0x4d, 0x85, 0xf6, //0x00001c2a testq        %r14, %r14
	0x0f, 0x84, 0xac, 0x0c, 0x00, 0x00, //0x00001c2d je           LBB0_498
	//0x00001c33 LBB0_362
	0x41, 0x0f, 0xb6, 0x01, //0x00001c33 movzbl       (%r9), %eax
	0x3c, 0x22, //0x00001c37 cmpb         $34, %al
	0x0f, 0x84, 0x8b, 0x00, 0x00, 0x00, //0x00001c39 je           LBB0_371
	0x3c, 0x5c, //0x00001c3f cmpb         $92, %al
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00001c41 je           LBB0_366
	0x3c, 0x1f, //0x00001c47 cmpb         $31, %al
	0x0f, 0x86, 0x26, 0x19, 0x00, 0x00, //0x00001c49 jbe          LBB0_603
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001c4f movq         $-1, %rax
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00001c56 movl         $1, %ecx
	0x49, 0x01, 0xc9, //0x00001c5b addq         %rcx, %r9
	0x49, 0x01, 0xc6, //0x00001c5e addq         %rax, %r14
	0x0f, 0x85, 0xcc, 0xff, 0xff, 0xff, //0x00001c61 jne          LBB0_362
	0xe9, 0x73, 0x0c, 0x00, 0x00, //0x00001c67 jmp          LBB0_498
	//0x00001c6c LBB0_366
	0x49, 0x83, 0xfe, 0x01, //0x00001c6c cmpq         $1, %r14
	0x0f, 0x84, 0xea, 0x0a, 0x00, 0x00, //0x00001c70 je           LBB0_474
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00001c76 movq         $-2, %rax
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00001c7d movl         $2, %ecx
	0x48, 0x83, 0xfa, 0xff, //0x00001c82 cmpq         $-1, %rdx
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00001c86 je           LBB0_369
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001c8c movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x00001c90 movq         $-72(%rbp), %r12
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00001c94 jmp          LBB0_370
	//0x00001c99 LBB0_369
	0x4c, 0x89, 0xca, //0x00001c99 movq         %r9, %rdx
	0x4c, 0x8b, 0x65, 0xb8, //0x00001c9c movq         $-72(%rbp), %r12
	0x4c, 0x29, 0xe2, //0x00001ca0 subq         %r12, %rdx
	0x48, 0x89, 0x55, 0xc8, //0x00001ca3 movq         %rdx, $-56(%rbp)
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001ca7 movq         $-48(%rbp), %r13
	//0x00001cab LBB0_370
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001cab movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x00001cb5 movq         $-64(%rbp), %rbx
	0x49, 0x01, 0xc9, //0x00001cb9 addq         %rcx, %r9
	0x49, 0x01, 0xc6, //0x00001cbc addq         %rax, %r14
	0x0f, 0x85, 0x6e, 0xff, 0xff, 0xff, //0x00001cbf jne          LBB0_362
	0xe9, 0x15, 0x0c, 0x00, 0x00, //0x00001cc5 jmp          LBB0_498
	//0x00001cca LBB0_371
	0x4c, 0x03, 0x4d, 0x90, //0x00001cca addq         $-112(%rbp), %r9
	0xe9, 0x21, 0x06, 0x00, 0x00, //0x00001cce jmp          LBB0_434
	//0x00001cd3 LBB0_372
	0x48, 0x85, 0xff, //0x00001cd3 testq        %rdi, %rdi
	0x0f, 0x85, 0x77, 0x18, 0x00, 0x00, //0x00001cd6 jne          LBB0_601
	0x49, 0x83, 0xc1, 0x20, //0x00001cdc addq         $32, %r9
	0x49, 0x83, 0xc6, 0xe0, //0x00001ce0 addq         $-32, %r14
	0x4d, 0x85, 0xdb, //0x00001ce4 testq        %r11, %r11
	0x0f, 0x85, 0x23, 0x04, 0x00, 0x00, //0x00001ce7 jne          LBB0_413
	//0x00001ced LBB0_374
	0x48, 0x8b, 0x55, 0xc8, //0x00001ced movq         $-56(%rbp), %rdx
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001cf1 movq         $-48(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001cf5 movabsq      $4294977024, %r11
	0x4d, 0x85, 0xf6, //0x00001cff testq        %r14, %r14
	0x0f, 0x84, 0xd7, 0x0b, 0x00, 0x00, //0x00001d02 je           LBB0_498
	//0x00001d08 LBB0_375
	0x41, 0x0f, 0xb6, 0x01, //0x00001d08 movzbl       (%r9), %eax
	0x3c, 0x22, //0x00001d0c cmpb         $34, %al
	0x0f, 0x84, 0x93, 0x00, 0x00, 0x00, //0x00001d0e je           LBB0_385
	0x3c, 0x5c, //0x00001d14 cmpb         $92, %al
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00001d16 je           LBB0_379
	0x3c, 0x1f, //0x00001d1c cmpb         $31, %al
	0x0f, 0x86, 0x51, 0x18, 0x00, 0x00, //0x00001d1e jbe          LBB0_603
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001d24 movq         $-1, %rax
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00001d2b movl         $1, %ecx
	0x49, 0x01, 0xc9, //0x00001d30 addq         %rcx, %r9
	0x49, 0x01, 0xc6, //0x00001d33 addq         %rax, %r14
	0x0f, 0x85, 0xcc, 0xff, 0xff, 0xff, //0x00001d36 jne          LBB0_375
	0xe9, 0x9e, 0x0b, 0x00, 0x00, //0x00001d3c jmp          LBB0_498
	//0x00001d41 LBB0_379
	0x49, 0x83, 0xfe, 0x01, //0x00001d41 cmpq         $1, %r14
	0x0f, 0x84, 0x15, 0x0a, 0x00, 0x00, //0x00001d45 je           LBB0_474
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00001d4b movq         $-2, %rax
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00001d52 movl         $2, %ecx
	0x48, 0x83, 0xfa, 0xff, //0x00001d57 cmpq         $-1, %rdx
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00001d5b je           LBB0_382
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001d61 movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x00001d65 movq         $-72(%rbp), %r12
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00001d69 jmp          LBB0_383
	//0x00001d6e LBB0_382
	0x4c, 0x89, 0xca, //0x00001d6e movq         %r9, %rdx
	0x4c, 0x8b, 0x65, 0xb8, //0x00001d71 movq         $-72(%rbp), %r12
	0x4c, 0x29, 0xe2, //0x00001d75 subq         %r12, %rdx
	0x48, 0x89, 0x55, 0xc8, //0x00001d78 movq         %rdx, $-56(%rbp)
	0x4c, 0x8b, 0x6d, 0xd0, //0x00001d7c movq         $-48(%rbp), %r13
	//0x00001d80 LBB0_383
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001d80 movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x00001d8a movq         $-64(%rbp), %rbx
	0x49, 0x01, 0xc9, //0x00001d8e addq         %rcx, %r9
	0x49, 0x01, 0xc6, //0x00001d91 addq         %rax, %r14
	0x0f, 0x85, 0x6e, 0xff, 0xff, 0xff, //0x00001d94 jne          LBB0_375
	0xe9, 0x40, 0x0b, 0x00, 0x00, //0x00001d9a jmp          LBB0_498
	//0x00001d9f LBB0_384
	0x0f, 0xbc, 0xc0, //0x00001d9f bsfl         %eax, %eax
	0xe9, 0x22, 0x02, 0x00, 0x00, //0x00001da2 jmp          LBB0_392
	//0x00001da7 LBB0_385
	0x4c, 0x03, 0x4d, 0x90, //0x00001da7 addq         $-112(%rbp), %r9
	0xe9, 0xf7, 0x06, 0x00, 0x00, //0x00001dab jmp          LBB0_454
	//0x00001db0 LBB0_386
	0x48, 0x8b, 0x45, 0xb8, //0x00001db0 movq         $-72(%rbp), %rax
	0x48, 0x8b, 0x55, 0xc0, //0x00001db4 movq         $-64(%rbp), %rdx
	0x4c, 0x8d, 0x34, 0x10, //0x00001db8 leaq         (%rax,%rdx), %r14
	0x49, 0x29, 0xce, //0x00001dbc subq         %rcx, %r14
	0x49, 0x29, 0xfe, //0x00001dbf subq         %rdi, %r14
	0xe9, 0x31, 0xfa, 0xff, 0xff, //0x00001dc2 jmp          LBB0_341
	//0x00001dc7 LBB0_239
	0x4d, 0x01, 0xe1, //0x00001dc7 addq         %r12, %r9
	0x49, 0x83, 0xfe, 0x20, //0x00001dca cmpq         $32, %r14
	0x48, 0x8b, 0x5d, 0xc0, //0x00001dce movq         $-64(%rbp), %rbx
	0x0f, 0x82, 0x68, 0x00, 0x00, 0x00, //0x00001dd2 jb           LBB0_244
	//0x00001dd8 LBB0_240
	0xf3, 0x41, 0x0f, 0x6f, 0x01, //0x00001dd8 movdqu       (%r9), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x49, 0x10, //0x00001ddd movdqu       $16(%r9), %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00001de3 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd1, //0x00001de7 pcmpeqb      %xmm9, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00001dec pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd1, //0x00001df0 movdqa       %xmm1, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd1, //0x00001df4 pcmpeqb      %xmm9, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00001df9 pmovmskb     %xmm2, %eax
	0x66, 0x41, 0x0f, 0x74, 0xc2, //0x00001dfd pcmpeqb      %xmm10, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00001e02 pmovmskb     %xmm0, %edx
	0x66, 0x41, 0x0f, 0x74, 0xca, //0x00001e06 pcmpeqb      %xmm10, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x00001e0b pmovmskb     %xmm1, %ecx
	0x48, 0xc1, 0xe0, 0x10, //0x00001e0f shlq         $16, %rax
	0x48, 0x09, 0xc7, //0x00001e13 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x10, //0x00001e16 shlq         $16, %rcx
	0x48, 0x09, 0xca, //0x00001e1a orq          %rcx, %rdx
	0x0f, 0x85, 0x0d, 0x08, 0x00, 0x00, //0x00001e1d jne          LBB0_464
	0x4d, 0x85, 0xdb, //0x00001e23 testq        %r11, %r11
	0x0f, 0x85, 0x21, 0x08, 0x00, 0x00, //0x00001e26 jne          LBB0_466
	0x45, 0x31, 0xdb, //0x00001e2c xorl         %r11d, %r11d
	0x48, 0x85, 0xff, //0x00001e2f testq        %rdi, %rdi
	0x0f, 0x85, 0x2d, 0xfb, 0xff, 0xff, //0x00001e32 jne          LBB0_76
	//0x00001e38 LBB0_243
	0x49, 0x83, 0xc1, 0x20, //0x00001e38 addq         $32, %r9
	0x49, 0x83, 0xc6, 0xe0, //0x00001e3c addq         $-32, %r14
	//0x00001e40 LBB0_244
	0x4d, 0x85, 0xdb, //0x00001e40 testq        %r11, %r11
	0x0f, 0x85, 0xa2, 0x08, 0x00, 0x00, //0x00001e43 jne          LBB0_470
	0x48, 0x8b, 0x55, 0xc8, //0x00001e49 movq         $-56(%rbp), %rdx
	0x4d, 0x85, 0xf6, //0x00001e4d testq        %r14, %r14
	0x0f, 0x84, 0x68, 0x02, 0x00, 0x00, //0x00001e50 je           LBB0_409
	//0x00001e56 LBB0_246
	0x31, 0xc9, //0x00001e56 xorl         %ecx, %ecx
	//0x00001e58 LBB0_247
	0x41, 0x0f, 0xb6, 0x04, 0x09, //0x00001e58 movzbl       (%r9,%rcx), %eax
	0x3c, 0x22, //0x00001e5d cmpb         $34, %al
	0x0f, 0x84, 0x52, 0x02, 0x00, 0x00, //0x00001e5f je           LBB0_408
	0x3c, 0x5c, //0x00001e65 cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00001e67 je           LBB0_387
	0x48, 0x83, 0xc1, 0x01, //0x00001e6d addq         $1, %rcx
	0x49, 0x39, 0xce, //0x00001e71 cmpq         %rcx, %r14
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x00001e74 jne          LBB0_247
	0xe9, 0x42, 0x03, 0x00, 0x00, //0x00001e7a jmp          LBB0_250
	//0x00001e7f LBB0_387
	0x49, 0x8d, 0x46, 0xff, //0x00001e7f leaq         $-1(%r14), %rax
	0x48, 0x39, 0xc8, //0x00001e83 cmpq         %rcx, %rax
	0x0f, 0x84, 0xd4, 0x08, 0x00, 0x00, //0x00001e86 je           LBB0_474
	0x48, 0x8b, 0x45, 0x80, //0x00001e8c movq         $-128(%rbp), %rax
	0x4c, 0x01, 0xc8, //0x00001e90 addq         %r9, %rax
	0x48, 0x01, 0xc8, //0x00001e93 addq         %rcx, %rax
	0x48, 0x83, 0xfa, 0xff, //0x00001e96 cmpq         $-1, %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x00001e9a movq         $-56(%rbp), %rsi
	0x48, 0x0f, 0x44, 0xf0, //0x00001e9e cmoveq       %rax, %rsi
	0x48, 0x89, 0x75, 0xc8, //0x00001ea2 movq         %rsi, $-56(%rbp)
	0x48, 0x0f, 0x44, 0xd0, //0x00001ea6 cmoveq       %rax, %rdx
	0x49, 0x01, 0xc9, //0x00001eaa addq         %rcx, %r9
	0x49, 0x83, 0xc1, 0x02, //0x00001ead addq         $2, %r9
	0x4c, 0x89, 0xf0, //0x00001eb1 movq         %r14, %rax
	0x48, 0x29, 0xc8, //0x00001eb4 subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00001eb7 addq         $-2, %rax
	0x49, 0x83, 0xc6, 0xfe, //0x00001ebb addq         $-2, %r14
	0x49, 0x39, 0xce, //0x00001ebf cmpq         %rcx, %r14
	0x49, 0x89, 0xc6, //0x00001ec2 movq         %rax, %r14
	0x0f, 0x85, 0x8b, 0xff, 0xff, 0xff, //0x00001ec5 jne          LBB0_246
	0xe9, 0x90, 0x08, 0x00, 0x00, //0x00001ecb jmp          LBB0_474
	//0x00001ed0 LBB0_312
	0x4d, 0x01, 0xe1, //0x00001ed0 addq         %r12, %r9
	0x49, 0x83, 0xfe, 0x20, //0x00001ed3 cmpq         $32, %r14
	0x0f, 0x82, 0x08, 0x01, 0x00, 0x00, //0x00001ed7 jb           LBB0_395
	//0x00001edd LBB0_313
	0xf3, 0x41, 0x0f, 0x6f, 0x29, //0x00001edd movdqu       (%r9), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x41, 0x10, //0x00001ee2 movdqu       $16(%r9), %xmm0
	0x66, 0x0f, 0x6f, 0xcd, //0x00001ee8 movdqa       %xmm5, %xmm1
	0x66, 0x41, 0x0f, 0x74, 0xc9, //0x00001eec pcmpeqb      %xmm9, %xmm1
	0x66, 0x0f, 0xd7, 0xf1, //0x00001ef1 pmovmskb     %xmm1, %esi
	0x66, 0x0f, 0x6f, 0xc8, //0x00001ef5 movdqa       %xmm0, %xmm1
	0x66, 0x0f, 0x6f, 0xd0, //0x00001ef9 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0xda, 0xd7, //0x00001efd pminub       %xmm15, %xmm2
	0x66, 0x0f, 0x74, 0xd0, //0x00001f02 pcmpeqb      %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xc1, //0x00001f06 pcmpeqb      %xmm9, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00001f0b pmovmskb     %xmm0, %eax
	0x66, 0x0f, 0x6f, 0xc5, //0x00001f0f movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0x74, 0xc2, //0x00001f13 pcmpeqb      %xmm10, %xmm0
	0x66, 0x0f, 0xd7, 0xd0, //0x00001f18 pmovmskb     %xmm0, %edx
	0x66, 0x41, 0x0f, 0x74, 0xca, //0x00001f1c pcmpeqb      %xmm10, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x00001f21 pmovmskb     %xmm1, %ecx
	0x66, 0x0f, 0xd7, 0xfa, //0x00001f25 pmovmskb     %xmm2, %edi
	0x48, 0xc1, 0xe0, 0x10, //0x00001f29 shlq         $16, %rax
	0x48, 0x09, 0xc6, //0x00001f2d orq          %rax, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x00001f30 shlq         $16, %rcx
	0x48, 0xc1, 0xe7, 0x10, //0x00001f34 shlq         $16, %rdi
	0x48, 0x09, 0xca, //0x00001f38 orq          %rcx, %rdx
	0x48, 0x8b, 0x5d, 0xc0, //0x00001f3b movq         $-64(%rbp), %rbx
	0x0f, 0x85, 0x4d, 0x07, 0x00, 0x00, //0x00001f3f jne          LBB0_467
	0x4d, 0x85, 0xdb, //0x00001f45 testq        %r11, %r11
	0x0f, 0x85, 0x61, 0x07, 0x00, 0x00, //0x00001f48 jne          LBB0_469
	0x45, 0x31, 0xdb, //0x00001f4e xorl         %r11d, %r11d
	//0x00001f51 LBB0_316
	0x66, 0x0f, 0x6f, 0xc5, //0x00001f51 movdqa       %xmm5, %xmm0
	0x66, 0x41, 0x0f, 0xda, 0xc7, //0x00001f55 pminub       %xmm15, %xmm0
	0x66, 0x0f, 0x74, 0xc5, //0x00001f5a pcmpeqb      %xmm5, %xmm0
	0x66, 0x0f, 0xd7, 0xc0, //0x00001f5e pmovmskb     %xmm0, %eax
	0x48, 0x09, 0xc7, //0x00001f62 orq          %rax, %rdi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00001f65 movl         $64, %ecx
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00001f6a movl         $64, %eax
	0x48, 0x85, 0xf6, //0x00001f6f testq        %rsi, %rsi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001f72 je           LBB0_318
	0x48, 0x0f, 0xbc, 0xc6, //0x00001f78 bsfq         %rsi, %rax
	//0x00001f7c LBB0_318
	0x48, 0x85, 0xff, //0x00001f7c testq        %rdi, %rdi
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001f7f je           LBB0_320
	0x48, 0x0f, 0xbc, 0xcf, //0x00001f85 bsfq         %rdi, %rcx
	//0x00001f89 LBB0_320
	0x48, 0x85, 0xf6, //0x00001f89 testq        %rsi, %rsi
	0x0f, 0x84, 0x42, 0x00, 0x00, 0x00, //0x00001f8c je           LBB0_393
	0x48, 0x39, 0xc1, //0x00001f92 cmpq         %rax, %rcx
	0x0f, 0x83, 0x9e, 0xfa, 0xff, 0xff, //0x00001f95 jae          LBB0_166
	0xe9, 0x0b, 0x16, 0x00, 0x00, //0x00001f9b jmp          LBB0_322
	//0x00001fa0 LBB0_389
	0x89, 0xd0, //0x00001fa0 movl         %edx, %eax
	0xe9, 0x22, 0x00, 0x00, 0x00, //0x00001fa2 jmp          LBB0_392
	//0x00001fa7 LBB0_390
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00001fa7 movq         $-1, %r14
	0x48, 0x89, 0xf1, //0x00001fae movq         %rsi, %rcx
	0x4d, 0x89, 0xc2, //0x00001fb1 movq         %r8, %r10
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001fb4 movq         $-1, %r12
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00001fbb movq         $-1, %r15
	0xe9, 0xea, 0xe8, 0xff, 0xff, //0x00001fc2 jmp          LBB0_114
	//0x00001fc7 LBB0_391
	0x89, 0xd8, //0x00001fc7 movl         %ebx, %eax
	//0x00001fc9 LBB0_392
	0x49, 0xf7, 0xd6, //0x00001fc9 notq         %r14
	0x49, 0x29, 0xc6, //0x00001fcc subq         %rax, %r14
	0xe9, 0x24, 0xf8, 0xff, 0xff, //0x00001fcf jmp          LBB0_341
	//0x00001fd4 LBB0_393
	0x48, 0x85, 0xff, //0x00001fd4 testq        %rdi, %rdi
	0x0f, 0x85, 0xd6, 0x15, 0x00, 0x00, //0x00001fd7 jne          LBB0_606
	0x49, 0x83, 0xc1, 0x20, //0x00001fdd addq         $32, %r9
	0x49, 0x83, 0xc6, 0xe0, //0x00001fe1 addq         $-32, %r14
	//0x00001fe5 LBB0_395
	0x4d, 0x85, 0xdb, //0x00001fe5 testq        %r11, %r11
	0x0f, 0x85, 0x3a, 0x07, 0x00, 0x00, //0x00001fe8 jne          LBB0_472
	0x48, 0x8b, 0x55, 0xc8, //0x00001fee movq         $-56(%rbp), %rdx
	0x4d, 0x85, 0xf6, //0x00001ff2 testq        %r14, %r14
	0x0f, 0x84, 0x65, 0x07, 0x00, 0x00, //0x00001ff5 je           LBB0_474
	//0x00001ffb LBB0_397
	0x41, 0x0f, 0xb6, 0x01, //0x00001ffb movzbl       (%r9), %eax
	0x3c, 0x22, //0x00001fff cmpb         $34, %al
	0x0f, 0x84, 0x9b, 0x00, 0x00, 0x00, //0x00002001 je           LBB0_407
	0x3c, 0x5c, //0x00002007 cmpb         $92, %al
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00002009 je           LBB0_402
	0x3c, 0x1f, //0x0000200f cmpb         $31, %al
	0x0f, 0x86, 0xab, 0x15, 0x00, 0x00, //0x00002011 jbe          LBB0_608
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002017 movq         $-1, %rax
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000201e movl         $1, %ecx
	//0x00002023 LBB0_401
	0x49, 0x01, 0xc9, //0x00002023 addq         %rcx, %r9
	0x49, 0x01, 0xc6, //0x00002026 addq         %rax, %r14
	0x0f, 0x85, 0xcc, 0xff, 0xff, 0xff, //0x00002029 jne          LBB0_397
	0xe9, 0x2c, 0x07, 0x00, 0x00, //0x0000202f jmp          LBB0_474
	//0x00002034 LBB0_402
	0x49, 0x83, 0xfe, 0x01, //0x00002034 cmpq         $1, %r14
	0x0f, 0x84, 0x22, 0x07, 0x00, 0x00, //0x00002038 je           LBB0_474
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000203e movq         $-2, %rax
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00002045 movl         $2, %ecx
	0x48, 0x83, 0xfa, 0xff, //0x0000204a cmpq         $-1, %rdx
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x0000204e jne          LBB0_401
	0x4c, 0x89, 0xca, //0x00002054 movq         %r9, %rdx
	0x48, 0x2b, 0x55, 0xb8, //0x00002057 subq         $-72(%rbp), %rdx
	0x48, 0x89, 0x55, 0xc8, //0x0000205b movq         %rdx, $-56(%rbp)
	0xe9, 0xbf, 0xff, 0xff, 0xff, //0x0000205f jmp          LBB0_401
	//0x00002064 LBB0_405
	0x4d, 0x01, 0xe1, //0x00002064 addq         %r12, %r9
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00002067 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x0000206f xorl         %r11d, %r11d
	0x49, 0x83, 0xfe, 0x20, //0x00002072 cmpq         $32, %r14
	0x48, 0x8b, 0x5d, 0xc0, //0x00002076 movq         $-64(%rbp), %rbx
	0x0f, 0x83, 0x85, 0xf8, 0xff, 0xff, //0x0000207a jae          LBB0_73
	0xe9, 0xb1, 0x01, 0x00, 0x00, //0x00002080 jmp          LBB0_422
	//0x00002085 LBB0_406
	0x4d, 0x01, 0xe1, //0x00002085 addq         %r12, %r9
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00002088 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x00002090 xorl         %r11d, %r11d
	0x49, 0x83, 0xfe, 0x20, //0x00002093 cmpq         $32, %r14
	0x0f, 0x83, 0xe2, 0xf8, 0xff, 0xff, //0x00002097 jae          LBB0_157
	0xe9, 0x6d, 0xfb, 0xff, 0xff, //0x0000209d jmp          LBB0_360
	//0x000020a2 LBB0_407
	0x4c, 0x03, 0x4d, 0x90, //0x000020a2 addq         $-112(%rbp), %r9
	0x4c, 0x8b, 0x6d, 0xd0, //0x000020a6 movq         $-48(%rbp), %r13
	0x48, 0x8b, 0x7d, 0xb0, //0x000020aa movq         $-80(%rbp), %rdi
	0x4c, 0x8b, 0x65, 0xb8, //0x000020ae movq         $-72(%rbp), %r12
	0xe9, 0xb5, 0xe5, 0xff, 0xff, //0x000020b2 jmp          LBB0_82
	//0x000020b7 LBB0_408
	0x49, 0x01, 0xc9, //0x000020b7 addq         %rcx, %r9
	0x49, 0x83, 0xc1, 0x01, //0x000020ba addq         $1, %r9
	//0x000020be LBB0_409
	0x4c, 0x8b, 0x65, 0xb8, //0x000020be movq         $-72(%rbp), %r12
	0x4d, 0x29, 0xe1, //0x000020c2 subq         %r12, %r9
	0xe9, 0x9a, 0xe5, 0xff, 0xff, //0x000020c5 jmp          LBB0_81
	//0x000020ca LBB0_410
	0x4d, 0x01, 0xe1, //0x000020ca addq         %r12, %r9
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x000020cd movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x000020d5 xorl         %r11d, %r11d
	0x49, 0x83, 0xfe, 0x20, //0x000020d8 cmpq         $32, %r14
	0x48, 0x8b, 0x5d, 0xc0, //0x000020dc movq         $-64(%rbp), %rbx
	0x0f, 0x83, 0xbe, 0xf9, 0xff, 0xff, //0x000020e0 jae          LBB0_139
	0xe9, 0xfe, 0x02, 0x00, 0x00, //0x000020e6 jmp          LBB0_442
	//0x000020eb LBB0_411
	0x4d, 0x01, 0xe1, //0x000020eb addq         %r12, %r9
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x000020ee movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x000020f6 xorl         %r11d, %r11d
	0x49, 0x83, 0xfe, 0x20, //0x000020f9 cmpq         $32, %r14
	0x0f, 0x83, 0x17, 0xfa, 0xff, 0xff, //0x000020fd jae          LBB0_183
	//0x00002103 LBB0_412
	0x48, 0x8b, 0x5d, 0xc0, //0x00002103 movq         $-64(%rbp), %rbx
	0x4d, 0x85, 0xdb, //0x00002107 testq        %r11, %r11
	0x0f, 0x84, 0xdd, 0xfb, 0xff, 0xff, //0x0000210a je           LBB0_374
	//0x00002110 LBB0_413
	0x4d, 0x85, 0xf6, //0x00002110 testq        %r14, %r14
	0x0f, 0x84, 0x47, 0x06, 0x00, 0x00, //0x00002113 je           LBB0_474
	0x48, 0x8b, 0x45, 0x88, //0x00002119 movq         $-120(%rbp), %rax
	0x4c, 0x01, 0xc8, //0x0000211d addq         %r9, %rax
	0x48, 0x8b, 0x4d, 0xc8, //0x00002120 movq         $-56(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x00002124 cmpq         $-1, %rcx
	0x48, 0x89, 0xca, //0x00002128 movq         %rcx, %rdx
	0x48, 0x0f, 0x44, 0xc8, //0x0000212b cmoveq       %rax, %rcx
	0x48, 0x0f, 0x44, 0xd0, //0x0000212f cmoveq       %rax, %rdx
	0x49, 0x83, 0xc1, 0x01, //0x00002133 addq         $1, %r9
	0x49, 0x83, 0xc6, 0xff, //0x00002137 addq         $-1, %r14
	0x48, 0x89, 0x4d, 0xc8, //0x0000213b movq         %rcx, $-56(%rbp)
	0x4c, 0x8b, 0x6d, 0xd0, //0x0000213f movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x00002143 movq         $-72(%rbp), %r12
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002147 movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x00002151 movq         $-64(%rbp), %rbx
	0x4d, 0x85, 0xf6, //0x00002155 testq        %r14, %r14
	0x0f, 0x85, 0xaa, 0xfb, 0xff, 0xff, //0x00002158 jne          LBB0_375
	0xe9, 0x7c, 0x07, 0x00, 0x00, //0x0000215e jmp          LBB0_498
	//0x00002163 LBB0_415
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002163 movq         $-1, %r8
	0x4c, 0x89, 0xf9, //0x0000216a movq         %r15, %rcx
	0x4c, 0x89, 0xde, //0x0000216d movq         %r11, %rsi
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00002170 movq         $-1, %r13
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00002177 movq         $-1, %r12
	0xe9, 0xc3, 0xf2, 0xff, 0xff, //0x0000217e jmp          LBB0_279
	//0x00002183 LBB0_416
	0x4d, 0x01, 0xe1, //0x00002183 addq         %r12, %r9
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x00002186 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x0000218e xorl         %r11d, %r11d
	0x49, 0x83, 0xfe, 0x20, //0x00002191 cmpq         $32, %r14
	0x48, 0x8b, 0x5d, 0xc0, //0x00002195 movq         $-64(%rbp), %rbx
	0x0f, 0x83, 0x39, 0xfc, 0xff, 0xff, //0x00002199 jae          LBB0_240
	0xe9, 0x9c, 0xfc, 0xff, 0xff, //0x0000219f jmp          LBB0_244
	//0x000021a4 LBB0_417
	0x4d, 0x01, 0xe1, //0x000021a4 addq         %r12, %r9
	0x48, 0xc7, 0x45, 0xc8, 0xff, 0xff, 0xff, 0xff, //0x000021a7 movq         $-1, $-56(%rbp)
	0x45, 0x31, 0xdb, //0x000021af xorl         %r11d, %r11d
	0x49, 0x83, 0xfe, 0x20, //0x000021b2 cmpq         $32, %r14
	0x0f, 0x83, 0x21, 0xfd, 0xff, 0xff, //0x000021b6 jae          LBB0_313
	0xe9, 0x24, 0xfe, 0xff, 0xff, //0x000021bc jmp          LBB0_395
	//0x000021c1 LBB0_250
	0x3c, 0x22, //0x000021c1 cmpb         $34, %al
	0x0f, 0x85, 0x97, 0x05, 0x00, 0x00, //0x000021c3 jne          LBB0_474
	0x4d, 0x01, 0xf1, //0x000021c9 addq         %r14, %r9
	0xe9, 0xed, 0xfe, 0xff, 0xff, //0x000021cc jmp          LBB0_409
	//0x000021d1 LBB0_418
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x000021d1 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x000021d6 jne          LBB0_420
	0x4c, 0x89, 0xc8, //0x000021dc movq         %r9, %rax
	0x48, 0x2b, 0x45, 0xb8, //0x000021df subq         $-72(%rbp), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x000021e3 bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x000021e7 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x000021ea movq         %rcx, $-56(%rbp)
	//0x000021ee LBB0_420
	0x44, 0x89, 0xd8, //0x000021ee movl         %r11d, %eax
	0xf7, 0xd0, //0x000021f1 notl         %eax
	0x21, 0xd0, //0x000021f3 andl         %edx, %eax
	0x41, 0x8d, 0x0c, 0x43, //0x000021f5 leal         (%r11,%rax,2), %ecx
	0x8d, 0x34, 0x00, //0x000021f9 leal         (%rax,%rax), %esi
	0xf7, 0xd6, //0x000021fc notl         %esi
	0x21, 0xd6, //0x000021fe andl         %edx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002200 andl         $-1431655766, %esi
	0x45, 0x31, 0xdb, //0x00002206 xorl         %r11d, %r11d
	0x01, 0xc6, //0x00002209 addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc3, //0x0000220b setb         %r11b
	0x01, 0xf6, //0x0000220f addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x00002211 xorl         $1431655765, %esi
	0x21, 0xce, //0x00002217 andl         %ecx, %esi
	0xf7, 0xd6, //0x00002219 notl         %esi
	0x21, 0xf7, //0x0000221b andl         %esi, %edi
	0x4c, 0x8b, 0x65, 0xb8, //0x0000221d movq         $-72(%rbp), %r12
	0x48, 0x8b, 0x5d, 0xc0, //0x00002221 movq         $-64(%rbp), %rbx
	0x48, 0x85, 0xff, //0x00002225 testq        %rdi, %rdi
	0x0f, 0x85, 0x37, 0xf7, 0xff, 0xff, //0x00002228 jne          LBB0_76
	//0x0000222e LBB0_421
	0x49, 0x83, 0xc1, 0x20, //0x0000222e addq         $32, %r9
	0x49, 0x83, 0xc6, 0xe0, //0x00002232 addq         $-32, %r14
	//0x00002236 LBB0_422
	0x4d, 0x85, 0xdb, //0x00002236 testq        %r11, %r11
	0x0f, 0x85, 0xf8, 0x02, 0x00, 0x00, //0x00002239 jne          LBB0_458
	0x48, 0x8b, 0x55, 0xc8, //0x0000223f movq         $-56(%rbp), %rdx
	0x4c, 0x8b, 0x6d, 0xd0, //0x00002243 movq         $-48(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002247 movabsq      $4294977024, %r11
	0x4d, 0x85, 0xf6, //0x00002251 testq        %r14, %r14
	0x0f, 0x84, 0x97, 0x00, 0x00, 0x00, //0x00002254 je           LBB0_433
	//0x0000225a LBB0_424
	0x31, 0xc9, //0x0000225a xorl         %ecx, %ecx
	//0x0000225c LBB0_425
	0x41, 0x0f, 0xb6, 0x04, 0x09, //0x0000225c movzbl       (%r9,%rcx), %eax
	0x3c, 0x22, //0x00002261 cmpb         $34, %al
	0x0f, 0x84, 0x81, 0x00, 0x00, 0x00, //0x00002263 je           LBB0_432
	0x3c, 0x5c, //0x00002269 cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000226b je           LBB0_430
	0x48, 0x83, 0xc1, 0x01, //0x00002271 addq         $1, %rcx
	0x49, 0x39, 0xce, //0x00002275 cmpq         %rcx, %r14
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x00002278 jne          LBB0_425
	0xe9, 0x82, 0x00, 0x00, 0x00, //0x0000227e jmp          LBB0_428
	//0x00002283 LBB0_430
	0x49, 0x8d, 0x46, 0xff, //0x00002283 leaq         $-1(%r14), %rax
	0x48, 0x39, 0xc8, //0x00002287 cmpq         %rcx, %rax
	0x0f, 0x84, 0xd0, 0x04, 0x00, 0x00, //0x0000228a je           LBB0_474
	0x48, 0x8b, 0x45, 0x80, //0x00002290 movq         $-128(%rbp), %rax
	0x4c, 0x01, 0xc8, //0x00002294 addq         %r9, %rax
	0x48, 0x01, 0xc8, //0x00002297 addq         %rcx, %rax
	0x48, 0x83, 0xfa, 0xff, //0x0000229a cmpq         $-1, %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x0000229e movq         $-56(%rbp), %rsi
	0x48, 0x0f, 0x44, 0xf0, //0x000022a2 cmoveq       %rax, %rsi
	0x48, 0x89, 0x75, 0xc8, //0x000022a6 movq         %rsi, $-56(%rbp)
	0x48, 0x0f, 0x44, 0xd0, //0x000022aa cmoveq       %rax, %rdx
	0x49, 0x01, 0xc9, //0x000022ae addq         %rcx, %r9
	0x49, 0x83, 0xc1, 0x02, //0x000022b1 addq         $2, %r9
	0x4c, 0x89, 0xf0, //0x000022b5 movq         %r14, %rax
	0x48, 0x29, 0xc8, //0x000022b8 subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000022bb addq         $-2, %rax
	0x49, 0x83, 0xc6, 0xfe, //0x000022bf addq         $-2, %r14
	0x49, 0x39, 0xce, //0x000022c3 cmpq         %rcx, %r14
	0x49, 0x89, 0xc6, //0x000022c6 movq         %rax, %r14
	0x4c, 0x8b, 0x6d, 0xd0, //0x000022c9 movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x000022cd movq         $-72(%rbp), %r12
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000022d1 movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x000022db movq         $-64(%rbp), %rbx
	0x0f, 0x85, 0x75, 0xff, 0xff, 0xff, //0x000022df jne          LBB0_424
	0xe9, 0xf5, 0x05, 0x00, 0x00, //0x000022e5 jmp          LBB0_498
	//0x000022ea LBB0_432
	0x49, 0x01, 0xc9, //0x000022ea addq         %rcx, %r9
	0x49, 0x83, 0xc1, 0x01, //0x000022ed addq         $1, %r9
	//0x000022f1 LBB0_433
	0x4d, 0x29, 0xe1, //0x000022f1 subq         %r12, %r9
	//0x000022f4 LBB0_434
	0x48, 0x8b, 0x7d, 0xb0, //0x000022f4 movq         $-80(%rbp), %rdi
	0x48, 0x8b, 0x55, 0xa0, //0x000022f8 movq         $-96(%rbp), %rdx
	0x4c, 0x8b, 0x75, 0xa8, //0x000022fc movq         $-88(%rbp), %r14
	0xe9, 0xe1, 0xec, 0xff, 0xff, //0x00002300 jmp          LBB0_214
	//0x00002305 LBB0_428
	0x3c, 0x22, //0x00002305 cmpb         $34, %al
	0x0f, 0x85, 0x53, 0x04, 0x00, 0x00, //0x00002307 jne          LBB0_474
	0x4d, 0x01, 0xf1, //0x0000230d addq         %r14, %r9
	0x4c, 0x8b, 0x6d, 0xd0, //0x00002310 movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x00002314 movq         $-72(%rbp), %r12
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002318 movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x00002322 movq         $-64(%rbp), %rbx
	0xe9, 0xc6, 0xff, 0xff, 0xff, //0x00002326 jmp          LBB0_433
	//0x0000232b LBB0_435
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x0000232b cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00002330 jne          LBB0_437
	0x4c, 0x89, 0xc8, //0x00002336 movq         %r9, %rax
	0x48, 0x2b, 0x45, 0xb8, //0x00002339 subq         $-72(%rbp), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x0000233d bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x00002341 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00002344 movq         %rcx, $-56(%rbp)
	//0x00002348 LBB0_437
	0x44, 0x89, 0xd8, //0x00002348 movl         %r11d, %eax
	0xf7, 0xd0, //0x0000234b notl         %eax
	0x21, 0xd0, //0x0000234d andl         %edx, %eax
	0x41, 0x8d, 0x0c, 0x43, //0x0000234f leal         (%r11,%rax,2), %ecx
	0x8d, 0x1c, 0x00, //0x00002353 leal         (%rax,%rax), %ebx
	0xf7, 0xd3, //0x00002356 notl         %ebx
	0x21, 0xd3, //0x00002358 andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000235a andl         $-1431655766, %ebx
	0x45, 0x31, 0xdb, //0x00002360 xorl         %r11d, %r11d
	0x01, 0xc3, //0x00002363 addl         %eax, %ebx
	0x41, 0x0f, 0x92, 0xc3, //0x00002365 setb         %r11b
	0x01, 0xdb, //0x00002369 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x0000236b xorl         $1431655765, %ebx
	0x21, 0xcb, //0x00002371 andl         %ecx, %ebx
	0xf7, 0xd3, //0x00002373 notl         %ebx
	0x21, 0xde, //0x00002375 andl         %ebx, %esi
	0x4c, 0x8b, 0x65, 0xb8, //0x00002377 movq         $-72(%rbp), %r12
	0x48, 0x8b, 0x5d, 0xc0, //0x0000237b movq         $-64(%rbp), %rbx
	0xe9, 0x6b, 0xf6, 0xff, 0xff, //0x0000237f jmp          LBB0_160
	//0x00002384 LBB0_438
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00002384 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00002389 jne          LBB0_440
	0x4c, 0x89, 0xc8, //0x0000238f movq         %r9, %rax
	0x48, 0x2b, 0x45, 0xb8, //0x00002392 subq         $-72(%rbp), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x00002396 bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x0000239a addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x0000239d movq         %rcx, $-56(%rbp)
	//0x000023a1 LBB0_440
	0x44, 0x89, 0xd8, //0x000023a1 movl         %r11d, %eax
	0xf7, 0xd0, //0x000023a4 notl         %eax
	0x21, 0xd0, //0x000023a6 andl         %edx, %eax
	0x41, 0x8d, 0x0c, 0x43, //0x000023a8 leal         (%r11,%rax,2), %ecx
	0x8d, 0x34, 0x00, //0x000023ac leal         (%rax,%rax), %esi
	0xf7, 0xd6, //0x000023af notl         %esi
	0x21, 0xd6, //0x000023b1 andl         %edx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x000023b3 andl         $-1431655766, %esi
	0x45, 0x31, 0xdb, //0x000023b9 xorl         %r11d, %r11d
	0x01, 0xc6, //0x000023bc addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc3, //0x000023be setb         %r11b
	0x01, 0xf6, //0x000023c2 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x000023c4 xorl         $1431655765, %esi
	0x21, 0xce, //0x000023ca andl         %ecx, %esi
	0xf7, 0xd6, //0x000023cc notl         %esi
	0x21, 0xf7, //0x000023ce andl         %esi, %edi
	0x4c, 0x8b, 0x65, 0xb8, //0x000023d0 movq         $-72(%rbp), %r12
	0x48, 0x8b, 0x5d, 0xc0, //0x000023d4 movq         $-64(%rbp), %rbx
	0x48, 0x85, 0xff, //0x000023d8 testq        %rdi, %rdi
	0x0f, 0x85, 0x23, 0xf7, 0xff, 0xff, //0x000023db jne          LBB0_142
	//0x000023e1 LBB0_441
	0x49, 0x83, 0xc1, 0x20, //0x000023e1 addq         $32, %r9
	0x49, 0x83, 0xc6, 0xe0, //0x000023e5 addq         $-32, %r14
	//0x000023e9 LBB0_442
	0x4d, 0x85, 0xdb, //0x000023e9 testq        %r11, %r11
	0x0f, 0x85, 0xeb, 0x01, 0x00, 0x00, //0x000023ec jne          LBB0_462
	0x48, 0x8b, 0x55, 0xc8, //0x000023f2 movq         $-56(%rbp), %rdx
	0x4c, 0x8b, 0x6d, 0xd0, //0x000023f6 movq         $-48(%rbp), %r13
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000023fa movabsq      $4294977024, %r11
	0x4d, 0x85, 0xf6, //0x00002404 testq        %r14, %r14
	0x0f, 0x84, 0x97, 0x00, 0x00, 0x00, //0x00002407 je           LBB0_453
	//0x0000240d LBB0_444
	0x31, 0xc9, //0x0000240d xorl         %ecx, %ecx
	//0x0000240f LBB0_445
	0x41, 0x0f, 0xb6, 0x04, 0x09, //0x0000240f movzbl       (%r9,%rcx), %eax
	0x3c, 0x22, //0x00002414 cmpb         $34, %al
	0x0f, 0x84, 0x81, 0x00, 0x00, 0x00, //0x00002416 je           LBB0_452
	0x3c, 0x5c, //0x0000241c cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000241e je           LBB0_450
	0x48, 0x83, 0xc1, 0x01, //0x00002424 addq         $1, %rcx
	0x49, 0x39, 0xce, //0x00002428 cmpq         %rcx, %r14
	0x0f, 0x85, 0xde, 0xff, 0xff, 0xff, //0x0000242b jne          LBB0_445
	0xe9, 0x82, 0x00, 0x00, 0x00, //0x00002431 jmp          LBB0_448
	//0x00002436 LBB0_450
	0x49, 0x8d, 0x46, 0xff, //0x00002436 leaq         $-1(%r14), %rax
	0x48, 0x39, 0xc8, //0x0000243a cmpq         %rcx, %rax
	0x0f, 0x84, 0x1d, 0x03, 0x00, 0x00, //0x0000243d je           LBB0_474
	0x48, 0x8b, 0x45, 0x80, //0x00002443 movq         $-128(%rbp), %rax
	0x4c, 0x01, 0xc8, //0x00002447 addq         %r9, %rax
	0x48, 0x01, 0xc8, //0x0000244a addq         %rcx, %rax
	0x48, 0x83, 0xfa, 0xff, //0x0000244d cmpq         $-1, %rdx
	0x48, 0x8b, 0x75, 0xc8, //0x00002451 movq         $-56(%rbp), %rsi
	0x48, 0x0f, 0x44, 0xf0, //0x00002455 cmoveq       %rax, %rsi
	0x48, 0x89, 0x75, 0xc8, //0x00002459 movq         %rsi, $-56(%rbp)
	0x48, 0x0f, 0x44, 0xd0, //0x0000245d cmoveq       %rax, %rdx
	0x49, 0x01, 0xc9, //0x00002461 addq         %rcx, %r9
	0x49, 0x83, 0xc1, 0x02, //0x00002464 addq         $2, %r9
	0x4c, 0x89, 0xf0, //0x00002468 movq         %r14, %rax
	0x48, 0x29, 0xc8, //0x0000246b subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x0000246e addq         $-2, %rax
	0x49, 0x83, 0xc6, 0xfe, //0x00002472 addq         $-2, %r14
	0x49, 0x39, 0xce, //0x00002476 cmpq         %rcx, %r14
	0x49, 0x89, 0xc6, //0x00002479 movq         %rax, %r14
	0x4c, 0x8b, 0x6d, 0xd0, //0x0000247c movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x00002480 movq         $-72(%rbp), %r12
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002484 movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x0000248e movq         $-64(%rbp), %rbx
	0x0f, 0x85, 0x75, 0xff, 0xff, 0xff, //0x00002492 jne          LBB0_444
	0xe9, 0x42, 0x04, 0x00, 0x00, //0x00002498 jmp          LBB0_498
	//0x0000249d LBB0_452
	0x49, 0x01, 0xc9, //0x0000249d addq         %rcx, %r9
	0x49, 0x83, 0xc1, 0x01, //0x000024a0 addq         $1, %r9
	//0x000024a4 LBB0_453
	0x4d, 0x29, 0xe1, //0x000024a4 subq         %r12, %r9
	//0x000024a7 LBB0_454
	0x48, 0x8b, 0x7d, 0xb0, //0x000024a7 movq         $-80(%rbp), %rdi
	0x48, 0x8b, 0x55, 0xa0, //0x000024ab movq         $-96(%rbp), %rdx
	0x4c, 0x8b, 0x75, 0xa8, //0x000024af movq         $-88(%rbp), %r14
	0xe9, 0xa8, 0xeb, 0xff, 0xff, //0x000024b3 jmp          LBB0_222
	//0x000024b8 LBB0_448
	0x3c, 0x22, //0x000024b8 cmpb         $34, %al
	0x0f, 0x85, 0xa0, 0x02, 0x00, 0x00, //0x000024ba jne          LBB0_474
	0x4d, 0x01, 0xf1, //0x000024c0 addq         %r14, %r9
	0x4c, 0x8b, 0x6d, 0xd0, //0x000024c3 movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x000024c7 movq         $-72(%rbp), %r12
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000024cb movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x000024d5 movq         $-64(%rbp), %rbx
	0xe9, 0xc6, 0xff, 0xff, 0xff, //0x000024d9 jmp          LBB0_453
	//0x000024de LBB0_455
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x000024de cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x000024e3 jne          LBB0_457
	0x4c, 0x89, 0xc8, //0x000024e9 movq         %r9, %rax
	0x48, 0x2b, 0x45, 0xb8, //0x000024ec subq         $-72(%rbp), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x000024f0 bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x000024f4 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x000024f7 movq         %rcx, $-56(%rbp)
	//0x000024fb LBB0_457
	0x44, 0x89, 0xd8, //0x000024fb movl         %r11d, %eax
	0xf7, 0xd0, //0x000024fe notl         %eax
	0x21, 0xd0, //0x00002500 andl         %edx, %eax
	0x41, 0x8d, 0x0c, 0x43, //0x00002502 leal         (%r11,%rax,2), %ecx
	0x8d, 0x1c, 0x00, //0x00002506 leal         (%rax,%rax), %ebx
	0xf7, 0xd3, //0x00002509 notl         %ebx
	0x21, 0xd3, //0x0000250b andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000250d andl         $-1431655766, %ebx
	0x45, 0x31, 0xdb, //0x00002513 xorl         %r11d, %r11d
	0x01, 0xc3, //0x00002516 addl         %eax, %ebx
	0x41, 0x0f, 0x92, 0xc3, //0x00002518 setb         %r11b
	0x01, 0xdb, //0x0000251c addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x0000251e xorl         $1431655765, %ebx
	0x21, 0xcb, //0x00002524 andl         %ecx, %ebx
	0xf7, 0xd3, //0x00002526 notl         %ebx
	0x21, 0xde, //0x00002528 andl         %ebx, %esi
	0x4c, 0x8b, 0x65, 0xb8, //0x0000252a movq         $-72(%rbp), %r12
	0x48, 0x8b, 0x5d, 0xc0, //0x0000252e movq         $-64(%rbp), %rbx
	0xe9, 0x57, 0xf6, 0xff, 0xff, //0x00002532 jmp          LBB0_186
	//0x00002537 LBB0_458
	0x4d, 0x85, 0xf6, //0x00002537 testq        %r14, %r14
	0x0f, 0x84, 0x20, 0x02, 0x00, 0x00, //0x0000253a je           LBB0_474
	0x48, 0x8b, 0x45, 0x88, //0x00002540 movq         $-120(%rbp), %rax
	0x4c, 0x01, 0xc8, //0x00002544 addq         %r9, %rax
	0x48, 0x8b, 0x4d, 0xc8, //0x00002547 movq         $-56(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x0000254b cmpq         $-1, %rcx
	0x48, 0x89, 0xca, //0x0000254f movq         %rcx, %rdx
	0x48, 0x0f, 0x44, 0xc8, //0x00002552 cmoveq       %rax, %rcx
	0x48, 0x0f, 0x44, 0xd0, //0x00002556 cmoveq       %rax, %rdx
	0x49, 0x83, 0xc1, 0x01, //0x0000255a addq         $1, %r9
	0x49, 0x83, 0xc6, 0xff, //0x0000255e addq         $-1, %r14
	0x48, 0x89, 0x4d, 0xc8, //0x00002562 movq         %rcx, $-56(%rbp)
	0x4c, 0x8b, 0x6d, 0xd0, //0x00002566 movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x0000256a movq         $-72(%rbp), %r12
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000256e movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x00002578 movq         $-64(%rbp), %rbx
	0x4d, 0x85, 0xf6, //0x0000257c testq        %r14, %r14
	0x0f, 0x85, 0xd5, 0xfc, 0xff, 0xff, //0x0000257f jne          LBB0_424
	0xe9, 0x67, 0xfd, 0xff, 0xff, //0x00002585 jmp          LBB0_433
	//0x0000258a LBB0_460
	0x4d, 0x85, 0xf6, //0x0000258a testq        %r14, %r14
	0x0f, 0x84, 0xcd, 0x01, 0x00, 0x00, //0x0000258d je           LBB0_474
	0x48, 0x8b, 0x45, 0x88, //0x00002593 movq         $-120(%rbp), %rax
	0x4c, 0x01, 0xc8, //0x00002597 addq         %r9, %rax
	0x48, 0x8b, 0x4d, 0xc8, //0x0000259a movq         $-56(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x0000259e cmpq         $-1, %rcx
	0x48, 0x89, 0xca, //0x000025a2 movq         %rcx, %rdx
	0x48, 0x0f, 0x44, 0xc8, //0x000025a5 cmoveq       %rax, %rcx
	0x48, 0x0f, 0x44, 0xd0, //0x000025a9 cmoveq       %rax, %rdx
	0x49, 0x83, 0xc1, 0x01, //0x000025ad addq         $1, %r9
	0x49, 0x83, 0xc6, 0xff, //0x000025b1 addq         $-1, %r14
	0x48, 0x89, 0x4d, 0xc8, //0x000025b5 movq         %rcx, $-56(%rbp)
	0x4c, 0x8b, 0x6d, 0xd0, //0x000025b9 movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x000025bd movq         $-72(%rbp), %r12
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000025c1 movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x000025cb movq         $-64(%rbp), %rbx
	0x4d, 0x85, 0xf6, //0x000025cf testq        %r14, %r14
	0x0f, 0x85, 0x5b, 0xf6, 0xff, 0xff, //0x000025d2 jne          LBB0_362
	0xe9, 0x02, 0x03, 0x00, 0x00, //0x000025d8 jmp          LBB0_498
	//0x000025dd LBB0_462
	0x4d, 0x85, 0xf6, //0x000025dd testq        %r14, %r14
	0x0f, 0x84, 0x7a, 0x01, 0x00, 0x00, //0x000025e0 je           LBB0_474
	0x48, 0x8b, 0x45, 0x88, //0x000025e6 movq         $-120(%rbp), %rax
	0x4c, 0x01, 0xc8, //0x000025ea addq         %r9, %rax
	0x48, 0x8b, 0x4d, 0xc8, //0x000025ed movq         $-56(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x000025f1 cmpq         $-1, %rcx
	0x48, 0x89, 0xca, //0x000025f5 movq         %rcx, %rdx
	0x48, 0x0f, 0x44, 0xc8, //0x000025f8 cmoveq       %rax, %rcx
	0x48, 0x0f, 0x44, 0xd0, //0x000025fc cmoveq       %rax, %rdx
	0x49, 0x83, 0xc1, 0x01, //0x00002600 addq         $1, %r9
	0x49, 0x83, 0xc6, 0xff, //0x00002604 addq         $-1, %r14
	0x48, 0x89, 0x4d, 0xc8, //0x00002608 movq         %rcx, $-56(%rbp)
	0x4c, 0x8b, 0x6d, 0xd0, //0x0000260c movq         $-48(%rbp), %r13
	0x4c, 0x8b, 0x65, 0xb8, //0x00002610 movq         $-72(%rbp), %r12
	0x49, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002614 movabsq      $4294977024, %r11
	0x48, 0x8b, 0x5d, 0xc0, //0x0000261e movq         $-64(%rbp), %rbx
	0x4d, 0x85, 0xf6, //0x00002622 testq        %r14, %r14
	0x0f, 0x85, 0xe2, 0xfd, 0xff, 0xff, //0x00002625 jne          LBB0_444
	0xe9, 0x74, 0xfe, 0xff, 0xff, //0x0000262b jmp          LBB0_453
	//0x00002630 LBB0_464
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00002630 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00002635 jne          LBB0_466
	0x4c, 0x89, 0xc8, //0x0000263b movq         %r9, %rax
	0x48, 0x2b, 0x45, 0xb8, //0x0000263e subq         $-72(%rbp), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x00002642 bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x00002646 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x00002649 movq         %rcx, $-56(%rbp)
	//0x0000264d LBB0_466
	0x44, 0x89, 0xd8, //0x0000264d movl         %r11d, %eax
	0xf7, 0xd0, //0x00002650 notl         %eax
	0x21, 0xd0, //0x00002652 andl         %edx, %eax
	0x41, 0x8d, 0x0c, 0x43, //0x00002654 leal         (%r11,%rax,2), %ecx
	0x8d, 0x34, 0x00, //0x00002658 leal         (%rax,%rax), %esi
	0xf7, 0xd6, //0x0000265b notl         %esi
	0x21, 0xd6, //0x0000265d andl         %edx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000265f andl         $-1431655766, %esi
	0x45, 0x31, 0xdb, //0x00002665 xorl         %r11d, %r11d
	0x01, 0xc6, //0x00002668 addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc3, //0x0000266a setb         %r11b
	0x01, 0xf6, //0x0000266e addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x00002670 xorl         $1431655765, %esi
	0x21, 0xce, //0x00002676 andl         %ecx, %esi
	0xf7, 0xd6, //0x00002678 notl         %esi
	0x21, 0xf7, //0x0000267a andl         %esi, %edi
	0x4c, 0x8b, 0x65, 0xb8, //0x0000267c movq         $-72(%rbp), %r12
	0x48, 0x8b, 0x5d, 0xc0, //0x00002680 movq         $-64(%rbp), %rbx
	0x48, 0x85, 0xff, //0x00002684 testq        %rdi, %rdi
	0x0f, 0x85, 0xd8, 0xf2, 0xff, 0xff, //0x00002687 jne          LBB0_76
	0xe9, 0xa6, 0xf7, 0xff, 0xff, //0x0000268d jmp          LBB0_243
	//0x00002692 LBB0_467
	0x48, 0x83, 0x7d, 0xc8, 0xff, //0x00002692 cmpq         $-1, $-56(%rbp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00002697 jne          LBB0_469
	0x4c, 0x89, 0xc8, //0x0000269d movq         %r9, %rax
	0x48, 0x2b, 0x45, 0xb8, //0x000026a0 subq         $-72(%rbp), %rax
	0x48, 0x0f, 0xbc, 0xca, //0x000026a4 bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x000026a8 addq         %rax, %rcx
	0x48, 0x89, 0x4d, 0xc8, //0x000026ab movq         %rcx, $-56(%rbp)
	//0x000026af LBB0_469
	0x44, 0x89, 0xd8, //0x000026af movl         %r11d, %eax
	0xf7, 0xd0, //0x000026b2 notl         %eax
	0x21, 0xd0, //0x000026b4 andl         %edx, %eax
	0x41, 0x8d, 0x0c, 0x43, //0x000026b6 leal         (%r11,%rax,2), %ecx
	0x8d, 0x1c, 0x00, //0x000026ba leal         (%rax,%rax), %ebx
	0xf7, 0xd3, //0x000026bd notl         %ebx
	0x21, 0xd3, //0x000026bf andl         %edx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x000026c1 andl         $-1431655766, %ebx
	0x45, 0x31, 0xdb, //0x000026c7 xorl         %r11d, %r11d
	0x01, 0xc3, //0x000026ca addl         %eax, %ebx
	0x41, 0x0f, 0x92, 0xc3, //0x000026cc setb         %r11b
	0x01, 0xdb, //0x000026d0 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x000026d2 xorl         $1431655765, %ebx
	0x21, 0xcb, //0x000026d8 andl         %ecx, %ebx
	0xf7, 0xd3, //0x000026da notl         %ebx
	0x21, 0xde, //0x000026dc andl         %ebx, %esi
	0x4c, 0x8b, 0x65, 0xb8, //0x000026de movq         $-72(%rbp), %r12
	0x48, 0x8b, 0x5d, 0xc0, //0x000026e2 movq         $-64(%rbp), %rbx
	0xe9, 0x66, 0xf8, 0xff, 0xff, //0x000026e6 jmp          LBB0_316
	//0x000026eb LBB0_470
	0x4d, 0x85, 0xf6, //0x000026eb testq        %r14, %r14
	0x0f, 0x84, 0x6c, 0x00, 0x00, 0x00, //0x000026ee je           LBB0_474
	0x48, 0x8b, 0x45, 0x88, //0x000026f4 movq         $-120(%rbp), %rax
	0x4c, 0x01, 0xc8, //0x000026f8 addq         %r9, %rax
	0x48, 0x8b, 0x4d, 0xc8, //0x000026fb movq         $-56(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x000026ff cmpq         $-1, %rcx
	0x48, 0x89, 0xca, //0x00002703 movq         %rcx, %rdx
	0x48, 0x0f, 0x44, 0xc8, //0x00002706 cmoveq       %rax, %rcx
	0x48, 0x0f, 0x44, 0xd0, //0x0000270a cmoveq       %rax, %rdx
	0x49, 0x83, 0xc1, 0x01, //0x0000270e addq         $1, %r9
	0x49, 0x83, 0xc6, 0xff, //0x00002712 addq         $-1, %r14
	0x48, 0x89, 0x4d, 0xc8, //0x00002716 movq         %rcx, $-56(%rbp)
	0x4d, 0x85, 0xf6, //0x0000271a testq        %r14, %r14
	0x0f, 0x85, 0x33, 0xf7, 0xff, 0xff, //0x0000271d jne          LBB0_246
	0xe9, 0x96, 0xf9, 0xff, 0xff, //0x00002723 jmp          LBB0_409
	//0x00002728 LBB0_472
	0x4d, 0x85, 0xf6, //0x00002728 testq        %r14, %r14
	0x0f, 0x84, 0x2f, 0x00, 0x00, 0x00, //0x0000272b je           LBB0_474
	0x48, 0x8b, 0x45, 0x88, //0x00002731 movq         $-120(%rbp), %rax
	0x4c, 0x01, 0xc8, //0x00002735 addq         %r9, %rax
	0x48, 0x8b, 0x4d, 0xc8, //0x00002738 movq         $-56(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x0000273c cmpq         $-1, %rcx
	0x48, 0x89, 0xca, //0x00002740 movq         %rcx, %rdx
	0x48, 0x0f, 0x44, 0xc8, //0x00002743 cmoveq       %rax, %rcx
	0x48, 0x0f, 0x44, 0xd0, //0x00002747 cmoveq       %rax, %rdx
	0x49, 0x83, 0xc1, 0x01, //0x0000274b addq         $1, %r9
	0x49, 0x83, 0xc6, 0xff, //0x0000274f addq         $-1, %r14
	0x48, 0x89, 0x4d, 0xc8, //0x00002753 movq         %rcx, $-56(%rbp)
	0x4d, 0x85, 0xf6, //0x00002757 testq        %r14, %r14
	0x0f, 0x85, 0x9b, 0xf8, 0xff, 0xff, //0x0000275a jne          LBB0_397
	//0x00002760 LBB0_474
	0x4c, 0x8b, 0x6d, 0xd0, //0x00002760 movq         $-48(%rbp), %r13
	0xe9, 0x76, 0x01, 0x00, 0x00, //0x00002764 jmp          LBB0_498
	//0x00002769 LBB0_475
	0x49, 0x89, 0x4d, 0x00, //0x00002769 movq         %rcx, (%r13)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000276d movq         $-1, %rax
	0xe9, 0x63, 0x0c, 0x00, 0x00, //0x00002774 jmp          LBB0_582
	//0x00002779 LBB0_476
	0x4d, 0x89, 0x4d, 0x00, //0x00002779 movq         %r9, (%r13)
	//0x0000277d LBB0_477
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000277d movq         $-1, %rax
	0xe9, 0x53, 0x0c, 0x00, 0x00, //0x00002784 jmp          LBB0_582
	//0x00002789 LBB0_478
	0x4c, 0x01, 0xe1, //0x00002789 addq         %r12, %rcx
	0x49, 0x89, 0xc8, //0x0000278c movq         %rcx, %r8
	//0x0000278f LBB0_479
	0x4d, 0x29, 0xe0, //0x0000278f subq         %r12, %r8
	0x4c, 0x89, 0xc1, //0x00002792 movq         %r8, %rcx
	//0x00002795 LBB0_480
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002795 movq         $-1, %rax
	0x49, 0x89, 0xc9, //0x0000279c movq         %rcx, %r9
	0x48, 0x39, 0xd9, //0x0000279f cmpq         %rbx, %rcx
	0x0f, 0x83, 0x34, 0x0c, 0x00, 0x00, //0x000027a2 jae          LBB0_582
	//0x000027a8 LBB0_481
	0x49, 0x8d, 0x59, 0x01, //0x000027a8 leaq         $1(%r9), %rbx
	0x49, 0x89, 0x5d, 0x00, //0x000027ac movq         %rbx, (%r13)
	0x43, 0x0f, 0xbe, 0x0c, 0x0c, //0x000027b0 movsbl       (%r12,%r9), %ecx
	0x83, 0xf9, 0x7b, //0x000027b5 cmpl         $123, %ecx
	0x0f, 0x87, 0x31, 0x01, 0x00, 0x00, //0x000027b8 ja           LBB0_499
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000027be movq         $-1, %rax
	0x48, 0x8d, 0x15, 0x04, 0x0e, 0x00, 0x00, //0x000027c5 leaq         $3588(%rip), %rdx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8a, //0x000027cc movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x000027d0 addq         %rdx, %rcx
	0xff, 0xe1, //0x000027d3 jmpq         *%rcx
	//0x000027d5 LBB0_483
	0x48, 0x8b, 0x47, 0x08, //0x000027d5 movq         $8(%rdi), %rax
	0x48, 0x89, 0xc1, //0x000027d9 movq         %rax, %rcx
	0x48, 0x29, 0xd9, //0x000027dc subq         %rbx, %rcx
	0x48, 0x83, 0xf9, 0x10, //0x000027df cmpq         $16, %rcx
	0x0f, 0x82, 0x51, 0x0d, 0x00, 0x00, //0x000027e3 jb           LBB0_598
	0x4c, 0x89, 0xc9, //0x000027e9 movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x000027ec notq         %rcx
	0xf3, 0x0f, 0x6f, 0x05, 0x09, 0xd8, 0xff, 0xff, //0x000027ef movdqu       $-10231(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x11, 0xd8, 0xff, 0xff, //0x000027f7 movdqu       $-10223(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x15, 0x19, 0xd8, 0xff, 0xff, //0x000027ff movdqu       $-10215(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002807 .p2align 4, 0x90
	//0x00002810 LBB0_485
	0xf3, 0x41, 0x0f, 0x6f, 0x1c, 0x1c, //0x00002810 movdqu       (%r12,%rbx), %xmm3
	0x66, 0x0f, 0x6f, 0xe3, //0x00002816 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x0000281a pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xdb, 0xd9, //0x0000281e pand         %xmm1, %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x00002822 pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xeb, 0xdc, //0x00002826 por          %xmm4, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x0000282a pmovmskb     %xmm3, %edx
	0x85, 0xd2, //0x0000282e testl        %edx, %edx
	0x0f, 0x85, 0x73, 0x00, 0x00, 0x00, //0x00002830 jne          LBB0_495
	0x48, 0x83, 0xc3, 0x10, //0x00002836 addq         $16, %rbx
	0x48, 0x8d, 0x14, 0x08, //0x0000283a leaq         (%rax,%rcx), %rdx
	0x48, 0x83, 0xc2, 0xf0, //0x0000283e addq         $-16, %rdx
	0x48, 0x83, 0xc1, 0xf0, //0x00002842 addq         $-16, %rcx
	0x48, 0x83, 0xfa, 0x0f, //0x00002846 cmpq         $15, %rdx
	0x0f, 0x87, 0xc0, 0xff, 0xff, 0xff, //0x0000284a ja           LBB0_485
	0x4c, 0x89, 0xe3, //0x00002850 movq         %r12, %rbx
	0x48, 0x29, 0xcb, //0x00002853 subq         %rcx, %rbx
	0x48, 0x01, 0xc8, //0x00002856 addq         %rcx, %rax
	0x48, 0x89, 0xc1, //0x00002859 movq         %rax, %rcx
	0x48, 0x85, 0xc9, //0x0000285c testq        %rcx, %rcx
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x0000285f je           LBB0_494
	//0x00002865 LBB0_488
	0x48, 0x8d, 0x3c, 0x0b, //0x00002865 leaq         (%rbx,%rcx), %rdi
	0x31, 0xc0, //0x00002869 xorl         %eax, %eax
	//0x0000286b LBB0_489
	0x0f, 0xb6, 0x14, 0x03, //0x0000286b movzbl       (%rbx,%rax), %edx
	0x80, 0xfa, 0x2c, //0x0000286f cmpb         $44, %dl
	0x0f, 0x84, 0x2e, 0x0b, 0x00, 0x00, //0x00002872 je           LBB0_575
	0x80, 0xfa, 0x7d, //0x00002878 cmpb         $125, %dl
	0x0f, 0x84, 0x25, 0x0b, 0x00, 0x00, //0x0000287b je           LBB0_575
	0x80, 0xfa, 0x5d, //0x00002881 cmpb         $93, %dl
	0x0f, 0x84, 0x1c, 0x0b, 0x00, 0x00, //0x00002884 je           LBB0_575
	0x48, 0x83, 0xc0, 0x01, //0x0000288a addq         $1, %rax
	0x48, 0x39, 0xc1, //0x0000288e cmpq         %rax, %rcx
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00002891 jne          LBB0_489
	0x48, 0x89, 0xfb, //0x00002897 movq         %rdi, %rbx
	//0x0000289a LBB0_494
	0x4c, 0x29, 0xe3, //0x0000289a subq         %r12, %rbx
	0x48, 0x8b, 0x45, 0xd0, //0x0000289d movq         $-48(%rbp), %rax
	0x48, 0x89, 0x18, //0x000028a1 movq         %rbx, (%rax)
	0xe9, 0x30, 0x0b, 0x00, 0x00, //0x000028a4 jmp          LBB0_581
	//0x000028a9 LBB0_495
	0x66, 0x0f, 0xbc, 0xc2, //0x000028a9 bsfw         %dx, %ax
	0x0f, 0xb7, 0xc0, //0x000028ad movzwl       %ax, %eax
	0x48, 0x29, 0xc8, //0x000028b0 subq         %rcx, %rax
	0x49, 0x89, 0x45, 0x00, //0x000028b3 movq         %rax, (%r13)
	0xe9, 0x1d, 0x0b, 0x00, 0x00, //0x000028b7 jmp          LBB0_581
	//0x000028bc LBB0_496
	0x49, 0x8d, 0x49, 0x04, //0x000028bc leaq         $4(%r9), %rcx
	0xe9, 0x91, 0x05, 0x00, 0x00, //0x000028c0 jmp          LBB0_536
	//0x000028c5 LBB0_585
	0x48, 0xc7, 0xc0, 0xf9, 0xff, 0xff, 0xff, //0x000028c5 movq         $-7, %rax
	0xe9, 0x0b, 0x0b, 0x00, 0x00, //0x000028cc jmp          LBB0_582
	//0x000028d1 LBB0_497
	0x49, 0x83, 0xf9, 0xff, //0x000028d1 cmpq         $-1, %r9
	0x48, 0x8b, 0x4d, 0xc8, //0x000028d5 movq         $-56(%rbp), %rcx
	0x0f, 0x85, 0xf6, 0x0a, 0x00, 0x00, //0x000028d9 jne          LBB0_580
	//0x000028df LBB0_498
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000028df movq         $-1, %r9
	0x48, 0x8b, 0x4d, 0x98, //0x000028e6 movq         $-104(%rbp), %rcx
	0xe9, 0xe6, 0x0a, 0x00, 0x00, //0x000028ea jmp          LBB0_580
	//0x000028ef LBB0_499
	0x4d, 0x89, 0x4d, 0x00, //0x000028ef movq         %r9, (%r13)
	0xe9, 0x2c, 0x0a, 0x00, 0x00, //0x000028f3 jmp          LBB0_567
	//0x000028f8 LBB0_500
	0x4c, 0x8b, 0x47, 0x08, //0x000028f8 movq         $8(%rdi), %r8
	0x4d, 0x89, 0xc6, //0x000028fc movq         %r8, %r14
	0x49, 0x29, 0xde, //0x000028ff subq         %rbx, %r14
	0x49, 0x83, 0xfe, 0x20, //0x00002902 cmpq         $32, %r14
	0x0f, 0x8c, 0x3f, 0x0c, 0x00, 0x00, //0x00002906 jl           LBB0_600
	0x4f, 0x8d, 0x14, 0x0c, //0x0000290c leaq         (%r12,%r9), %r10
	0x4d, 0x29, 0xc8, //0x00002910 subq         %r9, %r8
	0x41, 0xbf, 0x1f, 0x00, 0x00, 0x00, //0x00002913 movl         $31, %r15d
	0x45, 0x31, 0xf6, //0x00002919 xorl         %r14d, %r14d
	0xf3, 0x0f, 0x6f, 0x05, 0x0c, 0xd7, 0xff, 0xff, //0x0000291c movdqu       $-10484(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x14, 0xd7, 0xff, 0xff, //0x00002924 movdqu       $-10476(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x45, 0x31, 0xdb, //0x0000292c xorl         %r11d, %r11d
	0x90, //0x0000292f .p2align 4, 0x90
	//0x00002930 LBB0_502
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x32, 0x01, //0x00002930 movdqu       $1(%r10,%r14), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x32, 0x11, //0x00002937 movdqu       $17(%r10,%r14), %xmm3
	0x66, 0x0f, 0x6f, 0xe2, //0x0000293e movdqa       %xmm2, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x00002942 pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xd4, //0x00002946 pmovmskb     %xmm4, %edx
	0x66, 0x0f, 0x6f, 0xe3, //0x0000294a movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x0000294e pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xf4, //0x00002952 pmovmskb     %xmm4, %esi
	0x48, 0xc1, 0xe6, 0x10, //0x00002956 shlq         $16, %rsi
	0x48, 0x09, 0xd6, //0x0000295a orq          %rdx, %rsi
	0x66, 0x0f, 0x74, 0xd1, //0x0000295d pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00002961 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x74, 0xd9, //0x00002965 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xd3, //0x00002969 pmovmskb     %xmm3, %edx
	0x48, 0xc1, 0xe2, 0x10, //0x0000296d shlq         $16, %rdx
	0x48, 0x09, 0xca, //0x00002971 orq          %rcx, %rdx
	0x48, 0x89, 0xd1, //0x00002974 movq         %rdx, %rcx
	0x4c, 0x09, 0xd9, //0x00002977 orq          %r11, %rcx
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x0000297a je           LBB0_504
	0x44, 0x89, 0xd9, //0x00002980 movl         %r11d, %ecx
	0xf7, 0xd1, //0x00002983 notl         %ecx
	0x21, 0xd1, //0x00002985 andl         %edx, %ecx
	0x8d, 0x1c, 0x09, //0x00002987 leal         (%rcx,%rcx), %ebx
	0x44, 0x09, 0xdb, //0x0000298a orl          %r11d, %ebx
	0x89, 0xdf, //0x0000298d movl         %ebx, %edi
	0xf7, 0xd7, //0x0000298f notl         %edi
	0x21, 0xd7, //0x00002991 andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002993 andl         $-1431655766, %edi
	0x45, 0x31, 0xdb, //0x00002999 xorl         %r11d, %r11d
	0x01, 0xcf, //0x0000299c addl         %ecx, %edi
	0x41, 0x0f, 0x92, 0xc3, //0x0000299e setb         %r11b
	0x01, 0xff, //0x000029a2 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000029a4 xorl         $1431655765, %edi
	0x21, 0xdf, //0x000029aa andl         %ebx, %edi
	0xf7, 0xd7, //0x000029ac notl         %edi
	0x21, 0xfe, //0x000029ae andl         %edi, %esi
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000029b0 jmp          LBB0_505
	//0x000029b5 LBB0_504
	0x45, 0x31, 0xdb, //0x000029b5 xorl         %r11d, %r11d
	//0x000029b8 LBB0_505
	0x48, 0x85, 0xf6, //0x000029b8 testq        %rsi, %rsi
	0x0f, 0x85, 0x2d, 0x09, 0x00, 0x00, //0x000029bb jne          LBB0_563
	0x49, 0x83, 0xc6, 0x20, //0x000029c1 addq         $32, %r14
	0x4b, 0x8d, 0x0c, 0x38, //0x000029c5 leaq         (%r8,%r15), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x000029c9 addq         $-32, %rcx
	0x49, 0x83, 0xc7, 0xe0, //0x000029cd addq         $-32, %r15
	0x48, 0x83, 0xf9, 0x3f, //0x000029d1 cmpq         $63, %rcx
	0x0f, 0x8f, 0x55, 0xff, 0xff, 0xff, //0x000029d5 jg           LBB0_502
	0x4d, 0x85, 0xdb, //0x000029db testq        %r11, %r11
	0x0f, 0x85, 0xa3, 0x0b, 0x00, 0x00, //0x000029de jne          LBB0_604
	0x4b, 0x8d, 0x1c, 0x16, //0x000029e4 leaq         (%r14,%r10), %rbx
	0x48, 0x83, 0xc3, 0x01, //0x000029e8 addq         $1, %rbx
	0x49, 0xf7, 0xd6, //0x000029ec notq         %r14
	0x4d, 0x01, 0xc6, //0x000029ef addq         %r8, %r14
	//0x000029f2 LBB0_509
	0x48, 0x8b, 0x55, 0xd0, //0x000029f2 movq         $-48(%rbp), %rdx
	0x4d, 0x85, 0xf6, //0x000029f6 testq        %r14, %r14
	0x0f, 0x8f, 0x50, 0x09, 0x00, 0x00, //0x000029f9 jg           LBB0_570
	0xe9, 0xd8, 0x09, 0x00, 0x00, //0x000029ff jmp          LBB0_582
	//0x00002a04 LBB0_510
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002a04 movabsq      $6148914691236517205, %r15
	0x48, 0x89, 0x7d, 0xb0, //0x00002a0e movq         %rdi, $-80(%rbp)
	0x48, 0x8b, 0x4f, 0x08, //0x00002a12 movq         $8(%rdi), %rcx
	0x48, 0x29, 0xd9, //0x00002a16 subq         %rbx, %rcx
	0x49, 0x01, 0xdc, //0x00002a19 addq         %rbx, %r12
	0x31, 0xdb, //0x00002a1c xorl         %ebx, %ebx
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0x19, 0xd6, 0xff, 0xff, //0x00002a1e movdqu       $-10727(%rip), %xmm10  /* LCPI0_4+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x01, 0xd6, 0xff, 0xff, //0x00002a27 movdqu       $-10751(%rip), %xmm1  /* LCPI0_3+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xc9, //0x00002a2f pcmpeqd      %xmm9, %xmm9
	0xf3, 0x0f, 0x6f, 0x1d, 0x34, 0xd6, 0xff, 0xff, //0x00002a34 movdqu       $-10700(%rip), %xmm3  /* LCPI0_7+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0xdc, 0xd5, 0xff, 0xff, //0x00002a3c movdqu       $-10788(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0x49, 0xbd, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00002a44 movabsq      $3689348814741910323, %r13
	0x66, 0x45, 0x0f, 0xef, 0xc0, //0x00002a4e pxor         %xmm8, %xmm8
	0x31, 0xd2, //0x00002a53 xorl         %edx, %edx
	0x45, 0x31, 0xf6, //0x00002a55 xorl         %r14d, %r14d
	0x45, 0x31, 0xd2, //0x00002a58 xorl         %r10d, %r10d
	0x48, 0x83, 0xf9, 0x40, //0x00002a5b cmpq         $64, %rcx
	0x48, 0x89, 0x4d, 0xb8, //0x00002a5f movq         %rcx, $-72(%rbp)
	0x0f, 0x8c, 0x88, 0x02, 0x00, 0x00, //0x00002a63 jl           LBB0_519
	//0x00002a69 LBB0_513
	0xf3, 0x41, 0x0f, 0x6f, 0x04, 0x24, //0x00002a69 movdqu       (%r12), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x6c, 0x24, 0x10, //0x00002a6f movdqu       $16(%r12), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x7c, 0x24, 0x20, //0x00002a76 movdqu       $32(%r12), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x74, 0x24, 0x30, //0x00002a7d movdqu       $48(%r12), %xmm6
	0x66, 0x0f, 0x6f, 0xd0, //0x00002a84 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00002a88 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00002a8d pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd5, //0x00002a91 movdqa       %xmm5, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00002a95 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00002a9a pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd7, //0x00002a9e movdqa       %xmm7, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00002aa2 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00002aa7 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x6f, 0xd6, //0x00002aab movdqa       %xmm6, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00002aaf pcmpeqb      %xmm10, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xc2, //0x00002ab4 pmovmskb     %xmm2, %r8d
	0x49, 0xc1, 0xe0, 0x30, //0x00002ab9 shlq         $48, %r8
	0x48, 0xc1, 0xe6, 0x20, //0x00002abd shlq         $32, %rsi
	0x4c, 0x09, 0xc6, //0x00002ac1 orq          %r8, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x00002ac4 shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x00002ac8 orq          %rsi, %rcx
	0x48, 0x09, 0xcf, //0x00002acb orq          %rcx, %rdi
	0x48, 0x89, 0xf9, //0x00002ace movq         %rdi, %rcx
	0x48, 0x09, 0xd1, //0x00002ad1 orq          %rdx, %rcx
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00002ad4 jne          LBB0_515
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002ada movq         $-1, %rdi
	0x31, 0xc9, //0x00002ae1 xorl         %ecx, %ecx
	0x48, 0x89, 0x4d, 0xc0, //0x00002ae3 movq         %rcx, $-64(%rbp)
	0xe9, 0x44, 0x00, 0x00, 0x00, //0x00002ae7 jmp          LBB0_516
	//0x00002aec LBB0_515
	0x48, 0x89, 0xd1, //0x00002aec movq         %rdx, %rcx
	0x48, 0xf7, 0xd1, //0x00002aef notq         %rcx
	0x48, 0x21, 0xf9, //0x00002af2 andq         %rdi, %rcx
	0x48, 0x8d, 0x34, 0x09, //0x00002af5 leaq         (%rcx,%rcx), %rsi
	0x48, 0x09, 0xd6, //0x00002af9 orq          %rdx, %rsi
	0x48, 0x89, 0xf2, //0x00002afc movq         %rsi, %rdx
	0x48, 0xf7, 0xd2, //0x00002aff notq         %rdx
	0x49, 0x89, 0xd8, //0x00002b02 movq         %rbx, %r8
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002b05 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xdf, //0x00002b0f andq         %rbx, %rdi
	0x4c, 0x89, 0xc3, //0x00002b12 movq         %r8, %rbx
	0x48, 0x21, 0xd7, //0x00002b15 andq         %rdx, %rdi
	0x31, 0xd2, //0x00002b18 xorl         %edx, %edx
	0x48, 0x01, 0xcf, //0x00002b1a addq         %rcx, %rdi
	0x0f, 0x92, 0xc2, //0x00002b1d setb         %dl
	0x48, 0x89, 0x55, 0xc0, //0x00002b20 movq         %rdx, $-64(%rbp)
	0x48, 0x01, 0xff, //0x00002b24 addq         %rdi, %rdi
	0x4c, 0x31, 0xff, //0x00002b27 xorq         %r15, %rdi
	0x48, 0x21, 0xf7, //0x00002b2a andq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x00002b2d notq         %rdi
	//0x00002b30 LBB0_516
	0x66, 0x0f, 0x6f, 0xd6, //0x00002b30 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00002b34 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00002b38 pmovmskb     %xmm2, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x00002b3c shlq         $48, %rcx
	0x66, 0x0f, 0x6f, 0xd7, //0x00002b40 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00002b44 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00002b48 pmovmskb     %xmm2, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x00002b4c shlq         $32, %rsi
	0x48, 0x09, 0xce, //0x00002b50 orq          %rcx, %rsi
	0x66, 0x0f, 0x6f, 0xd5, //0x00002b53 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00002b57 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00002b5b pmovmskb     %xmm2, %ecx
	0x48, 0xc1, 0xe1, 0x10, //0x00002b5f shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x00002b63 orq          %rsi, %rcx
	0x66, 0x0f, 0x6f, 0xd0, //0x00002b66 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00002b6a pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00002b6e pmovmskb     %xmm2, %esi
	0x48, 0x09, 0xce, //0x00002b72 orq          %rcx, %rsi
	0x48, 0x21, 0xfe, //0x00002b75 andq         %rdi, %rsi
	0x66, 0x48, 0x0f, 0x6e, 0xd6, //0x00002b78 movq         %rsi, %xmm2
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd1, 0x00, //0x00002b7d pclmulqdq    $0, %xmm9, %xmm2
	0x66, 0x49, 0x0f, 0x7e, 0xd3, //0x00002b84 movq         %xmm2, %r11
	0x49, 0x31, 0xdb, //0x00002b89 xorq         %rbx, %r11
	0x66, 0x0f, 0x6f, 0xd0, //0x00002b8c movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00002b90 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00002b94 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x6f, 0xd5, //0x00002b98 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00002b9c pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00002ba0 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd7, //0x00002ba4 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00002ba8 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00002bac pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd6, //0x00002bb0 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00002bb4 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00002bb8 pmovmskb     %xmm2, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00002bbc shlq         $48, %rbx
	0x48, 0xc1, 0xe2, 0x20, //0x00002bc0 shlq         $32, %rdx
	0x48, 0x09, 0xda, //0x00002bc4 orq          %rbx, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x00002bc7 shlq         $16, %rcx
	0x48, 0x09, 0xd1, //0x00002bcb orq          %rdx, %rcx
	0x48, 0x09, 0xce, //0x00002bce orq          %rcx, %rsi
	0x4c, 0x89, 0xd9, //0x00002bd1 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00002bd4 notq         %rcx
	0x48, 0x21, 0xce, //0x00002bd7 andq         %rcx, %rsi
	0x66, 0x0f, 0x74, 0xc4, //0x00002bda pcmpeqb      %xmm4, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xc0, //0x00002bde pmovmskb     %xmm0, %r8d
	0x66, 0x0f, 0x74, 0xec, //0x00002be3 pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x00002be7 pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x74, 0xfc, //0x00002beb pcmpeqb      %xmm4, %xmm7
	0x66, 0x0f, 0xd7, 0xdf, //0x00002bef pmovmskb     %xmm7, %ebx
	0x66, 0x0f, 0x74, 0xf4, //0x00002bf3 pcmpeqb      %xmm4, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xfe, //0x00002bf7 pmovmskb     %xmm6, %r15d
	0x49, 0xc1, 0xe7, 0x30, //0x00002bfc shlq         $48, %r15
	0x48, 0xc1, 0xe3, 0x20, //0x00002c00 shlq         $32, %rbx
	0x4c, 0x09, 0xfb, //0x00002c04 orq          %r15, %rbx
	0x48, 0xc1, 0xe2, 0x10, //0x00002c07 shlq         $16, %rdx
	0x48, 0x09, 0xda, //0x00002c0b orq          %rbx, %rdx
	0x49, 0x09, 0xd0, //0x00002c0e orq          %rdx, %r8
	0x48, 0xbf, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00002c11 movabsq      $1085102592571150095, %rdi
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002c1b movabsq      $6148914691236517205, %r15
	0x49, 0x21, 0xc8, //0x00002c25 andq         %rcx, %r8
	0x0f, 0x84, 0x60, 0x00, 0x00, 0x00, //0x00002c28 je           LBB0_511
	0x90, 0x90, //0x00002c2e .p2align 4, 0x90
	//0x00002c30 LBB0_517
	0x49, 0x8d, 0x48, 0xff, //0x00002c30 leaq         $-1(%r8), %rcx
	0x48, 0x89, 0xca, //0x00002c34 movq         %rcx, %rdx
	0x48, 0x21, 0xf2, //0x00002c37 andq         %rsi, %rdx
	0x48, 0x89, 0xd3, //0x00002c3a movq         %rdx, %rbx
	0x48, 0xd1, 0xeb, //0x00002c3d shrq         %rbx
	0x4c, 0x21, 0xfb, //0x00002c40 andq         %r15, %rbx
	0x48, 0x29, 0xda, //0x00002c43 subq         %rbx, %rdx
	0x48, 0x89, 0xd3, //0x00002c46 movq         %rdx, %rbx
	0x4c, 0x21, 0xeb, //0x00002c49 andq         %r13, %rbx
	0x48, 0xc1, 0xea, 0x02, //0x00002c4c shrq         $2, %rdx
	0x4c, 0x21, 0xea, //0x00002c50 andq         %r13, %rdx
	0x48, 0x01, 0xda, //0x00002c53 addq         %rbx, %rdx
	0x48, 0x89, 0xd3, //0x00002c56 movq         %rdx, %rbx
	0x48, 0xc1, 0xeb, 0x04, //0x00002c59 shrq         $4, %rbx
	0x48, 0x01, 0xd3, //0x00002c5d addq         %rdx, %rbx
	0x48, 0x21, 0xfb, //0x00002c60 andq         %rdi, %rbx
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00002c63 movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xda, //0x00002c6d imulq        %rdx, %rbx
	0x48, 0xc1, 0xeb, 0x38, //0x00002c71 shrq         $56, %rbx
	0x4c, 0x01, 0xf3, //0x00002c75 addq         %r14, %rbx
	0x4c, 0x39, 0xd3, //0x00002c78 cmpq         %r10, %rbx
	0x0f, 0x86, 0x31, 0x06, 0x00, 0x00, //0x00002c7b jbe          LBB0_562
	0x49, 0x83, 0xc2, 0x01, //0x00002c81 addq         $1, %r10
	0x49, 0x21, 0xc8, //0x00002c85 andq         %rcx, %r8
	0x0f, 0x85, 0xa2, 0xff, 0xff, 0xff, //0x00002c88 jne          LBB0_517
	//0x00002c8e LBB0_511
	0x49, 0xc1, 0xfb, 0x3f, //0x00002c8e sarq         $63, %r11
	0x48, 0x89, 0xf1, //0x00002c92 movq         %rsi, %rcx
	0x48, 0xd1, 0xe9, //0x00002c95 shrq         %rcx
	0x4c, 0x21, 0xf9, //0x00002c98 andq         %r15, %rcx
	0x48, 0x29, 0xce, //0x00002c9b subq         %rcx, %rsi
	0x48, 0x89, 0xf1, //0x00002c9e movq         %rsi, %rcx
	0x4c, 0x21, 0xe9, //0x00002ca1 andq         %r13, %rcx
	0x48, 0xc1, 0xee, 0x02, //0x00002ca4 shrq         $2, %rsi
	0x4c, 0x21, 0xee, //0x00002ca8 andq         %r13, %rsi
	0x48, 0x01, 0xce, //0x00002cab addq         %rcx, %rsi
	0x48, 0x89, 0xf1, //0x00002cae movq         %rsi, %rcx
	0x48, 0xc1, 0xe9, 0x04, //0x00002cb1 shrq         $4, %rcx
	0x48, 0x01, 0xf1, //0x00002cb5 addq         %rsi, %rcx
	0x48, 0x21, 0xf9, //0x00002cb8 andq         %rdi, %rcx
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00002cbb movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x00002cc5 imulq        %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x38, //0x00002cc9 shrq         $56, %rcx
	0x49, 0x01, 0xce, //0x00002ccd addq         %rcx, %r14
	0x49, 0x83, 0xc4, 0x40, //0x00002cd0 addq         $64, %r12
	0x48, 0x8b, 0x4d, 0xb8, //0x00002cd4 movq         $-72(%rbp), %rcx
	0x48, 0x83, 0xc1, 0xc0, //0x00002cd8 addq         $-64, %rcx
	0x4c, 0x89, 0xdb, //0x00002cdc movq         %r11, %rbx
	0x48, 0x8b, 0x55, 0xc0, //0x00002cdf movq         $-64(%rbp), %rdx
	0x48, 0x83, 0xf9, 0x40, //0x00002ce3 cmpq         $64, %rcx
	0x48, 0x89, 0x4d, 0xb8, //0x00002ce7 movq         %rcx, $-72(%rbp)
	0x0f, 0x8d, 0x78, 0xfd, 0xff, 0xff, //0x00002ceb jge          LBB0_513
	//0x00002cf1 LBB0_519
	0x48, 0x85, 0xc9, //0x00002cf1 testq        %rcx, %rcx
	0x0f, 0x8e, 0x67, 0x08, 0x00, 0x00, //0x00002cf4 jle          LBB0_602
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x00002cfa movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00002d03 movdqu       %xmm8, $-176(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x40, 0xff, 0xff, 0xff, //0x00002d0c movdqu       %xmm8, $-192(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x30, 0xff, 0xff, 0xff, //0x00002d15 movdqu       %xmm8, $-208(%rbp)
	0x44, 0x89, 0xe1, //0x00002d1e movl         %r12d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00002d21 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x00002d27 cmpl         $4033, %ecx
	0x0f, 0x82, 0x3e, 0x00, 0x00, 0x00, //0x00002d2d jb           LBB0_523
	0x48, 0x83, 0x7d, 0xb8, 0x20, //0x00002d33 cmpq         $32, $-72(%rbp)
	0x0f, 0x82, 0x42, 0x00, 0x00, 0x00, //0x00002d38 jb           LBB0_524
	0x41, 0x0f, 0x10, 0x04, 0x24, //0x00002d3e movups       (%r12), %xmm0
	0x0f, 0x11, 0x85, 0x30, 0xff, 0xff, 0xff, //0x00002d43 movups       %xmm0, $-208(%rbp)
	0xf3, 0x41, 0x0f, 0x6f, 0x44, 0x24, 0x10, //0x00002d4a movdqu       $16(%r12), %xmm0
	0xf3, 0x0f, 0x7f, 0x85, 0x40, 0xff, 0xff, 0xff, //0x00002d51 movdqu       %xmm0, $-192(%rbp)
	0x49, 0x83, 0xc4, 0x20, //0x00002d59 addq         $32, %r12
	0x48, 0x8b, 0x4d, 0xb8, //0x00002d5d movq         $-72(%rbp), %rcx
	0x48, 0x8d, 0x79, 0xe0, //0x00002d61 leaq         $-32(%rcx), %rdi
	0x48, 0x8d, 0xb5, 0x50, 0xff, 0xff, 0xff, //0x00002d65 leaq         $-176(%rbp), %rsi
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00002d6c jmp          LBB0_525
	//0x00002d71 LBB0_523
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002d71 movabsq      $6148914691236517205, %r15
	0xe9, 0xe9, 0xfc, 0xff, 0xff, //0x00002d7b jmp          LBB0_513
	//0x00002d80 LBB0_524
	0x48, 0x8d, 0xb5, 0x30, 0xff, 0xff, 0xff, //0x00002d80 leaq         $-208(%rbp), %rsi
	0x48, 0x8b, 0x7d, 0xb8, //0x00002d87 movq         $-72(%rbp), %rdi
	//0x00002d8b LBB0_525
	0x48, 0x83, 0xff, 0x10, //0x00002d8b cmpq         $16, %rdi
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x00002d8f jb           LBB0_526
	0xf3, 0x41, 0x0f, 0x6f, 0x04, 0x24, //0x00002d95 movdqu       (%r12), %xmm0
	0xf3, 0x0f, 0x7f, 0x06, //0x00002d9b movdqu       %xmm0, (%rsi)
	0x49, 0x83, 0xc4, 0x10, //0x00002d9f addq         $16, %r12
	0x48, 0x83, 0xc6, 0x10, //0x00002da3 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00002da7 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00002dab cmpq         $8, %rdi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00002daf jae          LBB0_533
	//0x00002db5 LBB0_527
	0x48, 0x83, 0xff, 0x04, //0x00002db5 cmpq         $4, %rdi
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x00002db9 jb           LBB0_528
	//0x00002dbf LBB0_534
	0x41, 0x8b, 0x0c, 0x24, //0x00002dbf movl         (%r12), %ecx
	0x89, 0x0e, //0x00002dc3 movl         %ecx, (%rsi)
	0x49, 0x83, 0xc4, 0x04, //0x00002dc5 addq         $4, %r12
	0x48, 0x83, 0xc6, 0x04, //0x00002dc9 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00002dcd addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00002dd1 cmpq         $2, %rdi
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x00002dd5 jae          LBB0_529
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x00002ddb jmp          LBB0_530
	//0x00002de0 LBB0_526
	0x48, 0x83, 0xff, 0x08, //0x00002de0 cmpq         $8, %rdi
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x00002de4 jb           LBB0_527
	//0x00002dea LBB0_533
	0x49, 0x8b, 0x0c, 0x24, //0x00002dea movq         (%r12), %rcx
	0x48, 0x89, 0x0e, //0x00002dee movq         %rcx, (%rsi)
	0x49, 0x83, 0xc4, 0x08, //0x00002df1 addq         $8, %r12
	0x48, 0x83, 0xc6, 0x08, //0x00002df5 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00002df9 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00002dfd cmpq         $4, %rdi
	0x0f, 0x83, 0xb8, 0xff, 0xff, 0xff, //0x00002e01 jae          LBB0_534
	//0x00002e07 LBB0_528
	0x48, 0x83, 0xff, 0x02, //0x00002e07 cmpq         $2, %rdi
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00002e0b jb           LBB0_530
	//0x00002e11 LBB0_529
	0x41, 0x0f, 0xb7, 0x0c, 0x24, //0x00002e11 movzwl       (%r12), %ecx
	0x66, 0x89, 0x0e, //0x00002e16 movw         %cx, (%rsi)
	0x49, 0x83, 0xc4, 0x02, //0x00002e19 addq         $2, %r12
	0x48, 0x83, 0xc6, 0x02, //0x00002e1d addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00002e21 addq         $-2, %rdi
	//0x00002e25 LBB0_530
	0x4c, 0x89, 0xe1, //0x00002e25 movq         %r12, %rcx
	0x4c, 0x8d, 0xa5, 0x30, 0xff, 0xff, 0xff, //0x00002e28 leaq         $-208(%rbp), %r12
	0x48, 0x85, 0xff, //0x00002e2f testq        %rdi, %rdi
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002e32 movabsq      $6148914691236517205, %r15
	0x0f, 0x84, 0x27, 0xfc, 0xff, 0xff, //0x00002e3c je           LBB0_513
	0x8a, 0x09, //0x00002e42 movb         (%rcx), %cl
	0x88, 0x0e, //0x00002e44 movb         %cl, (%rsi)
	0x4c, 0x8d, 0xa5, 0x30, 0xff, 0xff, 0xff, //0x00002e46 leaq         $-208(%rbp), %r12
	0xe9, 0x17, 0xfc, 0xff, 0xff, //0x00002e4d jmp          LBB0_513
	//0x00002e52 LBB0_535
	0x49, 0x8d, 0x49, 0x05, //0x00002e52 leaq         $5(%r9), %rcx
	//0x00002e56 LBB0_536
	0x48, 0x3b, 0x4f, 0x08, //0x00002e56 cmpq         $8(%rdi), %rcx
	0x0f, 0x86, 0x75, 0x05, 0x00, 0x00, //0x00002e5a jbe          LBB0_580
	0xe9, 0x77, 0x05, 0x00, 0x00, //0x00002e60 jmp          LBB0_582
	//0x00002e65 LBB0_537
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002e65 movabsq      $6148914691236517205, %r15
	0x48, 0x89, 0x7d, 0xb0, //0x00002e6f movq         %rdi, $-80(%rbp)
	0x48, 0x8b, 0x4f, 0x08, //0x00002e73 movq         $8(%rdi), %rcx
	0x48, 0x29, 0xd9, //0x00002e77 subq         %rbx, %rcx
	0x49, 0x01, 0xdc, //0x00002e7a addq         %rbx, %r12
	0x31, 0xdb, //0x00002e7d xorl         %ebx, %ebx
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xb8, 0xd1, 0xff, 0xff, //0x00002e7f movdqu       $-11848(%rip), %xmm10  /* LCPI0_4+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0xa0, 0xd1, 0xff, 0xff, //0x00002e88 movdqu       $-11872(%rip), %xmm1  /* LCPI0_3+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xc9, //0x00002e90 pcmpeqd      %xmm9, %xmm9
	0xf3, 0x0f, 0x6f, 0x1d, 0xb3, 0xd1, 0xff, 0xff, //0x00002e95 movdqu       $-11853(%rip), %xmm3  /* LCPI0_5+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0xbb, 0xd1, 0xff, 0xff, //0x00002e9d movdqu       $-11845(%rip), %xmm4  /* LCPI0_6+0(%rip) */
	0x49, 0xbd, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00002ea5 movabsq      $3689348814741910323, %r13
	0x66, 0x45, 0x0f, 0xef, 0xc0, //0x00002eaf pxor         %xmm8, %xmm8
	0x31, 0xd2, //0x00002eb4 xorl         %edx, %edx
	0x45, 0x31, 0xf6, //0x00002eb6 xorl         %r14d, %r14d
	0x45, 0x31, 0xd2, //0x00002eb9 xorl         %r10d, %r10d
	0x48, 0x83, 0xf9, 0x40, //0x00002ebc cmpq         $64, %rcx
	0x48, 0x89, 0x4d, 0xb8, //0x00002ec0 movq         %rcx, $-72(%rbp)
	0x0f, 0x8c, 0x87, 0x02, 0x00, 0x00, //0x00002ec4 jl           LBB0_546
	//0x00002eca LBB0_540
	0xf3, 0x41, 0x0f, 0x6f, 0x04, 0x24, //0x00002eca movdqu       (%r12), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x6c, 0x24, 0x10, //0x00002ed0 movdqu       $16(%r12), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x7c, 0x24, 0x20, //0x00002ed7 movdqu       $32(%r12), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x74, 0x24, 0x30, //0x00002ede movdqu       $48(%r12), %xmm6
	0x66, 0x0f, 0x6f, 0xd0, //0x00002ee5 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00002ee9 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xfa, //0x00002eee pmovmskb     %xmm2, %edi
	0x66, 0x0f, 0x6f, 0xd5, //0x00002ef2 movdqa       %xmm5, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00002ef6 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00002efb pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd7, //0x00002eff movdqa       %xmm7, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00002f03 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00002f08 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x6f, 0xd6, //0x00002f0c movdqa       %xmm6, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00002f10 pcmpeqb      %xmm10, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xc2, //0x00002f15 pmovmskb     %xmm2, %r8d
	0x49, 0xc1, 0xe0, 0x30, //0x00002f1a shlq         $48, %r8
	0x48, 0xc1, 0xe6, 0x20, //0x00002f1e shlq         $32, %rsi
	0x4c, 0x09, 0xc6, //0x00002f22 orq          %r8, %rsi
	0x48, 0xc1, 0xe1, 0x10, //0x00002f25 shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x00002f29 orq          %rsi, %rcx
	0x48, 0x09, 0xcf, //0x00002f2c orq          %rcx, %rdi
	0x48, 0x89, 0xf9, //0x00002f2f movq         %rdi, %rcx
	0x48, 0x09, 0xd1, //0x00002f32 orq          %rdx, %rcx
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00002f35 jne          LBB0_542
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002f3b movq         $-1, %rdi
	0x31, 0xc9, //0x00002f42 xorl         %ecx, %ecx
	0x48, 0x89, 0x4d, 0xc0, //0x00002f44 movq         %rcx, $-64(%rbp)
	0xe9, 0x44, 0x00, 0x00, 0x00, //0x00002f48 jmp          LBB0_543
	//0x00002f4d LBB0_542
	0x48, 0x89, 0xd1, //0x00002f4d movq         %rdx, %rcx
	0x48, 0xf7, 0xd1, //0x00002f50 notq         %rcx
	0x48, 0x21, 0xf9, //0x00002f53 andq         %rdi, %rcx
	0x48, 0x8d, 0x34, 0x09, //0x00002f56 leaq         (%rcx,%rcx), %rsi
	0x48, 0x09, 0xd6, //0x00002f5a orq          %rdx, %rsi
	0x48, 0x89, 0xf2, //0x00002f5d movq         %rsi, %rdx
	0x48, 0xf7, 0xd2, //0x00002f60 notq         %rdx
	0x49, 0x89, 0xd8, //0x00002f63 movq         %rbx, %r8
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002f66 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xdf, //0x00002f70 andq         %rbx, %rdi
	0x4c, 0x89, 0xc3, //0x00002f73 movq         %r8, %rbx
	0x48, 0x21, 0xd7, //0x00002f76 andq         %rdx, %rdi
	0x31, 0xd2, //0x00002f79 xorl         %edx, %edx
	0x48, 0x01, 0xcf, //0x00002f7b addq         %rcx, %rdi
	0x0f, 0x92, 0xc2, //0x00002f7e setb         %dl
	0x48, 0x89, 0x55, 0xc0, //0x00002f81 movq         %rdx, $-64(%rbp)
	0x48, 0x01, 0xff, //0x00002f85 addq         %rdi, %rdi
	0x4c, 0x31, 0xff, //0x00002f88 xorq         %r15, %rdi
	0x48, 0x21, 0xf7, //0x00002f8b andq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x00002f8e notq         %rdi
	//0x00002f91 LBB0_543
	0x66, 0x0f, 0x6f, 0xd6, //0x00002f91 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00002f95 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00002f99 pmovmskb     %xmm2, %ecx
	0x48, 0xc1, 0xe1, 0x30, //0x00002f9d shlq         $48, %rcx
	0x66, 0x0f, 0x6f, 0xd7, //0x00002fa1 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00002fa5 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00002fa9 pmovmskb     %xmm2, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x00002fad shlq         $32, %rsi
	0x48, 0x09, 0xce, //0x00002fb1 orq          %rcx, %rsi
	0x66, 0x0f, 0x6f, 0xd5, //0x00002fb4 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00002fb8 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00002fbc pmovmskb     %xmm2, %ecx
	0x48, 0xc1, 0xe1, 0x10, //0x00002fc0 shlq         $16, %rcx
	0x48, 0x09, 0xf1, //0x00002fc4 orq          %rsi, %rcx
	0x66, 0x0f, 0x6f, 0xd0, //0x00002fc7 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00002fcb pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00002fcf pmovmskb     %xmm2, %esi
	0x48, 0x09, 0xce, //0x00002fd3 orq          %rcx, %rsi
	0x48, 0x21, 0xfe, //0x00002fd6 andq         %rdi, %rsi
	0x66, 0x48, 0x0f, 0x6e, 0xd6, //0x00002fd9 movq         %rsi, %xmm2
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd1, 0x00, //0x00002fde pclmulqdq    $0, %xmm9, %xmm2
	0x66, 0x49, 0x0f, 0x7e, 0xd3, //0x00002fe5 movq         %xmm2, %r11
	0x49, 0x31, 0xdb, //0x00002fea xorq         %rbx, %r11
	0x66, 0x0f, 0x6f, 0xd0, //0x00002fed movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00002ff1 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xf2, //0x00002ff5 pmovmskb     %xmm2, %esi
	0x66, 0x0f, 0x6f, 0xd5, //0x00002ff9 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00002ffd pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00003001 pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd7, //0x00003005 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00003009 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x0000300d pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd6, //0x00003011 movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00003015 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00003019 pmovmskb     %xmm2, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x0000301d shlq         $48, %rbx
	0x48, 0xc1, 0xe2, 0x20, //0x00003021 shlq         $32, %rdx
	0x48, 0x09, 0xda, //0x00003025 orq          %rbx, %rdx
	0x48, 0xc1, 0xe1, 0x10, //0x00003028 shlq         $16, %rcx
	0x48, 0x09, 0xd1, //0x0000302c orq          %rdx, %rcx
	0x48, 0x09, 0xce, //0x0000302f orq          %rcx, %rsi
	0x4c, 0x89, 0xd9, //0x00003032 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00003035 notq         %rcx
	0x48, 0x21, 0xce, //0x00003038 andq         %rcx, %rsi
	0x66, 0x0f, 0x74, 0xc4, //0x0000303b pcmpeqb      %xmm4, %xmm0
	0x66, 0x44, 0x0f, 0xd7, 0xc0, //0x0000303f pmovmskb     %xmm0, %r8d
	0x66, 0x0f, 0x74, 0xec, //0x00003044 pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xd5, //0x00003048 pmovmskb     %xmm5, %edx
	0x66, 0x0f, 0x74, 0xfc, //0x0000304c pcmpeqb      %xmm4, %xmm7
	0x66, 0x0f, 0xd7, 0xdf, //0x00003050 pmovmskb     %xmm7, %ebx
	0x66, 0x0f, 0x74, 0xf4, //0x00003054 pcmpeqb      %xmm4, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xfe, //0x00003058 pmovmskb     %xmm6, %r15d
	0x49, 0xc1, 0xe7, 0x30, //0x0000305d shlq         $48, %r15
	0x48, 0xc1, 0xe3, 0x20, //0x00003061 shlq         $32, %rbx
	0x4c, 0x09, 0xfb, //0x00003065 orq          %r15, %rbx
	0x48, 0xc1, 0xe2, 0x10, //0x00003068 shlq         $16, %rdx
	0x48, 0x09, 0xda, //0x0000306c orq          %rbx, %rdx
	0x49, 0x09, 0xd0, //0x0000306f orq          %rdx, %r8
	0x48, 0xbf, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00003072 movabsq      $1085102592571150095, %rdi
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000307c movabsq      $6148914691236517205, %r15
	0x49, 0x21, 0xc8, //0x00003086 andq         %rcx, %r8
	0x0f, 0x84, 0x5f, 0x00, 0x00, 0x00, //0x00003089 je           LBB0_538
	0x90, //0x0000308f .p2align 4, 0x90
	//0x00003090 LBB0_544
	0x49, 0x8d, 0x48, 0xff, //0x00003090 leaq         $-1(%r8), %rcx
	0x48, 0x89, 0xca, //0x00003094 movq         %rcx, %rdx
	0x48, 0x21, 0xf2, //0x00003097 andq         %rsi, %rdx
	0x48, 0x89, 0xd3, //0x0000309a movq         %rdx, %rbx
	0x48, 0xd1, 0xeb, //0x0000309d shrq         %rbx
	0x4c, 0x21, 0xfb, //0x000030a0 andq         %r15, %rbx
	0x48, 0x29, 0xda, //0x000030a3 subq         %rbx, %rdx
	0x48, 0x89, 0xd3, //0x000030a6 movq         %rdx, %rbx
	0x4c, 0x21, 0xeb, //0x000030a9 andq         %r13, %rbx
	0x48, 0xc1, 0xea, 0x02, //0x000030ac shrq         $2, %rdx
	0x4c, 0x21, 0xea, //0x000030b0 andq         %r13, %rdx
	0x48, 0x01, 0xda, //0x000030b3 addq         %rbx, %rdx
	0x48, 0x89, 0xd3, //0x000030b6 movq         %rdx, %rbx
	0x48, 0xc1, 0xeb, 0x04, //0x000030b9 shrq         $4, %rbx
	0x48, 0x01, 0xd3, //0x000030bd addq         %rdx, %rbx
	0x48, 0x21, 0xfb, //0x000030c0 andq         %rdi, %rbx
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000030c3 movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xda, //0x000030cd imulq        %rdx, %rbx
	0x48, 0xc1, 0xeb, 0x38, //0x000030d1 shrq         $56, %rbx
	0x4c, 0x01, 0xf3, //0x000030d5 addq         %r14, %rbx
	0x4c, 0x39, 0xd3, //0x000030d8 cmpq         %r10, %rbx
	0x0f, 0x86, 0xd1, 0x01, 0x00, 0x00, //0x000030db jbe          LBB0_562
	0x49, 0x83, 0xc2, 0x01, //0x000030e1 addq         $1, %r10
	0x49, 0x21, 0xc8, //0x000030e5 andq         %rcx, %r8
	0x0f, 0x85, 0xa2, 0xff, 0xff, 0xff, //0x000030e8 jne          LBB0_544
	//0x000030ee LBB0_538
	0x49, 0xc1, 0xfb, 0x3f, //0x000030ee sarq         $63, %r11
	0x48, 0x89, 0xf1, //0x000030f2 movq         %rsi, %rcx
	0x48, 0xd1, 0xe9, //0x000030f5 shrq         %rcx
	0x4c, 0x21, 0xf9, //0x000030f8 andq         %r15, %rcx
	0x48, 0x29, 0xce, //0x000030fb subq         %rcx, %rsi
	0x48, 0x89, 0xf1, //0x000030fe movq         %rsi, %rcx
	0x4c, 0x21, 0xe9, //0x00003101 andq         %r13, %rcx
	0x48, 0xc1, 0xee, 0x02, //0x00003104 shrq         $2, %rsi
	0x4c, 0x21, 0xee, //0x00003108 andq         %r13, %rsi
	0x48, 0x01, 0xce, //0x0000310b addq         %rcx, %rsi
	0x48, 0x89, 0xf1, //0x0000310e movq         %rsi, %rcx
	0x48, 0xc1, 0xe9, 0x04, //0x00003111 shrq         $4, %rcx
	0x48, 0x01, 0xf1, //0x00003115 addq         %rsi, %rcx
	0x48, 0x21, 0xf9, //0x00003118 andq         %rdi, %rcx
	0x48, 0xba, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x0000311b movabsq      $72340172838076673, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x00003125 imulq        %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x38, //0x00003129 shrq         $56, %rcx
	0x49, 0x01, 0xce, //0x0000312d addq         %rcx, %r14
	0x49, 0x83, 0xc4, 0x40, //0x00003130 addq         $64, %r12
	0x48, 0x8b, 0x4d, 0xb8, //0x00003134 movq         $-72(%rbp), %rcx
	0x48, 0x83, 0xc1, 0xc0, //0x00003138 addq         $-64, %rcx
	0x4c, 0x89, 0xdb, //0x0000313c movq         %r11, %rbx
	0x48, 0x8b, 0x55, 0xc0, //0x0000313f movq         $-64(%rbp), %rdx
	0x48, 0x83, 0xf9, 0x40, //0x00003143 cmpq         $64, %rcx
	0x48, 0x89, 0x4d, 0xb8, //0x00003147 movq         %rcx, $-72(%rbp)
	0x0f, 0x8d, 0x79, 0xfd, 0xff, 0xff, //0x0000314b jge          LBB0_540
	//0x00003151 LBB0_546
	0x48, 0x85, 0xc9, //0x00003151 testq        %rcx, %rcx
	0x0f, 0x8e, 0x07, 0x04, 0x00, 0x00, //0x00003154 jle          LBB0_602
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x0000315a movdqu       %xmm8, $-160(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x50, 0xff, 0xff, 0xff, //0x00003163 movdqu       %xmm8, $-176(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x40, 0xff, 0xff, 0xff, //0x0000316c movdqu       %xmm8, $-192(%rbp)
	0xf3, 0x44, 0x0f, 0x7f, 0x85, 0x30, 0xff, 0xff, 0xff, //0x00003175 movdqu       %xmm8, $-208(%rbp)
	0x44, 0x89, 0xe1, //0x0000317e movl         %r12d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00003181 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x00003187 cmpl         $4033, %ecx
	0x0f, 0x82, 0x3e, 0x00, 0x00, 0x00, //0x0000318d jb           LBB0_550
	0x48, 0x83, 0x7d, 0xb8, 0x20, //0x00003193 cmpq         $32, $-72(%rbp)
	0x0f, 0x82, 0x42, 0x00, 0x00, 0x00, //0x00003198 jb           LBB0_551
	0x41, 0x0f, 0x10, 0x04, 0x24, //0x0000319e movups       (%r12), %xmm0
	0x0f, 0x11, 0x85, 0x30, 0xff, 0xff, 0xff, //0x000031a3 movups       %xmm0, $-208(%rbp)
	0xf3, 0x41, 0x0f, 0x6f, 0x44, 0x24, 0x10, //0x000031aa movdqu       $16(%r12), %xmm0
	0xf3, 0x0f, 0x7f, 0x85, 0x40, 0xff, 0xff, 0xff, //0x000031b1 movdqu       %xmm0, $-192(%rbp)
	0x49, 0x83, 0xc4, 0x20, //0x000031b9 addq         $32, %r12
	0x48, 0x8b, 0x4d, 0xb8, //0x000031bd movq         $-72(%rbp), %rcx
	0x48, 0x8d, 0x79, 0xe0, //0x000031c1 leaq         $-32(%rcx), %rdi
	0x48, 0x8d, 0xb5, 0x50, 0xff, 0xff, 0xff, //0x000031c5 leaq         $-176(%rbp), %rsi
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x000031cc jmp          LBB0_552
	//0x000031d1 LBB0_550
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000031d1 movabsq      $6148914691236517205, %r15
	0xe9, 0xea, 0xfc, 0xff, 0xff, //0x000031db jmp          LBB0_540
	//0x000031e0 LBB0_551
	0x48, 0x8d, 0xb5, 0x30, 0xff, 0xff, 0xff, //0x000031e0 leaq         $-208(%rbp), %rsi
	0x48, 0x8b, 0x7d, 0xb8, //0x000031e7 movq         $-72(%rbp), %rdi
	//0x000031eb LBB0_552
	0x48, 0x83, 0xff, 0x10, //0x000031eb cmpq         $16, %rdi
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x000031ef jb           LBB0_553
	0xf3, 0x41, 0x0f, 0x6f, 0x04, 0x24, //0x000031f5 movdqu       (%r12), %xmm0
	0xf3, 0x0f, 0x7f, 0x06, //0x000031fb movdqu       %xmm0, (%rsi)
	0x49, 0x83, 0xc4, 0x10, //0x000031ff addq         $16, %r12
	0x48, 0x83, 0xc6, 0x10, //0x00003203 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00003207 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x0000320b cmpq         $8, %rdi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x0000320f jae          LBB0_560
	//0x00003215 LBB0_554
	0x48, 0x83, 0xff, 0x04, //0x00003215 cmpq         $4, %rdi
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x00003219 jb           LBB0_555
	//0x0000321f LBB0_561
	0x41, 0x8b, 0x0c, 0x24, //0x0000321f movl         (%r12), %ecx
	0x89, 0x0e, //0x00003223 movl         %ecx, (%rsi)
	0x49, 0x83, 0xc4, 0x04, //0x00003225 addq         $4, %r12
	0x48, 0x83, 0xc6, 0x04, //0x00003229 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x0000322d addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00003231 cmpq         $2, %rdi
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x00003235 jae          LBB0_556
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x0000323b jmp          LBB0_557
	//0x00003240 LBB0_553
	0x48, 0x83, 0xff, 0x08, //0x00003240 cmpq         $8, %rdi
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x00003244 jb           LBB0_554
	//0x0000324a LBB0_560
	0x49, 0x8b, 0x0c, 0x24, //0x0000324a movq         (%r12), %rcx
	0x48, 0x89, 0x0e, //0x0000324e movq         %rcx, (%rsi)
	0x49, 0x83, 0xc4, 0x08, //0x00003251 addq         $8, %r12
	0x48, 0x83, 0xc6, 0x08, //0x00003255 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00003259 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x0000325d cmpq         $4, %rdi
	0x0f, 0x83, 0xb8, 0xff, 0xff, 0xff, //0x00003261 jae          LBB0_561
	//0x00003267 LBB0_555
	0x48, 0x83, 0xff, 0x02, //0x00003267 cmpq         $2, %rdi
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x0000326b jb           LBB0_557
	//0x00003271 LBB0_556
	0x41, 0x0f, 0xb7, 0x0c, 0x24, //0x00003271 movzwl       (%r12), %ecx
	0x66, 0x89, 0x0e, //0x00003276 movw         %cx, (%rsi)
	0x49, 0x83, 0xc4, 0x02, //0x00003279 addq         $2, %r12
	0x48, 0x83, 0xc6, 0x02, //0x0000327d addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00003281 addq         $-2, %rdi
	//0x00003285 LBB0_557
	0x4c, 0x89, 0xe1, //0x00003285 movq         %r12, %rcx
	0x4c, 0x8d, 0xa5, 0x30, 0xff, 0xff, 0xff, //0x00003288 leaq         $-208(%rbp), %r12
	0x48, 0x85, 0xff, //0x0000328f testq        %rdi, %rdi
	0x49, 0xbf, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00003292 movabsq      $6148914691236517205, %r15
	0x0f, 0x84, 0x28, 0xfc, 0xff, 0xff, //0x0000329c je           LBB0_540
	0x8a, 0x09, //0x000032a2 movb         (%rcx), %cl
	0x88, 0x0e, //0x000032a4 movb         %cl, (%rsi)
	0x4c, 0x8d, 0xa5, 0x30, 0xff, 0xff, 0xff, //0x000032a6 leaq         $-208(%rbp), %r12
	0xe9, 0x18, 0xfc, 0xff, 0xff, //0x000032ad jmp          LBB0_540
	//0x000032b2 LBB0_562
	0x48, 0x8b, 0x75, 0xb0, //0x000032b2 movq         $-80(%rbp), %rsi
	0x48, 0x8b, 0x46, 0x08, //0x000032b6 movq         $8(%rsi), %rax
	0x49, 0x0f, 0xbc, 0xc8, //0x000032ba bsfq         %r8, %rcx
	0x48, 0x2b, 0x4d, 0xb8, //0x000032be subq         $-72(%rbp), %rcx
	0x48, 0x01, 0xc8, //0x000032c2 addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x000032c5 addq         $1, %rax
	0x48, 0x8b, 0x55, 0xd0, //0x000032c9 movq         $-48(%rbp), %rdx
	0x48, 0x89, 0x02, //0x000032cd movq         %rax, (%rdx)
	0x48, 0x8b, 0x4e, 0x08, //0x000032d0 movq         $8(%rsi), %rcx
	0x48, 0x39, 0xc8, //0x000032d4 cmpq         %rcx, %rax
	0x48, 0x0f, 0x47, 0xc1, //0x000032d7 cmovaq       %rcx, %rax
	0x48, 0x89, 0x02, //0x000032db movq         %rax, (%rdx)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000032de movq         $-1, %rax
	0x4c, 0x0f, 0x47, 0xc8, //0x000032e5 cmovaq       %rax, %r9
	0xe9, 0xeb, 0x00, 0x00, 0x00, //0x000032e9 jmp          LBB0_581
	//0x000032ee LBB0_563
	0x0f, 0xbc, 0xc6, //0x000032ee bsfl         %esi, %eax
	0x4c, 0x01, 0xc8, //0x000032f1 addq         %r9, %rax
	0x4c, 0x01, 0xf0, //0x000032f4 addq         %r14, %rax
	0x48, 0x83, 0xc0, 0x02, //0x000032f7 addq         $2, %rax
	0x48, 0x8b, 0x4d, 0xd0, //0x000032fb movq         $-48(%rbp), %rcx
	0x48, 0x89, 0x01, //0x000032ff movq         %rax, (%rcx)
	0xe9, 0xd2, 0x00, 0x00, 0x00, //0x00003302 jmp          LBB0_581
	//0x00003307 LBB0_564
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003307 movq         $-1, %rcx
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x0000330e jmp          LBB0_566
	//0x00003313 LBB0_565
	0x4c, 0x89, 0xc9, //0x00003313 movq         %r9, %rcx
	//0x00003316 LBB0_566
	0x48, 0xf7, 0xd1, //0x00003316 notq         %rcx
	0x48, 0x8b, 0x45, 0xc0, //0x00003319 movq         $-64(%rbp), %rax
	0x48, 0x01, 0xc8, //0x0000331d addq         %rcx, %rax
	0x49, 0x89, 0x45, 0x00, //0x00003320 movq         %rax, (%r13)
	//0x00003324 LBB0_567
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003324 movq         $-2, %rax
	0xe9, 0xac, 0x00, 0x00, 0x00, //0x0000332b jmp          LBB0_582
	//0x00003330 LBB0_568
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00003330 movq         $-2, %rcx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00003337 movl         $2, %eax
	0x48, 0x01, 0xc3, //0x0000333c addq         %rax, %rbx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000333f movq         $-1, %rax
	0x49, 0x01, 0xce, //0x00003346 addq         %rcx, %r14
	0x0f, 0x8e, 0x8d, 0x00, 0x00, 0x00, //0x00003349 jle          LBB0_582
	//0x0000334f LBB0_570
	0x0f, 0xb6, 0x03, //0x0000334f movzbl       (%rbx), %eax
	0x3c, 0x5c, //0x00003352 cmpb         $92, %al
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00003354 je           LBB0_568
	0x3c, 0x22, //0x0000335a cmpb         $34, %al
	0x0f, 0x84, 0xba, 0x01, 0x00, 0x00, //0x0000335c je           LBB0_597
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003362 movq         $-1, %rcx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00003369 movl         $1, %eax
	0x48, 0x01, 0xc3, //0x0000336e addq         %rax, %rbx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003371 movq         $-1, %rax
	0x49, 0x01, 0xce, //0x00003378 addq         %rcx, %r14
	0x0f, 0x8f, 0xce, 0xff, 0xff, 0xff, //0x0000337b jg           LBB0_570
	0xe9, 0x56, 0x00, 0x00, 0x00, //0x00003381 jmp          LBB0_582
	//0x00003386 LBB0_586
	0x49, 0x89, 0x4d, 0x00, //0x00003386 movq         %rcx, (%r13)
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x0000338a jmp          LBB0_582
	//0x0000338f LBB0_573
	0x48, 0x8b, 0x4d, 0xc8, //0x0000338f movq         $-56(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x00003393 cmpq         $-1, %rcx
	0x0f, 0x85, 0x2d, 0x00, 0x00, 0x00, //0x00003397 jne          LBB0_579
	0x48, 0x0f, 0xbc, 0xcf, //0x0000339d bsfq         %rdi, %rcx
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x000033a1 jmp          LBB0_578
	//0x000033a6 LBB0_575
	0x4c, 0x29, 0xe3, //0x000033a6 subq         %r12, %rbx
	0x48, 0x01, 0xc3, //0x000033a9 addq         %rax, %rbx
	0x49, 0x89, 0x5d, 0x00, //0x000033ac movq         %rbx, (%r13)
	0xe9, 0x24, 0x00, 0x00, 0x00, //0x000033b0 jmp          LBB0_581
	//0x000033b5 LBB0_576
	0x48, 0x8b, 0x4d, 0xc8, //0x000033b5 movq         $-56(%rbp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x000033b9 cmpq         $-1, %rcx
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x000033bd jne          LBB0_579
	0x49, 0x0f, 0xbc, 0xcd, //0x000033c3 bsfq         %r13, %rcx
	//0x000033c7 LBB0_578
	0x4c, 0x01, 0xc9, //0x000033c7 addq         %r9, %rcx
	//0x000033ca LBB0_579
	0x49, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000033ca movq         $-2, %r9
	0x4c, 0x8b, 0x6d, 0xd0, //0x000033d1 movq         $-48(%rbp), %r13
	//0x000033d5 LBB0_580
	0x49, 0x89, 0x4d, 0x00, //0x000033d5 movq         %rcx, (%r13)
	//0x000033d9 LBB0_581
	0x4c, 0x89, 0xc8, //0x000033d9 movq         %r9, %rax
	//0x000033dc LBB0_582
	0x48, 0x81, 0xc4, 0xa8, 0x00, 0x00, 0x00, //0x000033dc addq         $168, %rsp
	0x5b, //0x000033e3 popq         %rbx
	0x41, 0x5c, //0x000033e4 popq         %r12
	0x41, 0x5d, //0x000033e6 popq         %r13
	0x41, 0x5e, //0x000033e8 popq         %r14
	0x41, 0x5f, //0x000033ea popq         %r15
	0x5d, //0x000033ec popq         %rbp
	0xc3, //0x000033ed retq         
	//0x000033ee LBB0_583
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000033ee movq         $-1, %r14
	//0x000033f5 LBB0_584
	0x48, 0x8b, 0x4d, 0xc0, //0x000033f5 movq         $-64(%rbp), %rcx
	0x4c, 0x29, 0xf1, //0x000033f9 subq         %r14, %rcx
	0x48, 0x8b, 0x45, 0xd0, //0x000033fc movq         $-48(%rbp), %rax
	0x48, 0x89, 0x08, //0x00003400 movq         %rcx, (%rax)
	0xe9, 0x1c, 0xff, 0xff, 0xff, //0x00003403 jmp          LBB0_567
	//0x00003408 LBB0_587
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003408 movq         $-2, %rax
	0x80, 0xf9, 0x61, //0x0000340f cmpb         $97, %cl
	0x0f, 0x85, 0xc4, 0xff, 0xff, 0xff, //0x00003412 jne          LBB0_582
	0x48, 0x8d, 0x4e, 0x02, //0x00003418 leaq         $2(%rsi), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x0000341c movq         %rcx, (%r13)
	0x41, 0x80, 0x7c, 0x34, 0x02, 0x6c, //0x00003420 cmpb         $108, $2(%r12,%rsi)
	0x0f, 0x85, 0xb0, 0xff, 0xff, 0xff, //0x00003426 jne          LBB0_582
	0x48, 0x8d, 0x4e, 0x03, //0x0000342c leaq         $3(%rsi), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x00003430 movq         %rcx, (%r13)
	0x41, 0x80, 0x7c, 0x34, 0x03, 0x73, //0x00003434 cmpb         $115, $3(%r12,%rsi)
	0x0f, 0x85, 0x9c, 0xff, 0xff, 0xff, //0x0000343a jne          LBB0_582
	0x48, 0x8d, 0x4e, 0x04, //0x00003440 leaq         $4(%rsi), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x00003444 movq         %rcx, (%r13)
	0x41, 0x80, 0x7c, 0x34, 0x04, 0x65, //0x00003448 cmpb         $101, $4(%r12,%rsi)
	0x0f, 0x85, 0x88, 0xff, 0xff, 0xff, //0x0000344e jne          LBB0_582
	0x48, 0x83, 0xc6, 0x05, //0x00003454 addq         $5, %rsi
	0x49, 0x89, 0x75, 0x00, //0x00003458 movq         %rsi, (%r13)
	0xe9, 0x7b, 0xff, 0xff, 0xff, //0x0000345c jmp          LBB0_582
	//0x00003461 LBB0_293
	0x49, 0x89, 0x5d, 0x00, //0x00003461 movq         %rbx, (%r13)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003465 movq         $-2, %rax
	0x80, 0x39, 0x6e, //0x0000346c cmpb         $110, (%rcx)
	0x0f, 0x85, 0x67, 0xff, 0xff, 0xff, //0x0000346f jne          LBB0_582
	0x48, 0x8d, 0x4b, 0x01, //0x00003475 leaq         $1(%rbx), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x00003479 movq         %rcx, (%r13)
	0x41, 0x80, 0x7c, 0x1c, 0x01, 0x75, //0x0000347d cmpb         $117, $1(%r12,%rbx)
	0x0f, 0x85, 0x53, 0xff, 0xff, 0xff, //0x00003483 jne          LBB0_582
	0x48, 0x8d, 0x4b, 0x02, //0x00003489 leaq         $2(%rbx), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x0000348d movq         %rcx, (%r13)
	0x41, 0x80, 0x7c, 0x1c, 0x02, 0x6c, //0x00003491 cmpb         $108, $2(%r12,%rbx)
	0x0f, 0x85, 0x3f, 0xff, 0xff, 0xff, //0x00003497 jne          LBB0_582
	0x48, 0x8d, 0x4b, 0x03, //0x0000349d leaq         $3(%rbx), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x000034a1 movq         %rcx, (%r13)
	0x41, 0x80, 0x7c, 0x1c, 0x03, 0x6c, //0x000034a5 cmpb         $108, $3(%r12,%rbx)
	0x0f, 0x85, 0x2b, 0xff, 0xff, 0xff, //0x000034ab jne          LBB0_582
	0xe9, 0x50, 0x00, 0x00, 0x00, //0x000034b1 jmp          LBB0_596
	//0x000034b6 LBB0_592
	0x49, 0x89, 0x5d, 0x00, //0x000034b6 movq         %rbx, (%r13)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000034ba movq         $-2, %rax
	0x80, 0x39, 0x74, //0x000034c1 cmpb         $116, (%rcx)
	0x0f, 0x85, 0x12, 0xff, 0xff, 0xff, //0x000034c4 jne          LBB0_582
	0x48, 0x8d, 0x4b, 0x01, //0x000034ca leaq         $1(%rbx), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x000034ce movq         %rcx, (%r13)
	0x41, 0x80, 0x7c, 0x1c, 0x01, 0x72, //0x000034d2 cmpb         $114, $1(%r12,%rbx)
	0x0f, 0x85, 0xfe, 0xfe, 0xff, 0xff, //0x000034d8 jne          LBB0_582
	0x48, 0x8d, 0x4b, 0x02, //0x000034de leaq         $2(%rbx), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x000034e2 movq         %rcx, (%r13)
	0x41, 0x80, 0x7c, 0x1c, 0x02, 0x75, //0x000034e6 cmpb         $117, $2(%r12,%rbx)
	0x0f, 0x85, 0xea, 0xfe, 0xff, 0xff, //0x000034ec jne          LBB0_582
	0x48, 0x8d, 0x4b, 0x03, //0x000034f2 leaq         $3(%rbx), %rcx
	0x49, 0x89, 0x4d, 0x00, //0x000034f6 movq         %rcx, (%r13)
	0x41, 0x80, 0x7c, 0x1c, 0x03, 0x65, //0x000034fa cmpb         $101, $3(%r12,%rbx)
	0x0f, 0x85, 0xd6, 0xfe, 0xff, 0xff, //0x00003500 jne          LBB0_582
	//0x00003506 LBB0_596
	0x48, 0x83, 0xc3, 0x04, //0x00003506 addq         $4, %rbx
	0x49, 0x89, 0x5d, 0x00, //0x0000350a movq         %rbx, (%r13)
	0xe9, 0xc9, 0xfe, 0xff, 0xff, //0x0000350e jmp          LBB0_582
	//0x00003513 LBB0_599
	0x4c, 0x89, 0x4d, 0x98, //0x00003513 movq         %r9, $-104(%rbp)
	0xe9, 0xc3, 0xf3, 0xff, 0xff, //0x00003517 jmp          LBB0_498
	//0x0000351c LBB0_597
	0x4c, 0x29, 0xe3, //0x0000351c subq         %r12, %rbx
	0x48, 0x83, 0xc3, 0x01, //0x0000351f addq         $1, %rbx
	0x48, 0x89, 0x1a, //0x00003523 movq         %rbx, (%rdx)
	0xe9, 0xae, 0xfe, 0xff, 0xff, //0x00003526 jmp          LBB0_581
	//0x0000352b LBB0_345
	0x4c, 0x01, 0xc9, //0x0000352b addq         %r9, %rcx
	0x49, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000352e movq         $-2, %r9
	0xe9, 0x9b, 0xfe, 0xff, 0xff, //0x00003535 jmp          LBB0_580
	//0x0000353a LBB0_598
	0x4c, 0x01, 0xe3, //0x0000353a addq         %r12, %rbx
	0x48, 0x85, 0xc9, //0x0000353d testq        %rcx, %rcx
	0x0f, 0x85, 0x1f, 0xf3, 0xff, 0xff, //0x00003540 jne          LBB0_488
	0xe9, 0x4f, 0xf3, 0xff, 0xff, //0x00003546 jmp          LBB0_494
	//0x0000354b LBB0_600
	0x4c, 0x01, 0xe3, //0x0000354b addq         %r12, %rbx
	0xe9, 0x9f, 0xf4, 0xff, 0xff, //0x0000354e jmp          LBB0_509
	//0x00003553 LBB0_601
	0x4d, 0x29, 0xe1, //0x00003553 subq         %r12, %r9
	0x49, 0x01, 0xc9, //0x00003556 addq         %rcx, %r9
	0x4c, 0x89, 0xc9, //0x00003559 movq         %r9, %rcx
	0xe9, 0x69, 0xfe, 0xff, 0xff, //0x0000355c jmp          LBB0_579
	//0x00003561 LBB0_602
	0x48, 0x8b, 0x4d, 0xb0, //0x00003561 movq         $-80(%rbp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x00003565 movq         $8(%rcx), %rcx
	0x48, 0x8b, 0x55, 0xd0, //0x00003569 movq         $-48(%rbp), %rdx
	0x48, 0x89, 0x0a, //0x0000356d movq         %rcx, (%rdx)
	0xe9, 0x67, 0xfe, 0xff, 0xff, //0x00003570 jmp          LBB0_582
	//0x00003575 LBB0_603
	0x4d, 0x29, 0xe1, //0x00003575 subq         %r12, %r9
	0x4c, 0x89, 0xc9, //0x00003578 movq         %r9, %rcx
	0x49, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000357b movq         $-2, %r9
	0xe9, 0x4e, 0xfe, 0xff, 0xff, //0x00003582 jmp          LBB0_580
	//0x00003587 LBB0_604
	0x49, 0x8d, 0x48, 0xff, //0x00003587 leaq         $-1(%r8), %rcx
	0x4c, 0x39, 0xf1, //0x0000358b cmpq         %r14, %rcx
	0x0f, 0x84, 0x48, 0xfe, 0xff, 0xff, //0x0000358e je           LBB0_582
	0x4b, 0x8d, 0x1c, 0x16, //0x00003594 leaq         (%r14,%r10), %rbx
	0x48, 0x83, 0xc3, 0x02, //0x00003598 addq         $2, %rbx
	0x4d, 0x29, 0xf0, //0x0000359c subq         %r14, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x0000359f addq         $-2, %r8
	0x4d, 0x89, 0xc6, //0x000035a3 movq         %r8, %r14
	0xe9, 0x47, 0xf4, 0xff, 0xff, //0x000035a6 jmp          LBB0_509
	//0x000035ab LBB0_322
	0x4d, 0x29, 0xe1, //0x000035ab subq         %r12, %r9
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x000035ae jmp          LBB0_607
	//0x000035b3 LBB0_606
	0x4c, 0x2b, 0x4d, 0xb8, //0x000035b3 subq         $-72(%rbp), %r9
	//0x000035b7 LBB0_607
	0x49, 0x01, 0xc9, //0x000035b7 addq         %rcx, %r9
	0x4c, 0x89, 0xc9, //0x000035ba movq         %r9, %rcx
	0xe9, 0x08, 0xfe, 0xff, 0xff, //0x000035bd jmp          LBB0_579
	//0x000035c2 LBB0_608
	0x4c, 0x2b, 0x4d, 0xb8, //0x000035c2 subq         $-72(%rbp), %r9
	0x4c, 0x89, 0xc9, //0x000035c6 movq         %r9, %rcx
	0xe9, 0xfc, 0xfd, 0xff, 0xff, //0x000035c9 jmp          LBB0_579
	0x90, 0x90, //0x000035ce .p2align 2, 0x90
	// // .set L0_0_set_582, LBB0_582-LJTI0_0
	// // .set L0_0_set_499, LBB0_499-LJTI0_0
	// // .set L0_0_set_500, LBB0_500-LJTI0_0
	// // .set L0_0_set_483, LBB0_483-LJTI0_0
	// // .set L0_0_set_510, LBB0_510-LJTI0_0
	// // .set L0_0_set_535, LBB0_535-LJTI0_0
	// // .set L0_0_set_496, LBB0_496-LJTI0_0
	// // .set L0_0_set_537, LBB0_537-LJTI0_0
	//0x000035d0 LJTI0_0
	0x0c, 0xfe, 0xff, 0xff, //0x000035d0 .long L0_0_set_582
	0x1f, 0xf3, 0xff, 0xff, //0x000035d4 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000035d8 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000035dc .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000035e0 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000035e4 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000035e8 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000035ec .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000035f0 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000035f4 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000035f8 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000035fc .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003600 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003604 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003608 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000360c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003610 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003614 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003618 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000361c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003620 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003624 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003628 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000362c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003630 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003634 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003638 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000363c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003640 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003644 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003648 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000364c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003650 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003654 .long L0_0_set_499
	0x28, 0xf3, 0xff, 0xff, //0x00003658 .long L0_0_set_500
	0x1f, 0xf3, 0xff, 0xff, //0x0000365c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003660 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003664 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003668 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000366c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003670 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003674 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003678 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000367c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003680 .long L0_0_set_499
	0x05, 0xf2, 0xff, 0xff, //0x00003684 .long L0_0_set_483
	0x1f, 0xf3, 0xff, 0xff, //0x00003688 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000368c .long L0_0_set_499
	0x05, 0xf2, 0xff, 0xff, //0x00003690 .long L0_0_set_483
	0x05, 0xf2, 0xff, 0xff, //0x00003694 .long L0_0_set_483
	0x05, 0xf2, 0xff, 0xff, //0x00003698 .long L0_0_set_483
	0x05, 0xf2, 0xff, 0xff, //0x0000369c .long L0_0_set_483
	0x05, 0xf2, 0xff, 0xff, //0x000036a0 .long L0_0_set_483
	0x05, 0xf2, 0xff, 0xff, //0x000036a4 .long L0_0_set_483
	0x05, 0xf2, 0xff, 0xff, //0x000036a8 .long L0_0_set_483
	0x05, 0xf2, 0xff, 0xff, //0x000036ac .long L0_0_set_483
	0x05, 0xf2, 0xff, 0xff, //0x000036b0 .long L0_0_set_483
	0x05, 0xf2, 0xff, 0xff, //0x000036b4 .long L0_0_set_483
	0x1f, 0xf3, 0xff, 0xff, //0x000036b8 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036bc .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036c0 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036c4 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036c8 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036cc .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036d0 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036d4 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036d8 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036dc .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036e0 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036e4 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036e8 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036ec .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036f0 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036f4 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036f8 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000036fc .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003700 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003704 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003708 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000370c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003710 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003714 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003718 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000371c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003720 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003724 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003728 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000372c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003730 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003734 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003738 .long L0_0_set_499
	0x34, 0xf4, 0xff, 0xff, //0x0000373c .long L0_0_set_510
	0x1f, 0xf3, 0xff, 0xff, //0x00003740 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003744 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003748 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000374c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003750 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003754 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003758 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000375c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003760 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003764 .long L0_0_set_499
	0x82, 0xf8, 0xff, 0xff, //0x00003768 .long L0_0_set_535
	0x1f, 0xf3, 0xff, 0xff, //0x0000376c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003770 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003774 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003778 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000377c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003780 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003784 .long L0_0_set_499
	0xec, 0xf2, 0xff, 0xff, //0x00003788 .long L0_0_set_496
	0x1f, 0xf3, 0xff, 0xff, //0x0000378c .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003790 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003794 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x00003798 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x0000379c .long L0_0_set_499
	0xec, 0xf2, 0xff, 0xff, //0x000037a0 .long L0_0_set_496
	0x1f, 0xf3, 0xff, 0xff, //0x000037a4 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000037a8 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000037ac .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000037b0 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000037b4 .long L0_0_set_499
	0x1f, 0xf3, 0xff, 0xff, //0x000037b8 .long L0_0_set_499
	0x95, 0xf8, 0xff, 0xff, //0x000037bc .long L0_0_set_537
	// // .set L0_1_set_57, LBB0_57-LJTI0_1
	// // .set L0_1_set_85, LBB0_85-LJTI0_1
	// // .set L0_1_set_63, LBB0_63-LJTI0_1
	// // .set L0_1_set_83, LBB0_83-LJTI0_1
	// // .set L0_1_set_60, LBB0_60-LJTI0_1
	// // .set L0_1_set_88, LBB0_88-LJTI0_1
	//0x000037c0 LJTI0_1
	0xc5, 0xcc, 0xff, 0xff, //0x000037c0 .long L0_1_set_57
	0xde, 0xce, 0xff, 0xff, //0x000037c4 .long L0_1_set_85
	0x03, 0xcd, 0xff, 0xff, //0x000037c8 .long L0_1_set_63
	0xc7, 0xce, 0xff, 0xff, //0x000037cc .long L0_1_set_83
	0xde, 0xcc, 0xff, 0xff, //0x000037d0 .long L0_1_set_60
	0x7f, 0xd1, 0xff, 0xff, //0x000037d4 .long L0_1_set_88
	// // .set L0_2_set_582, LBB0_582-LJTI0_2
	// // .set L0_2_set_567, LBB0_567-LJTI0_2
	// // .set L0_2_set_231, LBB0_231-LJTI0_2
	// // .set L0_2_set_255, LBB0_255-LJTI0_2
	// // .set L0_2_set_90, LBB0_90-LJTI0_2
	// // .set L0_2_set_226, LBB0_226-LJTI0_2
	// // .set L0_2_set_228, LBB0_228-LJTI0_2
	// // .set L0_2_set_291, LBB0_291-LJTI0_2
	// // .set L0_2_set_299, LBB0_299-LJTI0_2
	// // .set L0_2_set_297, LBB0_297-LJTI0_2
	//0x000037d8 LJTI0_2
	0x04, 0xfc, 0xff, 0xff, //0x000037d8 .long L0_2_set_582
	0x4c, 0xfb, 0xff, 0xff, //0x000037dc .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000037e0 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000037e4 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000037e8 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000037ec .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000037f0 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000037f4 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000037f8 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000037fc .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003800 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003804 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003808 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000380c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003810 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003814 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003818 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000381c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003820 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003824 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003828 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000382c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003830 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003834 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003838 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000383c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003840 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003844 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003848 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000384c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003850 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003854 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003858 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000385c .long L0_2_set_567
	0x37, 0xd9, 0xff, 0xff, //0x00003860 .long L0_2_set_231
	0x4c, 0xfb, 0xff, 0xff, //0x00003864 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003868 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000386c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003870 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003874 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003878 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000387c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003880 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003884 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003888 .long L0_2_set_567
	0xca, 0xda, 0xff, 0xff, //0x0000388c .long L0_2_set_255
	0x4c, 0xfb, 0xff, 0xff, //0x00003890 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003894 .long L0_2_set_567
	0x1e, 0xcf, 0xff, 0xff, //0x00003898 .long L0_2_set_90
	0x1e, 0xcf, 0xff, 0xff, //0x0000389c .long L0_2_set_90
	0x1e, 0xcf, 0xff, 0xff, //0x000038a0 .long L0_2_set_90
	0x1e, 0xcf, 0xff, 0xff, //0x000038a4 .long L0_2_set_90
	0x1e, 0xcf, 0xff, 0xff, //0x000038a8 .long L0_2_set_90
	0x1e, 0xcf, 0xff, 0xff, //0x000038ac .long L0_2_set_90
	0x1e, 0xcf, 0xff, 0xff, //0x000038b0 .long L0_2_set_90
	0x1e, 0xcf, 0xff, 0xff, //0x000038b4 .long L0_2_set_90
	0x1e, 0xcf, 0xff, 0xff, //0x000038b8 .long L0_2_set_90
	0x1e, 0xcf, 0xff, 0xff, //0x000038bc .long L0_2_set_90
	0x4c, 0xfb, 0xff, 0xff, //0x000038c0 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038c4 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038c8 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038cc .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038d0 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038d4 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038d8 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038dc .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038e0 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038e4 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038e8 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038ec .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038f0 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038f4 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038f8 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000038fc .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003900 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003904 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003908 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000390c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003910 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003914 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003918 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000391c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003920 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003924 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003928 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000392c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003930 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003934 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003938 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000393c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003940 .long L0_2_set_567
	0xcf, 0xd8, 0xff, 0xff, //0x00003944 .long L0_2_set_226
	0x4c, 0xfb, 0xff, 0xff, //0x00003948 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000394c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003950 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003954 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003958 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000395c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003960 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003964 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003968 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000396c .long L0_2_set_567
	0xf3, 0xd8, 0xff, 0xff, //0x00003970 .long L0_2_set_228
	0x4c, 0xfb, 0xff, 0xff, //0x00003974 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003978 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000397c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003980 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003984 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003988 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000398c .long L0_2_set_567
	0x09, 0xdd, 0xff, 0xff, //0x00003990 .long L0_2_set_291
	0x4c, 0xfb, 0xff, 0xff, //0x00003994 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x00003998 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x0000399c .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000039a0 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000039a4 .long L0_2_set_567
	0x56, 0xdd, 0xff, 0xff, //0x000039a8 .long L0_2_set_299
	0x4c, 0xfb, 0xff, 0xff, //0x000039ac .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000039b0 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000039b4 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000039b8 .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000039bc .long L0_2_set_567
	0x4c, 0xfb, 0xff, 0xff, //0x000039c0 .long L0_2_set_567
	0x32, 0xdd, 0xff, 0xff, //0x000039c4 .long L0_2_set_297
	// // .set L0_3_set_280, LBB0_280-LJTI0_3
	// // .set L0_3_set_330, LBB0_330-LJTI0_3
	// // .set L0_3_set_286, LBB0_286-LJTI0_3
	// // .set L0_3_set_289, LBB0_289-LJTI0_3
	//0x000039c8 LJTI0_3
	0x9c, 0xda, 0xff, 0xff, //0x000039c8 .long L0_3_set_280
	0xbe, 0xdd, 0xff, 0xff, //0x000039cc .long L0_3_set_330
	0x9c, 0xda, 0xff, 0xff, //0x000039d0 .long L0_3_set_280
	0xea, 0xda, 0xff, 0xff, //0x000039d4 .long L0_3_set_286
	0xbe, 0xdd, 0xff, 0xff, //0x000039d8 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x000039dc .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x000039e0 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x000039e4 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x000039e8 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x000039ec .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x000039f0 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x000039f4 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x000039f8 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x000039fc .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x00003a00 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x00003a04 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x00003a08 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x00003a0c .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x00003a10 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x00003a14 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x00003a18 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x00003a1c .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x00003a20 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x00003a24 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x00003a28 .long L0_3_set_330
	0xbe, 0xdd, 0xff, 0xff, //0x00003a2c .long L0_3_set_330
	0x06, 0xdb, 0xff, 0xff, //0x00003a30 .long L0_3_set_289
	// // .set L0_4_set_122, LBB0_122-LJTI0_4
	// // .set L0_4_set_199, LBB0_199-LJTI0_4
	// // .set L0_4_set_124, LBB0_124-LJTI0_4
	// // .set L0_4_set_116, LBB0_116-LJTI0_4
	//0x00003a34 LJTI0_4
	0xe5, 0xce, 0xff, 0xff, //0x00003a34 .long L0_4_set_122
	0x05, 0xd5, 0xff, 0xff, //0x00003a38 .long L0_4_set_199
	0xe5, 0xce, 0xff, 0xff, //0x00003a3c .long L0_4_set_122
	0xf8, 0xce, 0xff, 0xff, //0x00003a40 .long L0_4_set_124
	0x05, 0xd5, 0xff, 0xff, //0x00003a44 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a48 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a4c .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a50 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a54 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a58 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a5c .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a60 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a64 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a68 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a6c .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a70 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a74 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a78 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a7c .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a80 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a84 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a88 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a8c .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a90 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a94 .long L0_4_set_199
	0x05, 0xd5, 0xff, 0xff, //0x00003a98 .long L0_4_set_199
	0x9d, 0xce, 0xff, 0xff, //0x00003a9c .long L0_4_set_116
	//0x00003aa0 .p2align 2, 0x00
	//0x00003aa0 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00003aa0 .long 2
}
 
