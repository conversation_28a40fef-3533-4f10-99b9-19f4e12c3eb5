// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__unquote = 16
)

const (
    _stack__unquote = 80
)

const (
    _size__unquote = 1888
)

var (
    _pcsp__unquote = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0x539, 80},
        {0x53a, 48},
        {0x53c, 40},
        {0x53e, 32},
        {0x540, 24},
        {0x542, 16},
        {0x543, 8},
        {0x544, 0},
        {0x760, 80},
    }
)

var _cfunc_unquote = []loader.CFunc{
    {"_unquote_entry", 0,  _entry__unquote, 0, nil},
    {"_unquote", _entry__unquote, _size__unquote, _stack__unquote, _pcsp__unquote},
}
