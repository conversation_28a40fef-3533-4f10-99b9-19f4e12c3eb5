// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__i64toa = 80
)

const (
    _stack__i64toa = 8
)

const (
    _size__i64toa = 2320
)

var (
    _pcsp__i64toa = [][2]uint32{
        {0x1, 0},
        {0xae, 8},
        {0xaf, 0},
        {0x201, 8},
        {0x202, 0},
        {0x287, 8},
        {0x288, 0},
        {0x464, 8},
        {0x465, 0},
        {0x4f0, 8},
        {0x4f1, 0},
        {0x62c, 8},
        {0x62d, 0},
        {0x797, 8},
        {0x798, 0},
        {0x909, 8},
        {0x910, 0},
    }
)

var _cfunc_i64toa = []loader.CFunc{
    {"_i64toa_entry", 0,  _entry__i64toa, 0, nil},
    {"_i64toa", _entry__i64toa, _size__i64toa, _stack__i64toa, _pcsp__i64toa},
}
