// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__vsigned = 0
)

const (
    _stack__vsigned = 16
)

const (
    _size__vsigned = 356
)

var (
    _pcsp__vsigned = [][2]uint32{
        {0x1, 0},
        {0x5, 8},
        {0x70, 16},
        {0x71, 8},
        {0x72, 0},
        {0x7d, 16},
        {0x7e, 8},
        {0x7f, 0},
        {0x117, 16},
        {0x118, 8},
        {0x119, 0},
        {0x11d, 16},
        {0x11e, 8},
        {0x11f, 0},
        {0x155, 16},
        {0x156, 8},
        {0x157, 0},
        {0x162, 16},
        {0x163, 8},
        {0x164, 0},
    }
)

var _cfunc_vsigned = []loader.CFunc{
    {"_vsigned_entry", 0,  _entry__vsigned, 0, nil},
    {"_vsigned", _entry__vsigned, _size__vsigned, _stack__vsigned, _pcsp__vsigned},
}
