// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_one = 256
)

const (
    _stack__skip_one = 216
)

const (
    _size__skip_one = 13520
)

var (
    _pcsp__skip_one = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x14, 48},
        {0x32e3, 216},
        {0x32e4, 48},
        {0x32e6, 40},
        {0x32e8, 32},
        {0x32ea, 24},
        {0x32ec, 16},
        {0x32ed, 8},
        {0x32ee, 0},
        {0x34d0, 216},
    }
)

var _cfunc_skip_one = []loader.CFunc{
    {"_skip_one_entry", 0,  _entry__skip_one, 0, nil},
    {"_skip_one", _entry__skip_one, _size__skip_one, _stack__skip_one, _pcsp__skip_one},
}
