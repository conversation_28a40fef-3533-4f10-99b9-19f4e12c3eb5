// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_number = 112
)

const (
    _stack__skip_number = 72
)

const (
    _size__skip_number = 1060
)

var (
    _pcsp__skip_number = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0x373, 72},
        {0x374, 48},
        {0x376, 40},
        {0x378, 32},
        {0x37a, 24},
        {0x37c, 16},
        {0x37d, 8},
        {0x37e, 0},
        {0x424, 72},
    }
)

var _cfunc_skip_number = []loader.CFunc{
    {"_skip_number_entry", 0,  _entry__skip_number, 0, nil},
    {"_skip_number", _entry__skip_number, _size__skip_number, _stack__skip_number, _pcsp__skip_number},
}
