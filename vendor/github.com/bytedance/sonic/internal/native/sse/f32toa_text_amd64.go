// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_f32toa = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, // QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	//0x00000010 .p2align 4, 0x90
	//0x00000010 _f32toa
	0x55, //0x00000010 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000011 movq         %rsp, %rbp
	0x41, 0x57, //0x00000014 pushq        %r15
	0x41, 0x56, //0x00000016 pushq        %r14
	0x41, 0x55, //0x00000018 pushq        %r13
	0x41, 0x54, //0x0000001a pushq        %r12
	0x53, //0x0000001c pushq        %rbx
	0x50, //0x0000001d pushq        %rax
	0x66, 0x0f, 0x7e, 0xc0, //0x0000001e movd         %xmm0, %eax
	0x89, 0xc1, //0x00000022 movl         %eax, %ecx
	0xc1, 0xe9, 0x17, //0x00000024 shrl         $23, %ecx
	0x0f, 0xb6, 0xd1, //0x00000027 movzbl       %cl, %edx
	0x81, 0xfa, 0xff, 0x00, 0x00, 0x00, //0x0000002a cmpl         $255, %edx
	0x0f, 0x84, 0x1f, 0x0e, 0x00, 0x00, //0x00000030 je           LBB0_148
	0xc6, 0x07, 0x2d, //0x00000036 movb         $45, (%rdi)
	0x41, 0x89, 0xc1, //0x00000039 movl         %eax, %r9d
	0x41, 0xc1, 0xe9, 0x1f, //0x0000003c shrl         $31, %r9d
	0x4e, 0x8d, 0x2c, 0x0f, //0x00000040 leaq         (%rdi,%r9), %r13
	0xa9, 0xff, 0xff, 0xff, 0x7f, //0x00000044 testl        $2147483647, %eax
	0x0f, 0x84, 0xc9, 0x01, 0x00, 0x00, //0x00000049 je           LBB0_6
	0x25, 0xff, 0xff, 0x7f, 0x00, //0x0000004f andl         $8388607, %eax
	0x85, 0xd2, //0x00000054 testl        %edx, %edx
	0x0f, 0x84, 0x00, 0x0e, 0x00, 0x00, //0x00000056 je           LBB0_149
	0x44, 0x8d, 0xb8, 0x00, 0x00, 0x80, 0x00, //0x0000005c leal         $8388608(%rax), %r15d
	0x44, 0x8d, 0x82, 0x6a, 0xff, 0xff, 0xff, //0x00000063 leal         $-150(%rdx), %r8d
	0x8d, 0x4a, 0x81, //0x0000006a leal         $-127(%rdx), %ecx
	0x83, 0xf9, 0x17, //0x0000006d cmpl         $23, %ecx
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x00000070 ja           LBB0_7
	0xb9, 0x96, 0x00, 0x00, 0x00, //0x00000076 movl         $150, %ecx
	0x29, 0xd1, //0x0000007b subl         %edx, %ecx
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000007d movq         $-1, %rbx
	0x48, 0xd3, 0xe3, //0x00000084 shlq         %cl, %rbx
	0xf7, 0xd3, //0x00000087 notl         %ebx
	0x44, 0x85, 0xfb, //0x00000089 testl        %r15d, %ebx
	0x0f, 0x84, 0x3f, 0x04, 0x00, 0x00, //0x0000008c je           LBB0_34
	//0x00000092 LBB0_7
	0x48, 0x89, 0x7d, 0xd0, //0x00000092 movq         %rdi, $-48(%rbp)
	//0x00000096 LBB0_8
	0x45, 0x89, 0xfe, //0x00000096 movl         %r15d, %r14d
	0x41, 0x83, 0xe6, 0x01, //0x00000099 andl         $1, %r14d
	0x85, 0xc0, //0x0000009d testl        %eax, %eax
	0x0f, 0x94, 0xc0, //0x0000009f sete         %al
	0x83, 0xfa, 0x02, //0x000000a2 cmpl         $2, %edx
	0x0f, 0x93, 0xc1, //0x000000a5 setae        %cl
	0x20, 0xc1, //0x000000a8 andb         %al, %cl
	0x0f, 0xb6, 0xc9, //0x000000aa movzbl       %cl, %ecx
	0x45, 0x89, 0xfa, //0x000000ad movl         %r15d, %r10d
	0x41, 0xc1, 0xe2, 0x02, //0x000000b0 shll         $2, %r10d
	0x42, 0x8d, 0x04, 0xb9, //0x000000b4 leal         (%rcx,%r15,4), %eax
	0x83, 0xc0, 0xfe, //0x000000b8 addl         $-2, %eax
	0x41, 0x69, 0xd0, 0x13, 0x44, 0x13, 0x00, //0x000000bb imull        $1262611, %r8d, %edx
	0x44, 0x8d, 0x9a, 0x01, 0x01, 0xf8, 0xff, //0x000000c2 leal         $-524031(%rdx), %r11d
	0x84, 0xc9, //0x000000c9 testb        %cl, %cl
	0x44, 0x0f, 0x44, 0xda, //0x000000cb cmovel       %edx, %r11d
	0x41, 0xc1, 0xfb, 0x16, //0x000000cf sarl         $22, %r11d
	0x41, 0x69, 0xcb, 0xb1, 0x6c, 0xe5, 0xff, //0x000000d3 imull        $-1741647, %r11d, %ecx
	0xc1, 0xe9, 0x13, //0x000000da shrl         $19, %ecx
	0x44, 0x01, 0xc1, //0x000000dd addl         %r8d, %ecx
	0xba, 0x1f, 0x00, 0x00, 0x00, //0x000000e0 movl         $31, %edx
	0x44, 0x29, 0xda, //0x000000e5 subl         %r11d, %edx
	0x80, 0xc1, 0x01, //0x000000e8 addb         $1, %cl
	0xd3, 0xe0, //0x000000eb shll         %cl, %eax
	0x48, 0x8d, 0x3d, 0x4c, 0x0e, 0x00, 0x00, //0x000000ed leaq         $3660(%rip), %rdi  /* _pow10_ceil_sig_f32.g+0(%rip) */
	0x4c, 0x8b, 0x24, 0xd7, //0x000000f4 movq         (%rdi,%rdx,8), %r12
	0x49, 0xf7, 0xe4, //0x000000f8 mulq         %r12
	0x46, 0x8d, 0x04, 0xbd, 0x02, 0x00, 0x00, 0x00, //0x000000fb leal         $2(,%r15,4), %r8d
	0x31, 0xf6, //0x00000103 xorl         %esi, %esi
	0x48, 0xc1, 0xe8, 0x21, //0x00000105 shrq         $33, %rax
	0x40, 0x0f, 0x95, 0xc6, //0x00000109 setne        %sil
	0x09, 0xd6, //0x0000010d orl          %edx, %esi
	0x41, 0xd3, 0xe2, //0x0000010f shll         %cl, %r10d
	0x4c, 0x89, 0xd0, //0x00000112 movq         %r10, %rax
	0x49, 0xf7, 0xe4, //0x00000115 mulq         %r12
	0x49, 0x89, 0xd2, //0x00000118 movq         %rdx, %r10
	0x45, 0x31, 0xff, //0x0000011b xorl         %r15d, %r15d
	0x48, 0xc1, 0xe8, 0x21, //0x0000011e shrq         $33, %rax
	0x41, 0x0f, 0x95, 0xc7, //0x00000122 setne        %r15b
	0x41, 0xd3, 0xe0, //0x00000126 shll         %cl, %r8d
	0x4c, 0x89, 0xc0, //0x00000129 movq         %r8, %rax
	0x49, 0xf7, 0xe4, //0x0000012c mulq         %r12
	0x45, 0x09, 0xd7, //0x0000012f orl          %r10d, %r15d
	0x31, 0xc9, //0x00000132 xorl         %ecx, %ecx
	0x48, 0xc1, 0xe8, 0x21, //0x00000134 shrq         $33, %rax
	0x0f, 0x95, 0xc1, //0x00000138 setne        %cl
	0x09, 0xd1, //0x0000013b orl          %edx, %ecx
	0x44, 0x01, 0xf6, //0x0000013d addl         %r14d, %esi
	0x44, 0x29, 0xf1, //0x00000140 subl         %r14d, %ecx
	0x41, 0x83, 0xff, 0x28, //0x00000143 cmpl         $40, %r15d
	0x0f, 0x82, 0x41, 0x00, 0x00, 0x00, //0x00000147 jb           LBB0_10
	0x44, 0x89, 0xd0, //0x0000014d movl         %r10d, %eax
	0xba, 0xcd, 0xcc, 0xcc, 0xcc, //0x00000150 movl         $3435973837, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x00000155 imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x25, //0x00000159 shrq         $37, %rdx
	0x89, 0xf0, //0x0000015d movl         %esi, %eax
	0x48, 0x8d, 0x3c, 0xd5, 0x00, 0x00, 0x00, 0x00, //0x0000015f leaq         (,%rdx,8), %rdi
	0x48, 0x8d, 0x1c, 0xbf, //0x00000167 leaq         (%rdi,%rdi,4), %rbx
	0x48, 0x39, 0xc3, //0x0000016b cmpq         %rax, %rbx
	0x41, 0x0f, 0x92, 0xc6, //0x0000016e setb         %r14b
	0x48, 0x8d, 0x3c, 0xbf, //0x00000172 leaq         (%rdi,%rdi,4), %rdi
	0x48, 0x83, 0xc7, 0x28, //0x00000176 addq         $40, %rdi
	0x89, 0xcb, //0x0000017a movl         %ecx, %ebx
	0x31, 0xc0, //0x0000017c xorl         %eax, %eax
	0x48, 0x39, 0xdf, //0x0000017e cmpq         %rbx, %rdi
	0x41, 0x0f, 0x96, 0xc0, //0x00000181 setbe        %r8b
	0x45, 0x38, 0xc6, //0x00000185 cmpb         %r8b, %r14b
	0x0f, 0x84, 0xf2, 0x01, 0x00, 0x00, //0x00000188 je           LBB0_18
	//0x0000018e LBB0_10
	0x45, 0x89, 0xd0, //0x0000018e movl         %r10d, %r8d
	0x41, 0xc1, 0xe8, 0x02, //0x00000191 shrl         $2, %r8d
	0x44, 0x89, 0xd2, //0x00000195 movl         %r10d, %edx
	0x83, 0xe2, 0xfc, //0x00000198 andl         $-4, %edx
	0x39, 0xd6, //0x0000019b cmpl         %edx, %esi
	0x40, 0x0f, 0x97, 0xc6, //0x0000019d seta         %sil
	0x8d, 0x42, 0x04, //0x000001a1 leal         $4(%rdx), %eax
	0x39, 0xc8, //0x000001a4 cmpl         %ecx, %eax
	0x0f, 0x96, 0xc3, //0x000001a6 setbe        %bl
	0x40, 0x30, 0xf3, //0x000001a9 xorb         %sil, %bl
	0x0f, 0x84, 0x7a, 0x00, 0x00, 0x00, //0x000001ac je           LBB0_14
	0x83, 0xca, 0x02, //0x000001b2 orl          $2, %edx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000001b5 movl         $1, %eax
	0x41, 0x39, 0xd7, //0x000001ba cmpl         %edx, %r15d
	0x0f, 0x87, 0x0e, 0x00, 0x00, 0x00, //0x000001bd ja           LBB0_13
	0x0f, 0x94, 0xc0, //0x000001c3 sete         %al
	0x41, 0xc0, 0xea, 0x02, //0x000001c6 shrb         $2, %r10b
	0x41, 0x20, 0xc2, //0x000001ca andb         %al, %r10b
	0x41, 0x0f, 0xb6, 0xc2, //0x000001cd movzbl       %r10b, %eax
	//0x000001d1 LBB0_13
	0x44, 0x01, 0xc0, //0x000001d1 addl         %r8d, %eax
	0x4d, 0x89, 0xef, //0x000001d4 movq         %r13, %r15
	0x3d, 0xa0, 0x86, 0x01, 0x00, //0x000001d7 cmpl         $100000, %eax
	0x4c, 0x8b, 0x6d, 0xd0, //0x000001dc movq         $-48(%rbp), %r13
	0x0f, 0x82, 0x61, 0x00, 0x00, 0x00, //0x000001e0 jb           LBB0_19
	//0x000001e6 LBB0_15
	0x41, 0xbc, 0x06, 0x00, 0x00, 0x00, //0x000001e6 movl         $6, %r12d
	0x3d, 0x40, 0x42, 0x0f, 0x00, //0x000001ec cmpl         $1000000, %eax
	0x0f, 0x82, 0x8e, 0x00, 0x00, 0x00, //0x000001f1 jb           LBB0_24
	0x41, 0xbc, 0x07, 0x00, 0x00, 0x00, //0x000001f7 movl         $7, %r12d
	0x3d, 0x80, 0x96, 0x98, 0x00, //0x000001fd cmpl         $10000000, %eax
	0x0f, 0x82, 0x7d, 0x00, 0x00, 0x00, //0x00000202 jb           LBB0_24
	0x3d, 0x00, 0xe1, 0xf5, 0x05, //0x00000208 cmpl         $100000000, %eax
	0x41, 0xbc, 0x09, 0x00, 0x00, 0x00, //0x0000020d movl         $9, %r12d
	0xe9, 0x69, 0x00, 0x00, 0x00, //0x00000213 jmp          LBB0_23
	//0x00000218 LBB0_6
	0x41, 0xc6, 0x45, 0x00, 0x30, //0x00000218 movb         $48, (%r13)
	0x41, 0x29, 0xfd, //0x0000021d subl         %edi, %r13d
	0x41, 0x83, 0xc5, 0x01, //0x00000220 addl         $1, %r13d
	0x44, 0x89, 0xe8, //0x00000224 movl         %r13d, %eax
	0xe9, 0x1a, 0x0c, 0x00, 0x00, //0x00000227 jmp          LBB0_147
	//0x0000022c LBB0_14
	0x39, 0xc1, //0x0000022c cmpl         %eax, %ecx
	0x41, 0x83, 0xd8, 0xff, //0x0000022e sbbl         $-1, %r8d
	0x44, 0x89, 0xc0, //0x00000232 movl         %r8d, %eax
	0x4d, 0x89, 0xef, //0x00000235 movq         %r13, %r15
	0x3d, 0xa0, 0x86, 0x01, 0x00, //0x00000238 cmpl         $100000, %eax
	0x4c, 0x8b, 0x6d, 0xd0, //0x0000023d movq         $-48(%rbp), %r13
	0x0f, 0x83, 0x9f, 0xff, 0xff, 0xff, //0x00000241 jae          LBB0_15
	//0x00000247 LBB0_19
	0x41, 0xbc, 0x01, 0x00, 0x00, 0x00, //0x00000247 movl         $1, %r12d
	0x83, 0xf8, 0x0a, //0x0000024d cmpl         $10, %eax
	0x0f, 0x82, 0x2f, 0x00, 0x00, 0x00, //0x00000250 jb           LBB0_24
	0x41, 0xbc, 0x02, 0x00, 0x00, 0x00, //0x00000256 movl         $2, %r12d
	0x83, 0xf8, 0x64, //0x0000025c cmpl         $100, %eax
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x0000025f jb           LBB0_24
	0x41, 0xbc, 0x03, 0x00, 0x00, 0x00, //0x00000265 movl         $3, %r12d
	0x3d, 0xe8, 0x03, 0x00, 0x00, //0x0000026b cmpl         $1000, %eax
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x00000270 jb           LBB0_24
	0x3d, 0x10, 0x27, 0x00, 0x00, //0x00000276 cmpl         $10000, %eax
	0x41, 0xbc, 0x05, 0x00, 0x00, 0x00, //0x0000027b movl         $5, %r12d
	//0x00000281 LBB0_23
	0x41, 0x83, 0xdc, 0x00, //0x00000281 sbbl         $0, %r12d
	//0x00000285 LBB0_24
	0x47, 0x8d, 0x14, 0x1c, //0x00000285 leal         (%r12,%r11), %r10d
	0x43, 0x8d, 0x0c, 0x1c, //0x00000289 leal         (%r12,%r11), %ecx
	0x83, 0xc1, 0xea, //0x0000028d addl         $-22, %ecx
	0x83, 0xf9, 0xe4, //0x00000290 cmpl         $-28, %ecx
	0x0f, 0x87, 0x78, 0x00, 0x00, 0x00, //0x00000293 ja           LBB0_28
	0x44, 0x89, 0xe1, //0x00000299 movl         %r12d, %ecx
	0x49, 0x8d, 0x14, 0x0f, //0x0000029c leaq         (%r15,%rcx), %rdx
	0x48, 0x83, 0xc2, 0x01, //0x000002a0 addq         $1, %rdx
	0x3d, 0x10, 0x27, 0x00, 0x00, //0x000002a4 cmpl         $10000, %eax
	0x0f, 0x82, 0xf1, 0x00, 0x00, 0x00, //0x000002a9 jb           LBB0_32
	0x89, 0xc7, //0x000002af movl         %eax, %edi
	0xbe, 0x59, 0x17, 0xb7, 0xd1, //0x000002b1 movl         $3518437209, %esi
	0x48, 0x0f, 0xaf, 0xf7, //0x000002b6 imulq        %rdi, %rsi
	0x48, 0xc1, 0xee, 0x2d, //0x000002ba shrq         $45, %rsi
	0x44, 0x69, 0xc6, 0xf0, 0xd8, 0xff, 0xff, //0x000002be imull        $-10000, %esi, %r8d
	0x41, 0x01, 0xc0, //0x000002c5 addl         %eax, %r8d
	0x0f, 0x84, 0xa7, 0x04, 0x00, 0x00, //0x000002c8 je           LBB0_64
	0x44, 0x89, 0xc0, //0x000002ce movl         %r8d, %eax
	0x48, 0x69, 0xc0, 0x1f, 0x85, 0xeb, 0x51, //0x000002d1 imulq        $1374389535, %rax, %rax
	0x48, 0xc1, 0xe8, 0x25, //0x000002d8 shrq         $37, %rax
	0x6b, 0xf8, 0x64, //0x000002dc imull        $100, %eax, %edi
	0x41, 0x29, 0xf8, //0x000002df subl         %edi, %r8d
	0x48, 0x8d, 0x3d, 0x87, 0x0b, 0x00, 0x00, //0x000002e2 leaq         $2951(%rip), %rdi  /* _Digits+0(%rip) */
	0x42, 0x0f, 0xb7, 0x1c, 0x47, //0x000002e9 movzwl       (%rdi,%r8,2), %ebx
	0x66, 0x89, 0x5a, 0xfe, //0x000002ee movw         %bx, $-2(%rdx)
	0x0f, 0xb7, 0x04, 0x47, //0x000002f2 movzwl       (%rdi,%rax,2), %eax
	0x66, 0x89, 0x42, 0xfc, //0x000002f6 movw         %ax, $-4(%rdx)
	0x45, 0x31, 0xc0, //0x000002fa xorl         %r8d, %r8d
	0x48, 0x83, 0xc2, 0xfc, //0x000002fd addq         $-4, %rdx
	0x83, 0xfe, 0x64, //0x00000301 cmpl         $100, %esi
	0x0f, 0x83, 0xa4, 0x00, 0x00, 0x00, //0x00000304 jae          LBB0_66
	//0x0000030a LBB0_33
	0x89, 0xf0, //0x0000030a movl         %esi, %eax
	0xe9, 0xde, 0x00, 0x00, 0x00, //0x0000030c jmp          LBB0_68
	//0x00000311 LBB0_28
	0x45, 0x89, 0xe0, //0x00000311 movl         %r12d, %r8d
	0x45, 0x85, 0xdb, //0x00000314 testl        %r11d, %r11d
	0x0f, 0x88, 0x54, 0x02, 0x00, 0x00, //0x00000317 js           LBB0_40
	0x4b, 0x8d, 0x14, 0x07, //0x0000031d leaq         (%r15,%r8), %rdx
	0x3d, 0x10, 0x27, 0x00, 0x00, //0x00000321 cmpl         $10000, %eax
	0x0f, 0x82, 0xb1, 0x02, 0x00, 0x00, //0x00000326 jb           LBB0_45
	0x89, 0xc1, //0x0000032c movl         %eax, %ecx
	0xbe, 0x59, 0x17, 0xb7, 0xd1, //0x0000032e movl         $3518437209, %esi
	0x48, 0x0f, 0xaf, 0xf1, //0x00000333 imulq        %rcx, %rsi
	0x48, 0xc1, 0xee, 0x2d, //0x00000337 shrq         $45, %rsi
	0x69, 0xce, 0xf0, 0xd8, 0xff, 0xff, //0x0000033b imull        $-10000, %esi, %ecx
	0x01, 0xc1, //0x00000341 addl         %eax, %ecx
	0x48, 0x69, 0xc1, 0x1f, 0x85, 0xeb, 0x51, //0x00000343 imulq        $1374389535, %rcx, %rax
	0x48, 0xc1, 0xe8, 0x25, //0x0000034a shrq         $37, %rax
	0x6b, 0xf8, 0x64, //0x0000034e imull        $100, %eax, %edi
	0x29, 0xf9, //0x00000351 subl         %edi, %ecx
	0x48, 0x8d, 0x3d, 0x16, 0x0b, 0x00, 0x00, //0x00000353 leaq         $2838(%rip), %rdi  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x4f, //0x0000035a movzwl       (%rdi,%rcx,2), %ecx
	0x66, 0x89, 0x4a, 0xfe, //0x0000035e movw         %cx, $-2(%rdx)
	0x48, 0x8d, 0x4a, 0xfc, //0x00000362 leaq         $-4(%rdx), %rcx
	0x0f, 0xb7, 0x04, 0x47, //0x00000366 movzwl       (%rdi,%rax,2), %eax
	0x66, 0x89, 0x42, 0xfc, //0x0000036a movw         %ax, $-4(%rdx)
	0x89, 0xf0, //0x0000036e movl         %esi, %eax
	0x83, 0xf8, 0x64, //0x00000370 cmpl         $100, %eax
	0x0f, 0x83, 0x70, 0x02, 0x00, 0x00, //0x00000373 jae          LBB0_46
	//0x00000379 LBB0_31
	0x89, 0xc6, //0x00000379 movl         %eax, %esi
	0xe9, 0xae, 0x02, 0x00, 0x00, //0x0000037b jmp          LBB0_48
	//0x00000380 LBB0_18
	0x44, 0x88, 0xc0, //0x00000380 movb         %r8b, %al
	0x01, 0xd0, //0x00000383 addl         %edx, %eax
	0x41, 0x83, 0xc3, 0x01, //0x00000385 addl         $1, %r11d
	0x4d, 0x89, 0xef, //0x00000389 movq         %r13, %r15
	0x3d, 0xa0, 0x86, 0x01, 0x00, //0x0000038c cmpl         $100000, %eax
	0x4c, 0x8b, 0x6d, 0xd0, //0x00000391 movq         $-48(%rbp), %r13
	0x0f, 0x83, 0x4b, 0xfe, 0xff, 0xff, //0x00000395 jae          LBB0_15
	0xe9, 0xa7, 0xfe, 0xff, 0xff, //0x0000039b jmp          LBB0_19
	//0x000003a0 LBB0_32
	0x45, 0x31, 0xc0, //0x000003a0 xorl         %r8d, %r8d
	0x89, 0xc6, //0x000003a3 movl         %eax, %esi
	0x83, 0xfe, 0x64, //0x000003a5 cmpl         $100, %esi
	0x0f, 0x82, 0x5c, 0xff, 0xff, 0xff, //0x000003a8 jb           LBB0_33
	//0x000003ae LBB0_66
	0x48, 0x83, 0xc2, 0xff, //0x000003ae addq         $-1, %rdx
	0x4c, 0x8d, 0x1d, 0xb7, 0x0a, 0x00, 0x00, //0x000003b2 leaq         $2743(%rip), %r11  /* _Digits+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003b9 .p2align 4, 0x90
	//0x000003c0 LBB0_67
	0x89, 0xf0, //0x000003c0 movl         %esi, %eax
	0x48, 0x69, 0xc0, 0x1f, 0x85, 0xeb, 0x51, //0x000003c2 imulq        $1374389535, %rax, %rax
	0x48, 0xc1, 0xe8, 0x25, //0x000003c9 shrq         $37, %rax
	0x6b, 0xf8, 0x64, //0x000003cd imull        $100, %eax, %edi
	0x89, 0xf3, //0x000003d0 movl         %esi, %ebx
	0x29, 0xfb, //0x000003d2 subl         %edi, %ebx
	0x41, 0x0f, 0xb7, 0x3c, 0x5b, //0x000003d4 movzwl       (%r11,%rbx,2), %edi
	0x66, 0x89, 0x7a, 0xff, //0x000003d9 movw         %di, $-1(%rdx)
	0x48, 0x83, 0xc2, 0xfe, //0x000003dd addq         $-2, %rdx
	0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x000003e1 cmpl         $9999, %esi
	0x89, 0xc6, //0x000003e7 movl         %eax, %esi
	0x0f, 0x87, 0xd1, 0xff, 0xff, 0xff, //0x000003e9 ja           LBB0_67
	//0x000003ef LBB0_68
	0x49, 0x8d, 0x57, 0x01, //0x000003ef leaq         $1(%r15), %rdx
	0x83, 0xf8, 0x0a, //0x000003f3 cmpl         $10, %eax
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x000003f6 jb           LBB0_70
	0x89, 0xc6, //0x000003fc movl         %eax, %esi
	0x48, 0x8d, 0x3d, 0x6b, 0x0a, 0x00, 0x00, //0x000003fe leaq         $2667(%rip), %rdi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x77, //0x00000405 movb         (%rdi,%rsi,2), %al
	0x8a, 0x5c, 0x77, 0x01, //0x00000408 movb         $1(%rdi,%rsi,2), %bl
	0x41, 0x88, 0x47, 0x01, //0x0000040c movb         %al, $1(%r15)
	0x41, 0x88, 0x5f, 0x02, //0x00000410 movb         %bl, $2(%r15)
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00000414 jmp          LBB0_71
	//0x00000419 LBB0_70
	0x04, 0x30, //0x00000419 addb         $48, %al
	0x88, 0x02, //0x0000041b movb         %al, (%rdx)
	//0x0000041d LBB0_71
	0x4d, 0x29, 0xc1, //0x0000041d subq         %r8, %r9
	0x4d, 0x01, 0xe9, //0x00000420 addq         %r13, %r9
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00000423 movl         $1, %esi
	0x4c, 0x29, 0xc6, //0x00000428 subq         %r8, %rsi
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000042b .p2align 4, 0x90
	//0x00000430 LBB0_72
	0x48, 0x83, 0xc6, 0xff, //0x00000430 addq         $-1, %rsi
	0x41, 0x80, 0x3c, 0x09, 0x30, //0x00000434 cmpb         $48, (%r9,%rcx)
	0x4d, 0x8d, 0x49, 0xff, //0x00000439 leaq         $-1(%r9), %r9
	0x0f, 0x84, 0xed, 0xff, 0xff, 0xff, //0x0000043d je           LBB0_72
	0x41, 0x88, 0x07, //0x00000443 movb         %al, (%r15)
	0x48, 0x01, 0xce, //0x00000446 addq         %rcx, %rsi
	0x48, 0x83, 0xfe, 0x02, //0x00000449 cmpq         $2, %rsi
	0x0f, 0x8c, 0x46, 0x00, 0x00, 0x00, //0x0000044d jl           LBB0_77
	0x49, 0x8d, 0x04, 0x09, //0x00000453 leaq         (%r9,%rcx), %rax
	0x48, 0x83, 0xc0, 0x02, //0x00000457 addq         $2, %rax
	0xc6, 0x02, 0x2e, //0x0000045b movb         $46, (%rdx)
	0xc6, 0x00, 0x65, //0x0000045e movb         $101, (%rax)
	0x45, 0x85, 0xd2, //0x00000461 testl        %r10d, %r10d
	0x0f, 0x8e, 0x43, 0x00, 0x00, 0x00, //0x00000464 jle          LBB0_78
	//0x0000046a LBB0_75
	0x41, 0x83, 0xc2, 0xff, //0x0000046a addl         $-1, %r10d
	0xc6, 0x40, 0x01, 0x2b, //0x0000046e movb         $43, $1(%rax)
	0x44, 0x89, 0xd1, //0x00000472 movl         %r10d, %ecx
	0x83, 0xf9, 0x0a, //0x00000475 cmpl         $10, %ecx
	0x0f, 0x82, 0x44, 0x00, 0x00, 0x00, //0x00000478 jb           LBB0_79
	//0x0000047e LBB0_76
	0x48, 0x63, 0xc9, //0x0000047e movslq       %ecx, %rcx
	0x48, 0x8d, 0x15, 0xe8, 0x09, 0x00, 0x00, //0x00000481 leaq         $2536(%rip), %rdx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x4a, //0x00000488 movzwl       (%rdx,%rcx,2), %ecx
	0x66, 0x89, 0x48, 0x02, //0x0000048c movw         %cx, $2(%rax)
	0x48, 0x83, 0xc0, 0x04, //0x00000490 addq         $4, %rax
	0xe9, 0xaa, 0x09, 0x00, 0x00, //0x00000494 jmp          LBB0_146
	//0x00000499 LBB0_77
	0x49, 0x8d, 0x04, 0x09, //0x00000499 leaq         (%r9,%rcx), %rax
	0x48, 0x83, 0xc0, 0x01, //0x0000049d addq         $1, %rax
	0xc6, 0x00, 0x65, //0x000004a1 movb         $101, (%rax)
	0x45, 0x85, 0xd2, //0x000004a4 testl        %r10d, %r10d
	0x0f, 0x8f, 0xbd, 0xff, 0xff, 0xff, //0x000004a7 jg           LBB0_75
	//0x000004ad LBB0_78
	0xc6, 0x40, 0x01, 0x2d, //0x000004ad movb         $45, $1(%rax)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000004b1 movl         $1, %ecx
	0x44, 0x29, 0xd1, //0x000004b6 subl         %r10d, %ecx
	0x83, 0xf9, 0x0a, //0x000004b9 cmpl         $10, %ecx
	0x0f, 0x83, 0xbc, 0xff, 0xff, 0xff, //0x000004bc jae          LBB0_76
	//0x000004c2 LBB0_79
	0x80, 0xc1, 0x30, //0x000004c2 addb         $48, %cl
	0x88, 0x48, 0x02, //0x000004c5 movb         %cl, $2(%rax)
	0x48, 0x83, 0xc0, 0x03, //0x000004c8 addq         $3, %rax
	0xe9, 0x72, 0x09, 0x00, 0x00, //0x000004cc jmp          LBB0_146
	//0x000004d1 LBB0_34
	0x41, 0xd3, 0xef, //0x000004d1 shrl         %cl, %r15d
	0x41, 0x81, 0xff, 0xa0, 0x86, 0x01, 0x00, //0x000004d4 cmpl         $100000, %r15d
	0x0f, 0x82, 0xd7, 0x01, 0x00, 0x00, //0x000004db jb           LBB0_55
	0xb9, 0x06, 0x00, 0x00, 0x00, //0x000004e1 movl         $6, %ecx
	0x41, 0x81, 0xff, 0x40, 0x42, 0x0f, 0x00, //0x000004e6 cmpl         $1000000, %r15d
	0x0f, 0x82, 0x22, 0x00, 0x00, 0x00, //0x000004ed jb           LBB0_38
	0xb9, 0x07, 0x00, 0x00, 0x00, //0x000004f3 movl         $7, %ecx
	0x41, 0x81, 0xff, 0x80, 0x96, 0x98, 0x00, //0x000004f8 cmpl         $10000000, %r15d
	0x0f, 0x82, 0x10, 0x00, 0x00, 0x00, //0x000004ff jb           LBB0_38
	0x41, 0x81, 0xff, 0x00, 0xe1, 0xf5, 0x05, //0x00000505 cmpl         $100000000, %r15d
	0xb9, 0x09, 0x00, 0x00, 0x00, //0x0000050c movl         $9, %ecx
	0x48, 0x83, 0xd9, 0x00, //0x00000511 sbbq         $0, %rcx
	//0x00000515 LBB0_38
	0x4c, 0x01, 0xe9, //0x00000515 addq         %r13, %rcx
	//0x00000518 LBB0_39
	0x44, 0x89, 0xf8, //0x00000518 movl         %r15d, %eax
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x0000051b movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x00000520 imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x00000524 shrq         $45, %rdx
	0x69, 0xc2, 0xf0, 0xd8, 0xff, 0xff, //0x00000528 imull        $-10000, %edx, %eax
	0x44, 0x01, 0xf8, //0x0000052e addl         %r15d, %eax
	0x48, 0x69, 0xf0, 0x1f, 0x85, 0xeb, 0x51, //0x00000531 imulq        $1374389535, %rax, %rsi
	0x48, 0xc1, 0xee, 0x25, //0x00000538 shrq         $37, %rsi
	0x6b, 0xde, 0x64, //0x0000053c imull        $100, %esi, %ebx
	0x29, 0xd8, //0x0000053f subl         %ebx, %eax
	0x48, 0x8d, 0x1d, 0x28, 0x09, 0x00, 0x00, //0x00000541 leaq         $2344(%rip), %rbx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x43, //0x00000548 movzwl       (%rbx,%rax,2), %eax
	0x66, 0x89, 0x41, 0xfe, //0x0000054c movw         %ax, $-2(%rcx)
	0x0f, 0xb7, 0x04, 0x73, //0x00000550 movzwl       (%rbx,%rsi,2), %eax
	0x66, 0x89, 0x41, 0xfc, //0x00000554 movw         %ax, $-4(%rcx)
	0x48, 0x89, 0xc8, //0x00000558 movq         %rcx, %rax
	0x48, 0x83, 0xc1, 0xfc, //0x0000055b addq         $-4, %rcx
	0x41, 0x89, 0xd7, //0x0000055f movl         %edx, %r15d
	0x41, 0x83, 0xff, 0x64, //0x00000562 cmpl         $100, %r15d
	0x0f, 0x83, 0x8c, 0x01, 0x00, 0x00, //0x00000566 jae          LBB0_59
	0xe9, 0xd1, 0x01, 0x00, 0x00, //0x0000056c jmp          LBB0_61
	//0x00000571 LBB0_40
	0x45, 0x85, 0xd2, //0x00000571 testl        %r10d, %r10d
	0x0f, 0x8f, 0xf7, 0x04, 0x00, 0x00, //0x00000574 jg           LBB0_107
	0x66, 0x41, 0xc7, 0x07, 0x30, 0x2e, //0x0000057a movw         $11824, (%r15)
	0x49, 0x83, 0xc7, 0x02, //0x00000580 addq         $2, %r15
	0x45, 0x85, 0xd2, //0x00000584 testl        %r10d, %r10d
	0x0f, 0x89, 0xe4, 0x04, 0x00, 0x00, //0x00000587 jns          LBB0_107
	0x45, 0x89, 0xe6, //0x0000058d movl         %r12d, %r14d
	0x41, 0xf7, 0xd6, //0x00000590 notl         %r14d
	0x45, 0x29, 0xde, //0x00000593 subl         %r11d, %r14d
	0x31, 0xc9, //0x00000596 xorl         %ecx, %ecx
	0x41, 0x83, 0xfe, 0x1f, //0x00000598 cmpl         $31, %r14d
	0x0f, 0x82, 0xaf, 0x04, 0x00, 0x00, //0x0000059c jb           LBB0_105
	0x4c, 0x89, 0xfb, //0x000005a2 movq         %r15, %rbx
	0x4c, 0x89, 0xef, //0x000005a5 movq         %r13, %rdi
	0x49, 0x83, 0xc6, 0x01, //0x000005a8 addq         $1, %r14
	0x4c, 0x89, 0xf1, //0x000005ac movq         %r14, %rcx
	0x48, 0x83, 0xe1, 0xe0, //0x000005af andq         $-32, %rcx
	0x48, 0x8d, 0x51, 0xe0, //0x000005b3 leaq         $-32(%rcx), %rdx
	0x49, 0x89, 0xd5, //0x000005b7 movq         %rdx, %r13
	0x49, 0xc1, 0xed, 0x05, //0x000005ba shrq         $5, %r13
	0x49, 0x83, 0xc5, 0x01, //0x000005be addq         $1, %r13
	0x45, 0x89, 0xef, //0x000005c2 movl         %r13d, %r15d
	0x41, 0x83, 0xe7, 0x07, //0x000005c5 andl         $7, %r15d
	0x48, 0x81, 0xfa, 0xe0, 0x00, 0x00, 0x00, //0x000005c9 cmpq         $224, %rdx
	0x0f, 0x83, 0x92, 0x03, 0x00, 0x00, //0x000005d0 jae          LBB0_99
	0x31, 0xd2, //0x000005d6 xorl         %edx, %edx
	0xe9, 0x29, 0x04, 0x00, 0x00, //0x000005d8 jmp          LBB0_101
	//0x000005dd LBB0_45
	0x48, 0x89, 0xd1, //0x000005dd movq         %rdx, %rcx
	0x83, 0xf8, 0x64, //0x000005e0 cmpl         $100, %eax
	0x0f, 0x82, 0x90, 0xfd, 0xff, 0xff, //0x000005e3 jb           LBB0_31
	//0x000005e9 LBB0_46
	0x48, 0x83, 0xc1, 0xff, //0x000005e9 addq         $-1, %rcx
	0x4c, 0x8d, 0x1d, 0x7c, 0x08, 0x00, 0x00, //0x000005ed leaq         $2172(%rip), %r11  /* _Digits+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000005f4 .p2align 4, 0x90
	//0x00000600 LBB0_47
	0x89, 0xc6, //0x00000600 movl         %eax, %esi
	0x48, 0x69, 0xf6, 0x1f, 0x85, 0xeb, 0x51, //0x00000602 imulq        $1374389535, %rsi, %rsi
	0x48, 0xc1, 0xee, 0x25, //0x00000609 shrq         $37, %rsi
	0x6b, 0xfe, 0x64, //0x0000060d imull        $100, %esi, %edi
	0x89, 0xc3, //0x00000610 movl         %eax, %ebx
	0x29, 0xfb, //0x00000612 subl         %edi, %ebx
	0x41, 0x0f, 0xb7, 0x3c, 0x5b, //0x00000614 movzwl       (%r11,%rbx,2), %edi
	0x66, 0x89, 0x79, 0xff, //0x00000619 movw         %di, $-1(%rcx)
	0x48, 0x83, 0xc1, 0xfe, //0x0000061d addq         $-2, %rcx
	0x3d, 0x0f, 0x27, 0x00, 0x00, //0x00000621 cmpl         $9999, %eax
	0x89, 0xf0, //0x00000626 movl         %esi, %eax
	0x0f, 0x87, 0xd2, 0xff, 0xff, 0xff, //0x00000628 ja           LBB0_47
	//0x0000062e LBB0_48
	0x4d, 0x63, 0xea, //0x0000062e movslq       %r10d, %r13
	0x83, 0xfe, 0x0a, //0x00000631 cmpl         $10, %esi
	0x0f, 0x82, 0x29, 0x00, 0x00, 0x00, //0x00000634 jb           LBB0_51
	0x89, 0xf0, //0x0000063a movl         %esi, %eax
	0x48, 0x8d, 0x0d, 0x2d, 0x08, 0x00, 0x00, //0x0000063c leaq         $2093(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x00000643 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x41, 0x89, 0x07, //0x00000647 movw         %ax, (%r15)
	0x4d, 0x01, 0xef, //0x0000064b addq         %r13, %r15
	0x4d, 0x39, 0xe8, //0x0000064e cmpq         %r13, %r8
	0x0f, 0x8c, 0x1f, 0x00, 0x00, 0x00, //0x00000651 jl           LBB0_52
	//0x00000657 LBB0_50
	0x4c, 0x89, 0xf8, //0x00000657 movq         %r15, %rax
	0x4c, 0x8b, 0x6d, 0xd0, //0x0000065a movq         $-48(%rbp), %r13
	0xe9, 0xe0, 0x07, 0x00, 0x00, //0x0000065e jmp          LBB0_146
	//0x00000663 LBB0_51
	0x40, 0x80, 0xc6, 0x30, //0x00000663 addb         $48, %sil
	0x41, 0x88, 0x37, //0x00000667 movb         %sil, (%r15)
	0x4d, 0x01, 0xef, //0x0000066a addq         %r13, %r15
	0x4d, 0x39, 0xe8, //0x0000066d cmpq         %r13, %r8
	0x0f, 0x8d, 0xe1, 0xff, 0xff, 0xff, //0x00000670 jge          LBB0_50
	//0x00000676 LBB0_52
	0x4c, 0x89, 0xff, //0x00000676 movq         %r15, %rdi
	0x48, 0x8b, 0x45, 0xd0, //0x00000679 movq         $-48(%rbp), %rax
	0x4c, 0x01, 0xc8, //0x0000067d addq         %r9, %rax
	0x4d, 0x8d, 0x34, 0x00, //0x00000680 leaq         (%r8,%rax), %r14
	0x49, 0x83, 0xc6, 0x01, //0x00000684 addq         $1, %r14
	0x49, 0x01, 0xc5, //0x00000688 addq         %rax, %r13
	0x4d, 0x39, 0xee, //0x0000068b cmpq         %r13, %r14
	0x4d, 0x89, 0xef, //0x0000068e movq         %r13, %r15
	0x4d, 0x0f, 0x47, 0xfe, //0x00000691 cmovaq       %r14, %r15
	0x4e, 0x8d, 0x1c, 0x00, //0x00000695 leaq         (%rax,%r8), %r11
	0x4d, 0x29, 0xdf, //0x00000699 subq         %r11, %r15
	0x49, 0x83, 0xff, 0x08, //0x0000069c cmpq         $8, %r15
	0x0f, 0x82, 0x9a, 0x02, 0x00, 0x00, //0x000006a0 jb           LBB0_96
	0x49, 0x83, 0xff, 0x20, //0x000006a6 cmpq         $32, %r15
	0x0f, 0x83, 0xdd, 0x00, 0x00, 0x00, //0x000006aa jae          LBB0_80
	0x45, 0x31, 0xd2, //0x000006b0 xorl         %r10d, %r10d
	0xe9, 0x03, 0x02, 0x00, 0x00, //0x000006b3 jmp          LBB0_89
	//0x000006b8 LBB0_55
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000006b8 movl         $1, %eax
	0x41, 0x83, 0xff, 0x0a, //0x000006bd cmpl         $10, %r15d
	0x0f, 0x82, 0x21, 0x00, 0x00, 0x00, //0x000006c1 jb           LBB0_58
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x000006c7 movl         $2, %eax
	0x41, 0x83, 0xff, 0x64, //0x000006cc cmpl         $100, %r15d
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x000006d0 jb           LBB0_58
	0xb8, 0x03, 0x00, 0x00, 0x00, //0x000006d6 movl         $3, %eax
	0x41, 0x81, 0xff, 0xe8, 0x03, 0x00, 0x00, //0x000006db cmpl         $1000, %r15d
	0x0f, 0x83, 0x2e, 0x02, 0x00, 0x00, //0x000006e2 jae          LBB0_93
	//0x000006e8 LBB0_58
	0x4c, 0x01, 0xe8, //0x000006e8 addq         %r13, %rax
	0x48, 0x89, 0xc1, //0x000006eb movq         %rax, %rcx
	0x41, 0x83, 0xff, 0x64, //0x000006ee cmpl         $100, %r15d
	0x0f, 0x82, 0x4a, 0x00, 0x00, 0x00, //0x000006f2 jb           LBB0_61
	//0x000006f8 LBB0_59
	0x48, 0x83, 0xc1, 0xff, //0x000006f8 addq         $-1, %rcx
	0x4c, 0x8d, 0x05, 0x6d, 0x07, 0x00, 0x00, //0x000006fc leaq         $1901(%rip), %r8  /* _Digits+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000703 .p2align 4, 0x90
	//0x00000710 LBB0_60
	0x44, 0x89, 0xfb, //0x00000710 movl         %r15d, %ebx
	0x44, 0x89, 0xfe, //0x00000713 movl         %r15d, %esi
	0x4c, 0x69, 0xfe, 0x1f, 0x85, 0xeb, 0x51, //0x00000716 imulq        $1374389535, %rsi, %r15
	0x49, 0xc1, 0xef, 0x25, //0x0000071d shrq         $37, %r15
	0x41, 0x6b, 0xf7, 0x64, //0x00000721 imull        $100, %r15d, %esi
	0x89, 0xda, //0x00000725 movl         %ebx, %edx
	0x29, 0xf2, //0x00000727 subl         %esi, %edx
	0x41, 0x0f, 0xb7, 0x14, 0x50, //0x00000729 movzwl       (%r8,%rdx,2), %edx
	0x66, 0x89, 0x51, 0xff, //0x0000072e movw         %dx, $-1(%rcx)
	0x48, 0x83, 0xc1, 0xfe, //0x00000732 addq         $-2, %rcx
	0x81, 0xfb, 0x0f, 0x27, 0x00, 0x00, //0x00000736 cmpl         $9999, %ebx
	0x0f, 0x87, 0xce, 0xff, 0xff, 0xff, //0x0000073c ja           LBB0_60
	//0x00000742 LBB0_61
	0x41, 0x83, 0xff, 0x0a, //0x00000742 cmpl         $10, %r15d
	0x0f, 0x82, 0x1a, 0x00, 0x00, 0x00, //0x00000746 jb           LBB0_63
	0x44, 0x89, 0xf9, //0x0000074c movl         %r15d, %ecx
	0x48, 0x8d, 0x15, 0x1a, 0x07, 0x00, 0x00, //0x0000074f leaq         $1818(%rip), %rdx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x4a, //0x00000756 movzwl       (%rdx,%rcx,2), %ecx
	0x66, 0x41, 0x89, 0x4d, 0x00, //0x0000075a movw         %cx, (%r13)
	0x29, 0xf8, //0x0000075f subl         %edi, %eax
	0xe9, 0xe0, 0x06, 0x00, 0x00, //0x00000761 jmp          LBB0_147
	//0x00000766 LBB0_63
	0x41, 0x80, 0xc7, 0x30, //0x00000766 addb         $48, %r15b
	0x45, 0x88, 0x7d, 0x00, //0x0000076a movb         %r15b, (%r13)
	0x29, 0xf8, //0x0000076e subl         %edi, %eax
	0xe9, 0xd1, 0x06, 0x00, 0x00, //0x00000770 jmp          LBB0_147
	//0x00000775 LBB0_64
	0x41, 0xb8, 0x04, 0x00, 0x00, 0x00, //0x00000775 movl         $4, %r8d
	0x48, 0x83, 0xc2, 0xfc, //0x0000077b addq         $-4, %rdx
	0x83, 0xfe, 0x64, //0x0000077f cmpl         $100, %esi
	0x0f, 0x82, 0x82, 0xfb, 0xff, 0xff, //0x00000782 jb           LBB0_33
	0xe9, 0x21, 0xfc, 0xff, 0xff, //0x00000788 jmp          LBB0_66
	//0x0000078d LBB0_80
	0x4d, 0x89, 0xfa, //0x0000078d movq         %r15, %r10
	0x49, 0x83, 0xe2, 0xe0, //0x00000790 andq         $-32, %r10
	0x49, 0x8d, 0x42, 0xe0, //0x00000794 leaq         $-32(%r10), %rax
	0x48, 0x89, 0xc6, //0x00000798 movq         %rax, %rsi
	0x48, 0xc1, 0xee, 0x05, //0x0000079b shrq         $5, %rsi
	0x48, 0x83, 0xc6, 0x01, //0x0000079f addq         $1, %rsi
	0x41, 0x89, 0xf4, //0x000007a3 movl         %esi, %r12d
	0x41, 0x83, 0xe4, 0x07, //0x000007a6 andl         $7, %r12d
	0x48, 0x3d, 0xe0, 0x00, 0x00, 0x00, //0x000007aa cmpq         $224, %rax
	0x0f, 0x83, 0x07, 0x00, 0x00, 0x00, //0x000007b0 jae          LBB0_82
	0x31, 0xc9, //0x000007b6 xorl         %ecx, %ecx
	0xe9, 0xa8, 0x00, 0x00, 0x00, //0x000007b8 jmp          LBB0_84
	//0x000007bd LBB0_82
	0x48, 0x83, 0xe6, 0xf8, //0x000007bd andq         $-8, %rsi
	0x4b, 0x8d, 0x04, 0x01, //0x000007c1 leaq         (%r9,%r8), %rax
	0x48, 0x8b, 0x4d, 0xd0, //0x000007c5 movq         $-48(%rbp), %rcx
	0x48, 0x01, 0xc8, //0x000007c9 addq         %rcx, %rax
	0x48, 0x05, 0xf0, 0x00, 0x00, 0x00, //0x000007cc addq         $240, %rax
	0x31, 0xc9, //0x000007d2 xorl         %ecx, %ecx
	0xf3, 0x0f, 0x6f, 0x05, 0x24, 0xf8, 0xff, 0xff, //0x000007d4 movdqu       $-2012(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, 0x90, //0x000007dc .p2align 4, 0x90
	//0x000007e0 LBB0_83
	0xf3, 0x0f, 0x7f, 0x84, 0x08, 0x10, 0xff, 0xff, 0xff, //0x000007e0 movdqu       %xmm0, $-240(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x08, 0x20, 0xff, 0xff, 0xff, //0x000007e9 movdqu       %xmm0, $-224(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x08, 0x30, 0xff, 0xff, 0xff, //0x000007f2 movdqu       %xmm0, $-208(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x08, 0x40, 0xff, 0xff, 0xff, //0x000007fb movdqu       %xmm0, $-192(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x08, 0x50, 0xff, 0xff, 0xff, //0x00000804 movdqu       %xmm0, $-176(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x08, 0x60, 0xff, 0xff, 0xff, //0x0000080d movdqu       %xmm0, $-160(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x84, 0x08, 0x70, 0xff, 0xff, 0xff, //0x00000816 movdqu       %xmm0, $-144(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x08, 0x80, //0x0000081f movdqu       %xmm0, $-128(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x08, 0x90, //0x00000825 movdqu       %xmm0, $-112(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x08, 0xa0, //0x0000082b movdqu       %xmm0, $-96(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x08, 0xb0, //0x00000831 movdqu       %xmm0, $-80(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x08, 0xc0, //0x00000837 movdqu       %xmm0, $-64(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x08, 0xd0, //0x0000083d movdqu       %xmm0, $-48(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x08, 0xe0, //0x00000843 movdqu       %xmm0, $-32(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x44, 0x08, 0xf0, //0x00000849 movdqu       %xmm0, $-16(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x04, 0x08, //0x0000084f movdqu       %xmm0, (%rax,%rcx)
	0x48, 0x81, 0xc1, 0x00, 0x01, 0x00, 0x00, //0x00000854 addq         $256, %rcx
	0x48, 0x83, 0xc6, 0xf8, //0x0000085b addq         $-8, %rsi
	0x0f, 0x85, 0x7b, 0xff, 0xff, 0xff, //0x0000085f jne          LBB0_83
	//0x00000865 LBB0_84
	0x4d, 0x85, 0xe4, //0x00000865 testq        %r12, %r12
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x00000868 je           LBB0_87
	0x4c, 0x01, 0xc9, //0x0000086e addq         %r9, %rcx
	0x4c, 0x01, 0xc1, //0x00000871 addq         %r8, %rcx
	0x48, 0x8b, 0x45, 0xd0, //0x00000874 movq         $-48(%rbp), %rax
	0x48, 0x01, 0xc8, //0x00000878 addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0x10, //0x0000087b addq         $16, %rax
	0x49, 0xc1, 0xe4, 0x05, //0x0000087f shlq         $5, %r12
	0x31, 0xc9, //0x00000883 xorl         %ecx, %ecx
	0xf3, 0x0f, 0x6f, 0x05, 0x73, 0xf7, 0xff, 0xff, //0x00000885 movdqu       $-2189(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, //0x0000088d .p2align 4, 0x90
	//0x00000890 LBB0_86
	0xf3, 0x0f, 0x7f, 0x44, 0x08, 0xf0, //0x00000890 movdqu       %xmm0, $-16(%rax,%rcx)
	0xf3, 0x0f, 0x7f, 0x04, 0x08, //0x00000896 movdqu       %xmm0, (%rax,%rcx)
	0x48, 0x83, 0xc1, 0x20, //0x0000089b addq         $32, %rcx
	0x49, 0x39, 0xcc, //0x0000089f cmpq         %rcx, %r12
	0x0f, 0x85, 0xe8, 0xff, 0xff, 0xff, //0x000008a2 jne          LBB0_86
	//0x000008a8 LBB0_87
	0x4d, 0x39, 0xd7, //0x000008a8 cmpq         %r10, %r15
	0x0f, 0x84, 0x59, 0x00, 0x00, 0x00, //0x000008ab je           LBB0_92
	0x41, 0xf6, 0xc7, 0x18, //0x000008b1 testb        $24, %r15b
	0x0f, 0x84, 0x82, 0x00, 0x00, 0x00, //0x000008b5 je           LBB0_95
	//0x000008bb LBB0_89
	0x4d, 0x39, 0xee, //0x000008bb cmpq         %r13, %r14
	0x4d, 0x0f, 0x47, 0xee, //0x000008be cmovaq       %r14, %r13
	0x4d, 0x29, 0xdd, //0x000008c2 subq         %r11, %r13
	0x4d, 0x89, 0xeb, //0x000008c5 movq         %r13, %r11
	0x49, 0x83, 0xe3, 0xf8, //0x000008c8 andq         $-8, %r11
	0x4c, 0x01, 0xda, //0x000008cc addq         %r11, %rdx
	0x4d, 0x01, 0xd1, //0x000008cf addq         %r10, %r9
	0x4d, 0x01, 0xc1, //0x000008d2 addq         %r8, %r9
	0x4c, 0x03, 0x4d, 0xd0, //0x000008d5 addq         $-48(%rbp), %r9
	0x4c, 0x89, 0xd9, //0x000008d9 movq         %r11, %rcx
	0x4c, 0x29, 0xd1, //0x000008dc subq         %r10, %rcx
	0x31, 0xf6, //0x000008df xorl         %esi, %esi
	0x48, 0xb8, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, //0x000008e1 movabsq      $3472328296227680304, %rax
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000008eb .p2align 4, 0x90
	//0x000008f0 LBB0_90
	0x49, 0x89, 0x04, 0x31, //0x000008f0 movq         %rax, (%r9,%rsi)
	0x48, 0x83, 0xc6, 0x08, //0x000008f4 addq         $8, %rsi
	0x48, 0x39, 0xf1, //0x000008f8 cmpq         %rsi, %rcx
	0x0f, 0x85, 0xef, 0xff, 0xff, 0xff, //0x000008fb jne          LBB0_90
	0x4d, 0x39, 0xdd, //0x00000901 cmpq         %r11, %r13
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x00000904 jne          LBB0_96
	//0x0000090a LBB0_92
	0x48, 0x89, 0xf8, //0x0000090a movq         %rdi, %rax
	0x4c, 0x8b, 0x6d, 0xd0, //0x0000090d movq         $-48(%rbp), %r13
	0xe9, 0x2d, 0x05, 0x00, 0x00, //0x00000911 jmp          LBB0_146
	//0x00000916 LBB0_93
	0x41, 0x81, 0xff, 0x10, 0x27, 0x00, 0x00, //0x00000916 cmpl         $10000, %r15d
	0x4c, 0x89, 0xe9, //0x0000091d movq         %r13, %rcx
	0x48, 0x83, 0xd9, 0x00, //0x00000920 sbbq         $0, %rcx
	0x48, 0x83, 0xc1, 0x05, //0x00000924 addq         $5, %rcx
	0x41, 0x81, 0xff, 0x10, 0x27, 0x00, 0x00, //0x00000928 cmpl         $10000, %r15d
	0x0f, 0x83, 0xe3, 0xfb, 0xff, 0xff, //0x0000092f jae          LBB0_39
	0x48, 0x89, 0xc8, //0x00000935 movq         %rcx, %rax
	0xe9, 0xbb, 0xfd, 0xff, 0xff, //0x00000938 jmp          LBB0_59
	//0x0000093d LBB0_95
	0x4c, 0x01, 0xd2, //0x0000093d addq         %r10, %rdx
	//0x00000940 LBB0_96
	0x4c, 0x8b, 0x6d, 0xd0, //0x00000940 movq         $-48(%rbp), %r13
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000944 .p2align 4, 0x90
	//0x00000950 LBB0_97
	0xc6, 0x02, 0x30, //0x00000950 movb         $48, (%rdx)
	0x48, 0x83, 0xc2, 0x01, //0x00000953 addq         $1, %rdx
	0x48, 0x39, 0xfa, //0x00000957 cmpq         %rdi, %rdx
	0x0f, 0x82, 0xf0, 0xff, 0xff, 0xff, //0x0000095a jb           LBB0_97
	0x48, 0x89, 0xf8, //0x00000960 movq         %rdi, %rax
	0xe9, 0xdb, 0x04, 0x00, 0x00, //0x00000963 jmp          LBB0_146
	//0x00000968 LBB0_99
	0x49, 0x83, 0xe5, 0xf8, //0x00000968 andq         $-8, %r13
	0x49, 0x8d, 0x34, 0x39, //0x0000096c leaq         (%r9,%rdi), %rsi
	0x48, 0x81, 0xc6, 0xf2, 0x00, 0x00, 0x00, //0x00000970 addq         $242, %rsi
	0x31, 0xd2, //0x00000977 xorl         %edx, %edx
	0xf3, 0x0f, 0x6f, 0x05, 0x7f, 0xf6, 0xff, 0xff, //0x00000979 movdqu       $-2433(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	//0x00000981 LBB0_100
	0xf3, 0x0f, 0x7f, 0x84, 0x16, 0x10, 0xff, 0xff, 0xff, //0x00000981 movdqu       %xmm0, $-240(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x84, 0x16, 0x20, 0xff, 0xff, 0xff, //0x0000098a movdqu       %xmm0, $-224(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x84, 0x16, 0x30, 0xff, 0xff, 0xff, //0x00000993 movdqu       %xmm0, $-208(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x84, 0x16, 0x40, 0xff, 0xff, 0xff, //0x0000099c movdqu       %xmm0, $-192(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x84, 0x16, 0x50, 0xff, 0xff, 0xff, //0x000009a5 movdqu       %xmm0, $-176(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x84, 0x16, 0x60, 0xff, 0xff, 0xff, //0x000009ae movdqu       %xmm0, $-160(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x84, 0x16, 0x70, 0xff, 0xff, 0xff, //0x000009b7 movdqu       %xmm0, $-144(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x44, 0x16, 0x80, //0x000009c0 movdqu       %xmm0, $-128(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x44, 0x16, 0x90, //0x000009c6 movdqu       %xmm0, $-112(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x44, 0x16, 0xa0, //0x000009cc movdqu       %xmm0, $-96(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x44, 0x16, 0xb0, //0x000009d2 movdqu       %xmm0, $-80(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x44, 0x16, 0xc0, //0x000009d8 movdqu       %xmm0, $-64(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x44, 0x16, 0xd0, //0x000009de movdqu       %xmm0, $-48(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x44, 0x16, 0xe0, //0x000009e4 movdqu       %xmm0, $-32(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x44, 0x16, 0xf0, //0x000009ea movdqu       %xmm0, $-16(%rsi,%rdx)
	0xf3, 0x0f, 0x7f, 0x04, 0x16, //0x000009f0 movdqu       %xmm0, (%rsi,%rdx)
	0x48, 0x81, 0xc2, 0x00, 0x01, 0x00, 0x00, //0x000009f5 addq         $256, %rdx
	0x49, 0x83, 0xc5, 0xf8, //0x000009fc addq         $-8, %r13
	0x0f, 0x85, 0x7b, 0xff, 0xff, 0xff, //0x00000a00 jne          LBB0_100
	//0x00000a06 LBB0_101
	0x4d, 0x85, 0xff, //0x00000a06 testq        %r15, %r15
	0x49, 0x89, 0xfd, //0x00000a09 movq         %rdi, %r13
	0x0f, 0x84, 0x30, 0x00, 0x00, 0x00, //0x00000a0c je           LBB0_104
	0x4c, 0x01, 0xca, //0x00000a12 addq         %r9, %rdx
	0x4c, 0x01, 0xea, //0x00000a15 addq         %r13, %rdx
	0x48, 0x83, 0xc2, 0x12, //0x00000a18 addq         $18, %rdx
	0x49, 0xc1, 0xe7, 0x05, //0x00000a1c shlq         $5, %r15
	0x31, 0xf6, //0x00000a20 xorl         %esi, %esi
	0xf3, 0x0f, 0x6f, 0x05, 0xd6, 0xf5, 0xff, 0xff, //0x00000a22 movdqu       $-2602(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	//0x00000a2a LBB0_103
	0xf3, 0x0f, 0x7f, 0x44, 0x32, 0xf0, //0x00000a2a movdqu       %xmm0, $-16(%rdx,%rsi)
	0xf3, 0x0f, 0x7f, 0x04, 0x32, //0x00000a30 movdqu       %xmm0, (%rdx,%rsi)
	0x48, 0x83, 0xc6, 0x20, //0x00000a35 addq         $32, %rsi
	0x49, 0x39, 0xf7, //0x00000a39 cmpq         %rsi, %r15
	0x0f, 0x85, 0xe8, 0xff, 0xff, 0xff, //0x00000a3c jne          LBB0_103
	//0x00000a42 LBB0_104
	0x49, 0x89, 0xdf, //0x00000a42 movq         %rbx, %r15
	0x49, 0x01, 0xcf, //0x00000a45 addq         %rcx, %r15
	0x49, 0x39, 0xce, //0x00000a48 cmpq         %rcx, %r14
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x00000a4b je           LBB0_107
	//0x00000a51 LBB0_105
	0x44, 0x01, 0xd1, //0x00000a51 addl         %r10d, %ecx
	0xf7, 0xd9, //0x00000a54 negl         %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a56 .p2align 4, 0x90
	//0x00000a60 LBB0_106
	0x41, 0xc6, 0x07, 0x30, //0x00000a60 movb         $48, (%r15)
	0x49, 0x83, 0xc7, 0x01, //0x00000a64 addq         $1, %r15
	0x83, 0xc1, 0xff, //0x00000a68 addl         $-1, %ecx
	0x0f, 0x85, 0xef, 0xff, 0xff, 0xff, //0x00000a6b jne          LBB0_106
	//0x00000a71 LBB0_107
	0x4c, 0x89, 0x7d, 0xd0, //0x00000a71 movq         %r15, $-48(%rbp)
	0x4d, 0x01, 0xc7, //0x00000a75 addq         %r8, %r15
	0x3d, 0x10, 0x27, 0x00, 0x00, //0x00000a78 cmpl         $10000, %eax
	0x0f, 0x82, 0x5f, 0x00, 0x00, 0x00, //0x00000a7d jb           LBB0_110
	0x89, 0xc2, //0x00000a83 movl         %eax, %edx
	0xbb, 0x59, 0x17, 0xb7, 0xd1, //0x00000a85 movl         $3518437209, %ebx
	0x48, 0x0f, 0xaf, 0xda, //0x00000a8a imulq        %rdx, %rbx
	0x48, 0xc1, 0xeb, 0x2d, //0x00000a8e shrq         $45, %rbx
	0x69, 0xd3, 0xf0, 0xd8, 0xff, 0xff, //0x00000a92 imull        $-10000, %ebx, %edx
	0x01, 0xc2, //0x00000a98 addl         %eax, %edx
	0x0f, 0x84, 0xd8, 0x01, 0x00, 0x00, //0x00000a9a je           LBB0_112
	0x89, 0xd0, //0x00000aa0 movl         %edx, %eax
	0x48, 0x69, 0xc0, 0x1f, 0x85, 0xeb, 0x51, //0x00000aa2 imulq        $1374389535, %rax, %rax
	0x48, 0xc1, 0xe8, 0x25, //0x00000aa9 shrq         $37, %rax
	0x6b, 0xf0, 0x64, //0x00000aad imull        $100, %eax, %esi
	0x29, 0xf2, //0x00000ab0 subl         %esi, %edx
	0x48, 0x8d, 0x35, 0xb7, 0x03, 0x00, 0x00, //0x00000ab2 leaq         $951(%rip), %rsi  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x14, 0x56, //0x00000ab9 movzwl       (%rsi,%rdx,2), %edx
	0x66, 0x41, 0x89, 0x57, 0xfe, //0x00000abd movw         %dx, $-2(%r15)
	0x0f, 0xb7, 0x04, 0x46, //0x00000ac2 movzwl       (%rsi,%rax,2), %eax
	0x66, 0x41, 0x89, 0x47, 0xfc, //0x00000ac6 movw         %ax, $-4(%r15)
	0x45, 0x31, 0xf6, //0x00000acb xorl         %r14d, %r14d
	0x49, 0x8d, 0x57, 0xfc, //0x00000ace leaq         $-4(%r15), %rdx
	0x83, 0xfb, 0x64, //0x00000ad2 cmpl         $100, %ebx
	0x0f, 0x83, 0x18, 0x00, 0x00, 0x00, //0x00000ad5 jae          LBB0_114
	//0x00000adb LBB0_111
	0x89, 0xd8, //0x00000adb movl         %ebx, %eax
	0xe9, 0x4c, 0x00, 0x00, 0x00, //0x00000add jmp          LBB0_116
	//0x00000ae2 LBB0_110
	0x45, 0x31, 0xf6, //0x00000ae2 xorl         %r14d, %r14d
	0x4c, 0x89, 0xfa, //0x00000ae5 movq         %r15, %rdx
	0x89, 0xc3, //0x00000ae8 movl         %eax, %ebx
	0x83, 0xfb, 0x64, //0x00000aea cmpl         $100, %ebx
	0x0f, 0x82, 0xe8, 0xff, 0xff, 0xff, //0x00000aed jb           LBB0_111
	//0x00000af3 LBB0_114
	0x48, 0x83, 0xc2, 0xff, //0x00000af3 addq         $-1, %rdx
	0x48, 0x8d, 0x35, 0x72, 0x03, 0x00, 0x00, //0x00000af7 leaq         $882(%rip), %rsi  /* _Digits+0(%rip) */
	0x90, 0x90, //0x00000afe .p2align 4, 0x90
	//0x00000b00 LBB0_115
	0x89, 0xd8, //0x00000b00 movl         %ebx, %eax
	0x48, 0x69, 0xc0, 0x1f, 0x85, 0xeb, 0x51, //0x00000b02 imulq        $1374389535, %rax, %rax
	0x48, 0xc1, 0xe8, 0x25, //0x00000b09 shrq         $37, %rax
	0x6b, 0xf8, 0x64, //0x00000b0d imull        $100, %eax, %edi
	0x89, 0xd9, //0x00000b10 movl         %ebx, %ecx
	0x29, 0xf9, //0x00000b12 subl         %edi, %ecx
	0x0f, 0xb7, 0x0c, 0x4e, //0x00000b14 movzwl       (%rsi,%rcx,2), %ecx
	0x66, 0x89, 0x4a, 0xff, //0x00000b18 movw         %cx, $-1(%rdx)
	0x48, 0x83, 0xc2, 0xfe, //0x00000b1c addq         $-2, %rdx
	0x81, 0xfb, 0x0f, 0x27, 0x00, 0x00, //0x00000b20 cmpl         $9999, %ebx
	0x89, 0xc3, //0x00000b26 movl         %eax, %ebx
	0x0f, 0x87, 0xd2, 0xff, 0xff, 0xff, //0x00000b28 ja           LBB0_115
	//0x00000b2e LBB0_116
	0x4d, 0x89, 0xe9, //0x00000b2e movq         %r13, %r9
	0x83, 0xf8, 0x0a, //0x00000b31 cmpl         $10, %eax
	0x0f, 0x82, 0x19, 0x00, 0x00, 0x00, //0x00000b34 jb           LBB0_118
	0x89, 0xc0, //0x00000b3a movl         %eax, %eax
	0x48, 0x8d, 0x0d, 0x2d, 0x03, 0x00, 0x00, //0x00000b3c leaq         $813(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x00000b43 movzwl       (%rcx,%rax,2), %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00000b47 movq         $-48(%rbp), %rcx
	0x66, 0x89, 0x01, //0x00000b4b movw         %ax, (%rcx)
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00000b4e jmp          LBB0_119
	//0x00000b53 LBB0_118
	0x04, 0x30, //0x00000b53 addb         $48, %al
	0x48, 0x8b, 0x4d, 0xd0, //0x00000b55 movq         $-48(%rbp), %rcx
	0x88, 0x01, //0x00000b59 movb         %al, (%rcx)
	//0x00000b5b LBB0_119
	0x4d, 0x29, 0xf7, //0x00000b5b subq         %r14, %r15
	0x4d, 0x29, 0xf0, //0x00000b5e subq         %r14, %r8
	0x49, 0x83, 0xc0, 0x01, //0x00000b61 addq         $1, %r8
	0x43, 0x8d, 0x04, 0x1e, //0x00000b65 leal         (%r14,%r11), %eax
	0xf6, 0xd8, //0x00000b69 negb         %al
	0x43, 0x8d, 0x14, 0x33, //0x00000b6b leal         (%r11,%r14), %edx
	0xf7, 0xda, //0x00000b6f negl         %edx
	0x47, 0x8d, 0x2c, 0x33, //0x00000b71 leal         (%r11,%r14), %r13d
	0x41, 0x83, 0xc5, 0xff, //0x00000b75 addl         $-1, %r13d
	0x43, 0x8d, 0x34, 0x33, //0x00000b79 leal         (%r11,%r14), %esi
	0x83, 0xc6, 0xfe, //0x00000b7d addl         $-2, %esi
	0x31, 0xc9, //0x00000b80 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000b82 .p2align 4, 0x90
	//0x00000b90 LBB0_120
	0x89, 0xc3, //0x00000b90 movl         %eax, %ebx
	0x8d, 0x43, 0x03, //0x00000b92 leal         $3(%rbx), %eax
	0x83, 0xc6, 0x01, //0x00000b95 addl         $1, %esi
	0x41, 0x80, 0x7c, 0x0f, 0xff, 0x30, //0x00000b98 cmpb         $48, $-1(%r15,%rcx)
	0x48, 0x8d, 0x49, 0xff, //0x00000b9e leaq         $-1(%rcx), %rcx
	0x0f, 0x84, 0xe8, 0xff, 0xff, 0xff, //0x00000ba2 je           LBB0_120
	0x49, 0x8d, 0x04, 0x0f, //0x00000ba8 leaq         (%r15,%rcx), %rax
	0x48, 0x83, 0xc0, 0x01, //0x00000bac addq         $1, %rax
	0x45, 0x85, 0xd2, //0x00000bb0 testl        %r10d, %r10d
	0x0f, 0x8e, 0xb7, 0x00, 0x00, 0x00, //0x00000bb3 jle          LBB0_126
	0x45, 0x29, 0xf4, //0x00000bb9 subl         %r14d, %r12d
	0x41, 0x8d, 0x3c, 0x0c, //0x00000bbc leal         (%r12,%rcx), %edi
	0x83, 0xc7, 0x01, //0x00000bc0 addl         $1, %edi
	0x41, 0x39, 0xfa, //0x00000bc3 cmpl         %edi, %r10d
	0x0f, 0x8d, 0x2e, 0x00, 0x00, 0x00, //0x00000bc6 jge          LBB0_127
	0x48, 0x63, 0xc2, //0x00000bcc movslq       %edx, %rax
	0x48, 0x8d, 0x34, 0x08, //0x00000bcf leaq         (%rax,%rcx), %rsi
	0x48, 0x83, 0xc6, 0x01, //0x00000bd3 addq         $1, %rsi
	0x85, 0xf6, //0x00000bd7 testl        %esi, %esi
	0x4d, 0x89, 0xcd, //0x00000bd9 movq         %r9, %r13
	0x0f, 0x8e, 0x17, 0x01, 0x00, 0x00, //0x00000bdc jle          LBB0_138
	0x41, 0x89, 0xf0, //0x00000be2 movl         %esi, %r8d
	0x49, 0x8d, 0x50, 0xff, //0x00000be5 leaq         $-1(%r8), %rdx
	0x48, 0x83, 0xfa, 0x03, //0x00000be9 cmpq         $3, %rdx
	0x0f, 0x83, 0x9d, 0x00, 0x00, 0x00, //0x00000bed jae          LBB0_132
	0x31, 0xd2, //0x00000bf3 xorl         %edx, %edx
	0xe9, 0xc2, 0x00, 0x00, 0x00, //0x00000bf5 jmp          LBB0_135
	//0x00000bfa LBB0_127
	0x45, 0x89, 0xea, //0x00000bfa movl         %r13d, %r10d
	0x49, 0x29, 0xca, //0x00000bfd subq         %rcx, %r10
	0x45, 0x85, 0xd2, //0x00000c00 testl        %r10d, %r10d
	0x0f, 0x8e, 0x67, 0x00, 0x00, 0x00, //0x00000c03 jle          LBB0_126
	0x43, 0x8d, 0x1c, 0x33, //0x00000c09 leal         (%r11,%r14), %ebx
	0x83, 0xc3, 0xfe, //0x00000c0d addl         $-2, %ebx
	0x48, 0x29, 0xcb, //0x00000c10 subq         %rcx, %rbx
	0x31, 0xd2, //0x00000c13 xorl         %edx, %edx
	0x83, 0xfb, 0x1f, //0x00000c15 cmpl         $31, %ebx
	0x4d, 0x89, 0xcd, //0x00000c18 movq         %r9, %r13
	0x0f, 0x82, 0x0f, 0x02, 0x00, 0x00, //0x00000c1b jb           LBB0_145
	0x45, 0x01, 0xde, //0x00000c21 addl         %r11d, %r14d
	0x41, 0x83, 0xc6, 0xfe, //0x00000c24 addl         $-2, %r14d
	0x49, 0x29, 0xce, //0x00000c28 subq         %rcx, %r14
	0x41, 0x89, 0xdb, //0x00000c2b movl         %ebx, %r11d
	0x49, 0x83, 0xc3, 0x01, //0x00000c2e addq         $1, %r11
	0x4c, 0x89, 0xda, //0x00000c32 movq         %r11, %rdx
	0x48, 0x83, 0xe2, 0xe0, //0x00000c35 andq         $-32, %rdx
	0x48, 0x8b, 0x7d, 0xd0, //0x00000c39 movq         $-48(%rbp), %rdi
	0x4c, 0x01, 0xc7, //0x00000c3d addq         %r8, %rdi
	0x89, 0xf0, //0x00000c40 movl         %esi, %eax
	0x48, 0x83, 0xc0, 0x01, //0x00000c42 addq         $1, %rax
	0x48, 0x83, 0xe0, 0xe0, //0x00000c46 andq         $-32, %rax
	0x48, 0x01, 0xf8, //0x00000c4a addq         %rdi, %rax
	0x48, 0x8d, 0x72, 0xe0, //0x00000c4d leaq         $-32(%rdx), %rsi
	0x49, 0x89, 0xf0, //0x00000c51 movq         %rsi, %r8
	0x49, 0xc1, 0xe8, 0x05, //0x00000c54 shrq         $5, %r8
	0x49, 0x83, 0xc0, 0x01, //0x00000c58 addq         $1, %r8
	0x48, 0x81, 0xfe, 0xe0, 0x00, 0x00, 0x00, //0x00000c5c cmpq         $224, %rsi
	0x0f, 0x83, 0xa8, 0x00, 0x00, 0x00, //0x00000c63 jae          LBB0_139
	0x31, 0xf6, //0x00000c69 xorl         %esi, %esi
	0xe9, 0x53, 0x01, 0x00, 0x00, //0x00000c6b jmp          LBB0_141
	//0x00000c70 LBB0_126
	0x4d, 0x89, 0xcd, //0x00000c70 movq         %r9, %r13
	0xe9, 0xcb, 0x01, 0x00, 0x00, //0x00000c73 jmp          LBB0_146
	//0x00000c78 LBB0_112
	0x41, 0xbe, 0x04, 0x00, 0x00, 0x00, //0x00000c78 movl         $4, %r14d
	0x49, 0x8d, 0x57, 0xfc, //0x00000c7e leaq         $-4(%r15), %rdx
	0x83, 0xfb, 0x64, //0x00000c82 cmpl         $100, %ebx
	0x0f, 0x82, 0x50, 0xfe, 0xff, 0xff, //0x00000c85 jb           LBB0_111
	0xe9, 0x63, 0xfe, 0xff, 0xff, //0x00000c8b jmp          LBB0_114
	//0x00000c90 LBB0_132
	0x83, 0xe6, 0xfc, //0x00000c90 andl         $-4, %esi
	0x48, 0xf7, 0xde, //0x00000c93 negq         %rsi
	0x31, 0xd2, //0x00000c96 xorl         %edx, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000c98 .p2align 4, 0x90
	//0x00000ca0 LBB0_133
	0x49, 0x8d, 0x3c, 0x17, //0x00000ca0 leaq         (%r15,%rdx), %rdi
	0x8b, 0x44, 0x39, 0xfd, //0x00000ca4 movl         $-3(%rcx,%rdi), %eax
	0x89, 0x44, 0x39, 0xfe, //0x00000ca8 movl         %eax, $-2(%rcx,%rdi)
	0x48, 0x83, 0xc2, 0xfc, //0x00000cac addq         $-4, %rdx
	0x48, 0x39, 0xd6, //0x00000cb0 cmpq         %rdx, %rsi
	0x0f, 0x85, 0xe7, 0xff, 0xff, 0xff, //0x00000cb3 jne          LBB0_133
	0x48, 0xf7, 0xda, //0x00000cb9 negq         %rdx
	//0x00000cbc LBB0_135
	0x41, 0xf6, 0xc0, 0x03, //0x00000cbc testb        $3, %r8b
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00000cc0 je           LBB0_138
	0x0f, 0xb6, 0xc3, //0x00000cc6 movzbl       %bl, %eax
	0x83, 0xe0, 0x03, //0x00000cc9 andl         $3, %eax
	0x48, 0xf7, 0xd8, //0x00000ccc negq         %rax
	0x4c, 0x89, 0xfe, //0x00000ccf movq         %r15, %rsi
	0x48, 0x29, 0xd6, //0x00000cd2 subq         %rdx, %rsi
	0x31, 0xd2, //0x00000cd5 xorl         %edx, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000cd7 .p2align 4, 0x90
	//0x00000ce0 LBB0_137
	0x48, 0x8d, 0x3c, 0x16, //0x00000ce0 leaq         (%rsi,%rdx), %rdi
	0x0f, 0xb6, 0x1c, 0x39, //0x00000ce4 movzbl       (%rcx,%rdi), %ebx
	0x88, 0x5c, 0x39, 0x01, //0x00000ce8 movb         %bl, $1(%rcx,%rdi)
	0x48, 0x83, 0xc2, 0xff, //0x00000cec addq         $-1, %rdx
	0x48, 0x39, 0xd0, //0x00000cf0 cmpq         %rdx, %rax
	0x0f, 0x85, 0xe7, 0xff, 0xff, 0xff, //0x00000cf3 jne          LBB0_137
	//0x00000cf9 LBB0_138
	0x49, 0x63, 0xc2, //0x00000cf9 movslq       %r10d, %rax
	0x48, 0x8b, 0x55, 0xd0, //0x00000cfc movq         $-48(%rbp), %rdx
	0xc6, 0x04, 0x02, 0x2e, //0x00000d00 movb         $46, (%rdx,%rax)
	0x49, 0x8d, 0x04, 0x0f, //0x00000d04 leaq         (%r15,%rcx), %rax
	0x48, 0x83, 0xc0, 0x02, //0x00000d08 addq         $2, %rax
	0xe9, 0x32, 0x01, 0x00, 0x00, //0x00000d0c jmp          LBB0_146
	//0x00000d11 LBB0_139
	0x44, 0x89, 0xf3, //0x00000d11 movl         %r14d, %ebx
	0x48, 0x83, 0xc3, 0x01, //0x00000d14 addq         $1, %rbx
	0x48, 0x83, 0xe3, 0xe0, //0x00000d18 andq         $-32, %rbx
	0x48, 0x83, 0xc3, 0xe0, //0x00000d1c addq         $-32, %rbx
	0x48, 0xc1, 0xeb, 0x05, //0x00000d20 shrq         $5, %rbx
	0x48, 0x83, 0xc3, 0x01, //0x00000d24 addq         $1, %rbx
	0x48, 0x83, 0xe3, 0xf8, //0x00000d28 andq         $-8, %rbx
	0x31, 0xf6, //0x00000d2c xorl         %esi, %esi
	0xf3, 0x0f, 0x6f, 0x05, 0xca, 0xf2, 0xff, 0xff, //0x00000d2e movdqu       $-3382(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	//0x00000d36 LBB0_140
	0x49, 0x8d, 0x3c, 0x37, //0x00000d36 leaq         (%r15,%rsi), %rdi
	0xf3, 0x0f, 0x7f, 0x44, 0x39, 0x01, //0x00000d3a movdqu       %xmm0, $1(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x44, 0x39, 0x11, //0x00000d40 movdqu       %xmm0, $17(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x44, 0x39, 0x21, //0x00000d46 movdqu       %xmm0, $33(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x44, 0x39, 0x31, //0x00000d4c movdqu       %xmm0, $49(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x44, 0x39, 0x41, //0x00000d52 movdqu       %xmm0, $65(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x44, 0x39, 0x51, //0x00000d58 movdqu       %xmm0, $81(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x44, 0x39, 0x61, //0x00000d5e movdqu       %xmm0, $97(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x44, 0x39, 0x71, //0x00000d64 movdqu       %xmm0, $113(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x84, 0x39, 0x81, 0x00, 0x00, 0x00, //0x00000d6a movdqu       %xmm0, $129(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x84, 0x39, 0x91, 0x00, 0x00, 0x00, //0x00000d73 movdqu       %xmm0, $145(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x84, 0x39, 0xa1, 0x00, 0x00, 0x00, //0x00000d7c movdqu       %xmm0, $161(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x84, 0x39, 0xb1, 0x00, 0x00, 0x00, //0x00000d85 movdqu       %xmm0, $177(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x84, 0x39, 0xc1, 0x00, 0x00, 0x00, //0x00000d8e movdqu       %xmm0, $193(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x84, 0x39, 0xd1, 0x00, 0x00, 0x00, //0x00000d97 movdqu       %xmm0, $209(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x84, 0x39, 0xe1, 0x00, 0x00, 0x00, //0x00000da0 movdqu       %xmm0, $225(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x84, 0x39, 0xf1, 0x00, 0x00, 0x00, //0x00000da9 movdqu       %xmm0, $241(%rcx,%rdi)
	0x48, 0x81, 0xc6, 0x00, 0x01, 0x00, 0x00, //0x00000db2 addq         $256, %rsi
	0x48, 0x83, 0xc3, 0xf8, //0x00000db9 addq         $-8, %rbx
	0x0f, 0x85, 0x73, 0xff, 0xff, 0xff, //0x00000dbd jne          LBB0_140
	//0x00000dc3 LBB0_141
	0x48, 0x01, 0xc8, //0x00000dc3 addq         %rcx, %rax
	0x41, 0xf6, 0xc0, 0x07, //0x00000dc6 testb        $7, %r8b
	0x0f, 0x84, 0x4d, 0x00, 0x00, 0x00, //0x00000dca je           LBB0_144
	0x41, 0x80, 0xc6, 0x01, //0x00000dd0 addb         $1, %r14b
	0x41, 0x80, 0xe6, 0xe0, //0x00000dd4 andb         $-32, %r14b
	0x41, 0x80, 0xc6, 0xe0, //0x00000dd8 addb         $-32, %r14b
	0x41, 0xc0, 0xee, 0x05, //0x00000ddc shrb         $5, %r14b
	0x41, 0x80, 0xc6, 0x01, //0x00000de0 addb         $1, %r14b
	0x45, 0x0f, 0xb6, 0xc6, //0x00000de4 movzbl       %r14b, %r8d
	0x41, 0x83, 0xe0, 0x07, //0x00000de8 andl         $7, %r8d
	0x49, 0xc1, 0xe0, 0x05, //0x00000dec shlq         $5, %r8
	0x4c, 0x01, 0xfe, //0x00000df0 addq         %r15, %rsi
	0x48, 0x83, 0xc6, 0x11, //0x00000df3 addq         $17, %rsi
	0x31, 0xdb, //0x00000df7 xorl         %ebx, %ebx
	0xf3, 0x0f, 0x6f, 0x05, 0xff, 0xf1, 0xff, 0xff, //0x00000df9 movdqu       $-3585(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	//0x00000e01 LBB0_143
	0x48, 0x8d, 0x3c, 0x1e, //0x00000e01 leaq         (%rsi,%rbx), %rdi
	0xf3, 0x0f, 0x7f, 0x44, 0x39, 0xf0, //0x00000e05 movdqu       %xmm0, $-16(%rcx,%rdi)
	0xf3, 0x0f, 0x7f, 0x04, 0x39, //0x00000e0b movdqu       %xmm0, (%rcx,%rdi)
	0x48, 0x83, 0xc3, 0x20, //0x00000e10 addq         $32, %rbx
	0x49, 0x39, 0xd8, //0x00000e14 cmpq         %rbx, %r8
	0x0f, 0x85, 0xe4, 0xff, 0xff, 0xff, //0x00000e17 jne          LBB0_143
	//0x00000e1d LBB0_144
	0x49, 0x39, 0xd3, //0x00000e1d cmpq         %rdx, %r11
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x00000e20 je           LBB0_146
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e26 .p2align 4, 0x90
	//0x00000e30 LBB0_145
	0xc6, 0x00, 0x30, //0x00000e30 movb         $48, (%rax)
	0x48, 0x83, 0xc0, 0x01, //0x00000e33 addq         $1, %rax
	0x83, 0xc2, 0x01, //0x00000e37 addl         $1, %edx
	0x44, 0x39, 0xd2, //0x00000e3a cmpl         %r10d, %edx
	0x0f, 0x8c, 0xed, 0xff, 0xff, 0xff, //0x00000e3d jl           LBB0_145
	//0x00000e43 LBB0_146
	0x44, 0x29, 0xe8, //0x00000e43 subl         %r13d, %eax
	//0x00000e46 LBB0_147
	0x48, 0x83, 0xc4, 0x08, //0x00000e46 addq         $8, %rsp
	0x5b, //0x00000e4a popq         %rbx
	0x41, 0x5c, //0x00000e4b popq         %r12
	0x41, 0x5d, //0x00000e4d popq         %r13
	0x41, 0x5e, //0x00000e4f popq         %r14
	0x41, 0x5f, //0x00000e51 popq         %r15
	0x5d, //0x00000e53 popq         %rbp
	0xc3, //0x00000e54 retq         
	//0x00000e55 LBB0_148
	0x31, 0xc0, //0x00000e55 xorl         %eax, %eax
	0xe9, 0xea, 0xff, 0xff, 0xff, //0x00000e57 jmp          LBB0_147
	//0x00000e5c LBB0_149
	0x48, 0x89, 0x7d, 0xd0, //0x00000e5c movq         %rdi, $-48(%rbp)
	0x41, 0xb8, 0x6b, 0xff, 0xff, 0xff, //0x00000e60 movl         $-149, %r8d
	0x41, 0x89, 0xc7, //0x00000e66 movl         %eax, %r15d
	0xe9, 0x28, 0xf2, 0xff, 0xff, //0x00000e69 jmp          LBB0_8
	0x00, 0x00, //0x00000e6e .p2align 4, 0x00
	//0x00000e70 _Digits
	0x30, 0x30, 0x30, 0x31, 0x30, 0x32, 0x30, 0x33, 0x30, 0x34, 0x30, 0x35, 0x30, 0x36, 0x30, 0x37, //0x00000e70 QUAD $0x3330323031303030; QUAD $0x3730363035303430  // .ascii 16, '0001020304050607'
	0x30, 0x38, 0x30, 0x39, 0x31, 0x30, 0x31, 0x31, 0x31, 0x32, 0x31, 0x33, 0x31, 0x34, 0x31, 0x35, //0x00000e80 QUAD $0x3131303139303830; QUAD $0x3531343133313231  // .ascii 16, '0809101112131415'
	0x31, 0x36, 0x31, 0x37, 0x31, 0x38, 0x31, 0x39, 0x32, 0x30, 0x32, 0x31, 0x32, 0x32, 0x32, 0x33, //0x00000e90 QUAD $0x3931383137313631; QUAD $0x3332323231323032  // .ascii 16, '1617181920212223'
	0x32, 0x34, 0x32, 0x35, 0x32, 0x36, 0x32, 0x37, 0x32, 0x38, 0x32, 0x39, 0x33, 0x30, 0x33, 0x31, //0x00000ea0 QUAD $0x3732363235323432; QUAD $0x3133303339323832  // .ascii 16, '2425262728293031'
	0x33, 0x32, 0x33, 0x33, 0x33, 0x34, 0x33, 0x35, 0x33, 0x36, 0x33, 0x37, 0x33, 0x38, 0x33, 0x39, //0x00000eb0 QUAD $0x3533343333333233; QUAD $0x3933383337333633  // .ascii 16, '3233343536373839'
	0x34, 0x30, 0x34, 0x31, 0x34, 0x32, 0x34, 0x33, 0x34, 0x34, 0x34, 0x35, 0x34, 0x36, 0x34, 0x37, //0x00000ec0 QUAD $0x3334323431343034; QUAD $0x3734363435343434  // .ascii 16, '4041424344454647'
	0x34, 0x38, 0x34, 0x39, 0x35, 0x30, 0x35, 0x31, 0x35, 0x32, 0x35, 0x33, 0x35, 0x34, 0x35, 0x35, //0x00000ed0 QUAD $0x3135303539343834; QUAD $0x3535343533353235  // .ascii 16, '4849505152535455'
	0x35, 0x36, 0x35, 0x37, 0x35, 0x38, 0x35, 0x39, 0x36, 0x30, 0x36, 0x31, 0x36, 0x32, 0x36, 0x33, //0x00000ee0 QUAD $0x3935383537353635; QUAD $0x3336323631363036  // .ascii 16, '5657585960616263'
	0x36, 0x34, 0x36, 0x35, 0x36, 0x36, 0x36, 0x37, 0x36, 0x38, 0x36, 0x39, 0x37, 0x30, 0x37, 0x31, //0x00000ef0 QUAD $0x3736363635363436; QUAD $0x3137303739363836  // .ascii 16, '6465666768697071'
	0x37, 0x32, 0x37, 0x33, 0x37, 0x34, 0x37, 0x35, 0x37, 0x36, 0x37, 0x37, 0x37, 0x38, 0x37, 0x39, //0x00000f00 QUAD $0x3537343733373237; QUAD $0x3937383737373637  // .ascii 16, '7273747576777879'
	0x38, 0x30, 0x38, 0x31, 0x38, 0x32, 0x38, 0x33, 0x38, 0x34, 0x38, 0x35, 0x38, 0x36, 0x38, 0x37, //0x00000f10 QUAD $0x3338323831383038; QUAD $0x3738363835383438  // .ascii 16, '8081828384858687'
	0x38, 0x38, 0x38, 0x39, 0x39, 0x30, 0x39, 0x31, 0x39, 0x32, 0x39, 0x33, 0x39, 0x34, 0x39, 0x35, //0x00000f20 QUAD $0x3139303939383838; QUAD $0x3539343933393239  // .ascii 16, '8889909192939495'
	0x39, 0x36, 0x39, 0x37, 0x39, 0x38, 0x39, 0x39, //0x00000f30 QUAD $0x3939383937393639  // .ascii 8, '96979899'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000f38 .p2align 4, 0x00
	//0x00000f40 _pow10_ceil_sig_f32.g
	0xf5, 0xfc, 0x43, 0x4b, 0x2c, 0xb3, 0xce, 0x81, //0x00000f40 .quad -9093133594791772939
	0x32, 0xfc, 0x14, 0x5e, 0xf7, 0x5f, 0x42, 0xa2, //0x00000f48 .quad -6754730975062328270
	0x3f, 0x3b, 0x9a, 0x35, 0xf5, 0xf7, 0xd2, 0xca, //0x00000f50 .quad -3831727700400522433
	0x0e, 0xca, 0x00, 0x83, 0xf2, 0xb5, 0x87, 0xfd, //0x00000f58 .quad -177973607073265138
	0x49, 0x7e, 0xe0, 0x91, 0xb7, 0xd1, 0x74, 0x9e, //0x00000f60 .quad -7028762532061872567
	0xdb, 0x9d, 0x58, 0x76, 0x25, 0x06, 0x12, 0xc6, //0x00000f68 .quad -4174267146649952805
	0x52, 0xc5, 0xee, 0xd3, 0xae, 0x87, 0x96, 0xf7, //0x00000f70 .quad -606147914885053102
	0x53, 0x3b, 0x75, 0x44, 0xcd, 0x14, 0xbe, 0x9a, //0x00000f78 .quad -7296371474444240045
	0x28, 0x8a, 0x92, 0x95, 0x00, 0x9a, 0x6d, 0xc1, //0x00000f80 .quad -4508778324627912152
	0xb2, 0x2c, 0xf7, 0xba, 0x80, 0x00, 0xc9, 0xf1, //0x00000f88 .quad -1024286887357502286
	0xef, 0x7b, 0xda, 0x74, 0x50, 0xa0, 0x1d, 0x97, //0x00000f90 .quad -7557708332239520785
	0xeb, 0x1a, 0x11, 0x92, 0x64, 0x08, 0xe5, 0xbc, //0x00000f98 .quad -4835449396872013077
	0xa6, 0x61, 0x95, 0xb6, 0x7d, 0x4a, 0x1e, 0xec, //0x00000fa0 .quad -1432625727662628442
	0x08, 0x5d, 0x1d, 0x92, 0x8e, 0xee, 0x92, 0x93, //0x00000fa8 .quad -7812920107430224632
	0x4a, 0xb4, 0xa4, 0x36, 0x32, 0xaa, 0x77, 0xb8, //0x00000fb0 .quad -5154464115860392886
	0x5c, 0xe1, 0x4d, 0xc4, 0xbe, 0x94, 0x95, 0xe6, //0x00000fb8 .quad -1831394126398103204
	0xda, 0xac, 0xb0, 0x3a, 0xf7, 0x7c, 0x1d, 0x90, //0x00000fc0 .quad -8062150356639896358
	0x10, 0xd8, 0x5c, 0x09, 0x35, 0xdc, 0x24, 0xb4, //0x00000fc8 .quad -5466001927372482544
	0x14, 0x0e, 0xb4, 0x4b, 0x42, 0x13, 0x2e, 0xe1, //0x00000fd0 .quad -2220816390788215276
	0xcc, 0x88, 0x50, 0x6f, 0x09, 0xcc, 0xbc, 0x8c, //0x00000fd8 .quad -8305539271883716404
	0xff, 0xaa, 0x24, 0xcb, 0x0b, 0xff, 0xeb, 0xaf, //0x00000fe0 .quad -5770238071427257601
	0xbf, 0xd5, 0xed, 0xbd, 0xce, 0xfe, 0xe6, 0xdb, //0x00000fe8 .quad -2601111570856684097
	0x98, 0xa5, 0xb4, 0x36, 0x41, 0x5f, 0x70, 0x89, //0x00000ff0 .quad -8543223759426509416
	0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00000ff8 .quad -6067343680855748867
	0xbd, 0x42, 0x7a, 0xe5, 0xd5, 0x94, 0xbf, 0xd6, //0x00001000 .quad -2972493582642298179
	0xb6, 0x69, 0x6c, 0xaf, 0x05, 0xbd, 0x37, 0x86, //0x00001008 .quad -8775337516792518218
	0x24, 0x84, 0x47, 0x1b, 0x47, 0xac, 0xc5, 0xa7, //0x00001010 .quad -6357485877563259868
	0x2c, 0x65, 0x19, 0xe2, 0x58, 0x17, 0xb7, 0xd1, //0x00001018 .quad -3335171328526686932
	0x3c, 0xdf, 0x4f, 0x8d, 0x97, 0x6e, 0x12, 0x83, //0x00001020 .quad -9002011107970261188
	0x0b, 0xd7, 0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, //0x00001028 .quad -6640827866535438581
	0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001030 .quad -3689348814741910323
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001038 .quad -9223372036854775808
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, //0x00001040 .quad -6917529027641081856
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, //0x00001048 .quad -4035225266123964416
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00001050 .quad -432345564227567616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x9c, //0x00001058 .quad -7187745005283311616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xc3, //0x00001060 .quad -4372995238176751616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xf4, //0x00001068 .quad -854558029293551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x96, 0x98, //0x00001070 .quad -7451627795949551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xbc, 0xbe, //0x00001078 .quad -4702848726509551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6b, 0xee, //0x00001080 .quad -1266874889709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x02, 0x95, //0x00001088 .quad -7709325833709551616
	0x00, 0x00, 0x00, 0x00, 0x40, 0xb7, 0x43, 0xba, //0x00001090 .quad -5024971273709551616
	0x00, 0x00, 0x00, 0x00, 0x10, 0xa5, 0xd4, 0xe8, //0x00001098 .quad -1669528073709551616
	0x00, 0x00, 0x00, 0x00, 0x2a, 0xe7, 0x84, 0x91, //0x000010a0 .quad -7960984073709551616
	0x00, 0x00, 0x00, 0x80, 0xf4, 0x20, 0xe6, 0xb5, //0x000010a8 .quad -5339544073709551616
	0x00, 0x00, 0x00, 0xa0, 0x31, 0xa9, 0x5f, 0xe3, //0x000010b0 .quad -2062744073709551616
	0x00, 0x00, 0x00, 0x04, 0xbf, 0xc9, 0x1b, 0x8e, //0x000010b8 .quad -8206744073709551616
	0x00, 0x00, 0x00, 0xc5, 0x2e, 0xbc, 0xa2, 0xb1, //0x000010c0 .quad -5646744073709551616
	0x00, 0x00, 0x40, 0x76, 0x3a, 0x6b, 0x0b, 0xde, //0x000010c8 .quad -2446744073709551616
	0x00, 0x00, 0xe8, 0x89, 0x04, 0x23, 0xc7, 0x8a, //0x000010d0 .quad -8446744073709551616
	0x00, 0x00, 0x62, 0xac, 0xc5, 0xeb, 0x78, 0xad, //0x000010d8 .quad -5946744073709551616
	0x00, 0x80, 0x7a, 0x17, 0xb7, 0x26, 0xd7, 0xd8, //0x000010e0 .quad -2821744073709551616
	0x00, 0x90, 0xac, 0x6e, 0x32, 0x78, 0x86, 0x87, //0x000010e8 .quad -8681119073709551616
	0x00, 0xb4, 0x57, 0x0a, 0x3f, 0x16, 0x68, 0xa9, //0x000010f0 .quad -6239712823709551616
	0x00, 0xa1, 0xed, 0xcc, 0xce, 0x1b, 0xc2, 0xd3, //0x000010f8 .quad -3187955011209551616
	0xa0, 0x84, 0x14, 0x40, 0x61, 0x51, 0x59, 0x84, //0x00001100 .quad -8910000909647051616
	0xc8, 0xa5, 0x19, 0x90, 0xb9, 0xa5, 0x6f, 0xa5, //0x00001108 .quad -6525815118631426616
	0x3a, 0x0f, 0x20, 0xf4, 0x27, 0x8f, 0xcb, 0xce, //0x00001110 .quad -3545582879861895366
	0x85, 0x09, 0x94, 0xf8, 0x78, 0x39, 0x3f, 0x81, //0x00001118 .quad -9133518327554766459
	0xe6, 0x0b, 0xb9, 0x36, 0xd7, 0x07, 0x8f, 0xa1, //0x00001120 .quad -6805211891016070170
	0xdf, 0x4e, 0x67, 0x04, 0xcd, 0xc9, 0xf2, 0xc9, //0x00001128 .quad -3894828845342699809
	0x97, 0x22, 0x81, 0x45, 0x40, 0x7c, 0x6f, 0xfc, //0x00001130 .quad -256850038250986857
	0x9e, 0xb5, 0x70, 0x2b, 0xa8, 0xad, 0xc5, 0x9d, //0x00001138 .quad -7078060301547948642
	0x06, 0xe3, 0x4c, 0x36, 0x12, 0x19, 0x37, 0xc5, //0x00001140 .quad -4235889358507547898
	0xc7, 0x1b, 0xe0, 0xc3, 0x56, 0xdf, 0x84, 0xf6, //0x00001148 .quad -683175679707046969
	0x5d, 0x11, 0x6c, 0x3a, 0x96, 0x0b, 0x13, 0x9a, //0x00001150 .quad -7344513827457986211
	0xb4, 0x15, 0x07, 0xc9, 0x7b, 0xce, 0x97, 0xc0, //0x00001158 .quad -4568956265895094860
	0x21, 0xdb, 0x48, 0xbb, 0x1a, 0xc2, 0xbd, 0xf0, //0x00001160 .quad -1099509313941480671
	0xf5, 0x88, 0x0d, 0xb5, 0x50, 0x99, 0x76, 0x96, //0x00001168 .quad -7604722348854507275
	0x32, 0xeb, 0x50, 0xe2, 0xa4, 0x3f, 0x14, 0xbc, //0x00001170 .quad -4894216917640746190
	0xfe, 0x25, 0xe5, 0x1a, 0x8e, 0x4f, 0x19, 0xeb, //0x00001178 .quad -1506085128623544834
	0xbf, 0x37, 0xcf, 0xd0, 0xb8, 0xd1, 0xef, 0x92, //0x00001180 .quad -7858832233030797377
	0xae, 0x05, 0x03, 0x05, 0x27, 0xc6, 0xab, 0xb7, //0x00001188 .quad -5211854272861108818
	0x1a, 0xc7, 0x43, 0xc6, 0xb0, 0xb7, 0x96, 0xe5, //0x00001190 .quad -1903131822648998118
	0x70, 0x5c, 0xea, 0x7b, 0xce, 0x32, 0x7e, 0x8f, //0x00001198 .quad -8106986416796705680
	0x8c, 0xf3, 0xe4, 0x1a, 0x82, 0xbf, 0x5d, 0xb3, //0x000011a0 .quad -5522047002568494196
}
 
