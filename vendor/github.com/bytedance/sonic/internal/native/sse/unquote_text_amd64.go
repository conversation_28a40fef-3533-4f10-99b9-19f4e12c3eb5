// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_unquote = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, // QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000010 .p2align 4, 0x90
	//0x00000010 _unquote
	0x55, //0x00000010 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000011 movq         %rsp, %rbp
	0x41, 0x57, //0x00000014 pushq        %r15
	0x41, 0x56, //0x00000016 pushq        %r14
	0x41, 0x55, //0x00000018 pushq        %r13
	0x41, 0x54, //0x0000001a pushq        %r12
	0x53, //0x0000001c pushq        %rbx
	0x48, 0x83, 0xec, 0x20, //0x0000001d subq         $32, %rsp
	0x48, 0x85, 0xf6, //0x00000021 testq        %rsi, %rsi
	0x0f, 0x84, 0x0f, 0x05, 0x00, 0x00, //0x00000024 je           LBB0_1
	0x48, 0x89, 0x4d, 0xd0, //0x0000002a movq         %rcx, $-48(%rbp)
	0x4c, 0x89, 0xc0, //0x0000002e movq         %r8, %rax
	0x4c, 0x89, 0x45, 0xb8, //0x00000031 movq         %r8, $-72(%rbp)
	0x41, 0x83, 0xe0, 0x01, //0x00000035 andl         $1, %r8d
	0x4c, 0x8d, 0x1d, 0x30, 0x07, 0x00, 0x00, //0x00000039 leaq         $1840(%rip), %r11  /* __UnquoteTab+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x05, 0xb8, 0xff, 0xff, 0xff, //0x00000040 movdqu       $-72(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0x48, 0x89, 0x7d, 0xc8, //0x00000048 movq         %rdi, $-56(%rbp)
	0x49, 0x89, 0xf9, //0x0000004c movq         %rdi, %r9
	0x48, 0x89, 0x75, 0xc0, //0x0000004f movq         %rsi, $-64(%rbp)
	0x49, 0x89, 0xf2, //0x00000053 movq         %rsi, %r10
	0x4c, 0x89, 0xc6, //0x00000056 movq         %r8, %rsi
	0x48, 0x89, 0xd0, //0x00000059 movq         %rdx, %rax
	//0x0000005c LBB0_3
	0x41, 0x80, 0x39, 0x5c, //0x0000005c cmpb         $92, (%r9)
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x00000060 jne          LBB0_5
	0x45, 0x31, 0xed, //0x00000066 xorl         %r13d, %r13d
	0xe9, 0xc2, 0x00, 0x00, 0x00, //0x00000069 jmp          LBB0_17
	0x90, 0x90, //0x0000006e .p2align 4, 0x90
	//0x00000070 LBB0_5
	0x4d, 0x89, 0xd7, //0x00000070 movq         %r10, %r15
	0x49, 0x89, 0xc4, //0x00000073 movq         %rax, %r12
	0x4d, 0x89, 0xcd, //0x00000076 movq         %r9, %r13
	0x49, 0x83, 0xfa, 0x10, //0x00000079 cmpq         $16, %r10
	0x0f, 0x8c, 0x45, 0x00, 0x00, 0x00, //0x0000007d jl           LBB0_11
	0x45, 0x31, 0xe4, //0x00000083 xorl         %r12d, %r12d
	0x4c, 0x89, 0xd3, //0x00000086 movq         %r10, %rbx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000089 .p2align 4, 0x90
	//0x00000090 LBB0_7
	0xf3, 0x43, 0x0f, 0x6f, 0x0c, 0x21, //0x00000090 movdqu       (%r9,%r12), %xmm1
	0xf3, 0x42, 0x0f, 0x7f, 0x0c, 0x20, //0x00000096 movdqu       %xmm1, (%rax,%r12)
	0x66, 0x0f, 0x74, 0xc8, //0x0000009c pcmpeqb      %xmm0, %xmm1
	0x66, 0x0f, 0xd7, 0xc9, //0x000000a0 pmovmskb     %xmm1, %ecx
	0x85, 0xc9, //0x000000a4 testl        %ecx, %ecx
	0x0f, 0x85, 0x6e, 0x00, 0x00, 0x00, //0x000000a6 jne          LBB0_8
	0x4c, 0x8d, 0x7b, 0xf0, //0x000000ac leaq         $-16(%rbx), %r15
	0x49, 0x83, 0xc4, 0x10, //0x000000b0 addq         $16, %r12
	0x48, 0x83, 0xfb, 0x1f, //0x000000b4 cmpq         $31, %rbx
	0x4c, 0x89, 0xfb, //0x000000b8 movq         %r15, %rbx
	0x0f, 0x87, 0xcf, 0xff, 0xff, 0xff, //0x000000bb ja           LBB0_7
	0x4f, 0x8d, 0x2c, 0x21, //0x000000c1 leaq         (%r9,%r12), %r13
	0x49, 0x01, 0xc4, //0x000000c5 addq         %rax, %r12
	//0x000000c8 LBB0_11
	0x4d, 0x85, 0xff, //0x000000c8 testq        %r15, %r15
	0x0f, 0x84, 0x6e, 0x04, 0x00, 0x00, //0x000000cb je           LBB0_94
	0x31, 0xdb, //0x000000d1 xorl         %ebx, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000000d3 .p2align 4, 0x90
	//0x000000e0 LBB0_13
	0x41, 0x0f, 0xb6, 0x4c, 0x1d, 0x00, //0x000000e0 movzbl       (%r13,%rbx), %ecx
	0x80, 0xf9, 0x5c, //0x000000e6 cmpb         $92, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000000e9 je           LBB0_15
	0x41, 0x88, 0x0c, 0x1c, //0x000000ef movb         %cl, (%r12,%rbx)
	0x48, 0x83, 0xc3, 0x01, //0x000000f3 addq         $1, %rbx
	0x49, 0x39, 0xdf, //0x000000f7 cmpq         %rbx, %r15
	0x0f, 0x85, 0xe0, 0xff, 0xff, 0xff, //0x000000fa jne          LBB0_13
	0xe9, 0x3a, 0x04, 0x00, 0x00, //0x00000100 jmp          LBB0_94
	//0x00000105 LBB0_15
	0x4d, 0x29, 0xcd, //0x00000105 subq         %r9, %r13
	0x49, 0x01, 0xdd, //0x00000108 addq         %rbx, %r13
	0x49, 0x83, 0xfd, 0xff, //0x0000010b cmpq         $-1, %r13
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x0000010f jne          LBB0_17
	0xe9, 0x25, 0x04, 0x00, 0x00, //0x00000115 jmp          LBB0_94
	//0x0000011a LBB0_8
	0x66, 0x0f, 0xbc, 0xc9, //0x0000011a bsfw         %cx, %cx
	0x44, 0x0f, 0xb7, 0xe9, //0x0000011e movzwl       %cx, %r13d
	0x4d, 0x01, 0xe5, //0x00000122 addq         %r12, %r13
	0x49, 0x83, 0xfd, 0xff, //0x00000125 cmpq         $-1, %r13
	0x0f, 0x84, 0x10, 0x04, 0x00, 0x00, //0x00000129 je           LBB0_94
	0x90, //0x0000012f .p2align 4, 0x90
	//0x00000130 LBB0_17
	0x49, 0x8d, 0x4d, 0x02, //0x00000130 leaq         $2(%r13), %rcx
	0x49, 0x29, 0xca, //0x00000134 subq         %rcx, %r10
	0x0f, 0x88, 0x13, 0x06, 0x00, 0x00, //0x00000137 js           LBB0_18
	0x4d, 0x01, 0xe9, //0x0000013d addq         %r13, %r9
	0x49, 0x83, 0xc1, 0x02, //0x00000140 addq         $2, %r9
	0x48, 0x85, 0xf6, //0x00000144 testq        %rsi, %rsi
	0x0f, 0x85, 0xeb, 0x02, 0x00, 0x00, //0x00000147 jne          LBB0_20
	//0x0000014d LBB0_31
	0x4e, 0x8d, 0x24, 0x28, //0x0000014d leaq         (%rax,%r13), %r12
	0x41, 0x0f, 0xb6, 0x49, 0xff, //0x00000151 movzbl       $-1(%r9), %ecx
	0x42, 0x8a, 0x1c, 0x19, //0x00000156 movb         (%rcx,%r11), %bl
	0x80, 0xfb, 0xff, //0x0000015a cmpb         $-1, %bl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x0000015d je           LBB0_36
	0x84, 0xdb, //0x00000163 testb        %bl, %bl
	0x0f, 0x84, 0xa6, 0x04, 0x00, 0x00, //0x00000165 je           LBB0_33
	0x41, 0x88, 0x1c, 0x24, //0x0000016b movb         %bl, (%r12)
	//0x0000016f LBB0_35
	0x49, 0x83, 0xc4, 0x01, //0x0000016f addq         $1, %r12
	0x4c, 0x89, 0xe0, //0x00000173 movq         %r12, %rax
	0xe9, 0xaf, 0x02, 0x00, 0x00, //0x00000176 jmp          LBB0_92
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000017b .p2align 4, 0x90
	//0x00000180 LBB0_36
	0x49, 0x83, 0xfa, 0x03, //0x00000180 cmpq         $3, %r10
	0x0f, 0x86, 0xc6, 0x05, 0x00, 0x00, //0x00000184 jbe          LBB0_18
	0x45, 0x8b, 0x31, //0x0000018a movl         (%r9), %r14d
	0x44, 0x89, 0xf3, //0x0000018d movl         %r14d, %ebx
	0xf7, 0xd3, //0x00000190 notl         %ebx
	0x41, 0x8d, 0x8e, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000192 leal         $-808464432(%r14), %ecx
	0x81, 0xe3, 0x80, 0x80, 0x80, 0x80, //0x00000199 andl         $-2139062144, %ebx
	0x85, 0xcb, //0x0000019f testl        %ecx, %ebx
	0x0f, 0x85, 0xad, 0x03, 0x00, 0x00, //0x000001a1 jne          LBB0_41
	0x41, 0x8d, 0x8e, 0x19, 0x19, 0x19, 0x19, //0x000001a7 leal         $421075225(%r14), %ecx
	0x44, 0x09, 0xf1, //0x000001ae orl          %r14d, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x000001b1 testl        $-2139062144, %ecx
	0x0f, 0x85, 0x97, 0x03, 0x00, 0x00, //0x000001b7 jne          LBB0_41
	0x4d, 0x89, 0xd8, //0x000001bd movq         %r11, %r8
	0x45, 0x89, 0xf7, //0x000001c0 movl         %r14d, %r15d
	0x41, 0x81, 0xe7, 0x7f, 0x7f, 0x7f, 0x7f, //0x000001c3 andl         $2139062143, %r15d
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x000001ca movl         $-1061109568, %ecx
	0x44, 0x29, 0xf9, //0x000001cf subl         %r15d, %ecx
	0x45, 0x8d, 0x9f, 0x46, 0x46, 0x46, 0x46, //0x000001d2 leal         $1179010630(%r15), %r11d
	0x21, 0xd9, //0x000001d9 andl         %ebx, %ecx
	0x44, 0x85, 0xd9, //0x000001db testl        %r11d, %ecx
	0x0f, 0x85, 0x70, 0x03, 0x00, 0x00, //0x000001de jne          LBB0_41
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x000001e4 movl         $-522133280, %ecx
	0x44, 0x29, 0xf9, //0x000001e9 subl         %r15d, %ecx
	0x41, 0x81, 0xc7, 0x39, 0x39, 0x39, 0x39, //0x000001ec addl         $960051513, %r15d
	0x21, 0xcb, //0x000001f3 andl         %ecx, %ebx
	0x44, 0x85, 0xfb, //0x000001f5 testl        %r15d, %ebx
	0x0f, 0x85, 0x56, 0x03, 0x00, 0x00, //0x000001f8 jne          LBB0_41
	0x48, 0x89, 0xd7, //0x000001fe movq         %rdx, %rdi
	0x41, 0x0f, 0xce, //0x00000201 bswapl       %r14d
	0x44, 0x89, 0xf1, //0x00000204 movl         %r14d, %ecx
	0xc1, 0xe9, 0x04, //0x00000207 shrl         $4, %ecx
	0xf7, 0xd1, //0x0000020a notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x0000020c andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000212 leal         (%rcx,%rcx,8), %ecx
	0x41, 0x81, 0xe6, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000215 andl         $252645135, %r14d
	0x41, 0x01, 0xce, //0x0000021c addl         %ecx, %r14d
	0x44, 0x89, 0xf1, //0x0000021f movl         %r14d, %ecx
	0xc1, 0xe9, 0x04, //0x00000222 shrl         $4, %ecx
	0x44, 0x09, 0xf1, //0x00000225 orl          %r14d, %ecx
	0x44, 0x0f, 0xb6, 0xf9, //0x00000228 movzbl       %cl, %r15d
	0xc1, 0xe9, 0x08, //0x0000022c shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x0000022f andl         $65280, %ecx
	0x41, 0x09, 0xcf, //0x00000235 orl          %ecx, %r15d
	0x49, 0x83, 0xc1, 0x04, //0x00000238 addq         $4, %r9
	0x49, 0x83, 0xc2, 0xfc, //0x0000023c addq         $-4, %r10
	0x41, 0x81, 0xff, 0x80, 0x00, 0x00, 0x00, //0x00000240 cmpl         $128, %r15d
	0x0f, 0x82, 0x3e, 0x02, 0x00, 0x00, //0x00000247 jb           LBB0_51
	0x4c, 0x01, 0xe8, //0x0000024d addq         %r13, %rax
	0x48, 0x83, 0xc0, 0x02, //0x00000250 addq         $2, %rax
	0x4d, 0x89, 0xc3, //0x00000254 movq         %r8, %r11
	0x48, 0x89, 0xfa, //0x00000257 movq         %rdi, %rdx
	//0x0000025a LBB0_55
	0x41, 0x81, 0xff, 0xff, 0x07, 0x00, 0x00, //0x0000025a cmpl         $2047, %r15d
	0x0f, 0x86, 0x36, 0x02, 0x00, 0x00, //0x00000261 jbe          LBB0_56
	0x41, 0x8d, 0x8f, 0x00, 0x20, 0xff, 0xff, //0x00000267 leal         $-57344(%r15), %ecx
	0x81, 0xf9, 0xff, 0xf7, 0xff, 0xff, //0x0000026e cmpl         $-2049, %ecx
	0x0f, 0x86, 0x86, 0x01, 0x00, 0x00, //0x00000274 jbe          LBB0_58
	0x48, 0x85, 0xf6, //0x0000027a testq        %rsi, %rsi
	0x0f, 0x85, 0x27, 0x01, 0x00, 0x00, //0x0000027d jne          LBB0_60
	0x49, 0x83, 0xfa, 0x06, //0x00000283 cmpq         $6, %r10
	0x0f, 0x8c, 0x42, 0x01, 0x00, 0x00, //0x00000287 jl           LBB0_69
	//0x0000028d LBB0_66
	0x41, 0x81, 0xff, 0xff, 0xdb, 0x00, 0x00, //0x0000028d cmpl         $56319, %r15d
	0x0f, 0x87, 0x35, 0x01, 0x00, 0x00, //0x00000294 ja           LBB0_69
	0x41, 0x80, 0x39, 0x5c, //0x0000029a cmpb         $92, (%r9)
	0x0f, 0x85, 0x2b, 0x01, 0x00, 0x00, //0x0000029e jne          LBB0_69
	0x41, 0x80, 0x79, 0x01, 0x75, //0x000002a4 cmpb         $117, $1(%r9)
	0x0f, 0x85, 0x20, 0x01, 0x00, 0x00, //0x000002a9 jne          LBB0_69
	0x45, 0x8b, 0x61, 0x02, //0x000002af movl         $2(%r9), %r12d
	0x45, 0x89, 0xe6, //0x000002b3 movl         %r12d, %r14d
	0x41, 0xf7, 0xd6, //0x000002b6 notl         %r14d
	0x41, 0x8d, 0x8c, 0x24, 0xd0, 0xcf, 0xcf, 0xcf, //0x000002b9 leal         $-808464432(%r12), %ecx
	0x41, 0x81, 0xe6, 0x80, 0x80, 0x80, 0x80, //0x000002c1 andl         $-2139062144, %r14d
	0x41, 0x85, 0xce, //0x000002c8 testl        %ecx, %r14d
	0x0f, 0x85, 0x5d, 0x03, 0x00, 0x00, //0x000002cb jne          LBB0_79
	0x41, 0x8d, 0x8c, 0x24, 0x19, 0x19, 0x19, 0x19, //0x000002d1 leal         $421075225(%r12), %ecx
	0x44, 0x09, 0xe1, //0x000002d9 orl          %r12d, %ecx
	0xf7, 0xc1, 0x80, 0x80, 0x80, 0x80, //0x000002dc testl        $-2139062144, %ecx
	0x0f, 0x85, 0x46, 0x03, 0x00, 0x00, //0x000002e2 jne          LBB0_79
	0x44, 0x89, 0xe3, //0x000002e8 movl         %r12d, %ebx
	0x81, 0xe3, 0x7f, 0x7f, 0x7f, 0x7f, //0x000002eb andl         $2139062143, %ebx
	0xb9, 0xc0, 0xc0, 0xc0, 0xc0, //0x000002f1 movl         $-1061109568, %ecx
	0x29, 0xd9, //0x000002f6 subl         %ebx, %ecx
	0x44, 0x8d, 0x9b, 0x46, 0x46, 0x46, 0x46, //0x000002f8 leal         $1179010630(%rbx), %r11d
	0x44, 0x21, 0xf1, //0x000002ff andl         %r14d, %ecx
	0x44, 0x85, 0xd9, //0x00000302 testl        %r11d, %ecx
	0x0f, 0x85, 0x23, 0x03, 0x00, 0x00, //0x00000305 jne          LBB0_79
	0xb9, 0xe0, 0xe0, 0xe0, 0xe0, //0x0000030b movl         $-522133280, %ecx
	0x29, 0xd9, //0x00000310 subl         %ebx, %ecx
	0x81, 0xc3, 0x39, 0x39, 0x39, 0x39, //0x00000312 addl         $960051513, %ebx
	0x41, 0x21, 0xce, //0x00000318 andl         %ecx, %r14d
	0x41, 0x85, 0xde, //0x0000031b testl        %ebx, %r14d
	0x0f, 0x85, 0x0a, 0x03, 0x00, 0x00, //0x0000031e jne          LBB0_79
	0x41, 0x0f, 0xcc, //0x00000324 bswapl       %r12d
	0x44, 0x89, 0xe1, //0x00000327 movl         %r12d, %ecx
	0xc1, 0xe9, 0x04, //0x0000032a shrl         $4, %ecx
	0xf7, 0xd1, //0x0000032d notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x0000032f andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000335 leal         (%rcx,%rcx,8), %ecx
	0x41, 0x81, 0xe4, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000338 andl         $252645135, %r12d
	0x41, 0x01, 0xcc, //0x0000033f addl         %ecx, %r12d
	0x44, 0x89, 0xe1, //0x00000342 movl         %r12d, %ecx
	0xc1, 0xe9, 0x04, //0x00000345 shrl         $4, %ecx
	0x44, 0x09, 0xe1, //0x00000348 orl          %r12d, %ecx
	0x44, 0x0f, 0xb6, 0xd9, //0x0000034b movzbl       %cl, %r11d
	0xc1, 0xe9, 0x08, //0x0000034f shrl         $8, %ecx
	0x81, 0xe1, 0x00, 0xff, 0x00, 0x00, //0x00000352 andl         $65280, %ecx
	0x42, 0x8d, 0x1c, 0x19, //0x00000358 leal         (%rcx,%r11), %ebx
	0x49, 0x83, 0xc1, 0x06, //0x0000035c addq         $6, %r9
	0x49, 0x83, 0xc2, 0xfa, //0x00000360 addq         $-6, %r10
	0x44, 0x01, 0xd9, //0x00000364 addl         %r11d, %ecx
	0x81, 0xc1, 0x00, 0x20, 0xff, 0xff, //0x00000367 addl         $-57344, %ecx
	0x81, 0xf9, 0xff, 0xfb, 0xff, 0xff, //0x0000036d cmpl         $-1025, %ecx
	0x4d, 0x89, 0xc3, //0x00000373 movq         %r8, %r11
	0x48, 0x89, 0xfa, //0x00000376 movq         %rdi, %rdx
	0x0f, 0x87, 0x3b, 0x01, 0x00, 0x00, //0x00000379 ja           LBB0_91
	0xf6, 0x45, 0xb8, 0x02, //0x0000037f testb        $2, $-72(%rbp)
	0x0f, 0x84, 0x95, 0x01, 0x00, 0x00, //0x00000383 je           LBB0_73
	0x66, 0xc7, 0x40, 0xfe, 0xef, 0xbf, //0x00000389 movw         $-16401, $-2(%rax)
	0xc6, 0x00, 0xbd, //0x0000038f movb         $-67, (%rax)
	0x48, 0x83, 0xc0, 0x03, //0x00000392 addq         $3, %rax
	0x41, 0x89, 0xdf, //0x00000396 movl         %ebx, %r15d
	0x81, 0xfb, 0x80, 0x00, 0x00, 0x00, //0x00000399 cmpl         $128, %ebx
	0x0f, 0x83, 0xb5, 0xfe, 0xff, 0xff, //0x0000039f jae          LBB0_55
	0xe9, 0x5c, 0x01, 0x00, 0x00, //0x000003a5 jmp          LBB0_52
	//0x000003aa LBB0_60
	0x4d, 0x85, 0xd2, //0x000003aa testq        %r10, %r10
	0x0f, 0x8e, 0x7b, 0x03, 0x00, 0x00, //0x000003ad jle          LBB0_61
	0x41, 0x80, 0x39, 0x5c, //0x000003b3 cmpb         $92, (%r9)
	0x0f, 0x85, 0x57, 0x01, 0x00, 0x00, //0x000003b7 jne          LBB0_71
	0x49, 0x83, 0xc2, 0xff, //0x000003bd addq         $-1, %r10
	0x49, 0x83, 0xc1, 0x01, //0x000003c1 addq         $1, %r9
	0x49, 0x83, 0xfa, 0x06, //0x000003c5 cmpq         $6, %r10
	0x0f, 0x8d, 0xbe, 0xfe, 0xff, 0xff, //0x000003c9 jge          LBB0_66
	//0x000003cf LBB0_69
	0xf6, 0x45, 0xb8, 0x02, //0x000003cf testb        $2, $-72(%rbp)
	0x0f, 0x84, 0x49, 0x03, 0x00, 0x00, //0x000003d3 je           LBB0_70
	//0x000003d9 LBB0_72
	0x66, 0xc7, 0x40, 0xfe, 0xef, 0xbf, //0x000003d9 movw         $-16401, $-2(%rax)
	0xc6, 0x00, 0xbd, //0x000003df movb         $-67, (%rax)
	0x48, 0x83, 0xc0, 0x01, //0x000003e2 addq         $1, %rax
	0x4d, 0x89, 0xc3, //0x000003e6 movq         %r8, %r11
	0x48, 0x89, 0xfa, //0x000003e9 movq         %rdi, %rdx
	0xe9, 0x39, 0x00, 0x00, 0x00, //0x000003ec jmp          LBB0_92
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003f1 .p2align 4, 0x90
	//0x00000400 LBB0_58
	0x44, 0x89, 0xf9, //0x00000400 movl         %r15d, %ecx
	0xc1, 0xe9, 0x0c, //0x00000403 shrl         $12, %ecx
	0x80, 0xc9, 0xe0, //0x00000406 orb          $-32, %cl
	0x88, 0x48, 0xfe, //0x00000409 movb         %cl, $-2(%rax)
	0x44, 0x89, 0xf9, //0x0000040c movl         %r15d, %ecx
	0xc1, 0xe9, 0x06, //0x0000040f shrl         $6, %ecx
	0x80, 0xe1, 0x3f, //0x00000412 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00000415 orb          $-128, %cl
	0x88, 0x48, 0xff, //0x00000418 movb         %cl, $-1(%rax)
	0x41, 0x80, 0xe7, 0x3f, //0x0000041b andb         $63, %r15b
	0x41, 0x80, 0xcf, 0x80, //0x0000041f orb          $-128, %r15b
	0x44, 0x88, 0x38, //0x00000423 movb         %r15b, (%rax)
	0x48, 0x83, 0xc0, 0x01, //0x00000426 addq         $1, %rax
	//0x0000042a LBB0_92
	0x4d, 0x85, 0xd2, //0x0000042a testq        %r10, %r10
	0x0f, 0x85, 0x29, 0xfc, 0xff, 0xff, //0x0000042d jne          LBB0_3
	0xe9, 0xd1, 0x01, 0x00, 0x00, //0x00000433 jmp          LBB0_93
	//0x00000438 LBB0_20
	0x45, 0x85, 0xd2, //0x00000438 testl        %r10d, %r10d
	0x0f, 0x84, 0x0f, 0x03, 0x00, 0x00, //0x0000043b je           LBB0_18
	0x41, 0x80, 0x79, 0xff, 0x5c, //0x00000441 cmpb         $92, $-1(%r9)
	0x0f, 0x85, 0xac, 0x02, 0x00, 0x00, //0x00000446 jne          LBB0_22
	0x41, 0x80, 0x39, 0x5c, //0x0000044c cmpb         $92, (%r9)
	0x0f, 0x85, 0x28, 0x00, 0x00, 0x00, //0x00000450 jne          LBB0_30
	0x41, 0x83, 0xfa, 0x01, //0x00000456 cmpl         $1, %r10d
	0x0f, 0x8e, 0xf0, 0x02, 0x00, 0x00, //0x0000045a jle          LBB0_18
	0x41, 0x8a, 0x49, 0x01, //0x00000460 movb         $1(%r9), %cl
	0x80, 0xf9, 0x22, //0x00000464 cmpb         $34, %cl
	0x0f, 0x84, 0x09, 0x00, 0x00, 0x00, //0x00000467 je           LBB0_29
	0x80, 0xf9, 0x5c, //0x0000046d cmpb         $92, %cl
	0x0f, 0x85, 0x9f, 0x02, 0x00, 0x00, //0x00000470 jne          LBB0_27
	//0x00000476 LBB0_29
	0x49, 0x83, 0xc1, 0x01, //0x00000476 addq         $1, %r9
	0x49, 0x83, 0xc2, 0xff, //0x0000047a addq         $-1, %r10
	//0x0000047e LBB0_30
	0x49, 0x83, 0xc1, 0x01, //0x0000047e addq         $1, %r9
	0x49, 0x83, 0xc2, 0xff, //0x00000482 addq         $-1, %r10
	0xe9, 0xc2, 0xfc, 0xff, 0xff, //0x00000486 jmp          LBB0_31
	//0x0000048b LBB0_51
	0x44, 0x89, 0xfb, //0x0000048b movl         %r15d, %ebx
	0x4d, 0x89, 0xc3, //0x0000048e movq         %r8, %r11
	0x48, 0x89, 0xfa, //0x00000491 movq         %rdi, %rdx
	0x41, 0x88, 0x1c, 0x24, //0x00000494 movb         %bl, (%r12)
	0xe9, 0xd2, 0xfc, 0xff, 0xff, //0x00000498 jmp          LBB0_35
	//0x0000049d LBB0_56
	0x44, 0x89, 0xf9, //0x0000049d movl         %r15d, %ecx
	0xc1, 0xe9, 0x06, //0x000004a0 shrl         $6, %ecx
	0x80, 0xc9, 0xc0, //0x000004a3 orb          $-64, %cl
	0x88, 0x48, 0xfe, //0x000004a6 movb         %cl, $-2(%rax)
	0x41, 0x80, 0xe7, 0x3f, //0x000004a9 andb         $63, %r15b
	0x41, 0x80, 0xcf, 0x80, //0x000004ad orb          $-128, %r15b
	0x44, 0x88, 0x78, 0xff, //0x000004b1 movb         %r15b, $-1(%rax)
	0xe9, 0x70, 0xff, 0xff, 0xff, //0x000004b5 jmp          LBB0_92
	//0x000004ba LBB0_91
	0x41, 0xc1, 0xe7, 0x0a, //0x000004ba shll         $10, %r15d
	0x41, 0x89, 0xdc, //0x000004be movl         %ebx, %r12d
	0x45, 0x01, 0xfc, //0x000004c1 addl         %r15d, %r12d
	0x44, 0x01, 0xfb, //0x000004c4 addl         %r15d, %ebx
	0x81, 0xc3, 0x00, 0x24, 0xa0, 0xfc, //0x000004c7 addl         $-56613888, %ebx
	0x89, 0xd9, //0x000004cd movl         %ebx, %ecx
	0xc1, 0xe9, 0x12, //0x000004cf shrl         $18, %ecx
	0x80, 0xc9, 0xf0, //0x000004d2 orb          $-16, %cl
	0x88, 0x48, 0xfe, //0x000004d5 movb         %cl, $-2(%rax)
	0x89, 0xd9, //0x000004d8 movl         %ebx, %ecx
	0xc1, 0xe9, 0x0c, //0x000004da shrl         $12, %ecx
	0x80, 0xe1, 0x3f, //0x000004dd andb         $63, %cl
	0x80, 0xc9, 0x80, //0x000004e0 orb          $-128, %cl
	0x88, 0x48, 0xff, //0x000004e3 movb         %cl, $-1(%rax)
	0xc1, 0xeb, 0x06, //0x000004e6 shrl         $6, %ebx
	0x80, 0xe3, 0x3f, //0x000004e9 andb         $63, %bl
	0x80, 0xcb, 0x80, //0x000004ec orb          $-128, %bl
	0x88, 0x18, //0x000004ef movb         %bl, (%rax)
	0x41, 0x80, 0xe4, 0x3f, //0x000004f1 andb         $63, %r12b
	0x41, 0x80, 0xcc, 0x80, //0x000004f5 orb          $-128, %r12b
	0x44, 0x88, 0x60, 0x01, //0x000004f9 movb         %r12b, $1(%rax)
	0x48, 0x83, 0xc0, 0x02, //0x000004fd addq         $2, %rax
	0xe9, 0x24, 0xff, 0xff, 0xff, //0x00000501 jmp          LBB0_92
	//0x00000506 LBB0_52
	0x48, 0x83, 0xc0, 0xfe, //0x00000506 addq         $-2, %rax
	0x49, 0x89, 0xc4, //0x0000050a movq         %rax, %r12
	0x88, 0x18, //0x0000050d movb         %bl, (%rax)
	0xe9, 0x5b, 0xfc, 0xff, 0xff, //0x0000050f jmp          LBB0_35
	//0x00000514 LBB0_71
	0xf6, 0x45, 0xb8, 0x02, //0x00000514 testb        $2, $-72(%rbp)
	0x0f, 0x85, 0xbb, 0xfe, 0xff, 0xff, //0x00000518 jne          LBB0_72
	//0x0000051e LBB0_73
	0x4c, 0x2b, 0x4d, 0xc8, //0x0000051e subq         $-56(%rbp), %r9
	//0x00000522 LBB0_74
	0x49, 0x83, 0xc1, 0xfc, //0x00000522 addq         $-4, %r9
	0x48, 0x8b, 0x45, 0xd0, //0x00000526 movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x08, //0x0000052a movq         %r9, (%rax)
	0x48, 0xc7, 0xc0, 0xfc, 0xff, 0xff, 0xff, //0x0000052d movq         $-4, %rax
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x00000534 jmp          LBB0_95
	//0x00000539 LBB0_1
	0x45, 0x31, 0xd2, //0x00000539 xorl         %r10d, %r10d
	0x48, 0x89, 0xd0, //0x0000053c movq         %rdx, %rax
	//0x0000053f LBB0_94
	0x4c, 0x01, 0xd0, //0x0000053f addq         %r10, %rax
	0x48, 0x29, 0xd0, //0x00000542 subq         %rdx, %rax
	//0x00000545 LBB0_95
	0x48, 0x83, 0xc4, 0x20, //0x00000545 addq         $32, %rsp
	0x5b, //0x00000549 popq         %rbx
	0x41, 0x5c, //0x0000054a popq         %r12
	0x41, 0x5d, //0x0000054c popq         %r13
	0x41, 0x5e, //0x0000054e popq         %r14
	0x41, 0x5f, //0x00000550 popq         %r15
	0x5d, //0x00000552 popq         %rbp
	0xc3, //0x00000553 retq         
	//0x00000554 LBB0_41
	0x4c, 0x89, 0xca, //0x00000554 movq         %r9, %rdx
	0x48, 0x2b, 0x55, 0xc8, //0x00000557 subq         $-56(%rbp), %rdx
	0x48, 0x8b, 0x7d, 0xd0, //0x0000055b movq         $-48(%rbp), %rdi
	0x48, 0x89, 0x17, //0x0000055f movq         %rdx, (%rdi)
	0x41, 0x8a, 0x09, //0x00000562 movb         (%r9), %cl
	0x8d, 0x71, 0xc6, //0x00000565 leal         $-58(%rcx), %esi
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000568 movq         $-2, %rax
	0x40, 0x80, 0xfe, 0xf5, //0x0000056f cmpb         $-11, %sil
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x00000573 ja           LBB0_43
	0x80, 0xe1, 0xdf, //0x00000579 andb         $-33, %cl
	0x80, 0xc1, 0xb9, //0x0000057c addb         $-71, %cl
	0x80, 0xf9, 0xfa, //0x0000057f cmpb         $-6, %cl
	0x0f, 0x82, 0xbd, 0xff, 0xff, 0xff, //0x00000582 jb           LBB0_95
	//0x00000588 LBB0_43
	0x48, 0x8d, 0x4a, 0x01, //0x00000588 leaq         $1(%rdx), %rcx
	0x48, 0x89, 0x0f, //0x0000058c movq         %rcx, (%rdi)
	0x41, 0x8a, 0x49, 0x01, //0x0000058f movb         $1(%r9), %cl
	0x8d, 0x71, 0xc6, //0x00000593 leal         $-58(%rcx), %esi
	0x40, 0x80, 0xfe, 0xf5, //0x00000596 cmpb         $-11, %sil
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x0000059a ja           LBB0_45
	0x80, 0xe1, 0xdf, //0x000005a0 andb         $-33, %cl
	0x80, 0xc1, 0xb9, //0x000005a3 addb         $-71, %cl
	0x80, 0xf9, 0xfa, //0x000005a6 cmpb         $-6, %cl
	0x0f, 0x82, 0x96, 0xff, 0xff, 0xff, //0x000005a9 jb           LBB0_95
	//0x000005af LBB0_45
	0x48, 0x8d, 0x4a, 0x02, //0x000005af leaq         $2(%rdx), %rcx
	0x48, 0x89, 0x0f, //0x000005b3 movq         %rcx, (%rdi)
	0x41, 0x8a, 0x49, 0x02, //0x000005b6 movb         $2(%r9), %cl
	0x8d, 0x71, 0xc6, //0x000005ba leal         $-58(%rcx), %esi
	0x40, 0x80, 0xfe, 0xf5, //0x000005bd cmpb         $-11, %sil
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x000005c1 ja           LBB0_47
	0x80, 0xe1, 0xdf, //0x000005c7 andb         $-33, %cl
	0x80, 0xc1, 0xb9, //0x000005ca addb         $-71, %cl
	0x80, 0xf9, 0xfa, //0x000005cd cmpb         $-6, %cl
	0x0f, 0x82, 0x6f, 0xff, 0xff, 0xff, //0x000005d0 jb           LBB0_95
	//0x000005d6 LBB0_47
	0x48, 0x8d, 0x4a, 0x03, //0x000005d6 leaq         $3(%rdx), %rcx
	0x48, 0x89, 0x0f, //0x000005da movq         %rcx, (%rdi)
	0x41, 0x8a, 0x49, 0x03, //0x000005dd movb         $3(%r9), %cl
	0x8d, 0x71, 0xc6, //0x000005e1 leal         $-58(%rcx), %esi
	0x40, 0x80, 0xfe, 0xf5, //0x000005e4 cmpb         $-11, %sil
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x000005e8 ja           LBB0_49
	0x80, 0xe1, 0xdf, //0x000005ee andb         $-33, %cl
	0x80, 0xc1, 0xb9, //0x000005f1 addb         $-71, %cl
	0x80, 0xf9, 0xfa, //0x000005f4 cmpb         $-6, %cl
	0x0f, 0x82, 0x48, 0xff, 0xff, 0xff, //0x000005f7 jb           LBB0_95
	//0x000005fd LBB0_49
	0x48, 0x83, 0xc2, 0x04, //0x000005fd addq         $4, %rdx
	0x48, 0x89, 0x17, //0x00000601 movq         %rdx, (%rdi)
	0xe9, 0x3c, 0xff, 0xff, 0xff, //0x00000604 jmp          LBB0_95
	//0x00000609 LBB0_93
	0x45, 0x31, 0xd2, //0x00000609 xorl         %r10d, %r10d
	0xe9, 0x2e, 0xff, 0xff, 0xff, //0x0000060c jmp          LBB0_94
	//0x00000611 LBB0_33
	0x48, 0x8b, 0x45, 0xc8, //0x00000611 movq         $-56(%rbp), %rax
	0x48, 0xf7, 0xd0, //0x00000615 notq         %rax
	0x49, 0x01, 0xc1, //0x00000618 addq         %rax, %r9
	0x48, 0x8b, 0x45, 0xd0, //0x0000061b movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x08, //0x0000061f movq         %r9, (%rax)
	0x48, 0xc7, 0xc0, 0xfd, 0xff, 0xff, 0xff, //0x00000622 movq         $-3, %rax
	0xe9, 0x17, 0xff, 0xff, 0xff, //0x00000629 jmp          LBB0_95
	//0x0000062e LBB0_79
	0x4c, 0x89, 0xca, //0x0000062e movq         %r9, %rdx
	0x48, 0x2b, 0x55, 0xc8, //0x00000631 subq         $-56(%rbp), %rdx
	0x48, 0x83, 0xc2, 0x02, //0x00000635 addq         $2, %rdx
	0x48, 0x8b, 0x45, 0xd0, //0x00000639 movq         $-48(%rbp), %rax
	0x48, 0x89, 0x10, //0x0000063d movq         %rdx, (%rax)
	0x41, 0x8a, 0x49, 0x02, //0x00000640 movb         $2(%r9), %cl
	0x8d, 0x71, 0xc6, //0x00000644 leal         $-58(%rcx), %esi
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000647 movq         $-2, %rax
	0x40, 0x80, 0xfe, 0xf5, //0x0000064e cmpb         $-11, %sil
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x00000652 ja           LBB0_81
	0x80, 0xe1, 0xdf, //0x00000658 andb         $-33, %cl
	0x80, 0xc1, 0xb9, //0x0000065b addb         $-71, %cl
	0x80, 0xf9, 0xfa, //0x0000065e cmpb         $-6, %cl
	0x0f, 0x82, 0xde, 0xfe, 0xff, 0xff, //0x00000661 jb           LBB0_95
	//0x00000667 LBB0_81
	0x48, 0x8d, 0x4a, 0x01, //0x00000667 leaq         $1(%rdx), %rcx
	0x48, 0x8b, 0x75, 0xd0, //0x0000066b movq         $-48(%rbp), %rsi
	0x48, 0x89, 0x0e, //0x0000066f movq         %rcx, (%rsi)
	0x41, 0x8a, 0x49, 0x03, //0x00000672 movb         $3(%r9), %cl
	0x8d, 0x71, 0xc6, //0x00000676 leal         $-58(%rcx), %esi
	0x40, 0x80, 0xfe, 0xf5, //0x00000679 cmpb         $-11, %sil
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x0000067d ja           LBB0_83
	0x80, 0xe1, 0xdf, //0x00000683 andb         $-33, %cl
	0x80, 0xc1, 0xb9, //0x00000686 addb         $-71, %cl
	0x80, 0xf9, 0xfa, //0x00000689 cmpb         $-6, %cl
	0x0f, 0x82, 0xb3, 0xfe, 0xff, 0xff, //0x0000068c jb           LBB0_95
	//0x00000692 LBB0_83
	0x48, 0x8d, 0x4a, 0x02, //0x00000692 leaq         $2(%rdx), %rcx
	0x48, 0x8b, 0x75, 0xd0, //0x00000696 movq         $-48(%rbp), %rsi
	0x48, 0x89, 0x0e, //0x0000069a movq         %rcx, (%rsi)
	0x41, 0x8a, 0x49, 0x04, //0x0000069d movb         $4(%r9), %cl
	0x8d, 0x71, 0xc6, //0x000006a1 leal         $-58(%rcx), %esi
	0x40, 0x80, 0xfe, 0xf5, //0x000006a4 cmpb         $-11, %sil
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x000006a8 ja           LBB0_85
	0x80, 0xe1, 0xdf, //0x000006ae andb         $-33, %cl
	0x80, 0xc1, 0xb9, //0x000006b1 addb         $-71, %cl
	0x80, 0xf9, 0xfa, //0x000006b4 cmpb         $-6, %cl
	0x0f, 0x82, 0x88, 0xfe, 0xff, 0xff, //0x000006b7 jb           LBB0_95
	//0x000006bd LBB0_85
	0x48, 0x8d, 0x4a, 0x03, //0x000006bd leaq         $3(%rdx), %rcx
	0x48, 0x8b, 0x75, 0xd0, //0x000006c1 movq         $-48(%rbp), %rsi
	0x48, 0x89, 0x0e, //0x000006c5 movq         %rcx, (%rsi)
	0x41, 0x8a, 0x49, 0x05, //0x000006c8 movb         $5(%r9), %cl
	0x8d, 0x71, 0xc6, //0x000006cc leal         $-58(%rcx), %esi
	0x40, 0x80, 0xfe, 0xf5, //0x000006cf cmpb         $-11, %sil
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x000006d3 ja           LBB0_87
	0x80, 0xe1, 0xdf, //0x000006d9 andb         $-33, %cl
	0x80, 0xc1, 0xb9, //0x000006dc addb         $-71, %cl
	0x80, 0xf9, 0xfa, //0x000006df cmpb         $-6, %cl
	0x0f, 0x82, 0x5d, 0xfe, 0xff, 0xff, //0x000006e2 jb           LBB0_95
	//0x000006e8 LBB0_87
	0x48, 0x83, 0xc2, 0x04, //0x000006e8 addq         $4, %rdx
	0x48, 0x8b, 0x4d, 0xd0, //0x000006ec movq         $-48(%rbp), %rcx
	0x48, 0x89, 0x11, //0x000006f0 movq         %rdx, (%rcx)
	0xe9, 0x4d, 0xfe, 0xff, 0xff, //0x000006f3 jmp          LBB0_95
	//0x000006f8 LBB0_22
	0x48, 0x8b, 0x45, 0xc8, //0x000006f8 movq         $-56(%rbp), %rax
	0x48, 0xf7, 0xd0, //0x000006fc notq         %rax
	0x49, 0x01, 0xc1, //0x000006ff addq         %rax, %r9
	//0x00000702 LBB0_28
	0x48, 0x8b, 0x45, 0xd0, //0x00000702 movq         $-48(%rbp), %rax
	0x4c, 0x89, 0x08, //0x00000706 movq         %r9, (%rax)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000709 movq         $-2, %rax
	0xe9, 0x30, 0xfe, 0xff, 0xff, //0x00000710 jmp          LBB0_95
	//0x00000715 LBB0_27
	0x4c, 0x2b, 0x4d, 0xc8, //0x00000715 subq         $-56(%rbp), %r9
	0x49, 0x83, 0xc1, 0x01, //0x00000719 addq         $1, %r9
	0xe9, 0xe0, 0xff, 0xff, 0xff, //0x0000071d jmp          LBB0_28
	//0x00000722 LBB0_70
	0x48, 0x03, 0x75, 0xc8, //0x00000722 addq         $-56(%rbp), %rsi
	0x49, 0x29, 0xf1, //0x00000726 subq         %rsi, %r9
	0xe9, 0xf4, 0xfd, 0xff, 0xff, //0x00000729 jmp          LBB0_74
	//0x0000072e LBB0_61
	0xf6, 0x45, 0xb8, 0x02, //0x0000072e testb        $2, $-72(%rbp)
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00000732 je           LBB0_18
	0x66, 0xc7, 0x40, 0xfe, 0xef, 0xbf, //0x00000738 movw         $-16401, $-2(%rax)
	0xc6, 0x00, 0xbd, //0x0000073e movb         $-67, (%rax)
	0x48, 0x83, 0xc0, 0x01, //0x00000741 addq         $1, %rax
	0x45, 0x31, 0xd2, //0x00000745 xorl         %r10d, %r10d
	0x48, 0x89, 0xfa, //0x00000748 movq         %rdi, %rdx
	0xe9, 0xef, 0xfd, 0xff, 0xff, //0x0000074b jmp          LBB0_94
	//0x00000750 LBB0_18
	0x48, 0x8b, 0x45, 0xd0, //0x00000750 movq         $-48(%rbp), %rax
	0x48, 0x8b, 0x4d, 0xc0, //0x00000754 movq         $-64(%rbp), %rcx
	0x48, 0x89, 0x08, //0x00000758 movq         %rcx, (%rax)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000075b movq         $-1, %rax
	0xe9, 0xde, 0xfd, 0xff, 0xff, //0x00000762 jmp          LBB0_95
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000767 .p2align 4, 0x00
	//0x00000770 __UnquoteTab
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000770 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000780 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, //0x00000790 QUAD $0x0000000000220000; QUAD $0x2f00000000000000  // .ascii 16, '\x00\x00"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00/'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000007a0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000007b0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5c, 0x00, 0x00, 0x00, //0x000007c0 QUAD $0x0000000000000000; QUAD $0x0000005c00000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\\\x00\x00\x00'
	0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, //0x000007d0 QUAD $0x000c000000080000; QUAD $0x000a000000000000  // .ascii 16, '\x00\x00\x08\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\n\x00'
	0x00, 0x00, 0x0d, 0x00, 0x09, 0xff, //0x000007e0 LONG $0x000d0000; WORD $0xff09  // .ascii 6, '\x00\x00\r\x00\t\xff'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000007e6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000007f6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000806 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000816 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000826 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000836 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000846 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000856 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000866 QUAD $0x0000000000000000; WORD $0x0000  // .space 10, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
}
 
