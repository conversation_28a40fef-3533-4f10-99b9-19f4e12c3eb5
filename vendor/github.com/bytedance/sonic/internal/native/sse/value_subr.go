// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package sse

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__value = 208
)

const (
    _stack__value = 128
)

const (
    _size__value = 12188
)

var (
    _pcsp__value = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0x1f9, 128},
        {0x1fa, 48},
        {0x1fc, 40},
        {0x1fe, 32},
        {0x200, 24},
        {0x202, 16},
        {0x203, 8},
        {0x204, 0},
        {0x2f9c, 128},
    }
)

var _cfunc_value = []loader.CFunc{
    {"_value_entry", 0,  _entry__value, 0, nil},
    {"_value", _entry__value, _size__value, _stack__value, _pcsp__value},
}
