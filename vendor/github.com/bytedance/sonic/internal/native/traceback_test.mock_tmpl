
// +build !race,amd64,go1.16,!go1.24

// Code generated by <PERSON><PERSON><PERSON>, DO NOT EDIT.

/*
 * Copyright 2021 ByteDance Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the     License    );
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an     AS IS     BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
package {{PACKAGE}}

import (
	"unsafe"
	"testing"

	"github.com/bytedance/sonic/loader"
)

// copied from g01.20.4 signal_linux_amd64.go
type sigctxt struct {
	info unsafe.Pointer
	ctxt unsafe.Pointer
}

type stackt struct {
	ss_sp     *byte
	ss_flags  int32
	pad_cgo_0 [4]byte
	ss_size   uintptr
}

type mcontext struct {
	gregs       [23]uint64
	fpregs      unsafe.Pointer
	__reserved1 [8]uint64
}

type sigcontext struct {
	r8          uint64
	r9          uint64
	r10         uint64
	r11         uint64
	r12         uint64
	r13         uint64
	r14         uint64
	r15         uint64
	rdi         uint64
	rsi         uint64
	rbp         uint64
	rbx         uint64
	rdx         uint64
	rax         uint64
	rcx         uint64
	rsp         uint64
	rip         uint64
	eflags      uint64
	cs          uint16
	gs          uint16
	fs          uint16
	__pad0      uint16
	err         uint64
	trapno      uint64
	oldmask     uint64
	cr2         uint64
	fpstate     unsafe.Pointer
	__reserved1 [8]uint64
}
type ucontext struct {
	uc_flags     uint64
	uc_link      *ucontext
	uc_stack     stackt
	uc_mcontext  mcontext
}

//go:nosplit
func (c *sigctxt) regs() *sigcontext {
	return (*sigcontext)(unsafe.Pointer(&(*ucontext)(c.ctxt).uc_mcontext))
}

func (c *sigctxt) rsp() uint64 { return c.regs().rsp }

//go:nosplit
func (c *sigctxt) sigpc() uintptr { return uintptr(c.rip()) }

//go:nosplit
func (c *sigctxt) rip() uint64 { return c.regs().rip }
func (c *sigctxt) sigsp() uintptr    { return uintptr(c.rsp()) }
func (c *sigctxt) siglr() uintptr    { return 0 }

// only used for test sonic trace
//go:linkname testSigtrap runtime.testSigtrap
var testSigtrap func(info unsafe.Pointer, c *sigctxt, gp unsafe.Pointer) bool 

func init() {
	if testSigtrap == nil {
		testSigtrap = sonicSigTrap
	}
}

//go:linkname traceback1 runtime.traceback1
func traceback1(pc, sp, lr uintptr, gp unsafe.Pointer, flags uint);

func sonicSigTrap(info unsafe.Pointer, c *sigctxt, gp unsafe.Pointer) bool {
	pc := c.sigpc()
	sp := c.sigsp()
	lr := c.siglr()
	traceback1(pc, sp, lr, gp, 0);
	return true
}


// =================== register mocked functions  =================== 

var F_mock_f32toa func()
var S_mock_f32toa uintptr

//go:nosplit
func mock_f32toa() {
    F_mock_f32toa()
}

var F_mock_f64toa func()
var S_mock_f64toa uintptr

//go:nosplit
func mock_f64toa() {
    F_mock_f64toa()
}

var F_mock_get_by_path func()
var S_mock_get_by_path uintptr

//go:nosplit
func mock_get_by_path() {
    F_mock_get_by_path()
}

var F_mock_html_escape func()
var S_mock_html_escape uintptr

//go:nosplit
func mock_html_escape() {
    F_mock_html_escape()
}

var F_mock_i64toa func()
var S_mock_i64toa uintptr

//go:nosplit
func mock_i64toa() {
    F_mock_i64toa()
}

var F_mock_lspace func()
var S_mock_lspace uintptr

//go:nosplit
func mock_lspace() {
    F_mock_lspace()
}

var F_mock_quote func()
var S_mock_quote uintptr

//go:nosplit
func mock_quote() {
    F_mock_quote()
}

var F_mock_skip_array func()
var S_mock_skip_array uintptr

//go:nosplit
func mock_skip_array() {
    F_mock_skip_array()
}

var F_mock_skip_number func()
var S_mock_skip_number uintptr

//go:nosplit
func mock_skip_number() {
    F_mock_skip_number()
}

var F_mock_skip_one func()
var S_mock_skip_one uintptr

//go:nosplit
func mock_skip_one() {
    F_mock_skip_one()
}

var F_mock_skip_object func()
var S_mock_skip_object uintptr

//go:nosplit
func mock_skip_object() {
    F_mock_skip_object()
}

var F_mock_skip_one_fast func()
var S_mock_skip_one_fast uintptr

//go:nosplit
func mock_skip_one_fast() {
    F_mock_skip_one_fast()
}

var F_mock_u64toa func()
var S_mock_u64toa uintptr

//go:nosplit
func mock_u64toa() {
    F_mock_u64toa()
}

var F_mock_unquote func()
var S_mock_unquote uintptr

//go:nosplit
func mock_unquote() {
    F_mock_unquote()
}

var F_mock_validate_one func()
var S_mock_validate_one uintptr

//go:nosplit
func mock_validate_one() {
    F_mock_validate_one()
}

var F_mock_validate_utf8 func()
var S_mock_validate_utf8 uintptr

//go:nosplit
func mock_validate_utf8() {
    F_mock_validate_utf8()
}

var F_mock_validate_utf8_fast func()
var S_mock_validate_utf8_fast uintptr

//go:nosplit
func mock_validate_utf8_fast() {
    F_mock_validate_utf8_fast()
}

var F_mock_vnumber func()
var S_mock_vnumber uintptr

//go:nosplit
func mock_vnumber() {
    F_mock_vnumber()
}

var F_mock_vsigned func()
var S_mock_vsigned uintptr

//go:nosplit
func mock_vsigned() {
    F_mock_vsigned()
}

var F_mock_vunsigned func()
var S_mock_vunsigned uintptr

//go:nosplit
func mock_vunsigned() {
    F_mock_vunsigned()
}

var F_mock_vstring func()
var S_mock_vstring uintptr

//go:nosplit
func mock_vstring() {
    F_mock_vstring()
}

var F_mock_value func()
var S_mock_value uintptr

//go:nosplit
func mock_value() {
    F_mock_value()
}

var F_mock_parse_with_padding func()
var S_mock_parse_with_padding uintptr

//go:nosplit
func mock_parse_with_padding() {
    F_mock_parse_with_padding()
}

var F_mock_lookup_small_key func()
var S_mock_lookup_small_key uintptr

//go:nosplit
func mock_lookup_small_key() {
    F_mock_lookup_small_key()
}

func init() {
    Mock()
}

func Mock() {
    loader.WrapGoC(_mock_text_f64toa, _cfunc_f64toa, []loader.GoC{{"_f64toa", &S_mock_f64toa, &F_mock_f64toa}}, "{{PACKAGE}}", "{{PACKAGE}}/f64toa.c")
    loader.WrapGoC(_mock_text_f32toa, _cfunc_f32toa, []loader.GoC{{"_f32toa", &S_mock_f32toa, &F_mock_f32toa}}, "{{PACKAGE}}", "{{PACKAGE}}/f32toa.c")
    loader.WrapGoC(_mock_text_get_by_path, _cfunc_get_by_path, []loader.GoC{{"_get_by_path", &S_mock_get_by_path, &F_mock_get_by_path}}, "{{PACKAGE}}", "{{PACKAGE}}/get_by_path.c")
    loader.WrapGoC(_mock_text_html_escape, _cfunc_html_escape, []loader.GoC{{"_html_escape", &S_mock_html_escape, &F_mock_html_escape}}, "{{PACKAGE}}", "{{PACKAGE}}/html_escape.c")
    loader.WrapGoC(_mock_text_i64toa, _cfunc_i64toa, []loader.GoC{{"_i64toa", &S_mock_i64toa, &F_mock_i64toa}}, "{{PACKAGE}}", "{{PACKAGE}}/i64toa.c")
    loader.WrapGoC(_mock_text_lspace, _cfunc_lspace, []loader.GoC{{"_lspace", &S_mock_lspace, &F_mock_lspace}}, "{{PACKAGE}}", "{{PACKAGE}}/lspace.c")
    loader.WrapGoC(_mock_text_quote, _cfunc_quote, []loader.GoC{{"_quote", &S_mock_quote, &F_mock_quote}}, "{{PACKAGE}}", "{{PACKAGE}}/quote.c")
    loader.WrapGoC(_mock_text_skip_array, _cfunc_skip_array, []loader.GoC{{"_skip_array", &S_mock_skip_array, &F_mock_skip_array}}, "{{PACKAGE}}", "{{PACKAGE}}/skip_array.c")
    loader.WrapGoC(_mock_text_skip_number, _cfunc_skip_number, []loader.GoC{{"_skip_number", &S_mock_skip_number, &F_mock_skip_number}}, "{{PACKAGE}}", "{{PACKAGE}}/skip_number.c")
    loader.WrapGoC(_mock_text_skip_one, _cfunc_skip_one, []loader.GoC{{"_skip_one", &S_mock_skip_one, &F_mock_skip_one}}, "{{PACKAGE}}", "{{PACKAGE}}/skip_one.c")
    loader.WrapGoC(_mock_text_skip_object, _cfunc_skip_object, []loader.GoC{{"_skip_object", &S_mock_skip_object, &F_mock_skip_object}}, "{{PACKAGE}}", "{{PACKAGE}}/skip_object.c")
    loader.WrapGoC(_mock_text_skip_one_fast, _cfunc_skip_one_fast, []loader.GoC{{"_skip_one_fast", &S_mock_skip_one_fast, &F_mock_skip_one_fast}}, "{{PACKAGE}}", "{{PACKAGE}}/skip_one_fast.c")
    loader.WrapGoC(_mock_text_u64toa, _cfunc_u64toa, []loader.GoC{{"_u64toa", &S_mock_u64toa, &F_mock_u64toa}}, "{{PACKAGE}}", "{{PACKAGE}}/u64toa.c")
    loader.WrapGoC(_mock_text_unquote, _cfunc_unquote, []loader.GoC{{"_unquote", &S_mock_unquote, &F_mock_unquote}}, "{{PACKAGE}}", "{{PACKAGE}}/unquote.c")
    loader.WrapGoC(_mock_text_validate_one, _cfunc_validate_one, []loader.GoC{{"_validate_one", &S_mock_validate_one, &F_mock_validate_one}}, "{{PACKAGE}}", "{{PACKAGE}}/validate_one.c")
    loader.WrapGoC(_mock_text_validate_utf8, _cfunc_validate_utf8, []loader.GoC{{"_validate_utf8", &S_mock_validate_utf8, &F_mock_validate_utf8}}, "{{PACKAGE}}", "{{PACKAGE}}/validate_utf8.c")
    loader.WrapGoC(_mock_text_validate_utf8_fast, _cfunc_validate_utf8_fast, []loader.GoC{{"_validate_utf8_fast", &S_mock_validate_utf8_fast, &F_mock_validate_utf8_fast}}, "{{PACKAGE}}", "{{PACKAGE}}/validate_utf8_fast.c")
    loader.WrapGoC(_mock_text_vnumber, _cfunc_vnumber, []loader.GoC{{"_vnumber", &S_mock_vnumber, &F_mock_vnumber}}, "{{PACKAGE}}", "{{PACKAGE}}/vnumber.c")
    loader.WrapGoC(_mock_text_vsigned, _cfunc_vsigned, []loader.GoC{{"_vsigned", &S_mock_vsigned, &F_mock_vsigned}}, "{{PACKAGE}}", "{{PACKAGE}}/vsigned.c")
    loader.WrapGoC(_mock_text_vunsigned, _cfunc_vunsigned, []loader.GoC{{"_vunsigned", &S_mock_vunsigned, &F_mock_vunsigned}}, "{{PACKAGE}}", "{{PACKAGE}}/vunsigned.c")
    loader.WrapGoC(_mock_text_vstring, _cfunc_vstring, []loader.GoC{{"_vstring", &S_mock_vstring, &F_mock_vstring}}, "{{PACKAGE}}", "{{PACKAGE}}/vstring.c")
    loader.WrapGoC(_mock_text_value, _cfunc_value, []loader.GoC{{"_value", &S_mock_value, &F_mock_value}}, "{{PACKAGE}}", "{{PACKAGE}}/value.c")
    loader.WrapGoC(_mock_text_parse_with_padding, _cfunc_parse_with_padding, []loader.GoC{{"_parse_with_padding", &S_mock_parse_with_padding, &F_mock_parse_with_padding}}, "{{PACKAGE}}", "{{PACKAGE}}/parse_with_padding.c")
    loader.WrapGoC(_mock_text_lookup_small_key, _cfunc_lookup_small_key, []loader.GoC{{"_lookup_small_key", &S_mock_lookup_small_key, &F_mock_lookup_small_key}}, "{{PACKAGE}}", "{{PACKAGE}}/lookup_small_key.c")
}

func TestTraceback(t *testing.T) {
    tests := []func() {
        mock_lspace,
        mock_f32toa,
        mock_f64toa,
        mock_html_escape,
        mock_i64toa,
        mock_quote,
        mock_skip_array,
        mock_skip_number,
        mock_skip_one,
        mock_get_by_path,
        mock_skip_object,
        mock_skip_one_fast,
        mock_u64toa,
        mock_unquote,
        mock_validate_one,
        mock_validate_utf8,
        mock_validate_utf8_fast,
        mock_vnumber,
        mock_vsigned,
        mock_vunsigned,
        mock_vstring,
        mock_value,
        mock_parse_with_padding,
        mock_lookup_small_key,
    }
    for _, f := range tests {
        f()
    }
}
