// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_skip_one = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 LCPI0_1
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000020 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000030 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000040 LCPI0_2
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000040 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000050 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000060 LCPI0_3
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000060 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000070 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000080 LCPI0_7
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000080 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000090 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x000000a0 LCPI0_8
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000a0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000b0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x000000c0 LCPI0_9
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x000000c0 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x000000d0 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x000000e0 LCPI0_10
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x000000e0 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x000000f0 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000100 LCPI0_11
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000100 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000110 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000120 LCPI0_13
	0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, //0x00000120 QUAD $0x1f1f1f1f1f1f1f1f; QUAD $0x1f1f1f1f1f1f1f1f  // .space 16, '\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f'
	0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, //0x00000130 QUAD $0x1f1f1f1f1f1f1f1f; QUAD $0x1f1f1f1f1f1f1f1f  // .space 16, '\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f'
	//0x00000140 LCPI0_14
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000140 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000150 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000160 LCPI0_15
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000160 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000170 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000180 LCPI0_16
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000180 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000190 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000001a0 LCPI0_17
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x000001a0 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x000001b0 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x000001c0 LCPI0_18
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x000001c0 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x000001d0 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x000001e0 LCPI0_19
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x000001e0 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x000001f0 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x00000200 .p2align 4, 0x00
	//0x00000200 LCPI0_4
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000200 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000210 LCPI0_5
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000210 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000220 LCPI0_6
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000220 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000230 LCPI0_12
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000230 .quad 1
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000238 .quad 0
	//0x00000240 LCPI0_20
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000240 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000250 LCPI0_21
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000250 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000260 LCPI0_22
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x00000260 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x00000270 LCPI0_23
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x00000270 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x00000280 LCPI0_24
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000280 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000290 LCPI0_25
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000290 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x000002a0 .p2align 4, 0x90
	//0x000002a0 _skip_one
	0x55, //0x000002a0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000002a1 movq         %rsp, %rbp
	0x41, 0x57, //0x000002a4 pushq        %r15
	0x41, 0x56, //0x000002a6 pushq        %r14
	0x41, 0x55, //0x000002a8 pushq        %r13
	0x41, 0x54, //0x000002aa pushq        %r12
	0x53, //0x000002ac pushq        %rbx
	0x48, 0x81, 0xec, 0xa0, 0x00, 0x00, 0x00, //0x000002ad subq         $160, %rsp
	0x49, 0x89, 0xcb, //0x000002b4 movq         %rcx, %r11
	0x49, 0x89, 0xf1, //0x000002b7 movq         %rsi, %r9
	0x41, 0xf6, 0xc3, 0x40, //0x000002ba testb        $64, %r11b
	0x48, 0x89, 0x7c, 0x24, 0x20, //0x000002be movq         %rdi, $32(%rsp)
	0x0f, 0x85, 0x7e, 0x00, 0x00, 0x00, //0x000002c3 jne          LBB0_2
	0xc5, 0xfa, 0x6f, 0x05, 0x5f, 0xff, 0xff, 0xff, //0x000002c9 vmovdqu      $-161(%rip), %xmm0  /* LCPI0_12+0(%rip) */
	0xc5, 0xfa, 0x7f, 0x02, //0x000002d1 vmovdqu      %xmm0, (%rdx)
	0x4d, 0x8b, 0x21, //0x000002d5 movq         (%r9), %r12
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000002d8 movq         $-1, %r15
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x000002df movl         $1, %r8d
	0xc5, 0xfe, 0x6f, 0x2d, 0x13, 0xfd, 0xff, 0xff, //0x000002e5 vmovdqu      $-749(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x8b, 0xfd, 0xff, 0xff, //0x000002ed vmovdqu      $-629(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xa3, 0xfd, 0xff, 0xff, //0x000002f5 vmovdqu      $-605(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x1b, 0xfe, 0xff, 0xff, //0x000002fd vmovdqu      $-485(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x33, 0xfe, 0xff, 0xff, //0x00000305 vmovdqu      $-461(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x4b, 0xfe, 0xff, 0xff, //0x0000030d vmovdqu      $-437(%rip), %ymm10  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x23, 0xfd, 0xff, 0xff, //0x00000315 vmovdqu      $-733(%rip), %ymm11  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x5b, 0xfe, 0xff, 0xff, //0x0000031d vmovdqu      $-421(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x73, 0xfe, 0xff, 0xff, //0x00000325 vmovdqu      $-397(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x8b, 0xfe, 0xff, 0xff, //0x0000032d vmovdqu      $-373(%rip), %ymm14  /* LCPI0_18+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xa3, 0xfe, 0xff, 0xff, //0x00000335 vmovdqu      $-349(%rip), %ymm15  /* LCPI0_19+0(%rip) */
	0x48, 0x89, 0x54, 0x24, 0x28, //0x0000033d movq         %rdx, $40(%rsp)
	0xe9, 0xd9, 0x01, 0x00, 0x00, //0x00000342 jmp          LBB0_37
	//0x00000347 LBB0_2
	0x4c, 0x8b, 0x1f, //0x00000347 movq         (%rdi), %r11
	0x48, 0x8b, 0x5f, 0x08, //0x0000034a movq         $8(%rdi), %rbx
	0x49, 0x8b, 0x31, //0x0000034e movq         (%r9), %rsi
	0x48, 0x39, 0xde, //0x00000351 cmpq         %rbx, %rsi
	0x0f, 0x83, 0x26, 0x00, 0x00, 0x00, //0x00000354 jae          LBB0_7
	0x41, 0x8a, 0x04, 0x33, //0x0000035a movb         (%r11,%rsi), %al
	0x3c, 0x0d, //0x0000035e cmpb         $13, %al
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00000360 je           LBB0_7
	0x3c, 0x20, //0x00000366 cmpb         $32, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00000368 je           LBB0_7
	0x04, 0xf5, //0x0000036e addb         $-11, %al
	0x3c, 0xfe, //0x00000370 cmpb         $-2, %al
	0x0f, 0x83, 0x08, 0x00, 0x00, 0x00, //0x00000372 jae          LBB0_7
	0x48, 0x89, 0xf0, //0x00000378 movq         %rsi, %rax
	0xe9, 0xa5, 0x24, 0x00, 0x00, //0x0000037b jmp          LBB0_533
	//0x00000380 LBB0_7
	0x48, 0x8d, 0x46, 0x01, //0x00000380 leaq         $1(%rsi), %rax
	0x48, 0x39, 0xd8, //0x00000384 cmpq         %rbx, %rax
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000387 jae          LBB0_11
	0x41, 0x8a, 0x0c, 0x03, //0x0000038d movb         (%r11,%rax), %cl
	0x80, 0xf9, 0x0d, //0x00000391 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000394 je           LBB0_11
	0x80, 0xf9, 0x20, //0x0000039a cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000039d je           LBB0_11
	0x80, 0xc1, 0xf5, //0x000003a3 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x000003a6 cmpb         $-2, %cl
	0x0f, 0x82, 0x76, 0x24, 0x00, 0x00, //0x000003a9 jb           LBB0_533
	//0x000003af LBB0_11
	0x48, 0x8d, 0x46, 0x02, //0x000003af leaq         $2(%rsi), %rax
	0x48, 0x39, 0xd8, //0x000003b3 cmpq         %rbx, %rax
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000003b6 jae          LBB0_15
	0x41, 0x8a, 0x0c, 0x03, //0x000003bc movb         (%r11,%rax), %cl
	0x80, 0xf9, 0x0d, //0x000003c0 cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000003c3 je           LBB0_15
	0x80, 0xf9, 0x20, //0x000003c9 cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000003cc je           LBB0_15
	0x80, 0xc1, 0xf5, //0x000003d2 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x000003d5 cmpb         $-2, %cl
	0x0f, 0x82, 0x47, 0x24, 0x00, 0x00, //0x000003d8 jb           LBB0_533
	//0x000003de LBB0_15
	0x48, 0x8d, 0x46, 0x03, //0x000003de leaq         $3(%rsi), %rax
	0x48, 0x39, 0xd8, //0x000003e2 cmpq         %rbx, %rax
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000003e5 jae          LBB0_19
	0x41, 0x8a, 0x0c, 0x03, //0x000003eb movb         (%r11,%rax), %cl
	0x80, 0xf9, 0x0d, //0x000003ef cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000003f2 je           LBB0_19
	0x80, 0xf9, 0x20, //0x000003f8 cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000003fb je           LBB0_19
	0x80, 0xc1, 0xf5, //0x00000401 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000404 cmpb         $-2, %cl
	0x0f, 0x82, 0x18, 0x24, 0x00, 0x00, //0x00000407 jb           LBB0_533
	//0x0000040d LBB0_19
	0x48, 0x8d, 0x46, 0x04, //0x0000040d leaq         $4(%rsi), %rax
	0x48, 0x89, 0xda, //0x00000411 movq         %rbx, %rdx
	0x48, 0x29, 0xc2, //0x00000414 subq         %rax, %rdx
	0x0f, 0x86, 0xd9, 0x23, 0x00, 0x00, //0x00000417 jbe          LBB0_531
	0x48, 0x83, 0xfa, 0x20, //0x0000041d cmpq         $32, %rdx
	0x0f, 0x82, 0xa4, 0x2e, 0x00, 0x00, //0x00000421 jb           LBB0_652
	0x48, 0xc7, 0xc2, 0xfc, 0xff, 0xff, 0xff, //0x00000427 movq         $-4, %rdx
	0x48, 0x29, 0xf2, //0x0000042e subq         %rsi, %rdx
	0xc5, 0xfe, 0x6f, 0x05, 0xc7, 0xfb, 0xff, 0xff, //0x00000431 vmovdqu      $-1081(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000439 .p2align 4, 0x90
	//0x00000440 LBB0_22
	0xc4, 0xc1, 0x7e, 0x6f, 0x0c, 0x03, //0x00000440 vmovdqu      (%r11,%rax), %ymm1
	0xc4, 0xe2, 0x7d, 0x00, 0xd1, //0x00000446 vpshufb      %ymm1, %ymm0, %ymm2
	0xc5, 0xf5, 0xf8, 0xda, //0x0000044b vpsubb       %ymm2, %ymm1, %ymm3
	0xc4, 0xe2, 0x7d, 0x17, 0xdb, //0x0000044f vptest       %ymm3, %ymm3
	0x0f, 0x85, 0xab, 0x23, 0x00, 0x00, //0x00000454 jne          LBB0_532
	0x48, 0x83, 0xc0, 0x20, //0x0000045a addq         $32, %rax
	0x48, 0x8d, 0x0c, 0x13, //0x0000045e leaq         (%rbx,%rdx), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00000462 addq         $-32, %rcx
	0x48, 0x83, 0xc2, 0xe0, //0x00000466 addq         $-32, %rdx
	0x48, 0x83, 0xf9, 0x1f, //0x0000046a cmpq         $31, %rcx
	0x0f, 0x87, 0xcc, 0xff, 0xff, 0xff, //0x0000046e ja           LBB0_22
	0x4c, 0x89, 0xd8, //0x00000474 movq         %r11, %rax
	0x48, 0x29, 0xd0, //0x00000477 subq         %rdx, %rax
	0x48, 0x01, 0xda, //0x0000047a addq         %rbx, %rdx
	0x48, 0x85, 0xd2, //0x0000047d testq        %rdx, %rdx
	0x0f, 0x84, 0x37, 0x00, 0x00, 0x00, //0x00000480 je           LBB0_30
	//0x00000486 LBB0_25
	0x4c, 0x8d, 0x04, 0x10, //0x00000486 leaq         (%rax,%rdx), %r8
	0x31, 0xf6, //0x0000048a xorl         %esi, %esi
	0x49, 0xba, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000048c movabsq      $4294977024, %r10
	//0x00000496 LBB0_26
	0x0f, 0xbe, 0x0c, 0x30, //0x00000496 movsbl       (%rax,%rsi), %ecx
	0x83, 0xf9, 0x20, //0x0000049a cmpl         $32, %ecx
	0x0f, 0x87, 0xe0, 0x2d, 0x00, 0x00, //0x0000049d ja           LBB0_644
	0x49, 0x0f, 0xa3, 0xca, //0x000004a3 btq          %rcx, %r10
	0x0f, 0x83, 0xd6, 0x2d, 0x00, 0x00, //0x000004a7 jae          LBB0_644
	0x48, 0x83, 0xc6, 0x01, //0x000004ad addq         $1, %rsi
	0x48, 0x39, 0xf2, //0x000004b1 cmpq         %rsi, %rdx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000004b4 jne          LBB0_26
	0x4c, 0x89, 0xc0, //0x000004ba movq         %r8, %rax
	//0x000004bd LBB0_30
	0x4c, 0x29, 0xd8, //0x000004bd subq         %r11, %rax
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000004c0 movq         $-1, %r14
	0x48, 0x39, 0xd8, //0x000004c7 cmpq         %rbx, %rax
	0x0f, 0x82, 0x55, 0x23, 0x00, 0x00, //0x000004ca jb           LBB0_533
	0xe9, 0x6a, 0x2c, 0x00, 0x00, //0x000004d0 jmp          LBB0_650
	//0x000004d5 LBB0_31
	0x4c, 0x0f, 0xbc, 0xe7, //0x000004d5 bsfq         %rdi, %r12
	0x49, 0x29, 0xf4, //0x000004d9 subq         %rsi, %r12
	//0x000004dc LBB0_32
	0x4d, 0x89, 0xf9, //0x000004dc movq         %r15, %r9
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x000004df movq         $32(%rsp), %rdi
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x000004e4 movq         $40(%rsp), %rdx
	0x4d, 0x89, 0xf7, //0x000004e9 movq         %r14, %r15
	//0x000004ec LBB0_33
	0x4d, 0x85, 0xe4, //0x000004ec testq        %r12, %r12
	0x0f, 0x88, 0xc6, 0x24, 0x00, 0x00, //0x000004ef js           LBB0_495
	//0x000004f5 LBB0_34
	0x4d, 0x89, 0x21, //0x000004f5 movq         %r12, (%r9)
	0x4d, 0x89, 0xd6, //0x000004f8 movq         %r10, %r14
	0x48, 0xb8, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x000004fb movabsq      $9223372036854775806, %rax
	0x49, 0x39, 0xc2, //0x00000505 cmpq         %rax, %r10
	0x0f, 0x87, 0x31, 0x2c, 0x00, 0x00, //0x00000508 ja           LBB0_650
	//0x0000050e LBB0_35
	0x48, 0x8b, 0x32, //0x0000050e movq         (%rdx), %rsi
	0x49, 0x89, 0xf0, //0x00000511 movq         %rsi, %r8
	0x4d, 0x89, 0xfe, //0x00000514 movq         %r15, %r14
	0x48, 0x85, 0xf6, //0x00000517 testq        %rsi, %rsi
	0x0f, 0x84, 0x1f, 0x2c, 0x00, 0x00, //0x0000051a je           LBB0_650
	//0x00000520 LBB0_37
	0x4c, 0x8b, 0x2f, //0x00000520 movq         (%rdi), %r13
	0x48, 0x8b, 0x5f, 0x08, //0x00000523 movq         $8(%rdi), %rbx
	0x49, 0x39, 0xdc, //0x00000527 cmpq         %rbx, %r12
	0x0f, 0x83, 0x30, 0x00, 0x00, 0x00, //0x0000052a jae          LBB0_42
	0x43, 0x8a, 0x44, 0x25, 0x00, //0x00000530 movb         (%r13,%r12), %al
	0x3c, 0x0d, //0x00000535 cmpb         $13, %al
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x00000537 je           LBB0_42
	0x3c, 0x20, //0x0000053d cmpb         $32, %al
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x0000053f je           LBB0_42
	0x04, 0xf5, //0x00000545 addb         $-11, %al
	0x3c, 0xfe, //0x00000547 cmpb         $-2, %al
	0x0f, 0x83, 0x11, 0x00, 0x00, 0x00, //0x00000549 jae          LBB0_42
	0x4d, 0x89, 0xe2, //0x0000054f movq         %r12, %r10
	0xe9, 0x83, 0x01, 0x00, 0x00, //0x00000552 jmp          LBB0_67
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000557 .p2align 4, 0x90
	//0x00000560 LBB0_42
	0x4d, 0x8d, 0x54, 0x24, 0x01, //0x00000560 leaq         $1(%r12), %r10
	0x49, 0x39, 0xda, //0x00000565 cmpq         %rbx, %r10
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000568 jae          LBB0_46
	0x43, 0x8a, 0x44, 0x15, 0x00, //0x0000056e movb         (%r13,%r10), %al
	0x3c, 0x0d, //0x00000573 cmpb         $13, %al
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000575 je           LBB0_46
	0x3c, 0x20, //0x0000057b cmpb         $32, %al
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000057d je           LBB0_46
	0x04, 0xf5, //0x00000583 addb         $-11, %al
	0x3c, 0xfe, //0x00000585 cmpb         $-2, %al
	0x0f, 0x82, 0x4d, 0x01, 0x00, 0x00, //0x00000587 jb           LBB0_67
	0x90, 0x90, 0x90, //0x0000058d .p2align 4, 0x90
	//0x00000590 LBB0_46
	0x4d, 0x8d, 0x54, 0x24, 0x02, //0x00000590 leaq         $2(%r12), %r10
	0x49, 0x39, 0xda, //0x00000595 cmpq         %rbx, %r10
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000598 jae          LBB0_50
	0x43, 0x8a, 0x44, 0x15, 0x00, //0x0000059e movb         (%r13,%r10), %al
	0x3c, 0x0d, //0x000005a3 cmpb         $13, %al
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000005a5 je           LBB0_50
	0x3c, 0x20, //0x000005ab cmpb         $32, %al
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000005ad je           LBB0_50
	0x04, 0xf5, //0x000005b3 addb         $-11, %al
	0x3c, 0xfe, //0x000005b5 cmpb         $-2, %al
	0x0f, 0x82, 0x1d, 0x01, 0x00, 0x00, //0x000005b7 jb           LBB0_67
	0x90, 0x90, 0x90, //0x000005bd .p2align 4, 0x90
	//0x000005c0 LBB0_50
	0x4d, 0x8d, 0x54, 0x24, 0x03, //0x000005c0 leaq         $3(%r12), %r10
	0x49, 0x39, 0xda, //0x000005c5 cmpq         %rbx, %r10
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000005c8 jae          LBB0_54
	0x43, 0x8a, 0x44, 0x15, 0x00, //0x000005ce movb         (%r13,%r10), %al
	0x3c, 0x0d, //0x000005d3 cmpb         $13, %al
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000005d5 je           LBB0_54
	0x3c, 0x20, //0x000005db cmpb         $32, %al
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000005dd je           LBB0_54
	0x04, 0xf5, //0x000005e3 addb         $-11, %al
	0x3c, 0xfe, //0x000005e5 cmpb         $-2, %al
	0x0f, 0x82, 0xed, 0x00, 0x00, 0x00, //0x000005e7 jb           LBB0_67
	0x90, 0x90, 0x90, //0x000005ed .p2align 4, 0x90
	//0x000005f0 LBB0_54
	0x4d, 0x8d, 0x54, 0x24, 0x04, //0x000005f0 leaq         $4(%r12), %r10
	0x48, 0x89, 0xde, //0x000005f5 movq         %rbx, %rsi
	0x4c, 0x29, 0xd6, //0x000005f8 subq         %r10, %rsi
	0x0f, 0x86, 0x8c, 0x23, 0x00, 0x00, //0x000005fb jbe          LBB0_553
	0x48, 0x83, 0xfe, 0x20, //0x00000601 cmpq         $32, %rsi
	0x0f, 0x82, 0x24, 0x17, 0x00, 0x00, //0x00000605 jb           LBB0_394
	0x48, 0xc7, 0xc6, 0xfc, 0xff, 0xff, 0xff, //0x0000060b movq         $-4, %rsi
	0x4c, 0x29, 0xe6, //0x00000612 subq         %r12, %rsi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000615 .p2align 4, 0x90
	//0x00000620 LBB0_57
	0xc4, 0x81, 0x7e, 0x6f, 0x44, 0x15, 0x00, //0x00000620 vmovdqu      (%r13,%r10), %ymm0
	0xc4, 0xe2, 0x55, 0x00, 0xc8, //0x00000627 vpshufb      %ymm0, %ymm5, %ymm1
	0xc5, 0xfd, 0xf8, 0xd1, //0x0000062c vpsubb       %ymm1, %ymm0, %ymm2
	0xc4, 0xe2, 0x7d, 0x17, 0xd2, //0x00000630 vptest       %ymm2, %ymm2
	0x0f, 0x85, 0x85, 0x00, 0x00, 0x00, //0x00000635 jne          LBB0_66
	0x49, 0x83, 0xc2, 0x20, //0x0000063b addq         $32, %r10
	0x48, 0x8d, 0x04, 0x33, //0x0000063f leaq         (%rbx,%rsi), %rax
	0x48, 0x83, 0xc0, 0xe0, //0x00000643 addq         $-32, %rax
	0x48, 0x83, 0xc6, 0xe0, //0x00000647 addq         $-32, %rsi
	0x48, 0x83, 0xf8, 0x1f, //0x0000064b cmpq         $31, %rax
	0x0f, 0x87, 0xcb, 0xff, 0xff, 0xff, //0x0000064f ja           LBB0_57
	0x4d, 0x89, 0xea, //0x00000655 movq         %r13, %r10
	0x49, 0x29, 0xf2, //0x00000658 subq         %rsi, %r10
	0x48, 0x01, 0xde, //0x0000065b addq         %rbx, %rsi
	0x48, 0x85, 0xf6, //0x0000065e testq        %rsi, %rsi
	0x0f, 0x84, 0x3c, 0x00, 0x00, 0x00, //0x00000661 je           LBB0_65
	//0x00000667 LBB0_60
	0x4d, 0x8d, 0x34, 0x32, //0x00000667 leaq         (%r10,%rsi), %r14
	0x31, 0xc9, //0x0000066b xorl         %ecx, %ecx
	0x90, 0x90, 0x90, //0x0000066d .p2align 4, 0x90
	//0x00000670 LBB0_61
	0x45, 0x0f, 0xbe, 0x24, 0x0a, //0x00000670 movsbl       (%r10,%rcx), %r12d
	0x41, 0x83, 0xfc, 0x20, //0x00000675 cmpl         $32, %r12d
	0x0f, 0x87, 0xbe, 0x14, 0x00, 0x00, //0x00000679 ja           LBB0_382
	0x48, 0xb8, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000067f movabsq      $4294977024, %rax
	0x4c, 0x0f, 0xa3, 0xe0, //0x00000689 btq          %r12, %rax
	0x0f, 0x83, 0xaa, 0x14, 0x00, 0x00, //0x0000068d jae          LBB0_382
	0x48, 0x83, 0xc1, 0x01, //0x00000693 addq         $1, %rcx
	0x48, 0x39, 0xce, //0x00000697 cmpq         %rcx, %rsi
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x0000069a jne          LBB0_61
	0x4d, 0x89, 0xf2, //0x000006a0 movq         %r14, %r10
	//0x000006a3 LBB0_65
	0x4d, 0x29, 0xea, //0x000006a3 subq         %r13, %r10
	0x49, 0x39, 0xda, //0x000006a6 cmpq         %rbx, %r10
	0x0f, 0x82, 0x2b, 0x00, 0x00, 0x00, //0x000006a9 jb           LBB0_67
	0xe9, 0xdc, 0x22, 0x00, 0x00, //0x000006af jmp          LBB0_554
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000006b4 .p2align 4, 0x90
	//0x000006c0 LBB0_66
	0xc5, 0xfd, 0x74, 0xc1, //0x000006c0 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000006c4 vpmovmskb    %ymm0, %eax
	0xf7, 0xd0, //0x000006c8 notl         %eax
	0x44, 0x0f, 0xbc, 0xd0, //0x000006ca bsfl         %eax, %r10d
	0x49, 0x29, 0xf2, //0x000006ce subq         %rsi, %r10
	0x49, 0x39, 0xda, //0x000006d1 cmpq         %rbx, %r10
	0x0f, 0x83, 0xb6, 0x22, 0x00, 0x00, //0x000006d4 jae          LBB0_554
	//0x000006da LBB0_67
	0x4d, 0x8d, 0x62, 0x01, //0x000006da leaq         $1(%r10), %r12
	0x4d, 0x89, 0x21, //0x000006de movq         %r12, (%r9)
	0x43, 0x0f, 0xbe, 0x4c, 0x15, 0x00, //0x000006e1 movsbl       (%r13,%r10), %ecx
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000006e7 movq         $-1, %r14
	0x85, 0xc9, //0x000006ee testl        %ecx, %ecx
	0x0f, 0x84, 0x49, 0x2a, 0x00, 0x00, //0x000006f0 je           LBB0_650
	0x49, 0x8d, 0x70, 0xff, //0x000006f6 leaq         $-1(%r8), %rsi
	0x42, 0x8b, 0x04, 0xc2, //0x000006fa movl         (%rdx,%r8,8), %eax
	0x49, 0x83, 0xff, 0xff, //0x000006fe cmpq         $-1, %r15
	0x4d, 0x0f, 0x44, 0xfa, //0x00000702 cmoveq       %r10, %r15
	0x83, 0xc0, 0xff, //0x00000706 addl         $-1, %eax
	0x83, 0xf8, 0x05, //0x00000709 cmpl         $5, %eax
	0x0f, 0x87, 0xd4, 0x01, 0x00, 0x00, //0x0000070c ja           LBB0_98
	0x48, 0x8d, 0x1d, 0x53, 0x2e, 0x00, 0x00, //0x00000712 leaq         $11859(%rip), %rbx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x04, 0x83, //0x00000719 movslq       (%rbx,%rax,4), %rax
	0x48, 0x01, 0xd8, //0x0000071d addq         %rbx, %rax
	0xff, 0xe0, //0x00000720 jmpq         *%rax
	//0x00000722 LBB0_70
	0x83, 0xf9, 0x2c, //0x00000722 cmpl         $44, %ecx
	0x0f, 0x84, 0xb0, 0x05, 0x00, 0x00, //0x00000725 je           LBB0_168
	0x83, 0xf9, 0x5d, //0x0000072b cmpl         $93, %ecx
	0x0f, 0x84, 0x90, 0x05, 0x00, 0x00, //0x0000072e je           LBB0_72
	0xe9, 0x8a, 0x29, 0x00, 0x00, //0x00000734 jmp          LBB0_623
	//0x00000739 LBB0_73
	0x80, 0xf9, 0x5d, //0x00000739 cmpb         $93, %cl
	0x0f, 0x84, 0x82, 0x05, 0x00, 0x00, //0x0000073c je           LBB0_72
	0x4a, 0xc7, 0x04, 0xc2, 0x01, 0x00, 0x00, 0x00, //0x00000742 movq         $1, (%rdx,%r8,8)
	0x83, 0xf9, 0x7b, //0x0000074a cmpl         $123, %ecx
	0x0f, 0x86, 0x9f, 0x01, 0x00, 0x00, //0x0000074d jbe          LBB0_75
	0xe9, 0x6b, 0x29, 0x00, 0x00, //0x00000753 jmp          LBB0_623
	//0x00000758 LBB0_76
	0x80, 0xf9, 0x22, //0x00000758 cmpb         $34, %cl
	0x0f, 0x85, 0x62, 0x29, 0x00, 0x00, //0x0000075b jne          LBB0_623
	0x4a, 0xc7, 0x04, 0xc2, 0x04, 0x00, 0x00, 0x00, //0x00000761 movq         $4, (%rdx,%r8,8)
	0x4c, 0x8b, 0x47, 0x08, //0x00000769 movq         $8(%rdi), %r8
	0x4c, 0x89, 0xc3, //0x0000076d movq         %r8, %rbx
	0x41, 0xf6, 0xc3, 0x20, //0x00000770 testb        $32, %r11b
	0x0f, 0x85, 0xeb, 0x06, 0x00, 0x00, //0x00000774 jne          LBB0_178
	0x4c, 0x29, 0xe3, //0x0000077a subq         %r12, %rbx
	0x0f, 0x84, 0x1b, 0x2b, 0x00, 0x00, //0x0000077d je           LBB0_656
	0x4d, 0x89, 0xfe, //0x00000783 movq         %r15, %r14
	0x4d, 0x89, 0xcf, //0x00000786 movq         %r9, %r15
	0x48, 0x83, 0xfb, 0x40, //0x00000789 cmpq         $64, %rbx
	0x0f, 0x82, 0x63, 0x17, 0x00, 0x00, //0x0000078d jb           LBB0_410
	0x48, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x00000793 movq         $-2, %rsi
	0x4c, 0x29, 0xd6, //0x0000079a subq         %r10, %rsi
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x0000079d movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc9, //0x000007a6 xorl         %r9d, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000007a9 .p2align 4, 0x90
	//0x000007b0 LBB0_81
	0xc4, 0x81, 0x7e, 0x6f, 0x44, 0x25, 0x00, //0x000007b0 vmovdqu      (%r13,%r12), %ymm0
	0xc4, 0x81, 0x7e, 0x6f, 0x4c, 0x25, 0x20, //0x000007b7 vmovdqu      $32(%r13,%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000007be vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000007c2 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x000007c6 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x000007ca vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x000007ce vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x000007d2 vpmovmskb    %ymm0, %edx
	0xc5, 0xf5, 0x74, 0xc7, //0x000007d6 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x000007da vpmovmskb    %ymm0, %ecx
	0x48, 0xc1, 0xe0, 0x20, //0x000007de shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x000007e2 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x000007e5 shlq         $32, %rcx
	0x48, 0x09, 0xca, //0x000007e9 orq          %rcx, %rdx
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x000007ec jne          LBB0_90
	0x4d, 0x85, 0xc9, //0x000007f2 testq        %r9, %r9
	0x0f, 0x85, 0x49, 0x00, 0x00, 0x00, //0x000007f5 jne          LBB0_92
	0x45, 0x31, 0xc9, //0x000007fb xorl         %r9d, %r9d
	0x48, 0x85, 0xff, //0x000007fe testq        %rdi, %rdi
	0x0f, 0x85, 0xce, 0xfc, 0xff, 0xff, //0x00000801 jne          LBB0_31
	//0x00000807 LBB0_84
	0x48, 0x83, 0xc3, 0xc0, //0x00000807 addq         $-64, %rbx
	0x48, 0x83, 0xc6, 0xc0, //0x0000080b addq         $-64, %rsi
	0x49, 0x83, 0xc4, 0x40, //0x0000080f addq         $64, %r12
	0x48, 0x83, 0xfb, 0x3f, //0x00000813 cmpq         $63, %rbx
	0x0f, 0x87, 0x93, 0xff, 0xff, 0xff, //0x00000817 ja           LBB0_81
	0xe9, 0x3c, 0x13, 0x00, 0x00, //0x0000081d jmp          LBB0_85
	//0x00000822 LBB0_90
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00000822 movq         %r11, $24(%rsp)
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00000827 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x0000082d jne          LBB0_93
	0x48, 0x0f, 0xbc, 0xc2, //0x00000833 bsfq         %rdx, %rax
	0x4c, 0x01, 0xe0, //0x00000837 addq         %r12, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x0000083a movq         %rax, $16(%rsp)
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x0000083f jmp          LBB0_93
	//0x00000844 LBB0_92
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00000844 movq         %r11, $24(%rsp)
	//0x00000849 LBB0_93
	0x4c, 0x89, 0xc8, //0x00000849 movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x0000084c notq         %rax
	0x48, 0x21, 0xd0, //0x0000084f andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x00000852 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xcb, //0x00000856 orq          %r9, %r11
	0x4c, 0x89, 0xd9, //0x00000859 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x0000085c notq         %rcx
	0x48, 0x21, 0xd1, //0x0000085f andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000862 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x0000086c andq         %rdx, %rcx
	0x45, 0x31, 0xc9, //0x0000086f xorl         %r9d, %r9d
	0x48, 0x01, 0xc1, //0x00000872 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc1, //0x00000875 setb         %r9b
	0x48, 0x01, 0xc9, //0x00000879 addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000087c movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x00000886 xorq         %rax, %rcx
	0x4c, 0x21, 0xd9, //0x00000889 andq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x0000088c notq         %rcx
	0x48, 0x21, 0xcf, //0x0000088f andq         %rcx, %rdi
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00000892 movq         $24(%rsp), %r11
	0x48, 0x85, 0xff, //0x00000897 testq        %rdi, %rdi
	0x0f, 0x84, 0x67, 0xff, 0xff, 0xff, //0x0000089a je           LBB0_84
	0xe9, 0x30, 0xfc, 0xff, 0xff, //0x000008a0 jmp          LBB0_31
	//0x000008a5 LBB0_94
	0x80, 0xf9, 0x3a, //0x000008a5 cmpb         $58, %cl
	0x0f, 0x85, 0x15, 0x28, 0x00, 0x00, //0x000008a8 jne          LBB0_623
	0x4a, 0xc7, 0x04, 0xc2, 0x00, 0x00, 0x00, 0x00, //0x000008ae movq         $0, (%rdx,%r8,8)
	0xe9, 0x53, 0xfc, 0xff, 0xff, //0x000008b6 jmp          LBB0_35
	//0x000008bb LBB0_96
	0x83, 0xf9, 0x2c, //0x000008bb cmpl         $44, %ecx
	0x0f, 0x85, 0xf7, 0x03, 0x00, 0x00, //0x000008be jne          LBB0_97
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x000008c4 cmpq         $4095, %r8
	0x0f, 0x8f, 0xde, 0x20, 0x00, 0x00, //0x000008cb jg           LBB0_631
	0x49, 0x8d, 0x40, 0x01, //0x000008d1 leaq         $1(%r8), %rax
	0x48, 0x89, 0x02, //0x000008d5 movq         %rax, (%rdx)
	0x4a, 0xc7, 0x44, 0xc2, 0x08, 0x03, 0x00, 0x00, 0x00, //0x000008d8 movq         $3, $8(%rdx,%r8,8)
	0xe9, 0x28, 0xfc, 0xff, 0xff, //0x000008e1 jmp          LBB0_35
	//0x000008e6 LBB0_98
	0x48, 0x89, 0x32, //0x000008e6 movq         %rsi, (%rdx)
	0x83, 0xf9, 0x7b, //0x000008e9 cmpl         $123, %ecx
	0x0f, 0x87, 0xd1, 0x27, 0x00, 0x00, //0x000008ec ja           LBB0_623
	//0x000008f2 LBB0_75
	0x4b, 0x8d, 0x04, 0x2a, //0x000008f2 leaq         (%r10,%r13), %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x000008f6 movq         %rax, $16(%rsp)
	0x89, 0xc8, //0x000008fb movl         %ecx, %eax
	0x48, 0x8d, 0x0d, 0x80, 0x2c, 0x00, 0x00, //0x000008fd leaq         $11392(%rip), %rcx  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x04, 0x81, //0x00000904 movslq       (%rcx,%rax,4), %rax
	0x48, 0x01, 0xc8, //0x00000908 addq         %rcx, %rax
	0xff, 0xe0, //0x0000090b jmpq         *%rax
	//0x0000090d LBB0_113
	0x48, 0x8b, 0x5f, 0x08, //0x0000090d movq         $8(%rdi), %rbx
	0x4c, 0x29, 0xd3, //0x00000911 subq         %r10, %rbx
	0x0f, 0x84, 0x83, 0x27, 0x00, 0x00, //0x00000914 je           LBB0_620
	0x4c, 0x89, 0x7c, 0x24, 0x38, //0x0000091a movq         %r15, $56(%rsp)
	0x4c, 0x8b, 0x74, 0x24, 0x10, //0x0000091f movq         $16(%rsp), %r14
	0x41, 0x80, 0x3e, 0x30, //0x00000924 cmpb         $48, (%r14)
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x00000928 jne          LBB0_118
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x0000092e movl         $1, %r15d
	0x48, 0x83, 0xfb, 0x01, //0x00000934 cmpq         $1, %rbx
	0x0f, 0x84, 0x1e, 0x13, 0x00, 0x00, //0x00000938 je           LBB0_392
	0x43, 0x8a, 0x44, 0x25, 0x00, //0x0000093e movb         (%r13,%r12), %al
	0x04, 0xd2, //0x00000943 addb         $-46, %al
	0x3c, 0x37, //0x00000945 cmpb         $55, %al
	0x0f, 0x87, 0x0f, 0x13, 0x00, 0x00, //0x00000947 ja           LBB0_392
	0x0f, 0xb6, 0xc0, //0x0000094d movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000950 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x0000095a btq          %rax, %rcx
	0x0f, 0x83, 0xf8, 0x12, 0x00, 0x00, //0x0000095e jae          LBB0_392
	//0x00000964 LBB0_118
	0x4c, 0x89, 0x4c, 0x24, 0x30, //0x00000964 movq         %r9, $48(%rsp)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00000969 movq         $-1, %r12
	0x48, 0x83, 0xfb, 0x20, //0x00000970 cmpq         $32, %rbx
	0x0f, 0x82, 0x57, 0x15, 0x00, 0x00, //0x00000974 jb           LBB0_409
	0x45, 0x31, 0xff, //0x0000097a xorl         %r15d, %r15d
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000097d movq         $-1, %r9
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000984 movq         $-1, %r8
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000098b .p2align 4, 0x90
	//0x00000990 LBB0_120
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x3e, //0x00000990 vmovdqu      (%r14,%r15), %ymm0
	0xc5, 0xb5, 0x74, 0xc8, //0x00000996 vpcmpeqb     %ymm0, %ymm9, %ymm1
	0xc5, 0xad, 0x74, 0xd0, //0x0000099a vpcmpeqb     %ymm0, %ymm10, %ymm2
	0xc5, 0xed, 0xeb, 0xc9, //0x0000099e vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xa5, 0xdb, 0xd0, //0x000009a2 vpand        %ymm0, %ymm11, %ymm2
	0xc5, 0x9d, 0x74, 0xd8, //0x000009a6 vpcmpeqb     %ymm0, %ymm12, %ymm3
	0xc5, 0xfd, 0xd7, 0xd3, //0x000009aa vpmovmskb    %ymm3, %edx
	0xc5, 0x95, 0x74, 0xd2, //0x000009ae vpcmpeqb     %ymm2, %ymm13, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000009b2 vpmovmskb    %ymm2, %edi
	0xc5, 0xfd, 0xd7, 0xf1, //0x000009b6 vpmovmskb    %ymm1, %esi
	0xc5, 0x8d, 0xfc, 0xc0, //0x000009ba vpaddb       %ymm0, %ymm14, %ymm0
	0xc5, 0x85, 0xda, 0xe0, //0x000009be vpminub      %ymm0, %ymm15, %ymm4
	0xc5, 0xfd, 0x74, 0xc4, //0x000009c2 vpcmpeqb     %ymm4, %ymm0, %ymm0
	0xc5, 0xe5, 0xeb, 0xd2, //0x000009c6 vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0xfd, 0xeb, 0xc2, //0x000009ca vpor         %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x000009ce vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000009d2 vpmovmskb    %ymm0, %eax
	0x48, 0xf7, 0xd0, //0x000009d6 notq         %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x000009d9 bsfq         %rax, %rcx
	0x83, 0xf9, 0x20, //0x000009dd cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000009e0 je           LBB0_122
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000009e6 movl         $-1, %eax
	0xd3, 0xe0, //0x000009eb shll         %cl, %eax
	0xf7, 0xd0, //0x000009ed notl         %eax
	0x21, 0xc2, //0x000009ef andl         %eax, %edx
	0x21, 0xc7, //0x000009f1 andl         %eax, %edi
	0x21, 0xf0, //0x000009f3 andl         %esi, %eax
	0x89, 0xc6, //0x000009f5 movl         %eax, %esi
	//0x000009f7 LBB0_122
	0x8d, 0x42, 0xff, //0x000009f7 leal         $-1(%rdx), %eax
	0x21, 0xd0, //0x000009fa andl         %edx, %eax
	0x0f, 0x85, 0xd4, 0x0f, 0x00, 0x00, //0x000009fc jne          LBB0_364
	0x8d, 0x47, 0xff, //0x00000a02 leal         $-1(%rdi), %eax
	0x21, 0xf8, //0x00000a05 andl         %edi, %eax
	0x0f, 0x85, 0xc9, 0x0f, 0x00, 0x00, //0x00000a07 jne          LBB0_364
	0x8d, 0x46, 0xff, //0x00000a0d leal         $-1(%rsi), %eax
	0x21, 0xf0, //0x00000a10 andl         %esi, %eax
	0x0f, 0x85, 0xbe, 0x0f, 0x00, 0x00, //0x00000a12 jne          LBB0_364
	0x85, 0xd2, //0x00000a18 testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000a1a je           LBB0_128
	0x0f, 0xbc, 0xd2, //0x00000a20 bsfl         %edx, %edx
	0x49, 0x83, 0xf8, 0xff, //0x00000a23 cmpq         $-1, %r8
	0x0f, 0x85, 0x24, 0x11, 0x00, 0x00, //0x00000a27 jne          LBB0_383
	0x4c, 0x01, 0xfa, //0x00000a2d addq         %r15, %rdx
	0x49, 0x89, 0xd0, //0x00000a30 movq         %rdx, %r8
	//0x00000a33 LBB0_128
	0x85, 0xff, //0x00000a33 testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000a35 je           LBB0_131
	0x0f, 0xbc, 0xd7, //0x00000a3b bsfl         %edi, %edx
	0x49, 0x83, 0xf9, 0xff, //0x00000a3e cmpq         $-1, %r9
	0x0f, 0x85, 0x09, 0x11, 0x00, 0x00, //0x00000a42 jne          LBB0_383
	0x4c, 0x01, 0xfa, //0x00000a48 addq         %r15, %rdx
	0x49, 0x89, 0xd1, //0x00000a4b movq         %rdx, %r9
	//0x00000a4e LBB0_131
	0x85, 0xf6, //0x00000a4e testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000a50 je           LBB0_134
	0x0f, 0xbc, 0xd6, //0x00000a56 bsfl         %esi, %edx
	0x49, 0x83, 0xfc, 0xff, //0x00000a59 cmpq         $-1, %r12
	0x0f, 0x85, 0xee, 0x10, 0x00, 0x00, //0x00000a5d jne          LBB0_383
	0x4c, 0x01, 0xfa, //0x00000a63 addq         %r15, %rdx
	0x49, 0x89, 0xd4, //0x00000a66 movq         %rdx, %r12
	//0x00000a69 LBB0_134
	0x83, 0xf9, 0x20, //0x00000a69 cmpl         $32, %ecx
	0x0f, 0x85, 0x2e, 0x05, 0x00, 0x00, //0x00000a6c jne          LBB0_198
	0x48, 0x83, 0xc3, 0xe0, //0x00000a72 addq         $-32, %rbx
	0x49, 0x83, 0xc7, 0x20, //0x00000a76 addq         $32, %r15
	0x48, 0x83, 0xfb, 0x1f, //0x00000a7a cmpq         $31, %rbx
	0x0f, 0x87, 0x0c, 0xff, 0xff, 0xff, //0x00000a7e ja           LBB0_120
	0xc5, 0xf8, 0x77, //0x00000a84 vzeroupper   
	0xc5, 0x7e, 0x6f, 0x3d, 0x51, 0xf7, 0xff, 0xff, //0x00000a87 vmovdqu      $-2223(%rip), %ymm15  /* LCPI0_19+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x29, 0xf7, 0xff, 0xff, //0x00000a8f vmovdqu      $-2263(%rip), %ymm14  /* LCPI0_18+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x01, 0xf7, 0xff, 0xff, //0x00000a97 vmovdqu      $-2303(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xd9, 0xf6, 0xff, 0xff, //0x00000a9f vmovdqu      $-2343(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x91, 0xf5, 0xff, 0xff, //0x00000aa7 vmovdqu      $-2671(%rip), %ymm11  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xa9, 0xf6, 0xff, 0xff, //0x00000aaf vmovdqu      $-2391(%rip), %ymm10  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x81, 0xf6, 0xff, 0xff, //0x00000ab7 vmovdqu      $-2431(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x59, 0xf6, 0xff, 0xff, //0x00000abf vmovdqu      $-2471(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xd1, 0xf5, 0xff, 0xff, //0x00000ac7 vmovdqu      $-2607(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xa9, 0xf5, 0xff, 0xff, //0x00000acf vmovdqu      $-2647(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x21, 0xf5, 0xff, 0xff, //0x00000ad7 vmovdqu      $-2783(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0x4d, 0x01, 0xf7, //0x00000adf addq         %r14, %r15
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00000ae2 movq         %r11, $24(%rsp)
	0x48, 0x83, 0xfb, 0x10, //0x00000ae7 cmpq         $16, %rbx
	0x0f, 0x82, 0x29, 0x01, 0x00, 0x00, //0x00000aeb jb           LBB0_155
	//0x00000af1 LBB0_137
	0x4d, 0x89, 0xfb, //0x00000af1 movq         %r15, %r11
	0x4d, 0x29, 0xf3, //0x00000af4 subq         %r14, %r11
	0x45, 0x31, 0xf6, //0x00000af7 xorl         %r14d, %r14d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000afa .p2align 4, 0x90
	//0x00000b00 LBB0_138
	0xc4, 0x81, 0x7a, 0x6f, 0x04, 0x37, //0x00000b00 vmovdqu      (%r15,%r14), %xmm0
	0xc5, 0xf9, 0x74, 0x0d, 0x32, 0xf7, 0xff, 0xff, //0x00000b06 vpcmpeqb     $-2254(%rip), %xmm0, %xmm1  /* LCPI0_20+0(%rip) */
	0xc5, 0xf9, 0x74, 0x15, 0x3a, 0xf7, 0xff, 0xff, //0x00000b0e vpcmpeqb     $-2246(%rip), %xmm0, %xmm2  /* LCPI0_21+0(%rip) */
	0xc5, 0xe9, 0xeb, 0xc9, //0x00000b16 vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xf9, 0xfc, 0x15, 0x3e, 0xf7, 0xff, 0xff, //0x00000b1a vpaddb       $-2242(%rip), %xmm0, %xmm2  /* LCPI0_22+0(%rip) */
	0xc5, 0xe9, 0xda, 0x1d, 0x46, 0xf7, 0xff, 0xff, //0x00000b22 vpminub      $-2234(%rip), %xmm2, %xmm3  /* LCPI0_23+0(%rip) */
	0xc5, 0xe9, 0x74, 0xd3, //0x00000b2a vpcmpeqb     %xmm3, %xmm2, %xmm2
	0xc5, 0xf9, 0xdb, 0x1d, 0xda, 0xf6, 0xff, 0xff, //0x00000b2e vpand        $-2342(%rip), %xmm0, %xmm3  /* LCPI0_5+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x42, 0xf7, 0xff, 0xff, //0x00000b36 vpcmpeqb     $-2238(%rip), %xmm0, %xmm0  /* LCPI0_24+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x4a, 0xf7, 0xff, 0xff, //0x00000b3e vpcmpeqb     $-2230(%rip), %xmm3, %xmm3  /* LCPI0_25+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00000b46 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xd9, 0xeb, 0xe1, //0x00000b4a vpor         %xmm1, %xmm4, %xmm4
	0xc5, 0xd9, 0xeb, 0xd2, //0x00000b4e vpor         %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xd0, //0x00000b52 vpmovmskb    %xmm0, %edx
	0xc5, 0xf9, 0xd7, 0xfb, //0x00000b56 vpmovmskb    %xmm3, %edi
	0xc5, 0xf9, 0xd7, 0xf1, //0x00000b5a vpmovmskb    %xmm1, %esi
	0xc5, 0xf9, 0xd7, 0xc2, //0x00000b5e vpmovmskb    %xmm2, %eax
	0xf7, 0xd0, //0x00000b62 notl         %eax
	0x0f, 0xbc, 0xc8, //0x00000b64 bsfl         %eax, %ecx
	0x83, 0xf9, 0x10, //0x00000b67 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00000b6a je           LBB0_140
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00000b70 movl         $-1, %eax
	0xd3, 0xe0, //0x00000b75 shll         %cl, %eax
	0xf7, 0xd0, //0x00000b77 notl         %eax
	0x21, 0xc2, //0x00000b79 andl         %eax, %edx
	0x21, 0xc7, //0x00000b7b andl         %eax, %edi
	0x21, 0xf0, //0x00000b7d andl         %esi, %eax
	0x89, 0xc6, //0x00000b7f movl         %eax, %esi
	//0x00000b81 LBB0_140
	0x8d, 0x42, 0xff, //0x00000b81 leal         $-1(%rdx), %eax
	0x21, 0xd0, //0x00000b84 andl         %edx, %eax
	0x0f, 0x85, 0x8a, 0x10, 0x00, 0x00, //0x00000b86 jne          LBB0_386
	0x8d, 0x47, 0xff, //0x00000b8c leal         $-1(%rdi), %eax
	0x21, 0xf8, //0x00000b8f andl         %edi, %eax
	0x0f, 0x85, 0x7f, 0x10, 0x00, 0x00, //0x00000b91 jne          LBB0_386
	0x8d, 0x46, 0xff, //0x00000b97 leal         $-1(%rsi), %eax
	0x21, 0xf0, //0x00000b9a andl         %esi, %eax
	0x0f, 0x85, 0x74, 0x10, 0x00, 0x00, //0x00000b9c jne          LBB0_386
	0x85, 0xd2, //0x00000ba2 testl        %edx, %edx
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000ba4 je           LBB0_146
	0x0f, 0xbc, 0xd2, //0x00000baa bsfl         %edx, %edx
	0x49, 0x83, 0xf8, 0xff, //0x00000bad cmpq         $-1, %r8
	0x0f, 0x85, 0x7b, 0x10, 0x00, 0x00, //0x00000bb1 jne          LBB0_388
	0x4c, 0x01, 0xda, //0x00000bb7 addq         %r11, %rdx
	0x4c, 0x01, 0xf2, //0x00000bba addq         %r14, %rdx
	0x49, 0x89, 0xd0, //0x00000bbd movq         %rdx, %r8
	//0x00000bc0 LBB0_146
	0x85, 0xff, //0x00000bc0 testl        %edi, %edi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000bc2 je           LBB0_149
	0x0f, 0xbc, 0xd7, //0x00000bc8 bsfl         %edi, %edx
	0x49, 0x83, 0xf9, 0xff, //0x00000bcb cmpq         $-1, %r9
	0x0f, 0x85, 0x5d, 0x10, 0x00, 0x00, //0x00000bcf jne          LBB0_388
	0x4c, 0x01, 0xda, //0x00000bd5 addq         %r11, %rdx
	0x4c, 0x01, 0xf2, //0x00000bd8 addq         %r14, %rdx
	0x49, 0x89, 0xd1, //0x00000bdb movq         %rdx, %r9
	//0x00000bde LBB0_149
	0x85, 0xf6, //0x00000bde testl        %esi, %esi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000be0 je           LBB0_152
	0x0f, 0xbc, 0xd6, //0x00000be6 bsfl         %esi, %edx
	0x49, 0x83, 0xfc, 0xff, //0x00000be9 cmpq         $-1, %r12
	0x0f, 0x85, 0x3f, 0x10, 0x00, 0x00, //0x00000bed jne          LBB0_388
	0x4c, 0x01, 0xda, //0x00000bf3 addq         %r11, %rdx
	0x4c, 0x01, 0xf2, //0x00000bf6 addq         %r14, %rdx
	0x49, 0x89, 0xd4, //0x00000bf9 movq         %rdx, %r12
	//0x00000bfc LBB0_152
	0x83, 0xf9, 0x10, //0x00000bfc cmpl         $16, %ecx
	0x0f, 0x85, 0x01, 0x05, 0x00, 0x00, //0x00000bff jne          LBB0_222
	0x48, 0x83, 0xc3, 0xf0, //0x00000c05 addq         $-16, %rbx
	0x49, 0x83, 0xc6, 0x10, //0x00000c09 addq         $16, %r14
	0x48, 0x83, 0xfb, 0x0f, //0x00000c0d cmpq         $15, %rbx
	0x0f, 0x87, 0xe9, 0xfe, 0xff, 0xff, //0x00000c11 ja           LBB0_138
	0x4d, 0x01, 0xf7, //0x00000c17 addq         %r14, %r15
	//0x00000c1a LBB0_155
	0x48, 0x85, 0xdb, //0x00000c1a testq        %rbx, %rbx
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00000c1d movq         $24(%rsp), %r11
	0x48, 0x8d, 0x35, 0xb7, 0x2b, 0x00, 0x00, //0x00000c22 leaq         $11191(%rip), %rsi  /* LJTI0_4+0(%rip) */
	0x0f, 0x84, 0xec, 0x04, 0x00, 0x00, //0x00000c29 je           LBB0_224
	0x4d, 0x8d, 0x34, 0x1f, //0x00000c2f leaq         (%r15,%rbx), %r14
	0x4c, 0x89, 0xff, //0x00000c33 movq         %r15, %rdi
	0x48, 0x2b, 0x7c, 0x24, 0x10, //0x00000c36 subq         $16(%rsp), %rdi
	0x31, 0xc9, //0x00000c3b xorl         %ecx, %ecx
	0xe9, 0x1b, 0x00, 0x00, 0x00, //0x00000c3d jmp          LBB0_160
	//0x00000c42 LBB0_157
	0x49, 0x83, 0xf8, 0xff, //0x00000c42 cmpq         $-1, %r8
	0x0f, 0x85, 0xd2, 0x0f, 0x00, 0x00, //0x00000c46 jne          LBB0_387
	0x4c, 0x8d, 0x04, 0x0f, //0x00000c4c leaq         (%rdi,%rcx), %r8
	//0x00000c50 .p2align 4, 0x90
	//0x00000c50 LBB0_159
	0x48, 0x83, 0xc1, 0x01, //0x00000c50 addq         $1, %rcx
	0x48, 0x39, 0xcb, //0x00000c54 cmpq         %rcx, %rbx
	0x0f, 0x84, 0x71, 0x0d, 0x00, 0x00, //0x00000c57 je           LBB0_362
	//0x00000c5d LBB0_160
	0x41, 0x0f, 0xbe, 0x14, 0x0f, //0x00000c5d movsbl       (%r15,%rcx), %edx
	0x8d, 0x42, 0xd0, //0x00000c62 leal         $-48(%rdx), %eax
	0x83, 0xf8, 0x0a, //0x00000c65 cmpl         $10, %eax
	0x0f, 0x82, 0xe2, 0xff, 0xff, 0xff, //0x00000c68 jb           LBB0_159
	0x8d, 0x42, 0xd5, //0x00000c6e leal         $-43(%rdx), %eax
	0x83, 0xf8, 0x1a, //0x00000c71 cmpl         $26, %eax
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x00000c74 ja           LBB0_165
	0x48, 0x63, 0x04, 0x86, //0x00000c7a movslq       (%rsi,%rax,4), %rax
	0x48, 0x01, 0xf0, //0x00000c7e addq         %rsi, %rax
	0xff, 0xe0, //0x00000c81 jmpq         *%rax
	//0x00000c83 LBB0_163
	0x49, 0x83, 0xfc, 0xff, //0x00000c83 cmpq         $-1, %r12
	0x0f, 0x85, 0x91, 0x0f, 0x00, 0x00, //0x00000c87 jne          LBB0_387
	0x4c, 0x8d, 0x24, 0x0f, //0x00000c8d leaq         (%rdi,%rcx), %r12
	0xe9, 0xba, 0xff, 0xff, 0xff, //0x00000c91 jmp          LBB0_159
	//0x00000c96 LBB0_165
	0x83, 0xfa, 0x65, //0x00000c96 cmpl         $101, %edx
	0x0f, 0x85, 0x79, 0x04, 0x00, 0x00, //0x00000c99 jne          LBB0_223
	//0x00000c9f LBB0_166
	0x49, 0x83, 0xf9, 0xff, //0x00000c9f cmpq         $-1, %r9
	0x0f, 0x85, 0x75, 0x0f, 0x00, 0x00, //0x00000ca3 jne          LBB0_387
	0x4c, 0x8d, 0x0c, 0x0f, //0x00000ca9 leaq         (%rdi,%rcx), %r9
	0xe9, 0x9e, 0xff, 0xff, 0xff, //0x00000cad jmp          LBB0_159
	//0x00000cb2 LBB0_99
	0x83, 0xf9, 0x22, //0x00000cb2 cmpl         $34, %ecx
	0x0f, 0x84, 0x42, 0x00, 0x00, 0x00, //0x00000cb5 je           LBB0_100
	//0x00000cbb LBB0_97
	0x83, 0xf9, 0x7d, //0x00000cbb cmpl         $125, %ecx
	0x0f, 0x85, 0xff, 0x23, 0x00, 0x00, //0x00000cbe jne          LBB0_623
	//0x00000cc4 LBB0_72
	0x48, 0x89, 0x32, //0x00000cc4 movq         %rsi, (%rdx)
	0x49, 0x89, 0xf0, //0x00000cc7 movq         %rsi, %r8
	0x4d, 0x89, 0xfe, //0x00000cca movq         %r15, %r14
	0x48, 0x85, 0xf6, //0x00000ccd testq        %rsi, %rsi
	0x0f, 0x85, 0x4a, 0xf8, 0xff, 0xff, //0x00000cd0 jne          LBB0_37
	0xe9, 0x64, 0x24, 0x00, 0x00, //0x00000cd6 jmp          LBB0_650
	//0x00000cdb LBB0_168
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x00000cdb cmpq         $4095, %r8
	0x0f, 0x8f, 0xc7, 0x1c, 0x00, 0x00, //0x00000ce2 jg           LBB0_631
	0x49, 0x8d, 0x40, 0x01, //0x00000ce8 leaq         $1(%r8), %rax
	0x48, 0x89, 0x02, //0x00000cec movq         %rax, (%rdx)
	0x4a, 0xc7, 0x44, 0xc2, 0x08, 0x00, 0x00, 0x00, 0x00, //0x00000cef movq         $0, $8(%rdx,%r8,8)
	0xe9, 0x11, 0xf8, 0xff, 0xff, //0x00000cf8 jmp          LBB0_35
	//0x00000cfd LBB0_100
	0x4a, 0xc7, 0x04, 0xc2, 0x02, 0x00, 0x00, 0x00, //0x00000cfd movq         $2, (%rdx,%r8,8)
	0x4c, 0x8b, 0x47, 0x08, //0x00000d05 movq         $8(%rdi), %r8
	0x4c, 0x89, 0xc3, //0x00000d09 movq         %r8, %rbx
	0x41, 0xf6, 0xc3, 0x20, //0x00000d0c testb        $32, %r11b
	0x0f, 0x85, 0x9b, 0x02, 0x00, 0x00, //0x00000d10 jne          LBB0_199
	0x4c, 0x29, 0xe3, //0x00000d16 subq         %r12, %rbx
	0x0f, 0x84, 0x7f, 0x25, 0x00, 0x00, //0x00000d19 je           LBB0_656
	0x4d, 0x89, 0xfe, //0x00000d1f movq         %r15, %r14
	0x4d, 0x89, 0xcf, //0x00000d22 movq         %r9, %r15
	0x48, 0x83, 0xfb, 0x40, //0x00000d25 cmpq         $64, %rbx
	0x0f, 0x82, 0x09, 0x12, 0x00, 0x00, //0x00000d29 jb           LBB0_413
	0x48, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x00000d2f movq         $-2, %rsi
	0x4c, 0x29, 0xd6, //0x00000d36 subq         %r10, %rsi
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00000d39 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc9, //0x00000d42 xorl         %r9d, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000d45 .p2align 4, 0x90
	//0x00000d50 LBB0_104
	0xc4, 0x81, 0x7e, 0x6f, 0x44, 0x25, 0x00, //0x00000d50 vmovdqu      (%r13,%r12), %ymm0
	0xc4, 0x81, 0x7e, 0x6f, 0x4c, 0x25, 0x20, //0x00000d57 vmovdqu      $32(%r13,%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000d5e vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00000d62 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000d66 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00000d6a vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x00000d6e vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00000d72 vpmovmskb    %ymm0, %edx
	0xc5, 0xf5, 0x74, 0xc7, //0x00000d76 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x00000d7a vpmovmskb    %ymm0, %ecx
	0x48, 0xc1, 0xe0, 0x20, //0x00000d7e shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00000d82 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00000d85 shlq         $32, %rcx
	0x48, 0x09, 0xca, //0x00000d89 orq          %rcx, %rdx
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00000d8c jne          LBB0_172
	0x4d, 0x85, 0xc9, //0x00000d92 testq        %r9, %r9
	0x0f, 0x85, 0x49, 0x00, 0x00, 0x00, //0x00000d95 jne          LBB0_174
	0x45, 0x31, 0xc9, //0x00000d9b xorl         %r9d, %r9d
	0x48, 0x85, 0xff, //0x00000d9e testq        %rdi, %rdi
	0x0f, 0x85, 0x99, 0x00, 0x00, 0x00, //0x00000da1 jne          LBB0_176
	//0x00000da7 LBB0_107
	0x48, 0x83, 0xc3, 0xc0, //0x00000da7 addq         $-64, %rbx
	0x48, 0x83, 0xc6, 0xc0, //0x00000dab addq         $-64, %rsi
	0x49, 0x83, 0xc4, 0x40, //0x00000daf addq         $64, %r12
	0x48, 0x83, 0xfb, 0x3f, //0x00000db3 cmpq         $63, %rbx
	0x0f, 0x87, 0x93, 0xff, 0xff, 0xff, //0x00000db7 ja           LBB0_104
	0xe9, 0xce, 0x0e, 0x00, 0x00, //0x00000dbd jmp          LBB0_108
	//0x00000dc2 LBB0_172
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00000dc2 movq         %r11, $24(%rsp)
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00000dc7 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x00000dcd jne          LBB0_175
	0x48, 0x0f, 0xbc, 0xc2, //0x00000dd3 bsfq         %rdx, %rax
	0x4c, 0x01, 0xe0, //0x00000dd7 addq         %r12, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00000dda movq         %rax, $16(%rsp)
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00000ddf jmp          LBB0_175
	//0x00000de4 LBB0_174
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00000de4 movq         %r11, $24(%rsp)
	//0x00000de9 LBB0_175
	0x4c, 0x89, 0xc8, //0x00000de9 movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00000dec notq         %rax
	0x48, 0x21, 0xd0, //0x00000def andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x00000df2 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xcb, //0x00000df6 orq          %r9, %r11
	0x4c, 0x89, 0xd9, //0x00000df9 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00000dfc notq         %rcx
	0x48, 0x21, 0xd1, //0x00000dff andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000e02 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x00000e0c andq         %rdx, %rcx
	0x45, 0x31, 0xc9, //0x00000e0f xorl         %r9d, %r9d
	0x48, 0x01, 0xc1, //0x00000e12 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc1, //0x00000e15 setb         %r9b
	0x48, 0x01, 0xc9, //0x00000e19 addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000e1c movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x00000e26 xorq         %rax, %rcx
	0x4c, 0x21, 0xd9, //0x00000e29 andq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00000e2c notq         %rcx
	0x48, 0x21, 0xcf, //0x00000e2f andq         %rcx, %rdi
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00000e32 movq         $24(%rsp), %r11
	0x48, 0x85, 0xff, //0x00000e37 testq        %rdi, %rdi
	0x0f, 0x84, 0x67, 0xff, 0xff, 0xff, //0x00000e3a je           LBB0_107
	//0x00000e40 LBB0_176
	0x4c, 0x0f, 0xbc, 0xe7, //0x00000e40 bsfq         %rdi, %r12
	0x49, 0x29, 0xf4, //0x00000e44 subq         %rsi, %r12
	//0x00000e47 LBB0_177
	0x4d, 0x89, 0xf9, //0x00000e47 movq         %r15, %r9
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00000e4a movq         $32(%rsp), %rdi
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x00000e4f movq         $40(%rsp), %rdx
	0x4d, 0x89, 0xf7, //0x00000e54 movq         %r14, %r15
	0x4d, 0x85, 0xe4, //0x00000e57 testq        %r12, %r12
	0x0f, 0x89, 0x2c, 0x04, 0x00, 0x00, //0x00000e5a jns          LBB0_241
	0xe9, 0x56, 0x1b, 0x00, 0x00, //0x00000e60 jmp          LBB0_495
	//0x00000e65 LBB0_178
	0x4c, 0x29, 0xe3, //0x00000e65 subq         %r12, %rbx
	0x0f, 0x84, 0x30, 0x24, 0x00, 0x00, //0x00000e68 je           LBB0_656
	0x4d, 0x89, 0xce, //0x00000e6e movq         %r9, %r14
	0x48, 0x83, 0xfb, 0x40, //0x00000e71 cmpq         $64, %rbx
	0x0f, 0x82, 0x99, 0x10, 0x00, 0x00, //0x00000e75 jb           LBB0_411
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00000e7b movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc9, //0x00000e84 xorl         %r9d, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e87 .p2align 4, 0x90
	//0x00000e90 LBB0_181
	0xc4, 0x81, 0x7e, 0x6f, 0x44, 0x25, 0x00, //0x00000e90 vmovdqu      (%r13,%r12), %ymm0
	0xc4, 0x81, 0x7e, 0x6f, 0x4c, 0x25, 0x20, //0x00000e97 vmovdqu      $32(%r13,%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000e9e vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00000ea2 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000ea6 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00000eaa vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xd7, //0x00000eae vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00000eb2 vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd7, //0x00000eb6 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00000eba vpmovmskb    %ymm2, %ecx
	0xc5, 0xbd, 0xda, 0xd1, //0x00000ebe vpminub      %ymm1, %ymm8, %ymm2
	0xc5, 0xf5, 0x74, 0xca, //0x00000ec2 vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00000ec6 vpmovmskb    %ymm1, %esi
	0x48, 0xc1, 0xe0, 0x20, //0x00000eca shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00000ece orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00000ed1 shlq         $32, %rcx
	0x48, 0xc1, 0xe6, 0x20, //0x00000ed5 shlq         $32, %rsi
	0x48, 0x09, 0xca, //0x00000ed9 orq          %rcx, %rdx
	0x0f, 0x85, 0x44, 0x00, 0x00, 0x00, //0x00000edc jne          LBB0_192
	0x4d, 0x85, 0xc9, //0x00000ee2 testq        %r9, %r9
	0x0f, 0x85, 0x5d, 0x00, 0x00, 0x00, //0x00000ee5 jne          LBB0_194
	0x45, 0x31, 0xc9, //0x00000eeb xorl         %r9d, %r9d
	//0x00000eee LBB0_184
	0xc5, 0xbd, 0xda, 0xc8, //0x00000eee vpminub      %ymm0, %ymm8, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x00000ef2 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00000ef6 vpmovmskb    %ymm0, %eax
	0x48, 0x09, 0xc6, //0x00000efa orq          %rax, %rsi
	0x48, 0x85, 0xff, //0x00000efd testq        %rdi, %rdi
	0x0f, 0x85, 0x7a, 0x0a, 0x00, 0x00, //0x00000f00 jne          LBB0_196
	0x48, 0x85, 0xf6, //0x00000f06 testq        %rsi, %rsi
	0x0f, 0x85, 0x0a, 0x22, 0x00, 0x00, //0x00000f09 jne          LBB0_645
	0x48, 0x83, 0xc3, 0xc0, //0x00000f0f addq         $-64, %rbx
	0x49, 0x83, 0xc4, 0x40, //0x00000f13 addq         $64, %r12
	0x48, 0x83, 0xfb, 0x3f, //0x00000f17 cmpq         $63, %rbx
	0x0f, 0x87, 0x6f, 0xff, 0xff, 0xff, //0x00000f1b ja           LBB0_181
	0xe9, 0x8b, 0x0c, 0x00, 0x00, //0x00000f21 jmp          LBB0_187
	//0x00000f26 LBB0_192
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00000f26 movq         %r11, $24(%rsp)
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00000f2b cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x00000f31 jne          LBB0_195
	0x48, 0x0f, 0xbc, 0xc2, //0x00000f37 bsfq         %rdx, %rax
	0x4c, 0x01, 0xe0, //0x00000f3b addq         %r12, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00000f3e movq         %rax, $16(%rsp)
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00000f43 jmp          LBB0_195
	//0x00000f48 LBB0_194
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00000f48 movq         %r11, $24(%rsp)
	//0x00000f4d LBB0_195
	0x4c, 0x89, 0xc8, //0x00000f4d movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00000f50 notq         %rax
	0x48, 0x21, 0xd0, //0x00000f53 andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x00000f56 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xcb, //0x00000f5a orq          %r9, %r11
	0x4c, 0x89, 0xd9, //0x00000f5d movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00000f60 notq         %rcx
	0x48, 0x21, 0xd1, //0x00000f63 andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000f66 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x00000f70 andq         %rdx, %rcx
	0x45, 0x31, 0xc9, //0x00000f73 xorl         %r9d, %r9d
	0x48, 0x01, 0xc1, //0x00000f76 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc1, //0x00000f79 setb         %r9b
	0x48, 0x01, 0xc9, //0x00000f7d addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000f80 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x00000f8a xorq         %rax, %rcx
	0x4c, 0x21, 0xd9, //0x00000f8d andq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00000f90 notq         %rcx
	0x48, 0x21, 0xcf, //0x00000f93 andq         %rcx, %rdi
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00000f96 movq         $24(%rsp), %r11
	0xe9, 0x4e, 0xff, 0xff, 0xff, //0x00000f9b jmp          LBB0_184
	//0x00000fa0 LBB0_198
	0x4c, 0x01, 0xf9, //0x00000fa0 addq         %r15, %rcx
	0x4c, 0x01, 0xf1, //0x00000fa3 addq         %r14, %rcx
	0xc5, 0xf8, 0x77, //0x00000fa6 vzeroupper   
	0x49, 0x89, 0xcf, //0x00000fa9 movq         %rcx, %r15
	0xe9, 0x6f, 0x01, 0x00, 0x00, //0x00000fac jmp          LBB0_225
	//0x00000fb1 LBB0_199
	0x4c, 0x29, 0xe3, //0x00000fb1 subq         %r12, %rbx
	0x0f, 0x84, 0xe4, 0x22, 0x00, 0x00, //0x00000fb4 je           LBB0_656
	0x4d, 0x89, 0xce, //0x00000fba movq         %r9, %r14
	0x48, 0x83, 0xfb, 0x40, //0x00000fbd cmpq         $64, %rbx
	0x0f, 0x82, 0x8f, 0x0f, 0x00, 0x00, //0x00000fc1 jb           LBB0_414
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00000fc7 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc9, //0x00000fd0 xorl         %r9d, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000fd3 .p2align 4, 0x90
	//0x00000fe0 LBB0_202
	0xc4, 0x81, 0x7e, 0x6f, 0x44, 0x25, 0x00, //0x00000fe0 vmovdqu      (%r13,%r12), %ymm0
	0xc4, 0x81, 0x7e, 0x6f, 0x4c, 0x25, 0x20, //0x00000fe7 vmovdqu      $32(%r13,%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000fee vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00000ff2 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000ff6 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00000ffa vpmovmskb    %ymm2, %ecx
	0xc5, 0xfd, 0x74, 0xd7, //0x00000ffe vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001002 vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd7, //0x00001006 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x0000100a vpmovmskb    %ymm2, %eax
	0xc5, 0xbd, 0xda, 0xd1, //0x0000100e vpminub      %ymm1, %ymm8, %ymm2
	0xc5, 0xf5, 0x74, 0xca, //0x00001012 vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001016 vpmovmskb    %ymm1, %esi
	0x48, 0xc1, 0xe1, 0x20, //0x0000101a shlq         $32, %rcx
	0x48, 0x09, 0xcf, //0x0000101e orq          %rcx, %rdi
	0x48, 0xc1, 0xe0, 0x20, //0x00001021 shlq         $32, %rax
	0x48, 0xc1, 0xe6, 0x20, //0x00001025 shlq         $32, %rsi
	0x48, 0x09, 0xc2, //0x00001029 orq          %rax, %rdx
	0x0f, 0x85, 0x44, 0x00, 0x00, 0x00, //0x0000102c jne          LBB0_213
	0x4d, 0x85, 0xc9, //0x00001032 testq        %r9, %r9
	0x0f, 0x85, 0x5d, 0x00, 0x00, 0x00, //0x00001035 jne          LBB0_215
	0x45, 0x31, 0xc9, //0x0000103b xorl         %r9d, %r9d
	//0x0000103e LBB0_205
	0xc5, 0xbd, 0xda, 0xc8, //0x0000103e vpminub      %ymm0, %ymm8, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x00001042 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00001046 vpmovmskb    %ymm0, %eax
	0x48, 0x09, 0xc6, //0x0000104a orq          %rax, %rsi
	0x48, 0x85, 0xff, //0x0000104d testq        %rdi, %rdi
	0x0f, 0x85, 0x9a, 0x00, 0x00, 0x00, //0x00001050 jne          LBB0_217
	0x48, 0x85, 0xf6, //0x00001056 testq        %rsi, %rsi
	0x0f, 0x85, 0xba, 0x20, 0x00, 0x00, //0x00001059 jne          LBB0_645
	0x48, 0x83, 0xc3, 0xc0, //0x0000105f addq         $-64, %rbx
	0x49, 0x83, 0xc4, 0x40, //0x00001063 addq         $64, %r12
	0x48, 0x83, 0xfb, 0x3f, //0x00001067 cmpq         $63, %rbx
	0x0f, 0x87, 0x6f, 0xff, 0xff, 0xff, //0x0000106b ja           LBB0_202
	0xe9, 0x6d, 0x0c, 0x00, 0x00, //0x00001071 jmp          LBB0_208
	//0x00001076 LBB0_213
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00001076 movq         %r11, $24(%rsp)
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x0000107b cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x00001081 jne          LBB0_216
	0x48, 0x0f, 0xbc, 0xc2, //0x00001087 bsfq         %rdx, %rax
	0x4c, 0x01, 0xe0, //0x0000108b addq         %r12, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x0000108e movq         %rax, $16(%rsp)
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00001093 jmp          LBB0_216
	//0x00001098 LBB0_215
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00001098 movq         %r11, $24(%rsp)
	//0x0000109d LBB0_216
	0x4c, 0x89, 0xc8, //0x0000109d movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x000010a0 notq         %rax
	0x48, 0x21, 0xd0, //0x000010a3 andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x000010a6 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xcb, //0x000010aa orq          %r9, %r11
	0x4c, 0x89, 0xd9, //0x000010ad movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x000010b0 notq         %rcx
	0x48, 0x21, 0xd1, //0x000010b3 andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000010b6 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x000010c0 andq         %rdx, %rcx
	0x45, 0x31, 0xc9, //0x000010c3 xorl         %r9d, %r9d
	0x48, 0x01, 0xc1, //0x000010c6 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc1, //0x000010c9 setb         %r9b
	0x48, 0x01, 0xc9, //0x000010cd addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000010d0 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x000010da xorq         %rax, %rcx
	0x4c, 0x21, 0xd9, //0x000010dd andq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x000010e0 notq         %rcx
	0x48, 0x21, 0xcf, //0x000010e3 andq         %rcx, %rdi
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x000010e6 movq         $24(%rsp), %r11
	0xe9, 0x4e, 0xff, 0xff, 0xff, //0x000010eb jmp          LBB0_205
	//0x000010f0 LBB0_217
	0x48, 0x0f, 0xbc, 0xc7, //0x000010f0 bsfq         %rdi, %rax
	0x48, 0x85, 0xf6, //0x000010f4 testq        %rsi, %rsi
	0x0f, 0x84, 0x64, 0x01, 0x00, 0x00, //0x000010f7 je           LBB0_238
	0x48, 0x0f, 0xbc, 0xf6, //0x000010fd bsfq         %rsi, %rsi
	0xe9, 0x60, 0x01, 0x00, 0x00, //0x00001101 jmp          LBB0_239
	//0x00001106 LBB0_222
	0x89, 0xc8, //0x00001106 movl         %ecx, %eax
	0x49, 0x01, 0xc7, //0x00001108 addq         %rax, %r15
	0x4d, 0x01, 0xf7, //0x0000110b addq         %r14, %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x0000110e movq         $24(%rsp), %r11
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00001113 jmp          LBB0_224
	//0x00001118 LBB0_223
	0x49, 0x01, 0xcf, //0x00001118 addq         %rcx, %r15
	//0x0000111b LBB0_224
	0x4c, 0x8b, 0x74, 0x24, 0x10, //0x0000111b movq         $16(%rsp), %r14
	//0x00001120 LBB0_225
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001120 movq         $-1, %rcx
	0x4d, 0x85, 0xc0, //0x00001127 testq        %r8, %r8
	0x0f, 0x84, 0x81, 0x1f, 0x00, 0x00, //0x0000112a je           LBB0_622
	0x4d, 0x85, 0xe4, //0x00001130 testq        %r12, %r12
	0x0f, 0x84, 0x78, 0x1f, 0x00, 0x00, //0x00001133 je           LBB0_622
	0x4d, 0x85, 0xc9, //0x00001139 testq        %r9, %r9
	0x0f, 0x84, 0x6f, 0x1f, 0x00, 0x00, //0x0000113c je           LBB0_622
	0x4d, 0x29, 0xf7, //0x00001142 subq         %r14, %r15
	0x49, 0x8d, 0x4f, 0xff, //0x00001145 leaq         $-1(%r15), %rcx
	0x49, 0x39, 0xc8, //0x00001149 cmpq         %rcx, %r8
	0x0f, 0x84, 0x8c, 0x00, 0x00, 0x00, //0x0000114c je           LBB0_234
	0x49, 0x39, 0xcc, //0x00001152 cmpq         %rcx, %r12
	0x0f, 0x84, 0x83, 0x00, 0x00, 0x00, //0x00001155 je           LBB0_234
	0x49, 0x39, 0xc9, //0x0000115b cmpq         %rcx, %r9
	0x0f, 0x84, 0x7a, 0x00, 0x00, 0x00, //0x0000115e je           LBB0_234
	0x4d, 0x85, 0xe4, //0x00001164 testq        %r12, %r12
	0xc5, 0xfe, 0x6f, 0x2d, 0x91, 0xee, 0xff, 0xff, //0x00001167 vmovdqu      $-4463(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x09, 0xef, 0xff, 0xff, //0x0000116f vmovdqu      $-4343(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x21, 0xef, 0xff, 0xff, //0x00001177 vmovdqu      $-4319(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x99, 0xef, 0xff, 0xff, //0x0000117f vmovdqu      $-4199(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0xb1, 0xef, 0xff, 0xff, //0x00001187 vmovdqu      $-4175(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xc9, 0xef, 0xff, 0xff, //0x0000118f vmovdqu      $-4151(%rip), %ymm10  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xa1, 0xee, 0xff, 0xff, //0x00001197 vmovdqu      $-4447(%rip), %ymm11  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xd9, 0xef, 0xff, 0xff, //0x0000119f vmovdqu      $-4135(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xf1, 0xef, 0xff, 0xff, //0x000011a7 vmovdqu      $-4111(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x09, 0xf0, 0xff, 0xff, //0x000011af vmovdqu      $-4087(%rip), %ymm14  /* LCPI0_18+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x21, 0xf0, 0xff, 0xff, //0x000011b7 vmovdqu      $-4063(%rip), %ymm15  /* LCPI0_19+0(%rip) */
	0x0f, 0x8e, 0x79, 0x00, 0x00, 0x00, //0x000011bf jle          LBB0_235
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x000011c5 leaq         $-1(%r12), %rax
	0x49, 0x39, 0xc1, //0x000011ca cmpq         %rax, %r9
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x000011cd je           LBB0_235
	0x49, 0xf7, 0xd4, //0x000011d3 notq         %r12
	0x4d, 0x89, 0xe7, //0x000011d6 movq         %r12, %r15
	0xe9, 0x6d, 0x0a, 0x00, 0x00, //0x000011d9 jmp          LBB0_390
	//0x000011de LBB0_234
	0x49, 0xf7, 0xdf, //0x000011de negq         %r15
	0xc5, 0xfe, 0x6f, 0x2d, 0x17, 0xee, 0xff, 0xff, //0x000011e1 vmovdqu      $-4585(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x8f, 0xee, 0xff, 0xff, //0x000011e9 vmovdqu      $-4465(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xa7, 0xee, 0xff, 0xff, //0x000011f1 vmovdqu      $-4441(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x1f, 0xef, 0xff, 0xff, //0x000011f9 vmovdqu      $-4321(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x37, 0xef, 0xff, 0xff, //0x00001201 vmovdqu      $-4297(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x4f, 0xef, 0xff, 0xff, //0x00001209 vmovdqu      $-4273(%rip), %ymm10  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x27, 0xee, 0xff, 0xff, //0x00001211 vmovdqu      $-4569(%rip), %ymm11  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x5f, 0xef, 0xff, 0xff, //0x00001219 vmovdqu      $-4257(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x77, 0xef, 0xff, 0xff, //0x00001221 vmovdqu      $-4233(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x8f, 0xef, 0xff, 0xff, //0x00001229 vmovdqu      $-4209(%rip), %ymm14  /* LCPI0_18+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xa7, 0xef, 0xff, 0xff, //0x00001231 vmovdqu      $-4185(%rip), %ymm15  /* LCPI0_19+0(%rip) */
	0xe9, 0x0d, 0x0a, 0x00, 0x00, //0x00001239 jmp          LBB0_390
	//0x0000123e LBB0_235
	0x4c, 0x89, 0xc0, //0x0000123e movq         %r8, %rax
	0x4c, 0x09, 0xc8, //0x00001241 orq          %r9, %rax
	0x0f, 0x99, 0xc1, //0x00001244 setns        %cl
	0x0f, 0x88, 0xe1, 0x05, 0x00, 0x00, //0x00001247 js           LBB0_341
	0x4d, 0x39, 0xc8, //0x0000124d cmpq         %r9, %r8
	0x0f, 0x8c, 0xd8, 0x05, 0x00, 0x00, //0x00001250 jl           LBB0_341
	0x49, 0xf7, 0xd0, //0x00001256 notq         %r8
	0x4d, 0x89, 0xc7, //0x00001259 movq         %r8, %r15
	0xe9, 0xea, 0x09, 0x00, 0x00, //0x0000125c jmp          LBB0_390
	//0x00001261 LBB0_238
	0xbe, 0x40, 0x00, 0x00, 0x00, //0x00001261 movl         $64, %esi
	//0x00001266 LBB0_239
	0x4d, 0x89, 0xf1, //0x00001266 movq         %r14, %r9
	0x48, 0x39, 0xc6, //0x00001269 cmpq         %rax, %rsi
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x0000126c movq         $32(%rsp), %rdi
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x00001271 movq         $40(%rsp), %rdx
	0x0f, 0x82, 0x3d, 0x20, 0x00, 0x00, //0x00001276 jb           LBB0_363
	0x49, 0x01, 0xc4, //0x0000127c addq         %rax, %r12
	0x49, 0x83, 0xc4, 0x01, //0x0000127f addq         $1, %r12
	0x4d, 0x85, 0xe4, //0x00001283 testq        %r12, %r12
	0x0f, 0x88, 0x2f, 0x17, 0x00, 0x00, //0x00001286 js           LBB0_495
	//0x0000128c LBB0_241
	0x4d, 0x89, 0x21, //0x0000128c movq         %r12, (%r9)
	0x4d, 0x89, 0xd6, //0x0000128f movq         %r10, %r14
	0x48, 0xb8, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00001292 movabsq      $9223372036854775806, %rax
	0x49, 0x39, 0xc2, //0x0000129c cmpq         %rax, %r10
	0x0f, 0x87, 0x9a, 0x1e, 0x00, 0x00, //0x0000129f ja           LBB0_650
	0x48, 0x8b, 0x02, //0x000012a5 movq         (%rdx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x000012a8 cmpq         $4095, %rax
	0x0f, 0x8f, 0xfb, 0x16, 0x00, 0x00, //0x000012ae jg           LBB0_631
	0x48, 0x8d, 0x48, 0x01, //0x000012b4 leaq         $1(%rax), %rcx
	0x48, 0x89, 0x0a, //0x000012b8 movq         %rcx, (%rdx)
	0x48, 0xc7, 0x44, 0xc2, 0x08, 0x04, 0x00, 0x00, 0x00, //0x000012bb movq         $4, $8(%rdx,%rax,8)
	0xe9, 0x45, 0xf2, 0xff, 0xff, //0x000012c4 jmp          LBB0_35
	//0x000012c9 LBB0_244
	0x48, 0x8b, 0x47, 0x08, //0x000012c9 movq         $8(%rdi), %rax
	0x48, 0x8d, 0x48, 0xfc, //0x000012cd leaq         $-4(%rax), %rcx
	0x49, 0x39, 0xca, //0x000012d1 cmpq         %rcx, %r10
	0x0f, 0x83, 0x7a, 0x1e, 0x00, 0x00, //0x000012d4 jae          LBB0_630
	0x43, 0x8b, 0x4c, 0x25, 0x00, //0x000012da movl         (%r13,%r12), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x000012df cmpl         $1702063201, %ecx
	0x0f, 0x85, 0x8c, 0x1e, 0x00, 0x00, //0x000012e5 jne          LBB0_632
	0x4d, 0x8d, 0x62, 0x05, //0x000012eb leaq         $5(%r10), %r12
	0xe9, 0x01, 0xf2, 0xff, 0xff, //0x000012ef jmp          LBB0_34
	//0x000012f4 LBB0_247
	0x4c, 0x8b, 0x47, 0x08, //0x000012f4 movq         $8(%rdi), %r8
	0x4c, 0x89, 0xc3, //0x000012f8 movq         %r8, %rbx
	0x41, 0xf6, 0xc3, 0x20, //0x000012fb testb        $32, %r11b
	0x0f, 0x85, 0x42, 0x05, 0x00, 0x00, //0x000012ff jne          LBB0_342
	0x4c, 0x29, 0xe3, //0x00001305 subq         %r12, %rbx
	0x0f, 0x84, 0x90, 0x1f, 0x00, 0x00, //0x00001308 je           LBB0_656
	0x4d, 0x89, 0xfe, //0x0000130e movq         %r15, %r14
	0x4d, 0x89, 0xcf, //0x00001311 movq         %r9, %r15
	0x48, 0x83, 0xfb, 0x40, //0x00001314 cmpq         $64, %rbx
	0x0f, 0x82, 0x86, 0x0c, 0x00, 0x00, //0x00001318 jb           LBB0_417
	0x48, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x0000131e movq         $-2, %rsi
	0x4c, 0x29, 0xd6, //0x00001325 subq         %r10, %rsi
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00001328 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc9, //0x00001331 xorl         %r9d, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001334 .p2align 4, 0x90
	//0x00001340 LBB0_251
	0xc4, 0x81, 0x7e, 0x6f, 0x44, 0x25, 0x00, //0x00001340 vmovdqu      (%r13,%r12), %ymm0
	0xc4, 0x81, 0x7e, 0x6f, 0x4c, 0x25, 0x20, //0x00001347 vmovdqu      $32(%r13,%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x0000134e vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00001352 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00001356 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x0000135a vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x0000135e vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00001362 vpmovmskb    %ymm0, %edx
	0xc5, 0xf5, 0x74, 0xc7, //0x00001366 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x0000136a vpmovmskb    %ymm0, %ecx
	0x48, 0xc1, 0xe0, 0x20, //0x0000136e shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00001372 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00001375 shlq         $32, %rcx
	0x48, 0x09, 0xca, //0x00001379 orq          %rcx, %rdx
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x0000137c jne          LBB0_269
	0x4d, 0x85, 0xc9, //0x00001382 testq        %r9, %r9
	0x0f, 0x85, 0x49, 0x00, 0x00, 0x00, //0x00001385 jne          LBB0_271
	0x45, 0x31, 0xc9, //0x0000138b xorl         %r9d, %r9d
	0x48, 0x85, 0xff, //0x0000138e testq        %rdi, %rdi
	0x0f, 0x85, 0x3e, 0xf1, 0xff, 0xff, //0x00001391 jne          LBB0_31
	//0x00001397 LBB0_254
	0x48, 0x83, 0xc3, 0xc0, //0x00001397 addq         $-64, %rbx
	0x48, 0x83, 0xc6, 0xc0, //0x0000139b addq         $-64, %rsi
	0x49, 0x83, 0xc4, 0x40, //0x0000139f addq         $64, %r12
	0x48, 0x83, 0xfb, 0x3f, //0x000013a3 cmpq         $63, %rbx
	0x0f, 0x87, 0x93, 0xff, 0xff, 0xff, //0x000013a7 ja           LBB0_251
	0xe9, 0xbf, 0x09, 0x00, 0x00, //0x000013ad jmp          LBB0_255
	//0x000013b2 LBB0_269
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x000013b2 movq         %r11, $24(%rsp)
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x000013b7 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x000013bd jne          LBB0_272
	0x48, 0x0f, 0xbc, 0xc2, //0x000013c3 bsfq         %rdx, %rax
	0x4c, 0x01, 0xe0, //0x000013c7 addq         %r12, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x000013ca movq         %rax, $16(%rsp)
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x000013cf jmp          LBB0_272
	//0x000013d4 LBB0_271
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x000013d4 movq         %r11, $24(%rsp)
	//0x000013d9 LBB0_272
	0x4c, 0x89, 0xc8, //0x000013d9 movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x000013dc notq         %rax
	0x48, 0x21, 0xd0, //0x000013df andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x000013e2 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xcb, //0x000013e6 orq          %r9, %r11
	0x4c, 0x89, 0xd9, //0x000013e9 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x000013ec notq         %rcx
	0x48, 0x21, 0xd1, //0x000013ef andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000013f2 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x000013fc andq         %rdx, %rcx
	0x45, 0x31, 0xc9, //0x000013ff xorl         %r9d, %r9d
	0x48, 0x01, 0xc1, //0x00001402 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc1, //0x00001405 setb         %r9b
	0x48, 0x01, 0xc9, //0x00001409 addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000140c movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x00001416 xorq         %rax, %rcx
	0x4c, 0x21, 0xd9, //0x00001419 andq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x0000141c notq         %rcx
	0x48, 0x21, 0xcf, //0x0000141f andq         %rcx, %rdi
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001422 movq         $24(%rsp), %r11
	0x48, 0x85, 0xff, //0x00001427 testq        %rdi, %rdi
	0x0f, 0x84, 0x67, 0xff, 0xff, 0xff, //0x0000142a je           LBB0_254
	0xe9, 0xa0, 0xf0, 0xff, 0xff, //0x00001430 jmp          LBB0_31
	//0x00001435 LBB0_273
	0x4c, 0x89, 0x7c, 0x24, 0x38, //0x00001435 movq         %r15, $56(%rsp)
	0x4c, 0x8b, 0x7f, 0x08, //0x0000143a movq         $8(%rdi), %r15
	0x4d, 0x29, 0xe7, //0x0000143e subq         %r12, %r15
	0x0f, 0x84, 0x24, 0x1d, 0x00, 0x00, //0x00001441 je           LBB0_629
	0x4f, 0x8d, 0x34, 0x2c, //0x00001447 leaq         (%r12,%r13), %r14
	0x41, 0x80, 0x3e, 0x30, //0x0000144b cmpb         $48, (%r14)
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x0000144f jne          LBB0_278
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001455 movl         $1, %ebx
	0x49, 0x83, 0xff, 0x01, //0x0000145a cmpq         $1, %r15
	0x0f, 0x84, 0x05, 0x09, 0x00, 0x00, //0x0000145e je           LBB0_402
	0x41, 0x8a, 0x46, 0x01, //0x00001464 movb         $1(%r14), %al
	0x04, 0xd2, //0x00001468 addb         $-46, %al
	0x3c, 0x37, //0x0000146a cmpb         $55, %al
	0x0f, 0x87, 0xf7, 0x08, 0x00, 0x00, //0x0000146c ja           LBB0_402
	0x0f, 0xb6, 0xc0, //0x00001472 movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00001475 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x0000147f btq          %rax, %rcx
	0x0f, 0x83, 0xe0, 0x08, 0x00, 0x00, //0x00001483 jae          LBB0_402
	//0x00001489 LBB0_278
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00001489 movq         %r11, $24(%rsp)
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000148e movq         $-1, %r8
	0x49, 0x83, 0xff, 0x20, //0x00001495 cmpq         $32, %r15
	0x0f, 0x82, 0xdb, 0x0a, 0x00, 0x00, //0x00001499 jb           LBB0_416
	0x31, 0xdb, //0x0000149f xorl         %ebx, %ebx
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000014a1 movq         $-1, %r11
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000014a8 movq         $-1, %r12
	0x90, //0x000014af .p2align 4, 0x90
	//0x000014b0 LBB0_280
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x1e, //0x000014b0 vmovdqu      (%r14,%rbx), %ymm0
	0xc5, 0xb5, 0x74, 0xc8, //0x000014b6 vpcmpeqb     %ymm0, %ymm9, %ymm1
	0xc5, 0xad, 0x74, 0xd0, //0x000014ba vpcmpeqb     %ymm0, %ymm10, %ymm2
	0xc5, 0xed, 0xeb, 0xc9, //0x000014be vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xa5, 0xdb, 0xd0, //0x000014c2 vpand        %ymm0, %ymm11, %ymm2
	0xc5, 0x9d, 0x74, 0xd8, //0x000014c6 vpcmpeqb     %ymm0, %ymm12, %ymm3
	0xc5, 0xfd, 0xd7, 0xd3, //0x000014ca vpmovmskb    %ymm3, %edx
	0xc5, 0x95, 0x74, 0xd2, //0x000014ce vpcmpeqb     %ymm2, %ymm13, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000014d2 vpmovmskb    %ymm2, %edi
	0xc5, 0xfd, 0xd7, 0xf1, //0x000014d6 vpmovmskb    %ymm1, %esi
	0xc5, 0x8d, 0xfc, 0xc0, //0x000014da vpaddb       %ymm0, %ymm14, %ymm0
	0xc5, 0x85, 0xda, 0xe0, //0x000014de vpminub      %ymm0, %ymm15, %ymm4
	0xc5, 0xfd, 0x74, 0xc4, //0x000014e2 vpcmpeqb     %ymm4, %ymm0, %ymm0
	0xc5, 0xe5, 0xeb, 0xd2, //0x000014e6 vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0xfd, 0xeb, 0xc2, //0x000014ea vpor         %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x000014ee vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000014f2 vpmovmskb    %ymm0, %eax
	0x48, 0xf7, 0xd0, //0x000014f6 notq         %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x000014f9 bsfq         %rax, %rcx
	0x83, 0xf9, 0x20, //0x000014fd cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00001500 je           LBB0_282
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001506 movl         $-1, %eax
	0xd3, 0xe0, //0x0000150b shll         %cl, %eax
	0xf7, 0xd0, //0x0000150d notl         %eax
	0x21, 0xc2, //0x0000150f andl         %eax, %edx
	0x21, 0xc7, //0x00001511 andl         %eax, %edi
	0x21, 0xf0, //0x00001513 andl         %esi, %eax
	0x89, 0xc6, //0x00001515 movl         %eax, %esi
	//0x00001517 LBB0_282
	0x8d, 0x42, 0xff, //0x00001517 leal         $-1(%rdx), %eax
	0x21, 0xd0, //0x0000151a andl         %edx, %eax
	0x0f, 0x85, 0x26, 0x08, 0x00, 0x00, //0x0000151c jne          LBB0_396
	0x8d, 0x47, 0xff, //0x00001522 leal         $-1(%rdi), %eax
	0x21, 0xf8, //0x00001525 andl         %edi, %eax
	0x0f, 0x85, 0x1b, 0x08, 0x00, 0x00, //0x00001527 jne          LBB0_396
	0x8d, 0x46, 0xff, //0x0000152d leal         $-1(%rsi), %eax
	0x21, 0xf0, //0x00001530 andl         %esi, %eax
	0x0f, 0x85, 0x10, 0x08, 0x00, 0x00, //0x00001532 jne          LBB0_396
	0x85, 0xd2, //0x00001538 testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x0000153a je           LBB0_288
	0x0f, 0xbc, 0xd2, //0x00001540 bsfl         %edx, %edx
	0x49, 0x83, 0xfc, 0xff, //0x00001543 cmpq         $-1, %r12
	0x0f, 0x85, 0x03, 0x08, 0x00, 0x00, //0x00001547 jne          LBB0_397
	0x48, 0x01, 0xda, //0x0000154d addq         %rbx, %rdx
	0x49, 0x89, 0xd4, //0x00001550 movq         %rdx, %r12
	//0x00001553 LBB0_288
	0x85, 0xff, //0x00001553 testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00001555 je           LBB0_291
	0x0f, 0xbc, 0xd7, //0x0000155b bsfl         %edi, %edx
	0x49, 0x83, 0xfb, 0xff, //0x0000155e cmpq         $-1, %r11
	0x0f, 0x85, 0xe8, 0x07, 0x00, 0x00, //0x00001562 jne          LBB0_397
	0x48, 0x01, 0xda, //0x00001568 addq         %rbx, %rdx
	0x49, 0x89, 0xd3, //0x0000156b movq         %rdx, %r11
	//0x0000156e LBB0_291
	0x85, 0xf6, //0x0000156e testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00001570 je           LBB0_294
	0x0f, 0xbc, 0xd6, //0x00001576 bsfl         %esi, %edx
	0x49, 0x83, 0xf8, 0xff, //0x00001579 cmpq         $-1, %r8
	0x0f, 0x85, 0xcd, 0x07, 0x00, 0x00, //0x0000157d jne          LBB0_397
	0x48, 0x01, 0xda, //0x00001583 addq         %rbx, %rdx
	0x49, 0x89, 0xd0, //0x00001586 movq         %rdx, %r8
	//0x00001589 LBB0_294
	0x83, 0xf9, 0x20, //0x00001589 cmpl         $32, %ecx
	0x0f, 0x85, 0x2b, 0x04, 0x00, 0x00, //0x0000158c jne          LBB0_361
	0x49, 0x83, 0xc7, 0xe0, //0x00001592 addq         $-32, %r15
	0x48, 0x83, 0xc3, 0x20, //0x00001596 addq         $32, %rbx
	0x49, 0x83, 0xff, 0x1f, //0x0000159a cmpq         $31, %r15
	0x0f, 0x87, 0x0c, 0xff, 0xff, 0xff, //0x0000159e ja           LBB0_280
	0xc5, 0xf8, 0x77, //0x000015a4 vzeroupper   
	0x4c, 0x01, 0xf3, //0x000015a7 addq         %r14, %rbx
	0x4c, 0x89, 0x74, 0x24, 0x10, //0x000015aa movq         %r14, $16(%rsp)
	0x4c, 0x89, 0x4c, 0x24, 0x30, //0x000015af movq         %r9, $48(%rsp)
	0x49, 0x83, 0xff, 0x10, //0x000015b4 cmpq         $16, %r15
	0x0f, 0x82, 0x2c, 0x01, 0x00, 0x00, //0x000015b8 jb           LBB0_315
	//0x000015be LBB0_297
	0x48, 0x89, 0xd8, //0x000015be movq         %rbx, %rax
	0x4c, 0x29, 0xd0, //0x000015c1 subq         %r10, %rax
	0x4d, 0x89, 0xe9, //0x000015c4 movq         %r13, %r9
	0x49, 0xf7, 0xd1, //0x000015c7 notq         %r9
	0x49, 0x01, 0xc1, //0x000015ca addq         %rax, %r9
	0x45, 0x31, 0xf6, //0x000015cd xorl         %r14d, %r14d
	//0x000015d0 LBB0_298
	0xc4, 0xa1, 0x7a, 0x6f, 0x04, 0x33, //0x000015d0 vmovdqu      (%rbx,%r14), %xmm0
	0xc5, 0xf9, 0x74, 0x0d, 0x62, 0xec, 0xff, 0xff, //0x000015d6 vpcmpeqb     $-5022(%rip), %xmm0, %xmm1  /* LCPI0_20+0(%rip) */
	0xc5, 0xf9, 0x74, 0x15, 0x6a, 0xec, 0xff, 0xff, //0x000015de vpcmpeqb     $-5014(%rip), %xmm0, %xmm2  /* LCPI0_21+0(%rip) */
	0xc5, 0xe9, 0xeb, 0xc9, //0x000015e6 vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xf9, 0xfc, 0x15, 0x6e, 0xec, 0xff, 0xff, //0x000015ea vpaddb       $-5010(%rip), %xmm0, %xmm2  /* LCPI0_22+0(%rip) */
	0xc5, 0xe9, 0xda, 0x1d, 0x76, 0xec, 0xff, 0xff, //0x000015f2 vpminub      $-5002(%rip), %xmm2, %xmm3  /* LCPI0_23+0(%rip) */
	0xc5, 0xe9, 0x74, 0xd3, //0x000015fa vpcmpeqb     %xmm3, %xmm2, %xmm2
	0xc5, 0xf9, 0xdb, 0x1d, 0x0a, 0xec, 0xff, 0xff, //0x000015fe vpand        $-5110(%rip), %xmm0, %xmm3  /* LCPI0_5+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x72, 0xec, 0xff, 0xff, //0x00001606 vpcmpeqb     $-5006(%rip), %xmm0, %xmm0  /* LCPI0_24+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x7a, 0xec, 0xff, 0xff, //0x0000160e vpcmpeqb     $-4998(%rip), %xmm3, %xmm3  /* LCPI0_25+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00001616 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xd9, 0xeb, 0xe1, //0x0000161a vpor         %xmm1, %xmm4, %xmm4
	0xc5, 0xd9, 0xeb, 0xd2, //0x0000161e vpor         %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xd0, //0x00001622 vpmovmskb    %xmm0, %edx
	0xc5, 0xf9, 0xd7, 0xfb, //0x00001626 vpmovmskb    %xmm3, %edi
	0xc5, 0xf9, 0xd7, 0xf1, //0x0000162a vpmovmskb    %xmm1, %esi
	0xc5, 0xf9, 0xd7, 0xc2, //0x0000162e vpmovmskb    %xmm2, %eax
	0xf7, 0xd0, //0x00001632 notl         %eax
	0x0f, 0xbc, 0xc8, //0x00001634 bsfl         %eax, %ecx
	0x83, 0xf9, 0x10, //0x00001637 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x0000163a je           LBB0_300
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001640 movl         $-1, %eax
	0xd3, 0xe0, //0x00001645 shll         %cl, %eax
	0xf7, 0xd0, //0x00001647 notl         %eax
	0x21, 0xc2, //0x00001649 andl         %eax, %edx
	0x21, 0xc7, //0x0000164b andl         %eax, %edi
	0x21, 0xf0, //0x0000164d andl         %esi, %eax
	0x89, 0xc6, //0x0000164f movl         %eax, %esi
	//0x00001651 LBB0_300
	0x8d, 0x42, 0xff, //0x00001651 leal         $-1(%rdx), %eax
	0x21, 0xd0, //0x00001654 andl         %edx, %eax
	0x0f, 0x85, 0x41, 0x08, 0x00, 0x00, //0x00001656 jne          LBB0_405
	0x8d, 0x47, 0xff, //0x0000165c leal         $-1(%rdi), %eax
	0x21, 0xf8, //0x0000165f andl         %edi, %eax
	0x0f, 0x85, 0x36, 0x08, 0x00, 0x00, //0x00001661 jne          LBB0_405
	0x8d, 0x46, 0xff, //0x00001667 leal         $-1(%rsi), %eax
	0x21, 0xf0, //0x0000166a andl         %esi, %eax
	0x0f, 0x85, 0x2b, 0x08, 0x00, 0x00, //0x0000166c jne          LBB0_405
	0x85, 0xd2, //0x00001672 testl        %edx, %edx
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001674 je           LBB0_306
	0x0f, 0xbc, 0xd2, //0x0000167a bsfl         %edx, %edx
	0x49, 0x83, 0xfc, 0xff, //0x0000167d cmpq         $-1, %r12
	0x0f, 0x85, 0x2f, 0x08, 0x00, 0x00, //0x00001681 jne          LBB0_407
	0x4c, 0x01, 0xca, //0x00001687 addq         %r9, %rdx
	0x4c, 0x01, 0xf2, //0x0000168a addq         %r14, %rdx
	0x49, 0x89, 0xd4, //0x0000168d movq         %rdx, %r12
	//0x00001690 LBB0_306
	0x85, 0xff, //0x00001690 testl        %edi, %edi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001692 je           LBB0_309
	0x0f, 0xbc, 0xd7, //0x00001698 bsfl         %edi, %edx
	0x49, 0x83, 0xfb, 0xff, //0x0000169b cmpq         $-1, %r11
	0x0f, 0x85, 0x11, 0x08, 0x00, 0x00, //0x0000169f jne          LBB0_407
	0x4c, 0x01, 0xca, //0x000016a5 addq         %r9, %rdx
	0x4c, 0x01, 0xf2, //0x000016a8 addq         %r14, %rdx
	0x49, 0x89, 0xd3, //0x000016ab movq         %rdx, %r11
	//0x000016ae LBB0_309
	0x85, 0xf6, //0x000016ae testl        %esi, %esi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000016b0 je           LBB0_312
	0x0f, 0xbc, 0xd6, //0x000016b6 bsfl         %esi, %edx
	0x49, 0x83, 0xf8, 0xff, //0x000016b9 cmpq         $-1, %r8
	0x0f, 0x85, 0xf3, 0x07, 0x00, 0x00, //0x000016bd jne          LBB0_407
	0x4c, 0x01, 0xca, //0x000016c3 addq         %r9, %rdx
	0x4c, 0x01, 0xf2, //0x000016c6 addq         %r14, %rdx
	0x49, 0x89, 0xd0, //0x000016c9 movq         %rdx, %r8
	//0x000016cc LBB0_312
	0x83, 0xf9, 0x10, //0x000016cc cmpl         $16, %ecx
	0x0f, 0x85, 0x09, 0x03, 0x00, 0x00, //0x000016cf jne          LBB0_365
	0x49, 0x83, 0xc7, 0xf0, //0x000016d5 addq         $-16, %r15
	0x49, 0x83, 0xc6, 0x10, //0x000016d9 addq         $16, %r14
	0x49, 0x83, 0xff, 0x0f, //0x000016dd cmpq         $15, %r15
	0x0f, 0x87, 0xe9, 0xfe, 0xff, 0xff, //0x000016e1 ja           LBB0_298
	0x4c, 0x01, 0xf3, //0x000016e7 addq         %r14, %rbx
	//0x000016ea LBB0_315
	0x4d, 0x85, 0xff, //0x000016ea testq        %r15, %r15
	0x4c, 0x8b, 0x4c, 0x24, 0x30, //0x000016ed movq         $48(%rsp), %r9
	0x48, 0x8d, 0x35, 0x7b, 0x20, 0x00, 0x00, //0x000016f2 leaq         $8315(%rip), %rsi  /* LJTI0_3+0(%rip) */
	0x0f, 0x84, 0xf4, 0x02, 0x00, 0x00, //0x000016f9 je           LBB0_367
	0x4e, 0x8d, 0x34, 0x3b, //0x000016ff leaq         (%rbx,%r15), %r14
	0x48, 0x89, 0xd8, //0x00001703 movq         %rbx, %rax
	0x4c, 0x29, 0xd0, //0x00001706 subq         %r10, %rax
	0x4c, 0x89, 0xef, //0x00001709 movq         %r13, %rdi
	0x48, 0xf7, 0xd7, //0x0000170c notq         %rdi
	0x48, 0x01, 0xc7, //0x0000170f addq         %rax, %rdi
	0x31, 0xc9, //0x00001712 xorl         %ecx, %ecx
	0xe9, 0x14, 0x00, 0x00, 0x00, //0x00001714 jmp          LBB0_318
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001719 .p2align 4, 0x90
	//0x00001720 LBB0_317
	0x48, 0x83, 0xc1, 0x01, //0x00001720 addq         $1, %rcx
	0x49, 0x39, 0xcf, //0x00001724 cmpq         %rcx, %r15
	0x0f, 0x84, 0x13, 0x06, 0x00, 0x00, //0x00001727 je           LBB0_395
	//0x0000172d LBB0_318
	0x0f, 0xbe, 0x14, 0x0b, //0x0000172d movsbl       (%rbx,%rcx), %edx
	0x8d, 0x42, 0xd0, //0x00001731 leal         $-48(%rdx), %eax
	0x83, 0xf8, 0x0a, //0x00001734 cmpl         $10, %eax
	0x0f, 0x82, 0xe3, 0xff, 0xff, 0xff, //0x00001737 jb           LBB0_317
	0x8d, 0x42, 0xd5, //0x0000173d leal         $-43(%rdx), %eax
	0x83, 0xf8, 0x1a, //0x00001740 cmpl         $26, %eax
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x00001743 ja           LBB0_323
	0x48, 0x63, 0x04, 0x86, //0x00001749 movslq       (%rsi,%rax,4), %rax
	0x48, 0x01, 0xf0, //0x0000174d addq         %rsi, %rax
	0xff, 0xe0, //0x00001750 jmpq         *%rax
	//0x00001752 LBB0_321
	0x49, 0x83, 0xf8, 0xff, //0x00001752 cmpq         $-1, %r8
	0x0f, 0x85, 0x49, 0x07, 0x00, 0x00, //0x00001756 jne          LBB0_406
	0x4c, 0x8d, 0x04, 0x0f, //0x0000175c leaq         (%rdi,%rcx), %r8
	0xe9, 0xbb, 0xff, 0xff, 0xff, //0x00001760 jmp          LBB0_317
	//0x00001765 LBB0_323
	0x83, 0xfa, 0x65, //0x00001765 cmpl         $101, %edx
	0x0f, 0x85, 0x82, 0x02, 0x00, 0x00, //0x00001768 jne          LBB0_366
	//0x0000176e LBB0_324
	0x49, 0x83, 0xfb, 0xff, //0x0000176e cmpq         $-1, %r11
	0x0f, 0x85, 0x2d, 0x07, 0x00, 0x00, //0x00001772 jne          LBB0_406
	0x4c, 0x8d, 0x1c, 0x0f, //0x00001778 leaq         (%rdi,%rcx), %r11
	0xe9, 0x9f, 0xff, 0xff, 0xff, //0x0000177c jmp          LBB0_317
	//0x00001781 LBB0_326
	0x49, 0x83, 0xfc, 0xff, //0x00001781 cmpq         $-1, %r12
	0x0f, 0x85, 0x1a, 0x07, 0x00, 0x00, //0x00001785 jne          LBB0_406
	0x4c, 0x8d, 0x24, 0x0f, //0x0000178b leaq         (%rdi,%rcx), %r12
	0xe9, 0x8c, 0xff, 0xff, 0xff, //0x0000178f jmp          LBB0_317
	//0x00001794 LBB0_328
	0x48, 0x8b, 0x02, //0x00001794 movq         (%rdx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001797 cmpq         $4095, %rax
	0x0f, 0x8f, 0x0c, 0x12, 0x00, 0x00, //0x0000179d jg           LBB0_631
	0x48, 0x8d, 0x48, 0x01, //0x000017a3 leaq         $1(%rax), %rcx
	0x48, 0x89, 0x0a, //0x000017a7 movq         %rcx, (%rdx)
	0x48, 0xc7, 0x44, 0xc2, 0x08, 0x05, 0x00, 0x00, 0x00, //0x000017aa movq         $5, $8(%rdx,%rax,8)
	0xe9, 0x56, 0xed, 0xff, 0xff, //0x000017b3 jmp          LBB0_35
	//0x000017b8 LBB0_330
	0x48, 0x8b, 0x47, 0x08, //0x000017b8 movq         $8(%rdi), %rax
	0x48, 0x8d, 0x48, 0xfd, //0x000017bc leaq         $-3(%rax), %rcx
	0x49, 0x39, 0xca, //0x000017c0 cmpq         %rcx, %r10
	0x0f, 0x83, 0x8b, 0x19, 0x00, 0x00, //0x000017c3 jae          LBB0_630
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x000017c9 movq         $16(%rsp), %rax
	0x81, 0x38, 0x6e, 0x75, 0x6c, 0x6c, //0x000017ce cmpl         $1819047278, (%rax)
	0x0f, 0x84, 0x27, 0x00, 0x00, 0x00, //0x000017d4 je           LBB0_338
	0xe9, 0xed, 0x19, 0x00, 0x00, //0x000017da jmp          LBB0_332
	//0x000017df LBB0_336
	0x48, 0x8b, 0x47, 0x08, //0x000017df movq         $8(%rdi), %rax
	0x48, 0x8d, 0x48, 0xfd, //0x000017e3 leaq         $-3(%rax), %rcx
	0x49, 0x39, 0xca, //0x000017e7 cmpq         %rcx, %r10
	0x0f, 0x83, 0x64, 0x19, 0x00, 0x00, //0x000017ea jae          LBB0_630
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x000017f0 movq         $16(%rsp), %rax
	0x81, 0x38, 0x74, 0x72, 0x75, 0x65, //0x000017f5 cmpl         $1702195828, (%rax)
	0x0f, 0x85, 0x1c, 0x1a, 0x00, 0x00, //0x000017fb jne          LBB0_637
	//0x00001801 LBB0_338
	0x4d, 0x8d, 0x62, 0x04, //0x00001801 leaq         $4(%r10), %r12
	0xe9, 0xeb, 0xec, 0xff, 0xff, //0x00001805 jmp          LBB0_34
	//0x0000180a LBB0_339
	0x48, 0x8b, 0x02, //0x0000180a movq         (%rdx), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x0000180d cmpq         $4095, %rax
	0x0f, 0x8f, 0x96, 0x11, 0x00, 0x00, //0x00001813 jg           LBB0_631
	0x48, 0x8d, 0x48, 0x01, //0x00001819 leaq         $1(%rax), %rcx
	0x48, 0x89, 0x0a, //0x0000181d movq         %rcx, (%rdx)
	0x48, 0xc7, 0x44, 0xc2, 0x08, 0x06, 0x00, 0x00, 0x00, //0x00001820 movq         $6, $8(%rdx,%rax,8)
	0xe9, 0xe0, 0xec, 0xff, 0xff, //0x00001829 jmp          LBB0_35
	//0x0000182e LBB0_341
	0x49, 0x8d, 0x41, 0xff, //0x0000182e leaq         $-1(%r9), %rax
	0x49, 0x39, 0xc0, //0x00001832 cmpq         %rax, %r8
	0x49, 0xf7, 0xd1, //0x00001835 notq         %r9
	0x4d, 0x0f, 0x45, 0xcf, //0x00001838 cmovneq      %r15, %r9
	0x84, 0xc9, //0x0000183c testb        %cl, %cl
	0x4d, 0x0f, 0x45, 0xf9, //0x0000183e cmovneq      %r9, %r15
	0xe9, 0x04, 0x04, 0x00, 0x00, //0x00001842 jmp          LBB0_390
	//0x00001847 LBB0_342
	0x4c, 0x29, 0xe3, //0x00001847 subq         %r12, %rbx
	0x0f, 0x84, 0x4e, 0x1a, 0x00, 0x00, //0x0000184a je           LBB0_656
	0x4d, 0x89, 0xce, //0x00001850 movq         %r9, %r14
	0x48, 0x83, 0xfb, 0x40, //0x00001853 cmpq         $64, %rbx
	0x0f, 0x82, 0x65, 0x07, 0x00, 0x00, //0x00001857 jb           LBB0_418
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x0000185d movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc9, //0x00001866 xorl         %r9d, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001869 .p2align 4, 0x90
	//0x00001870 LBB0_345
	0xc4, 0x81, 0x7e, 0x6f, 0x44, 0x25, 0x00, //0x00001870 vmovdqu      (%r13,%r12), %ymm0
	0xc4, 0x81, 0x7e, 0x6f, 0x4c, 0x25, 0x20, //0x00001877 vmovdqu      $32(%r13,%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x0000187e vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00001882 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00001886 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x0000188a vpmovmskb    %ymm2, %ecx
	0xc5, 0xfd, 0x74, 0xd7, //0x0000188e vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001892 vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd7, //0x00001896 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x0000189a vpmovmskb    %ymm2, %eax
	0xc5, 0xbd, 0xda, 0xd1, //0x0000189e vpminub      %ymm1, %ymm8, %ymm2
	0xc5, 0xf5, 0x74, 0xca, //0x000018a2 vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x000018a6 vpmovmskb    %ymm1, %esi
	0x48, 0xc1, 0xe1, 0x20, //0x000018aa shlq         $32, %rcx
	0x48, 0x09, 0xcf, //0x000018ae orq          %rcx, %rdi
	0x48, 0xc1, 0xe0, 0x20, //0x000018b1 shlq         $32, %rax
	0x48, 0xc1, 0xe6, 0x20, //0x000018b5 shlq         $32, %rsi
	0x48, 0x09, 0xc2, //0x000018b9 orq          %rax, %rdx
	0x0f, 0x85, 0x44, 0x00, 0x00, 0x00, //0x000018bc jne          LBB0_356
	0x4d, 0x85, 0xc9, //0x000018c2 testq        %r9, %r9
	0x0f, 0x85, 0x5d, 0x00, 0x00, 0x00, //0x000018c5 jne          LBB0_358
	0x45, 0x31, 0xc9, //0x000018cb xorl         %r9d, %r9d
	//0x000018ce LBB0_348
	0xc5, 0xbd, 0xda, 0xc8, //0x000018ce vpminub      %ymm0, %ymm8, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x000018d2 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000018d6 vpmovmskb    %ymm0, %eax
	0x48, 0x09, 0xc6, //0x000018da orq          %rax, %rsi
	0x48, 0x85, 0xff, //0x000018dd testq        %rdi, %rdi
	0x0f, 0x85, 0x9a, 0x00, 0x00, 0x00, //0x000018e0 jne          LBB0_196
	0x48, 0x85, 0xf6, //0x000018e6 testq        %rsi, %rsi
	0x0f, 0x85, 0x2a, 0x18, 0x00, 0x00, //0x000018e9 jne          LBB0_645
	0x48, 0x83, 0xc3, 0xc0, //0x000018ef addq         $-64, %rbx
	0x49, 0x83, 0xc4, 0x40, //0x000018f3 addq         $64, %r12
	0x48, 0x83, 0xfb, 0x3f, //0x000018f7 cmpq         $63, %rbx
	0x0f, 0x87, 0x6f, 0xff, 0xff, 0xff, //0x000018fb ja           LBB0_345
	0xe9, 0x4b, 0x05, 0x00, 0x00, //0x00001901 jmp          LBB0_351
	//0x00001906 LBB0_356
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00001906 movq         %r11, $24(%rsp)
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x0000190b cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x00001911 jne          LBB0_359
	0x48, 0x0f, 0xbc, 0xc2, //0x00001917 bsfq         %rdx, %rax
	0x4c, 0x01, 0xe0, //0x0000191b addq         %r12, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x0000191e movq         %rax, $16(%rsp)
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00001923 jmp          LBB0_359
	//0x00001928 LBB0_358
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00001928 movq         %r11, $24(%rsp)
	//0x0000192d LBB0_359
	0x4c, 0x89, 0xc8, //0x0000192d movq         %r9, %rax
	0x48, 0xf7, 0xd0, //0x00001930 notq         %rax
	0x48, 0x21, 0xd0, //0x00001933 andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x00001936 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xcb, //0x0000193a orq          %r9, %r11
	0x4c, 0x89, 0xd9, //0x0000193d movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00001940 notq         %rcx
	0x48, 0x21, 0xd1, //0x00001943 andq         %rdx, %rcx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001946 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x00001950 andq         %rdx, %rcx
	0x45, 0x31, 0xc9, //0x00001953 xorl         %r9d, %r9d
	0x48, 0x01, 0xc1, //0x00001956 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc1, //0x00001959 setb         %r9b
	0x48, 0x01, 0xc9, //0x0000195d addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001960 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x0000196a xorq         %rax, %rcx
	0x4c, 0x21, 0xd9, //0x0000196d andq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00001970 notq         %rcx
	0x48, 0x21, 0xcf, //0x00001973 andq         %rcx, %rdi
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001976 movq         $24(%rsp), %r11
	0xe9, 0x4e, 0xff, 0xff, 0xff, //0x0000197b jmp          LBB0_348
	//0x00001980 LBB0_196
	0x48, 0x0f, 0xbc, 0xc7, //0x00001980 bsfq         %rdi, %rax
	0x48, 0x85, 0xf6, //0x00001984 testq        %rsi, %rsi
	0x0f, 0x84, 0x09, 0x00, 0x00, 0x00, //0x00001987 je           LBB0_219
	0x48, 0x0f, 0xbc, 0xf6, //0x0000198d bsfq         %rsi, %rsi
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00001991 jmp          LBB0_220
	//0x00001996 LBB0_219
	0xbe, 0x40, 0x00, 0x00, 0x00, //0x00001996 movl         $64, %esi
	//0x0000199b LBB0_220
	0x4d, 0x89, 0xf1, //0x0000199b movq         %r14, %r9
	0x48, 0x39, 0xc6, //0x0000199e cmpq         %rax, %rsi
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x000019a1 movq         $32(%rsp), %rdi
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x000019a6 movq         $40(%rsp), %rdx
	0x0f, 0x82, 0x08, 0x19, 0x00, 0x00, //0x000019ab jb           LBB0_363
	0x49, 0x01, 0xc4, //0x000019b1 addq         %rax, %r12
	0x49, 0x83, 0xc4, 0x01, //0x000019b4 addq         $1, %r12
	0xe9, 0x2f, 0xeb, 0xff, 0xff, //0x000019b8 jmp          LBB0_33
	//0x000019bd LBB0_361
	0x48, 0x01, 0xd9, //0x000019bd addq         %rbx, %rcx
	0x4c, 0x01, 0xf1, //0x000019c0 addq         %r14, %rcx
	0xc5, 0xf8, 0x77, //0x000019c3 vzeroupper   
	0x48, 0x89, 0xcb, //0x000019c6 movq         %rcx, %rbx
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x000019c9 jmp          LBB0_368
	//0x000019ce LBB0_362
	0x4d, 0x89, 0xf7, //0x000019ce movq         %r14, %r15
	0xe9, 0x45, 0xf7, 0xff, 0xff, //0x000019d1 jmp          LBB0_224
	//0x000019d6 LBB0_364
	0x0f, 0xbc, 0xc0, //0x000019d6 bsfl         %eax, %eax
	0xe9, 0x75, 0x01, 0x00, 0x00, //0x000019d9 jmp          LBB0_384
	//0x000019de LBB0_365
	0x89, 0xc8, //0x000019de movl         %ecx, %eax
	0x48, 0x01, 0xc3, //0x000019e0 addq         %rax, %rbx
	0x4c, 0x01, 0xf3, //0x000019e3 addq         %r14, %rbx
	0x4c, 0x8b, 0x4c, 0x24, 0x30, //0x000019e6 movq         $48(%rsp), %r9
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000019eb jmp          LBB0_367
	//0x000019f0 LBB0_366
	0x48, 0x01, 0xcb, //0x000019f0 addq         %rcx, %rbx
	//0x000019f3 LBB0_367
	0x4c, 0x8b, 0x74, 0x24, 0x10, //0x000019f3 movq         $16(%rsp), %r14
	//0x000019f8 LBB0_368
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000019f8 movq         $-1, %rcx
	0x4d, 0x85, 0xe4, //0x000019ff testq        %r12, %r12
	0x0f, 0x84, 0x70, 0x18, 0x00, 0x00, //0x00001a02 je           LBB0_643
	0x4d, 0x85, 0xc0, //0x00001a08 testq        %r8, %r8
	0x0f, 0x84, 0x67, 0x18, 0x00, 0x00, //0x00001a0b je           LBB0_643
	0x4d, 0x85, 0xdb, //0x00001a11 testq        %r11, %r11
	0x0f, 0x84, 0x5e, 0x18, 0x00, 0x00, //0x00001a14 je           LBB0_643
	0x4c, 0x29, 0xf3, //0x00001a1a subq         %r14, %rbx
	0x48, 0x8d, 0x4b, 0xff, //0x00001a1d leaq         $-1(%rbx), %rcx
	0x49, 0x39, 0xcc, //0x00001a21 cmpq         %rcx, %r12
	0x0f, 0x84, 0x8b, 0x00, 0x00, 0x00, //0x00001a24 je           LBB0_377
	0x49, 0x39, 0xc8, //0x00001a2a cmpq         %rcx, %r8
	0x0f, 0x84, 0x82, 0x00, 0x00, 0x00, //0x00001a2d je           LBB0_377
	0x49, 0x39, 0xcb, //0x00001a33 cmpq         %rcx, %r11
	0x0f, 0x84, 0x79, 0x00, 0x00, 0x00, //0x00001a36 je           LBB0_377
	0x4d, 0x85, 0xc0, //0x00001a3c testq        %r8, %r8
	0xc5, 0xfe, 0x6f, 0x2d, 0xb9, 0xe5, 0xff, 0xff, //0x00001a3f vmovdqu      $-6727(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x31, 0xe6, 0xff, 0xff, //0x00001a47 vmovdqu      $-6607(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x49, 0xe6, 0xff, 0xff, //0x00001a4f vmovdqu      $-6583(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xc1, 0xe6, 0xff, 0xff, //0x00001a57 vmovdqu      $-6463(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0xd9, 0xe6, 0xff, 0xff, //0x00001a5f vmovdqu      $-6439(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xf1, 0xe6, 0xff, 0xff, //0x00001a67 vmovdqu      $-6415(%rip), %ymm10  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xc9, 0xe5, 0xff, 0xff, //0x00001a6f vmovdqu      $-6711(%rip), %ymm11  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x01, 0xe7, 0xff, 0xff, //0x00001a77 vmovdqu      $-6399(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x19, 0xe7, 0xff, 0xff, //0x00001a7f vmovdqu      $-6375(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x31, 0xe7, 0xff, 0xff, //0x00001a87 vmovdqu      $-6351(%rip), %ymm14  /* LCPI0_18+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x49, 0xe7, 0xff, 0xff, //0x00001a8f vmovdqu      $-6327(%rip), %ymm15  /* LCPI0_19+0(%rip) */
	0x0f, 0x8e, 0x7d, 0x00, 0x00, 0x00, //0x00001a97 jle          LBB0_379
	0x49, 0x8d, 0x40, 0xff, //0x00001a9d leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc3, //0x00001aa1 cmpq         %rax, %r11
	0x0f, 0x84, 0x70, 0x00, 0x00, 0x00, //0x00001aa4 je           LBB0_379
	0x49, 0xf7, 0xd0, //0x00001aaa notq         %r8
	0x4c, 0x89, 0xc3, //0x00001aad movq         %r8, %rbx
	0xe9, 0xa3, 0x02, 0x00, 0x00, //0x00001ab0 jmp          LBB0_399
	//0x00001ab5 LBB0_377
	0x48, 0xf7, 0xdb, //0x00001ab5 negq         %rbx
	//0x00001ab8 LBB0_378
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001ab8 movq         $24(%rsp), %r11
	0xc5, 0xfe, 0x6f, 0x2d, 0x3b, 0xe5, 0xff, 0xff, //0x00001abd vmovdqu      $-6853(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xb3, 0xe5, 0xff, 0xff, //0x00001ac5 vmovdqu      $-6733(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xcb, 0xe5, 0xff, 0xff, //0x00001acd vmovdqu      $-6709(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x43, 0xe6, 0xff, 0xff, //0x00001ad5 vmovdqu      $-6589(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x5b, 0xe6, 0xff, 0xff, //0x00001add vmovdqu      $-6565(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x73, 0xe6, 0xff, 0xff, //0x00001ae5 vmovdqu      $-6541(%rip), %ymm10  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x4b, 0xe5, 0xff, 0xff, //0x00001aed vmovdqu      $-6837(%rip), %ymm11  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x83, 0xe6, 0xff, 0xff, //0x00001af5 vmovdqu      $-6525(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x9b, 0xe6, 0xff, 0xff, //0x00001afd vmovdqu      $-6501(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xb3, 0xe6, 0xff, 0xff, //0x00001b05 vmovdqu      $-6477(%rip), %ymm14  /* LCPI0_18+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xcb, 0xe6, 0xff, 0xff, //0x00001b0d vmovdqu      $-6453(%rip), %ymm15  /* LCPI0_19+0(%rip) */
	0xe9, 0x43, 0x02, 0x00, 0x00, //0x00001b15 jmp          LBB0_400
	//0x00001b1a LBB0_379
	0x4c, 0x89, 0xe0, //0x00001b1a movq         %r12, %rax
	0x4c, 0x09, 0xd8, //0x00001b1d orq          %r11, %rax
	0x0f, 0x99, 0xc1, //0x00001b20 setns        %cl
	0x0f, 0x88, 0xd4, 0x00, 0x00, 0x00, //0x00001b23 js           LBB0_385
	0x4d, 0x39, 0xdc, //0x00001b29 cmpq         %r11, %r12
	0x0f, 0x8c, 0xcb, 0x00, 0x00, 0x00, //0x00001b2c jl           LBB0_385
	0x49, 0xf7, 0xd4, //0x00001b32 notq         %r12
	0x4c, 0x89, 0xe3, //0x00001b35 movq         %r12, %rbx
	0xe9, 0x1b, 0x02, 0x00, 0x00, //0x00001b38 jmp          LBB0_399
	//0x00001b3d LBB0_382
	0x4d, 0x29, 0xea, //0x00001b3d subq         %r13, %r10
	0x49, 0x01, 0xca, //0x00001b40 addq         %rcx, %r10
	0x49, 0x39, 0xda, //0x00001b43 cmpq         %rbx, %r10
	0x0f, 0x82, 0x8e, 0xeb, 0xff, 0xff, //0x00001b46 jb           LBB0_67
	0xe9, 0x3f, 0x0e, 0x00, 0x00, //0x00001b4c jmp          LBB0_554
	//0x00001b51 LBB0_383
	0x89, 0xd0, //0x00001b51 movl         %edx, %eax
	//0x00001b53 LBB0_384
	0x49, 0xf7, 0xd7, //0x00001b53 notq         %r15
	0x49, 0x29, 0xc7, //0x00001b56 subq         %rax, %r15
	0xe9, 0xed, 0x00, 0x00, 0x00, //0x00001b59 jmp          LBB0_390
	//0x00001b5e LBB0_85
	0x4d, 0x01, 0xec, //0x00001b5e addq         %r13, %r12
	0x48, 0x83, 0xfb, 0x20, //0x00001b61 cmpq         $32, %rbx
	0x0f, 0x82, 0xe4, 0x04, 0x00, 0x00, //0x00001b65 jb           LBB0_423
	//0x00001b6b LBB0_86
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001b6b vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001b71 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001b75 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x00001b79 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00001b7d vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x00001b81 testl        %edx, %edx
	0x0f, 0x85, 0x68, 0x04, 0x00, 0x00, //0x00001b83 jne          LBB0_419
	0x4d, 0x85, 0xc9, //0x00001b89 testq        %r9, %r9
	0x0f, 0x85, 0x7d, 0x04, 0x00, 0x00, //0x00001b8c jne          LBB0_421
	0x45, 0x31, 0xc9, //0x00001b92 xorl         %r9d, %r9d
	0x48, 0x85, 0xf6, //0x00001b95 testq        %rsi, %rsi
	0x0f, 0x84, 0xa9, 0x04, 0x00, 0x00, //0x00001b98 je           LBB0_422
	//0x00001b9e LBB0_89
	0x48, 0x0f, 0xbc, 0xc6, //0x00001b9e bsfq         %rsi, %rax
	0x4d, 0x29, 0xec, //0x00001ba2 subq         %r13, %r12
	0x49, 0x01, 0xc4, //0x00001ba5 addq         %rax, %r12
	0x49, 0x83, 0xc4, 0x01, //0x00001ba8 addq         $1, %r12
	0xe9, 0x2b, 0xe9, 0xff, 0xff, //0x00001bac jmp          LBB0_32
	//0x00001bb1 LBB0_187
	0x4d, 0x01, 0xec, //0x00001bb1 addq         %r13, %r12
	0x4c, 0x89, 0xd8, //0x00001bb4 movq         %r11, %rax
	0x48, 0x83, 0xfb, 0x20, //0x00001bb7 cmpq         $32, %rbx
	0x0f, 0x82, 0x6f, 0x03, 0x00, 0x00, //0x00001bbb jb           LBB0_412
	//0x00001bc1 LBB0_188
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001bc1 vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001bc7 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xd9, //0x00001bcb vpmovmskb    %ymm1, %r11d
	0xc5, 0xfd, 0x74, 0xcf, //0x00001bcf vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001bd3 vpmovmskb    %ymm1, %edx
	0x85, 0xd2, //0x00001bd7 testl        %edx, %edx
	0x0f, 0x85, 0x28, 0x05, 0x00, 0x00, //0x00001bd9 jne          LBB0_436
	0x4d, 0x85, 0xc9, //0x00001bdf testq        %r9, %r9
	0x0f, 0x85, 0x3d, 0x05, 0x00, 0x00, //0x00001be2 jne          LBB0_438
	0x45, 0x31, 0xc9, //0x00001be8 xorl         %r9d, %r9d
	0x4d, 0x85, 0xdb, //0x00001beb testq        %r11, %r11
	0x0f, 0x84, 0x6a, 0x05, 0x00, 0x00, //0x00001bee je           LBB0_439
	//0x00001bf4 LBB0_191
	0x49, 0x0f, 0xbc, 0xd3, //0x00001bf4 bsfq         %r11, %rdx
	0xe9, 0x66, 0x05, 0x00, 0x00, //0x00001bf8 jmp          LBB0_440
	//0x00001bfd LBB0_385
	0x49, 0x8d, 0x43, 0xff, //0x00001bfd leaq         $-1(%r11), %rax
	0x49, 0x39, 0xc4, //0x00001c01 cmpq         %rax, %r12
	0x49, 0xf7, 0xd3, //0x00001c04 notq         %r11
	0x4c, 0x0f, 0x45, 0xdb, //0x00001c07 cmovneq      %rbx, %r11
	0x84, 0xc9, //0x00001c0b testb        %cl, %cl
	0x49, 0x0f, 0x45, 0xdb, //0x00001c0d cmovneq      %r11, %rbx
	0xe9, 0x42, 0x01, 0x00, 0x00, //0x00001c11 jmp          LBB0_399
	//0x00001c16 LBB0_386
	0x0f, 0xbc, 0xc0, //0x00001c16 bsfl         %eax, %eax
	0xe9, 0x16, 0x00, 0x00, 0x00, //0x00001c19 jmp          LBB0_389
	//0x00001c1e LBB0_387
	0x4d, 0x01, 0xd5, //0x00001c1e addq         %r10, %r13
	0x4d, 0x29, 0xfd, //0x00001c21 subq         %r15, %r13
	0x48, 0xf7, 0xd1, //0x00001c24 notq         %rcx
	0x4c, 0x01, 0xe9, //0x00001c27 addq         %r13, %rcx
	0x49, 0x89, 0xcf, //0x00001c2a movq         %rcx, %r15
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x00001c2d jmp          LBB0_390
	//0x00001c32 LBB0_388
	0x89, 0xd0, //0x00001c32 movl         %edx, %eax
	//0x00001c34 LBB0_389
	0x4d, 0x01, 0xd5, //0x00001c34 addq         %r10, %r13
	0x4d, 0x29, 0xfd, //0x00001c37 subq         %r15, %r13
	0x49, 0x29, 0xc5, //0x00001c3a subq         %rax, %r13
	0x49, 0xf7, 0xd6, //0x00001c3d notq         %r14
	0x4d, 0x01, 0xee, //0x00001c40 addq         %r13, %r14
	0x4d, 0x89, 0xf7, //0x00001c43 movq         %r14, %r15
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001c46 movq         $24(%rsp), %r11
	//0x00001c4b LBB0_390
	0x4d, 0x85, 0xff, //0x00001c4b testq        %r15, %r15
	0x4c, 0x8b, 0x4c, 0x24, 0x30, //0x00001c4e movq         $48(%rsp), %r9
	0x0f, 0x88, 0x55, 0x14, 0x00, 0x00, //0x00001c53 js           LBB0_621
	0x4d, 0x8b, 0x21, //0x00001c59 movq         (%r9), %r12
	//0x00001c5c LBB0_392
	0x4d, 0x01, 0xfc, //0x00001c5c addq         %r15, %r12
	0x49, 0x83, 0xc4, 0xff, //0x00001c5f addq         $-1, %r12
	//0x00001c63 LBB0_393
	0x4d, 0x89, 0x21, //0x00001c63 movq         %r12, (%r9)
	0x4d, 0x89, 0xd6, //0x00001c66 movq         %r10, %r14
	0x48, 0xb8, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00001c69 movabsq      $9223372036854775806, %rax
	0x49, 0x39, 0xc2, //0x00001c73 cmpq         %rax, %r10
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00001c76 movq         $32(%rsp), %rdi
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x00001c7b movq         $40(%rsp), %rdx
	0x4c, 0x8b, 0x7c, 0x24, 0x38, //0x00001c80 movq         $56(%rsp), %r15
	0x0f, 0x86, 0x83, 0xe8, 0xff, 0xff, //0x00001c85 jbe          LBB0_35
	0xe9, 0xaf, 0x14, 0x00, 0x00, //0x00001c8b jmp          LBB0_650
	//0x00001c90 LBB0_108
	0x4d, 0x01, 0xec, //0x00001c90 addq         %r13, %r12
	0x48, 0x83, 0xfb, 0x20, //0x00001c93 cmpq         $32, %rbx
	0x0f, 0x82, 0x26, 0x06, 0x00, 0x00, //0x00001c97 jb           LBB0_461
	//0x00001c9d LBB0_109
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001c9d vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001ca3 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001ca7 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x00001cab vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00001caf vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x00001cb3 testl        %edx, %edx
	0x0f, 0x85, 0xaa, 0x05, 0x00, 0x00, //0x00001cb5 jne          LBB0_457
	0x4d, 0x85, 0xc9, //0x00001cbb testq        %r9, %r9
	0x0f, 0x85, 0xbf, 0x05, 0x00, 0x00, //0x00001cbe jne          LBB0_459
	0x45, 0x31, 0xc9, //0x00001cc4 xorl         %r9d, %r9d
	0x48, 0x85, 0xf6, //0x00001cc7 testq        %rsi, %rsi
	0x0f, 0x84, 0xeb, 0x05, 0x00, 0x00, //0x00001cca je           LBB0_460
	//0x00001cd0 LBB0_112
	0x48, 0x0f, 0xbc, 0xc6, //0x00001cd0 bsfq         %rsi, %rax
	0x4d, 0x29, 0xec, //0x00001cd4 subq         %r13, %r12
	0x49, 0x01, 0xc4, //0x00001cd7 addq         %rax, %r12
	0x49, 0x83, 0xc4, 0x01, //0x00001cda addq         $1, %r12
	0xe9, 0x64, 0xf1, 0xff, 0xff, //0x00001cde jmp          LBB0_177
	//0x00001ce3 LBB0_208
	0x4d, 0x01, 0xec, //0x00001ce3 addq         %r13, %r12
	0x4c, 0x89, 0xd8, //0x00001ce6 movq         %r11, %rax
	0x48, 0x83, 0xfb, 0x20, //0x00001ce9 cmpq         $32, %rbx
	0x0f, 0x82, 0x7f, 0x02, 0x00, 0x00, //0x00001ced jb           LBB0_415
	//0x00001cf3 LBB0_209
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001cf3 vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001cf9 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xd9, //0x00001cfd vpmovmskb    %ymm1, %r11d
	0xc5, 0xfd, 0x74, 0xcf, //0x00001d01 vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001d05 vpmovmskb    %ymm1, %edx
	0x85, 0xd2, //0x00001d09 testl        %edx, %edx
	0x0f, 0x85, 0x6a, 0x06, 0x00, 0x00, //0x00001d0b jne          LBB0_474
	0x4d, 0x85, 0xc9, //0x00001d11 testq        %r9, %r9
	0x0f, 0x85, 0x7f, 0x06, 0x00, 0x00, //0x00001d14 jne          LBB0_476
	0x45, 0x31, 0xc9, //0x00001d1a xorl         %r9d, %r9d
	0x4d, 0x85, 0xdb, //0x00001d1d testq        %r11, %r11
	0x0f, 0x84, 0xac, 0x06, 0x00, 0x00, //0x00001d20 je           LBB0_477
	//0x00001d26 LBB0_212
	0x49, 0x0f, 0xbc, 0xd3, //0x00001d26 bsfq         %r11, %rdx
	0xe9, 0xa8, 0x06, 0x00, 0x00, //0x00001d2a jmp          LBB0_478
	//0x00001d2f LBB0_394
	0x4d, 0x01, 0xea, //0x00001d2f addq         %r13, %r10
	0x48, 0x85, 0xf6, //0x00001d32 testq        %rsi, %rsi
	0x0f, 0x85, 0x2c, 0xe9, 0xff, 0xff, //0x00001d35 jne          LBB0_60
	0xe9, 0x63, 0xe9, 0xff, 0xff, //0x00001d3b jmp          LBB0_65
	//0x00001d40 LBB0_395
	0x4c, 0x89, 0xf3, //0x00001d40 movq         %r14, %rbx
	0xe9, 0xab, 0xfc, 0xff, 0xff, //0x00001d43 jmp          LBB0_367
	//0x00001d48 LBB0_396
	0x0f, 0xbc, 0xc0, //0x00001d48 bsfl         %eax, %eax
	0xe9, 0x02, 0x00, 0x00, 0x00, //0x00001d4b jmp          LBB0_398
	//0x00001d50 LBB0_397
	0x89, 0xd0, //0x00001d50 movl         %edx, %eax
	//0x00001d52 LBB0_398
	0x48, 0xf7, 0xd3, //0x00001d52 notq         %rbx
	0x48, 0x29, 0xc3, //0x00001d55 subq         %rax, %rbx
	//0x00001d58 LBB0_399
	0x4c, 0x8b, 0x5c, 0x24, 0x18, //0x00001d58 movq         $24(%rsp), %r11
	//0x00001d5d LBB0_400
	0x48, 0x85, 0xdb, //0x00001d5d testq        %rbx, %rbx
	0x0f, 0x88, 0x0f, 0x15, 0x00, 0x00, //0x00001d60 js           LBB0_642
	0x4d, 0x8b, 0x21, //0x00001d66 movq         (%r9), %r12
	//0x00001d69 LBB0_402
	0x49, 0x01, 0xdc, //0x00001d69 addq         %rbx, %r12
	0xe9, 0xf2, 0xfe, 0xff, 0xff, //0x00001d6c jmp          LBB0_393
	//0x00001d71 LBB0_255
	0x4d, 0x01, 0xec, //0x00001d71 addq         %r13, %r12
	0x48, 0x83, 0xfb, 0x20, //0x00001d74 cmpq         $32, %rbx
	0x0f, 0x82, 0x3b, 0x00, 0x00, 0x00, //0x00001d78 jb           LBB0_260
	//0x00001d7e LBB0_256
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001d7e vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001d84 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001d88 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x00001d8c vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00001d90 vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x00001d94 testl        %edx, %edx
	0x0f, 0x85, 0x56, 0x08, 0x00, 0x00, //0x00001d96 jne          LBB0_505
	0x4d, 0x85, 0xc9, //0x00001d9c testq        %r9, %r9
	0x0f, 0x85, 0x6b, 0x08, 0x00, 0x00, //0x00001d9f jne          LBB0_507
	0x45, 0x31, 0xc9, //0x00001da5 xorl         %r9d, %r9d
	0x48, 0x85, 0xf6, //0x00001da8 testq        %rsi, %rsi
	0x0f, 0x85, 0xed, 0xfd, 0xff, 0xff, //0x00001dab jne          LBB0_89
	//0x00001db1 LBB0_259
	0x49, 0x83, 0xc4, 0x20, //0x00001db1 addq         $32, %r12
	0x48, 0x83, 0xc3, 0xe0, //0x00001db5 addq         $-32, %rbx
	//0x00001db9 LBB0_260
	0x4d, 0x85, 0xc9, //0x00001db9 testq        %r9, %r9
	0x0f, 0x85, 0xaf, 0x09, 0x00, 0x00, //0x00001dbc jne          LBB0_526
	0x4c, 0x8b, 0x4c, 0x24, 0x10, //0x00001dc2 movq         $16(%rsp), %r9
	0x48, 0x85, 0xdb, //0x00001dc7 testq        %rbx, %rbx
	0x0f, 0x84, 0x1e, 0x03, 0x00, 0x00, //0x00001dca je           LBB0_435
	//0x00001dd0 LBB0_262
	0x4c, 0x89, 0xee, //0x00001dd0 movq         %r13, %rsi
	0x48, 0xf7, 0xde, //0x00001dd3 negq         %rsi
	//0x00001dd6 LBB0_263
	0x31, 0xff, //0x00001dd6 xorl         %edi, %edi
	//0x00001dd8 LBB0_264
	0x41, 0x0f, 0xb6, 0x14, 0x3c, //0x00001dd8 movzbl       (%r12,%rdi), %edx
	0x80, 0xfa, 0x22, //0x00001ddd cmpb         $34, %dl
	0x0f, 0x84, 0x01, 0x03, 0x00, 0x00, //0x00001de0 je           LBB0_434
	0x80, 0xfa, 0x5c, //0x00001de6 cmpb         $92, %dl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00001de9 je           LBB0_403
	0x48, 0x83, 0xc7, 0x01, //0x00001def addq         $1, %rdi
	0x48, 0x39, 0xfb, //0x00001df3 cmpq         %rdi, %rbx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00001df6 jne          LBB0_264
	0xe9, 0xe2, 0x01, 0x00, 0x00, //0x00001dfc jmp          LBB0_267
	//0x00001e01 LBB0_403
	0x48, 0x8d, 0x43, 0xff, //0x00001e01 leaq         $-1(%rbx), %rax
	0x48, 0x39, 0xf8, //0x00001e05 cmpq         %rdi, %rax
	0x0f, 0x84, 0x09, 0x15, 0x00, 0x00, //0x00001e08 je           LBB0_268
	0x4a, 0x8d, 0x04, 0x26, //0x00001e0e leaq         (%rsi,%r12), %rax
	0x48, 0x01, 0xf8, //0x00001e12 addq         %rdi, %rax
	0x49, 0x83, 0xf9, 0xff, //0x00001e15 cmpq         $-1, %r9
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00001e19 movq         $16(%rsp), %rcx
	0x48, 0x0f, 0x44, 0xc8, //0x00001e1e cmoveq       %rax, %rcx
	0x48, 0x89, 0x4c, 0x24, 0x10, //0x00001e22 movq         %rcx, $16(%rsp)
	0x4c, 0x0f, 0x44, 0xc8, //0x00001e27 cmoveq       %rax, %r9
	0x49, 0x01, 0xfc, //0x00001e2b addq         %rdi, %r12
	0x49, 0x83, 0xc4, 0x02, //0x00001e2e addq         $2, %r12
	0x48, 0x89, 0xd8, //0x00001e32 movq         %rbx, %rax
	0x48, 0x29, 0xf8, //0x00001e35 subq         %rdi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00001e38 addq         $-2, %rax
	0x48, 0x83, 0xc3, 0xfe, //0x00001e3c addq         $-2, %rbx
	0x48, 0x39, 0xfb, //0x00001e40 cmpq         %rdi, %rbx
	0x48, 0x89, 0xc3, //0x00001e43 movq         %rax, %rbx
	0x0f, 0x85, 0x8a, 0xff, 0xff, 0xff, //0x00001e46 jne          LBB0_263
	0xe9, 0xc6, 0x14, 0x00, 0x00, //0x00001e4c jmp          LBB0_268
	//0x00001e51 LBB0_351
	0x4d, 0x01, 0xec, //0x00001e51 addq         %r13, %r12
	0x4c, 0x89, 0xd8, //0x00001e54 movq         %r11, %rax
	0x48, 0x83, 0xfb, 0x20, //0x00001e57 cmpq         $32, %rbx
	0x0f, 0x82, 0x70, 0x08, 0x00, 0x00, //0x00001e5b jb           LBB0_515
	//0x00001e61 LBB0_352
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001e61 vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001e67 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xd9, //0x00001e6b vpmovmskb    %ymm1, %r11d
	0xc5, 0xfd, 0x74, 0xcf, //0x00001e6f vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00001e73 vpmovmskb    %ymm1, %edx
	0x85, 0xd2, //0x00001e77 testl        %edx, %edx
	0x0f, 0x85, 0xce, 0x07, 0x00, 0x00, //0x00001e79 jne          LBB0_508
	0x4d, 0x85, 0xc9, //0x00001e7f testq        %r9, %r9
	0x0f, 0x85, 0xe3, 0x07, 0x00, 0x00, //0x00001e82 jne          LBB0_510
	0x45, 0x31, 0xc9, //0x00001e88 xorl         %r9d, %r9d
	0x4d, 0x85, 0xdb, //0x00001e8b testq        %r11, %r11
	0x0f, 0x84, 0x10, 0x08, 0x00, 0x00, //0x00001e8e je           LBB0_511
	//0x00001e94 LBB0_355
	0x49, 0x0f, 0xbc, 0xd3, //0x00001e94 bsfq         %r11, %rdx
	0xe9, 0x0c, 0x08, 0x00, 0x00, //0x00001e98 jmp          LBB0_512
	//0x00001e9d LBB0_405
	0x0f, 0xbc, 0xc0, //0x00001e9d bsfl         %eax, %eax
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00001ea0 jmp          LBB0_408
	//0x00001ea5 LBB0_406
	0x4d, 0x01, 0xd5, //0x00001ea5 addq         %r10, %r13
	0x49, 0x29, 0xdd, //0x00001ea8 subq         %rbx, %r13
	0x49, 0x29, 0xcd, //0x00001eab subq         %rcx, %r13
	0x4c, 0x89, 0xeb, //0x00001eae movq         %r13, %rbx
	0xe9, 0x02, 0xfc, 0xff, 0xff, //0x00001eb1 jmp          LBB0_378
	//0x00001eb6 LBB0_407
	0x89, 0xd0, //0x00001eb6 movl         %edx, %eax
	//0x00001eb8 LBB0_408
	0x4d, 0x01, 0xd5, //0x00001eb8 addq         %r10, %r13
	0x49, 0x29, 0xdd, //0x00001ebb subq         %rbx, %r13
	0x49, 0x29, 0xc5, //0x00001ebe subq         %rax, %r13
	0x4d, 0x29, 0xf5, //0x00001ec1 subq         %r14, %r13
	0x4c, 0x89, 0xeb, //0x00001ec4 movq         %r13, %rbx
	0x4c, 0x8b, 0x4c, 0x24, 0x30, //0x00001ec7 movq         $48(%rsp), %r9
	0xe9, 0xe7, 0xfb, 0xff, 0xff, //0x00001ecc jmp          LBB0_378
	//0x00001ed1 LBB0_409
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001ed1 movq         $-1, %r9
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001ed8 movq         $-1, %r8
	0x4d, 0x89, 0xf7, //0x00001edf movq         %r14, %r15
	0x4c, 0x89, 0x5c, 0x24, 0x18, //0x00001ee2 movq         %r11, $24(%rsp)
	0x48, 0x83, 0xfb, 0x10, //0x00001ee7 cmpq         $16, %rbx
	0x0f, 0x83, 0x00, 0xec, 0xff, 0xff, //0x00001eeb jae          LBB0_137
	0xe9, 0x24, 0xed, 0xff, 0xff, //0x00001ef1 jmp          LBB0_155
	//0x00001ef6 LBB0_410
	0x4d, 0x01, 0xec, //0x00001ef6 addq         %r13, %r12
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00001ef9 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc9, //0x00001f02 xorl         %r9d, %r9d
	0x48, 0x83, 0xfb, 0x20, //0x00001f05 cmpq         $32, %rbx
	0x0f, 0x83, 0x5c, 0xfc, 0xff, 0xff, //0x00001f09 jae          LBB0_86
	0xe9, 0x3b, 0x01, 0x00, 0x00, //0x00001f0f jmp          LBB0_423
	//0x00001f14 LBB0_411
	0x4d, 0x01, 0xec, //0x00001f14 addq         %r13, %r12
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00001f17 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc9, //0x00001f20 xorl         %r9d, %r9d
	0x4c, 0x89, 0xd8, //0x00001f23 movq         %r11, %rax
	0x48, 0x83, 0xfb, 0x20, //0x00001f26 cmpq         $32, %rbx
	0x0f, 0x83, 0x91, 0xfc, 0xff, 0xff, //0x00001f2a jae          LBB0_188
	//0x00001f30 LBB0_412
	0x49, 0x89, 0xc3, //0x00001f30 movq         %rax, %r11
	0xe9, 0x7e, 0x02, 0x00, 0x00, //0x00001f33 jmp          LBB0_445
	//0x00001f38 LBB0_413
	0x4d, 0x01, 0xec, //0x00001f38 addq         %r13, %r12
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00001f3b movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc9, //0x00001f44 xorl         %r9d, %r9d
	0x48, 0x83, 0xfb, 0x20, //0x00001f47 cmpq         $32, %rbx
	0x0f, 0x83, 0x4c, 0xfd, 0xff, 0xff, //0x00001f4b jae          LBB0_109
	0xe9, 0x6d, 0x03, 0x00, 0x00, //0x00001f51 jmp          LBB0_461
	//0x00001f56 LBB0_414
	0x4d, 0x01, 0xec, //0x00001f56 addq         %r13, %r12
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00001f59 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc9, //0x00001f62 xorl         %r9d, %r9d
	0x4c, 0x89, 0xd8, //0x00001f65 movq         %r11, %rax
	0x48, 0x83, 0xfb, 0x20, //0x00001f68 cmpq         $32, %rbx
	0x0f, 0x83, 0x81, 0xfd, 0xff, 0xff, //0x00001f6c jae          LBB0_209
	//0x00001f72 LBB0_415
	0x49, 0x89, 0xc3, //0x00001f72 movq         %rax, %r11
	0xe9, 0xb0, 0x04, 0x00, 0x00, //0x00001f75 jmp          LBB0_483
	//0x00001f7a LBB0_416
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001f7a movq         $-1, %r11
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00001f81 movq         $-1, %r12
	0x4c, 0x89, 0xf3, //0x00001f88 movq         %r14, %rbx
	0x4c, 0x89, 0x74, 0x24, 0x10, //0x00001f8b movq         %r14, $16(%rsp)
	0x4c, 0x89, 0x4c, 0x24, 0x30, //0x00001f90 movq         %r9, $48(%rsp)
	0x49, 0x83, 0xff, 0x10, //0x00001f95 cmpq         $16, %r15
	0x0f, 0x83, 0x1f, 0xf6, 0xff, 0xff, //0x00001f99 jae          LBB0_297
	0xe9, 0x46, 0xf7, 0xff, 0xff, //0x00001f9f jmp          LBB0_315
	//0x00001fa4 LBB0_417
	0x4d, 0x01, 0xec, //0x00001fa4 addq         %r13, %r12
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00001fa7 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc9, //0x00001fb0 xorl         %r9d, %r9d
	0x48, 0x83, 0xfb, 0x20, //0x00001fb3 cmpq         $32, %rbx
	0x0f, 0x83, 0xc1, 0xfd, 0xff, 0xff, //0x00001fb7 jae          LBB0_256
	0xe9, 0xf7, 0xfd, 0xff, 0xff, //0x00001fbd jmp          LBB0_260
	//0x00001fc2 LBB0_418
	0x4d, 0x01, 0xec, //0x00001fc2 addq         %r13, %r12
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00001fc5 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc9, //0x00001fce xorl         %r9d, %r9d
	0x4c, 0x89, 0xd8, //0x00001fd1 movq         %r11, %rax
	0x48, 0x83, 0xfb, 0x20, //0x00001fd4 cmpq         $32, %rbx
	0x0f, 0x83, 0x83, 0xfe, 0xff, 0xff, //0x00001fd8 jae          LBB0_352
	0xe9, 0xee, 0x06, 0x00, 0x00, //0x00001fde jmp          LBB0_515
	//0x00001fe3 LBB0_267
	0x80, 0xfa, 0x22, //0x00001fe3 cmpb         $34, %dl
	0x0f, 0x84, 0x13, 0x01, 0x00, 0x00, //0x00001fe6 je           LBB0_431
	0xe9, 0x26, 0x13, 0x00, 0x00, //0x00001fec jmp          LBB0_268
	//0x00001ff1 LBB0_419
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00001ff1 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00001ff7 jne          LBB0_421
	0x4c, 0x89, 0xe0, //0x00001ffd movq         %r12, %rax
	0x4c, 0x29, 0xe8, //0x00002000 subq         %r13, %rax
	0x48, 0x0f, 0xbc, 0xca, //0x00002003 bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x00002007 addq         %rax, %rcx
	0x48, 0x89, 0x4c, 0x24, 0x10, //0x0000200a movq         %rcx, $16(%rsp)
	//0x0000200f LBB0_421
	0x44, 0x89, 0xc8, //0x0000200f movl         %r9d, %eax
	0xf7, 0xd0, //0x00002012 notl         %eax
	0x21, 0xd0, //0x00002014 andl         %edx, %eax
	0x8d, 0x0c, 0x00, //0x00002016 leal         (%rax,%rax), %ecx
	0x41, 0x8d, 0x3c, 0x41, //0x00002019 leal         (%r9,%rax,2), %edi
	0xf7, 0xd1, //0x0000201d notl         %ecx
	0x21, 0xd1, //0x0000201f andl         %edx, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002021 andl         $-1431655766, %ecx
	0x45, 0x31, 0xc9, //0x00002027 xorl         %r9d, %r9d
	0x01, 0xc1, //0x0000202a addl         %eax, %ecx
	0x41, 0x0f, 0x92, 0xc1, //0x0000202c setb         %r9b
	0x01, 0xc9, //0x00002030 addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x00002032 xorl         $1431655765, %ecx
	0x21, 0xf9, //0x00002038 andl         %edi, %ecx
	0xf7, 0xd1, //0x0000203a notl         %ecx
	0x21, 0xce, //0x0000203c andl         %ecx, %esi
	0x48, 0x85, 0xf6, //0x0000203e testq        %rsi, %rsi
	0x0f, 0x85, 0x57, 0xfb, 0xff, 0xff, //0x00002041 jne          LBB0_89
	//0x00002047 LBB0_422
	0x49, 0x83, 0xc4, 0x20, //0x00002047 addq         $32, %r12
	0x48, 0x83, 0xc3, 0xe0, //0x0000204b addq         $-32, %rbx
	//0x0000204f LBB0_423
	0x4d, 0x85, 0xc9, //0x0000204f testq        %r9, %r9
	0x0f, 0x85, 0x8a, 0x04, 0x00, 0x00, //0x00002052 jne          LBB0_497
	0x4c, 0x8b, 0x4c, 0x24, 0x10, //0x00002058 movq         $16(%rsp), %r9
	0x48, 0x85, 0xdb, //0x0000205d testq        %rbx, %rbx
	0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, //0x00002060 je           LBB0_435
	//0x00002066 LBB0_425
	0x4c, 0x89, 0xee, //0x00002066 movq         %r13, %rsi
	0x48, 0xf7, 0xde, //0x00002069 negq         %rsi
	//0x0000206c LBB0_426
	0x31, 0xff, //0x0000206c xorl         %edi, %edi
	//0x0000206e LBB0_427
	0x41, 0x0f, 0xb6, 0x14, 0x3c, //0x0000206e movzbl       (%r12,%rdi), %edx
	0x80, 0xfa, 0x22, //0x00002073 cmpb         $34, %dl
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x00002076 je           LBB0_434
	0x80, 0xfa, 0x5c, //0x0000207c cmpb         $92, %dl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000207f je           LBB0_432
	0x48, 0x83, 0xc7, 0x01, //0x00002085 addq         $1, %rdi
	0x48, 0x39, 0xfb, //0x00002089 cmpq         %rdi, %rbx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x0000208c jne          LBB0_427
	0xe9, 0x5f, 0x00, 0x00, 0x00, //0x00002092 jmp          LBB0_430
	//0x00002097 LBB0_432
	0x48, 0x8d, 0x43, 0xff, //0x00002097 leaq         $-1(%rbx), %rax
	0x48, 0x39, 0xf8, //0x0000209b cmpq         %rdi, %rax
	0x0f, 0x84, 0x73, 0x12, 0x00, 0x00, //0x0000209e je           LBB0_268
	0x4a, 0x8d, 0x04, 0x26, //0x000020a4 leaq         (%rsi,%r12), %rax
	0x48, 0x01, 0xf8, //0x000020a8 addq         %rdi, %rax
	0x49, 0x83, 0xf9, 0xff, //0x000020ab cmpq         $-1, %r9
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x000020af movq         $16(%rsp), %rcx
	0x48, 0x0f, 0x44, 0xc8, //0x000020b4 cmoveq       %rax, %rcx
	0x48, 0x89, 0x4c, 0x24, 0x10, //0x000020b8 movq         %rcx, $16(%rsp)
	0x4c, 0x0f, 0x44, 0xc8, //0x000020bd cmoveq       %rax, %r9
	0x49, 0x01, 0xfc, //0x000020c1 addq         %rdi, %r12
	0x49, 0x83, 0xc4, 0x02, //0x000020c4 addq         $2, %r12
	0x48, 0x89, 0xd8, //0x000020c8 movq         %rbx, %rax
	0x48, 0x29, 0xf8, //0x000020cb subq         %rdi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000020ce addq         $-2, %rax
	0x48, 0x83, 0xc3, 0xfe, //0x000020d2 addq         $-2, %rbx
	0x48, 0x39, 0xfb, //0x000020d6 cmpq         %rdi, %rbx
	0x48, 0x89, 0xc3, //0x000020d9 movq         %rax, %rbx
	0x0f, 0x85, 0x8a, 0xff, 0xff, 0xff, //0x000020dc jne          LBB0_426
	0xe9, 0x30, 0x12, 0x00, 0x00, //0x000020e2 jmp          LBB0_268
	//0x000020e7 LBB0_434
	0x49, 0x01, 0xfc, //0x000020e7 addq         %rdi, %r12
	0x49, 0x83, 0xc4, 0x01, //0x000020ea addq         $1, %r12
	//0x000020ee LBB0_435
	0x4d, 0x29, 0xec, //0x000020ee subq         %r13, %r12
	0xe9, 0xe6, 0xe3, 0xff, 0xff, //0x000020f1 jmp          LBB0_32
	//0x000020f6 LBB0_430
	0x80, 0xfa, 0x22, //0x000020f6 cmpb         $34, %dl
	0x0f, 0x85, 0x18, 0x12, 0x00, 0x00, //0x000020f9 jne          LBB0_268
	//0x000020ff LBB0_431
	0x49, 0x01, 0xdc, //0x000020ff addq         %rbx, %r12
	0xe9, 0xe7, 0xff, 0xff, 0xff, //0x00002102 jmp          LBB0_435
	//0x00002107 LBB0_436
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00002107 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x0000210d jne          LBB0_438
	0x4c, 0x89, 0xe1, //0x00002113 movq         %r12, %rcx
	0x4c, 0x29, 0xe9, //0x00002116 subq         %r13, %rcx
	0x48, 0x0f, 0xbc, 0xf2, //0x00002119 bsfq         %rdx, %rsi
	0x48, 0x01, 0xce, //0x0000211d addq         %rcx, %rsi
	0x48, 0x89, 0x74, 0x24, 0x10, //0x00002120 movq         %rsi, $16(%rsp)
	//0x00002125 LBB0_438
	0x44, 0x89, 0xcf, //0x00002125 movl         %r9d, %edi
	0xf7, 0xd7, //0x00002128 notl         %edi
	0x21, 0xd7, //0x0000212a andl         %edx, %edi
	0x8d, 0x0c, 0x3f, //0x0000212c leal         (%rdi,%rdi), %ecx
	0x41, 0x8d, 0x34, 0x79, //0x0000212f leal         (%r9,%rdi,2), %esi
	0xf7, 0xd1, //0x00002133 notl         %ecx
	0x21, 0xd1, //0x00002135 andl         %edx, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002137 andl         $-1431655766, %ecx
	0x45, 0x31, 0xc9, //0x0000213d xorl         %r9d, %r9d
	0x01, 0xf9, //0x00002140 addl         %edi, %ecx
	0x41, 0x0f, 0x92, 0xc1, //0x00002142 setb         %r9b
	0x01, 0xc9, //0x00002146 addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x00002148 xorl         $1431655765, %ecx
	0x21, 0xf1, //0x0000214e andl         %esi, %ecx
	0xf7, 0xd1, //0x00002150 notl         %ecx
	0x41, 0x21, 0xcb, //0x00002152 andl         %ecx, %r11d
	0x4d, 0x85, 0xdb, //0x00002155 testq        %r11, %r11
	0x0f, 0x85, 0x96, 0xfa, 0xff, 0xff, //0x00002158 jne          LBB0_191
	//0x0000215e LBB0_439
	0xba, 0x40, 0x00, 0x00, 0x00, //0x0000215e movl         $64, %edx
	//0x00002163 LBB0_440
	0xc5, 0xbd, 0xda, 0xc8, //0x00002163 vpminub      %ymm0, %ymm8, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x00002167 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x0000216b vpmovmskb    %ymm0, %esi
	0x0f, 0xbc, 0xfe, //0x0000216f bsfl         %esi, %edi
	0x4d, 0x85, 0xdb, //0x00002172 testq        %r11, %r11
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x00002175 je           LBB0_443
	//0x0000217b LBB0_441
	0x85, 0xf6, //0x0000217b testl        %esi, %esi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x0000217d movl         $64, %ecx
	0x0f, 0x44, 0xf9, //0x00002182 cmovel       %ecx, %edi
	0x48, 0x39, 0xfa, //0x00002185 cmpq         %rdi, %rdx
	0x0f, 0x87, 0x79, 0x11, 0x00, 0x00, //0x00002188 ja           LBB0_664
	0x49, 0x89, 0xc3, //0x0000218e movq         %rax, %r11
	0x4d, 0x29, 0xec, //0x00002191 subq         %r13, %r12
	0x49, 0x01, 0xd4, //0x00002194 addq         %rdx, %r12
	0x49, 0x83, 0xc4, 0x01, //0x00002197 addq         $1, %r12
	0x4d, 0x89, 0xf1, //0x0000219b movq         %r14, %r9
	0xe9, 0xb3, 0x00, 0x00, 0x00, //0x0000219e jmp          LBB0_456
	//0x000021a3 LBB0_443
	0x85, 0xf6, //0x000021a3 testl        %esi, %esi
	0x0f, 0x85, 0x85, 0x11, 0x00, 0x00, //0x000021a5 jne          LBB0_662
	0x49, 0x89, 0xc3, //0x000021ab movq         %rax, %r11
	0x49, 0x83, 0xc4, 0x20, //0x000021ae addq         $32, %r12
	0x48, 0x83, 0xc3, 0xe0, //0x000021b2 addq         $-32, %rbx
	//0x000021b6 LBB0_445
	0x4d, 0x85, 0xc9, //0x000021b6 testq        %r9, %r9
	0x0f, 0x85, 0x64, 0x03, 0x00, 0x00, //0x000021b9 jne          LBB0_499
	0x48, 0x8b, 0x74, 0x24, 0x10, //0x000021bf movq         $16(%rsp), %rsi
	0x4d, 0x89, 0xf1, //0x000021c4 movq         %r14, %r9
	0x48, 0x85, 0xdb, //0x000021c7 testq        %rbx, %rbx
	0x0f, 0x84, 0xfa, 0x07, 0x00, 0x00, //0x000021ca je           LBB0_496
	//0x000021d0 LBB0_447
	0x41, 0x0f, 0xb6, 0x14, 0x24, //0x000021d0 movzbl       (%r12), %edx
	0x80, 0xfa, 0x22, //0x000021d5 cmpb         $34, %dl
	0x0f, 0x84, 0x71, 0x00, 0x00, 0x00, //0x000021d8 je           LBB0_455
	0x80, 0xfa, 0x5c, //0x000021de cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x000021e1 je           LBB0_451
	0x80, 0xfa, 0x1f, //0x000021e7 cmpb         $31, %dl
	0x0f, 0x86, 0x4a, 0x11, 0x00, 0x00, //0x000021ea jbe          LBB0_659
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000021f0 movq         $-1, %rdx
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x000021f7 movl         $1, %edi
	0x49, 0x01, 0xfc, //0x000021fc addq         %rdi, %r12
	0x48, 0x01, 0xd3, //0x000021ff addq         %rdx, %rbx
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x00002202 jne          LBB0_447
	0xe9, 0xbd, 0x07, 0x00, 0x00, //0x00002208 jmp          LBB0_496
	//0x0000220d LBB0_451
	0x48, 0x83, 0xfb, 0x01, //0x0000220d cmpq         $1, %rbx
	0x0f, 0x84, 0xd7, 0x05, 0x00, 0x00, //0x00002211 je           LBB0_530
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00002217 movq         $-2, %rdx
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x0000221e movl         $2, %edi
	0x48, 0x83, 0xfe, 0xff, //0x00002223 cmpq         $-1, %rsi
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00002227 jne          LBB0_454
	0x4c, 0x89, 0xe6, //0x0000222d movq         %r12, %rsi
	0x4c, 0x29, 0xee, //0x00002230 subq         %r13, %rsi
	0x48, 0x89, 0x74, 0x24, 0x10, //0x00002233 movq         %rsi, $16(%rsp)
	//0x00002238 LBB0_454
	0x4d, 0x89, 0xf1, //0x00002238 movq         %r14, %r9
	0x49, 0x89, 0xc3, //0x0000223b movq         %rax, %r11
	0x49, 0x01, 0xfc, //0x0000223e addq         %rdi, %r12
	0x48, 0x01, 0xd3, //0x00002241 addq         %rdx, %rbx
	0x0f, 0x85, 0x86, 0xff, 0xff, 0xff, //0x00002244 jne          LBB0_447
	0xe9, 0x7b, 0x07, 0x00, 0x00, //0x0000224a jmp          LBB0_496
	//0x0000224f LBB0_455
	0x4d, 0x29, 0xec, //0x0000224f subq         %r13, %r12
	0x49, 0x83, 0xc4, 0x01, //0x00002252 addq         $1, %r12
	//0x00002256 LBB0_456
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x00002256 movq         $32(%rsp), %rdi
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x0000225b movq         $40(%rsp), %rdx
	0xe9, 0x87, 0xe2, 0xff, 0xff, //0x00002260 jmp          LBB0_33
	//0x00002265 LBB0_457
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00002265 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x0000226b jne          LBB0_459
	0x4c, 0x89, 0xe0, //0x00002271 movq         %r12, %rax
	0x4c, 0x29, 0xe8, //0x00002274 subq         %r13, %rax
	0x48, 0x0f, 0xbc, 0xca, //0x00002277 bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x0000227b addq         %rax, %rcx
	0x48, 0x89, 0x4c, 0x24, 0x10, //0x0000227e movq         %rcx, $16(%rsp)
	//0x00002283 LBB0_459
	0x44, 0x89, 0xc8, //0x00002283 movl         %r9d, %eax
	0xf7, 0xd0, //0x00002286 notl         %eax
	0x21, 0xd0, //0x00002288 andl         %edx, %eax
	0x8d, 0x0c, 0x00, //0x0000228a leal         (%rax,%rax), %ecx
	0x41, 0x8d, 0x3c, 0x41, //0x0000228d leal         (%r9,%rax,2), %edi
	0xf7, 0xd1, //0x00002291 notl         %ecx
	0x21, 0xd1, //0x00002293 andl         %edx, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002295 andl         $-1431655766, %ecx
	0x45, 0x31, 0xc9, //0x0000229b xorl         %r9d, %r9d
	0x01, 0xc1, //0x0000229e addl         %eax, %ecx
	0x41, 0x0f, 0x92, 0xc1, //0x000022a0 setb         %r9b
	0x01, 0xc9, //0x000022a4 addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x000022a6 xorl         $1431655765, %ecx
	0x21, 0xf9, //0x000022ac andl         %edi, %ecx
	0xf7, 0xd1, //0x000022ae notl         %ecx
	0x21, 0xce, //0x000022b0 andl         %ecx, %esi
	0x48, 0x85, 0xf6, //0x000022b2 testq        %rsi, %rsi
	0x0f, 0x85, 0x15, 0xfa, 0xff, 0xff, //0x000022b5 jne          LBB0_112
	//0x000022bb LBB0_460
	0x49, 0x83, 0xc4, 0x20, //0x000022bb addq         $32, %r12
	0x48, 0x83, 0xc3, 0xe0, //0x000022bf addq         $-32, %rbx
	//0x000022c3 LBB0_461
	0x4d, 0x85, 0xc9, //0x000022c3 testq        %r9, %r9
	0x0f, 0x85, 0x9e, 0x02, 0x00, 0x00, //0x000022c6 jne          LBB0_501
	0x4c, 0x8b, 0x4c, 0x24, 0x10, //0x000022cc movq         $16(%rsp), %r9
	0x48, 0x85, 0xdb, //0x000022d1 testq        %rbx, %rbx
	0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, //0x000022d4 je           LBB0_473
	//0x000022da LBB0_463
	0x4c, 0x89, 0xee, //0x000022da movq         %r13, %rsi
	0x48, 0xf7, 0xde, //0x000022dd negq         %rsi
	//0x000022e0 LBB0_464
	0x31, 0xff, //0x000022e0 xorl         %edi, %edi
	//0x000022e2 LBB0_465
	0x41, 0x0f, 0xb6, 0x14, 0x3c, //0x000022e2 movzbl       (%r12,%rdi), %edx
	0x80, 0xfa, 0x22, //0x000022e7 cmpb         $34, %dl
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x000022ea je           LBB0_472
	0x80, 0xfa, 0x5c, //0x000022f0 cmpb         $92, %dl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000022f3 je           LBB0_470
	0x48, 0x83, 0xc7, 0x01, //0x000022f9 addq         $1, %rdi
	0x48, 0x39, 0xfb, //0x000022fd cmpq         %rdi, %rbx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00002300 jne          LBB0_465
	0xe9, 0x5f, 0x00, 0x00, 0x00, //0x00002306 jmp          LBB0_468
	//0x0000230b LBB0_470
	0x48, 0x8d, 0x43, 0xff, //0x0000230b leaq         $-1(%rbx), %rax
	0x48, 0x39, 0xf8, //0x0000230f cmpq         %rdi, %rax
	0x0f, 0x84, 0xff, 0x0f, 0x00, 0x00, //0x00002312 je           LBB0_268
	0x4a, 0x8d, 0x04, 0x26, //0x00002318 leaq         (%rsi,%r12), %rax
	0x48, 0x01, 0xf8, //0x0000231c addq         %rdi, %rax
	0x49, 0x83, 0xf9, 0xff, //0x0000231f cmpq         $-1, %r9
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00002323 movq         $16(%rsp), %rcx
	0x48, 0x0f, 0x44, 0xc8, //0x00002328 cmoveq       %rax, %rcx
	0x48, 0x89, 0x4c, 0x24, 0x10, //0x0000232c movq         %rcx, $16(%rsp)
	0x4c, 0x0f, 0x44, 0xc8, //0x00002331 cmoveq       %rax, %r9
	0x49, 0x01, 0xfc, //0x00002335 addq         %rdi, %r12
	0x49, 0x83, 0xc4, 0x02, //0x00002338 addq         $2, %r12
	0x48, 0x89, 0xd8, //0x0000233c movq         %rbx, %rax
	0x48, 0x29, 0xf8, //0x0000233f subq         %rdi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00002342 addq         $-2, %rax
	0x48, 0x83, 0xc3, 0xfe, //0x00002346 addq         $-2, %rbx
	0x48, 0x39, 0xfb, //0x0000234a cmpq         %rdi, %rbx
	0x48, 0x89, 0xc3, //0x0000234d movq         %rax, %rbx
	0x0f, 0x85, 0x8a, 0xff, 0xff, 0xff, //0x00002350 jne          LBB0_464
	0xe9, 0xbc, 0x0f, 0x00, 0x00, //0x00002356 jmp          LBB0_268
	//0x0000235b LBB0_472
	0x49, 0x01, 0xfc, //0x0000235b addq         %rdi, %r12
	0x49, 0x83, 0xc4, 0x01, //0x0000235e addq         $1, %r12
	//0x00002362 LBB0_473
	0x4d, 0x29, 0xec, //0x00002362 subq         %r13, %r12
	0xe9, 0xdd, 0xea, 0xff, 0xff, //0x00002365 jmp          LBB0_177
	//0x0000236a LBB0_468
	0x80, 0xfa, 0x22, //0x0000236a cmpb         $34, %dl
	0x0f, 0x85, 0xa4, 0x0f, 0x00, 0x00, //0x0000236d jne          LBB0_268
	0x49, 0x01, 0xdc, //0x00002373 addq         %rbx, %r12
	0xe9, 0xe7, 0xff, 0xff, 0xff, //0x00002376 jmp          LBB0_473
	//0x0000237b LBB0_474
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x0000237b cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00002381 jne          LBB0_476
	0x4c, 0x89, 0xe1, //0x00002387 movq         %r12, %rcx
	0x4c, 0x29, 0xe9, //0x0000238a subq         %r13, %rcx
	0x48, 0x0f, 0xbc, 0xf2, //0x0000238d bsfq         %rdx, %rsi
	0x48, 0x01, 0xce, //0x00002391 addq         %rcx, %rsi
	0x48, 0x89, 0x74, 0x24, 0x10, //0x00002394 movq         %rsi, $16(%rsp)
	//0x00002399 LBB0_476
	0x44, 0x89, 0xcf, //0x00002399 movl         %r9d, %edi
	0xf7, 0xd7, //0x0000239c notl         %edi
	0x21, 0xd7, //0x0000239e andl         %edx, %edi
	0x8d, 0x0c, 0x3f, //0x000023a0 leal         (%rdi,%rdi), %ecx
	0x41, 0x8d, 0x34, 0x79, //0x000023a3 leal         (%r9,%rdi,2), %esi
	0xf7, 0xd1, //0x000023a7 notl         %ecx
	0x21, 0xd1, //0x000023a9 andl         %edx, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x000023ab andl         $-1431655766, %ecx
	0x45, 0x31, 0xc9, //0x000023b1 xorl         %r9d, %r9d
	0x01, 0xf9, //0x000023b4 addl         %edi, %ecx
	0x41, 0x0f, 0x92, 0xc1, //0x000023b6 setb         %r9b
	0x01, 0xc9, //0x000023ba addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x000023bc xorl         $1431655765, %ecx
	0x21, 0xf1, //0x000023c2 andl         %esi, %ecx
	0xf7, 0xd1, //0x000023c4 notl         %ecx
	0x41, 0x21, 0xcb, //0x000023c6 andl         %ecx, %r11d
	0x4d, 0x85, 0xdb, //0x000023c9 testq        %r11, %r11
	0x0f, 0x85, 0x54, 0xf9, 0xff, 0xff, //0x000023cc jne          LBB0_212
	//0x000023d2 LBB0_477
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000023d2 movl         $64, %edx
	//0x000023d7 LBB0_478
	0xc5, 0xbd, 0xda, 0xc8, //0x000023d7 vpminub      %ymm0, %ymm8, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x000023db vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x000023df vpmovmskb    %ymm0, %esi
	0x0f, 0xbc, 0xfe, //0x000023e3 bsfl         %esi, %edi
	0x4d, 0x85, 0xdb, //0x000023e6 testq        %r11, %r11
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x000023e9 je           LBB0_481
	0x85, 0xf6, //0x000023ef testl        %esi, %esi
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x000023f1 movl         $64, %ecx
	0x0f, 0x44, 0xf9, //0x000023f6 cmovel       %ecx, %edi
	0x48, 0x39, 0xfa, //0x000023f9 cmpq         %rdi, %rdx
	0x0f, 0x87, 0x05, 0x0f, 0x00, 0x00, //0x000023fc ja           LBB0_664
	0x49, 0x89, 0xc3, //0x00002402 movq         %rax, %r11
	0x4d, 0x29, 0xec, //0x00002405 subq         %r13, %r12
	0x49, 0x01, 0xd4, //0x00002408 addq         %rdx, %r12
	0x49, 0x83, 0xc4, 0x01, //0x0000240b addq         $1, %r12
	0x4d, 0x89, 0xf1, //0x0000240f movq         %r14, %r9
	0xe9, 0xb3, 0x00, 0x00, 0x00, //0x00002412 jmp          LBB0_494
	//0x00002417 LBB0_481
	0x85, 0xf6, //0x00002417 testl        %esi, %esi
	0x0f, 0x85, 0x11, 0x0f, 0x00, 0x00, //0x00002419 jne          LBB0_662
	0x49, 0x89, 0xc3, //0x0000241f movq         %rax, %r11
	0x49, 0x83, 0xc4, 0x20, //0x00002422 addq         $32, %r12
	0x48, 0x83, 0xc3, 0xe0, //0x00002426 addq         $-32, %rbx
	//0x0000242a LBB0_483
	0x4d, 0x85, 0xc9, //0x0000242a testq        %r9, %r9
	0x0f, 0x85, 0x78, 0x01, 0x00, 0x00, //0x0000242d jne          LBB0_503
	0x48, 0x8b, 0x74, 0x24, 0x10, //0x00002433 movq         $16(%rsp), %rsi
	0x4d, 0x89, 0xf1, //0x00002438 movq         %r14, %r9
	0x48, 0x85, 0xdb, //0x0000243b testq        %rbx, %rbx
	0x0f, 0x84, 0x86, 0x05, 0x00, 0x00, //0x0000243e je           LBB0_496
	//0x00002444 LBB0_485
	0x41, 0x0f, 0xb6, 0x14, 0x24, //0x00002444 movzbl       (%r12), %edx
	0x80, 0xfa, 0x22, //0x00002449 cmpb         $34, %dl
	0x0f, 0x84, 0x71, 0x00, 0x00, 0x00, //0x0000244c je           LBB0_493
	0x80, 0xfa, 0x5c, //0x00002452 cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00002455 je           LBB0_489
	0x80, 0xfa, 0x1f, //0x0000245b cmpb         $31, %dl
	0x0f, 0x86, 0xd6, 0x0e, 0x00, 0x00, //0x0000245e jbe          LBB0_659
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002464 movq         $-1, %rdx
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x0000246b movl         $1, %edi
	0x49, 0x01, 0xfc, //0x00002470 addq         %rdi, %r12
	0x48, 0x01, 0xd3, //0x00002473 addq         %rdx, %rbx
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x00002476 jne          LBB0_485
	0xe9, 0x49, 0x05, 0x00, 0x00, //0x0000247c jmp          LBB0_496
	//0x00002481 LBB0_489
	0x48, 0x83, 0xfb, 0x01, //0x00002481 cmpq         $1, %rbx
	0x0f, 0x84, 0x63, 0x03, 0x00, 0x00, //0x00002485 je           LBB0_530
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x0000248b movq         $-2, %rdx
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x00002492 movl         $2, %edi
	0x48, 0x83, 0xfe, 0xff, //0x00002497 cmpq         $-1, %rsi
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x0000249b jne          LBB0_492
	0x4c, 0x89, 0xe6, //0x000024a1 movq         %r12, %rsi
	0x4c, 0x29, 0xee, //0x000024a4 subq         %r13, %rsi
	0x48, 0x89, 0x74, 0x24, 0x10, //0x000024a7 movq         %rsi, $16(%rsp)
	//0x000024ac LBB0_492
	0x4d, 0x89, 0xf1, //0x000024ac movq         %r14, %r9
	0x49, 0x89, 0xc3, //0x000024af movq         %rax, %r11
	0x49, 0x01, 0xfc, //0x000024b2 addq         %rdi, %r12
	0x48, 0x01, 0xd3, //0x000024b5 addq         %rdx, %rbx
	0x0f, 0x85, 0x86, 0xff, 0xff, 0xff, //0x000024b8 jne          LBB0_485
	0xe9, 0x07, 0x05, 0x00, 0x00, //0x000024be jmp          LBB0_496
	//0x000024c3 LBB0_493
	0x4d, 0x29, 0xec, //0x000024c3 subq         %r13, %r12
	0x49, 0x83, 0xc4, 0x01, //0x000024c6 addq         $1, %r12
	//0x000024ca LBB0_494
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x000024ca movq         $32(%rsp), %rdi
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x000024cf movq         $40(%rsp), %rdx
	0x4d, 0x85, 0xe4, //0x000024d4 testq        %r12, %r12
	0x0f, 0x89, 0xaf, 0xed, 0xff, 0xff, //0x000024d7 jns          LBB0_241
	0xe9, 0xd9, 0x04, 0x00, 0x00, //0x000024dd jmp          LBB0_495
	//0x000024e2 LBB0_497
	0x48, 0x85, 0xdb, //0x000024e2 testq        %rbx, %rbx
	0x0f, 0x84, 0x2c, 0x0e, 0x00, 0x00, //0x000024e5 je           LBB0_268
	0x4d, 0x89, 0xe9, //0x000024eb movq         %r13, %r9
	0x49, 0xf7, 0xd1, //0x000024ee notq         %r9
	0x4d, 0x01, 0xe1, //0x000024f1 addq         %r12, %r9
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x000024f4 movq         $16(%rsp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x000024f9 cmpq         $-1, %rcx
	0x48, 0x89, 0xc8, //0x000024fd movq         %rcx, %rax
	0x49, 0x0f, 0x44, 0xc1, //0x00002500 cmoveq       %r9, %rax
	0x4c, 0x0f, 0x45, 0xc9, //0x00002504 cmovneq      %rcx, %r9
	0x49, 0x83, 0xc4, 0x01, //0x00002508 addq         $1, %r12
	0x48, 0x83, 0xc3, 0xff, //0x0000250c addq         $-1, %rbx
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00002510 movq         %rax, $16(%rsp)
	0x48, 0x85, 0xdb, //0x00002515 testq        %rbx, %rbx
	0x0f, 0x85, 0x48, 0xfb, 0xff, 0xff, //0x00002518 jne          LBB0_425
	0xe9, 0xcb, 0xfb, 0xff, 0xff, //0x0000251e jmp          LBB0_435
	//0x00002523 LBB0_499
	0x48, 0x85, 0xdb, //0x00002523 testq        %rbx, %rbx
	0x0f, 0x84, 0xc2, 0x02, 0x00, 0x00, //0x00002526 je           LBB0_530
	0x4c, 0x89, 0xee, //0x0000252c movq         %r13, %rsi
	0x48, 0xf7, 0xd6, //0x0000252f notq         %rsi
	0x4c, 0x01, 0xe6, //0x00002532 addq         %r12, %rsi
	0x48, 0x8b, 0x54, 0x24, 0x10, //0x00002535 movq         $16(%rsp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x0000253a cmpq         $-1, %rdx
	0x48, 0x89, 0xd1, //0x0000253e movq         %rdx, %rcx
	0x48, 0x0f, 0x44, 0xce, //0x00002541 cmoveq       %rsi, %rcx
	0x48, 0x0f, 0x45, 0xf2, //0x00002545 cmovneq      %rdx, %rsi
	0x49, 0x83, 0xc4, 0x01, //0x00002549 addq         $1, %r12
	0x48, 0x83, 0xc3, 0xff, //0x0000254d addq         $-1, %rbx
	0x48, 0x89, 0x4c, 0x24, 0x10, //0x00002551 movq         %rcx, $16(%rsp)
	0x4d, 0x89, 0xf1, //0x00002556 movq         %r14, %r9
	0x49, 0x89, 0xc3, //0x00002559 movq         %rax, %r11
	0x48, 0x85, 0xdb, //0x0000255c testq        %rbx, %rbx
	0x0f, 0x85, 0x6b, 0xfc, 0xff, 0xff, //0x0000255f jne          LBB0_447
	0xe9, 0x60, 0x04, 0x00, 0x00, //0x00002565 jmp          LBB0_496
	//0x0000256a LBB0_501
	0x48, 0x85, 0xdb, //0x0000256a testq        %rbx, %rbx
	0x0f, 0x84, 0xa4, 0x0d, 0x00, 0x00, //0x0000256d je           LBB0_268
	0x4d, 0x89, 0xe9, //0x00002573 movq         %r13, %r9
	0x49, 0xf7, 0xd1, //0x00002576 notq         %r9
	0x4d, 0x01, 0xe1, //0x00002579 addq         %r12, %r9
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x0000257c movq         $16(%rsp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x00002581 cmpq         $-1, %rcx
	0x48, 0x89, 0xc8, //0x00002585 movq         %rcx, %rax
	0x49, 0x0f, 0x44, 0xc1, //0x00002588 cmoveq       %r9, %rax
	0x4c, 0x0f, 0x45, 0xc9, //0x0000258c cmovneq      %rcx, %r9
	0x49, 0x83, 0xc4, 0x01, //0x00002590 addq         $1, %r12
	0x48, 0x83, 0xc3, 0xff, //0x00002594 addq         $-1, %rbx
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00002598 movq         %rax, $16(%rsp)
	0x48, 0x85, 0xdb, //0x0000259d testq        %rbx, %rbx
	0x0f, 0x85, 0x34, 0xfd, 0xff, 0xff, //0x000025a0 jne          LBB0_463
	0xe9, 0xb7, 0xfd, 0xff, 0xff, //0x000025a6 jmp          LBB0_473
	//0x000025ab LBB0_503
	0x48, 0x85, 0xdb, //0x000025ab testq        %rbx, %rbx
	0x0f, 0x84, 0x3a, 0x02, 0x00, 0x00, //0x000025ae je           LBB0_530
	0x4c, 0x89, 0xee, //0x000025b4 movq         %r13, %rsi
	0x48, 0xf7, 0xd6, //0x000025b7 notq         %rsi
	0x4c, 0x01, 0xe6, //0x000025ba addq         %r12, %rsi
	0x48, 0x8b, 0x54, 0x24, 0x10, //0x000025bd movq         $16(%rsp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x000025c2 cmpq         $-1, %rdx
	0x48, 0x89, 0xd1, //0x000025c6 movq         %rdx, %rcx
	0x48, 0x0f, 0x44, 0xce, //0x000025c9 cmoveq       %rsi, %rcx
	0x48, 0x0f, 0x45, 0xf2, //0x000025cd cmovneq      %rdx, %rsi
	0x49, 0x83, 0xc4, 0x01, //0x000025d1 addq         $1, %r12
	0x48, 0x83, 0xc3, 0xff, //0x000025d5 addq         $-1, %rbx
	0x48, 0x89, 0x4c, 0x24, 0x10, //0x000025d9 movq         %rcx, $16(%rsp)
	0x4d, 0x89, 0xf1, //0x000025de movq         %r14, %r9
	0x49, 0x89, 0xc3, //0x000025e1 movq         %rax, %r11
	0x48, 0x85, 0xdb, //0x000025e4 testq        %rbx, %rbx
	0x0f, 0x85, 0x57, 0xfe, 0xff, 0xff, //0x000025e7 jne          LBB0_485
	0xe9, 0xd8, 0x03, 0x00, 0x00, //0x000025ed jmp          LBB0_496
	//0x000025f2 LBB0_505
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x000025f2 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x000025f8 jne          LBB0_507
	0x4c, 0x89, 0xe0, //0x000025fe movq         %r12, %rax
	0x4c, 0x29, 0xe8, //0x00002601 subq         %r13, %rax
	0x48, 0x0f, 0xbc, 0xca, //0x00002604 bsfq         %rdx, %rcx
	0x48, 0x01, 0xc1, //0x00002608 addq         %rax, %rcx
	0x48, 0x89, 0x4c, 0x24, 0x10, //0x0000260b movq         %rcx, $16(%rsp)
	//0x00002610 LBB0_507
	0x44, 0x89, 0xc8, //0x00002610 movl         %r9d, %eax
	0xf7, 0xd0, //0x00002613 notl         %eax
	0x21, 0xd0, //0x00002615 andl         %edx, %eax
	0x8d, 0x0c, 0x00, //0x00002617 leal         (%rax,%rax), %ecx
	0x41, 0x8d, 0x3c, 0x41, //0x0000261a leal         (%r9,%rax,2), %edi
	0xf7, 0xd1, //0x0000261e notl         %ecx
	0x21, 0xd1, //0x00002620 andl         %edx, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002622 andl         $-1431655766, %ecx
	0x45, 0x31, 0xc9, //0x00002628 xorl         %r9d, %r9d
	0x01, 0xc1, //0x0000262b addl         %eax, %ecx
	0x41, 0x0f, 0x92, 0xc1, //0x0000262d setb         %r9b
	0x01, 0xc9, //0x00002631 addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x00002633 xorl         $1431655765, %ecx
	0x21, 0xf9, //0x00002639 andl         %edi, %ecx
	0xf7, 0xd1, //0x0000263b notl         %ecx
	0x21, 0xce, //0x0000263d andl         %ecx, %esi
	0x48, 0x85, 0xf6, //0x0000263f testq        %rsi, %rsi
	0x0f, 0x85, 0x56, 0xf5, 0xff, 0xff, //0x00002642 jne          LBB0_89
	0xe9, 0x64, 0xf7, 0xff, 0xff, //0x00002648 jmp          LBB0_259
	//0x0000264d LBB0_508
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x0000264d cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00002653 jne          LBB0_510
	0x4c, 0x89, 0xe1, //0x00002659 movq         %r12, %rcx
	0x4c, 0x29, 0xe9, //0x0000265c subq         %r13, %rcx
	0x48, 0x0f, 0xbc, 0xf2, //0x0000265f bsfq         %rdx, %rsi
	0x48, 0x01, 0xce, //0x00002663 addq         %rcx, %rsi
	0x48, 0x89, 0x74, 0x24, 0x10, //0x00002666 movq         %rsi, $16(%rsp)
	//0x0000266b LBB0_510
	0x44, 0x89, 0xcf, //0x0000266b movl         %r9d, %edi
	0xf7, 0xd7, //0x0000266e notl         %edi
	0x21, 0xd7, //0x00002670 andl         %edx, %edi
	0x8d, 0x0c, 0x3f, //0x00002672 leal         (%rdi,%rdi), %ecx
	0x41, 0x8d, 0x34, 0x79, //0x00002675 leal         (%r9,%rdi,2), %esi
	0xf7, 0xd1, //0x00002679 notl         %ecx
	0x21, 0xd1, //0x0000267b andl         %edx, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000267d andl         $-1431655766, %ecx
	0x45, 0x31, 0xc9, //0x00002683 xorl         %r9d, %r9d
	0x01, 0xf9, //0x00002686 addl         %edi, %ecx
	0x41, 0x0f, 0x92, 0xc1, //0x00002688 setb         %r9b
	0x01, 0xc9, //0x0000268c addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x0000268e xorl         $1431655765, %ecx
	0x21, 0xf1, //0x00002694 andl         %esi, %ecx
	0xf7, 0xd1, //0x00002696 notl         %ecx
	0x41, 0x21, 0xcb, //0x00002698 andl         %ecx, %r11d
	0x4d, 0x85, 0xdb, //0x0000269b testq        %r11, %r11
	0x0f, 0x85, 0xf0, 0xf7, 0xff, 0xff, //0x0000269e jne          LBB0_355
	//0x000026a4 LBB0_511
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000026a4 movl         $64, %edx
	//0x000026a9 LBB0_512
	0xc5, 0xbd, 0xda, 0xc8, //0x000026a9 vpminub      %ymm0, %ymm8, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x000026ad vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x000026b1 vpmovmskb    %ymm0, %esi
	0x0f, 0xbc, 0xfe, //0x000026b5 bsfl         %esi, %edi
	0x4d, 0x85, 0xdb, //0x000026b8 testq        %r11, %r11
	0x0f, 0x85, 0xba, 0xfa, 0xff, 0xff, //0x000026bb jne          LBB0_441
	0x85, 0xf6, //0x000026c1 testl        %esi, %esi
	0x0f, 0x85, 0x67, 0x0c, 0x00, 0x00, //0x000026c3 jne          LBB0_662
	0x49, 0x83, 0xc4, 0x20, //0x000026c9 addq         $32, %r12
	0x48, 0x83, 0xc3, 0xe0, //0x000026cd addq         $-32, %rbx
	//0x000026d1 LBB0_515
	0x4d, 0x85, 0xc9, //0x000026d1 testq        %r9, %r9
	0x0f, 0x85, 0xd8, 0x00, 0x00, 0x00, //0x000026d4 jne          LBB0_528
	0x48, 0x8b, 0x74, 0x24, 0x10, //0x000026da movq         $16(%rsp), %rsi
	0x48, 0x85, 0xdb, //0x000026df testq        %rbx, %rbx
	0x0f, 0x84, 0x06, 0x01, 0x00, 0x00, //0x000026e2 je           LBB0_530
	//0x000026e8 LBB0_517
	0x41, 0x0f, 0xb6, 0x14, 0x24, //0x000026e8 movzbl       (%r12), %edx
	0x80, 0xfa, 0x22, //0x000026ed cmpb         $34, %dl
	0x0f, 0x84, 0x5f, 0x00, 0x00, 0x00, //0x000026f0 je           LBB0_525
	0x80, 0xfa, 0x5c, //0x000026f6 cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x000026f9 je           LBB0_522
	0x80, 0xfa, 0x1f, //0x000026ff cmpb         $31, %dl
	0x0f, 0x86, 0x68, 0x0c, 0x00, 0x00, //0x00002702 jbe          LBB0_663
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00002708 movq         $-1, %rdx
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x0000270f movl         $1, %edi
	//0x00002714 LBB0_521
	0x49, 0x01, 0xfc, //0x00002714 addq         %rdi, %r12
	0x48, 0x01, 0xd3, //0x00002717 addq         %rdx, %rbx
	0x0f, 0x85, 0xc8, 0xff, 0xff, 0xff, //0x0000271a jne          LBB0_517
	0xe9, 0xc9, 0x00, 0x00, 0x00, //0x00002720 jmp          LBB0_530
	//0x00002725 LBB0_522
	0x48, 0x83, 0xfb, 0x01, //0x00002725 cmpq         $1, %rbx
	0x0f, 0x84, 0xbf, 0x00, 0x00, 0x00, //0x00002729 je           LBB0_530
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x0000272f movq         $-2, %rdx
	0xbf, 0x02, 0x00, 0x00, 0x00, //0x00002736 movl         $2, %edi
	0x48, 0x83, 0xfe, 0xff, //0x0000273b cmpq         $-1, %rsi
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x0000273f jne          LBB0_521
	0x4c, 0x89, 0xe6, //0x00002745 movq         %r12, %rsi
	0x4c, 0x29, 0xee, //0x00002748 subq         %r13, %rsi
	0x48, 0x89, 0x74, 0x24, 0x10, //0x0000274b movq         %rsi, $16(%rsp)
	0xe9, 0xbf, 0xff, 0xff, 0xff, //0x00002750 jmp          LBB0_521
	//0x00002755 LBB0_525
	0x4d, 0x29, 0xec, //0x00002755 subq         %r13, %r12
	0x49, 0x83, 0xc4, 0x01, //0x00002758 addq         $1, %r12
	0x4d, 0x89, 0xf1, //0x0000275c movq         %r14, %r9
	0x48, 0x8b, 0x7c, 0x24, 0x20, //0x0000275f movq         $32(%rsp), %rdi
	0x49, 0x89, 0xc3, //0x00002764 movq         %rax, %r11
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x00002767 movq         $40(%rsp), %rdx
	0xe9, 0x7b, 0xdd, 0xff, 0xff, //0x0000276c jmp          LBB0_33
	//0x00002771 LBB0_526
	0x48, 0x85, 0xdb, //0x00002771 testq        %rbx, %rbx
	0x0f, 0x84, 0x9d, 0x0b, 0x00, 0x00, //0x00002774 je           LBB0_268
	0x4d, 0x89, 0xe9, //0x0000277a movq         %r13, %r9
	0x49, 0xf7, 0xd1, //0x0000277d notq         %r9
	0x4d, 0x01, 0xe1, //0x00002780 addq         %r12, %r9
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00002783 movq         $16(%rsp), %rcx
	0x48, 0x83, 0xf9, 0xff, //0x00002788 cmpq         $-1, %rcx
	0x48, 0x89, 0xc8, //0x0000278c movq         %rcx, %rax
	0x49, 0x0f, 0x44, 0xc1, //0x0000278f cmoveq       %r9, %rax
	0x4c, 0x0f, 0x45, 0xc9, //0x00002793 cmovneq      %rcx, %r9
	0x49, 0x83, 0xc4, 0x01, //0x00002797 addq         $1, %r12
	0x48, 0x83, 0xc3, 0xff, //0x0000279b addq         $-1, %rbx
	0x48, 0x89, 0x44, 0x24, 0x10, //0x0000279f movq         %rax, $16(%rsp)
	0x48, 0x85, 0xdb, //0x000027a4 testq        %rbx, %rbx
	0x0f, 0x85, 0x23, 0xf6, 0xff, 0xff, //0x000027a7 jne          LBB0_262
	0xe9, 0x3c, 0xf9, 0xff, 0xff, //0x000027ad jmp          LBB0_435
	//0x000027b2 LBB0_528
	0x48, 0x85, 0xdb, //0x000027b2 testq        %rbx, %rbx
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x000027b5 je           LBB0_530
	0x4c, 0x89, 0xee, //0x000027bb movq         %r13, %rsi
	0x48, 0xf7, 0xd6, //0x000027be notq         %rsi
	0x4c, 0x01, 0xe6, //0x000027c1 addq         %r12, %rsi
	0x48, 0x8b, 0x54, 0x24, 0x10, //0x000027c4 movq         $16(%rsp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x000027c9 cmpq         $-1, %rdx
	0x48, 0x89, 0xd1, //0x000027cd movq         %rdx, %rcx
	0x48, 0x0f, 0x44, 0xce, //0x000027d0 cmoveq       %rsi, %rcx
	0x48, 0x0f, 0x45, 0xf2, //0x000027d4 cmovneq      %rdx, %rsi
	0x49, 0x83, 0xc4, 0x01, //0x000027d8 addq         $1, %r12
	0x48, 0x83, 0xc3, 0xff, //0x000027dc addq         $-1, %rbx
	0x48, 0x89, 0x4c, 0x24, 0x10, //0x000027e0 movq         %rcx, $16(%rsp)
	0x48, 0x85, 0xdb, //0x000027e5 testq        %rbx, %rbx
	0x0f, 0x85, 0xfa, 0xfe, 0xff, 0xff, //0x000027e8 jne          LBB0_517
	//0x000027ee LBB0_530
	0x4d, 0x89, 0xf1, //0x000027ee movq         %r14, %r9
	0xe9, 0xd4, 0x01, 0x00, 0x00, //0x000027f1 jmp          LBB0_496
	//0x000027f6 LBB0_531
	0x49, 0x89, 0x01, //0x000027f6 movq         %rax, (%r9)
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000027f9 movq         $-1, %r14
	0xe9, 0x3a, 0x09, 0x00, 0x00, //0x00002800 jmp          LBB0_650
	//0x00002805 LBB0_532
	0xc5, 0xf5, 0x74, 0xc2, //0x00002805 vpcmpeqb     %ymm2, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00002809 vpmovmskb    %ymm0, %eax
	0xf7, 0xd0, //0x0000280d notl         %eax
	0x0f, 0xbc, 0xc0, //0x0000280f bsfl         %eax, %eax
	0x48, 0x29, 0xd0, //0x00002812 subq         %rdx, %rax
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002815 movq         $-1, %r14
	0x48, 0x39, 0xd8, //0x0000281c cmpq         %rbx, %rax
	0x0f, 0x83, 0x1a, 0x09, 0x00, 0x00, //0x0000281f jae          LBB0_650
	//0x00002825 LBB0_533
	0x48, 0x8d, 0x50, 0x01, //0x00002825 leaq         $1(%rax), %rdx
	0x49, 0x89, 0x11, //0x00002829 movq         %rdx, (%r9)
	0x41, 0x0f, 0xbe, 0x0c, 0x03, //0x0000282c movsbl       (%r11,%rax), %ecx
	0x83, 0xf9, 0x7b, //0x00002831 cmpl         $123, %ecx
	0x0f, 0x87, 0x9f, 0x01, 0x00, 0x00, //0x00002834 ja           LBB0_556
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x0000283a movq         $-1, %r14
	0x48, 0x8d, 0x35, 0x34, 0x0b, 0x00, 0x00, //0x00002841 leaq         $2868(%rip), %rsi  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8e, //0x00002848 movslq       (%rsi,%rcx,4), %rcx
	0x48, 0x01, 0xf1, //0x0000284c addq         %rsi, %rcx
	0xff, 0xe1, //0x0000284f jmpq         *%rcx
	//0x00002851 LBB0_535
	0x48, 0x8b, 0x7f, 0x08, //0x00002851 movq         $8(%rdi), %rdi
	0x48, 0x89, 0xfe, //0x00002855 movq         %rdi, %rsi
	0x48, 0x29, 0xd6, //0x00002858 subq         %rdx, %rsi
	0x48, 0x83, 0xfe, 0x20, //0x0000285b cmpq         $32, %rsi
	0x0f, 0x82, 0x77, 0x0a, 0x00, 0x00, //0x0000285f jb           LBB0_653
	0x48, 0x89, 0xc6, //0x00002865 movq         %rax, %rsi
	0x48, 0xf7, 0xd6, //0x00002868 notq         %rsi
	0xc5, 0xfe, 0x6f, 0x05, 0xad, 0xd7, 0xff, 0xff, //0x0000286b vmovdqu      $-10323(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xc5, 0xd7, 0xff, 0xff, //0x00002873 vmovdqu      $-10299(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0xdd, 0xd7, 0xff, 0xff, //0x0000287b vmovdqu      $-10275(%rip), %ymm2  /* LCPI0_3+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002883 .p2align 4, 0x90
	//0x00002890 LBB0_537
	0xc4, 0xc1, 0x7e, 0x6f, 0x1c, 0x13, //0x00002890 vmovdqu      (%r11,%rdx), %ymm3
	0xc5, 0xe5, 0x74, 0xe0, //0x00002896 vpcmpeqb     %ymm0, %ymm3, %ymm4
	0xc5, 0xe5, 0xdb, 0xd9, //0x0000289a vpand        %ymm1, %ymm3, %ymm3
	0xc5, 0xe5, 0x74, 0xda, //0x0000289e vpcmpeqb     %ymm2, %ymm3, %ymm3
	0xc5, 0xe5, 0xeb, 0xdc, //0x000028a2 vpor         %ymm4, %ymm3, %ymm3
	0xc5, 0xfd, 0xd7, 0xcb, //0x000028a6 vpmovmskb    %ymm3, %ecx
	0x85, 0xc9, //0x000028aa testl        %ecx, %ecx
	0x0f, 0x85, 0xca, 0x00, 0x00, 0x00, //0x000028ac jne          LBB0_551
	0x48, 0x83, 0xc2, 0x20, //0x000028b2 addq         $32, %rdx
	0x48, 0x8d, 0x0c, 0x37, //0x000028b6 leaq         (%rdi,%rsi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x000028ba addq         $-32, %rcx
	0x48, 0x83, 0xc6, 0xe0, //0x000028be addq         $-32, %rsi
	0x48, 0x83, 0xf9, 0x1f, //0x000028c2 cmpq         $31, %rcx
	0x0f, 0x87, 0xc4, 0xff, 0xff, 0xff, //0x000028c6 ja           LBB0_537
	0x4c, 0x89, 0xda, //0x000028cc movq         %r11, %rdx
	0x48, 0x29, 0xf2, //0x000028cf subq         %rsi, %rdx
	0x48, 0x01, 0xf7, //0x000028d2 addq         %rsi, %rdi
	0x48, 0x89, 0xfe, //0x000028d5 movq         %rdi, %rsi
	0x48, 0x83, 0xfe, 0x10, //0x000028d8 cmpq         $16, %rsi
	0x0f, 0x82, 0x54, 0x00, 0x00, 0x00, //0x000028dc jb           LBB0_543
	//0x000028e2 LBB0_540
	0x4c, 0x89, 0xdf, //0x000028e2 movq         %r11, %rdi
	0x48, 0x29, 0xd7, //0x000028e5 subq         %rdx, %rdi
	0xc5, 0xfa, 0x6f, 0x05, 0x10, 0xd9, 0xff, 0xff, //0x000028e8 vmovdqu      $-9968(%rip), %xmm0  /* LCPI0_4+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0x18, 0xd9, 0xff, 0xff, //0x000028f0 vmovdqu      $-9960(%rip), %xmm1  /* LCPI0_5+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x20, 0xd9, 0xff, 0xff, //0x000028f8 vmovdqu      $-9952(%rip), %xmm2  /* LCPI0_6+0(%rip) */
	//0x00002900 LBB0_541
	0xc5, 0xfa, 0x6f, 0x1a, //0x00002900 vmovdqu      (%rdx), %xmm3
	0xc5, 0xe1, 0x74, 0xe0, //0x00002904 vpcmpeqb     %xmm0, %xmm3, %xmm4
	0xc5, 0xe1, 0xdb, 0xd9, //0x00002908 vpand        %xmm1, %xmm3, %xmm3
	0xc5, 0xe1, 0x74, 0xda, //0x0000290c vpcmpeqb     %xmm2, %xmm3, %xmm3
	0xc5, 0xe1, 0xeb, 0xdc, //0x00002910 vpor         %xmm4, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xcb, //0x00002914 vpmovmskb    %xmm3, %ecx
	0x85, 0xc9, //0x00002918 testl        %ecx, %ecx
	0x0f, 0x85, 0x3c, 0x08, 0x00, 0x00, //0x0000291a jne          LBB0_628
	0x48, 0x83, 0xc2, 0x10, //0x00002920 addq         $16, %rdx
	0x48, 0x83, 0xc6, 0xf0, //0x00002924 addq         $-16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00002928 addq         $-16, %rdi
	0x48, 0x83, 0xfe, 0x0f, //0x0000292c cmpq         $15, %rsi
	0x0f, 0x87, 0xca, 0xff, 0xff, 0xff, //0x00002930 ja           LBB0_541
	//0x00002936 LBB0_543
	0x48, 0x85, 0xf6, //0x00002936 testq        %rsi, %rsi
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00002939 je           LBB0_550
	0x48, 0x8d, 0x1c, 0x32, //0x0000293f leaq         (%rdx,%rsi), %rbx
	0x31, 0xff, //0x00002943 xorl         %edi, %edi
	//0x00002945 LBB0_545
	0x0f, 0xb6, 0x0c, 0x3a, //0x00002945 movzbl       (%rdx,%rdi), %ecx
	0x80, 0xf9, 0x2c, //0x00002949 cmpb         $44, %cl
	0x0f, 0x84, 0x9c, 0x09, 0x00, 0x00, //0x0000294c je           LBB0_654
	0x80, 0xf9, 0x7d, //0x00002952 cmpb         $125, %cl
	0x0f, 0x84, 0x93, 0x09, 0x00, 0x00, //0x00002955 je           LBB0_654
	0x80, 0xf9, 0x5d, //0x0000295b cmpb         $93, %cl
	0x0f, 0x84, 0x8a, 0x09, 0x00, 0x00, //0x0000295e je           LBB0_654
	0x48, 0x83, 0xc7, 0x01, //0x00002964 addq         $1, %rdi
	0x48, 0x39, 0xfe, //0x00002968 cmpq         %rdi, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x0000296b jne          LBB0_545
	0x48, 0x89, 0xda, //0x00002971 movq         %rbx, %rdx
	//0x00002974 LBB0_550
	0x4c, 0x29, 0xda, //0x00002974 subq         %r11, %rdx
	0xe9, 0x78, 0x09, 0x00, 0x00, //0x00002977 jmp          LBB0_655
	//0x0000297c LBB0_551
	0x0f, 0xbc, 0xc9, //0x0000297c bsfl         %ecx, %ecx
	0x48, 0x29, 0xf1, //0x0000297f subq         %rsi, %rcx
	//0x00002982 LBB0_552
	0x49, 0x89, 0x09, //0x00002982 movq         %rcx, (%r9)
	0x49, 0x89, 0xc6, //0x00002985 movq         %rax, %r14
	0xe9, 0xb2, 0x07, 0x00, 0x00, //0x00002988 jmp          LBB0_650
	//0x0000298d LBB0_553
	0x4d, 0x89, 0x11, //0x0000298d movq         %r10, (%r9)
	//0x00002990 LBB0_554
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002990 movq         $-1, %r14
	0xe9, 0xa3, 0x07, 0x00, 0x00, //0x00002997 jmp          LBB0_650
	//0x0000299c LBB0_555
	0x48, 0x8d, 0x48, 0x04, //0x0000299c leaq         $4(%rax), %rcx
	0x48, 0x3b, 0x4f, 0x08, //0x000029a0 cmpq         $8(%rdi), %rcx
	0x0f, 0x86, 0xd8, 0xff, 0xff, 0xff, //0x000029a4 jbe          LBB0_552
	0xe9, 0x90, 0x07, 0x00, 0x00, //0x000029aa jmp          LBB0_650
	//0x000029af LBB0_631
	0x49, 0xc7, 0xc6, 0xf9, 0xff, 0xff, 0xff, //0x000029af movq         $-7, %r14
	0xe9, 0x84, 0x07, 0x00, 0x00, //0x000029b6 jmp          LBB0_650
	//0x000029bb LBB0_495
	0x49, 0x83, 0xfc, 0xff, //0x000029bb cmpq         $-1, %r12
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x000029bf movq         $16(%rsp), %rax
	0x0f, 0x85, 0x6f, 0x07, 0x00, 0x00, //0x000029c4 jne          LBB0_649
	//0x000029ca LBB0_496
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000029ca movq         $-1, %r12
	0x4c, 0x89, 0xc0, //0x000029d1 movq         %r8, %rax
	0xe9, 0x60, 0x07, 0x00, 0x00, //0x000029d4 jmp          LBB0_649
	//0x000029d9 LBB0_556
	0x49, 0x89, 0x01, //0x000029d9 movq         %rax, (%r9)
	0xe9, 0xe2, 0x06, 0x00, 0x00, //0x000029dc jmp          LBB0_623
	//0x000029e1 LBB0_557
	0x4d, 0x89, 0xcd, //0x000029e1 movq         %r9, %r13
	0x4c, 0x8b, 0x47, 0x08, //0x000029e4 movq         $8(%rdi), %r8
	0x4d, 0x89, 0xc7, //0x000029e8 movq         %r8, %r15
	0x49, 0x29, 0xd7, //0x000029eb subq         %rdx, %r15
	0x49, 0x83, 0xff, 0x20, //0x000029ee cmpq         $32, %r15
	0x0f, 0x8c, 0x07, 0x09, 0x00, 0x00, //0x000029f2 jl           LBB0_657
	0x4d, 0x8d, 0x0c, 0x03, //0x000029f8 leaq         (%r11,%rax), %r9
	0x49, 0x29, 0xc0, //0x000029fc subq         %rax, %r8
	0xbb, 0x1f, 0x00, 0x00, 0x00, //0x000029ff movl         $31, %ebx
	0x45, 0x31, 0xff, //0x00002a04 xorl         %r15d, %r15d
	0xc5, 0xfe, 0x6f, 0x05, 0x71, 0xd6, 0xff, 0xff, //0x00002a07 vmovdqu      $-10639(%rip), %ymm0  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x89, 0xd6, 0xff, 0xff, //0x00002a0f vmovdqu      $-10615(%rip), %ymm1  /* LCPI0_8+0(%rip) */
	0x45, 0x31, 0xe4, //0x00002a17 xorl         %r12d, %r12d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002a1a .p2align 4, 0x90
	//0x00002a20 LBB0_559
	0xc4, 0x81, 0x7e, 0x6f, 0x54, 0x39, 0x01, //0x00002a20 vmovdqu      $1(%r9,%r15), %ymm2
	0xc5, 0xed, 0x74, 0xd8, //0x00002a27 vpcmpeqb     %ymm0, %ymm2, %ymm3
	0xc5, 0x7d, 0xd7, 0xd3, //0x00002a2b vpmovmskb    %ymm3, %r10d
	0xc5, 0xed, 0x74, 0xd1, //0x00002a2f vpcmpeqb     %ymm1, %ymm2, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00002a33 vpmovmskb    %ymm2, %ecx
	0x85, 0xc9, //0x00002a37 testl        %ecx, %ecx
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00002a39 jne          LBB0_562
	0x4d, 0x85, 0xe4, //0x00002a3f testq        %r12, %r12
	0x0f, 0x85, 0x08, 0x00, 0x00, 0x00, //0x00002a42 jne          LBB0_562
	0x45, 0x31, 0xe4, //0x00002a48 xorl         %r12d, %r12d
	0xe9, 0x31, 0x00, 0x00, 0x00, //0x00002a4b jmp          LBB0_563
	//0x00002a50 LBB0_562
	0x44, 0x89, 0xe6, //0x00002a50 movl         %r12d, %esi
	0xf7, 0xd6, //0x00002a53 notl         %esi
	0x21, 0xce, //0x00002a55 andl         %ecx, %esi
	0x8d, 0x14, 0x36, //0x00002a57 leal         (%rsi,%rsi), %edx
	0x44, 0x09, 0xe2, //0x00002a5a orl          %r12d, %edx
	0x89, 0xd7, //0x00002a5d movl         %edx, %edi
	0xf7, 0xd7, //0x00002a5f notl         %edi
	0x21, 0xcf, //0x00002a61 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002a63 andl         $-1431655766, %edi
	0x45, 0x31, 0xe4, //0x00002a69 xorl         %r12d, %r12d
	0x01, 0xf7, //0x00002a6c addl         %esi, %edi
	0x41, 0x0f, 0x92, 0xc4, //0x00002a6e setb         %r12b
	0x01, 0xff, //0x00002a72 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00002a74 xorl         $1431655765, %edi
	0x21, 0xd7, //0x00002a7a andl         %edx, %edi
	0xf7, 0xd7, //0x00002a7c notl         %edi
	0x41, 0x21, 0xfa, //0x00002a7e andl         %edi, %r10d
	//0x00002a81 LBB0_563
	0x4d, 0x85, 0xd2, //0x00002a81 testq        %r10, %r10
	0x0f, 0x85, 0xf9, 0x05, 0x00, 0x00, //0x00002a84 jne          LBB0_619
	0x49, 0x83, 0xc7, 0x20, //0x00002a8a addq         $32, %r15
	0x49, 0x8d, 0x0c, 0x18, //0x00002a8e leaq         (%r8,%rbx), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00002a92 addq         $-32, %rcx
	0x48, 0x83, 0xc3, 0xe0, //0x00002a96 addq         $-32, %rbx
	0x48, 0x83, 0xf9, 0x3f, //0x00002a9a cmpq         $63, %rcx
	0x0f, 0x8f, 0x7c, 0xff, 0xff, 0xff, //0x00002a9e jg           LBB0_559
	0x4d, 0x85, 0xe4, //0x00002aa4 testq        %r12, %r12
	0x0f, 0x85, 0x9f, 0x08, 0x00, 0x00, //0x00002aa7 jne          LBB0_660
	0x4b, 0x8d, 0x14, 0x0f, //0x00002aad leaq         (%r15,%r9), %rdx
	0x48, 0x83, 0xc2, 0x01, //0x00002ab1 addq         $1, %rdx
	0x49, 0xf7, 0xd7, //0x00002ab5 notq         %r15
	0x4d, 0x01, 0xc7, //0x00002ab8 addq         %r8, %r15
	//0x00002abb LBB0_567
	0x4d, 0x85, 0xff, //0x00002abb testq        %r15, %r15
	0x0f, 0x8e, 0x7b, 0x06, 0x00, 0x00, //0x00002abe jle          LBB0_650
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002ac4 movq         $-1, %r14
	0xe9, 0x17, 0x06, 0x00, 0x00, //0x00002acb jmp          LBB0_625
	//0x00002ad0 LBB0_569
	0x48, 0x8d, 0x48, 0x05, //0x00002ad0 leaq         $5(%rax), %rcx
	0x48, 0x3b, 0x4f, 0x08, //0x00002ad4 cmpq         $8(%rdi), %rcx
	0x0f, 0x86, 0xa4, 0xfe, 0xff, 0xff, //0x00002ad8 jbe          LBB0_552
	0xe9, 0x5c, 0x06, 0x00, 0x00, //0x00002ade jmp          LBB0_650
	//0x00002ae3 LBB0_570
	0x4d, 0x89, 0xca, //0x00002ae3 movq         %r9, %r10
	0x4c, 0x8b, 0x7f, 0x08, //0x00002ae6 movq         $8(%rdi), %r15
	0x49, 0x29, 0xd7, //0x00002aea subq         %rdx, %r15
	0x49, 0x01, 0xd3, //0x00002aed addq         %rdx, %r11
	0x45, 0x31, 0xc0, //0x00002af0 xorl         %r8d, %r8d
	0xc5, 0xfe, 0x6f, 0x05, 0xa5, 0xd5, 0xff, 0xff, //0x00002af3 vmovdqu      $-10843(%rip), %ymm0  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x7d, 0xd5, 0xff, 0xff, //0x00002afb vmovdqu      $-10883(%rip), %ymm1  /* LCPI0_7+0(%rip) */
	0xc5, 0xe9, 0x76, 0xd2, //0x00002b03 vpcmpeqd     %xmm2, %xmm2, %xmm2
	0xc5, 0xfe, 0x6f, 0x1d, 0xb1, 0xd5, 0xff, 0xff, //0x00002b07 vmovdqu      $-10831(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xc9, 0xd5, 0xff, 0xff, //0x00002b0f vmovdqu      $-10807(%rip), %ymm4  /* LCPI0_10+0(%rip) */
	0xc4, 0x41, 0x31, 0xef, 0xc9, //0x00002b17 vpxor        %xmm9, %xmm9, %xmm9
	0x45, 0x31, 0xe4, //0x00002b1c xorl         %r12d, %r12d
	0x45, 0x31, 0xc9, //0x00002b1f xorl         %r9d, %r9d
	0x31, 0xd2, //0x00002b22 xorl         %edx, %edx
	0x49, 0x83, 0xff, 0x40, //0x00002b24 cmpq         $64, %r15
	0x0f, 0x8c, 0x4b, 0x01, 0x00, 0x00, //0x00002b28 jl           LBB0_579
	//0x00002b2e LBB0_573
	0xc4, 0xc1, 0x7e, 0x6f, 0x3b, //0x00002b2e vmovdqu      (%r11), %ymm7
	0xc4, 0xc1, 0x7e, 0x6f, 0x73, 0x20, //0x00002b33 vmovdqu      $32(%r11), %ymm6
	0xc5, 0x45, 0x74, 0xc0, //0x00002b39 vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf0, //0x00002b3d vpmovmskb    %ymm8, %esi
	0xc5, 0x4d, 0x74, 0xc0, //0x00002b42 vpcmpeqb     %ymm0, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xc8, //0x00002b46 vpmovmskb    %ymm8, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00002b4b shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x00002b4f orq          %rcx, %rsi
	0x48, 0x89, 0xf1, //0x00002b52 movq         %rsi, %rcx
	0x4c, 0x09, 0xe1, //0x00002b55 orq          %r12, %rcx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00002b58 jne          LBB0_575
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002b5e movq         $-1, %rsi
	0x45, 0x31, 0xe4, //0x00002b65 xorl         %r12d, %r12d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00002b68 jmp          LBB0_576
	//0x00002b6d LBB0_575
	0x4c, 0x89, 0xe1, //0x00002b6d movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x00002b70 notq         %rcx
	0x48, 0x21, 0xf1, //0x00002b73 andq         %rsi, %rcx
	0x4c, 0x8d, 0x2c, 0x09, //0x00002b76 leaq         (%rcx,%rcx), %r13
	0x4d, 0x09, 0xe5, //0x00002b7a orq          %r12, %r13
	0x4c, 0x89, 0xeb, //0x00002b7d movq         %r13, %rbx
	0x48, 0xf7, 0xd3, //0x00002b80 notq         %rbx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002b83 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x00002b8d andq         %rdi, %rsi
	0x48, 0x21, 0xde, //0x00002b90 andq         %rbx, %rsi
	0x45, 0x31, 0xe4, //0x00002b93 xorl         %r12d, %r12d
	0x48, 0x01, 0xce, //0x00002b96 addq         %rcx, %rsi
	0x41, 0x0f, 0x92, 0xc4, //0x00002b99 setb         %r12b
	0x48, 0x01, 0xf6, //0x00002b9d addq         %rsi, %rsi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002ba0 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xce, //0x00002baa xorq         %rcx, %rsi
	0x4c, 0x21, 0xee, //0x00002bad andq         %r13, %rsi
	0x48, 0xf7, 0xd6, //0x00002bb0 notq         %rsi
	//0x00002bb3 LBB0_576
	0xc5, 0x4d, 0x74, 0xc1, //0x00002bb3 vpcmpeqb     %ymm1, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xc8, //0x00002bb7 vpmovmskb    %ymm8, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00002bbc shlq         $32, %rcx
	0xc5, 0x45, 0x74, 0xc1, //0x00002bc0 vpcmpeqb     %ymm1, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf8, //0x00002bc4 vpmovmskb    %ymm8, %edi
	0x48, 0x09, 0xcf, //0x00002bc9 orq          %rcx, %rdi
	0x48, 0x21, 0xf7, //0x00002bcc andq         %rsi, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xef, //0x00002bcf vmovq        %rdi, %xmm5
	0xc4, 0xe3, 0x51, 0x44, 0xea, 0x00, //0x00002bd4 vpclmulqdq   $0, %xmm2, %xmm5, %xmm5
	0xc4, 0xc1, 0xf9, 0x7e, 0xed, //0x00002bda vmovq        %xmm5, %r13
	0x4d, 0x31, 0xc5, //0x00002bdf xorq         %r8, %r13
	0xc5, 0xc5, 0x74, 0xeb, //0x00002be2 vpcmpeqb     %ymm3, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xfd, //0x00002be6 vpmovmskb    %ymm5, %edi
	0xc5, 0xcd, 0x74, 0xeb, //0x00002bea vpcmpeqb     %ymm3, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xcd, //0x00002bee vpmovmskb    %ymm5, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00002bf2 shlq         $32, %rcx
	0x48, 0x09, 0xcf, //0x00002bf6 orq          %rcx, %rdi
	0x4c, 0x89, 0xe9, //0x00002bf9 movq         %r13, %rcx
	0x48, 0xf7, 0xd1, //0x00002bfc notq         %rcx
	0x48, 0x21, 0xcf, //0x00002bff andq         %rcx, %rdi
	0xc5, 0xc5, 0x74, 0xec, //0x00002c02 vpcmpeqb     %ymm4, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xdd, //0x00002c06 vpmovmskb    %ymm5, %ebx
	0xc5, 0xcd, 0x74, 0xec, //0x00002c0a vpcmpeqb     %ymm4, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xf5, //0x00002c0e vpmovmskb    %ymm5, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x00002c12 shlq         $32, %rsi
	0x48, 0x09, 0xf3, //0x00002c16 orq          %rsi, %rbx
	0x48, 0x21, 0xcb, //0x00002c19 andq         %rcx, %rbx
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00002c1c je           LBB0_571
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002c22 .p2align 4, 0x90
	//0x00002c30 LBB0_577
	0x48, 0x8d, 0x4b, 0xff, //0x00002c30 leaq         $-1(%rbx), %rcx
	0x48, 0x89, 0xce, //0x00002c34 movq         %rcx, %rsi
	0x48, 0x21, 0xfe, //0x00002c37 andq         %rdi, %rsi
	0xf3, 0x48, 0x0f, 0xb8, 0xf6, //0x00002c3a popcntq      %rsi, %rsi
	0x4c, 0x01, 0xce, //0x00002c3f addq         %r9, %rsi
	0x48, 0x39, 0xd6, //0x00002c42 cmpq         %rdx, %rsi
	0x0f, 0x86, 0xfd, 0x03, 0x00, 0x00, //0x00002c45 jbe          LBB0_618
	0x48, 0x83, 0xc2, 0x01, //0x00002c4b addq         $1, %rdx
	0x48, 0x21, 0xcb, //0x00002c4f andq         %rcx, %rbx
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00002c52 jne          LBB0_577
	//0x00002c58 LBB0_571
	0x49, 0xc1, 0xfd, 0x3f, //0x00002c58 sarq         $63, %r13
	0xf3, 0x48, 0x0f, 0xb8, 0xcf, //0x00002c5c popcntq      %rdi, %rcx
	0x49, 0x01, 0xc9, //0x00002c61 addq         %rcx, %r9
	0x49, 0x83, 0xc3, 0x40, //0x00002c64 addq         $64, %r11
	0x49, 0x83, 0xc7, 0xc0, //0x00002c68 addq         $-64, %r15
	0x4d, 0x89, 0xe8, //0x00002c6c movq         %r13, %r8
	0x49, 0x83, 0xff, 0x40, //0x00002c6f cmpq         $64, %r15
	0x0f, 0x8d, 0xb5, 0xfe, 0xff, 0xff, //0x00002c73 jge          LBB0_573
	//0x00002c79 LBB0_579
	0x4d, 0x85, 0xff, //0x00002c79 testq        %r15, %r15
	0x0f, 0x8e, 0x9d, 0x06, 0x00, 0x00, //0x00002c7c jle          LBB0_658
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x60, //0x00002c82 vmovdqu      %ymm9, $96(%rsp)
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x40, //0x00002c88 vmovdqu      %ymm9, $64(%rsp)
	0x44, 0x89, 0xd9, //0x00002c8e movl         %r11d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00002c91 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x00002c97 cmpl         $4033, %ecx
	0x0f, 0x82, 0x8b, 0xfe, 0xff, 0xff, //0x00002c9d jb           LBB0_573
	0x49, 0x83, 0xff, 0x20, //0x00002ca3 cmpq         $32, %r15
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x00002ca7 jb           LBB0_583
	0xc4, 0xc1, 0x7e, 0x6f, 0x2b, //0x00002cad vmovdqu      (%r11), %ymm5
	0xc5, 0xfe, 0x7f, 0x6c, 0x24, 0x40, //0x00002cb2 vmovdqu      %ymm5, $64(%rsp)
	0x49, 0x83, 0xc3, 0x20, //0x00002cb8 addq         $32, %r11
	0x49, 0x8d, 0x7f, 0xe0, //0x00002cbc leaq         $-32(%r15), %rdi
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x00002cc0 leaq         $96(%rsp), %rsi
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00002cc5 jmp          LBB0_584
	//0x00002cca LBB0_583
	0x48, 0x8d, 0x74, 0x24, 0x40, //0x00002cca leaq         $64(%rsp), %rsi
	0x4c, 0x89, 0xff, //0x00002ccf movq         %r15, %rdi
	//0x00002cd2 LBB0_584
	0x48, 0x83, 0xff, 0x10, //0x00002cd2 cmpq         $16, %rdi
	0x0f, 0x82, 0x5a, 0x00, 0x00, 0x00, //0x00002cd6 jb           LBB0_585
	0xc4, 0xc1, 0x7a, 0x6f, 0x2b, //0x00002cdc vmovdqu      (%r11), %xmm5
	0xc5, 0xfa, 0x7f, 0x2e, //0x00002ce1 vmovdqu      %xmm5, (%rsi)
	0x49, 0x83, 0xc3, 0x10, //0x00002ce5 addq         $16, %r11
	0x48, 0x83, 0xc6, 0x10, //0x00002ce9 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00002ced addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00002cf1 cmpq         $8, %rdi
	0x0f, 0x83, 0x45, 0x00, 0x00, 0x00, //0x00002cf5 jae          LBB0_590
	//0x00002cfb LBB0_586
	0x48, 0x83, 0xff, 0x04, //0x00002cfb cmpq         $4, %rdi
	0x0f, 0x82, 0x57, 0x00, 0x00, 0x00, //0x00002cff jb           LBB0_587
	//0x00002d05 LBB0_591
	0x41, 0x8b, 0x0b, //0x00002d05 movl         (%r11), %ecx
	0x89, 0x0e, //0x00002d08 movl         %ecx, (%rsi)
	0x49, 0x83, 0xc3, 0x04, //0x00002d0a addq         $4, %r11
	0x48, 0x83, 0xc6, 0x04, //0x00002d0e addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00002d12 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00002d16 cmpq         $2, %rdi
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x00002d1a jae          LBB0_592
	//0x00002d20 LBB0_588
	0x4c, 0x89, 0xdb, //0x00002d20 movq         %r11, %rbx
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00002d23 leaq         $64(%rsp), %r11
	0x48, 0x85, 0xff, //0x00002d28 testq        %rdi, %rdi
	0x0f, 0x85, 0x59, 0x00, 0x00, 0x00, //0x00002d2b jne          LBB0_593
	0xe9, 0xf8, 0xfd, 0xff, 0xff, //0x00002d31 jmp          LBB0_573
	//0x00002d36 LBB0_585
	0x48, 0x83, 0xff, 0x08, //0x00002d36 cmpq         $8, %rdi
	0x0f, 0x82, 0xbb, 0xff, 0xff, 0xff, //0x00002d3a jb           LBB0_586
	//0x00002d40 LBB0_590
	0x49, 0x8b, 0x0b, //0x00002d40 movq         (%r11), %rcx
	0x48, 0x89, 0x0e, //0x00002d43 movq         %rcx, (%rsi)
	0x49, 0x83, 0xc3, 0x08, //0x00002d46 addq         $8, %r11
	0x48, 0x83, 0xc6, 0x08, //0x00002d4a addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00002d4e addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00002d52 cmpq         $4, %rdi
	0x0f, 0x83, 0xa9, 0xff, 0xff, 0xff, //0x00002d56 jae          LBB0_591
	//0x00002d5c LBB0_587
	0x48, 0x83, 0xff, 0x02, //0x00002d5c cmpq         $2, %rdi
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00002d60 jb           LBB0_588
	//0x00002d66 LBB0_592
	0x41, 0x0f, 0xb7, 0x0b, //0x00002d66 movzwl       (%r11), %ecx
	0x66, 0x89, 0x0e, //0x00002d6a movw         %cx, (%rsi)
	0x49, 0x83, 0xc3, 0x02, //0x00002d6d addq         $2, %r11
	0x48, 0x83, 0xc6, 0x02, //0x00002d71 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00002d75 addq         $-2, %rdi
	0x4c, 0x89, 0xdb, //0x00002d79 movq         %r11, %rbx
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00002d7c leaq         $64(%rsp), %r11
	0x48, 0x85, 0xff, //0x00002d81 testq        %rdi, %rdi
	0x0f, 0x84, 0xa4, 0xfd, 0xff, 0xff, //0x00002d84 je           LBB0_573
	//0x00002d8a LBB0_593
	0x8a, 0x0b, //0x00002d8a movb         (%rbx), %cl
	0x88, 0x0e, //0x00002d8c movb         %cl, (%rsi)
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00002d8e leaq         $64(%rsp), %r11
	0xe9, 0x96, 0xfd, 0xff, 0xff, //0x00002d93 jmp          LBB0_573
	//0x00002d98 LBB0_594
	0x4d, 0x89, 0xca, //0x00002d98 movq         %r9, %r10
	0x4c, 0x8b, 0x7f, 0x08, //0x00002d9b movq         $8(%rdi), %r15
	0x49, 0x29, 0xd7, //0x00002d9f subq         %rdx, %r15
	0x49, 0x01, 0xd3, //0x00002da2 addq         %rdx, %r11
	0x45, 0x31, 0xc0, //0x00002da5 xorl         %r8d, %r8d
	0xc5, 0xfe, 0x6f, 0x05, 0xf0, 0xd2, 0xff, 0xff, //0x00002da8 vmovdqu      $-11536(%rip), %ymm0  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xc8, 0xd2, 0xff, 0xff, //0x00002db0 vmovdqu      $-11576(%rip), %ymm1  /* LCPI0_7+0(%rip) */
	0xc5, 0xe9, 0x76, 0xd2, //0x00002db8 vpcmpeqd     %xmm2, %xmm2, %xmm2
	0xc5, 0xfe, 0x6f, 0x1d, 0x3c, 0xd3, 0xff, 0xff, //0x00002dbc vmovdqu      $-11460(%rip), %ymm3  /* LCPI0_11+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x94, 0xd2, 0xff, 0xff, //0x00002dc4 vmovdqu      $-11628(%rip), %ymm4  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x31, 0xef, 0xc9, //0x00002dcc vpxor        %xmm9, %xmm9, %xmm9
	0x45, 0x31, 0xe4, //0x00002dd1 xorl         %r12d, %r12d
	0x45, 0x31, 0xc9, //0x00002dd4 xorl         %r9d, %r9d
	0x31, 0xd2, //0x00002dd7 xorl         %edx, %edx
	0x49, 0x83, 0xff, 0x40, //0x00002dd9 cmpq         $64, %r15
	0x0f, 0x8c, 0x46, 0x01, 0x00, 0x00, //0x00002ddd jl           LBB0_603
	//0x00002de3 LBB0_597
	0xc4, 0xc1, 0x7e, 0x6f, 0x3b, //0x00002de3 vmovdqu      (%r11), %ymm7
	0xc4, 0xc1, 0x7e, 0x6f, 0x73, 0x20, //0x00002de8 vmovdqu      $32(%r11), %ymm6
	0xc5, 0x45, 0x74, 0xc0, //0x00002dee vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf0, //0x00002df2 vpmovmskb    %ymm8, %esi
	0xc5, 0x4d, 0x74, 0xc0, //0x00002df7 vpcmpeqb     %ymm0, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xc8, //0x00002dfb vpmovmskb    %ymm8, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00002e00 shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x00002e04 orq          %rcx, %rsi
	0x48, 0x89, 0xf1, //0x00002e07 movq         %rsi, %rcx
	0x4c, 0x09, 0xe1, //0x00002e0a orq          %r12, %rcx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00002e0d jne          LBB0_599
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002e13 movq         $-1, %rsi
	0x45, 0x31, 0xe4, //0x00002e1a xorl         %r12d, %r12d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00002e1d jmp          LBB0_600
	//0x00002e22 LBB0_599
	0x4c, 0x89, 0xe1, //0x00002e22 movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x00002e25 notq         %rcx
	0x48, 0x21, 0xf1, //0x00002e28 andq         %rsi, %rcx
	0x4c, 0x8d, 0x2c, 0x09, //0x00002e2b leaq         (%rcx,%rcx), %r13
	0x4d, 0x09, 0xe5, //0x00002e2f orq          %r12, %r13
	0x4c, 0x89, 0xeb, //0x00002e32 movq         %r13, %rbx
	0x48, 0xf7, 0xd3, //0x00002e35 notq         %rbx
	0x48, 0xbf, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002e38 movabsq      $-6148914691236517206, %rdi
	0x48, 0x21, 0xfe, //0x00002e42 andq         %rdi, %rsi
	0x48, 0x21, 0xde, //0x00002e45 andq         %rbx, %rsi
	0x45, 0x31, 0xe4, //0x00002e48 xorl         %r12d, %r12d
	0x48, 0x01, 0xce, //0x00002e4b addq         %rcx, %rsi
	0x41, 0x0f, 0x92, 0xc4, //0x00002e4e setb         %r12b
	0x48, 0x01, 0xf6, //0x00002e52 addq         %rsi, %rsi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002e55 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xce, //0x00002e5f xorq         %rcx, %rsi
	0x4c, 0x21, 0xee, //0x00002e62 andq         %r13, %rsi
	0x48, 0xf7, 0xd6, //0x00002e65 notq         %rsi
	//0x00002e68 LBB0_600
	0xc5, 0x4d, 0x74, 0xc1, //0x00002e68 vpcmpeqb     %ymm1, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xc8, //0x00002e6c vpmovmskb    %ymm8, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00002e71 shlq         $32, %rcx
	0xc5, 0x45, 0x74, 0xc1, //0x00002e75 vpcmpeqb     %ymm1, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf8, //0x00002e79 vpmovmskb    %ymm8, %edi
	0x48, 0x09, 0xcf, //0x00002e7e orq          %rcx, %rdi
	0x48, 0x21, 0xf7, //0x00002e81 andq         %rsi, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xef, //0x00002e84 vmovq        %rdi, %xmm5
	0xc4, 0xe3, 0x51, 0x44, 0xea, 0x00, //0x00002e89 vpclmulqdq   $0, %xmm2, %xmm5, %xmm5
	0xc4, 0xc1, 0xf9, 0x7e, 0xed, //0x00002e8f vmovq        %xmm5, %r13
	0x4d, 0x31, 0xc5, //0x00002e94 xorq         %r8, %r13
	0xc5, 0xc5, 0x74, 0xeb, //0x00002e97 vpcmpeqb     %ymm3, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xfd, //0x00002e9b vpmovmskb    %ymm5, %edi
	0xc5, 0xcd, 0x74, 0xeb, //0x00002e9f vpcmpeqb     %ymm3, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xcd, //0x00002ea3 vpmovmskb    %ymm5, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00002ea7 shlq         $32, %rcx
	0x48, 0x09, 0xcf, //0x00002eab orq          %rcx, %rdi
	0x4c, 0x89, 0xe9, //0x00002eae movq         %r13, %rcx
	0x48, 0xf7, 0xd1, //0x00002eb1 notq         %rcx
	0x48, 0x21, 0xcf, //0x00002eb4 andq         %rcx, %rdi
	0xc5, 0xc5, 0x74, 0xec, //0x00002eb7 vpcmpeqb     %ymm4, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xdd, //0x00002ebb vpmovmskb    %ymm5, %ebx
	0xc5, 0xcd, 0x74, 0xec, //0x00002ebf vpcmpeqb     %ymm4, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xf5, //0x00002ec3 vpmovmskb    %ymm5, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x00002ec7 shlq         $32, %rsi
	0x48, 0x09, 0xf3, //0x00002ecb orq          %rsi, %rbx
	0x48, 0x21, 0xcb, //0x00002ece andq         %rcx, %rbx
	0x0f, 0x84, 0x31, 0x00, 0x00, 0x00, //0x00002ed1 je           LBB0_595
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002ed7 .p2align 4, 0x90
	//0x00002ee0 LBB0_601
	0x48, 0x8d, 0x4b, 0xff, //0x00002ee0 leaq         $-1(%rbx), %rcx
	0x48, 0x89, 0xce, //0x00002ee4 movq         %rcx, %rsi
	0x48, 0x21, 0xfe, //0x00002ee7 andq         %rdi, %rsi
	0xf3, 0x48, 0x0f, 0xb8, 0xf6, //0x00002eea popcntq      %rsi, %rsi
	0x4c, 0x01, 0xce, //0x00002eef addq         %r9, %rsi
	0x48, 0x39, 0xd6, //0x00002ef2 cmpq         %rdx, %rsi
	0x0f, 0x86, 0x4d, 0x01, 0x00, 0x00, //0x00002ef5 jbe          LBB0_618
	0x48, 0x83, 0xc2, 0x01, //0x00002efb addq         $1, %rdx
	0x48, 0x21, 0xcb, //0x00002eff andq         %rcx, %rbx
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00002f02 jne          LBB0_601
	//0x00002f08 LBB0_595
	0x49, 0xc1, 0xfd, 0x3f, //0x00002f08 sarq         $63, %r13
	0xf3, 0x48, 0x0f, 0xb8, 0xcf, //0x00002f0c popcntq      %rdi, %rcx
	0x49, 0x01, 0xc9, //0x00002f11 addq         %rcx, %r9
	0x49, 0x83, 0xc3, 0x40, //0x00002f14 addq         $64, %r11
	0x49, 0x83, 0xc7, 0xc0, //0x00002f18 addq         $-64, %r15
	0x4d, 0x89, 0xe8, //0x00002f1c movq         %r13, %r8
	0x49, 0x83, 0xff, 0x40, //0x00002f1f cmpq         $64, %r15
	0x0f, 0x8d, 0xba, 0xfe, 0xff, 0xff, //0x00002f23 jge          LBB0_597
	//0x00002f29 LBB0_603
	0x4d, 0x85, 0xff, //0x00002f29 testq        %r15, %r15
	0x0f, 0x8e, 0xed, 0x03, 0x00, 0x00, //0x00002f2c jle          LBB0_658
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x60, //0x00002f32 vmovdqu      %ymm9, $96(%rsp)
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x40, //0x00002f38 vmovdqu      %ymm9, $64(%rsp)
	0x44, 0x89, 0xd9, //0x00002f3e movl         %r11d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00002f41 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x00002f47 cmpl         $4033, %ecx
	0x0f, 0x82, 0x90, 0xfe, 0xff, 0xff, //0x00002f4d jb           LBB0_597
	0x49, 0x83, 0xff, 0x20, //0x00002f53 cmpq         $32, %r15
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x00002f57 jb           LBB0_607
	0xc4, 0xc1, 0x7e, 0x6f, 0x2b, //0x00002f5d vmovdqu      (%r11), %ymm5
	0xc5, 0xfe, 0x7f, 0x6c, 0x24, 0x40, //0x00002f62 vmovdqu      %ymm5, $64(%rsp)
	0x49, 0x83, 0xc3, 0x20, //0x00002f68 addq         $32, %r11
	0x49, 0x8d, 0x7f, 0xe0, //0x00002f6c leaq         $-32(%r15), %rdi
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x00002f70 leaq         $96(%rsp), %rsi
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00002f75 jmp          LBB0_608
	//0x00002f7a LBB0_607
	0x48, 0x8d, 0x74, 0x24, 0x40, //0x00002f7a leaq         $64(%rsp), %rsi
	0x4c, 0x89, 0xff, //0x00002f7f movq         %r15, %rdi
	//0x00002f82 LBB0_608
	0x48, 0x83, 0xff, 0x10, //0x00002f82 cmpq         $16, %rdi
	0x0f, 0x82, 0x5a, 0x00, 0x00, 0x00, //0x00002f86 jb           LBB0_609
	0xc4, 0xc1, 0x7a, 0x6f, 0x2b, //0x00002f8c vmovdqu      (%r11), %xmm5
	0xc5, 0xfa, 0x7f, 0x2e, //0x00002f91 vmovdqu      %xmm5, (%rsi)
	0x49, 0x83, 0xc3, 0x10, //0x00002f95 addq         $16, %r11
	0x48, 0x83, 0xc6, 0x10, //0x00002f99 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00002f9d addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00002fa1 cmpq         $8, %rdi
	0x0f, 0x83, 0x45, 0x00, 0x00, 0x00, //0x00002fa5 jae          LBB0_614
	//0x00002fab LBB0_610
	0x48, 0x83, 0xff, 0x04, //0x00002fab cmpq         $4, %rdi
	0x0f, 0x82, 0x57, 0x00, 0x00, 0x00, //0x00002faf jb           LBB0_611
	//0x00002fb5 LBB0_615
	0x41, 0x8b, 0x0b, //0x00002fb5 movl         (%r11), %ecx
	0x89, 0x0e, //0x00002fb8 movl         %ecx, (%rsi)
	0x49, 0x83, 0xc3, 0x04, //0x00002fba addq         $4, %r11
	0x48, 0x83, 0xc6, 0x04, //0x00002fbe addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00002fc2 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00002fc6 cmpq         $2, %rdi
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x00002fca jae          LBB0_616
	//0x00002fd0 LBB0_612
	0x4c, 0x89, 0xdb, //0x00002fd0 movq         %r11, %rbx
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00002fd3 leaq         $64(%rsp), %r11
	0x48, 0x85, 0xff, //0x00002fd8 testq        %rdi, %rdi
	0x0f, 0x85, 0x59, 0x00, 0x00, 0x00, //0x00002fdb jne          LBB0_617
	0xe9, 0xfd, 0xfd, 0xff, 0xff, //0x00002fe1 jmp          LBB0_597
	//0x00002fe6 LBB0_609
	0x48, 0x83, 0xff, 0x08, //0x00002fe6 cmpq         $8, %rdi
	0x0f, 0x82, 0xbb, 0xff, 0xff, 0xff, //0x00002fea jb           LBB0_610
	//0x00002ff0 LBB0_614
	0x49, 0x8b, 0x0b, //0x00002ff0 movq         (%r11), %rcx
	0x48, 0x89, 0x0e, //0x00002ff3 movq         %rcx, (%rsi)
	0x49, 0x83, 0xc3, 0x08, //0x00002ff6 addq         $8, %r11
	0x48, 0x83, 0xc6, 0x08, //0x00002ffa addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00002ffe addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00003002 cmpq         $4, %rdi
	0x0f, 0x83, 0xa9, 0xff, 0xff, 0xff, //0x00003006 jae          LBB0_615
	//0x0000300c LBB0_611
	0x48, 0x83, 0xff, 0x02, //0x0000300c cmpq         $2, %rdi
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00003010 jb           LBB0_612
	//0x00003016 LBB0_616
	0x41, 0x0f, 0xb7, 0x0b, //0x00003016 movzwl       (%r11), %ecx
	0x66, 0x89, 0x0e, //0x0000301a movw         %cx, (%rsi)
	0x49, 0x83, 0xc3, 0x02, //0x0000301d addq         $2, %r11
	0x48, 0x83, 0xc6, 0x02, //0x00003021 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00003025 addq         $-2, %rdi
	0x4c, 0x89, 0xdb, //0x00003029 movq         %r11, %rbx
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x0000302c leaq         $64(%rsp), %r11
	0x48, 0x85, 0xff, //0x00003031 testq        %rdi, %rdi
	0x0f, 0x84, 0xa9, 0xfd, 0xff, 0xff, //0x00003034 je           LBB0_597
	//0x0000303a LBB0_617
	0x8a, 0x0b, //0x0000303a movb         (%rbx), %cl
	0x88, 0x0e, //0x0000303c movb         %cl, (%rsi)
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x0000303e leaq         $64(%rsp), %r11
	0xe9, 0x9b, 0xfd, 0xff, 0xff, //0x00003043 jmp          LBB0_597
	//0x00003048 LBB0_618
	0x48, 0x8b, 0x74, 0x24, 0x20, //0x00003048 movq         $32(%rsp), %rsi
	0x48, 0x8b, 0x4e, 0x08, //0x0000304d movq         $8(%rsi), %rcx
	0x48, 0x0f, 0xbc, 0xd3, //0x00003051 bsfq         %rbx, %rdx
	0x4c, 0x29, 0xfa, //0x00003055 subq         %r15, %rdx
	0x48, 0x01, 0xd1, //0x00003058 addq         %rdx, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x0000305b addq         $1, %rcx
	0x49, 0x89, 0x0a, //0x0000305f movq         %rcx, (%r10)
	0x48, 0x8b, 0x56, 0x08, //0x00003062 movq         $8(%rsi), %rdx
	0x48, 0x39, 0xd1, //0x00003066 cmpq         %rdx, %rcx
	0x48, 0x0f, 0x47, 0xca, //0x00003069 cmovaq       %rdx, %rcx
	0x49, 0x89, 0x0a, //0x0000306d movq         %rcx, (%r10)
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003070 movq         $-1, %rcx
	0x48, 0x0f, 0x47, 0xc1, //0x00003077 cmovaq       %rcx, %rax
	0x49, 0x89, 0xc6, //0x0000307b movq         %rax, %r14
	0xe9, 0xbc, 0x00, 0x00, 0x00, //0x0000307e jmp          LBB0_650
	//0x00003083 LBB0_619
	0x41, 0x0f, 0xbc, 0xca, //0x00003083 bsfl         %r10d, %ecx
	0x48, 0x01, 0xc1, //0x00003087 addq         %rax, %rcx
	0x4c, 0x01, 0xf9, //0x0000308a addq         %r15, %rcx
	0x48, 0x83, 0xc1, 0x02, //0x0000308d addq         $2, %rcx
	0x49, 0x89, 0x4d, 0x00, //0x00003091 movq         %rcx, (%r13)
	0x49, 0x89, 0xc6, //0x00003095 movq         %rax, %r14
	0xe9, 0xa2, 0x00, 0x00, 0x00, //0x00003098 jmp          LBB0_650
	//0x0000309d LBB0_620
	0x4c, 0x89, 0x4c, 0x24, 0x30, //0x0000309d movq         %r9, $48(%rsp)
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000030a2 movq         $-1, %rcx
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000030a9 jmp          LBB0_622
	//0x000030ae LBB0_621
	0x4c, 0x89, 0xf9, //0x000030ae movq         %r15, %rcx
	//0x000030b1 LBB0_622
	0x48, 0x8b, 0x54, 0x24, 0x30, //0x000030b1 movq         $48(%rsp), %rdx
	0x48, 0x8b, 0x02, //0x000030b6 movq         (%rdx), %rax
	0x48, 0x29, 0xc8, //0x000030b9 subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x000030bc addq         $-2, %rax
	0x48, 0x89, 0x02, //0x000030c0 movq         %rax, (%rdx)
	//0x000030c3 LBB0_623
	0x49, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x000030c3 movq         $-2, %r14
	0xe9, 0x70, 0x00, 0x00, 0x00, //0x000030ca jmp          LBB0_650
	//0x000030cf LBB0_624
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x000030cf movq         $-2, %rcx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x000030d6 movl         $2, %esi
	0x48, 0x01, 0xf2, //0x000030db addq         %rsi, %rdx
	0x49, 0x01, 0xcf, //0x000030de addq         %rcx, %r15
	0x0f, 0x8e, 0x58, 0x00, 0x00, 0x00, //0x000030e1 jle          LBB0_650
	//0x000030e7 LBB0_625
	0x0f, 0xb6, 0x0a, //0x000030e7 movzbl       (%rdx), %ecx
	0x80, 0xf9, 0x5c, //0x000030ea cmpb         $92, %cl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x000030ed je           LBB0_624
	0x80, 0xf9, 0x22, //0x000030f3 cmpb         $34, %cl
	0x0f, 0x84, 0xaa, 0x01, 0x00, 0x00, //0x000030f6 je           LBB0_651
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000030fc movq         $-1, %rcx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00003103 movl         $1, %esi
	0x48, 0x01, 0xf2, //0x00003108 addq         %rsi, %rdx
	0x49, 0x01, 0xcf, //0x0000310b addq         %rcx, %r15
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x0000310e jg           LBB0_625
	0xe9, 0x26, 0x00, 0x00, 0x00, //0x00003114 jmp          LBB0_650
	//0x00003119 LBB0_645
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00003119 movq         $16(%rsp), %rax
	0x48, 0x83, 0xf8, 0xff, //0x0000311e cmpq         $-1, %rax
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00003122 jne          LBB0_648
	0x48, 0x0f, 0xbc, 0xc6, //0x00003128 bsfq         %rsi, %rax
	//0x0000312c LBB0_647
	0x4c, 0x01, 0xe0, //0x0000312c addq         %r12, %rax
	//0x0000312f LBB0_648
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x0000312f movq         $-2, %r12
	0x4d, 0x89, 0xf1, //0x00003136 movq         %r14, %r9
	//0x00003139 LBB0_649
	0x49, 0x89, 0x01, //0x00003139 movq         %rax, (%r9)
	0x4d, 0x89, 0xe6, //0x0000313c movq         %r12, %r14
	//0x0000313f LBB0_650
	0x4c, 0x89, 0xf0, //0x0000313f movq         %r14, %rax
	0x48, 0x8d, 0x65, 0xd8, //0x00003142 leaq         $-40(%rbp), %rsp
	0x5b, //0x00003146 popq         %rbx
	0x41, 0x5c, //0x00003147 popq         %r12
	0x41, 0x5d, //0x00003149 popq         %r13
	0x41, 0x5e, //0x0000314b popq         %r14
	0x41, 0x5f, //0x0000314d popq         %r15
	0x5d, //0x0000314f popq         %rbp
	0xc5, 0xf8, 0x77, //0x00003150 vzeroupper   
	0xc3, //0x00003153 retq         
	//0x00003154 LBB0_630
	0x49, 0x89, 0x01, //0x00003154 movq         %rax, (%r9)
	0xe9, 0xe3, 0xff, 0xff, 0xff, //0x00003157 jmp          LBB0_650
	//0x0000315c LBB0_628
	0x66, 0x0f, 0xbc, 0xc9, //0x0000315c bsfw         %cx, %cx
	0x0f, 0xb7, 0xc9, //0x00003160 movzwl       %cx, %ecx
	0x48, 0x29, 0xf9, //0x00003163 subq         %rdi, %rcx
	0xe9, 0x17, 0xf8, 0xff, 0xff, //0x00003166 jmp          LBB0_552
	//0x0000316b LBB0_629
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000316b movq         $-1, %rcx
	0xe9, 0x01, 0x01, 0x00, 0x00, //0x00003172 jmp          LBB0_643
	//0x00003177 LBB0_632
	0x49, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x00003177 movq         $-2, %r14
	0x80, 0xf9, 0x61, //0x0000317e cmpb         $97, %cl
	0x0f, 0x85, 0xb8, 0xff, 0xff, 0xff, //0x00003181 jne          LBB0_650
	0x49, 0x8d, 0x42, 0x02, //0x00003187 leaq         $2(%r10), %rax
	0x49, 0x89, 0x01, //0x0000318b movq         %rax, (%r9)
	0x43, 0x80, 0x7c, 0x15, 0x02, 0x6c, //0x0000318e cmpb         $108, $2(%r13,%r10)
	0x0f, 0x85, 0xa5, 0xff, 0xff, 0xff, //0x00003194 jne          LBB0_650
	0x49, 0x8d, 0x42, 0x03, //0x0000319a leaq         $3(%r10), %rax
	0x49, 0x89, 0x01, //0x0000319e movq         %rax, (%r9)
	0x43, 0x80, 0x7c, 0x15, 0x03, 0x73, //0x000031a1 cmpb         $115, $3(%r13,%r10)
	0x0f, 0x85, 0x92, 0xff, 0xff, 0xff, //0x000031a7 jne          LBB0_650
	0x49, 0x8d, 0x42, 0x04, //0x000031ad leaq         $4(%r10), %rax
	0x49, 0x89, 0x01, //0x000031b1 movq         %rax, (%r9)
	0x43, 0x80, 0x7c, 0x15, 0x04, 0x65, //0x000031b4 cmpb         $101, $4(%r13,%r10)
	0x0f, 0x85, 0x7f, 0xff, 0xff, 0xff, //0x000031ba jne          LBB0_650
	0x49, 0x83, 0xc2, 0x05, //0x000031c0 addq         $5, %r10
	0x4d, 0x89, 0x11, //0x000031c4 movq         %r10, (%r9)
	0xe9, 0x73, 0xff, 0xff, 0xff, //0x000031c7 jmp          LBB0_650
	//0x000031cc LBB0_332
	0x4d, 0x89, 0x11, //0x000031cc movq         %r10, (%r9)
	0x49, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x000031cf movq         $-2, %r14
	0x80, 0x38, 0x6e, //0x000031d6 cmpb         $110, (%rax)
	0x0f, 0x85, 0x60, 0xff, 0xff, 0xff, //0x000031d9 jne          LBB0_650
	0x49, 0x8d, 0x42, 0x01, //0x000031df leaq         $1(%r10), %rax
	0x49, 0x89, 0x01, //0x000031e3 movq         %rax, (%r9)
	0x43, 0x80, 0x7c, 0x15, 0x01, 0x75, //0x000031e6 cmpb         $117, $1(%r13,%r10)
	0x0f, 0x85, 0x4d, 0xff, 0xff, 0xff, //0x000031ec jne          LBB0_650
	0x49, 0x8d, 0x42, 0x02, //0x000031f2 leaq         $2(%r10), %rax
	0x49, 0x89, 0x01, //0x000031f6 movq         %rax, (%r9)
	0x43, 0x80, 0x7c, 0x15, 0x02, 0x6c, //0x000031f9 cmpb         $108, $2(%r13,%r10)
	0x0f, 0x85, 0x3a, 0xff, 0xff, 0xff, //0x000031ff jne          LBB0_650
	0x49, 0x8d, 0x42, 0x03, //0x00003205 leaq         $3(%r10), %rax
	0x49, 0x89, 0x01, //0x00003209 movq         %rax, (%r9)
	0x43, 0x80, 0x7c, 0x15, 0x03, 0x6c, //0x0000320c cmpb         $108, $3(%r13,%r10)
	0x0f, 0x84, 0x51, 0x00, 0x00, 0x00, //0x00003212 je           LBB0_641
	0xe9, 0x22, 0xff, 0xff, 0xff, //0x00003218 jmp          LBB0_650
	//0x0000321d LBB0_637
	0x4d, 0x89, 0x11, //0x0000321d movq         %r10, (%r9)
	0x49, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x00003220 movq         $-2, %r14
	0x80, 0x38, 0x74, //0x00003227 cmpb         $116, (%rax)
	0x0f, 0x85, 0x0f, 0xff, 0xff, 0xff, //0x0000322a jne          LBB0_650
	0x49, 0x8d, 0x42, 0x01, //0x00003230 leaq         $1(%r10), %rax
	0x49, 0x89, 0x01, //0x00003234 movq         %rax, (%r9)
	0x43, 0x80, 0x7c, 0x15, 0x01, 0x72, //0x00003237 cmpb         $114, $1(%r13,%r10)
	0x0f, 0x85, 0xfc, 0xfe, 0xff, 0xff, //0x0000323d jne          LBB0_650
	0x49, 0x8d, 0x42, 0x02, //0x00003243 leaq         $2(%r10), %rax
	0x49, 0x89, 0x01, //0x00003247 movq         %rax, (%r9)
	0x43, 0x80, 0x7c, 0x15, 0x02, 0x75, //0x0000324a cmpb         $117, $2(%r13,%r10)
	0x0f, 0x85, 0xe9, 0xfe, 0xff, 0xff, //0x00003250 jne          LBB0_650
	0x49, 0x8d, 0x42, 0x03, //0x00003256 leaq         $3(%r10), %rax
	0x49, 0x89, 0x01, //0x0000325a movq         %rax, (%r9)
	0x43, 0x80, 0x7c, 0x15, 0x03, 0x65, //0x0000325d cmpb         $101, $3(%r13,%r10)
	0x0f, 0x85, 0xd6, 0xfe, 0xff, 0xff, //0x00003263 jne          LBB0_650
	//0x00003269 LBB0_641
	0x49, 0x83, 0xc2, 0x04, //0x00003269 addq         $4, %r10
	0x4d, 0x89, 0x11, //0x0000326d movq         %r10, (%r9)
	0xe9, 0xca, 0xfe, 0xff, 0xff, //0x00003270 jmp          LBB0_650
	//0x00003275 LBB0_642
	0x48, 0x89, 0xd9, //0x00003275 movq         %rbx, %rcx
	//0x00003278 LBB0_643
	0x48, 0xf7, 0xd1, //0x00003278 notq         %rcx
	0x49, 0x01, 0x09, //0x0000327b addq         %rcx, (%r9)
	0xe9, 0x40, 0xfe, 0xff, 0xff, //0x0000327e jmp          LBB0_623
	//0x00003283 LBB0_644
	0x4c, 0x29, 0xd8, //0x00003283 subq         %r11, %rax
	0x48, 0x01, 0xf0, //0x00003286 addq         %rsi, %rax
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00003289 movq         $-1, %r14
	0x48, 0x39, 0xd8, //0x00003290 cmpq         %rbx, %rax
	0x0f, 0x82, 0x8c, 0xf5, 0xff, 0xff, //0x00003293 jb           LBB0_533
	0xe9, 0xa1, 0xfe, 0xff, 0xff, //0x00003299 jmp          LBB0_650
	//0x0000329e LBB0_656
	0x4d, 0x89, 0xe0, //0x0000329e movq         %r12, %r8
	0xe9, 0x24, 0xf7, 0xff, 0xff, //0x000032a1 jmp          LBB0_496
	//0x000032a6 LBB0_651
	0x4c, 0x29, 0xda, //0x000032a6 subq         %r11, %rdx
	0x48, 0x83, 0xc2, 0x01, //0x000032a9 addq         $1, %rdx
	0x49, 0x89, 0x55, 0x00, //0x000032ad movq         %rdx, (%r13)
	0x49, 0x89, 0xc6, //0x000032b1 movq         %rax, %r14
	0xe9, 0x86, 0xfe, 0xff, 0xff, //0x000032b4 jmp          LBB0_650
	//0x000032b9 LBB0_363
	0x4c, 0x01, 0xe6, //0x000032b9 addq         %r12, %rsi
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x000032bc movq         $-2, %r12
	0x48, 0x89, 0xf0, //0x000032c3 movq         %rsi, %rax
	0xe9, 0x6e, 0xfe, 0xff, 0xff, //0x000032c6 jmp          LBB0_649
	//0x000032cb LBB0_652
	0x4c, 0x01, 0xd8, //0x000032cb addq         %r11, %rax
	0x48, 0x85, 0xd2, //0x000032ce testq        %rdx, %rdx
	0x0f, 0x85, 0xaf, 0xd1, 0xff, 0xff, //0x000032d1 jne          LBB0_25
	0xe9, 0xe1, 0xd1, 0xff, 0xff, //0x000032d7 jmp          LBB0_30
	//0x000032dc LBB0_653
	0x4c, 0x01, 0xda, //0x000032dc addq         %r11, %rdx
	0x48, 0x83, 0xfe, 0x10, //0x000032df cmpq         $16, %rsi
	0x0f, 0x83, 0xf9, 0xf5, 0xff, 0xff, //0x000032e3 jae          LBB0_540
	0xe9, 0x48, 0xf6, 0xff, 0xff, //0x000032e9 jmp          LBB0_543
	//0x000032ee LBB0_654
	0x4c, 0x29, 0xda, //0x000032ee subq         %r11, %rdx
	0x48, 0x01, 0xfa, //0x000032f1 addq         %rdi, %rdx
	//0x000032f4 LBB0_655
	0x49, 0x89, 0x11, //0x000032f4 movq         %rdx, (%r9)
	0x49, 0x89, 0xc6, //0x000032f7 movq         %rax, %r14
	0xe9, 0x40, 0xfe, 0xff, 0xff, //0x000032fa jmp          LBB0_650
	//0x000032ff LBB0_657
	0x4c, 0x01, 0xda, //0x000032ff addq         %r11, %rdx
	0xe9, 0xb4, 0xf7, 0xff, 0xff, //0x00003302 jmp          LBB0_567
	//0x00003307 LBB0_664
	0x89, 0xf8, //0x00003307 movl         %edi, %eax
	0x4d, 0x29, 0xec, //0x00003309 subq         %r13, %r12
	0x49, 0x01, 0xc4, //0x0000330c addq         %rax, %r12
	0x4c, 0x89, 0xe0, //0x0000330f movq         %r12, %rax
	0xe9, 0x18, 0xfe, 0xff, 0xff, //0x00003312 jmp          LBB0_648
	//0x00003317 LBB0_268
	0x4d, 0x89, 0xf9, //0x00003317 movq         %r15, %r9
	0xe9, 0xab, 0xf6, 0xff, 0xff, //0x0000331a jmp          LBB0_496
	//0x0000331f LBB0_658
	0x48, 0x8b, 0x44, 0x24, 0x20, //0x0000331f movq         $32(%rsp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00003324 movq         $8(%rax), %rax
	0x49, 0x89, 0x02, //0x00003328 movq         %rax, (%r10)
	0xe9, 0x0f, 0xfe, 0xff, 0xff, //0x0000332b jmp          LBB0_650
	//0x00003330 LBB0_662
	0x4d, 0x29, 0xec, //0x00003330 subq         %r13, %r12
	0x89, 0xf8, //0x00003333 movl         %edi, %eax
	0xe9, 0xf2, 0xfd, 0xff, 0xff, //0x00003335 jmp          LBB0_647
	//0x0000333a LBB0_659
	0x4d, 0x29, 0xec, //0x0000333a subq         %r13, %r12
	0x4c, 0x89, 0xe0, //0x0000333d movq         %r12, %rax
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x00003340 movq         $-2, %r12
	0xe9, 0xed, 0xfd, 0xff, 0xff, //0x00003347 jmp          LBB0_649
	//0x0000334c LBB0_660
	0x49, 0x8d, 0x48, 0xff, //0x0000334c leaq         $-1(%r8), %rcx
	0x4c, 0x39, 0xf9, //0x00003350 cmpq         %r15, %rcx
	0x0f, 0x84, 0xe6, 0xfd, 0xff, 0xff, //0x00003353 je           LBB0_650
	0x4b, 0x8d, 0x14, 0x0f, //0x00003359 leaq         (%r15,%r9), %rdx
	0x48, 0x83, 0xc2, 0x02, //0x0000335d addq         $2, %rdx
	0x4d, 0x29, 0xf8, //0x00003361 subq         %r15, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00003364 addq         $-2, %r8
	0x4d, 0x89, 0xc7, //0x00003368 movq         %r8, %r15
	0xe9, 0x4b, 0xf7, 0xff, 0xff, //0x0000336b jmp          LBB0_567
	//0x00003370 LBB0_663
	0x4d, 0x29, 0xec, //0x00003370 subq         %r13, %r12
	0x4c, 0x89, 0xe0, //0x00003373 movq         %r12, %rax
	0xe9, 0xb4, 0xfd, 0xff, 0xff, //0x00003376 jmp          LBB0_648
	0x90, //0x0000337b .p2align 2, 0x90
	// // .set L0_0_set_650, LBB0_650-LJTI0_0
	// // .set L0_0_set_556, LBB0_556-LJTI0_0
	// // .set L0_0_set_557, LBB0_557-LJTI0_0
	// // .set L0_0_set_535, LBB0_535-LJTI0_0
	// // .set L0_0_set_594, LBB0_594-LJTI0_0
	// // .set L0_0_set_569, LBB0_569-LJTI0_0
	// // .set L0_0_set_555, LBB0_555-LJTI0_0
	// // .set L0_0_set_570, LBB0_570-LJTI0_0
	//0x0000337c LJTI0_0
	0xc3, 0xfd, 0xff, 0xff, //0x0000337c .long L0_0_set_650
	0x5d, 0xf6, 0xff, 0xff, //0x00003380 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003384 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003388 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000338c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003390 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003394 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003398 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000339c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033a0 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033a4 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033a8 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033ac .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033b0 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033b4 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033b8 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033bc .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033c0 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033c4 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033c8 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033cc .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033d0 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033d4 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033d8 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033dc .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033e0 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033e4 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033e8 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033ec .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033f0 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033f4 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033f8 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000033fc .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003400 .long L0_0_set_556
	0x65, 0xf6, 0xff, 0xff, //0x00003404 .long L0_0_set_557
	0x5d, 0xf6, 0xff, 0xff, //0x00003408 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000340c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003410 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003414 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003418 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000341c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003420 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003424 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003428 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000342c .long L0_0_set_556
	0xd5, 0xf4, 0xff, 0xff, //0x00003430 .long L0_0_set_535
	0x5d, 0xf6, 0xff, 0xff, //0x00003434 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003438 .long L0_0_set_556
	0xd5, 0xf4, 0xff, 0xff, //0x0000343c .long L0_0_set_535
	0xd5, 0xf4, 0xff, 0xff, //0x00003440 .long L0_0_set_535
	0xd5, 0xf4, 0xff, 0xff, //0x00003444 .long L0_0_set_535
	0xd5, 0xf4, 0xff, 0xff, //0x00003448 .long L0_0_set_535
	0xd5, 0xf4, 0xff, 0xff, //0x0000344c .long L0_0_set_535
	0xd5, 0xf4, 0xff, 0xff, //0x00003450 .long L0_0_set_535
	0xd5, 0xf4, 0xff, 0xff, //0x00003454 .long L0_0_set_535
	0xd5, 0xf4, 0xff, 0xff, //0x00003458 .long L0_0_set_535
	0xd5, 0xf4, 0xff, 0xff, //0x0000345c .long L0_0_set_535
	0xd5, 0xf4, 0xff, 0xff, //0x00003460 .long L0_0_set_535
	0x5d, 0xf6, 0xff, 0xff, //0x00003464 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003468 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000346c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003470 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003474 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003478 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000347c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003480 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003484 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003488 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000348c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003490 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003494 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003498 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000349c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034a0 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034a4 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034a8 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034ac .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034b0 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034b4 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034b8 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034bc .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034c0 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034c4 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034c8 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034cc .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034d0 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034d4 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034d8 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034dc .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034e0 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034e4 .long L0_0_set_556
	0x1c, 0xfa, 0xff, 0xff, //0x000034e8 .long L0_0_set_594
	0x5d, 0xf6, 0xff, 0xff, //0x000034ec .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034f0 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034f4 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034f8 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x000034fc .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003500 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003504 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003508 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000350c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003510 .long L0_0_set_556
	0x54, 0xf7, 0xff, 0xff, //0x00003514 .long L0_0_set_569
	0x5d, 0xf6, 0xff, 0xff, //0x00003518 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000351c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003520 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003524 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003528 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000352c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003530 .long L0_0_set_556
	0x20, 0xf6, 0xff, 0xff, //0x00003534 .long L0_0_set_555
	0x5d, 0xf6, 0xff, 0xff, //0x00003538 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000353c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003540 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003544 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003548 .long L0_0_set_556
	0x20, 0xf6, 0xff, 0xff, //0x0000354c .long L0_0_set_555
	0x5d, 0xf6, 0xff, 0xff, //0x00003550 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003554 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003558 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x0000355c .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003560 .long L0_0_set_556
	0x5d, 0xf6, 0xff, 0xff, //0x00003564 .long L0_0_set_556
	0x67, 0xf7, 0xff, 0xff, //0x00003568 .long L0_0_set_570
	// // .set L0_1_set_70, LBB0_70-LJTI0_1
	// // .set L0_1_set_96, LBB0_96-LJTI0_1
	// // .set L0_1_set_76, LBB0_76-LJTI0_1
	// // .set L0_1_set_94, LBB0_94-LJTI0_1
	// // .set L0_1_set_73, LBB0_73-LJTI0_1
	// // .set L0_1_set_99, LBB0_99-LJTI0_1
	//0x0000356c LJTI0_1
	0xb6, 0xd1, 0xff, 0xff, //0x0000356c .long L0_1_set_70
	0x4f, 0xd3, 0xff, 0xff, //0x00003570 .long L0_1_set_96
	0xec, 0xd1, 0xff, 0xff, //0x00003574 .long L0_1_set_76
	0x39, 0xd3, 0xff, 0xff, //0x00003578 .long L0_1_set_94
	0xcd, 0xd1, 0xff, 0xff, //0x0000357c .long L0_1_set_73
	0x46, 0xd7, 0xff, 0xff, //0x00003580 .long L0_1_set_99
	// // .set L0_2_set_650, LBB0_650-LJTI0_2
	// // .set L0_2_set_623, LBB0_623-LJTI0_2
	// // .set L0_2_set_247, LBB0_247-LJTI0_2
	// // .set L0_2_set_273, LBB0_273-LJTI0_2
	// // .set L0_2_set_113, LBB0_113-LJTI0_2
	// // .set L0_2_set_328, LBB0_328-LJTI0_2
	// // .set L0_2_set_244, LBB0_244-LJTI0_2
	// // .set L0_2_set_330, LBB0_330-LJTI0_2
	// // .set L0_2_set_336, LBB0_336-LJTI0_2
	// // .set L0_2_set_339, LBB0_339-LJTI0_2
	//0x00003584 LJTI0_2
	0xbb, 0xfb, 0xff, 0xff, //0x00003584 .long L0_2_set_650
	0x3f, 0xfb, 0xff, 0xff, //0x00003588 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x0000358c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003590 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003594 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003598 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x0000359c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035a0 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035a4 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035a8 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035ac .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035b0 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035b4 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035b8 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035bc .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035c0 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035c4 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035c8 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035cc .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035d0 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035d4 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035d8 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035dc .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035e0 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035e4 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035e8 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035ec .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035f0 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035f4 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035f8 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000035fc .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003600 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003604 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003608 .long L0_2_set_623
	0x70, 0xdd, 0xff, 0xff, //0x0000360c .long L0_2_set_247
	0x3f, 0xfb, 0xff, 0xff, //0x00003610 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003614 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003618 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x0000361c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003620 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003624 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003628 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x0000362c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003630 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003634 .long L0_2_set_623
	0xb1, 0xde, 0xff, 0xff, //0x00003638 .long L0_2_set_273
	0x3f, 0xfb, 0xff, 0xff, //0x0000363c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003640 .long L0_2_set_623
	0x89, 0xd3, 0xff, 0xff, //0x00003644 .long L0_2_set_113
	0x89, 0xd3, 0xff, 0xff, //0x00003648 .long L0_2_set_113
	0x89, 0xd3, 0xff, 0xff, //0x0000364c .long L0_2_set_113
	0x89, 0xd3, 0xff, 0xff, //0x00003650 .long L0_2_set_113
	0x89, 0xd3, 0xff, 0xff, //0x00003654 .long L0_2_set_113
	0x89, 0xd3, 0xff, 0xff, //0x00003658 .long L0_2_set_113
	0x89, 0xd3, 0xff, 0xff, //0x0000365c .long L0_2_set_113
	0x89, 0xd3, 0xff, 0xff, //0x00003660 .long L0_2_set_113
	0x89, 0xd3, 0xff, 0xff, //0x00003664 .long L0_2_set_113
	0x89, 0xd3, 0xff, 0xff, //0x00003668 .long L0_2_set_113
	0x3f, 0xfb, 0xff, 0xff, //0x0000366c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003670 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003674 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003678 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x0000367c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003680 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003684 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003688 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x0000368c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003690 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003694 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003698 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x0000369c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036a0 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036a4 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036a8 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036ac .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036b0 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036b4 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036b8 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036bc .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036c0 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036c4 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036c8 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036cc .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036d0 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036d4 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036d8 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036dc .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036e0 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036e4 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036e8 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036ec .long L0_2_set_623
	0x10, 0xe2, 0xff, 0xff, //0x000036f0 .long L0_2_set_328
	0x3f, 0xfb, 0xff, 0xff, //0x000036f4 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036f8 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x000036fc .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003700 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003704 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003708 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x0000370c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003710 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003714 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003718 .long L0_2_set_623
	0x45, 0xdd, 0xff, 0xff, //0x0000371c .long L0_2_set_244
	0x3f, 0xfb, 0xff, 0xff, //0x00003720 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003724 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003728 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x0000372c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003730 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003734 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003738 .long L0_2_set_623
	0x34, 0xe2, 0xff, 0xff, //0x0000373c .long L0_2_set_330
	0x3f, 0xfb, 0xff, 0xff, //0x00003740 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003744 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003748 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x0000374c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003750 .long L0_2_set_623
	0x5b, 0xe2, 0xff, 0xff, //0x00003754 .long L0_2_set_336
	0x3f, 0xfb, 0xff, 0xff, //0x00003758 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x0000375c .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003760 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003764 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x00003768 .long L0_2_set_623
	0x3f, 0xfb, 0xff, 0xff, //0x0000376c .long L0_2_set_623
	0x86, 0xe2, 0xff, 0xff, //0x00003770 .long L0_2_set_339
	// // .set L0_3_set_321, LBB0_321-LJTI0_3
	// // .set L0_3_set_366, LBB0_366-LJTI0_3
	// // .set L0_3_set_326, LBB0_326-LJTI0_3
	// // .set L0_3_set_324, LBB0_324-LJTI0_3
	//0x00003774 LJTI0_3
	0xde, 0xdf, 0xff, 0xff, //0x00003774 .long L0_3_set_321
	0x7c, 0xe2, 0xff, 0xff, //0x00003778 .long L0_3_set_366
	0xde, 0xdf, 0xff, 0xff, //0x0000377c .long L0_3_set_321
	0x0d, 0xe0, 0xff, 0xff, //0x00003780 .long L0_3_set_326
	0x7c, 0xe2, 0xff, 0xff, //0x00003784 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x00003788 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x0000378c .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x00003790 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x00003794 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x00003798 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x0000379c .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037a0 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037a4 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037a8 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037ac .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037b0 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037b4 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037b8 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037bc .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037c0 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037c4 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037c8 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037cc .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037d0 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037d4 .long L0_3_set_366
	0x7c, 0xe2, 0xff, 0xff, //0x000037d8 .long L0_3_set_366
	0xfa, 0xdf, 0xff, 0xff, //0x000037dc .long L0_3_set_324
	// // .set L0_4_set_163, LBB0_163-LJTI0_4
	// // .set L0_4_set_223, LBB0_223-LJTI0_4
	// // .set L0_4_set_157, LBB0_157-LJTI0_4
	// // .set L0_4_set_166, LBB0_166-LJTI0_4
	//0x000037e0 LJTI0_4
	0xa3, 0xd4, 0xff, 0xff, //0x000037e0 .long L0_4_set_163
	0x38, 0xd9, 0xff, 0xff, //0x000037e4 .long L0_4_set_223
	0xa3, 0xd4, 0xff, 0xff, //0x000037e8 .long L0_4_set_163
	0x62, 0xd4, 0xff, 0xff, //0x000037ec .long L0_4_set_157
	0x38, 0xd9, 0xff, 0xff, //0x000037f0 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x000037f4 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x000037f8 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x000037fc .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003800 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003804 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003808 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x0000380c .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003810 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003814 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003818 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x0000381c .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003820 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003824 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003828 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x0000382c .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003830 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003834 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003838 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x0000383c .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003840 .long L0_4_set_223
	0x38, 0xd9, 0xff, 0xff, //0x00003844 .long L0_4_set_223
	0xbf, 0xd4, 0xff, 0xff, //0x00003848 .long L0_4_set_166
	//0x0000384c .p2align 2, 0x00
	//0x0000384c _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x0000384c .long 2
}
 
