// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_validate_utf8_fast = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, // QUAD $0x0f0f0f0f0f0f0f0f; QUAD $0x0f0f0f0f0f0f0f0f  // .space 16, '\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f'
	0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000010 QUAD $0x0f0f0f0f0f0f0f0f; QUAD $0x0f0f0f0f0f0f0f0f  // .space 16, '\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f\x0f'
	//0x00000020 LCPI0_1
	0x02, //0x00000020 .byte 2
	0x02, //0x00000021 .byte 2
	0x02, //0x00000022 .byte 2
	0x02, //0x00000023 .byte 2
	0x02, //0x00000024 .byte 2
	0x02, //0x00000025 .byte 2
	0x02, //0x00000026 .byte 2
	0x02, //0x00000027 .byte 2
	0x80, //0x00000028 .byte 128
	0x80, //0x00000029 .byte 128
	0x80, //0x0000002a .byte 128
	0x80, //0x0000002b .byte 128
	0x21, //0x0000002c .byte 33
	0x01, //0x0000002d .byte 1
	0x15, //0x0000002e .byte 21
	0x49, //0x0000002f .byte 73
	0x02, //0x00000030 .byte 2
	0x02, //0x00000031 .byte 2
	0x02, //0x00000032 .byte 2
	0x02, //0x00000033 .byte 2
	0x02, //0x00000034 .byte 2
	0x02, //0x00000035 .byte 2
	0x02, //0x00000036 .byte 2
	0x02, //0x00000037 .byte 2
	0x80, //0x00000038 .byte 128
	0x80, //0x00000039 .byte 128
	0x80, //0x0000003a .byte 128
	0x80, //0x0000003b .byte 128
	0x21, //0x0000003c .byte 33
	0x01, //0x0000003d .byte 1
	0x15, //0x0000003e .byte 21
	0x49, //0x0000003f .byte 73
	//0x00000040 LCPI0_2
	0xe7, //0x00000040 .byte 231
	0xa3, //0x00000041 .byte 163
	0x83, //0x00000042 .byte 131
	0x83, //0x00000043 .byte 131
	0x8b, //0x00000044 .byte 139
	0xcb, //0x00000045 .byte 203
	0xcb, //0x00000046 .byte 203
	0xcb, //0x00000047 .byte 203
	0xcb, //0x00000048 .byte 203
	0xcb, //0x00000049 .byte 203
	0xcb, //0x0000004a .byte 203
	0xcb, //0x0000004b .byte 203
	0xcb, //0x0000004c .byte 203
	0xdb, //0x0000004d .byte 219
	0xcb, //0x0000004e .byte 203
	0xcb, //0x0000004f .byte 203
	0xe7, //0x00000050 .byte 231
	0xa3, //0x00000051 .byte 163
	0x83, //0x00000052 .byte 131
	0x83, //0x00000053 .byte 131
	0x8b, //0x00000054 .byte 139
	0xcb, //0x00000055 .byte 203
	0xcb, //0x00000056 .byte 203
	0xcb, //0x00000057 .byte 203
	0xcb, //0x00000058 .byte 203
	0xcb, //0x00000059 .byte 203
	0xcb, //0x0000005a .byte 203
	0xcb, //0x0000005b .byte 203
	0xcb, //0x0000005c .byte 203
	0xdb, //0x0000005d .byte 219
	0xcb, //0x0000005e .byte 203
	0xcb, //0x0000005f .byte 203
	//0x00000060 LCPI0_3
	0x01, //0x00000060 .byte 1
	0x01, //0x00000061 .byte 1
	0x01, //0x00000062 .byte 1
	0x01, //0x00000063 .byte 1
	0x01, //0x00000064 .byte 1
	0x01, //0x00000065 .byte 1
	0x01, //0x00000066 .byte 1
	0x01, //0x00000067 .byte 1
	0xe6, //0x00000068 .byte 230
	0xae, //0x00000069 .byte 174
	0xba, //0x0000006a .byte 186
	0xba, //0x0000006b .byte 186
	0x01, //0x0000006c .byte 1
	0x01, //0x0000006d .byte 1
	0x01, //0x0000006e .byte 1
	0x01, //0x0000006f .byte 1
	0x01, //0x00000070 .byte 1
	0x01, //0x00000071 .byte 1
	0x01, //0x00000072 .byte 1
	0x01, //0x00000073 .byte 1
	0x01, //0x00000074 .byte 1
	0x01, //0x00000075 .byte 1
	0x01, //0x00000076 .byte 1
	0x01, //0x00000077 .byte 1
	0xe6, //0x00000078 .byte 230
	0xae, //0x00000079 .byte 174
	0xba, //0x0000007a .byte 186
	0xba, //0x0000007b .byte 186
	0x01, //0x0000007c .byte 1
	0x01, //0x0000007d .byte 1
	0x01, //0x0000007e .byte 1
	0x01, //0x0000007f .byte 1
	//0x00000080 LCPI0_4
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000080 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000090 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x000000a0 LCPI0_5
	0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, //0x000000a0 QUAD $0xefefefefefefefef; QUAD $0xefefefefefefefef  // .space 16, '\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef'
	0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, 0xef, //0x000000b0 QUAD $0xefefefefefefefef; QUAD $0xefefefefefefefef  // .space 16, '\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef\xef'
	//0x000000c0 LCPI0_7
	0xff, //0x000000c0 .byte 255
	0xff, //0x000000c1 .byte 255
	0xff, //0x000000c2 .byte 255
	0xff, //0x000000c3 .byte 255
	0xff, //0x000000c4 .byte 255
	0xff, //0x000000c5 .byte 255
	0xff, //0x000000c6 .byte 255
	0xff, //0x000000c7 .byte 255
	0xff, //0x000000c8 .byte 255
	0xff, //0x000000c9 .byte 255
	0xff, //0x000000ca .byte 255
	0xff, //0x000000cb .byte 255
	0xff, //0x000000cc .byte 255
	0xff, //0x000000cd .byte 255
	0xff, //0x000000ce .byte 255
	0xff, //0x000000cf .byte 255
	0xff, //0x000000d0 .byte 255
	0xff, //0x000000d1 .byte 255
	0xff, //0x000000d2 .byte 255
	0xff, //0x000000d3 .byte 255
	0xff, //0x000000d4 .byte 255
	0xff, //0x000000d5 .byte 255
	0xff, //0x000000d6 .byte 255
	0xff, //0x000000d7 .byte 255
	0xff, //0x000000d8 .byte 255
	0xff, //0x000000d9 .byte 255
	0xff, //0x000000da .byte 255
	0xff, //0x000000db .byte 255
	0xff, //0x000000dc .byte 255
	0xef, //0x000000dd .byte 239
	0xdf, //0x000000de .byte 223
	0xbf, //0x000000df .byte 191
	//0x000000e0 LCPI0_8
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000000e0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000000f0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	//0x00000100 .p2align 3, 0x00
	//0x00000100 LCPI0_6
	0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, //0x00000100 .quad -9187201950435737472
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000108 .p2align 4, 0x90
	//0x00000110 _validate_utf8_fast
	0x55, //0x00000110 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000111 movq         %rsp, %rbp
	0x53, //0x00000114 pushq        %rbx
	0x48, 0x81, 0xec, 0xa0, 0x00, 0x00, 0x00, //0x00000115 subq         $160, %rsp
	0x4c, 0x8b, 0x57, 0x08, //0x0000011c movq         $8(%rdi), %r10
	0x4d, 0x85, 0xd2, //0x00000120 testq        %r10, %r10
	0x0f, 0x84, 0xb4, 0x07, 0x00, 0x00, //0x00000123 je           LBB0_28
	0x4c, 0x8b, 0x0f, //0x00000129 movq         (%rdi), %r9
	0x4d, 0x01, 0xca, //0x0000012c addq         %r9, %r10
	0x49, 0x8d, 0x72, 0x80, //0x0000012f leaq         $-128(%r10), %rsi
	0xc5, 0xf1, 0xef, 0xc9, //0x00000133 vpxor        %xmm1, %xmm1, %xmm1
	0xc5, 0xe9, 0xef, 0xd2, //0x00000137 vpxor        %xmm2, %xmm2, %xmm2
	0xc5, 0xf9, 0xef, 0xc0, //0x0000013b vpxor        %xmm0, %xmm0, %xmm0
	0x4c, 0x89, 0xc8, //0x0000013f movq         %r9, %rax
	0x4c, 0x39, 0xce, //0x00000142 cmpq         %r9, %rsi
	0x0f, 0x86, 0x6a, 0x03, 0x00, 0x00, //0x00000145 jbe          LBB0_12
	0xc5, 0xfe, 0x6f, 0x25, 0xad, 0xfe, 0xff, 0xff, //0x0000014b vmovdqu      $-339(%rip), %ymm4  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xe5, 0xfe, 0xff, 0xff, //0x00000153 vmovdqu      $-283(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xfd, 0xfe, 0xff, 0xff, //0x0000015b vmovdqu      $-259(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x15, 0xff, 0xff, 0xff, //0x00000163 vmovdqu      $-235(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x2d, 0xff, 0xff, 0xff, //0x0000016b vmovdqu      $-211(%rip), %ymm9  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x45, 0xff, 0xff, 0xff, //0x00000173 vmovdqu      $-187(%rip), %ymm10  /* LCPI0_7+0(%rip) */
	0x4c, 0x89, 0xc8, //0x0000017b movq         %r9, %rax
	0xc5, 0xf9, 0xef, 0xc0, //0x0000017e vpxor        %xmm0, %xmm0, %xmm0
	0xc5, 0xe9, 0xef, 0xd2, //0x00000182 vpxor        %xmm2, %xmm2, %xmm2
	0xc5, 0xf1, 0xef, 0xc9, //0x00000186 vpxor        %xmm1, %xmm1, %xmm1
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000018a .p2align 4, 0x90
	//0x00000190 LBB0_3
	0xc5, 0x7e, 0x6f, 0x38, //0x00000190 vmovdqu      (%rax), %ymm15
	0xc5, 0x7e, 0x6f, 0x68, 0x20, //0x00000194 vmovdqu      $32(%rax), %ymm13
	0xc5, 0x7e, 0x6f, 0x60, 0x40, //0x00000199 vmovdqu      $64(%rax), %ymm12
	0xc5, 0x7e, 0x6f, 0x58, 0x60, //0x0000019e vmovdqu      $96(%rax), %ymm11
	0xc4, 0xc1, 0x15, 0xeb, 0xdf, //0x000001a3 vpor         %ymm15, %ymm13, %ymm3
	0xc4, 0x41, 0x25, 0xeb, 0xf4, //0x000001a8 vpor         %ymm12, %ymm11, %ymm14
	0xc5, 0x8d, 0xeb, 0xeb, //0x000001ad vpor         %ymm3, %ymm14, %ymm5
	0xc5, 0xfd, 0xd7, 0xcd, //0x000001b1 vpmovmskb    %ymm5, %ecx
	0x85, 0xc9, //0x000001b5 testl        %ecx, %ecx
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x000001b7 jne          LBB0_6
	0xc5, 0xf5, 0xeb, 0xc0, //0x000001bd vpor         %ymm0, %ymm1, %ymm0
	//0x000001c1 LBB0_5
	0x48, 0x83, 0xe8, 0x80, //0x000001c1 subq         $-128, %rax
	0x48, 0x39, 0xf0, //0x000001c5 cmpq         %rsi, %rax
	0x0f, 0x82, 0xc2, 0xff, 0xff, 0xff, //0x000001c8 jb           LBB0_3
	0xe9, 0xe2, 0x02, 0x00, 0x00, //0x000001ce jmp          LBB0_12
	//0x000001d3 LBB0_6
	0xc5, 0xfd, 0xd7, 0xcb, //0x000001d3 vpmovmskb    %ymm3, %ecx
	0x85, 0xc9, //0x000001d7 testl        %ecx, %ecx
	0x0f, 0x85, 0xf0, 0x00, 0x00, 0x00, //0x000001d9 jne          LBB0_9
	0xc5, 0xf5, 0xeb, 0xc0, //0x000001df vpor         %ymm0, %ymm1, %ymm0
	0xc4, 0xc3, 0x6d, 0x46, 0xcc, 0x21, //0x000001e3 vperm2i128   $33, %ymm12, %ymm2, %ymm1
	0xc4, 0xe3, 0x1d, 0x0f, 0xd1, 0x0f, //0x000001e9 vpalignr     $15, %ymm1, %ymm12, %ymm2
	0xc5, 0xe5, 0x71, 0xd2, 0x04, //0x000001ef vpsrlw       $4, %ymm2, %ymm3
	0xc5, 0xe5, 0xdb, 0xdc, //0x000001f4 vpand        %ymm4, %ymm3, %ymm3
	0xc5, 0x7e, 0x6f, 0x35, 0x20, 0xfe, 0xff, 0xff, //0x000001f8 vmovdqu      $-480(%rip), %ymm14  /* LCPI0_1+0(%rip) */
	0xc4, 0xe2, 0x0d, 0x00, 0xdb, //0x00000200 vpshufb      %ymm3, %ymm14, %ymm3
	0xc5, 0xed, 0xdb, 0xd4, //0x00000205 vpand        %ymm4, %ymm2, %ymm2
	0xc4, 0xe2, 0x4d, 0x00, 0xd2, //0x00000209 vpshufb      %ymm2, %ymm6, %ymm2
	0xc4, 0xc1, 0x55, 0x71, 0xd4, 0x04, //0x0000020e vpsrlw       $4, %ymm12, %ymm5
	0xc5, 0xd5, 0xdb, 0xec, //0x00000214 vpand        %ymm4, %ymm5, %ymm5
	0xc4, 0xe2, 0x45, 0x00, 0xed, //0x00000218 vpshufb      %ymm5, %ymm7, %ymm5
	0xc5, 0xed, 0xdb, 0xd5, //0x0000021d vpand        %ymm5, %ymm2, %ymm2
	0xc5, 0xe5, 0xdb, 0xd2, //0x00000221 vpand        %ymm2, %ymm3, %ymm2
	0xc4, 0xe3, 0x1d, 0x0f, 0xd9, 0x0e, //0x00000225 vpalignr     $14, %ymm1, %ymm12, %ymm3
	0xc4, 0xe3, 0x1d, 0x0f, 0xc9, 0x0d, //0x0000022b vpalignr     $13, %ymm1, %ymm12, %ymm1
	0xc4, 0xc1, 0x65, 0xd8, 0xd8, //0x00000231 vpsubusb     %ymm8, %ymm3, %ymm3
	0xc4, 0xc1, 0x75, 0xd8, 0xc9, //0x00000236 vpsubusb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xeb, 0xcb, //0x0000023b vpor         %ymm3, %ymm1, %ymm1
	0xc4, 0x41, 0x11, 0xef, 0xed, //0x0000023f vpxor        %xmm13, %xmm13, %xmm13
	0xc5, 0x95, 0x74, 0xc9, //0x00000244 vpcmpeqb     %ymm1, %ymm13, %ymm1
	0xc4, 0xe2, 0x7d, 0x59, 0x1d, 0xaf, 0xfe, 0xff, 0xff, //0x00000248 vpbroadcastq $-337(%rip), %ymm3  /* LCPI0_6+0(%rip) */
	0xc5, 0xf5, 0xdf, 0xcb, //0x00000251 vpandn       %ymm3, %ymm1, %ymm1
	0xc5, 0xf5, 0xef, 0xca, //0x00000255 vpxor        %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x00000259 vpor         %ymm1, %ymm0, %ymm0
	0xc4, 0xc3, 0x1d, 0x46, 0xcb, 0x21, //0x0000025d vperm2i128   $33, %ymm11, %ymm12, %ymm1
	0xc4, 0xe3, 0x25, 0x0f, 0xd1, 0x0f, //0x00000263 vpalignr     $15, %ymm1, %ymm11, %ymm2
	0xc5, 0xd5, 0x71, 0xd2, 0x04, //0x00000269 vpsrlw       $4, %ymm2, %ymm5
	0xc5, 0xd5, 0xdb, 0xec, //0x0000026e vpand        %ymm4, %ymm5, %ymm5
	0xc4, 0xe2, 0x0d, 0x00, 0xed, //0x00000272 vpshufb      %ymm5, %ymm14, %ymm5
	0xc5, 0xed, 0xdb, 0xd4, //0x00000277 vpand        %ymm4, %ymm2, %ymm2
	0xc4, 0xe2, 0x4d, 0x00, 0xd2, //0x0000027b vpshufb      %ymm2, %ymm6, %ymm2
	0xc4, 0xc1, 0x1d, 0x71, 0xd3, 0x04, //0x00000280 vpsrlw       $4, %ymm11, %ymm12
	0xc5, 0x1d, 0xdb, 0xe4, //0x00000286 vpand        %ymm4, %ymm12, %ymm12
	0xc4, 0x42, 0x45, 0x00, 0xe4, //0x0000028a vpshufb      %ymm12, %ymm7, %ymm12
	0xc5, 0x9d, 0xdb, 0xd2, //0x0000028f vpand        %ymm2, %ymm12, %ymm2
	0xc5, 0xd5, 0xdb, 0xd2, //0x00000293 vpand        %ymm2, %ymm5, %ymm2
	0xc4, 0xe3, 0x25, 0x0f, 0xe9, 0x0e, //0x00000297 vpalignr     $14, %ymm1, %ymm11, %ymm5
	0xc4, 0xe3, 0x25, 0x0f, 0xc9, 0x0d, //0x0000029d vpalignr     $13, %ymm1, %ymm11, %ymm1
	0xc4, 0xc1, 0x55, 0xd8, 0xe8, //0x000002a3 vpsubusb     %ymm8, %ymm5, %ymm5
	0xc4, 0xc1, 0x75, 0xd8, 0xc9, //0x000002a8 vpsubusb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xeb, 0xcd, //0x000002ad vpor         %ymm5, %ymm1, %ymm1
	0xc5, 0x95, 0x74, 0xc9, //0x000002b1 vpcmpeqb     %ymm1, %ymm13, %ymm1
	0xc5, 0xf5, 0xdf, 0xcb, //0x000002b5 vpandn       %ymm3, %ymm1, %ymm1
	0xc5, 0xf5, 0xef, 0xca, //0x000002b9 vpxor        %ymm2, %ymm1, %ymm1
	//0x000002bd LBB0_8
	0xc5, 0xfd, 0xeb, 0xc1, //0x000002bd vpor         %ymm1, %ymm0, %ymm0
	0xc4, 0xc1, 0x25, 0xd8, 0xca, //0x000002c1 vpsubusb     %ymm10, %ymm11, %ymm1
	0xc5, 0x7d, 0x7f, 0xda, //0x000002c6 vmovdqa      %ymm11, %ymm2
	0xe9, 0xf2, 0xfe, 0xff, 0xff, //0x000002ca jmp          LBB0_5
	//0x000002cf LBB0_9
	0xc4, 0xc3, 0x6d, 0x46, 0xcf, 0x21, //0x000002cf vperm2i128   $33, %ymm15, %ymm2, %ymm1
	0xc4, 0xe3, 0x05, 0x0f, 0xd1, 0x0f, //0x000002d5 vpalignr     $15, %ymm1, %ymm15, %ymm2
	0xc5, 0xe5, 0x71, 0xd2, 0x04, //0x000002db vpsrlw       $4, %ymm2, %ymm3
	0xc5, 0xe5, 0xdb, 0xdc, //0x000002e0 vpand        %ymm4, %ymm3, %ymm3
	0xc5, 0xfe, 0x6f, 0x2d, 0x34, 0xfd, 0xff, 0xff, //0x000002e4 vmovdqu      $-716(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc4, 0xe2, 0x55, 0x00, 0xdb, //0x000002ec vpshufb      %ymm3, %ymm5, %ymm3
	0xc5, 0xed, 0xdb, 0xd4, //0x000002f1 vpand        %ymm4, %ymm2, %ymm2
	0xc4, 0xe2, 0x4d, 0x00, 0xd2, //0x000002f5 vpshufb      %ymm2, %ymm6, %ymm2
	0xc4, 0xc1, 0x55, 0x71, 0xd7, 0x04, //0x000002fa vpsrlw       $4, %ymm15, %ymm5
	0xc5, 0xd5, 0xdb, 0xec, //0x00000300 vpand        %ymm4, %ymm5, %ymm5
	0xc4, 0xe2, 0x45, 0x00, 0xed, //0x00000304 vpshufb      %ymm5, %ymm7, %ymm5
	0xc5, 0xed, 0xdb, 0xd5, //0x00000309 vpand        %ymm5, %ymm2, %ymm2
	0xc5, 0xe5, 0xdb, 0xd2, //0x0000030d vpand        %ymm2, %ymm3, %ymm2
	0xc4, 0xe3, 0x05, 0x0f, 0xd9, 0x0e, //0x00000311 vpalignr     $14, %ymm1, %ymm15, %ymm3
	0xc4, 0xe3, 0x05, 0x0f, 0xc9, 0x0d, //0x00000317 vpalignr     $13, %ymm1, %ymm15, %ymm1
	0xc4, 0xc1, 0x65, 0xd8, 0xd8, //0x0000031d vpsubusb     %ymm8, %ymm3, %ymm3
	0xc4, 0xc1, 0x75, 0xd8, 0xc9, //0x00000322 vpsubusb     %ymm9, %ymm1, %ymm1
	0xc5, 0xf5, 0xeb, 0xcb, //0x00000327 vpor         %ymm3, %ymm1, %ymm1
	0xc5, 0xe1, 0xef, 0xdb, //0x0000032b vpxor        %xmm3, %xmm3, %xmm3
	0xc5, 0xf5, 0x74, 0xdb, //0x0000032f vpcmpeqb     %ymm3, %ymm1, %ymm3
	0xc4, 0xe2, 0x7d, 0x59, 0x0d, 0xc4, 0xfd, 0xff, 0xff, //0x00000333 vpbroadcastq $-572(%rip), %ymm1  /* LCPI0_6+0(%rip) */
	0xc5, 0xe5, 0xdf, 0xd9, //0x0000033c vpandn       %ymm1, %ymm3, %ymm3
	0xc5, 0xe5, 0xef, 0xd2, //0x00000340 vpxor        %ymm2, %ymm3, %ymm2
	0xc5, 0xfe, 0x7f, 0x54, 0x24, 0x60, //0x00000344 vmovdqu      %ymm2, $96(%rsp)
	0xc4, 0xc3, 0x05, 0x46, 0xdd, 0x21, //0x0000034a vperm2i128   $33, %ymm13, %ymm15, %ymm3
	0xc4, 0xe3, 0x15, 0x0f, 0xeb, 0x0f, //0x00000350 vpalignr     $15, %ymm3, %ymm13, %ymm5
	0xc5, 0x85, 0x71, 0xd5, 0x04, //0x00000356 vpsrlw       $4, %ymm5, %ymm15
	0xc5, 0x05, 0xdb, 0xfc, //0x0000035b vpand        %ymm4, %ymm15, %ymm15
	0xc5, 0xfe, 0x6f, 0x15, 0xb9, 0xfc, 0xff, 0xff, //0x0000035f vmovdqu      $-839(%rip), %ymm2  /* LCPI0_1+0(%rip) */
	0xc4, 0x42, 0x6d, 0x00, 0xff, //0x00000367 vpshufb      %ymm15, %ymm2, %ymm15
	0xc5, 0xd5, 0xdb, 0xec, //0x0000036c vpand        %ymm4, %ymm5, %ymm5
	0xc4, 0xe2, 0x4d, 0x00, 0xed, //0x00000370 vpshufb      %ymm5, %ymm6, %ymm5
	0xc4, 0xc1, 0x6d, 0x71, 0xd5, 0x04, //0x00000375 vpsrlw       $4, %ymm13, %ymm2
	0xc5, 0xed, 0xdb, 0xd4, //0x0000037b vpand        %ymm4, %ymm2, %ymm2
	0xc4, 0xe2, 0x45, 0x00, 0xd2, //0x0000037f vpshufb      %ymm2, %ymm7, %ymm2
	0xc5, 0xd5, 0xdb, 0xd2, //0x00000384 vpand        %ymm2, %ymm5, %ymm2
	0xc5, 0x85, 0xdb, 0xd2, //0x00000388 vpand        %ymm2, %ymm15, %ymm2
	0xc4, 0xe3, 0x15, 0x0f, 0xeb, 0x0e, //0x0000038c vpalignr     $14, %ymm3, %ymm13, %ymm5
	0xc4, 0xe3, 0x15, 0x0f, 0xdb, 0x0d, //0x00000392 vpalignr     $13, %ymm3, %ymm13, %ymm3
	0xc4, 0xc1, 0x55, 0xd8, 0xe8, //0x00000398 vpsubusb     %ymm8, %ymm5, %ymm5
	0xc4, 0xc1, 0x65, 0xd8, 0xd9, //0x0000039d vpsubusb     %ymm9, %ymm3, %ymm3
	0xc5, 0xe5, 0xeb, 0xdd, //0x000003a2 vpor         %ymm5, %ymm3, %ymm3
	0xc5, 0xe5, 0x74, 0x1d, 0x32, 0xfd, 0xff, 0xff, //0x000003a6 vpcmpeqb     $-718(%rip), %ymm3, %ymm3  /* LCPI0_8+0(%rip) */
	0xc5, 0xe5, 0xdf, 0xd9, //0x000003ae vpandn       %ymm1, %ymm3, %ymm3
	0xc5, 0xe5, 0xef, 0xd2, //0x000003b2 vpxor        %ymm2, %ymm3, %ymm2
	0xc5, 0xfd, 0xeb, 0x44, 0x24, 0x60, //0x000003b6 vpor         $96(%rsp), %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc2, //0x000003bc vpor         %ymm2, %ymm0, %ymm0
	0xc4, 0xc1, 0x7d, 0xd7, 0xce, //0x000003c0 vpmovmskb    %ymm14, %ecx
	0x85, 0xc9, //0x000003c5 testl        %ecx, %ecx
	0x0f, 0x84, 0xd6, 0x00, 0x00, 0x00, //0x000003c7 je           LBB0_11
	0xc4, 0xc3, 0x15, 0x46, 0xd4, 0x21, //0x000003cd vperm2i128   $33, %ymm12, %ymm13, %ymm2
	0xc4, 0xe3, 0x1d, 0x0f, 0xda, 0x0f, //0x000003d3 vpalignr     $15, %ymm2, %ymm12, %ymm3
	0xc5, 0xd5, 0x71, 0xd3, 0x04, //0x000003d9 vpsrlw       $4, %ymm3, %ymm5
	0xc5, 0xd5, 0xdb, 0xec, //0x000003de vpand        %ymm4, %ymm5, %ymm5
	0xc5, 0x7e, 0x6f, 0x3d, 0x36, 0xfc, 0xff, 0xff, //0x000003e2 vmovdqu      $-970(%rip), %ymm15  /* LCPI0_1+0(%rip) */
	0xc4, 0xe2, 0x05, 0x00, 0xed, //0x000003ea vpshufb      %ymm5, %ymm15, %ymm5
	0xc5, 0xe5, 0xdb, 0xdc, //0x000003ef vpand        %ymm4, %ymm3, %ymm3
	0xc4, 0xe2, 0x4d, 0x00, 0xdb, //0x000003f3 vpshufb      %ymm3, %ymm6, %ymm3
	0xc4, 0xc1, 0x15, 0x71, 0xd4, 0x04, //0x000003f8 vpsrlw       $4, %ymm12, %ymm13
	0xc5, 0x15, 0xdb, 0xec, //0x000003fe vpand        %ymm4, %ymm13, %ymm13
	0xc4, 0x42, 0x45, 0x00, 0xed, //0x00000402 vpshufb      %ymm13, %ymm7, %ymm13
	0xc5, 0x95, 0xdb, 0xdb, //0x00000407 vpand        %ymm3, %ymm13, %ymm3
	0xc5, 0xd5, 0xdb, 0xdb, //0x0000040b vpand        %ymm3, %ymm5, %ymm3
	0xc4, 0xe3, 0x1d, 0x0f, 0xea, 0x0e, //0x0000040f vpalignr     $14, %ymm2, %ymm12, %ymm5
	0xc4, 0xe3, 0x1d, 0x0f, 0xd2, 0x0d, //0x00000415 vpalignr     $13, %ymm2, %ymm12, %ymm2
	0xc4, 0xc1, 0x55, 0xd8, 0xe8, //0x0000041b vpsubusb     %ymm8, %ymm5, %ymm5
	0xc4, 0xc1, 0x6d, 0xd8, 0xd1, //0x00000420 vpsubusb     %ymm9, %ymm2, %ymm2
	0xc5, 0xed, 0xeb, 0xd5, //0x00000425 vpor         %ymm5, %ymm2, %ymm2
	0xc4, 0x41, 0x09, 0xef, 0xf6, //0x00000429 vpxor        %xmm14, %xmm14, %xmm14
	0xc5, 0x8d, 0x74, 0xd2, //0x0000042e vpcmpeqb     %ymm2, %ymm14, %ymm2
	0xc5, 0xed, 0xdf, 0xd1, //0x00000432 vpandn       %ymm1, %ymm2, %ymm2
	0xc5, 0xed, 0xef, 0xd3, //0x00000436 vpxor        %ymm3, %ymm2, %ymm2
	0xc4, 0xc3, 0x1d, 0x46, 0xdb, 0x21, //0x0000043a vperm2i128   $33, %ymm11, %ymm12, %ymm3
	0xc4, 0xe3, 0x25, 0x0f, 0xeb, 0x0f, //0x00000440 vpalignr     $15, %ymm3, %ymm11, %ymm5
	0xc5, 0x9d, 0x71, 0xd5, 0x04, //0x00000446 vpsrlw       $4, %ymm5, %ymm12
	0xc5, 0x1d, 0xdb, 0xe4, //0x0000044b vpand        %ymm4, %ymm12, %ymm12
	0xc4, 0x42, 0x05, 0x00, 0xe4, //0x0000044f vpshufb      %ymm12, %ymm15, %ymm12
	0xc5, 0xd5, 0xdb, 0xec, //0x00000454 vpand        %ymm4, %ymm5, %ymm5
	0xc4, 0xe2, 0x4d, 0x00, 0xed, //0x00000458 vpshufb      %ymm5, %ymm6, %ymm5
	0xc4, 0xc1, 0x15, 0x71, 0xd3, 0x04, //0x0000045d vpsrlw       $4, %ymm11, %ymm13
	0xc5, 0x15, 0xdb, 0xec, //0x00000463 vpand        %ymm4, %ymm13, %ymm13
	0xc4, 0x42, 0x45, 0x00, 0xed, //0x00000467 vpshufb      %ymm13, %ymm7, %ymm13
	0xc5, 0x95, 0xdb, 0xed, //0x0000046c vpand        %ymm5, %ymm13, %ymm5
	0xc5, 0x9d, 0xdb, 0xed, //0x00000470 vpand        %ymm5, %ymm12, %ymm5
	0xc4, 0x63, 0x25, 0x0f, 0xe3, 0x0e, //0x00000474 vpalignr     $14, %ymm3, %ymm11, %ymm12
	0xc4, 0xe3, 0x25, 0x0f, 0xdb, 0x0d, //0x0000047a vpalignr     $13, %ymm3, %ymm11, %ymm3
	0xc4, 0x41, 0x1d, 0xd8, 0xe0, //0x00000480 vpsubusb     %ymm8, %ymm12, %ymm12
	0xc4, 0xc1, 0x65, 0xd8, 0xd9, //0x00000485 vpsubusb     %ymm9, %ymm3, %ymm3
	0xc5, 0x9d, 0xeb, 0xdb, //0x0000048a vpor         %ymm3, %ymm12, %ymm3
	0xc5, 0x8d, 0x74, 0xdb, //0x0000048e vpcmpeqb     %ymm3, %ymm14, %ymm3
	0xc5, 0xe5, 0xdf, 0xc9, //0x00000492 vpandn       %ymm1, %ymm3, %ymm1
	0xc5, 0xf5, 0xef, 0xcd, //0x00000496 vpxor        %ymm5, %ymm1, %ymm1
	0xc5, 0xed, 0xeb, 0xc0, //0x0000049a vpor         %ymm0, %ymm2, %ymm0
	0xe9, 0x1a, 0xfe, 0xff, 0xff, //0x0000049e jmp          LBB0_8
	//0x000004a3 LBB0_11
	0xc4, 0xc1, 0x15, 0xd8, 0xca, //0x000004a3 vpsubusb     %ymm10, %ymm13, %ymm1
	0xc5, 0xfd, 0xeb, 0xc1, //0x000004a8 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0x7d, 0x7f, 0xea, //0x000004ac vmovdqa      %ymm13, %ymm2
	0xe9, 0x0c, 0xfd, 0xff, 0xff, //0x000004b0 jmp          LBB0_5
	//0x000004b5 LBB0_12
	0x49, 0x8d, 0x72, 0xc0, //0x000004b5 leaq         $-64(%r10), %rsi
	0x48, 0x39, 0xf0, //0x000004b9 cmpq         %rsi, %rax
	0x0f, 0x83, 0x59, 0x01, 0x00, 0x00, //0x000004bc jae          LBB0_17
	0xc5, 0xfe, 0x6f, 0x1d, 0x36, 0xfb, 0xff, 0xff, //0x000004c2 vmovdqu      $-1226(%rip), %ymm3  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x4e, 0xfb, 0xff, 0xff, //0x000004ca vmovdqu      $-1202(%rip), %ymm4  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x66, 0xfb, 0xff, 0xff, //0x000004d2 vmovdqu      $-1178(%rip), %ymm5  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x7e, 0xfb, 0xff, 0xff, //0x000004da vmovdqu      $-1154(%rip), %ymm6  /* LCPI0_3+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x96, 0xfb, 0xff, 0xff, //0x000004e2 vmovdqu      $-1130(%rip), %ymm7  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xae, 0xfb, 0xff, 0xff, //0x000004ea vmovdqu      $-1106(%rip), %ymm8  /* LCPI0_5+0(%rip) */
	0xc4, 0x41, 0x31, 0xef, 0xc9, //0x000004f2 vpxor        %xmm9, %xmm9, %xmm9
	0xc5, 0x7e, 0x6f, 0x15, 0xc1, 0xfb, 0xff, 0xff, //0x000004f7 vmovdqu      $-1087(%rip), %ymm10  /* LCPI0_7+0(%rip) */
	0x90, //0x000004ff .p2align 4, 0x90
	//0x00000500 LBB0_14
	0xc5, 0x7e, 0x6f, 0x20, //0x00000500 vmovdqu      (%rax), %ymm12
	0xc5, 0x7e, 0x6f, 0x58, 0x20, //0x00000504 vmovdqu      $32(%rax), %ymm11
	0xc4, 0x41, 0x25, 0xeb, 0xec, //0x00000509 vpor         %ymm12, %ymm11, %ymm13
	0xc4, 0xc1, 0x7d, 0xd7, 0xcd, //0x0000050e vpmovmskb    %ymm13, %ecx
	0x85, 0xc9, //0x00000513 testl        %ecx, %ecx
	0x0f, 0x85, 0x16, 0x00, 0x00, 0x00, //0x00000515 jne          LBB0_16
	0xc5, 0xf5, 0xeb, 0xc0, //0x0000051b vpor         %ymm0, %ymm1, %ymm0
	0x48, 0x83, 0xc0, 0x40, //0x0000051f addq         $64, %rax
	0x48, 0x39, 0xf0, //0x00000523 cmpq         %rsi, %rax
	0x0f, 0x82, 0xd4, 0xff, 0xff, 0xff, //0x00000526 jb           LBB0_14
	0xe9, 0xea, 0x00, 0x00, 0x00, //0x0000052c jmp          LBB0_17
	//0x00000531 LBB0_16
	0xc4, 0xc3, 0x6d, 0x46, 0xcc, 0x21, //0x00000531 vperm2i128   $33, %ymm12, %ymm2, %ymm1
	0xc4, 0xe3, 0x1d, 0x0f, 0xd1, 0x0f, //0x00000537 vpalignr     $15, %ymm1, %ymm12, %ymm2
	0xc5, 0x95, 0x71, 0xd2, 0x04, //0x0000053d vpsrlw       $4, %ymm2, %ymm13
	0xc5, 0x15, 0xdb, 0xeb, //0x00000542 vpand        %ymm3, %ymm13, %ymm13
	0xc4, 0x42, 0x5d, 0x00, 0xed, //0x00000546 vpshufb      %ymm13, %ymm4, %ymm13
	0xc5, 0xed, 0xdb, 0xd3, //0x0000054b vpand        %ymm3, %ymm2, %ymm2
	0xc4, 0xe2, 0x55, 0x00, 0xd2, //0x0000054f vpshufb      %ymm2, %ymm5, %ymm2
	0xc4, 0xc1, 0x0d, 0x71, 0xd4, 0x04, //0x00000554 vpsrlw       $4, %ymm12, %ymm14
	0xc5, 0x0d, 0xdb, 0xf3, //0x0000055a vpand        %ymm3, %ymm14, %ymm14
	0xc4, 0x42, 0x4d, 0x00, 0xf6, //0x0000055e vpshufb      %ymm14, %ymm6, %ymm14
	0xc5, 0x8d, 0xdb, 0xd2, //0x00000563 vpand        %ymm2, %ymm14, %ymm2
	0xc5, 0x95, 0xdb, 0xd2, //0x00000567 vpand        %ymm2, %ymm13, %ymm2
	0xc4, 0x63, 0x1d, 0x0f, 0xe9, 0x0e, //0x0000056b vpalignr     $14, %ymm1, %ymm12, %ymm13
	0xc4, 0xe3, 0x1d, 0x0f, 0xc9, 0x0d, //0x00000571 vpalignr     $13, %ymm1, %ymm12, %ymm1
	0xc5, 0x15, 0xd8, 0xef, //0x00000577 vpsubusb     %ymm7, %ymm13, %ymm13
	0xc4, 0xc1, 0x75, 0xd8, 0xc8, //0x0000057b vpsubusb     %ymm8, %ymm1, %ymm1
	0xc5, 0x95, 0xeb, 0xc9, //0x00000580 vpor         %ymm1, %ymm13, %ymm1
	0xc5, 0xb5, 0x74, 0xc9, //0x00000584 vpcmpeqb     %ymm1, %ymm9, %ymm1
	0xc4, 0x62, 0x7d, 0x59, 0x2d, 0x6f, 0xfb, 0xff, 0xff, //0x00000588 vpbroadcastq $-1169(%rip), %ymm13  /* LCPI0_6+0(%rip) */
	0xc4, 0xc1, 0x75, 0xdf, 0xcd, //0x00000591 vpandn       %ymm13, %ymm1, %ymm1
	0xc5, 0xf5, 0xef, 0xca, //0x00000596 vpxor        %ymm2, %ymm1, %ymm1
	0xc4, 0xc3, 0x1d, 0x46, 0xd3, 0x21, //0x0000059a vperm2i128   $33, %ymm11, %ymm12, %ymm2
	0xc4, 0x63, 0x25, 0x0f, 0xe2, 0x0f, //0x000005a0 vpalignr     $15, %ymm2, %ymm11, %ymm12
	0xc4, 0xc1, 0x0d, 0x71, 0xd4, 0x04, //0x000005a6 vpsrlw       $4, %ymm12, %ymm14
	0xc5, 0x0d, 0xdb, 0xf3, //0x000005ac vpand        %ymm3, %ymm14, %ymm14
	0xc4, 0x42, 0x5d, 0x00, 0xf6, //0x000005b0 vpshufb      %ymm14, %ymm4, %ymm14
	0xc5, 0x1d, 0xdb, 0xe3, //0x000005b5 vpand        %ymm3, %ymm12, %ymm12
	0xc4, 0x42, 0x55, 0x00, 0xe4, //0x000005b9 vpshufb      %ymm12, %ymm5, %ymm12
	0xc4, 0xc1, 0x05, 0x71, 0xd3, 0x04, //0x000005be vpsrlw       $4, %ymm11, %ymm15
	0xc5, 0x05, 0xdb, 0xfb, //0x000005c4 vpand        %ymm3, %ymm15, %ymm15
	0xc4, 0x42, 0x4d, 0x00, 0xff, //0x000005c8 vpshufb      %ymm15, %ymm6, %ymm15
	0xc4, 0x41, 0x1d, 0xdb, 0xe7, //0x000005cd vpand        %ymm15, %ymm12, %ymm12
	0xc4, 0x41, 0x0d, 0xdb, 0xe4, //0x000005d2 vpand        %ymm12, %ymm14, %ymm12
	0xc4, 0x63, 0x25, 0x0f, 0xf2, 0x0e, //0x000005d7 vpalignr     $14, %ymm2, %ymm11, %ymm14
	0xc4, 0xe3, 0x25, 0x0f, 0xd2, 0x0d, //0x000005dd vpalignr     $13, %ymm2, %ymm11, %ymm2
	0xc5, 0x0d, 0xd8, 0xf7, //0x000005e3 vpsubusb     %ymm7, %ymm14, %ymm14
	0xc4, 0xc1, 0x6d, 0xd8, 0xd0, //0x000005e7 vpsubusb     %ymm8, %ymm2, %ymm2
	0xc5, 0x8d, 0xeb, 0xd2, //0x000005ec vpor         %ymm2, %ymm14, %ymm2
	0xc5, 0xb5, 0x74, 0xd2, //0x000005f0 vpcmpeqb     %ymm2, %ymm9, %ymm2
	0xc4, 0xc1, 0x6d, 0xdf, 0xd5, //0x000005f4 vpandn       %ymm13, %ymm2, %ymm2
	0xc5, 0x9d, 0xef, 0xd2, //0x000005f9 vpxor        %ymm2, %ymm12, %ymm2
	0xc5, 0xf5, 0xeb, 0xc0, //0x000005fd vpor         %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xeb, 0xc2, //0x00000601 vpor         %ymm2, %ymm0, %ymm0
	0xc4, 0xc1, 0x25, 0xd8, 0xca, //0x00000605 vpsubusb     %ymm10, %ymm11, %ymm1
	0xc5, 0x7d, 0x7f, 0xda, //0x0000060a vmovdqa      %ymm11, %ymm2
	0x48, 0x83, 0xc0, 0x40, //0x0000060e addq         $64, %rax
	0x48, 0x39, 0xf0, //0x00000612 cmpq         %rsi, %rax
	0x0f, 0x82, 0xe5, 0xfe, 0xff, 0xff, //0x00000615 jb           LBB0_14
	//0x0000061b LBB0_17
	0xc5, 0xe1, 0xef, 0xdb, //0x0000061b vpxor        %xmm3, %xmm3, %xmm3
	0xc5, 0xfe, 0x7f, 0x5c, 0x24, 0x40, //0x0000061f vmovdqu      %ymm3, $64(%rsp)
	0xc5, 0xfe, 0x7f, 0x5c, 0x24, 0x20, //0x00000625 vmovdqu      %ymm3, $32(%rsp)
	0xc5, 0xd9, 0xef, 0xe4, //0x0000062b vpxor        %xmm4, %xmm4, %xmm4
	0x4c, 0x39, 0xd0, //0x0000062f cmpq         %r10, %rax
	0x0f, 0x83, 0x78, 0x00, 0x00, 0x00, //0x00000632 jae          LBB0_25
	0x4c, 0x89, 0xd7, //0x00000638 movq         %r10, %rdi
	0x48, 0x29, 0xc7, //0x0000063b subq         %rax, %rdi
	0x48, 0x83, 0xff, 0x10, //0x0000063e cmpq         $16, %rdi
	0x0f, 0x82, 0x23, 0x00, 0x00, 0x00, //0x00000642 jb           LBB0_21
	0x48, 0x8d, 0x0c, 0x38, //0x00000648 leaq         (%rax,%rdi), %rcx
	0x48, 0x8d, 0x54, 0x24, 0x20, //0x0000064c leaq         $32(%rsp), %rdx
	0x48, 0x39, 0xca, //0x00000651 cmpq         %rcx, %rdx
	0x0f, 0x83, 0x8f, 0x02, 0x00, 0x00, //0x00000654 jae          LBB0_29
	0x48, 0x8d, 0x0c, 0x3c, //0x0000065a leaq         (%rsp,%rdi), %rcx
	0x48, 0x83, 0xc1, 0x20, //0x0000065e addq         $32, %rcx
	0x48, 0x39, 0xc8, //0x00000662 cmpq         %rcx, %rax
	0x0f, 0x83, 0x7e, 0x02, 0x00, 0x00, //0x00000665 jae          LBB0_29
	//0x0000066b LBB0_21
	0x31, 0xf6, //0x0000066b xorl         %esi, %esi
	0x48, 0x89, 0xc1, //0x0000066d movq         %rax, %rcx
	//0x00000670 LBB0_22
	0x48, 0x8d, 0x14, 0x34, //0x00000670 leaq         (%rsp,%rsi), %rdx
	0x48, 0x83, 0xc2, 0x20, //0x00000674 addq         $32, %rdx
	0x4c, 0x89, 0xd7, //0x00000678 movq         %r10, %rdi
	0x48, 0x29, 0xf7, //0x0000067b subq         %rsi, %rdi
	0x48, 0x29, 0xc7, //0x0000067e subq         %rax, %rdi
	0x31, 0xc0, //0x00000681 xorl         %eax, %eax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000683 .p2align 4, 0x90
	//0x00000690 LBB0_23
	0x0f, 0xb6, 0x1c, 0x01, //0x00000690 movzbl       (%rcx,%rax), %ebx
	0x88, 0x1c, 0x02, //0x00000694 movb         %bl, (%rdx,%rax)
	0x48, 0x83, 0xc0, 0x01, //0x00000697 addq         $1, %rax
	0x48, 0x39, 0xc7, //0x0000069b cmpq         %rax, %rdi
	0x0f, 0x85, 0xec, 0xff, 0xff, 0xff, //0x0000069e jne          LBB0_23
	//0x000006a4 LBB0_24
	0xc5, 0xfe, 0x6f, 0x64, 0x24, 0x20, //0x000006a4 vmovdqu      $32(%rsp), %ymm4
	0xc5, 0xfe, 0x6f, 0x5c, 0x24, 0x40, //0x000006aa vmovdqu      $64(%rsp), %ymm3
	//0x000006b0 LBB0_25
	0xc5, 0xdd, 0xeb, 0xeb, //0x000006b0 vpor         %ymm3, %ymm4, %ymm5
	0xc5, 0xfd, 0xd7, 0xc5, //0x000006b4 vpmovmskb    %ymm5, %eax
	0x85, 0xc0, //0x000006b8 testl        %eax, %eax
	0x0f, 0x85, 0x8f, 0x03, 0x00, 0x00, //0x000006ba jne          LBB0_43
	0xc5, 0xfd, 0xeb, 0xc1, //0x000006c0 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x000006c4 vpor         %ymm1, %ymm0, %ymm0
	0xc4, 0xe2, 0x7d, 0x17, 0xc0, //0x000006c8 vptest       %ymm0, %ymm0
	0x0f, 0x84, 0x0a, 0x02, 0x00, 0x00, //0x000006cd je           LBB0_28
	//0x000006d3 LBB0_44
	0x49, 0x8d, 0x72, 0xfd, //0x000006d3 leaq         $-3(%r10), %rsi
	0x4c, 0x89, 0xc8, //0x000006d7 movq         %r9, %rax
	0x4c, 0x39, 0xce, //0x000006da cmpq         %r9, %rsi
	0x0f, 0x86, 0xdd, 0x00, 0x00, 0x00, //0x000006dd jbe          LBB0_58
	0x4c, 0x89, 0xc8, //0x000006e3 movq         %r9, %rax
	0xe9, 0x11, 0x00, 0x00, 0x00, //0x000006e6 jmp          LBB0_47
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000006eb .p2align 4, 0x90
	//0x000006f0 LBB0_46
	0x48, 0x01, 0xd0, //0x000006f0 addq         %rdx, %rax
	0x48, 0x39, 0xf0, //0x000006f3 cmpq         %rsi, %rax
	0x0f, 0x83, 0xc4, 0x00, 0x00, 0x00, //0x000006f6 jae          LBB0_58
	//0x000006fc LBB0_47
	0xba, 0x01, 0x00, 0x00, 0x00, //0x000006fc movl         $1, %edx
	0x80, 0x38, 0x00, //0x00000701 cmpb         $0, (%rax)
	0x0f, 0x89, 0xe6, 0xff, 0xff, 0xff, //0x00000704 jns          LBB0_46
	0x8b, 0x08, //0x0000070a movl         (%rax), %ecx
	0x89, 0xca, //0x0000070c movl         %ecx, %edx
	0x81, 0xe2, 0xf0, 0xc0, 0xc0, 0x00, //0x0000070e andl         $12632304, %edx
	0x81, 0xfa, 0xe0, 0x80, 0x80, 0x00, //0x00000714 cmpl         $8421600, %edx
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x0000071a jne          LBB0_51
	0x89, 0xcf, //0x00000720 movl         %ecx, %edi
	0x81, 0xe7, 0x0f, 0x20, 0x00, 0x00, //0x00000722 andl         $8207, %edi
	0x81, 0xff, 0x0d, 0x20, 0x00, 0x00, //0x00000728 cmpl         $8205, %edi
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x0000072e je           LBB0_51
	0xba, 0x03, 0x00, 0x00, 0x00, //0x00000734 movl         $3, %edx
	0x85, 0xff, //0x00000739 testl        %edi, %edi
	0x0f, 0x85, 0xaf, 0xff, 0xff, 0xff, //0x0000073b jne          LBB0_46
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000741 .p2align 4, 0x90
	//0x00000750 LBB0_51
	0x89, 0xca, //0x00000750 movl         %ecx, %edx
	0x81, 0xe2, 0xe0, 0xc0, 0x00, 0x00, //0x00000752 andl         $49376, %edx
	0x81, 0xfa, 0xc0, 0x80, 0x00, 0x00, //0x00000758 cmpl         $32960, %edx
	0x0f, 0x85, 0x10, 0x00, 0x00, 0x00, //0x0000075e jne          LBB0_53
	0x89, 0xcf, //0x00000764 movl         %ecx, %edi
	0xba, 0x02, 0x00, 0x00, 0x00, //0x00000766 movl         $2, %edx
	0x83, 0xe7, 0x1e, //0x0000076b andl         $30, %edi
	0x0f, 0x85, 0x7c, 0xff, 0xff, 0xff, //0x0000076e jne          LBB0_46
	//0x00000774 LBB0_53
	0x89, 0xca, //0x00000774 movl         %ecx, %edx
	0x81, 0xe2, 0xf8, 0xc0, 0xc0, 0xc0, //0x00000776 andl         $-1061109512, %edx
	0x81, 0xfa, 0xf0, 0x80, 0x80, 0x80, //0x0000077c cmpl         $-2139062032, %edx
	0x0f, 0x85, 0x28, 0x00, 0x00, 0x00, //0x00000782 jne          LBB0_57
	0x89, 0xca, //0x00000788 movl         %ecx, %edx
	0x81, 0xe2, 0x07, 0x30, 0x00, 0x00, //0x0000078a andl         $12295, %edx
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00000790 je           LBB0_57
	0xba, 0x04, 0x00, 0x00, 0x00, //0x00000796 movl         $4, %edx
	0xf6, 0xc1, 0x04, //0x0000079b testb        $4, %cl
	0x0f, 0x84, 0x4c, 0xff, 0xff, 0xff, //0x0000079e je           LBB0_46
	0x81, 0xe1, 0x03, 0x30, 0x00, 0x00, //0x000007a4 andl         $12291, %ecx
	0x0f, 0x84, 0x40, 0xff, 0xff, 0xff, //0x000007aa je           LBB0_46
	//0x000007b0 LBB0_57
	0x48, 0xf7, 0xd0, //0x000007b0 notq         %rax
	0x4c, 0x01, 0xc8, //0x000007b3 addq         %r9, %rax
	0x48, 0x8d, 0x65, 0xf8, //0x000007b6 leaq         $-8(%rbp), %rsp
	0x5b, //0x000007ba popq         %rbx
	0x5d, //0x000007bb popq         %rbp
	0xc5, 0xf8, 0x77, //0x000007bc vzeroupper   
	0xc3, //0x000007bf retq         
	//0x000007c0 LBB0_58
	0x4c, 0x39, 0xd0, //0x000007c0 cmpq         %r10, %rax
	0x0f, 0x83, 0x14, 0x01, 0x00, 0x00, //0x000007c3 jae          LBB0_28
	0x4c, 0x8d, 0x44, 0x24, 0x20, //0x000007c9 leaq         $32(%rsp), %r8
	0x4c, 0x8d, 0x5c, 0x24, 0x1e, //0x000007ce leaq         $30(%rsp), %r11
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x000007d3 jmp          LBB0_61
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000007d8 .p2align 4, 0x90
	//0x000007e0 LBB0_60
	0x48, 0x83, 0xc0, 0x01, //0x000007e0 addq         $1, %rax
	0x4c, 0x39, 0xd0, //0x000007e4 cmpq         %r10, %rax
	0x0f, 0x83, 0xf0, 0x00, 0x00, 0x00, //0x000007e7 jae          LBB0_28
	//0x000007ed LBB0_61
	0x80, 0x38, 0x00, //0x000007ed cmpb         $0, (%rax)
	0x0f, 0x89, 0xea, 0xff, 0xff, 0xff, //0x000007f0 jns          LBB0_60
	0xc6, 0x44, 0x24, 0x20, 0x00, //0x000007f6 movb         $0, $32(%rsp)
	0xc6, 0x44, 0x24, 0x1e, 0x00, //0x000007fb movb         $0, $30(%rsp)
	0x4c, 0x89, 0xd2, //0x00000800 movq         %r10, %rdx
	0x48, 0x29, 0xc2, //0x00000803 subq         %rax, %rdx
	0x48, 0x83, 0xfa, 0x02, //0x00000806 cmpq         $2, %rdx
	0x0f, 0x82, 0x34, 0x00, 0x00, 0x00, //0x0000080a jb           LBB0_65
	0x0f, 0xb6, 0x30, //0x00000810 movzbl       (%rax), %esi
	0x0f, 0xb6, 0x48, 0x01, //0x00000813 movzbl       $1(%rax), %ecx
	0x40, 0x88, 0x74, 0x24, 0x20, //0x00000817 movb         %sil, $32(%rsp)
	0x48, 0x8d, 0x78, 0x02, //0x0000081c leaq         $2(%rax), %rdi
	0x48, 0x83, 0xc2, 0xfe, //0x00000820 addq         $-2, %rdx
	0x4c, 0x89, 0xdb, //0x00000824 movq         %r11, %rbx
	0x48, 0x85, 0xd2, //0x00000827 testq        %rdx, %rdx
	0x0f, 0x84, 0x27, 0x00, 0x00, 0x00, //0x0000082a je           LBB0_66
	//0x00000830 LBB0_64
	0x0f, 0xb6, 0x17, //0x00000830 movzbl       (%rdi), %edx
	0x88, 0x13, //0x00000833 movb         %dl, (%rbx)
	0x0f, 0xb6, 0x74, 0x24, 0x20, //0x00000835 movzbl       $32(%rsp), %esi
	0x0f, 0xb6, 0x54, 0x24, 0x1e, //0x0000083a movzbl       $30(%rsp), %edx
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x0000083f jmp          LBB0_67
	//0x00000844 LBB0_65
	0x31, 0xf6, //0x00000844 xorl         %esi, %esi
	0x31, 0xc9, //0x00000846 xorl         %ecx, %ecx
	0x4c, 0x89, 0xc3, //0x00000848 movq         %r8, %rbx
	0x48, 0x89, 0xc7, //0x0000084b movq         %rax, %rdi
	0x48, 0x85, 0xd2, //0x0000084e testq        %rdx, %rdx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00000851 jne          LBB0_64
	//0x00000857 LBB0_66
	0x31, 0xd2, //0x00000857 xorl         %edx, %edx
	//0x00000859 LBB0_67
	0x0f, 0xb6, 0xd2, //0x00000859 movzbl       %dl, %edx
	0xc1, 0xe2, 0x10, //0x0000085c shll         $16, %edx
	0x0f, 0xb6, 0xf9, //0x0000085f movzbl       %cl, %edi
	0xc1, 0xe7, 0x08, //0x00000862 shll         $8, %edi
	0x09, 0xd7, //0x00000865 orl          %edx, %edi
	0x40, 0x0f, 0xb6, 0xce, //0x00000867 movzbl       %sil, %ecx
	0x09, 0xf9, //0x0000086b orl          %edi, %ecx
	0x89, 0xca, //0x0000086d movl         %ecx, %edx
	0x81, 0xe2, 0xf0, 0xc0, 0xc0, 0x00, //0x0000086f andl         $12632304, %edx
	0x81, 0xfa, 0xe0, 0x80, 0x80, 0x00, //0x00000875 cmpl         $8421600, %edx
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x0000087b jne          LBB0_70
	0x89, 0xcf, //0x00000881 movl         %ecx, %edi
	0x81, 0xe7, 0x0f, 0x20, 0x00, 0x00, //0x00000883 andl         $8207, %edi
	0x81, 0xff, 0x0d, 0x20, 0x00, 0x00, //0x00000889 cmpl         $8205, %edi
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x0000088f je           LBB0_70
	0xba, 0x03, 0x00, 0x00, 0x00, //0x00000895 movl         $3, %edx
	0x85, 0xff, //0x0000089a testl        %edi, %edi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x0000089c jne          LBB0_72
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008a2 .p2align 4, 0x90
	//0x000008b0 LBB0_70
	0x40, 0xf6, 0xc6, 0x1e, //0x000008b0 testb        $30, %sil
	0x0f, 0x84, 0xf6, 0xfe, 0xff, 0xff, //0x000008b4 je           LBB0_57
	0x81, 0xe1, 0xe0, 0xc0, 0x00, 0x00, //0x000008ba andl         $49376, %ecx
	0xba, 0x02, 0x00, 0x00, 0x00, //0x000008c0 movl         $2, %edx
	0x81, 0xf9, 0xc0, 0x80, 0x00, 0x00, //0x000008c5 cmpl         $32960, %ecx
	0x0f, 0x85, 0xdf, 0xfe, 0xff, 0xff, //0x000008cb jne          LBB0_57
	//0x000008d1 LBB0_72
	0x48, 0x01, 0xd0, //0x000008d1 addq         %rdx, %rax
	0x4c, 0x39, 0xd0, //0x000008d4 cmpq         %r10, %rax
	0x0f, 0x82, 0x10, 0xff, 0xff, 0xff, //0x000008d7 jb           LBB0_61
	//0x000008dd LBB0_28
	0x31, 0xc0, //0x000008dd xorl         %eax, %eax
	0x48, 0x8d, 0x65, 0xf8, //0x000008df leaq         $-8(%rbp), %rsp
	0x5b, //0x000008e3 popq         %rbx
	0x5d, //0x000008e4 popq         %rbp
	0xc5, 0xf8, 0x77, //0x000008e5 vzeroupper   
	0xc3, //0x000008e8 retq         
	//0x000008e9 LBB0_29
	0x48, 0x81, 0xff, 0x80, 0x00, 0x00, 0x00, //0x000008e9 cmpq         $128, %rdi
	0x0f, 0x83, 0x07, 0x00, 0x00, 0x00, //0x000008f0 jae          LBB0_31
	0x31, 0xf6, //0x000008f6 xorl         %esi, %esi
	0xe9, 0x14, 0x01, 0x00, 0x00, //0x000008f8 jmp          LBB0_39
	//0x000008fd LBB0_31
	0x48, 0x89, 0xfe, //0x000008fd movq         %rdi, %rsi
	0x48, 0x83, 0xe6, 0x80, //0x00000900 andq         $-128, %rsi
	0x48, 0x8d, 0x4e, 0x80, //0x00000904 leaq         $-128(%rsi), %rcx
	0x49, 0x89, 0xc8, //0x00000908 movq         %rcx, %r8
	0x49, 0xc1, 0xe8, 0x07, //0x0000090b shrq         $7, %r8
	0x49, 0x83, 0xc0, 0x01, //0x0000090f addq         $1, %r8
	0x48, 0x85, 0xc9, //0x00000913 testq        %rcx, %rcx
	0x0f, 0x84, 0xa4, 0x00, 0x00, 0x00, //0x00000916 je           LBB0_34
	0x4c, 0x89, 0xc2, //0x0000091c movq         %r8, %rdx
	0x48, 0x83, 0xe2, 0xfe, //0x0000091f andq         $-2, %rdx
	0x31, 0xc9, //0x00000923 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000925 .p2align 4, 0x90
	//0x00000930 LBB0_33
	0xc5, 0xfc, 0x10, 0x1c, 0x08, //0x00000930 vmovups      (%rax,%rcx), %ymm3
	0xc5, 0xfc, 0x10, 0x64, 0x08, 0x20, //0x00000935 vmovups      $32(%rax,%rcx), %ymm4
	0xc5, 0xfc, 0x10, 0x6c, 0x08, 0x40, //0x0000093b vmovups      $64(%rax,%rcx), %ymm5
	0xc5, 0xfc, 0x10, 0x74, 0x08, 0x60, //0x00000941 vmovups      $96(%rax,%rcx), %ymm6
	0xc5, 0xfc, 0x11, 0x5c, 0x0c, 0x20, //0x00000947 vmovups      %ymm3, $32(%rsp,%rcx)
	0xc5, 0xfc, 0x11, 0x64, 0x0c, 0x40, //0x0000094d vmovups      %ymm4, $64(%rsp,%rcx)
	0xc5, 0xfc, 0x11, 0x6c, 0x0c, 0x60, //0x00000953 vmovups      %ymm5, $96(%rsp,%rcx)
	0xc5, 0xfc, 0x11, 0xb4, 0x0c, 0x80, 0x00, 0x00, 0x00, //0x00000959 vmovups      %ymm6, $128(%rsp,%rcx)
	0xc5, 0xfe, 0x6f, 0x9c, 0x08, 0x80, 0x00, 0x00, 0x00, //0x00000962 vmovdqu      $128(%rax,%rcx), %ymm3
	0xc5, 0xfe, 0x6f, 0xa4, 0x08, 0xa0, 0x00, 0x00, 0x00, //0x0000096b vmovdqu      $160(%rax,%rcx), %ymm4
	0xc5, 0xfe, 0x6f, 0xac, 0x08, 0xc0, 0x00, 0x00, 0x00, //0x00000974 vmovdqu      $192(%rax,%rcx), %ymm5
	0xc5, 0xfe, 0x6f, 0xb4, 0x08, 0xe0, 0x00, 0x00, 0x00, //0x0000097d vmovdqu      $224(%rax,%rcx), %ymm6
	0xc5, 0xfe, 0x7f, 0x9c, 0x0c, 0xa0, 0x00, 0x00, 0x00, //0x00000986 vmovdqu      %ymm3, $160(%rsp,%rcx)
	0xc5, 0xfe, 0x7f, 0xa4, 0x0c, 0xc0, 0x00, 0x00, 0x00, //0x0000098f vmovdqu      %ymm4, $192(%rsp,%rcx)
	0xc5, 0xfe, 0x7f, 0xac, 0x0c, 0xe0, 0x00, 0x00, 0x00, //0x00000998 vmovdqu      %ymm5, $224(%rsp,%rcx)
	0xc5, 0xfe, 0x7f, 0xb4, 0x0c, 0x00, 0x01, 0x00, 0x00, //0x000009a1 vmovdqu      %ymm6, $256(%rsp,%rcx)
	0x48, 0x81, 0xc1, 0x00, 0x01, 0x00, 0x00, //0x000009aa addq         $256, %rcx
	0x48, 0x83, 0xc2, 0xfe, //0x000009b1 addq         $-2, %rdx
	0x0f, 0x85, 0x75, 0xff, 0xff, 0xff, //0x000009b5 jne          LBB0_33
	0xe9, 0x02, 0x00, 0x00, 0x00, //0x000009bb jmp          LBB0_35
	//0x000009c0 LBB0_34
	0x31, 0xc9, //0x000009c0 xorl         %ecx, %ecx
	//0x000009c2 LBB0_35
	0x41, 0xf6, 0xc0, 0x01, //0x000009c2 testb        $1, %r8b
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x000009c6 je           LBB0_37
	0xc5, 0xfe, 0x6f, 0x1c, 0x08, //0x000009cc vmovdqu      (%rax,%rcx), %ymm3
	0xc5, 0xfe, 0x6f, 0x64, 0x08, 0x20, //0x000009d1 vmovdqu      $32(%rax,%rcx), %ymm4
	0xc5, 0xfe, 0x6f, 0x6c, 0x08, 0x40, //0x000009d7 vmovdqu      $64(%rax,%rcx), %ymm5
	0xc5, 0xfe, 0x6f, 0x74, 0x08, 0x60, //0x000009dd vmovdqu      $96(%rax,%rcx), %ymm6
	0xc5, 0xfe, 0x7f, 0x5c, 0x0c, 0x20, //0x000009e3 vmovdqu      %ymm3, $32(%rsp,%rcx)
	0xc5, 0xfe, 0x7f, 0x64, 0x0c, 0x40, //0x000009e9 vmovdqu      %ymm4, $64(%rsp,%rcx)
	0xc5, 0xfe, 0x7f, 0x6c, 0x0c, 0x60, //0x000009ef vmovdqu      %ymm5, $96(%rsp,%rcx)
	0xc5, 0xfe, 0x7f, 0xb4, 0x0c, 0x80, 0x00, 0x00, 0x00, //0x000009f5 vmovdqu      %ymm6, $128(%rsp,%rcx)
	//0x000009fe LBB0_37
	0x48, 0x39, 0xf7, //0x000009fe cmpq         %rsi, %rdi
	0x0f, 0x84, 0x9d, 0xfc, 0xff, 0xff, //0x00000a01 je           LBB0_24
	0x40, 0xf6, 0xc7, 0x70, //0x00000a07 testb        $112, %dil
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00000a0b je           LBB0_42
	//0x00000a11 LBB0_39
	0x48, 0x89, 0xf2, //0x00000a11 movq         %rsi, %rdx
	0x48, 0x89, 0xfe, //0x00000a14 movq         %rdi, %rsi
	0x48, 0x83, 0xe6, 0xf0, //0x00000a17 andq         $-16, %rsi
	0x48, 0x8d, 0x0c, 0x30, //0x00000a1b leaq         (%rax,%rsi), %rcx
	0x90, //0x00000a1f .p2align 4, 0x90
	//0x00000a20 LBB0_40
	0xc5, 0xfa, 0x6f, 0x1c, 0x10, //0x00000a20 vmovdqu      (%rax,%rdx), %xmm3
	0xc5, 0xfa, 0x7f, 0x5c, 0x14, 0x20, //0x00000a25 vmovdqu      %xmm3, $32(%rsp,%rdx)
	0x48, 0x83, 0xc2, 0x10, //0x00000a2b addq         $16, %rdx
	0x48, 0x39, 0xd6, //0x00000a2f cmpq         %rdx, %rsi
	0x0f, 0x85, 0xe8, 0xff, 0xff, 0xff, //0x00000a32 jne          LBB0_40
	0x48, 0x39, 0xf7, //0x00000a38 cmpq         %rsi, %rdi
	0x0f, 0x85, 0x2f, 0xfc, 0xff, 0xff, //0x00000a3b jne          LBB0_22
	0xe9, 0x5e, 0xfc, 0xff, 0xff, //0x00000a41 jmp          LBB0_24
	//0x00000a46 LBB0_42
	0x48, 0x8d, 0x0c, 0x30, //0x00000a46 leaq         (%rax,%rsi), %rcx
	0xe9, 0x21, 0xfc, 0xff, 0xff, //0x00000a4a jmp          LBB0_22
	//0x00000a4f LBB0_43
	0xc4, 0xe3, 0x6d, 0x46, 0xd4, 0x21, //0x00000a4f vperm2i128   $33, %ymm4, %ymm2, %ymm2
	0xc4, 0xe3, 0x5d, 0x0f, 0xea, 0x0f, //0x00000a55 vpalignr     $15, %ymm2, %ymm4, %ymm5
	0xc5, 0xcd, 0x71, 0xd5, 0x04, //0x00000a5b vpsrlw       $4, %ymm5, %ymm6
	0xc5, 0xfe, 0x6f, 0x0d, 0x98, 0xf5, 0xff, 0xff, //0x00000a60 vmovdqu      $-2664(%rip), %ymm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xcd, 0xdb, 0xf1, //0x00000a68 vpand        %ymm1, %ymm6, %ymm6
	0xc5, 0xfe, 0x6f, 0x3d, 0xac, 0xf5, 0xff, 0xff, //0x00000a6c vmovdqu      $-2644(%rip), %ymm7  /* LCPI0_1+0(%rip) */
	0xc4, 0xe2, 0x45, 0x00, 0xf6, //0x00000a74 vpshufb      %ymm6, %ymm7, %ymm6
	0xc5, 0xd5, 0xdb, 0xe9, //0x00000a79 vpand        %ymm1, %ymm5, %ymm5
	0xc5, 0x7e, 0x6f, 0x05, 0xbb, 0xf5, 0xff, 0xff, //0x00000a7d vmovdqu      $-2629(%rip), %ymm8  /* LCPI0_2+0(%rip) */
	0xc4, 0xe2, 0x3d, 0x00, 0xed, //0x00000a85 vpshufb      %ymm5, %ymm8, %ymm5
	0xc5, 0xb5, 0x71, 0xd4, 0x04, //0x00000a8a vpsrlw       $4, %ymm4, %ymm9
	0xc5, 0x35, 0xdb, 0xc9, //0x00000a8f vpand        %ymm1, %ymm9, %ymm9
	0xc5, 0x7e, 0x6f, 0x15, 0xc5, 0xf5, 0xff, 0xff, //0x00000a93 vmovdqu      $-2619(%rip), %ymm10  /* LCPI0_3+0(%rip) */
	0xc4, 0x42, 0x2d, 0x00, 0xc9, //0x00000a9b vpshufb      %ymm9, %ymm10, %ymm9
	0xc5, 0xb5, 0xdb, 0xed, //0x00000aa0 vpand        %ymm5, %ymm9, %ymm5
	0xc5, 0xcd, 0xdb, 0xed, //0x00000aa4 vpand        %ymm5, %ymm6, %ymm5
	0xc4, 0xe3, 0x5d, 0x0f, 0xf2, 0x0e, //0x00000aa8 vpalignr     $14, %ymm2, %ymm4, %ymm6
	0xc4, 0xe3, 0x5d, 0x0f, 0xd2, 0x0d, //0x00000aae vpalignr     $13, %ymm2, %ymm4, %ymm2
	0xc5, 0x7e, 0x6f, 0x0d, 0xc4, 0xf5, 0xff, 0xff, //0x00000ab4 vmovdqu      $-2620(%rip), %ymm9  /* LCPI0_4+0(%rip) */
	0xc4, 0xc1, 0x4d, 0xd8, 0xf1, //0x00000abc vpsubusb     %ymm9, %ymm6, %ymm6
	0xc5, 0x7e, 0x6f, 0x1d, 0xd7, 0xf5, 0xff, 0xff, //0x00000ac1 vmovdqu      $-2601(%rip), %ymm11  /* LCPI0_5+0(%rip) */
	0xc4, 0xc1, 0x6d, 0xd8, 0xd3, //0x00000ac9 vpsubusb     %ymm11, %ymm2, %ymm2
	0xc5, 0xed, 0xeb, 0xd6, //0x00000ace vpor         %ymm6, %ymm2, %ymm2
	0xc5, 0xc9, 0xef, 0xf6, //0x00000ad2 vpxor        %xmm6, %xmm6, %xmm6
	0xc5, 0xed, 0x74, 0xd6, //0x00000ad6 vpcmpeqb     %ymm6, %ymm2, %ymm2
	0xc4, 0x62, 0x7d, 0x59, 0x25, 0x1d, 0xf6, 0xff, 0xff, //0x00000ada vpbroadcastq $-2531(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc4, 0xc1, 0x6d, 0xdf, 0xd4, //0x00000ae3 vpandn       %ymm12, %ymm2, %ymm2
	0xc5, 0xed, 0xef, 0xd5, //0x00000ae8 vpxor        %ymm5, %ymm2, %ymm2
	0xc4, 0xe3, 0x5d, 0x46, 0xe3, 0x21, //0x00000aec vperm2i128   $33, %ymm3, %ymm4, %ymm4
	0xc4, 0xe3, 0x65, 0x0f, 0xec, 0x0f, //0x00000af2 vpalignr     $15, %ymm4, %ymm3, %ymm5
	0xc5, 0x95, 0x71, 0xd5, 0x04, //0x00000af8 vpsrlw       $4, %ymm5, %ymm13
	0xc5, 0x15, 0xdb, 0xe9, //0x00000afd vpand        %ymm1, %ymm13, %ymm13
	0xc4, 0xc2, 0x45, 0x00, 0xfd, //0x00000b01 vpshufb      %ymm13, %ymm7, %ymm7
	0xc5, 0xd5, 0xdb, 0xe9, //0x00000b06 vpand        %ymm1, %ymm5, %ymm5
	0xc4, 0xe2, 0x3d, 0x00, 0xed, //0x00000b0a vpshufb      %ymm5, %ymm8, %ymm5
	0xc5, 0xbd, 0x71, 0xd3, 0x04, //0x00000b0f vpsrlw       $4, %ymm3, %ymm8
	0xc5, 0xbd, 0xdb, 0xc9, //0x00000b14 vpand        %ymm1, %ymm8, %ymm1
	0xc4, 0xe2, 0x2d, 0x00, 0xc9, //0x00000b18 vpshufb      %ymm1, %ymm10, %ymm1
	0xc5, 0xd5, 0xdb, 0xc9, //0x00000b1d vpand        %ymm1, %ymm5, %ymm1
	0xc5, 0xc5, 0xdb, 0xc9, //0x00000b21 vpand        %ymm1, %ymm7, %ymm1
	0xc4, 0xe3, 0x65, 0x0f, 0xec, 0x0e, //0x00000b25 vpalignr     $14, %ymm4, %ymm3, %ymm5
	0xc4, 0xe3, 0x65, 0x0f, 0xe4, 0x0d, //0x00000b2b vpalignr     $13, %ymm4, %ymm3, %ymm4
	0xc4, 0xc1, 0x55, 0xd8, 0xe9, //0x00000b31 vpsubusb     %ymm9, %ymm5, %ymm5
	0xc4, 0xc1, 0x5d, 0xd8, 0xe3, //0x00000b36 vpsubusb     %ymm11, %ymm4, %ymm4
	0xc5, 0xdd, 0xeb, 0xe5, //0x00000b3b vpor         %ymm5, %ymm4, %ymm4
	0xc5, 0xdd, 0x74, 0xe6, //0x00000b3f vpcmpeqb     %ymm6, %ymm4, %ymm4
	0xc4, 0xc1, 0x5d, 0xdf, 0xe4, //0x00000b43 vpandn       %ymm12, %ymm4, %ymm4
	0xc5, 0xdd, 0xef, 0xc9, //0x00000b48 vpxor        %ymm1, %ymm4, %ymm1
	0xc5, 0xed, 0xeb, 0xc0, //0x00000b4c vpor         %ymm0, %ymm2, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x00000b50 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xe5, 0xd8, 0x0d, 0x64, 0xf5, 0xff, 0xff, //0x00000b54 vpsubusb     $-2716(%rip), %ymm3, %ymm1  /* LCPI0_7+0(%rip) */
	0xc5, 0xfd, 0xeb, 0xc1, //0x00000b5c vpor         %ymm1, %ymm0, %ymm0
	0xc4, 0xe2, 0x7d, 0x17, 0xc0, //0x00000b60 vptest       %ymm0, %ymm0
	0x0f, 0x84, 0x72, 0xfd, 0xff, 0xff, //0x00000b65 je           LBB0_28
	0xe9, 0x63, 0xfb, 0xff, 0xff, //0x00000b6b jmp          LBB0_44
	//0x00000b70 .p2align 2, 0x00
	//0x00000b70 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000b70 .long 2
}
 
