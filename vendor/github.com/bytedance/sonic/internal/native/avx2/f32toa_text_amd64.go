// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_f32toa = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, // QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, //0x00000010 QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	//0x00000020 .p2align 4, 0x00
	//0x00000020 LCPI0_1
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, //0x00000020 QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	//0x00000030 .p2align 4, 0x90
	//0x00000030 _f32toa
	0x55, //0x00000030 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000031 movq         %rsp, %rbp
	0x41, 0x57, //0x00000034 pushq        %r15
	0x41, 0x56, //0x00000036 pushq        %r14
	0x41, 0x55, //0x00000038 pushq        %r13
	0x41, 0x54, //0x0000003a pushq        %r12
	0x53, //0x0000003c pushq        %rbx
	0x50, //0x0000003d pushq        %rax
	0xc5, 0xf9, 0x7e, 0xc0, //0x0000003e vmovd        %xmm0, %eax
	0x89, 0xc1, //0x00000042 movl         %eax, %ecx
	0xc1, 0xe9, 0x17, //0x00000044 shrl         $23, %ecx
	0x0f, 0xb6, 0xd1, //0x00000047 movzbl       %cl, %edx
	0x81, 0xfa, 0xff, 0x00, 0x00, 0x00, //0x0000004a cmpl         $255, %edx
	0x0f, 0x84, 0x72, 0x0e, 0x00, 0x00, //0x00000050 je           LBB0_148
	0xc6, 0x07, 0x2d, //0x00000056 movb         $45, (%rdi)
	0x41, 0x89, 0xc1, //0x00000059 movl         %eax, %r9d
	0x41, 0xc1, 0xe9, 0x1f, //0x0000005c shrl         $31, %r9d
	0x4e, 0x8d, 0x2c, 0x0f, //0x00000060 leaq         (%rdi,%r9), %r13
	0xa9, 0xff, 0xff, 0xff, 0x7f, //0x00000064 testl        $2147483647, %eax
	0x0f, 0x84, 0xc9, 0x01, 0x00, 0x00, //0x00000069 je           LBB0_6
	0x25, 0xff, 0xff, 0x7f, 0x00, //0x0000006f andl         $8388607, %eax
	0x85, 0xd2, //0x00000074 testl        %edx, %edx
	0x0f, 0x84, 0x53, 0x0e, 0x00, 0x00, //0x00000076 je           LBB0_149
	0x44, 0x8d, 0xb8, 0x00, 0x00, 0x80, 0x00, //0x0000007c leal         $8388608(%rax), %r15d
	0x44, 0x8d, 0x82, 0x6a, 0xff, 0xff, 0xff, //0x00000083 leal         $-150(%rdx), %r8d
	0x8d, 0x4a, 0x81, //0x0000008a leal         $-127(%rdx), %ecx
	0x83, 0xf9, 0x17, //0x0000008d cmpl         $23, %ecx
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x00000090 ja           LBB0_7
	0xb9, 0x96, 0x00, 0x00, 0x00, //0x00000096 movl         $150, %ecx
	0x29, 0xd1, //0x0000009b subl         %edx, %ecx
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000009d movq         $-1, %rbx
	0x48, 0xd3, 0xe3, //0x000000a4 shlq         %cl, %rbx
	0xf7, 0xd3, //0x000000a7 notl         %ebx
	0x44, 0x85, 0xfb, //0x000000a9 testl        %r15d, %ebx
	0x0f, 0x84, 0x3f, 0x04, 0x00, 0x00, //0x000000ac je           LBB0_34
	//0x000000b2 LBB0_7
	0x48, 0x89, 0x7d, 0xd0, //0x000000b2 movq         %rdi, $-48(%rbp)
	//0x000000b6 LBB0_8
	0x45, 0x89, 0xfe, //0x000000b6 movl         %r15d, %r14d
	0x41, 0x83, 0xe6, 0x01, //0x000000b9 andl         $1, %r14d
	0x85, 0xc0, //0x000000bd testl        %eax, %eax
	0x0f, 0x94, 0xc0, //0x000000bf sete         %al
	0x83, 0xfa, 0x02, //0x000000c2 cmpl         $2, %edx
	0x0f, 0x93, 0xc1, //0x000000c5 setae        %cl
	0x20, 0xc1, //0x000000c8 andb         %al, %cl
	0x0f, 0xb6, 0xc9, //0x000000ca movzbl       %cl, %ecx
	0x45, 0x89, 0xfa, //0x000000cd movl         %r15d, %r10d
	0x41, 0xc1, 0xe2, 0x02, //0x000000d0 shll         $2, %r10d
	0x42, 0x8d, 0x04, 0xb9, //0x000000d4 leal         (%rcx,%r15,4), %eax
	0x83, 0xc0, 0xfe, //0x000000d8 addl         $-2, %eax
	0x41, 0x69, 0xd0, 0x13, 0x44, 0x13, 0x00, //0x000000db imull        $1262611, %r8d, %edx
	0x44, 0x8d, 0x9a, 0x01, 0x01, 0xf8, 0xff, //0x000000e2 leal         $-524031(%rdx), %r11d
	0x84, 0xc9, //0x000000e9 testb        %cl, %cl
	0x44, 0x0f, 0x44, 0xda, //0x000000eb cmovel       %edx, %r11d
	0x41, 0xc1, 0xfb, 0x16, //0x000000ef sarl         $22, %r11d
	0x41, 0x69, 0xcb, 0xb1, 0x6c, 0xe5, 0xff, //0x000000f3 imull        $-1741647, %r11d, %ecx
	0xc1, 0xe9, 0x13, //0x000000fa shrl         $19, %ecx
	0x44, 0x01, 0xc1, //0x000000fd addl         %r8d, %ecx
	0xba, 0x1f, 0x00, 0x00, 0x00, //0x00000100 movl         $31, %edx
	0x44, 0x29, 0xda, //0x00000105 subl         %r11d, %edx
	0x80, 0xc1, 0x01, //0x00000108 addb         $1, %cl
	0xd3, 0xe0, //0x0000010b shll         %cl, %eax
	0x48, 0x8d, 0x3d, 0xac, 0x0e, 0x00, 0x00, //0x0000010d leaq         $3756(%rip), %rdi  /* _pow10_ceil_sig_f32.g+0(%rip) */
	0x4c, 0x8b, 0x24, 0xd7, //0x00000114 movq         (%rdi,%rdx,8), %r12
	0x49, 0xf7, 0xe4, //0x00000118 mulq         %r12
	0x46, 0x8d, 0x04, 0xbd, 0x02, 0x00, 0x00, 0x00, //0x0000011b leal         $2(,%r15,4), %r8d
	0x31, 0xf6, //0x00000123 xorl         %esi, %esi
	0x48, 0xc1, 0xe8, 0x21, //0x00000125 shrq         $33, %rax
	0x40, 0x0f, 0x95, 0xc6, //0x00000129 setne        %sil
	0x09, 0xd6, //0x0000012d orl          %edx, %esi
	0x41, 0xd3, 0xe2, //0x0000012f shll         %cl, %r10d
	0x4c, 0x89, 0xd0, //0x00000132 movq         %r10, %rax
	0x49, 0xf7, 0xe4, //0x00000135 mulq         %r12
	0x49, 0x89, 0xd2, //0x00000138 movq         %rdx, %r10
	0x45, 0x31, 0xff, //0x0000013b xorl         %r15d, %r15d
	0x48, 0xc1, 0xe8, 0x21, //0x0000013e shrq         $33, %rax
	0x41, 0x0f, 0x95, 0xc7, //0x00000142 setne        %r15b
	0x41, 0xd3, 0xe0, //0x00000146 shll         %cl, %r8d
	0x4c, 0x89, 0xc0, //0x00000149 movq         %r8, %rax
	0x49, 0xf7, 0xe4, //0x0000014c mulq         %r12
	0x45, 0x09, 0xd7, //0x0000014f orl          %r10d, %r15d
	0x31, 0xc9, //0x00000152 xorl         %ecx, %ecx
	0x48, 0xc1, 0xe8, 0x21, //0x00000154 shrq         $33, %rax
	0x0f, 0x95, 0xc1, //0x00000158 setne        %cl
	0x09, 0xd1, //0x0000015b orl          %edx, %ecx
	0x44, 0x01, 0xf6, //0x0000015d addl         %r14d, %esi
	0x44, 0x29, 0xf1, //0x00000160 subl         %r14d, %ecx
	0x41, 0x83, 0xff, 0x28, //0x00000163 cmpl         $40, %r15d
	0x0f, 0x82, 0x41, 0x00, 0x00, 0x00, //0x00000167 jb           LBB0_10
	0x44, 0x89, 0xd0, //0x0000016d movl         %r10d, %eax
	0xba, 0xcd, 0xcc, 0xcc, 0xcc, //0x00000170 movl         $3435973837, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x00000175 imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x25, //0x00000179 shrq         $37, %rdx
	0x89, 0xf0, //0x0000017d movl         %esi, %eax
	0x48, 0x8d, 0x3c, 0xd5, 0x00, 0x00, 0x00, 0x00, //0x0000017f leaq         (,%rdx,8), %rdi
	0x48, 0x8d, 0x1c, 0xbf, //0x00000187 leaq         (%rdi,%rdi,4), %rbx
	0x48, 0x39, 0xc3, //0x0000018b cmpq         %rax, %rbx
	0x41, 0x0f, 0x92, 0xc6, //0x0000018e setb         %r14b
	0x48, 0x8d, 0x3c, 0xbf, //0x00000192 leaq         (%rdi,%rdi,4), %rdi
	0x48, 0x83, 0xc7, 0x28, //0x00000196 addq         $40, %rdi
	0x89, 0xcb, //0x0000019a movl         %ecx, %ebx
	0x31, 0xc0, //0x0000019c xorl         %eax, %eax
	0x48, 0x39, 0xdf, //0x0000019e cmpq         %rbx, %rdi
	0x41, 0x0f, 0x96, 0xc0, //0x000001a1 setbe        %r8b
	0x45, 0x38, 0xc6, //0x000001a5 cmpb         %r8b, %r14b
	0x0f, 0x84, 0xf2, 0x01, 0x00, 0x00, //0x000001a8 je           LBB0_18
	//0x000001ae LBB0_10
	0x45, 0x89, 0xd0, //0x000001ae movl         %r10d, %r8d
	0x41, 0xc1, 0xe8, 0x02, //0x000001b1 shrl         $2, %r8d
	0x44, 0x89, 0xd2, //0x000001b5 movl         %r10d, %edx
	0x83, 0xe2, 0xfc, //0x000001b8 andl         $-4, %edx
	0x39, 0xd6, //0x000001bb cmpl         %edx, %esi
	0x40, 0x0f, 0x97, 0xc6, //0x000001bd seta         %sil
	0x8d, 0x42, 0x04, //0x000001c1 leal         $4(%rdx), %eax
	0x39, 0xc8, //0x000001c4 cmpl         %ecx, %eax
	0x0f, 0x96, 0xc3, //0x000001c6 setbe        %bl
	0x40, 0x30, 0xf3, //0x000001c9 xorb         %sil, %bl
	0x0f, 0x84, 0x7a, 0x00, 0x00, 0x00, //0x000001cc je           LBB0_14
	0x83, 0xca, 0x02, //0x000001d2 orl          $2, %edx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000001d5 movl         $1, %eax
	0x41, 0x39, 0xd7, //0x000001da cmpl         %edx, %r15d
	0x0f, 0x87, 0x0e, 0x00, 0x00, 0x00, //0x000001dd ja           LBB0_13
	0x0f, 0x94, 0xc0, //0x000001e3 sete         %al
	0x41, 0xc0, 0xea, 0x02, //0x000001e6 shrb         $2, %r10b
	0x41, 0x20, 0xc2, //0x000001ea andb         %al, %r10b
	0x41, 0x0f, 0xb6, 0xc2, //0x000001ed movzbl       %r10b, %eax
	//0x000001f1 LBB0_13
	0x44, 0x01, 0xc0, //0x000001f1 addl         %r8d, %eax
	0x4d, 0x89, 0xef, //0x000001f4 movq         %r13, %r15
	0x3d, 0xa0, 0x86, 0x01, 0x00, //0x000001f7 cmpl         $100000, %eax
	0x4c, 0x8b, 0x6d, 0xd0, //0x000001fc movq         $-48(%rbp), %r13
	0x0f, 0x82, 0x61, 0x00, 0x00, 0x00, //0x00000200 jb           LBB0_19
	//0x00000206 LBB0_15
	0x41, 0xbc, 0x06, 0x00, 0x00, 0x00, //0x00000206 movl         $6, %r12d
	0x3d, 0x40, 0x42, 0x0f, 0x00, //0x0000020c cmpl         $1000000, %eax
	0x0f, 0x82, 0x8e, 0x00, 0x00, 0x00, //0x00000211 jb           LBB0_24
	0x41, 0xbc, 0x07, 0x00, 0x00, 0x00, //0x00000217 movl         $7, %r12d
	0x3d, 0x80, 0x96, 0x98, 0x00, //0x0000021d cmpl         $10000000, %eax
	0x0f, 0x82, 0x7d, 0x00, 0x00, 0x00, //0x00000222 jb           LBB0_24
	0x3d, 0x00, 0xe1, 0xf5, 0x05, //0x00000228 cmpl         $100000000, %eax
	0x41, 0xbc, 0x09, 0x00, 0x00, 0x00, //0x0000022d movl         $9, %r12d
	0xe9, 0x69, 0x00, 0x00, 0x00, //0x00000233 jmp          LBB0_23
	//0x00000238 LBB0_6
	0x41, 0xc6, 0x45, 0x00, 0x30, //0x00000238 movb         $48, (%r13)
	0x41, 0x29, 0xfd, //0x0000023d subl         %edi, %r13d
	0x41, 0x83, 0xc5, 0x01, //0x00000240 addl         $1, %r13d
	0x44, 0x89, 0xe8, //0x00000244 movl         %r13d, %eax
	0xe9, 0x6a, 0x0c, 0x00, 0x00, //0x00000247 jmp          LBB0_147
	//0x0000024c LBB0_14
	0x39, 0xc1, //0x0000024c cmpl         %eax, %ecx
	0x41, 0x83, 0xd8, 0xff, //0x0000024e sbbl         $-1, %r8d
	0x44, 0x89, 0xc0, //0x00000252 movl         %r8d, %eax
	0x4d, 0x89, 0xef, //0x00000255 movq         %r13, %r15
	0x3d, 0xa0, 0x86, 0x01, 0x00, //0x00000258 cmpl         $100000, %eax
	0x4c, 0x8b, 0x6d, 0xd0, //0x0000025d movq         $-48(%rbp), %r13
	0x0f, 0x83, 0x9f, 0xff, 0xff, 0xff, //0x00000261 jae          LBB0_15
	//0x00000267 LBB0_19
	0x41, 0xbc, 0x01, 0x00, 0x00, 0x00, //0x00000267 movl         $1, %r12d
	0x83, 0xf8, 0x0a, //0x0000026d cmpl         $10, %eax
	0x0f, 0x82, 0x2f, 0x00, 0x00, 0x00, //0x00000270 jb           LBB0_24
	0x41, 0xbc, 0x02, 0x00, 0x00, 0x00, //0x00000276 movl         $2, %r12d
	0x83, 0xf8, 0x64, //0x0000027c cmpl         $100, %eax
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x0000027f jb           LBB0_24
	0x41, 0xbc, 0x03, 0x00, 0x00, 0x00, //0x00000285 movl         $3, %r12d
	0x3d, 0xe8, 0x03, 0x00, 0x00, //0x0000028b cmpl         $1000, %eax
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x00000290 jb           LBB0_24
	0x3d, 0x10, 0x27, 0x00, 0x00, //0x00000296 cmpl         $10000, %eax
	0x41, 0xbc, 0x05, 0x00, 0x00, 0x00, //0x0000029b movl         $5, %r12d
	//0x000002a1 LBB0_23
	0x41, 0x83, 0xdc, 0x00, //0x000002a1 sbbl         $0, %r12d
	//0x000002a5 LBB0_24
	0x47, 0x8d, 0x14, 0x1c, //0x000002a5 leal         (%r12,%r11), %r10d
	0x43, 0x8d, 0x0c, 0x1c, //0x000002a9 leal         (%r12,%r11), %ecx
	0x83, 0xc1, 0xea, //0x000002ad addl         $-22, %ecx
	0x83, 0xf9, 0xe4, //0x000002b0 cmpl         $-28, %ecx
	0x0f, 0x87, 0x78, 0x00, 0x00, 0x00, //0x000002b3 ja           LBB0_28
	0x44, 0x89, 0xe1, //0x000002b9 movl         %r12d, %ecx
	0x49, 0x8d, 0x14, 0x0f, //0x000002bc leaq         (%r15,%rcx), %rdx
	0x48, 0x83, 0xc2, 0x01, //0x000002c0 addq         $1, %rdx
	0x3d, 0x10, 0x27, 0x00, 0x00, //0x000002c4 cmpl         $10000, %eax
	0x0f, 0x82, 0xf1, 0x00, 0x00, 0x00, //0x000002c9 jb           LBB0_32
	0x89, 0xc7, //0x000002cf movl         %eax, %edi
	0xbe, 0x59, 0x17, 0xb7, 0xd1, //0x000002d1 movl         $3518437209, %esi
	0x48, 0x0f, 0xaf, 0xf7, //0x000002d6 imulq        %rdi, %rsi
	0x48, 0xc1, 0xee, 0x2d, //0x000002da shrq         $45, %rsi
	0x44, 0x69, 0xc6, 0xf0, 0xd8, 0xff, 0xff, //0x000002de imull        $-10000, %esi, %r8d
	0x41, 0x01, 0xc0, //0x000002e5 addl         %eax, %r8d
	0x0f, 0x84, 0xa7, 0x04, 0x00, 0x00, //0x000002e8 je           LBB0_64
	0x44, 0x89, 0xc0, //0x000002ee movl         %r8d, %eax
	0x48, 0x69, 0xc0, 0x1f, 0x85, 0xeb, 0x51, //0x000002f1 imulq        $1374389535, %rax, %rax
	0x48, 0xc1, 0xe8, 0x25, //0x000002f8 shrq         $37, %rax
	0x6b, 0xf8, 0x64, //0x000002fc imull        $100, %eax, %edi
	0x41, 0x29, 0xf8, //0x000002ff subl         %edi, %r8d
	0x48, 0x8d, 0x3d, 0xe7, 0x0b, 0x00, 0x00, //0x00000302 leaq         $3047(%rip), %rdi  /* _Digits+0(%rip) */
	0x42, 0x0f, 0xb7, 0x1c, 0x47, //0x00000309 movzwl       (%rdi,%r8,2), %ebx
	0x66, 0x89, 0x5a, 0xfe, //0x0000030e movw         %bx, $-2(%rdx)
	0x0f, 0xb7, 0x04, 0x47, //0x00000312 movzwl       (%rdi,%rax,2), %eax
	0x66, 0x89, 0x42, 0xfc, //0x00000316 movw         %ax, $-4(%rdx)
	0x45, 0x31, 0xc0, //0x0000031a xorl         %r8d, %r8d
	0x48, 0x83, 0xc2, 0xfc, //0x0000031d addq         $-4, %rdx
	0x83, 0xfe, 0x64, //0x00000321 cmpl         $100, %esi
	0x0f, 0x83, 0xa4, 0x00, 0x00, 0x00, //0x00000324 jae          LBB0_66
	//0x0000032a LBB0_33
	0x89, 0xf0, //0x0000032a movl         %esi, %eax
	0xe9, 0xde, 0x00, 0x00, 0x00, //0x0000032c jmp          LBB0_68
	//0x00000331 LBB0_28
	0x45, 0x89, 0xe0, //0x00000331 movl         %r12d, %r8d
	0x45, 0x85, 0xdb, //0x00000334 testl        %r11d, %r11d
	0x0f, 0x88, 0x54, 0x02, 0x00, 0x00, //0x00000337 js           LBB0_40
	0x4b, 0x8d, 0x14, 0x07, //0x0000033d leaq         (%r15,%r8), %rdx
	0x3d, 0x10, 0x27, 0x00, 0x00, //0x00000341 cmpl         $10000, %eax
	0x0f, 0x82, 0xb1, 0x02, 0x00, 0x00, //0x00000346 jb           LBB0_45
	0x89, 0xc1, //0x0000034c movl         %eax, %ecx
	0xbe, 0x59, 0x17, 0xb7, 0xd1, //0x0000034e movl         $3518437209, %esi
	0x48, 0x0f, 0xaf, 0xf1, //0x00000353 imulq        %rcx, %rsi
	0x48, 0xc1, 0xee, 0x2d, //0x00000357 shrq         $45, %rsi
	0x69, 0xce, 0xf0, 0xd8, 0xff, 0xff, //0x0000035b imull        $-10000, %esi, %ecx
	0x01, 0xc1, //0x00000361 addl         %eax, %ecx
	0x48, 0x69, 0xc1, 0x1f, 0x85, 0xeb, 0x51, //0x00000363 imulq        $1374389535, %rcx, %rax
	0x48, 0xc1, 0xe8, 0x25, //0x0000036a shrq         $37, %rax
	0x6b, 0xf8, 0x64, //0x0000036e imull        $100, %eax, %edi
	0x29, 0xf9, //0x00000371 subl         %edi, %ecx
	0x48, 0x8d, 0x3d, 0x76, 0x0b, 0x00, 0x00, //0x00000373 leaq         $2934(%rip), %rdi  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x4f, //0x0000037a movzwl       (%rdi,%rcx,2), %ecx
	0x66, 0x89, 0x4a, 0xfe, //0x0000037e movw         %cx, $-2(%rdx)
	0x48, 0x8d, 0x4a, 0xfc, //0x00000382 leaq         $-4(%rdx), %rcx
	0x0f, 0xb7, 0x04, 0x47, //0x00000386 movzwl       (%rdi,%rax,2), %eax
	0x66, 0x89, 0x42, 0xfc, //0x0000038a movw         %ax, $-4(%rdx)
	0x89, 0xf0, //0x0000038e movl         %esi, %eax
	0x83, 0xf8, 0x64, //0x00000390 cmpl         $100, %eax
	0x0f, 0x83, 0x70, 0x02, 0x00, 0x00, //0x00000393 jae          LBB0_46
	//0x00000399 LBB0_31
	0x89, 0xc6, //0x00000399 movl         %eax, %esi
	0xe9, 0xae, 0x02, 0x00, 0x00, //0x0000039b jmp          LBB0_48
	//0x000003a0 LBB0_18
	0x44, 0x88, 0xc0, //0x000003a0 movb         %r8b, %al
	0x01, 0xd0, //0x000003a3 addl         %edx, %eax
	0x41, 0x83, 0xc3, 0x01, //0x000003a5 addl         $1, %r11d
	0x4d, 0x89, 0xef, //0x000003a9 movq         %r13, %r15
	0x3d, 0xa0, 0x86, 0x01, 0x00, //0x000003ac cmpl         $100000, %eax
	0x4c, 0x8b, 0x6d, 0xd0, //0x000003b1 movq         $-48(%rbp), %r13
	0x0f, 0x83, 0x4b, 0xfe, 0xff, 0xff, //0x000003b5 jae          LBB0_15
	0xe9, 0xa7, 0xfe, 0xff, 0xff, //0x000003bb jmp          LBB0_19
	//0x000003c0 LBB0_32
	0x45, 0x31, 0xc0, //0x000003c0 xorl         %r8d, %r8d
	0x89, 0xc6, //0x000003c3 movl         %eax, %esi
	0x83, 0xfe, 0x64, //0x000003c5 cmpl         $100, %esi
	0x0f, 0x82, 0x5c, 0xff, 0xff, 0xff, //0x000003c8 jb           LBB0_33
	//0x000003ce LBB0_66
	0x48, 0x83, 0xc2, 0xff, //0x000003ce addq         $-1, %rdx
	0x4c, 0x8d, 0x1d, 0x17, 0x0b, 0x00, 0x00, //0x000003d2 leaq         $2839(%rip), %r11  /* _Digits+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003d9 .p2align 4, 0x90
	//0x000003e0 LBB0_67
	0x89, 0xf0, //0x000003e0 movl         %esi, %eax
	0x48, 0x69, 0xc0, 0x1f, 0x85, 0xeb, 0x51, //0x000003e2 imulq        $1374389535, %rax, %rax
	0x48, 0xc1, 0xe8, 0x25, //0x000003e9 shrq         $37, %rax
	0x6b, 0xf8, 0x64, //0x000003ed imull        $100, %eax, %edi
	0x89, 0xf3, //0x000003f0 movl         %esi, %ebx
	0x29, 0xfb, //0x000003f2 subl         %edi, %ebx
	0x41, 0x0f, 0xb7, 0x3c, 0x5b, //0x000003f4 movzwl       (%r11,%rbx,2), %edi
	0x66, 0x89, 0x7a, 0xff, //0x000003f9 movw         %di, $-1(%rdx)
	0x48, 0x83, 0xc2, 0xfe, //0x000003fd addq         $-2, %rdx
	0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x00000401 cmpl         $9999, %esi
	0x89, 0xc6, //0x00000407 movl         %eax, %esi
	0x0f, 0x87, 0xd1, 0xff, 0xff, 0xff, //0x00000409 ja           LBB0_67
	//0x0000040f LBB0_68
	0x49, 0x8d, 0x57, 0x01, //0x0000040f leaq         $1(%r15), %rdx
	0x83, 0xf8, 0x0a, //0x00000413 cmpl         $10, %eax
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x00000416 jb           LBB0_70
	0x89, 0xc6, //0x0000041c movl         %eax, %esi
	0x48, 0x8d, 0x3d, 0xcb, 0x0a, 0x00, 0x00, //0x0000041e leaq         $2763(%rip), %rdi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x77, //0x00000425 movb         (%rdi,%rsi,2), %al
	0x8a, 0x5c, 0x77, 0x01, //0x00000428 movb         $1(%rdi,%rsi,2), %bl
	0x41, 0x88, 0x47, 0x01, //0x0000042c movb         %al, $1(%r15)
	0x41, 0x88, 0x5f, 0x02, //0x00000430 movb         %bl, $2(%r15)
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00000434 jmp          LBB0_71
	//0x00000439 LBB0_70
	0x04, 0x30, //0x00000439 addb         $48, %al
	0x88, 0x02, //0x0000043b movb         %al, (%rdx)
	//0x0000043d LBB0_71
	0x4d, 0x29, 0xc1, //0x0000043d subq         %r8, %r9
	0x4d, 0x01, 0xe9, //0x00000440 addq         %r13, %r9
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00000443 movl         $1, %esi
	0x4c, 0x29, 0xc6, //0x00000448 subq         %r8, %rsi
	0x90, 0x90, 0x90, 0x90, 0x90, //0x0000044b .p2align 4, 0x90
	//0x00000450 LBB0_72
	0x48, 0x83, 0xc6, 0xff, //0x00000450 addq         $-1, %rsi
	0x41, 0x80, 0x3c, 0x09, 0x30, //0x00000454 cmpb         $48, (%r9,%rcx)
	0x4d, 0x8d, 0x49, 0xff, //0x00000459 leaq         $-1(%r9), %r9
	0x0f, 0x84, 0xed, 0xff, 0xff, 0xff, //0x0000045d je           LBB0_72
	0x41, 0x88, 0x07, //0x00000463 movb         %al, (%r15)
	0x48, 0x01, 0xce, //0x00000466 addq         %rcx, %rsi
	0x48, 0x83, 0xfe, 0x02, //0x00000469 cmpq         $2, %rsi
	0x0f, 0x8c, 0x46, 0x00, 0x00, 0x00, //0x0000046d jl           LBB0_77
	0x49, 0x8d, 0x04, 0x09, //0x00000473 leaq         (%r9,%rcx), %rax
	0x48, 0x83, 0xc0, 0x02, //0x00000477 addq         $2, %rax
	0xc6, 0x02, 0x2e, //0x0000047b movb         $46, (%rdx)
	0xc6, 0x00, 0x65, //0x0000047e movb         $101, (%rax)
	0x45, 0x85, 0xd2, //0x00000481 testl        %r10d, %r10d
	0x0f, 0x8e, 0x43, 0x00, 0x00, 0x00, //0x00000484 jle          LBB0_78
	//0x0000048a LBB0_75
	0x41, 0x83, 0xc2, 0xff, //0x0000048a addl         $-1, %r10d
	0xc6, 0x40, 0x01, 0x2b, //0x0000048e movb         $43, $1(%rax)
	0x44, 0x89, 0xd1, //0x00000492 movl         %r10d, %ecx
	0x83, 0xf9, 0x0a, //0x00000495 cmpl         $10, %ecx
	0x0f, 0x82, 0x44, 0x00, 0x00, 0x00, //0x00000498 jb           LBB0_79
	//0x0000049e LBB0_76
	0x48, 0x63, 0xc9, //0x0000049e movslq       %ecx, %rcx
	0x48, 0x8d, 0x15, 0x48, 0x0a, 0x00, 0x00, //0x000004a1 leaq         $2632(%rip), %rdx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x4a, //0x000004a8 movzwl       (%rdx,%rcx,2), %ecx
	0x66, 0x89, 0x48, 0x02, //0x000004ac movw         %cx, $2(%rax)
	0x48, 0x83, 0xc0, 0x04, //0x000004b0 addq         $4, %rax
	0xe9, 0xfa, 0x09, 0x00, 0x00, //0x000004b4 jmp          LBB0_146
	//0x000004b9 LBB0_77
	0x49, 0x8d, 0x04, 0x09, //0x000004b9 leaq         (%r9,%rcx), %rax
	0x48, 0x83, 0xc0, 0x01, //0x000004bd addq         $1, %rax
	0xc6, 0x00, 0x65, //0x000004c1 movb         $101, (%rax)
	0x45, 0x85, 0xd2, //0x000004c4 testl        %r10d, %r10d
	0x0f, 0x8f, 0xbd, 0xff, 0xff, 0xff, //0x000004c7 jg           LBB0_75
	//0x000004cd LBB0_78
	0xc6, 0x40, 0x01, 0x2d, //0x000004cd movb         $45, $1(%rax)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000004d1 movl         $1, %ecx
	0x44, 0x29, 0xd1, //0x000004d6 subl         %r10d, %ecx
	0x83, 0xf9, 0x0a, //0x000004d9 cmpl         $10, %ecx
	0x0f, 0x83, 0xbc, 0xff, 0xff, 0xff, //0x000004dc jae          LBB0_76
	//0x000004e2 LBB0_79
	0x80, 0xc1, 0x30, //0x000004e2 addb         $48, %cl
	0x88, 0x48, 0x02, //0x000004e5 movb         %cl, $2(%rax)
	0x48, 0x83, 0xc0, 0x03, //0x000004e8 addq         $3, %rax
	0xe9, 0xc2, 0x09, 0x00, 0x00, //0x000004ec jmp          LBB0_146
	//0x000004f1 LBB0_34
	0x41, 0xd3, 0xef, //0x000004f1 shrl         %cl, %r15d
	0x41, 0x81, 0xff, 0xa0, 0x86, 0x01, 0x00, //0x000004f4 cmpl         $100000, %r15d
	0x0f, 0x82, 0xda, 0x01, 0x00, 0x00, //0x000004fb jb           LBB0_55
	0xb9, 0x06, 0x00, 0x00, 0x00, //0x00000501 movl         $6, %ecx
	0x41, 0x81, 0xff, 0x40, 0x42, 0x0f, 0x00, //0x00000506 cmpl         $1000000, %r15d
	0x0f, 0x82, 0x22, 0x00, 0x00, 0x00, //0x0000050d jb           LBB0_38
	0xb9, 0x07, 0x00, 0x00, 0x00, //0x00000513 movl         $7, %ecx
	0x41, 0x81, 0xff, 0x80, 0x96, 0x98, 0x00, //0x00000518 cmpl         $10000000, %r15d
	0x0f, 0x82, 0x10, 0x00, 0x00, 0x00, //0x0000051f jb           LBB0_38
	0x41, 0x81, 0xff, 0x00, 0xe1, 0xf5, 0x05, //0x00000525 cmpl         $100000000, %r15d
	0xb9, 0x09, 0x00, 0x00, 0x00, //0x0000052c movl         $9, %ecx
	0x48, 0x83, 0xd9, 0x00, //0x00000531 sbbq         $0, %rcx
	//0x00000535 LBB0_38
	0x4c, 0x01, 0xe9, //0x00000535 addq         %r13, %rcx
	//0x00000538 LBB0_39
	0x44, 0x89, 0xf8, //0x00000538 movl         %r15d, %eax
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x0000053b movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x00000540 imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x00000544 shrq         $45, %rdx
	0x69, 0xc2, 0xf0, 0xd8, 0xff, 0xff, //0x00000548 imull        $-10000, %edx, %eax
	0x44, 0x01, 0xf8, //0x0000054e addl         %r15d, %eax
	0x48, 0x69, 0xf0, 0x1f, 0x85, 0xeb, 0x51, //0x00000551 imulq        $1374389535, %rax, %rsi
	0x48, 0xc1, 0xee, 0x25, //0x00000558 shrq         $37, %rsi
	0x6b, 0xde, 0x64, //0x0000055c imull        $100, %esi, %ebx
	0x29, 0xd8, //0x0000055f subl         %ebx, %eax
	0x48, 0x8d, 0x1d, 0x88, 0x09, 0x00, 0x00, //0x00000561 leaq         $2440(%rip), %rbx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x43, //0x00000568 movzwl       (%rbx,%rax,2), %eax
	0x66, 0x89, 0x41, 0xfe, //0x0000056c movw         %ax, $-2(%rcx)
	0x0f, 0xb7, 0x04, 0x73, //0x00000570 movzwl       (%rbx,%rsi,2), %eax
	0x66, 0x89, 0x41, 0xfc, //0x00000574 movw         %ax, $-4(%rcx)
	0x48, 0x89, 0xc8, //0x00000578 movq         %rcx, %rax
	0x48, 0x83, 0xc1, 0xfc, //0x0000057b addq         $-4, %rcx
	0x41, 0x89, 0xd7, //0x0000057f movl         %edx, %r15d
	0x41, 0x83, 0xff, 0x64, //0x00000582 cmpl         $100, %r15d
	0x0f, 0x83, 0x8f, 0x01, 0x00, 0x00, //0x00000586 jae          LBB0_59
	0xe9, 0xd1, 0x01, 0x00, 0x00, //0x0000058c jmp          LBB0_61
	//0x00000591 LBB0_40
	0x45, 0x85, 0xd2, //0x00000591 testl        %r10d, %r10d
	0x0f, 0x8f, 0x27, 0x05, 0x00, 0x00, //0x00000594 jg           LBB0_107
	0x66, 0x41, 0xc7, 0x07, 0x30, 0x2e, //0x0000059a movw         $11824, (%r15)
	0x49, 0x83, 0xc7, 0x02, //0x000005a0 addq         $2, %r15
	0x45, 0x85, 0xd2, //0x000005a4 testl        %r10d, %r10d
	0x0f, 0x89, 0x14, 0x05, 0x00, 0x00, //0x000005a7 jns          LBB0_107
	0x45, 0x89, 0xe6, //0x000005ad movl         %r12d, %r14d
	0x41, 0xf7, 0xd6, //0x000005b0 notl         %r14d
	0x45, 0x29, 0xde, //0x000005b3 subl         %r11d, %r14d
	0x31, 0xc9, //0x000005b6 xorl         %ecx, %ecx
	0x41, 0x83, 0xfe, 0x7f, //0x000005b8 cmpl         $127, %r14d
	0x0f, 0x82, 0xe7, 0x04, 0x00, 0x00, //0x000005bc jb           LBB0_105
	0x4c, 0x89, 0xfb, //0x000005c2 movq         %r15, %rbx
	0x4c, 0x89, 0xef, //0x000005c5 movq         %r13, %rdi
	0x49, 0x83, 0xc6, 0x01, //0x000005c8 addq         $1, %r14
	0x4c, 0x89, 0xf1, //0x000005cc movq         %r14, %rcx
	0x48, 0x83, 0xe1, 0x80, //0x000005cf andq         $-128, %rcx
	0x48, 0x8d, 0x51, 0x80, //0x000005d3 leaq         $-128(%rcx), %rdx
	0x49, 0x89, 0xd5, //0x000005d7 movq         %rdx, %r13
	0x49, 0xc1, 0xed, 0x07, //0x000005da shrq         $7, %r13
	0x49, 0x83, 0xc5, 0x01, //0x000005de addq         $1, %r13
	0x45, 0x89, 0xef, //0x000005e2 movl         %r13d, %r15d
	0x41, 0x83, 0xe7, 0x03, //0x000005e5 andl         $3, %r15d
	0x48, 0x81, 0xfa, 0x80, 0x01, 0x00, 0x00, //0x000005e9 cmpq         $384, %rdx
	0x0f, 0x83, 0xb2, 0x03, 0x00, 0x00, //0x000005f0 jae          LBB0_99
	0x31, 0xd2, //0x000005f6 xorl         %edx, %edx
	0xe9, 0x55, 0x04, 0x00, 0x00, //0x000005f8 jmp          LBB0_101
	//0x000005fd LBB0_45
	0x48, 0x89, 0xd1, //0x000005fd movq         %rdx, %rcx
	0x83, 0xf8, 0x64, //0x00000600 cmpl         $100, %eax
	0x0f, 0x82, 0x90, 0xfd, 0xff, 0xff, //0x00000603 jb           LBB0_31
	//0x00000609 LBB0_46
	0x48, 0x83, 0xc1, 0xff, //0x00000609 addq         $-1, %rcx
	0x4c, 0x8d, 0x1d, 0xdc, 0x08, 0x00, 0x00, //0x0000060d leaq         $2268(%rip), %r11  /* _Digits+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000614 .p2align 4, 0x90
	//0x00000620 LBB0_47
	0x89, 0xc6, //0x00000620 movl         %eax, %esi
	0x48, 0x69, 0xf6, 0x1f, 0x85, 0xeb, 0x51, //0x00000622 imulq        $1374389535, %rsi, %rsi
	0x48, 0xc1, 0xee, 0x25, //0x00000629 shrq         $37, %rsi
	0x6b, 0xfe, 0x64, //0x0000062d imull        $100, %esi, %edi
	0x89, 0xc3, //0x00000630 movl         %eax, %ebx
	0x29, 0xfb, //0x00000632 subl         %edi, %ebx
	0x41, 0x0f, 0xb7, 0x3c, 0x5b, //0x00000634 movzwl       (%r11,%rbx,2), %edi
	0x66, 0x89, 0x79, 0xff, //0x00000639 movw         %di, $-1(%rcx)
	0x48, 0x83, 0xc1, 0xfe, //0x0000063d addq         $-2, %rcx
	0x3d, 0x0f, 0x27, 0x00, 0x00, //0x00000641 cmpl         $9999, %eax
	0x89, 0xf0, //0x00000646 movl         %esi, %eax
	0x0f, 0x87, 0xd2, 0xff, 0xff, 0xff, //0x00000648 ja           LBB0_47
	//0x0000064e LBB0_48
	0x4d, 0x63, 0xea, //0x0000064e movslq       %r10d, %r13
	0x83, 0xfe, 0x0a, //0x00000651 cmpl         $10, %esi
	0x0f, 0x82, 0x29, 0x00, 0x00, 0x00, //0x00000654 jb           LBB0_51
	0x89, 0xf0, //0x0000065a movl         %esi, %eax
	0x48, 0x8d, 0x0d, 0x8d, 0x08, 0x00, 0x00, //0x0000065c leaq         $2189(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x00000663 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x41, 0x89, 0x07, //0x00000667 movw         %ax, (%r15)
	0x4d, 0x01, 0xef, //0x0000066b addq         %r13, %r15
	0x4d, 0x39, 0xe8, //0x0000066e cmpq         %r13, %r8
	0x0f, 0x8c, 0x1f, 0x00, 0x00, 0x00, //0x00000671 jl           LBB0_52
	//0x00000677 LBB0_50
	0x4c, 0x89, 0xf8, //0x00000677 movq         %r15, %rax
	0x4c, 0x8b, 0x6d, 0xd0, //0x0000067a movq         $-48(%rbp), %r13
	0xe9, 0x30, 0x08, 0x00, 0x00, //0x0000067e jmp          LBB0_146
	//0x00000683 LBB0_51
	0x40, 0x80, 0xc6, 0x30, //0x00000683 addb         $48, %sil
	0x41, 0x88, 0x37, //0x00000687 movb         %sil, (%r15)
	0x4d, 0x01, 0xef, //0x0000068a addq         %r13, %r15
	0x4d, 0x39, 0xe8, //0x0000068d cmpq         %r13, %r8
	0x0f, 0x8d, 0xe1, 0xff, 0xff, 0xff, //0x00000690 jge          LBB0_50
	//0x00000696 LBB0_52
	0x4c, 0x89, 0xff, //0x00000696 movq         %r15, %rdi
	0x48, 0x8b, 0x45, 0xd0, //0x00000699 movq         $-48(%rbp), %rax
	0x4c, 0x01, 0xc8, //0x0000069d addq         %r9, %rax
	0x4d, 0x8d, 0x34, 0x00, //0x000006a0 leaq         (%r8,%rax), %r14
	0x49, 0x83, 0xc6, 0x01, //0x000006a4 addq         $1, %r14
	0x49, 0x01, 0xc5, //0x000006a8 addq         %rax, %r13
	0x4d, 0x39, 0xee, //0x000006ab cmpq         %r13, %r14
	0x4d, 0x89, 0xef, //0x000006ae movq         %r13, %r15
	0x4d, 0x0f, 0x47, 0xfe, //0x000006b1 cmovaq       %r14, %r15
	0x4e, 0x8d, 0x1c, 0x00, //0x000006b5 leaq         (%rax,%r8), %r11
	0x4d, 0x29, 0xdf, //0x000006b9 subq         %r11, %r15
	0x49, 0x83, 0xff, 0x10, //0x000006bc cmpq         $16, %r15
	0x0f, 0x82, 0xbc, 0x02, 0x00, 0x00, //0x000006c0 jb           LBB0_96
	0x49, 0x81, 0xff, 0x80, 0x00, 0x00, 0x00, //0x000006c6 cmpq         $128, %r15
	0x0f, 0x83, 0xda, 0x00, 0x00, 0x00, //0x000006cd jae          LBB0_80
	0x45, 0x31, 0xd2, //0x000006d3 xorl         %r10d, %r10d
	0xe9, 0x1c, 0x02, 0x00, 0x00, //0x000006d6 jmp          LBB0_89
	//0x000006db LBB0_55
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000006db movl         $1, %eax
	0x41, 0x83, 0xff, 0x0a, //0x000006e0 cmpl         $10, %r15d
	0x0f, 0x82, 0x21, 0x00, 0x00, 0x00, //0x000006e4 jb           LBB0_58
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x000006ea movl         $2, %eax
	0x41, 0x83, 0xff, 0x64, //0x000006ef cmpl         $100, %r15d
	0x0f, 0x82, 0x12, 0x00, 0x00, 0x00, //0x000006f3 jb           LBB0_58
	0xb8, 0x03, 0x00, 0x00, 0x00, //0x000006f9 movl         $3, %eax
	0x41, 0x81, 0xff, 0xe8, 0x03, 0x00, 0x00, //0x000006fe cmpl         $1000, %r15d
	0x0f, 0x83, 0x4d, 0x02, 0x00, 0x00, //0x00000705 jae          LBB0_93
	//0x0000070b LBB0_58
	0x4c, 0x01, 0xe8, //0x0000070b addq         %r13, %rax
	0x48, 0x89, 0xc1, //0x0000070e movq         %rax, %rcx
	0x41, 0x83, 0xff, 0x64, //0x00000711 cmpl         $100, %r15d
	0x0f, 0x82, 0x47, 0x00, 0x00, 0x00, //0x00000715 jb           LBB0_61
	//0x0000071b LBB0_59
	0x48, 0x83, 0xc1, 0xff, //0x0000071b addq         $-1, %rcx
	0x4c, 0x8d, 0x05, 0xca, 0x07, 0x00, 0x00, //0x0000071f leaq         $1994(%rip), %r8  /* _Digits+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000726 .p2align 4, 0x90
	//0x00000730 LBB0_60
	0x44, 0x89, 0xfb, //0x00000730 movl         %r15d, %ebx
	0x44, 0x89, 0xfe, //0x00000733 movl         %r15d, %esi
	0x4c, 0x69, 0xfe, 0x1f, 0x85, 0xeb, 0x51, //0x00000736 imulq        $1374389535, %rsi, %r15
	0x49, 0xc1, 0xef, 0x25, //0x0000073d shrq         $37, %r15
	0x41, 0x6b, 0xf7, 0x64, //0x00000741 imull        $100, %r15d, %esi
	0x89, 0xda, //0x00000745 movl         %ebx, %edx
	0x29, 0xf2, //0x00000747 subl         %esi, %edx
	0x41, 0x0f, 0xb7, 0x14, 0x50, //0x00000749 movzwl       (%r8,%rdx,2), %edx
	0x66, 0x89, 0x51, 0xff, //0x0000074e movw         %dx, $-1(%rcx)
	0x48, 0x83, 0xc1, 0xfe, //0x00000752 addq         $-2, %rcx
	0x81, 0xfb, 0x0f, 0x27, 0x00, 0x00, //0x00000756 cmpl         $9999, %ebx
	0x0f, 0x87, 0xce, 0xff, 0xff, 0xff, //0x0000075c ja           LBB0_60
	//0x00000762 LBB0_61
	0x41, 0x83, 0xff, 0x0a, //0x00000762 cmpl         $10, %r15d
	0x0f, 0x82, 0x1a, 0x00, 0x00, 0x00, //0x00000766 jb           LBB0_63
	0x44, 0x89, 0xf9, //0x0000076c movl         %r15d, %ecx
	0x48, 0x8d, 0x15, 0x7a, 0x07, 0x00, 0x00, //0x0000076f leaq         $1914(%rip), %rdx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x0c, 0x4a, //0x00000776 movzwl       (%rdx,%rcx,2), %ecx
	0x66, 0x41, 0x89, 0x4d, 0x00, //0x0000077a movw         %cx, (%r13)
	0x29, 0xf8, //0x0000077f subl         %edi, %eax
	0xe9, 0x30, 0x07, 0x00, 0x00, //0x00000781 jmp          LBB0_147
	//0x00000786 LBB0_63
	0x41, 0x80, 0xc7, 0x30, //0x00000786 addb         $48, %r15b
	0x45, 0x88, 0x7d, 0x00, //0x0000078a movb         %r15b, (%r13)
	0x29, 0xf8, //0x0000078e subl         %edi, %eax
	0xe9, 0x21, 0x07, 0x00, 0x00, //0x00000790 jmp          LBB0_147
	//0x00000795 LBB0_64
	0x41, 0xb8, 0x04, 0x00, 0x00, 0x00, //0x00000795 movl         $4, %r8d
	0x48, 0x83, 0xc2, 0xfc, //0x0000079b addq         $-4, %rdx
	0x83, 0xfe, 0x64, //0x0000079f cmpl         $100, %esi
	0x0f, 0x82, 0x82, 0xfb, 0xff, 0xff, //0x000007a2 jb           LBB0_33
	0xe9, 0x21, 0xfc, 0xff, 0xff, //0x000007a8 jmp          LBB0_66
	//0x000007ad LBB0_80
	0x4d, 0x89, 0xfa, //0x000007ad movq         %r15, %r10
	0x49, 0x83, 0xe2, 0x80, //0x000007b0 andq         $-128, %r10
	0x49, 0x8d, 0x42, 0x80, //0x000007b4 leaq         $-128(%r10), %rax
	0x48, 0x89, 0xc6, //0x000007b8 movq         %rax, %rsi
	0x48, 0xc1, 0xee, 0x07, //0x000007bb shrq         $7, %rsi
	0x48, 0x83, 0xc6, 0x01, //0x000007bf addq         $1, %rsi
	0x41, 0x89, 0xf4, //0x000007c3 movl         %esi, %r12d
	0x41, 0x83, 0xe4, 0x03, //0x000007c6 andl         $3, %r12d
	0x48, 0x3d, 0x80, 0x01, 0x00, 0x00, //0x000007ca cmpq         $384, %rax
	0x0f, 0x83, 0x07, 0x00, 0x00, 0x00, //0x000007d0 jae          LBB0_82
	0x31, 0xc9, //0x000007d6 xorl         %ecx, %ecx
	0xe9, 0xb4, 0x00, 0x00, 0x00, //0x000007d8 jmp          LBB0_84
	//0x000007dd LBB0_82
	0x48, 0x83, 0xe6, 0xfc, //0x000007dd andq         $-4, %rsi
	0x4b, 0x8d, 0x04, 0x01, //0x000007e1 leaq         (%r9,%r8), %rax
	0x48, 0x8b, 0x4d, 0xd0, //0x000007e5 movq         $-48(%rbp), %rcx
	0x48, 0x01, 0xc8, //0x000007e9 addq         %rcx, %rax
	0x48, 0x05, 0xe0, 0x01, 0x00, 0x00, //0x000007ec addq         $480, %rax
	0x31, 0xc9, //0x000007f2 xorl         %ecx, %ecx
	0xc5, 0xfe, 0x6f, 0x05, 0x04, 0xf8, 0xff, 0xff, //0x000007f4 vmovdqu      $-2044(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, 0x90, //0x000007fc .p2align 4, 0x90
	//0x00000800 LBB0_83
	0xc5, 0xfe, 0x7f, 0x84, 0x08, 0x20, 0xfe, 0xff, 0xff, //0x00000800 vmovdqu      %ymm0, $-480(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x84, 0x08, 0x40, 0xfe, 0xff, 0xff, //0x00000809 vmovdqu      %ymm0, $-448(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x84, 0x08, 0x60, 0xfe, 0xff, 0xff, //0x00000812 vmovdqu      %ymm0, $-416(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x84, 0x08, 0x80, 0xfe, 0xff, 0xff, //0x0000081b vmovdqu      %ymm0, $-384(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x84, 0x08, 0xa0, 0xfe, 0xff, 0xff, //0x00000824 vmovdqu      %ymm0, $-352(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x84, 0x08, 0xc0, 0xfe, 0xff, 0xff, //0x0000082d vmovdqu      %ymm0, $-320(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x84, 0x08, 0xe0, 0xfe, 0xff, 0xff, //0x00000836 vmovdqu      %ymm0, $-288(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x84, 0x08, 0x00, 0xff, 0xff, 0xff, //0x0000083f vmovdqu      %ymm0, $-256(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x84, 0x08, 0x20, 0xff, 0xff, 0xff, //0x00000848 vmovdqu      %ymm0, $-224(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x84, 0x08, 0x40, 0xff, 0xff, 0xff, //0x00000851 vmovdqu      %ymm0, $-192(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x84, 0x08, 0x60, 0xff, 0xff, 0xff, //0x0000085a vmovdqu      %ymm0, $-160(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x44, 0x08, 0x80, //0x00000863 vmovdqu      %ymm0, $-128(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x44, 0x08, 0xa0, //0x00000869 vmovdqu      %ymm0, $-96(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x44, 0x08, 0xc0, //0x0000086f vmovdqu      %ymm0, $-64(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x44, 0x08, 0xe0, //0x00000875 vmovdqu      %ymm0, $-32(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x04, 0x08, //0x0000087b vmovdqu      %ymm0, (%rax,%rcx)
	0x48, 0x81, 0xc1, 0x00, 0x02, 0x00, 0x00, //0x00000880 addq         $512, %rcx
	0x48, 0x83, 0xc6, 0xfc, //0x00000887 addq         $-4, %rsi
	0x0f, 0x85, 0x6f, 0xff, 0xff, 0xff, //0x0000088b jne          LBB0_83
	//0x00000891 LBB0_84
	0x4d, 0x85, 0xe4, //0x00000891 testq        %r12, %r12
	0x0f, 0x84, 0x4a, 0x00, 0x00, 0x00, //0x00000894 je           LBB0_87
	0x4c, 0x01, 0xc9, //0x0000089a addq         %r9, %rcx
	0x4c, 0x01, 0xc1, //0x0000089d addq         %r8, %rcx
	0x48, 0x8b, 0x45, 0xd0, //0x000008a0 movq         $-48(%rbp), %rax
	0x48, 0x01, 0xc8, //0x000008a4 addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0x60, //0x000008a7 addq         $96, %rax
	0x49, 0xc1, 0xe4, 0x07, //0x000008ab shlq         $7, %r12
	0x31, 0xc9, //0x000008af xorl         %ecx, %ecx
	0xc5, 0xfe, 0x6f, 0x05, 0x47, 0xf7, 0xff, 0xff, //0x000008b1 vmovdqu      $-2233(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000008b9 .p2align 4, 0x90
	//0x000008c0 LBB0_86
	0xc5, 0xfe, 0x7f, 0x44, 0x08, 0xa0, //0x000008c0 vmovdqu      %ymm0, $-96(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x44, 0x08, 0xc0, //0x000008c6 vmovdqu      %ymm0, $-64(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x44, 0x08, 0xe0, //0x000008cc vmovdqu      %ymm0, $-32(%rax,%rcx)
	0xc5, 0xfe, 0x7f, 0x04, 0x08, //0x000008d2 vmovdqu      %ymm0, (%rax,%rcx)
	0x48, 0x83, 0xe9, 0x80, //0x000008d7 subq         $-128, %rcx
	0x49, 0x39, 0xcc, //0x000008db cmpq         %rcx, %r12
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000008de jne          LBB0_86
	//0x000008e4 LBB0_87
	0x4d, 0x39, 0xd7, //0x000008e4 cmpq         %r10, %r15
	0x0f, 0x84, 0x5f, 0x00, 0x00, 0x00, //0x000008e7 je           LBB0_92
	0x41, 0xf6, 0xc7, 0x70, //0x000008ed testb        $112, %r15b
	0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, //0x000008f1 je           LBB0_95
	//0x000008f7 LBB0_89
	0x4d, 0x39, 0xee, //0x000008f7 cmpq         %r13, %r14
	0x4d, 0x0f, 0x47, 0xee, //0x000008fa cmovaq       %r14, %r13
	0x4d, 0x29, 0xdd, //0x000008fe subq         %r11, %r13
	0x4c, 0x89, 0xe8, //0x00000901 movq         %r13, %rax
	0x48, 0x83, 0xe0, 0xf0, //0x00000904 andq         $-16, %rax
	0x48, 0x01, 0xc2, //0x00000908 addq         %rax, %rdx
	0x4d, 0x01, 0xd1, //0x0000090b addq         %r10, %r9
	0x4d, 0x01, 0xc1, //0x0000090e addq         %r8, %r9
	0x4c, 0x03, 0x4d, 0xd0, //0x00000911 addq         $-48(%rbp), %r9
	0x48, 0x89, 0xc1, //0x00000915 movq         %rax, %rcx
	0x4c, 0x29, 0xd1, //0x00000918 subq         %r10, %rcx
	0x31, 0xf6, //0x0000091b xorl         %esi, %esi
	0xc5, 0xfa, 0x6f, 0x05, 0xfb, 0xf6, 0xff, 0xff, //0x0000091d vmovdqu      $-2309(%rip), %xmm0  /* LCPI0_1+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000925 .p2align 4, 0x90
	//0x00000930 LBB0_90
	0xc4, 0xc1, 0x7a, 0x7f, 0x04, 0x31, //0x00000930 vmovdqu      %xmm0, (%r9,%rsi)
	0x48, 0x83, 0xc6, 0x10, //0x00000936 addq         $16, %rsi
	0x48, 0x39, 0xf1, //0x0000093a cmpq         %rsi, %rcx
	0x0f, 0x85, 0xed, 0xff, 0xff, 0xff, //0x0000093d jne          LBB0_90
	0x49, 0x39, 0xc5, //0x00000943 cmpq         %rax, %r13
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x00000946 jne          LBB0_96
	//0x0000094c LBB0_92
	0x48, 0x89, 0xf8, //0x0000094c movq         %rdi, %rax
	0x4c, 0x8b, 0x6d, 0xd0, //0x0000094f movq         $-48(%rbp), %r13
	0xe9, 0x5b, 0x05, 0x00, 0x00, //0x00000953 jmp          LBB0_146
	//0x00000958 LBB0_93
	0x41, 0x81, 0xff, 0x10, 0x27, 0x00, 0x00, //0x00000958 cmpl         $10000, %r15d
	0x4c, 0x89, 0xe9, //0x0000095f movq         %r13, %rcx
	0x48, 0x83, 0xd9, 0x00, //0x00000962 sbbq         $0, %rcx
	0x48, 0x83, 0xc1, 0x05, //0x00000966 addq         $5, %rcx
	0x41, 0x81, 0xff, 0x10, 0x27, 0x00, 0x00, //0x0000096a cmpl         $10000, %r15d
	0x0f, 0x83, 0xc1, 0xfb, 0xff, 0xff, //0x00000971 jae          LBB0_39
	0x48, 0x89, 0xc8, //0x00000977 movq         %rcx, %rax
	0xe9, 0x9c, 0xfd, 0xff, 0xff, //0x0000097a jmp          LBB0_59
	//0x0000097f LBB0_95
	0x4c, 0x01, 0xd2, //0x0000097f addq         %r10, %rdx
	//0x00000982 LBB0_96
	0x4c, 0x8b, 0x6d, 0xd0, //0x00000982 movq         $-48(%rbp), %r13
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000986 .p2align 4, 0x90
	//0x00000990 LBB0_97
	0xc6, 0x02, 0x30, //0x00000990 movb         $48, (%rdx)
	0x48, 0x83, 0xc2, 0x01, //0x00000993 addq         $1, %rdx
	0x48, 0x39, 0xfa, //0x00000997 cmpq         %rdi, %rdx
	0x0f, 0x82, 0xf0, 0xff, 0xff, 0xff, //0x0000099a jb           LBB0_97
	0x48, 0x89, 0xf8, //0x000009a0 movq         %rdi, %rax
	0xe9, 0x0b, 0x05, 0x00, 0x00, //0x000009a3 jmp          LBB0_146
	//0x000009a8 LBB0_99
	0x49, 0x83, 0xe5, 0xfc, //0x000009a8 andq         $-4, %r13
	0x49, 0x8d, 0x34, 0x39, //0x000009ac leaq         (%r9,%rdi), %rsi
	0x48, 0x81, 0xc6, 0xe2, 0x01, 0x00, 0x00, //0x000009b0 addq         $482, %rsi
	0x31, 0xd2, //0x000009b7 xorl         %edx, %edx
	0xc5, 0xfe, 0x6f, 0x05, 0x3f, 0xf6, 0xff, 0xff, //0x000009b9 vmovdqu      $-2497(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	//0x000009c1 LBB0_100
	0xc5, 0xfe, 0x7f, 0x84, 0x16, 0x20, 0xfe, 0xff, 0xff, //0x000009c1 vmovdqu      %ymm0, $-480(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x84, 0x16, 0x40, 0xfe, 0xff, 0xff, //0x000009ca vmovdqu      %ymm0, $-448(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x84, 0x16, 0x60, 0xfe, 0xff, 0xff, //0x000009d3 vmovdqu      %ymm0, $-416(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x84, 0x16, 0x80, 0xfe, 0xff, 0xff, //0x000009dc vmovdqu      %ymm0, $-384(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x84, 0x16, 0xa0, 0xfe, 0xff, 0xff, //0x000009e5 vmovdqu      %ymm0, $-352(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x84, 0x16, 0xc0, 0xfe, 0xff, 0xff, //0x000009ee vmovdqu      %ymm0, $-320(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x84, 0x16, 0xe0, 0xfe, 0xff, 0xff, //0x000009f7 vmovdqu      %ymm0, $-288(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x84, 0x16, 0x00, 0xff, 0xff, 0xff, //0x00000a00 vmovdqu      %ymm0, $-256(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x84, 0x16, 0x20, 0xff, 0xff, 0xff, //0x00000a09 vmovdqu      %ymm0, $-224(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x84, 0x16, 0x40, 0xff, 0xff, 0xff, //0x00000a12 vmovdqu      %ymm0, $-192(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x84, 0x16, 0x60, 0xff, 0xff, 0xff, //0x00000a1b vmovdqu      %ymm0, $-160(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x44, 0x16, 0x80, //0x00000a24 vmovdqu      %ymm0, $-128(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x44, 0x16, 0xa0, //0x00000a2a vmovdqu      %ymm0, $-96(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x44, 0x16, 0xc0, //0x00000a30 vmovdqu      %ymm0, $-64(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x44, 0x16, 0xe0, //0x00000a36 vmovdqu      %ymm0, $-32(%rsi,%rdx)
	0xc5, 0xfe, 0x7f, 0x04, 0x16, //0x00000a3c vmovdqu      %ymm0, (%rsi,%rdx)
	0x48, 0x81, 0xc2, 0x00, 0x02, 0x00, 0x00, //0x00000a41 addq         $512, %rdx
	0x49, 0x83, 0xc5, 0xfc, //0x00000a48 addq         $-4, %r13
	0x0f, 0x85, 0x6f, 0xff, 0xff, 0xff, //0x00000a4c jne          LBB0_100
	//0x00000a52 LBB0_101
	0x4d, 0x85, 0xff, //0x00000a52 testq        %r15, %r15
	0x49, 0x89, 0xfd, //0x00000a55 movq         %rdi, %r13
	0x0f, 0x84, 0x3c, 0x00, 0x00, 0x00, //0x00000a58 je           LBB0_104
	0x4c, 0x01, 0xca, //0x00000a5e addq         %r9, %rdx
	0x4c, 0x01, 0xea, //0x00000a61 addq         %r13, %rdx
	0x48, 0x83, 0xc2, 0x62, //0x00000a64 addq         $98, %rdx
	0x49, 0xc1, 0xe7, 0x07, //0x00000a68 shlq         $7, %r15
	0x31, 0xf6, //0x00000a6c xorl         %esi, %esi
	0xc5, 0xfe, 0x6f, 0x05, 0x8a, 0xf5, 0xff, 0xff, //0x00000a6e vmovdqu      $-2678(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	//0x00000a76 LBB0_103
	0xc5, 0xfe, 0x7f, 0x44, 0x32, 0xa0, //0x00000a76 vmovdqu      %ymm0, $-96(%rdx,%rsi)
	0xc5, 0xfe, 0x7f, 0x44, 0x32, 0xc0, //0x00000a7c vmovdqu      %ymm0, $-64(%rdx,%rsi)
	0xc5, 0xfe, 0x7f, 0x44, 0x32, 0xe0, //0x00000a82 vmovdqu      %ymm0, $-32(%rdx,%rsi)
	0xc5, 0xfe, 0x7f, 0x04, 0x32, //0x00000a88 vmovdqu      %ymm0, (%rdx,%rsi)
	0x48, 0x83, 0xee, 0x80, //0x00000a8d subq         $-128, %rsi
	0x49, 0x39, 0xf7, //0x00000a91 cmpq         %rsi, %r15
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00000a94 jne          LBB0_103
	//0x00000a9a LBB0_104
	0x49, 0x89, 0xdf, //0x00000a9a movq         %rbx, %r15
	0x49, 0x01, 0xcf, //0x00000a9d addq         %rcx, %r15
	0x49, 0x39, 0xce, //0x00000aa0 cmpq         %rcx, %r14
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00000aa3 je           LBB0_107
	//0x00000aa9 LBB0_105
	0x44, 0x01, 0xd1, //0x00000aa9 addl         %r10d, %ecx
	0xf7, 0xd9, //0x00000aac negl         %ecx
	0x90, 0x90, //0x00000aae .p2align 4, 0x90
	//0x00000ab0 LBB0_106
	0x41, 0xc6, 0x07, 0x30, //0x00000ab0 movb         $48, (%r15)
	0x49, 0x83, 0xc7, 0x01, //0x00000ab4 addq         $1, %r15
	0x83, 0xc1, 0xff, //0x00000ab8 addl         $-1, %ecx
	0x0f, 0x85, 0xef, 0xff, 0xff, 0xff, //0x00000abb jne          LBB0_106
	//0x00000ac1 LBB0_107
	0x4c, 0x89, 0x7d, 0xd0, //0x00000ac1 movq         %r15, $-48(%rbp)
	0x4d, 0x01, 0xc7, //0x00000ac5 addq         %r8, %r15
	0x3d, 0x10, 0x27, 0x00, 0x00, //0x00000ac8 cmpl         $10000, %eax
	0x0f, 0x82, 0x5f, 0x00, 0x00, 0x00, //0x00000acd jb           LBB0_110
	0x89, 0xc2, //0x00000ad3 movl         %eax, %edx
	0xbb, 0x59, 0x17, 0xb7, 0xd1, //0x00000ad5 movl         $3518437209, %ebx
	0x48, 0x0f, 0xaf, 0xda, //0x00000ada imulq        %rdx, %rbx
	0x48, 0xc1, 0xeb, 0x2d, //0x00000ade shrq         $45, %rbx
	0x69, 0xd3, 0xf0, 0xd8, 0xff, 0xff, //0x00000ae2 imull        $-10000, %ebx, %edx
	0x01, 0xc2, //0x00000ae8 addl         %eax, %edx
	0x0f, 0x84, 0xd8, 0x01, 0x00, 0x00, //0x00000aea je           LBB0_112
	0x89, 0xd0, //0x00000af0 movl         %edx, %eax
	0x48, 0x69, 0xc0, 0x1f, 0x85, 0xeb, 0x51, //0x00000af2 imulq        $1374389535, %rax, %rax
	0x48, 0xc1, 0xe8, 0x25, //0x00000af9 shrq         $37, %rax
	0x6b, 0xf0, 0x64, //0x00000afd imull        $100, %eax, %esi
	0x29, 0xf2, //0x00000b00 subl         %esi, %edx
	0x48, 0x8d, 0x35, 0xe7, 0x03, 0x00, 0x00, //0x00000b02 leaq         $999(%rip), %rsi  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x14, 0x56, //0x00000b09 movzwl       (%rsi,%rdx,2), %edx
	0x66, 0x41, 0x89, 0x57, 0xfe, //0x00000b0d movw         %dx, $-2(%r15)
	0x0f, 0xb7, 0x04, 0x46, //0x00000b12 movzwl       (%rsi,%rax,2), %eax
	0x66, 0x41, 0x89, 0x47, 0xfc, //0x00000b16 movw         %ax, $-4(%r15)
	0x45, 0x31, 0xf6, //0x00000b1b xorl         %r14d, %r14d
	0x49, 0x8d, 0x57, 0xfc, //0x00000b1e leaq         $-4(%r15), %rdx
	0x83, 0xfb, 0x64, //0x00000b22 cmpl         $100, %ebx
	0x0f, 0x83, 0x18, 0x00, 0x00, 0x00, //0x00000b25 jae          LBB0_114
	//0x00000b2b LBB0_111
	0x89, 0xd8, //0x00000b2b movl         %ebx, %eax
	0xe9, 0x4c, 0x00, 0x00, 0x00, //0x00000b2d jmp          LBB0_116
	//0x00000b32 LBB0_110
	0x45, 0x31, 0xf6, //0x00000b32 xorl         %r14d, %r14d
	0x4c, 0x89, 0xfa, //0x00000b35 movq         %r15, %rdx
	0x89, 0xc3, //0x00000b38 movl         %eax, %ebx
	0x83, 0xfb, 0x64, //0x00000b3a cmpl         $100, %ebx
	0x0f, 0x82, 0xe8, 0xff, 0xff, 0xff, //0x00000b3d jb           LBB0_111
	//0x00000b43 LBB0_114
	0x48, 0x83, 0xc2, 0xff, //0x00000b43 addq         $-1, %rdx
	0x48, 0x8d, 0x35, 0xa2, 0x03, 0x00, 0x00, //0x00000b47 leaq         $930(%rip), %rsi  /* _Digits+0(%rip) */
	0x90, 0x90, //0x00000b4e .p2align 4, 0x90
	//0x00000b50 LBB0_115
	0x89, 0xd8, //0x00000b50 movl         %ebx, %eax
	0x48, 0x69, 0xc0, 0x1f, 0x85, 0xeb, 0x51, //0x00000b52 imulq        $1374389535, %rax, %rax
	0x48, 0xc1, 0xe8, 0x25, //0x00000b59 shrq         $37, %rax
	0x6b, 0xf8, 0x64, //0x00000b5d imull        $100, %eax, %edi
	0x89, 0xd9, //0x00000b60 movl         %ebx, %ecx
	0x29, 0xf9, //0x00000b62 subl         %edi, %ecx
	0x0f, 0xb7, 0x0c, 0x4e, //0x00000b64 movzwl       (%rsi,%rcx,2), %ecx
	0x66, 0x89, 0x4a, 0xff, //0x00000b68 movw         %cx, $-1(%rdx)
	0x48, 0x83, 0xc2, 0xfe, //0x00000b6c addq         $-2, %rdx
	0x81, 0xfb, 0x0f, 0x27, 0x00, 0x00, //0x00000b70 cmpl         $9999, %ebx
	0x89, 0xc3, //0x00000b76 movl         %eax, %ebx
	0x0f, 0x87, 0xd2, 0xff, 0xff, 0xff, //0x00000b78 ja           LBB0_115
	//0x00000b7e LBB0_116
	0x4d, 0x89, 0xe9, //0x00000b7e movq         %r13, %r9
	0x83, 0xf8, 0x0a, //0x00000b81 cmpl         $10, %eax
	0x0f, 0x82, 0x19, 0x00, 0x00, 0x00, //0x00000b84 jb           LBB0_118
	0x89, 0xc0, //0x00000b8a movl         %eax, %eax
	0x48, 0x8d, 0x0d, 0x5d, 0x03, 0x00, 0x00, //0x00000b8c leaq         $861(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x00000b93 movzwl       (%rcx,%rax,2), %eax
	0x48, 0x8b, 0x4d, 0xd0, //0x00000b97 movq         $-48(%rbp), %rcx
	0x66, 0x89, 0x01, //0x00000b9b movw         %ax, (%rcx)
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00000b9e jmp          LBB0_119
	//0x00000ba3 LBB0_118
	0x04, 0x30, //0x00000ba3 addb         $48, %al
	0x48, 0x8b, 0x4d, 0xd0, //0x00000ba5 movq         $-48(%rbp), %rcx
	0x88, 0x01, //0x00000ba9 movb         %al, (%rcx)
	//0x00000bab LBB0_119
	0x4d, 0x29, 0xf7, //0x00000bab subq         %r14, %r15
	0x4d, 0x29, 0xf0, //0x00000bae subq         %r14, %r8
	0x49, 0x83, 0xc0, 0x01, //0x00000bb1 addq         $1, %r8
	0x43, 0x8d, 0x04, 0x1e, //0x00000bb5 leal         (%r14,%r11), %eax
	0xf6, 0xd8, //0x00000bb9 negb         %al
	0x43, 0x8d, 0x14, 0x33, //0x00000bbb leal         (%r11,%r14), %edx
	0xf7, 0xda, //0x00000bbf negl         %edx
	0x47, 0x8d, 0x2c, 0x33, //0x00000bc1 leal         (%r11,%r14), %r13d
	0x41, 0x83, 0xc5, 0xff, //0x00000bc5 addl         $-1, %r13d
	0x43, 0x8d, 0x34, 0x33, //0x00000bc9 leal         (%r11,%r14), %esi
	0x83, 0xc6, 0xfe, //0x00000bcd addl         $-2, %esi
	0x31, 0xc9, //0x00000bd0 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000bd2 .p2align 4, 0x90
	//0x00000be0 LBB0_120
	0x89, 0xc3, //0x00000be0 movl         %eax, %ebx
	0x8d, 0x43, 0x03, //0x00000be2 leal         $3(%rbx), %eax
	0x83, 0xc6, 0x01, //0x00000be5 addl         $1, %esi
	0x41, 0x80, 0x7c, 0x0f, 0xff, 0x30, //0x00000be8 cmpb         $48, $-1(%r15,%rcx)
	0x48, 0x8d, 0x49, 0xff, //0x00000bee leaq         $-1(%rcx), %rcx
	0x0f, 0x84, 0xe8, 0xff, 0xff, 0xff, //0x00000bf2 je           LBB0_120
	0x49, 0x8d, 0x04, 0x0f, //0x00000bf8 leaq         (%r15,%rcx), %rax
	0x48, 0x83, 0xc0, 0x01, //0x00000bfc addq         $1, %rax
	0x45, 0x85, 0xd2, //0x00000c00 testl        %r10d, %r10d
	0x0f, 0x8e, 0xb7, 0x00, 0x00, 0x00, //0x00000c03 jle          LBB0_126
	0x45, 0x29, 0xf4, //0x00000c09 subl         %r14d, %r12d
	0x41, 0x8d, 0x3c, 0x0c, //0x00000c0c leal         (%r12,%rcx), %edi
	0x83, 0xc7, 0x01, //0x00000c10 addl         $1, %edi
	0x41, 0x39, 0xfa, //0x00000c13 cmpl         %edi, %r10d
	0x0f, 0x8d, 0x2e, 0x00, 0x00, 0x00, //0x00000c16 jge          LBB0_127
	0x48, 0x63, 0xc2, //0x00000c1c movslq       %edx, %rax
	0x48, 0x8d, 0x34, 0x08, //0x00000c1f leaq         (%rax,%rcx), %rsi
	0x48, 0x83, 0xc6, 0x01, //0x00000c23 addq         $1, %rsi
	0x85, 0xf6, //0x00000c27 testl        %esi, %esi
	0x4d, 0x89, 0xcd, //0x00000c29 movq         %r9, %r13
	0x0f, 0x8e, 0x17, 0x01, 0x00, 0x00, //0x00000c2c jle          LBB0_138
	0x41, 0x89, 0xf0, //0x00000c32 movl         %esi, %r8d
	0x49, 0x8d, 0x50, 0xff, //0x00000c35 leaq         $-1(%r8), %rdx
	0x48, 0x83, 0xfa, 0x03, //0x00000c39 cmpq         $3, %rdx
	0x0f, 0x83, 0x9d, 0x00, 0x00, 0x00, //0x00000c3d jae          LBB0_132
	0x31, 0xd2, //0x00000c43 xorl         %edx, %edx
	0xe9, 0xc2, 0x00, 0x00, 0x00, //0x00000c45 jmp          LBB0_135
	//0x00000c4a LBB0_127
	0x45, 0x89, 0xea, //0x00000c4a movl         %r13d, %r10d
	0x49, 0x29, 0xca, //0x00000c4d subq         %rcx, %r10
	0x45, 0x85, 0xd2, //0x00000c50 testl        %r10d, %r10d
	0x0f, 0x8e, 0x67, 0x00, 0x00, 0x00, //0x00000c53 jle          LBB0_126
	0x43, 0x8d, 0x1c, 0x33, //0x00000c59 leal         (%r11,%r14), %ebx
	0x83, 0xc3, 0xfe, //0x00000c5d addl         $-2, %ebx
	0x48, 0x29, 0xcb, //0x00000c60 subq         %rcx, %rbx
	0x31, 0xd2, //0x00000c63 xorl         %edx, %edx
	0x83, 0xfb, 0x7f, //0x00000c65 cmpl         $127, %ebx
	0x4d, 0x89, 0xcd, //0x00000c68 movq         %r9, %r13
	0x0f, 0x82, 0x2f, 0x02, 0x00, 0x00, //0x00000c6b jb           LBB0_145
	0x45, 0x01, 0xde, //0x00000c71 addl         %r11d, %r14d
	0x41, 0x83, 0xc6, 0xfe, //0x00000c74 addl         $-2, %r14d
	0x49, 0x29, 0xce, //0x00000c78 subq         %rcx, %r14
	0x41, 0x89, 0xdb, //0x00000c7b movl         %ebx, %r11d
	0x49, 0x83, 0xc3, 0x01, //0x00000c7e addq         $1, %r11
	0x4c, 0x89, 0xda, //0x00000c82 movq         %r11, %rdx
	0x48, 0x83, 0xe2, 0x80, //0x00000c85 andq         $-128, %rdx
	0x48, 0x8b, 0x7d, 0xd0, //0x00000c89 movq         $-48(%rbp), %rdi
	0x4c, 0x01, 0xc7, //0x00000c8d addq         %r8, %rdi
	0x89, 0xf0, //0x00000c90 movl         %esi, %eax
	0x48, 0x83, 0xc0, 0x01, //0x00000c92 addq         $1, %rax
	0x48, 0x83, 0xe0, 0x80, //0x00000c96 andq         $-128, %rax
	0x48, 0x01, 0xf8, //0x00000c9a addq         %rdi, %rax
	0x48, 0x8d, 0x72, 0x80, //0x00000c9d leaq         $-128(%rdx), %rsi
	0x49, 0x89, 0xf0, //0x00000ca1 movq         %rsi, %r8
	0x49, 0xc1, 0xe8, 0x07, //0x00000ca4 shrq         $7, %r8
	0x49, 0x83, 0xc0, 0x01, //0x00000ca8 addq         $1, %r8
	0x48, 0x81, 0xfe, 0x80, 0x01, 0x00, 0x00, //0x00000cac cmpq         $384, %rsi
	0x0f, 0x83, 0xa8, 0x00, 0x00, 0x00, //0x00000cb3 jae          LBB0_139
	0x31, 0xf6, //0x00000cb9 xorl         %esi, %esi
	0xe9, 0x5f, 0x01, 0x00, 0x00, //0x00000cbb jmp          LBB0_141
	//0x00000cc0 LBB0_126
	0x4d, 0x89, 0xcd, //0x00000cc0 movq         %r9, %r13
	0xe9, 0xeb, 0x01, 0x00, 0x00, //0x00000cc3 jmp          LBB0_146
	//0x00000cc8 LBB0_112
	0x41, 0xbe, 0x04, 0x00, 0x00, 0x00, //0x00000cc8 movl         $4, %r14d
	0x49, 0x8d, 0x57, 0xfc, //0x00000cce leaq         $-4(%r15), %rdx
	0x83, 0xfb, 0x64, //0x00000cd2 cmpl         $100, %ebx
	0x0f, 0x82, 0x50, 0xfe, 0xff, 0xff, //0x00000cd5 jb           LBB0_111
	0xe9, 0x63, 0xfe, 0xff, 0xff, //0x00000cdb jmp          LBB0_114
	//0x00000ce0 LBB0_132
	0x83, 0xe6, 0xfc, //0x00000ce0 andl         $-4, %esi
	0x48, 0xf7, 0xde, //0x00000ce3 negq         %rsi
	0x31, 0xd2, //0x00000ce6 xorl         %edx, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000ce8 .p2align 4, 0x90
	//0x00000cf0 LBB0_133
	0x49, 0x8d, 0x3c, 0x17, //0x00000cf0 leaq         (%r15,%rdx), %rdi
	0x8b, 0x44, 0x39, 0xfd, //0x00000cf4 movl         $-3(%rcx,%rdi), %eax
	0x89, 0x44, 0x39, 0xfe, //0x00000cf8 movl         %eax, $-2(%rcx,%rdi)
	0x48, 0x83, 0xc2, 0xfc, //0x00000cfc addq         $-4, %rdx
	0x48, 0x39, 0xd6, //0x00000d00 cmpq         %rdx, %rsi
	0x0f, 0x85, 0xe7, 0xff, 0xff, 0xff, //0x00000d03 jne          LBB0_133
	0x48, 0xf7, 0xda, //0x00000d09 negq         %rdx
	//0x00000d0c LBB0_135
	0x41, 0xf6, 0xc0, 0x03, //0x00000d0c testb        $3, %r8b
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00000d10 je           LBB0_138
	0x0f, 0xb6, 0xc3, //0x00000d16 movzbl       %bl, %eax
	0x83, 0xe0, 0x03, //0x00000d19 andl         $3, %eax
	0x48, 0xf7, 0xd8, //0x00000d1c negq         %rax
	0x4c, 0x89, 0xfe, //0x00000d1f movq         %r15, %rsi
	0x48, 0x29, 0xd6, //0x00000d22 subq         %rdx, %rsi
	0x31, 0xd2, //0x00000d25 xorl         %edx, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000d27 .p2align 4, 0x90
	//0x00000d30 LBB0_137
	0x48, 0x8d, 0x3c, 0x16, //0x00000d30 leaq         (%rsi,%rdx), %rdi
	0x0f, 0xb6, 0x1c, 0x39, //0x00000d34 movzbl       (%rcx,%rdi), %ebx
	0x88, 0x5c, 0x39, 0x01, //0x00000d38 movb         %bl, $1(%rcx,%rdi)
	0x48, 0x83, 0xc2, 0xff, //0x00000d3c addq         $-1, %rdx
	0x48, 0x39, 0xd0, //0x00000d40 cmpq         %rdx, %rax
	0x0f, 0x85, 0xe7, 0xff, 0xff, 0xff, //0x00000d43 jne          LBB0_137
	//0x00000d49 LBB0_138
	0x49, 0x63, 0xc2, //0x00000d49 movslq       %r10d, %rax
	0x48, 0x8b, 0x55, 0xd0, //0x00000d4c movq         $-48(%rbp), %rdx
	0xc6, 0x04, 0x02, 0x2e, //0x00000d50 movb         $46, (%rdx,%rax)
	0x49, 0x8d, 0x04, 0x0f, //0x00000d54 leaq         (%r15,%rcx), %rax
	0x48, 0x83, 0xc0, 0x02, //0x00000d58 addq         $2, %rax
	0xe9, 0x52, 0x01, 0x00, 0x00, //0x00000d5c jmp          LBB0_146
	//0x00000d61 LBB0_139
	0x44, 0x89, 0xf3, //0x00000d61 movl         %r14d, %ebx
	0x48, 0x83, 0xc3, 0x01, //0x00000d64 addq         $1, %rbx
	0x48, 0x83, 0xe3, 0x80, //0x00000d68 andq         $-128, %rbx
	0x48, 0x83, 0xc3, 0x80, //0x00000d6c addq         $-128, %rbx
	0x48, 0xc1, 0xeb, 0x07, //0x00000d70 shrq         $7, %rbx
	0x48, 0x83, 0xc3, 0x01, //0x00000d74 addq         $1, %rbx
	0x48, 0x83, 0xe3, 0xfc, //0x00000d78 andq         $-4, %rbx
	0x31, 0xf6, //0x00000d7c xorl         %esi, %esi
	0xc5, 0xfe, 0x6f, 0x05, 0x7a, 0xf2, 0xff, 0xff, //0x00000d7e vmovdqu      $-3462(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	//0x00000d86 LBB0_140
	0x49, 0x8d, 0x3c, 0x37, //0x00000d86 leaq         (%r15,%rsi), %rdi
	0xc5, 0xfe, 0x7f, 0x44, 0x39, 0x01, //0x00000d8a vmovdqu      %ymm0, $1(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x44, 0x39, 0x21, //0x00000d90 vmovdqu      %ymm0, $33(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x44, 0x39, 0x41, //0x00000d96 vmovdqu      %ymm0, $65(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x44, 0x39, 0x61, //0x00000d9c vmovdqu      %ymm0, $97(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x84, 0x39, 0x81, 0x00, 0x00, 0x00, //0x00000da2 vmovdqu      %ymm0, $129(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x84, 0x39, 0xa1, 0x00, 0x00, 0x00, //0x00000dab vmovdqu      %ymm0, $161(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x84, 0x39, 0xc1, 0x00, 0x00, 0x00, //0x00000db4 vmovdqu      %ymm0, $193(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x84, 0x39, 0xe1, 0x00, 0x00, 0x00, //0x00000dbd vmovdqu      %ymm0, $225(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x84, 0x39, 0x01, 0x01, 0x00, 0x00, //0x00000dc6 vmovdqu      %ymm0, $257(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x84, 0x39, 0x21, 0x01, 0x00, 0x00, //0x00000dcf vmovdqu      %ymm0, $289(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x84, 0x39, 0x41, 0x01, 0x00, 0x00, //0x00000dd8 vmovdqu      %ymm0, $321(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x84, 0x39, 0x61, 0x01, 0x00, 0x00, //0x00000de1 vmovdqu      %ymm0, $353(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x84, 0x39, 0x81, 0x01, 0x00, 0x00, //0x00000dea vmovdqu      %ymm0, $385(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x84, 0x39, 0xa1, 0x01, 0x00, 0x00, //0x00000df3 vmovdqu      %ymm0, $417(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x84, 0x39, 0xc1, 0x01, 0x00, 0x00, //0x00000dfc vmovdqu      %ymm0, $449(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x84, 0x39, 0xe1, 0x01, 0x00, 0x00, //0x00000e05 vmovdqu      %ymm0, $481(%rcx,%rdi)
	0x48, 0x81, 0xc6, 0x00, 0x02, 0x00, 0x00, //0x00000e0e addq         $512, %rsi
	0x48, 0x83, 0xc3, 0xfc, //0x00000e15 addq         $-4, %rbx
	0x0f, 0x85, 0x67, 0xff, 0xff, 0xff, //0x00000e19 jne          LBB0_140
	//0x00000e1f LBB0_141
	0x48, 0x01, 0xc8, //0x00000e1f addq         %rcx, %rax
	0x41, 0xf6, 0xc0, 0x03, //0x00000e22 testb        $3, %r8b
	0x0f, 0x84, 0x5c, 0x00, 0x00, 0x00, //0x00000e26 je           LBB0_144
	0x41, 0x83, 0xc6, 0x01, //0x00000e2c addl         $1, %r14d
	0x41, 0x81, 0xe6, 0x80, 0x01, 0x00, 0x00, //0x00000e30 andl         $384, %r14d
	0x41, 0x83, 0xc6, 0x80, //0x00000e37 addl         $-128, %r14d
	0x41, 0xc1, 0xee, 0x07, //0x00000e3b shrl         $7, %r14d
	0x41, 0x80, 0xc6, 0x01, //0x00000e3f addb         $1, %r14b
	0x45, 0x0f, 0xb6, 0xc6, //0x00000e43 movzbl       %r14b, %r8d
	0x41, 0x83, 0xe0, 0x03, //0x00000e47 andl         $3, %r8d
	0x49, 0xc1, 0xe0, 0x07, //0x00000e4b shlq         $7, %r8
	0x4c, 0x01, 0xfe, //0x00000e4f addq         %r15, %rsi
	0x48, 0x83, 0xc6, 0x61, //0x00000e52 addq         $97, %rsi
	0x31, 0xdb, //0x00000e56 xorl         %ebx, %ebx
	0xc5, 0xfe, 0x6f, 0x05, 0xa0, 0xf1, 0xff, 0xff, //0x00000e58 vmovdqu      $-3680(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	//0x00000e60 LBB0_143
	0x48, 0x8d, 0x3c, 0x1e, //0x00000e60 leaq         (%rsi,%rbx), %rdi
	0xc5, 0xfe, 0x7f, 0x44, 0x39, 0xa0, //0x00000e64 vmovdqu      %ymm0, $-96(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x44, 0x39, 0xc0, //0x00000e6a vmovdqu      %ymm0, $-64(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x44, 0x39, 0xe0, //0x00000e70 vmovdqu      %ymm0, $-32(%rcx,%rdi)
	0xc5, 0xfe, 0x7f, 0x04, 0x39, //0x00000e76 vmovdqu      %ymm0, (%rcx,%rdi)
	0x48, 0x83, 0xeb, 0x80, //0x00000e7b subq         $-128, %rbx
	0x49, 0x39, 0xd8, //0x00000e7f cmpq         %rbx, %r8
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00000e82 jne          LBB0_143
	//0x00000e88 LBB0_144
	0x49, 0x39, 0xd3, //0x00000e88 cmpq         %rdx, %r11
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x00000e8b je           LBB0_146
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e91 .p2align 4, 0x90
	//0x00000ea0 LBB0_145
	0xc6, 0x00, 0x30, //0x00000ea0 movb         $48, (%rax)
	0x48, 0x83, 0xc0, 0x01, //0x00000ea3 addq         $1, %rax
	0x83, 0xc2, 0x01, //0x00000ea7 addl         $1, %edx
	0x44, 0x39, 0xd2, //0x00000eaa cmpl         %r10d, %edx
	0x0f, 0x8c, 0xed, 0xff, 0xff, 0xff, //0x00000ead jl           LBB0_145
	//0x00000eb3 LBB0_146
	0x44, 0x29, 0xe8, //0x00000eb3 subl         %r13d, %eax
	//0x00000eb6 LBB0_147
	0x48, 0x83, 0xc4, 0x08, //0x00000eb6 addq         $8, %rsp
	0x5b, //0x00000eba popq         %rbx
	0x41, 0x5c, //0x00000ebb popq         %r12
	0x41, 0x5d, //0x00000ebd popq         %r13
	0x41, 0x5e, //0x00000ebf popq         %r14
	0x41, 0x5f, //0x00000ec1 popq         %r15
	0x5d, //0x00000ec3 popq         %rbp
	0xc5, 0xf8, 0x77, //0x00000ec4 vzeroupper   
	0xc3, //0x00000ec7 retq         
	//0x00000ec8 LBB0_148
	0x31, 0xc0, //0x00000ec8 xorl         %eax, %eax
	0xe9, 0xe7, 0xff, 0xff, 0xff, //0x00000eca jmp          LBB0_147
	//0x00000ecf LBB0_149
	0x48, 0x89, 0x7d, 0xd0, //0x00000ecf movq         %rdi, $-48(%rbp)
	0x41, 0xb8, 0x6b, 0xff, 0xff, 0xff, //0x00000ed3 movl         $-149, %r8d
	0x41, 0x89, 0xc7, //0x00000ed9 movl         %eax, %r15d
	0xe9, 0xd5, 0xf1, 0xff, 0xff, //0x00000edc jmp          LBB0_8
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000ee1 .p2align 4, 0x00
	//0x00000ef0 _Digits
	0x30, 0x30, 0x30, 0x31, 0x30, 0x32, 0x30, 0x33, 0x30, 0x34, 0x30, 0x35, 0x30, 0x36, 0x30, 0x37, //0x00000ef0 QUAD $0x3330323031303030; QUAD $0x3730363035303430  // .ascii 16, '0001020304050607'
	0x30, 0x38, 0x30, 0x39, 0x31, 0x30, 0x31, 0x31, 0x31, 0x32, 0x31, 0x33, 0x31, 0x34, 0x31, 0x35, //0x00000f00 QUAD $0x3131303139303830; QUAD $0x3531343133313231  // .ascii 16, '0809101112131415'
	0x31, 0x36, 0x31, 0x37, 0x31, 0x38, 0x31, 0x39, 0x32, 0x30, 0x32, 0x31, 0x32, 0x32, 0x32, 0x33, //0x00000f10 QUAD $0x3931383137313631; QUAD $0x3332323231323032  // .ascii 16, '1617181920212223'
	0x32, 0x34, 0x32, 0x35, 0x32, 0x36, 0x32, 0x37, 0x32, 0x38, 0x32, 0x39, 0x33, 0x30, 0x33, 0x31, //0x00000f20 QUAD $0x3732363235323432; QUAD $0x3133303339323832  // .ascii 16, '2425262728293031'
	0x33, 0x32, 0x33, 0x33, 0x33, 0x34, 0x33, 0x35, 0x33, 0x36, 0x33, 0x37, 0x33, 0x38, 0x33, 0x39, //0x00000f30 QUAD $0x3533343333333233; QUAD $0x3933383337333633  // .ascii 16, '3233343536373839'
	0x34, 0x30, 0x34, 0x31, 0x34, 0x32, 0x34, 0x33, 0x34, 0x34, 0x34, 0x35, 0x34, 0x36, 0x34, 0x37, //0x00000f40 QUAD $0x3334323431343034; QUAD $0x3734363435343434  // .ascii 16, '4041424344454647'
	0x34, 0x38, 0x34, 0x39, 0x35, 0x30, 0x35, 0x31, 0x35, 0x32, 0x35, 0x33, 0x35, 0x34, 0x35, 0x35, //0x00000f50 QUAD $0x3135303539343834; QUAD $0x3535343533353235  // .ascii 16, '4849505152535455'
	0x35, 0x36, 0x35, 0x37, 0x35, 0x38, 0x35, 0x39, 0x36, 0x30, 0x36, 0x31, 0x36, 0x32, 0x36, 0x33, //0x00000f60 QUAD $0x3935383537353635; QUAD $0x3336323631363036  // .ascii 16, '5657585960616263'
	0x36, 0x34, 0x36, 0x35, 0x36, 0x36, 0x36, 0x37, 0x36, 0x38, 0x36, 0x39, 0x37, 0x30, 0x37, 0x31, //0x00000f70 QUAD $0x3736363635363436; QUAD $0x3137303739363836  // .ascii 16, '6465666768697071'
	0x37, 0x32, 0x37, 0x33, 0x37, 0x34, 0x37, 0x35, 0x37, 0x36, 0x37, 0x37, 0x37, 0x38, 0x37, 0x39, //0x00000f80 QUAD $0x3537343733373237; QUAD $0x3937383737373637  // .ascii 16, '7273747576777879'
	0x38, 0x30, 0x38, 0x31, 0x38, 0x32, 0x38, 0x33, 0x38, 0x34, 0x38, 0x35, 0x38, 0x36, 0x38, 0x37, //0x00000f90 QUAD $0x3338323831383038; QUAD $0x3738363835383438  // .ascii 16, '8081828384858687'
	0x38, 0x38, 0x38, 0x39, 0x39, 0x30, 0x39, 0x31, 0x39, 0x32, 0x39, 0x33, 0x39, 0x34, 0x39, 0x35, //0x00000fa0 QUAD $0x3139303939383838; QUAD $0x3539343933393239  // .ascii 16, '8889909192939495'
	0x39, 0x36, 0x39, 0x37, 0x39, 0x38, 0x39, 0x39, //0x00000fb0 QUAD $0x3939383937393639  // .ascii 8, '96979899'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000fb8 .p2align 4, 0x00
	//0x00000fc0 _pow10_ceil_sig_f32.g
	0xf5, 0xfc, 0x43, 0x4b, 0x2c, 0xb3, 0xce, 0x81, //0x00000fc0 .quad -9093133594791772939
	0x32, 0xfc, 0x14, 0x5e, 0xf7, 0x5f, 0x42, 0xa2, //0x00000fc8 .quad -6754730975062328270
	0x3f, 0x3b, 0x9a, 0x35, 0xf5, 0xf7, 0xd2, 0xca, //0x00000fd0 .quad -3831727700400522433
	0x0e, 0xca, 0x00, 0x83, 0xf2, 0xb5, 0x87, 0xfd, //0x00000fd8 .quad -177973607073265138
	0x49, 0x7e, 0xe0, 0x91, 0xb7, 0xd1, 0x74, 0x9e, //0x00000fe0 .quad -7028762532061872567
	0xdb, 0x9d, 0x58, 0x76, 0x25, 0x06, 0x12, 0xc6, //0x00000fe8 .quad -4174267146649952805
	0x52, 0xc5, 0xee, 0xd3, 0xae, 0x87, 0x96, 0xf7, //0x00000ff0 .quad -606147914885053102
	0x53, 0x3b, 0x75, 0x44, 0xcd, 0x14, 0xbe, 0x9a, //0x00000ff8 .quad -7296371474444240045
	0x28, 0x8a, 0x92, 0x95, 0x00, 0x9a, 0x6d, 0xc1, //0x00001000 .quad -4508778324627912152
	0xb2, 0x2c, 0xf7, 0xba, 0x80, 0x00, 0xc9, 0xf1, //0x00001008 .quad -1024286887357502286
	0xef, 0x7b, 0xda, 0x74, 0x50, 0xa0, 0x1d, 0x97, //0x00001010 .quad -7557708332239520785
	0xeb, 0x1a, 0x11, 0x92, 0x64, 0x08, 0xe5, 0xbc, //0x00001018 .quad -4835449396872013077
	0xa6, 0x61, 0x95, 0xb6, 0x7d, 0x4a, 0x1e, 0xec, //0x00001020 .quad -1432625727662628442
	0x08, 0x5d, 0x1d, 0x92, 0x8e, 0xee, 0x92, 0x93, //0x00001028 .quad -7812920107430224632
	0x4a, 0xb4, 0xa4, 0x36, 0x32, 0xaa, 0x77, 0xb8, //0x00001030 .quad -5154464115860392886
	0x5c, 0xe1, 0x4d, 0xc4, 0xbe, 0x94, 0x95, 0xe6, //0x00001038 .quad -1831394126398103204
	0xda, 0xac, 0xb0, 0x3a, 0xf7, 0x7c, 0x1d, 0x90, //0x00001040 .quad -8062150356639896358
	0x10, 0xd8, 0x5c, 0x09, 0x35, 0xdc, 0x24, 0xb4, //0x00001048 .quad -5466001927372482544
	0x14, 0x0e, 0xb4, 0x4b, 0x42, 0x13, 0x2e, 0xe1, //0x00001050 .quad -2220816390788215276
	0xcc, 0x88, 0x50, 0x6f, 0x09, 0xcc, 0xbc, 0x8c, //0x00001058 .quad -8305539271883716404
	0xff, 0xaa, 0x24, 0xcb, 0x0b, 0xff, 0xeb, 0xaf, //0x00001060 .quad -5770238071427257601
	0xbf, 0xd5, 0xed, 0xbd, 0xce, 0xfe, 0xe6, 0xdb, //0x00001068 .quad -2601111570856684097
	0x98, 0xa5, 0xb4, 0x36, 0x41, 0x5f, 0x70, 0x89, //0x00001070 .quad -8543223759426509416
	0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00001078 .quad -6067343680855748867
	0xbd, 0x42, 0x7a, 0xe5, 0xd5, 0x94, 0xbf, 0xd6, //0x00001080 .quad -2972493582642298179
	0xb6, 0x69, 0x6c, 0xaf, 0x05, 0xbd, 0x37, 0x86, //0x00001088 .quad -8775337516792518218
	0x24, 0x84, 0x47, 0x1b, 0x47, 0xac, 0xc5, 0xa7, //0x00001090 .quad -6357485877563259868
	0x2c, 0x65, 0x19, 0xe2, 0x58, 0x17, 0xb7, 0xd1, //0x00001098 .quad -3335171328526686932
	0x3c, 0xdf, 0x4f, 0x8d, 0x97, 0x6e, 0x12, 0x83, //0x000010a0 .quad -9002011107970261188
	0x0b, 0xd7, 0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, //0x000010a8 .quad -6640827866535438581
	0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000010b0 .quad -3689348814741910323
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x000010b8 .quad -9223372036854775808
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, //0x000010c0 .quad -6917529027641081856
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, //0x000010c8 .quad -4035225266123964416
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x000010d0 .quad -432345564227567616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x9c, //0x000010d8 .quad -7187745005283311616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xc3, //0x000010e0 .quad -4372995238176751616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xf4, //0x000010e8 .quad -854558029293551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x96, 0x98, //0x000010f0 .quad -7451627795949551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xbc, 0xbe, //0x000010f8 .quad -4702848726509551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6b, 0xee, //0x00001100 .quad -1266874889709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x02, 0x95, //0x00001108 .quad -7709325833709551616
	0x00, 0x00, 0x00, 0x00, 0x40, 0xb7, 0x43, 0xba, //0x00001110 .quad -5024971273709551616
	0x00, 0x00, 0x00, 0x00, 0x10, 0xa5, 0xd4, 0xe8, //0x00001118 .quad -1669528073709551616
	0x00, 0x00, 0x00, 0x00, 0x2a, 0xe7, 0x84, 0x91, //0x00001120 .quad -7960984073709551616
	0x00, 0x00, 0x00, 0x80, 0xf4, 0x20, 0xe6, 0xb5, //0x00001128 .quad -5339544073709551616
	0x00, 0x00, 0x00, 0xa0, 0x31, 0xa9, 0x5f, 0xe3, //0x00001130 .quad -2062744073709551616
	0x00, 0x00, 0x00, 0x04, 0xbf, 0xc9, 0x1b, 0x8e, //0x00001138 .quad -8206744073709551616
	0x00, 0x00, 0x00, 0xc5, 0x2e, 0xbc, 0xa2, 0xb1, //0x00001140 .quad -5646744073709551616
	0x00, 0x00, 0x40, 0x76, 0x3a, 0x6b, 0x0b, 0xde, //0x00001148 .quad -2446744073709551616
	0x00, 0x00, 0xe8, 0x89, 0x04, 0x23, 0xc7, 0x8a, //0x00001150 .quad -8446744073709551616
	0x00, 0x00, 0x62, 0xac, 0xc5, 0xeb, 0x78, 0xad, //0x00001158 .quad -5946744073709551616
	0x00, 0x80, 0x7a, 0x17, 0xb7, 0x26, 0xd7, 0xd8, //0x00001160 .quad -2821744073709551616
	0x00, 0x90, 0xac, 0x6e, 0x32, 0x78, 0x86, 0x87, //0x00001168 .quad -8681119073709551616
	0x00, 0xb4, 0x57, 0x0a, 0x3f, 0x16, 0x68, 0xa9, //0x00001170 .quad -6239712823709551616
	0x00, 0xa1, 0xed, 0xcc, 0xce, 0x1b, 0xc2, 0xd3, //0x00001178 .quad -3187955011209551616
	0xa0, 0x84, 0x14, 0x40, 0x61, 0x51, 0x59, 0x84, //0x00001180 .quad -8910000909647051616
	0xc8, 0xa5, 0x19, 0x90, 0xb9, 0xa5, 0x6f, 0xa5, //0x00001188 .quad -6525815118631426616
	0x3a, 0x0f, 0x20, 0xf4, 0x27, 0x8f, 0xcb, 0xce, //0x00001190 .quad -3545582879861895366
	0x85, 0x09, 0x94, 0xf8, 0x78, 0x39, 0x3f, 0x81, //0x00001198 .quad -9133518327554766459
	0xe6, 0x0b, 0xb9, 0x36, 0xd7, 0x07, 0x8f, 0xa1, //0x000011a0 .quad -6805211891016070170
	0xdf, 0x4e, 0x67, 0x04, 0xcd, 0xc9, 0xf2, 0xc9, //0x000011a8 .quad -3894828845342699809
	0x97, 0x22, 0x81, 0x45, 0x40, 0x7c, 0x6f, 0xfc, //0x000011b0 .quad -256850038250986857
	0x9e, 0xb5, 0x70, 0x2b, 0xa8, 0xad, 0xc5, 0x9d, //0x000011b8 .quad -7078060301547948642
	0x06, 0xe3, 0x4c, 0x36, 0x12, 0x19, 0x37, 0xc5, //0x000011c0 .quad -4235889358507547898
	0xc7, 0x1b, 0xe0, 0xc3, 0x56, 0xdf, 0x84, 0xf6, //0x000011c8 .quad -683175679707046969
	0x5d, 0x11, 0x6c, 0x3a, 0x96, 0x0b, 0x13, 0x9a, //0x000011d0 .quad -7344513827457986211
	0xb4, 0x15, 0x07, 0xc9, 0x7b, 0xce, 0x97, 0xc0, //0x000011d8 .quad -4568956265895094860
	0x21, 0xdb, 0x48, 0xbb, 0x1a, 0xc2, 0xbd, 0xf0, //0x000011e0 .quad -1099509313941480671
	0xf5, 0x88, 0x0d, 0xb5, 0x50, 0x99, 0x76, 0x96, //0x000011e8 .quad -7604722348854507275
	0x32, 0xeb, 0x50, 0xe2, 0xa4, 0x3f, 0x14, 0xbc, //0x000011f0 .quad -4894216917640746190
	0xfe, 0x25, 0xe5, 0x1a, 0x8e, 0x4f, 0x19, 0xeb, //0x000011f8 .quad -1506085128623544834
	0xbf, 0x37, 0xcf, 0xd0, 0xb8, 0xd1, 0xef, 0x92, //0x00001200 .quad -7858832233030797377
	0xae, 0x05, 0x03, 0x05, 0x27, 0xc6, 0xab, 0xb7, //0x00001208 .quad -5211854272861108818
	0x1a, 0xc7, 0x43, 0xc6, 0xb0, 0xb7, 0x96, 0xe5, //0x00001210 .quad -1903131822648998118
	0x70, 0x5c, 0xea, 0x7b, 0xce, 0x32, 0x7e, 0x8f, //0x00001218 .quad -8106986416796705680
	0x8c, 0xf3, 0xe4, 0x1a, 0x82, 0xbf, 0x5d, 0xb3, //0x00001220 .quad -5522047002568494196
}
 
