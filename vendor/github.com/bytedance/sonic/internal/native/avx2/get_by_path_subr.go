// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__get_by_path = 640
)

const (
    _stack__get_by_path = 240
)

const (
    _size__get_by_path = 21184
)

var (
    _pcsp__get_by_path = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x14, 48},
        {0x4907, 240},
        {0x4908, 48},
        {0x490a, 40},
        {0x490c, 32},
        {0x490e, 24},
        {0x4910, 16},
        {0x4911, 8},
        {0x4915, 0},
        {0x52c0, 240},
    }
)

var _cfunc_get_by_path = []loader.CFunc{
    {"_get_by_path_entry", 0,  _entry__get_by_path, 0, nil},
    {"_get_by_path", _entry__get_by_path, _size__get_by_path, _stack__get_by_path, _pcsp__get_by_path},
}
