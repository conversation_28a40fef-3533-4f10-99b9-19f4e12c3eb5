// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_lspace = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 .p2align 4, 0x90
	//0x00000020 _lspace
	0x55, //0x00000020 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000021 movq         %rsp, %rbp
	0x48, 0x89, 0xd0, //0x00000024 movq         %rdx, %rax
	0x48, 0x89, 0xf1, //0x00000027 movq         %rsi, %rcx
	0x48, 0x29, 0xd1, //0x0000002a subq         %rdx, %rcx
	0x48, 0x83, 0xf9, 0x20, //0x0000002d cmpq         $32, %rcx
	0x0f, 0x82, 0xc8, 0x00, 0x00, 0x00, //0x00000031 jb           LBB0_1
	0x48, 0x89, 0xc1, //0x00000037 movq         %rax, %rcx
	0x48, 0xf7, 0xd9, //0x0000003a negq         %rcx
	0xc5, 0xfe, 0x6f, 0x05, 0xbb, 0xff, 0xff, 0xff, //0x0000003d vmovdqu      $-69(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000045 .p2align 4, 0x90
	//0x00000050 LBB0_10
	0xc5, 0xfe, 0x6f, 0x0c, 0x07, //0x00000050 vmovdqu      (%rdi,%rax), %ymm1
	0xc4, 0xe2, 0x7d, 0x00, 0xd1, //0x00000055 vpshufb      %ymm1, %ymm0, %ymm2
	0xc5, 0xf5, 0xf8, 0xda, //0x0000005a vpsubb       %ymm2, %ymm1, %ymm3
	0xc4, 0xe2, 0x7d, 0x17, 0xdb, //0x0000005e vptest       %ymm3, %ymm3
	0x0f, 0x85, 0x76, 0x00, 0x00, 0x00, //0x00000063 jne          LBB0_11
	0x48, 0x83, 0xc0, 0x20, //0x00000069 addq         $32, %rax
	0x48, 0x8d, 0x14, 0x0e, //0x0000006d leaq         (%rsi,%rcx), %rdx
	0x48, 0x83, 0xc2, 0xe0, //0x00000071 addq         $-32, %rdx
	0x48, 0x83, 0xc1, 0xe0, //0x00000075 addq         $-32, %rcx
	0x48, 0x83, 0xfa, 0x1f, //0x00000079 cmpq         $31, %rdx
	0x0f, 0x87, 0xcd, 0xff, 0xff, 0xff, //0x0000007d ja           LBB0_10
	0x48, 0x89, 0xf8, //0x00000083 movq         %rdi, %rax
	0x48, 0x29, 0xc8, //0x00000086 subq         %rcx, %rax
	0x48, 0x01, 0xce, //0x00000089 addq         %rcx, %rsi
	0x48, 0x89, 0xf1, //0x0000008c movq         %rsi, %rcx
	0x48, 0x85, 0xc9, //0x0000008f testq        %rcx, %rcx
	0x0f, 0x84, 0x3f, 0x00, 0x00, 0x00, //0x00000092 je           LBB0_14
	//0x00000098 LBB0_5
	0x4c, 0x8d, 0x04, 0x08, //0x00000098 leaq         (%rax,%rcx), %r8
	0x31, 0xd2, //0x0000009c xorl         %edx, %edx
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000009e movabsq      $4294977024, %r9
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000000a8 .p2align 4, 0x90
	//0x000000b0 LBB0_6
	0x0f, 0xbe, 0x34, 0x10, //0x000000b0 movsbl       (%rax,%rdx), %esi
	0x83, 0xfe, 0x20, //0x000000b4 cmpl         $32, %esi
	0x0f, 0x87, 0x37, 0x00, 0x00, 0x00, //0x000000b7 ja           LBB0_8
	0x49, 0x0f, 0xa3, 0xf1, //0x000000bd btq          %rsi, %r9
	0x0f, 0x83, 0x2d, 0x00, 0x00, 0x00, //0x000000c1 jae          LBB0_8
	0x48, 0x83, 0xc2, 0x01, //0x000000c7 addq         $1, %rdx
	0x48, 0x39, 0xd1, //0x000000cb cmpq         %rdx, %rcx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000000ce jne          LBB0_6
	0x4c, 0x89, 0xc0, //0x000000d4 movq         %r8, %rax
	//0x000000d7 LBB0_14
	0x48, 0x29, 0xf8, //0x000000d7 subq         %rdi, %rax
	0x5d, //0x000000da popq         %rbp
	0xc5, 0xf8, 0x77, //0x000000db vzeroupper   
	0xc3, //0x000000de retq         
	//0x000000df LBB0_11
	0xc5, 0xf5, 0x74, 0xc2, //0x000000df vpcmpeqb     %ymm2, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000000e3 vpmovmskb    %ymm0, %eax
	0xf7, 0xd0, //0x000000e7 notl         %eax
	0x0f, 0xbc, 0xc0, //0x000000e9 bsfl         %eax, %eax
	0x48, 0x29, 0xc8, //0x000000ec subq         %rcx, %rax
	0x5d, //0x000000ef popq         %rbp
	0xc5, 0xf8, 0x77, //0x000000f0 vzeroupper   
	0xc3, //0x000000f3 retq         
	//0x000000f4 LBB0_8
	0x48, 0x29, 0xf8, //0x000000f4 subq         %rdi, %rax
	0x48, 0x01, 0xd0, //0x000000f7 addq         %rdx, %rax
	0x5d, //0x000000fa popq         %rbp
	0xc5, 0xf8, 0x77, //0x000000fb vzeroupper   
	0xc3, //0x000000fe retq         
	//0x000000ff LBB0_1
	0x48, 0x01, 0xf8, //0x000000ff addq         %rdi, %rax
	0x48, 0x85, 0xc9, //0x00000102 testq        %rcx, %rcx
	0x0f, 0x85, 0x8d, 0xff, 0xff, 0xff, //0x00000105 jne          LBB0_5
	0xe9, 0xc7, 0xff, 0xff, 0xff, //0x0000010b jmp          LBB0_14
}
 
