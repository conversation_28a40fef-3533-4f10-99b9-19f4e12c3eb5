// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_skip_one_fast = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 LCPI0_1
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000020 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000030 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000040 LCPI0_2
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000040 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000050 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000060 LCPI0_3
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000060 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000070 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000080 LCPI0_7
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000080 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000090 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x000000a0 LCPI0_8
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000a0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000b0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x000000c0 LCPI0_9
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x000000c0 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x000000d0 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x000000e0 LCPI0_10
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x000000e0 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x000000f0 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000100 LCPI0_11
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000100 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000110 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000120 .p2align 4, 0x00
	//0x00000120 LCPI0_4
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000120 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000130 LCPI0_5
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000130 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000140 LCPI0_6
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000140 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000150 .p2align 4, 0x90
	//0x00000150 _skip_one_fast
	0x55, //0x00000150 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000151 movq         %rsp, %rbp
	0x41, 0x57, //0x00000154 pushq        %r15
	0x41, 0x56, //0x00000156 pushq        %r14
	0x41, 0x55, //0x00000158 pushq        %r13
	0x41, 0x54, //0x0000015a pushq        %r12
	0x53, //0x0000015c pushq        %rbx
	0x48, 0x81, 0xec, 0x80, 0x00, 0x00, 0x00, //0x0000015d subq         $128, %rsp
	0x4c, 0x8b, 0x37, //0x00000164 movq         (%rdi), %r14
	0x48, 0x8b, 0x5f, 0x08, //0x00000167 movq         $8(%rdi), %rbx
	0x48, 0x8b, 0x06, //0x0000016b movq         (%rsi), %rax
	0x48, 0x39, 0xd8, //0x0000016e cmpq         %rbx, %rax
	0x0f, 0x83, 0x2a, 0x00, 0x00, 0x00, //0x00000171 jae          LBB0_5
	0x41, 0x8a, 0x0c, 0x06, //0x00000177 movb         (%r14,%rax), %cl
	0x80, 0xf9, 0x0d, //0x0000017b cmpb         $13, %cl
	0x0f, 0x84, 0x1d, 0x00, 0x00, 0x00, //0x0000017e je           LBB0_5
	0x80, 0xf9, 0x20, //0x00000184 cmpb         $32, %cl
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x00000187 je           LBB0_5
	0x80, 0xc1, 0xf5, //0x0000018d addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000190 cmpb         $-2, %cl
	0x0f, 0x83, 0x08, 0x00, 0x00, 0x00, //0x00000193 jae          LBB0_5
	0x48, 0x89, 0xc1, //0x00000199 movq         %rax, %rcx
	0xe9, 0x85, 0x01, 0x00, 0x00, //0x0000019c jmp          LBB0_35
	//0x000001a1 LBB0_5
	0x48, 0x8d, 0x48, 0x01, //0x000001a1 leaq         $1(%rax), %rcx
	0x48, 0x39, 0xd9, //0x000001a5 cmpq         %rbx, %rcx
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000001a8 jae          LBB0_9
	0x41, 0x8a, 0x14, 0x0e, //0x000001ae movb         (%r14,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x000001b2 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000001b5 je           LBB0_9
	0x80, 0xfa, 0x20, //0x000001bb cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000001be je           LBB0_9
	0x80, 0xc2, 0xf5, //0x000001c4 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x000001c7 cmpb         $-2, %dl
	0x0f, 0x82, 0x56, 0x01, 0x00, 0x00, //0x000001ca jb           LBB0_35
	//0x000001d0 LBB0_9
	0x48, 0x8d, 0x48, 0x02, //0x000001d0 leaq         $2(%rax), %rcx
	0x48, 0x39, 0xd9, //0x000001d4 cmpq         %rbx, %rcx
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x000001d7 jae          LBB0_13
	0x41, 0x8a, 0x14, 0x0e, //0x000001dd movb         (%r14,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x000001e1 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x000001e4 je           LBB0_13
	0x80, 0xfa, 0x20, //0x000001ea cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000001ed je           LBB0_13
	0x80, 0xc2, 0xf5, //0x000001f3 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x000001f6 cmpb         $-2, %dl
	0x0f, 0x82, 0x27, 0x01, 0x00, 0x00, //0x000001f9 jb           LBB0_35
	//0x000001ff LBB0_13
	0x48, 0x8d, 0x48, 0x03, //0x000001ff leaq         $3(%rax), %rcx
	0x48, 0x39, 0xd9, //0x00000203 cmpq         %rbx, %rcx
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00000206 jae          LBB0_17
	0x41, 0x8a, 0x14, 0x0e, //0x0000020c movb         (%r14,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x00000210 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00000213 je           LBB0_17
	0x80, 0xfa, 0x20, //0x00000219 cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000021c je           LBB0_17
	0x80, 0xc2, 0xf5, //0x00000222 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00000225 cmpb         $-2, %dl
	0x0f, 0x82, 0xf8, 0x00, 0x00, 0x00, //0x00000228 jb           LBB0_35
	//0x0000022e LBB0_17
	0x48, 0x8d, 0x48, 0x04, //0x0000022e leaq         $4(%rax), %rcx
	0x48, 0x89, 0xda, //0x00000232 movq         %rbx, %rdx
	0x48, 0x29, 0xca, //0x00000235 subq         %rcx, %rdx
	0x0f, 0x86, 0xb9, 0x00, 0x00, 0x00, //0x00000238 jbe          LBB0_18
	0x48, 0x83, 0xfa, 0x20, //0x0000023e cmpq         $32, %rdx
	0x0f, 0x82, 0xb8, 0x09, 0x00, 0x00, //0x00000242 jb           LBB0_20
	0x48, 0xc7, 0xc2, 0xfc, 0xff, 0xff, 0xff, //0x00000248 movq         $-4, %rdx
	0x48, 0x29, 0xc2, //0x0000024f subq         %rax, %rdx
	0xc5, 0xfe, 0x6f, 0x05, 0xa6, 0xfd, 0xff, 0xff, //0x00000252 vmovdqu      $-602(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000025a .p2align 4, 0x90
	//0x00000260 LBB0_29
	0xc4, 0xc1, 0x7e, 0x6f, 0x0c, 0x0e, //0x00000260 vmovdqu      (%r14,%rcx), %ymm1
	0xc4, 0xe2, 0x7d, 0x00, 0xd1, //0x00000266 vpshufb      %ymm1, %ymm0, %ymm2
	0xc5, 0xf5, 0xf8, 0xda, //0x0000026b vpsubb       %ymm2, %ymm1, %ymm3
	0xc4, 0xe2, 0x7d, 0x17, 0xdb, //0x0000026f vptest       %ymm3, %ymm3
	0x0f, 0x85, 0x8c, 0x00, 0x00, 0x00, //0x00000274 jne          LBB0_30
	0x48, 0x83, 0xc1, 0x20, //0x0000027a addq         $32, %rcx
	0x48, 0x8d, 0x04, 0x13, //0x0000027e leaq         (%rbx,%rdx), %rax
	0x48, 0x83, 0xc0, 0xe0, //0x00000282 addq         $-32, %rax
	0x48, 0x83, 0xc2, 0xe0, //0x00000286 addq         $-32, %rdx
	0x48, 0x83, 0xf8, 0x1f, //0x0000028a cmpq         $31, %rax
	0x0f, 0x87, 0xcc, 0xff, 0xff, 0xff, //0x0000028e ja           LBB0_29
	0x4c, 0x89, 0xf1, //0x00000294 movq         %r14, %rcx
	0x48, 0x29, 0xd1, //0x00000297 subq         %rdx, %rcx
	0x48, 0x01, 0xda, //0x0000029a addq         %rbx, %rdx
	0x48, 0x85, 0xd2, //0x0000029d testq        %rdx, %rdx
	0x0f, 0x84, 0x39, 0x00, 0x00, 0x00, //0x000002a0 je           LBB0_33
	//0x000002a6 LBB0_24
	0x4c, 0x8d, 0x04, 0x11, //0x000002a6 leaq         (%rcx,%rdx), %r8
	0x45, 0x31, 0xd2, //0x000002aa xorl         %r10d, %r10d
	0x49, 0xb9, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000002ad movabsq      $4294977024, %r9
	//0x000002b7 LBB0_25
	0x42, 0x0f, 0xbe, 0x04, 0x11, //0x000002b7 movsbl       (%rcx,%r10), %eax
	0x83, 0xf8, 0x20, //0x000002bc cmpl         $32, %eax
	0x0f, 0x87, 0x14, 0x09, 0x00, 0x00, //0x000002bf ja           LBB0_27
	0x49, 0x0f, 0xa3, 0xc1, //0x000002c5 btq          %rax, %r9
	0x0f, 0x83, 0x0a, 0x09, 0x00, 0x00, //0x000002c9 jae          LBB0_27
	0x49, 0x83, 0xc2, 0x01, //0x000002cf addq         $1, %r10
	0x4c, 0x39, 0xd2, //0x000002d3 cmpq         %r10, %rdx
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x000002d6 jne          LBB0_25
	0x4c, 0x89, 0xc1, //0x000002dc movq         %r8, %rcx
	//0x000002df LBB0_33
	0x4c, 0x29, 0xf1, //0x000002df subq         %r14, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000002e2 movq         $-1, %rax
	0x48, 0x39, 0xd9, //0x000002e9 cmpq         %rbx, %rcx
	0x0f, 0x82, 0x34, 0x00, 0x00, 0x00, //0x000002ec jb           LBB0_35
	0xe9, 0x91, 0x01, 0x00, 0x00, //0x000002f2 jmp          LBB0_138
	//0x000002f7 LBB0_18
	0x48, 0x89, 0x0e, //0x000002f7 movq         %rcx, (%rsi)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000002fa movq         $-1, %rax
	0xe9, 0x82, 0x01, 0x00, 0x00, //0x00000301 jmp          LBB0_138
	//0x00000306 LBB0_30
	0xc5, 0xf5, 0x74, 0xc2, //0x00000306 vpcmpeqb     %ymm2, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x0000030a vpmovmskb    %ymm0, %eax
	0xf7, 0xd0, //0x0000030e notl         %eax
	0x0f, 0xbc, 0xc8, //0x00000310 bsfl         %eax, %ecx
	0x48, 0x29, 0xd1, //0x00000313 subq         %rdx, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000316 movq         $-1, %rax
	0x48, 0x39, 0xd9, //0x0000031d cmpq         %rbx, %rcx
	0x0f, 0x83, 0x62, 0x01, 0x00, 0x00, //0x00000320 jae          LBB0_138
	//0x00000326 LBB0_35
	0x48, 0x8d, 0x51, 0x01, //0x00000326 leaq         $1(%rcx), %rdx
	0x48, 0x89, 0x16, //0x0000032a movq         %rdx, (%rsi)
	0x41, 0x0f, 0xbe, 0x1c, 0x0e, //0x0000032d movsbl       (%r14,%rcx), %ebx
	0x83, 0xfb, 0x7b, //0x00000332 cmpl         $123, %ebx
	0x0f, 0x87, 0x72, 0x01, 0x00, 0x00, //0x00000335 ja           LBB0_134
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000033b movq         $-1, %rax
	0x4c, 0x8d, 0x05, 0x1f, 0x09, 0x00, 0x00, //0x00000342 leaq         $2335(%rip), %r8  /* LJTI0_0+0(%rip) */
	0x49, 0x63, 0x1c, 0x98, //0x00000349 movslq       (%r8,%rbx,4), %rbx
	0x4c, 0x01, 0xc3, //0x0000034d addq         %r8, %rbx
	0xff, 0xe3, //0x00000350 jmpq         *%rbx
	//0x00000352 LBB0_110
	0x48, 0x8b, 0x7f, 0x08, //0x00000352 movq         $8(%rdi), %rdi
	0x48, 0x89, 0xf8, //0x00000356 movq         %rdi, %rax
	0x48, 0x29, 0xd0, //0x00000359 subq         %rdx, %rax
	0x48, 0x83, 0xf8, 0x20, //0x0000035c cmpq         $32, %rax
	0x0f, 0x82, 0xab, 0x08, 0x00, 0x00, //0x00000360 jb           LBB0_111
	0x48, 0x89, 0xc8, //0x00000366 movq         %rcx, %rax
	0x48, 0xf7, 0xd0, //0x00000369 notq         %rax
	0xc5, 0xfe, 0x6f, 0x05, 0xac, 0xfc, 0xff, 0xff, //0x0000036c vmovdqu      $-852(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xc4, 0xfc, 0xff, 0xff, //0x00000374 vmovdqu      $-828(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0xdc, 0xfc, 0xff, 0xff, //0x0000037c vmovdqu      $-804(%rip), %ymm2  /* LCPI0_3+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000384 .p2align 4, 0x90
	//0x00000390 LBB0_119
	0xc4, 0xc1, 0x7e, 0x6f, 0x1c, 0x16, //0x00000390 vmovdqu      (%r14,%rdx), %ymm3
	0xc5, 0xe5, 0x74, 0xe0, //0x00000396 vpcmpeqb     %ymm0, %ymm3, %ymm4
	0xc5, 0xe5, 0xdb, 0xd9, //0x0000039a vpand        %ymm1, %ymm3, %ymm3
	0xc5, 0xe5, 0x74, 0xda, //0x0000039e vpcmpeqb     %ymm2, %ymm3, %ymm3
	0xc5, 0xe5, 0xeb, 0xdc, //0x000003a2 vpor         %ymm4, %ymm3, %ymm3
	0xc5, 0xfd, 0xd7, 0xdb, //0x000003a6 vpmovmskb    %ymm3, %ebx
	0x85, 0xdb, //0x000003aa testl        %ebx, %ebx
	0x0f, 0x85, 0xca, 0x00, 0x00, 0x00, //0x000003ac jne          LBB0_120
	0x48, 0x83, 0xc2, 0x20, //0x000003b2 addq         $32, %rdx
	0x48, 0x8d, 0x1c, 0x07, //0x000003b6 leaq         (%rdi,%rax), %rbx
	0x48, 0x83, 0xc3, 0xe0, //0x000003ba addq         $-32, %rbx
	0x48, 0x83, 0xc0, 0xe0, //0x000003be addq         $-32, %rax
	0x48, 0x83, 0xfb, 0x1f, //0x000003c2 cmpq         $31, %rbx
	0x0f, 0x87, 0xc4, 0xff, 0xff, 0xff, //0x000003c6 ja           LBB0_119
	0x4c, 0x89, 0xf2, //0x000003cc movq         %r14, %rdx
	0x48, 0x29, 0xc2, //0x000003cf subq         %rax, %rdx
	0x48, 0x01, 0xc7, //0x000003d2 addq         %rax, %rdi
	0x48, 0x89, 0xf8, //0x000003d5 movq         %rdi, %rax
	0x48, 0x83, 0xf8, 0x10, //0x000003d8 cmpq         $16, %rax
	0x0f, 0x82, 0x54, 0x00, 0x00, 0x00, //0x000003dc jb           LBB0_122
	//0x000003e2 LBB0_115
	0x4c, 0x89, 0xf7, //0x000003e2 movq         %r14, %rdi
	0x48, 0x29, 0xd7, //0x000003e5 subq         %rdx, %rdi
	0xc5, 0xfa, 0x6f, 0x05, 0x30, 0xfd, 0xff, 0xff, //0x000003e8 vmovdqu      $-720(%rip), %xmm0  /* LCPI0_4+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0x38, 0xfd, 0xff, 0xff, //0x000003f0 vmovdqu      $-712(%rip), %xmm1  /* LCPI0_5+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x40, 0xfd, 0xff, 0xff, //0x000003f8 vmovdqu      $-704(%rip), %xmm2  /* LCPI0_6+0(%rip) */
	//0x00000400 LBB0_116
	0xc5, 0xfa, 0x6f, 0x1a, //0x00000400 vmovdqu      (%rdx), %xmm3
	0xc5, 0xe1, 0x74, 0xe0, //0x00000404 vpcmpeqb     %xmm0, %xmm3, %xmm4
	0xc5, 0xe1, 0xdb, 0xd9, //0x00000408 vpand        %xmm1, %xmm3, %xmm3
	0xc5, 0xe1, 0x74, 0xda, //0x0000040c vpcmpeqb     %xmm2, %xmm3, %xmm3
	0xc5, 0xe1, 0xeb, 0xdc, //0x00000410 vpor         %xmm4, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xdb, //0x00000414 vpmovmskb    %xmm3, %ebx
	0x85, 0xdb, //0x00000418 testl        %ebx, %ebx
	0x0f, 0x85, 0xa7, 0x07, 0x00, 0x00, //0x0000041a jne          LBB0_117
	0x48, 0x83, 0xc2, 0x10, //0x00000420 addq         $16, %rdx
	0x48, 0x83, 0xc0, 0xf0, //0x00000424 addq         $-16, %rax
	0x48, 0x83, 0xc7, 0xf0, //0x00000428 addq         $-16, %rdi
	0x48, 0x83, 0xf8, 0x0f, //0x0000042c cmpq         $15, %rax
	0x0f, 0x87, 0xca, 0xff, 0xff, 0xff, //0x00000430 ja           LBB0_116
	//0x00000436 LBB0_122
	0x48, 0x85, 0xc0, //0x00000436 testq        %rax, %rax
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00000439 je           LBB0_130
	0x4c, 0x8d, 0x04, 0x02, //0x0000043f leaq         (%rdx,%rax), %r8
	0x31, 0xff, //0x00000443 xorl         %edi, %edi
	//0x00000445 LBB0_124
	0x0f, 0xb6, 0x1c, 0x3a, //0x00000445 movzbl       (%rdx,%rdi), %ebx
	0x80, 0xfb, 0x2c, //0x00000449 cmpb         $44, %bl
	0x0f, 0x84, 0xd1, 0x07, 0x00, 0x00, //0x0000044c je           LBB0_127
	0x80, 0xfb, 0x7d, //0x00000452 cmpb         $125, %bl
	0x0f, 0x84, 0xc8, 0x07, 0x00, 0x00, //0x00000455 je           LBB0_127
	0x80, 0xfb, 0x5d, //0x0000045b cmpb         $93, %bl
	0x0f, 0x84, 0xbf, 0x07, 0x00, 0x00, //0x0000045e je           LBB0_127
	0x48, 0x83, 0xc7, 0x01, //0x00000464 addq         $1, %rdi
	0x48, 0x39, 0xf8, //0x00000468 cmpq         %rdi, %rax
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x0000046b jne          LBB0_124
	0x4c, 0x89, 0xc2, //0x00000471 movq         %r8, %rdx
	//0x00000474 LBB0_130
	0x4c, 0x29, 0xf2, //0x00000474 subq         %r14, %rdx
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00000477 jmp          LBB0_136
	//0x0000047c LBB0_120
	0x0f, 0xbc, 0xd3, //0x0000047c bsfl         %ebx, %edx
	0x48, 0x29, 0xc2, //0x0000047f subq         %rax, %rdx
	//0x00000482 LBB0_136
	0x48, 0x89, 0x16, //0x00000482 movq         %rdx, (%rsi)
	//0x00000485 LBB0_137
	0x48, 0x89, 0xc8, //0x00000485 movq         %rcx, %rax
	//0x00000488 LBB0_138
	0x48, 0x8d, 0x65, 0xd8, //0x00000488 leaq         $-40(%rbp), %rsp
	0x5b, //0x0000048c popq         %rbx
	0x41, 0x5c, //0x0000048d popq         %r12
	0x41, 0x5d, //0x0000048f popq         %r13
	0x41, 0x5e, //0x00000491 popq         %r14
	0x41, 0x5f, //0x00000493 popq         %r15
	0x5d, //0x00000495 popq         %rbp
	0xc5, 0xf8, 0x77, //0x00000496 vzeroupper   
	0xc3, //0x00000499 retq         
	//0x0000049a LBB0_131
	0x48, 0x8d, 0x51, 0x04, //0x0000049a leaq         $4(%rcx), %rdx
	0x48, 0x3b, 0x57, 0x08, //0x0000049e cmpq         $8(%rdi), %rdx
	0x0f, 0x86, 0xda, 0xff, 0xff, 0xff, //0x000004a2 jbe          LBB0_136
	0xe9, 0xdb, 0xff, 0xff, 0xff, //0x000004a8 jmp          LBB0_138
	//0x000004ad LBB0_134
	0x48, 0x89, 0x0e, //0x000004ad movq         %rcx, (%rsi)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000004b0 movq         $-2, %rax
	0xe9, 0xcc, 0xff, 0xff, 0xff, //0x000004b7 jmp          LBB0_138
	//0x000004bc LBB0_89
	0x4c, 0x8b, 0x47, 0x08, //0x000004bc movq         $8(%rdi), %r8
	0x4d, 0x89, 0xc3, //0x000004c0 movq         %r8, %r11
	0x49, 0x29, 0xd3, //0x000004c3 subq         %rdx, %r11
	0x49, 0x83, 0xfb, 0x20, //0x000004c6 cmpq         $32, %r11
	0x0f, 0x8c, 0x5e, 0x07, 0x00, 0x00, //0x000004ca jl           LBB0_90
	0x4d, 0x8d, 0x0c, 0x0e, //0x000004d0 leaq         (%r14,%rcx), %r9
	0x49, 0x29, 0xc8, //0x000004d4 subq         %rcx, %r8
	0x41, 0xbd, 0x1f, 0x00, 0x00, 0x00, //0x000004d7 movl         $31, %r13d
	0x45, 0x31, 0xdb, //0x000004dd xorl         %r11d, %r11d
	0xc5, 0xfe, 0x6f, 0x05, 0x98, 0xfb, 0xff, 0xff, //0x000004e0 vmovdqu      $-1128(%rip), %ymm0  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xb0, 0xfb, 0xff, 0xff, //0x000004e8 vmovdqu      $-1104(%rip), %ymm1  /* LCPI0_8+0(%rip) */
	0x45, 0x31, 0xff, //0x000004f0 xorl         %r15d, %r15d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000004f3 .p2align 4, 0x90
	//0x00000500 LBB0_92
	0xc4, 0x81, 0x7e, 0x6f, 0x54, 0x19, 0x01, //0x00000500 vmovdqu      $1(%r9,%r11), %ymm2
	0xc5, 0xed, 0x74, 0xd8, //0x00000507 vpcmpeqb     %ymm0, %ymm2, %ymm3
	0xc5, 0x7d, 0xd7, 0xd3, //0x0000050b vpmovmskb    %ymm3, %r10d
	0xc5, 0xed, 0x74, 0xd1, //0x0000050f vpcmpeqb     %ymm1, %ymm2, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00000513 vpmovmskb    %ymm2, %edi
	0x85, 0xff, //0x00000517 testl        %edi, %edi
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00000519 jne          LBB0_95
	0x4d, 0x85, 0xff, //0x0000051f testq        %r15, %r15
	0x0f, 0x85, 0x08, 0x00, 0x00, 0x00, //0x00000522 jne          LBB0_95
	0x45, 0x31, 0xff, //0x00000528 xorl         %r15d, %r15d
	0xe9, 0x34, 0x00, 0x00, 0x00, //0x0000052b jmp          LBB0_96
	//0x00000530 .p2align 4, 0x90
	//0x00000530 LBB0_95
	0x44, 0x89, 0xfb, //0x00000530 movl         %r15d, %ebx
	0xf7, 0xd3, //0x00000533 notl         %ebx
	0x21, 0xfb, //0x00000535 andl         %edi, %ebx
	0x44, 0x8d, 0x24, 0x1b, //0x00000537 leal         (%rbx,%rbx), %r12d
	0x45, 0x09, 0xfc, //0x0000053b orl          %r15d, %r12d
	0x44, 0x89, 0xe2, //0x0000053e movl         %r12d, %edx
	0xf7, 0xd2, //0x00000541 notl         %edx
	0x21, 0xfa, //0x00000543 andl         %edi, %edx
	0x81, 0xe2, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000545 andl         $-1431655766, %edx
	0x45, 0x31, 0xff, //0x0000054b xorl         %r15d, %r15d
	0x01, 0xda, //0x0000054e addl         %ebx, %edx
	0x41, 0x0f, 0x92, 0xc7, //0x00000550 setb         %r15b
	0x01, 0xd2, //0x00000554 addl         %edx, %edx
	0x81, 0xf2, 0x55, 0x55, 0x55, 0x55, //0x00000556 xorl         $1431655765, %edx
	0x44, 0x21, 0xe2, //0x0000055c andl         %r12d, %edx
	0xf7, 0xd2, //0x0000055f notl         %edx
	0x41, 0x21, 0xd2, //0x00000561 andl         %edx, %r10d
	//0x00000564 LBB0_96
	0x4d, 0x85, 0xd2, //0x00000564 testq        %r10, %r10
	0x0f, 0x85, 0x01, 0x06, 0x00, 0x00, //0x00000567 jne          LBB0_97
	0x49, 0x83, 0xc3, 0x20, //0x0000056d addq         $32, %r11
	0x4b, 0x8d, 0x14, 0x28, //0x00000571 leaq         (%r8,%r13), %rdx
	0x48, 0x83, 0xc2, 0xe0, //0x00000575 addq         $-32, %rdx
	0x49, 0x83, 0xc5, 0xe0, //0x00000579 addq         $-32, %r13
	0x48, 0x83, 0xfa, 0x3f, //0x0000057d cmpq         $63, %rdx
	0x0f, 0x8f, 0x79, 0xff, 0xff, 0xff, //0x00000581 jg           LBB0_92
	0x4d, 0x85, 0xff, //0x00000587 testq        %r15, %r15
	0x0f, 0x85, 0xb2, 0x06, 0x00, 0x00, //0x0000058a jne          LBB0_102
	0x4b, 0x8d, 0x14, 0x0b, //0x00000590 leaq         (%r11,%r9), %rdx
	0x48, 0x83, 0xc2, 0x01, //0x00000594 addq         $1, %rdx
	0x49, 0xf7, 0xd3, //0x00000598 notq         %r11
	0x4d, 0x01, 0xc3, //0x0000059b addq         %r8, %r11
	//0x0000059e LBB0_104
	0x4d, 0x85, 0xdb, //0x0000059e testq        %r11, %r11
	0x0f, 0x8f, 0xfc, 0x05, 0x00, 0x00, //0x000005a1 jg           LBB0_105
	0xe9, 0xdc, 0xfe, 0xff, 0xff, //0x000005a7 jmp          LBB0_138
	//0x000005ac LBB0_37
	0x48, 0x8b, 0x5f, 0x08, //0x000005ac movq         $8(%rdi), %rbx
	0x48, 0x29, 0xd3, //0x000005b0 subq         %rdx, %rbx
	0x49, 0x01, 0xd6, //0x000005b3 addq         %rdx, %r14
	0x45, 0x31, 0xdb, //0x000005b6 xorl         %r11d, %r11d
	0xc5, 0xfe, 0x6f, 0x05, 0xdf, 0xfa, 0xff, 0xff, //0x000005b9 vmovdqu      $-1313(%rip), %ymm0  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xb7, 0xfa, 0xff, 0xff, //0x000005c1 vmovdqu      $-1353(%rip), %ymm1  /* LCPI0_7+0(%rip) */
	0xc5, 0xe9, 0x76, 0xd2, //0x000005c9 vpcmpeqd     %xmm2, %xmm2, %xmm2
	0xc5, 0xfe, 0x6f, 0x1d, 0x2b, 0xfb, 0xff, 0xff, //0x000005cd vmovdqu      $-1237(%rip), %ymm3  /* LCPI0_11+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x83, 0xfa, 0xff, 0xff, //0x000005d5 vmovdqu      $-1405(%rip), %ymm4  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x30, 0x57, 0xc9, //0x000005dd vxorps       %xmm9, %xmm9, %xmm9
	0x45, 0x31, 0xe4, //0x000005e2 xorl         %r12d, %r12d
	0x45, 0x31, 0xff, //0x000005e5 xorl         %r15d, %r15d
	0x45, 0x31, 0xc0, //0x000005e8 xorl         %r8d, %r8d
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x000005eb jmp          LBB0_38
	//0x000005f0 LBB0_47
	0x49, 0xc1, 0xfd, 0x3f, //0x000005f0 sarq         $63, %r13
	0xf3, 0x49, 0x0f, 0xb8, 0xd3, //0x000005f4 popcntq      %r11, %rdx
	0x49, 0x01, 0xd7, //0x000005f9 addq         %rdx, %r15
	0x49, 0x83, 0xc6, 0x40, //0x000005fc addq         $64, %r14
	0x48, 0x8b, 0x5c, 0x24, 0x18, //0x00000600 movq         $24(%rsp), %rbx
	0x48, 0x83, 0xc3, 0xc0, //0x00000605 addq         $-64, %rbx
	0x4d, 0x89, 0xeb, //0x00000609 movq         %r13, %r11
	//0x0000060c LBB0_38
	0x48, 0x83, 0xfb, 0x40, //0x0000060c cmpq         $64, %rbx
	0x48, 0x89, 0x5c, 0x24, 0x18, //0x00000610 movq         %rbx, $24(%rsp)
	0x0f, 0x8c, 0x22, 0x01, 0x00, 0x00, //0x00000615 jl           LBB0_48
	//0x0000061b LBB0_39
	0xc4, 0xc1, 0x7e, 0x6f, 0x3e, //0x0000061b vmovdqu      (%r14), %ymm7
	0xc4, 0xc1, 0x7e, 0x6f, 0x76, 0x20, //0x00000620 vmovdqu      $32(%r14), %ymm6
	0xc5, 0x45, 0x74, 0xc0, //0x00000626 vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc4, 0x41, 0x7d, 0xd7, 0xc8, //0x0000062a vpmovmskb    %ymm8, %r9d
	0xc5, 0x4d, 0x74, 0xc0, //0x0000062f vpcmpeqb     %ymm0, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xd8, //0x00000633 vpmovmskb    %ymm8, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x00000638 shlq         $32, %rbx
	0x49, 0x09, 0xd9, //0x0000063c orq          %rbx, %r9
	0x4c, 0x89, 0xcb, //0x0000063f movq         %r9, %rbx
	0x4c, 0x09, 0xe3, //0x00000642 orq          %r12, %rbx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00000645 jne          LBB0_41
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000064b movq         $-1, %r9
	0x45, 0x31, 0xe4, //0x00000652 xorl         %r12d, %r12d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00000655 jmp          LBB0_42
	//0x0000065a LBB0_41
	0x4c, 0x89, 0xe3, //0x0000065a movq         %r12, %rbx
	0x48, 0xf7, 0xd3, //0x0000065d notq         %rbx
	0x4c, 0x21, 0xcb, //0x00000660 andq         %r9, %rbx
	0x4c, 0x8d, 0x2c, 0x1b, //0x00000663 leaq         (%rbx,%rbx), %r13
	0x4d, 0x09, 0xe5, //0x00000667 orq          %r12, %r13
	0x4d, 0x89, 0xec, //0x0000066a movq         %r13, %r12
	0x49, 0xf7, 0xd4, //0x0000066d notq         %r12
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000670 movabsq      $-6148914691236517206, %rdx
	0x49, 0x21, 0xd1, //0x0000067a andq         %rdx, %r9
	0x4d, 0x21, 0xe1, //0x0000067d andq         %r12, %r9
	0x45, 0x31, 0xe4, //0x00000680 xorl         %r12d, %r12d
	0x49, 0x01, 0xd9, //0x00000683 addq         %rbx, %r9
	0x41, 0x0f, 0x92, 0xc4, //0x00000686 setb         %r12b
	0x4d, 0x01, 0xc9, //0x0000068a addq         %r9, %r9
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000068d movabsq      $6148914691236517205, %rdx
	0x49, 0x31, 0xd1, //0x00000697 xorq         %rdx, %r9
	0x4d, 0x21, 0xe9, //0x0000069a andq         %r13, %r9
	0x49, 0xf7, 0xd1, //0x0000069d notq         %r9
	//0x000006a0 LBB0_42
	0xc5, 0x4d, 0x74, 0xc1, //0x000006a0 vpcmpeqb     %ymm1, %ymm6, %ymm8
	0xc4, 0x41, 0x7d, 0xd7, 0xe8, //0x000006a4 vpmovmskb    %ymm8, %r13d
	0x49, 0xc1, 0xe5, 0x20, //0x000006a9 shlq         $32, %r13
	0xc5, 0x45, 0x74, 0xc1, //0x000006ad vpcmpeqb     %ymm1, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xd8, //0x000006b1 vpmovmskb    %ymm8, %ebx
	0x4c, 0x09, 0xeb, //0x000006b6 orq          %r13, %rbx
	0x4c, 0x21, 0xcb, //0x000006b9 andq         %r9, %rbx
	0xc4, 0xe1, 0xf9, 0x6e, 0xeb, //0x000006bc vmovq        %rbx, %xmm5
	0xc4, 0xe3, 0x51, 0x44, 0xea, 0x00, //0x000006c1 vpclmulqdq   $0, %xmm2, %xmm5, %xmm5
	0xc4, 0xc1, 0xf9, 0x7e, 0xed, //0x000006c7 vmovq        %xmm5, %r13
	0x4d, 0x31, 0xdd, //0x000006cc xorq         %r11, %r13
	0xc5, 0xc5, 0x74, 0xeb, //0x000006cf vpcmpeqb     %ymm3, %ymm7, %ymm5
	0xc5, 0x7d, 0xd7, 0xdd, //0x000006d3 vpmovmskb    %ymm5, %r11d
	0xc5, 0xcd, 0x74, 0xeb, //0x000006d7 vpcmpeqb     %ymm3, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xdd, //0x000006db vpmovmskb    %ymm5, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x000006df shlq         $32, %rbx
	0x49, 0x09, 0xdb, //0x000006e3 orq          %rbx, %r11
	0x4d, 0x89, 0xe9, //0x000006e6 movq         %r13, %r9
	0x49, 0xf7, 0xd1, //0x000006e9 notq         %r9
	0x4d, 0x21, 0xcb, //0x000006ec andq         %r9, %r11
	0xc5, 0xc5, 0x74, 0xec, //0x000006ef vpcmpeqb     %ymm4, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xdd, //0x000006f3 vpmovmskb    %ymm5, %ebx
	0xc5, 0xcd, 0x74, 0xec, //0x000006f7 vpcmpeqb     %ymm4, %ymm6, %ymm5
	0xc5, 0x7d, 0xd7, 0xd5, //0x000006fb vpmovmskb    %ymm5, %r10d
	0x49, 0xc1, 0xe2, 0x20, //0x000006ff shlq         $32, %r10
	0x4c, 0x09, 0xd3, //0x00000703 orq          %r10, %rbx
	0x4c, 0x21, 0xcb, //0x00000706 andq         %r9, %rbx
	0x0f, 0x84, 0xe1, 0xfe, 0xff, 0xff, //0x00000709 je           LBB0_47
	0x90, //0x0000070f .p2align 4, 0x90
	//0x00000710 LBB0_44
	0x4c, 0x8d, 0x4b, 0xff, //0x00000710 leaq         $-1(%rbx), %r9
	0x4c, 0x89, 0xca, //0x00000714 movq         %r9, %rdx
	0x4c, 0x21, 0xda, //0x00000717 andq         %r11, %rdx
	0xf3, 0x48, 0x0f, 0xb8, 0xd2, //0x0000071a popcntq      %rdx, %rdx
	0x4c, 0x01, 0xfa, //0x0000071f addq         %r15, %rdx
	0x4c, 0x39, 0xc2, //0x00000722 cmpq         %r8, %rdx
	0x0f, 0x86, 0x0e, 0x04, 0x00, 0x00, //0x00000725 jbe          LBB0_45
	0x49, 0x83, 0xc0, 0x01, //0x0000072b addq         $1, %r8
	0x4c, 0x21, 0xcb, //0x0000072f andq         %r9, %rbx
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00000732 jne          LBB0_44
	0xe9, 0xb3, 0xfe, 0xff, 0xff, //0x00000738 jmp          LBB0_47
	//0x0000073d LBB0_48
	0x48, 0x85, 0xdb, //0x0000073d testq        %rbx, %rbx
	0x0f, 0x8e, 0xf0, 0x04, 0x00, 0x00, //0x00000740 jle          LBB0_49
	0xc5, 0x7c, 0x11, 0x4c, 0x24, 0x40, //0x00000746 vmovups      %ymm9, $64(%rsp)
	0xc5, 0x7c, 0x11, 0x4c, 0x24, 0x20, //0x0000074c vmovups      %ymm9, $32(%rsp)
	0x44, 0x89, 0xf2, //0x00000752 movl         %r14d, %edx
	0x81, 0xe2, 0xff, 0x0f, 0x00, 0x00, //0x00000755 andl         $4095, %edx
	0x81, 0xfa, 0xc1, 0x0f, 0x00, 0x00, //0x0000075b cmpl         $4033, %edx
	0x0f, 0x82, 0xb4, 0xfe, 0xff, 0xff, //0x00000761 jb           LBB0_39
	0x48, 0x83, 0x7c, 0x24, 0x18, 0x20, //0x00000767 cmpq         $32, $24(%rsp)
	0x0f, 0x82, 0x22, 0x00, 0x00, 0x00, //0x0000076d jb           LBB0_52
	0xc4, 0xc1, 0x7c, 0x10, 0x2e, //0x00000773 vmovups      (%r14), %ymm5
	0xc5, 0xfc, 0x11, 0x6c, 0x24, 0x20, //0x00000778 vmovups      %ymm5, $32(%rsp)
	0x49, 0x83, 0xc6, 0x20, //0x0000077e addq         $32, %r14
	0x48, 0x8b, 0x54, 0x24, 0x18, //0x00000782 movq         $24(%rsp), %rdx
	0x48, 0x8d, 0x5a, 0xe0, //0x00000787 leaq         $-32(%rdx), %rbx
	0x4c, 0x8d, 0x4c, 0x24, 0x40, //0x0000078b leaq         $64(%rsp), %r9
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00000790 jmp          LBB0_54
	//0x00000795 LBB0_52
	0x4c, 0x8d, 0x4c, 0x24, 0x20, //0x00000795 leaq         $32(%rsp), %r9
	0x48, 0x8b, 0x5c, 0x24, 0x18, //0x0000079a movq         $24(%rsp), %rbx
	//0x0000079f LBB0_54
	0x48, 0x83, 0xfb, 0x10, //0x0000079f cmpq         $16, %rbx
	0x0f, 0x83, 0x34, 0x00, 0x00, 0x00, //0x000007a3 jae          LBB0_55
	0x48, 0x83, 0xfb, 0x08, //0x000007a9 cmpq         $8, %rbx
	0x0f, 0x83, 0x4a, 0x00, 0x00, 0x00, //0x000007ad jae          LBB0_57
	//0x000007b3 LBB0_58
	0x48, 0x83, 0xfb, 0x04, //0x000007b3 cmpq         $4, %rbx
	0x0f, 0x83, 0x5c, 0x00, 0x00, 0x00, //0x000007b7 jae          LBB0_59
	//0x000007bd LBB0_60
	0x48, 0x83, 0xfb, 0x02, //0x000007bd cmpq         $2, %rbx
	0x0f, 0x83, 0x6e, 0x00, 0x00, 0x00, //0x000007c1 jae          LBB0_61
	//0x000007c7 LBB0_62
	0x4c, 0x89, 0xf2, //0x000007c7 movq         %r14, %rdx
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x000007ca leaq         $32(%rsp), %r14
	0x48, 0x85, 0xdb, //0x000007cf testq        %rbx, %rbx
	0x0f, 0x85, 0x82, 0x00, 0x00, 0x00, //0x000007d2 jne          LBB0_63
	0xe9, 0x3e, 0xfe, 0xff, 0xff, //0x000007d8 jmp          LBB0_39
	//0x000007dd LBB0_55
	0xc4, 0xc1, 0x78, 0x10, 0x2e, //0x000007dd vmovups      (%r14), %xmm5
	0xc4, 0xc1, 0x78, 0x11, 0x29, //0x000007e2 vmovups      %xmm5, (%r9)
	0x49, 0x83, 0xc6, 0x10, //0x000007e7 addq         $16, %r14
	0x49, 0x83, 0xc1, 0x10, //0x000007eb addq         $16, %r9
	0x48, 0x83, 0xc3, 0xf0, //0x000007ef addq         $-16, %rbx
	0x48, 0x83, 0xfb, 0x08, //0x000007f3 cmpq         $8, %rbx
	0x0f, 0x82, 0xb6, 0xff, 0xff, 0xff, //0x000007f7 jb           LBB0_58
	//0x000007fd LBB0_57
	0x49, 0x8b, 0x16, //0x000007fd movq         (%r14), %rdx
	0x49, 0x89, 0x11, //0x00000800 movq         %rdx, (%r9)
	0x49, 0x83, 0xc6, 0x08, //0x00000803 addq         $8, %r14
	0x49, 0x83, 0xc1, 0x08, //0x00000807 addq         $8, %r9
	0x48, 0x83, 0xc3, 0xf8, //0x0000080b addq         $-8, %rbx
	0x48, 0x83, 0xfb, 0x04, //0x0000080f cmpq         $4, %rbx
	0x0f, 0x82, 0xa4, 0xff, 0xff, 0xff, //0x00000813 jb           LBB0_60
	//0x00000819 LBB0_59
	0x41, 0x8b, 0x16, //0x00000819 movl         (%r14), %edx
	0x41, 0x89, 0x11, //0x0000081c movl         %edx, (%r9)
	0x49, 0x83, 0xc6, 0x04, //0x0000081f addq         $4, %r14
	0x49, 0x83, 0xc1, 0x04, //0x00000823 addq         $4, %r9
	0x48, 0x83, 0xc3, 0xfc, //0x00000827 addq         $-4, %rbx
	0x48, 0x83, 0xfb, 0x02, //0x0000082b cmpq         $2, %rbx
	0x0f, 0x82, 0x92, 0xff, 0xff, 0xff, //0x0000082f jb           LBB0_62
	//0x00000835 LBB0_61
	0x41, 0x0f, 0xb7, 0x16, //0x00000835 movzwl       (%r14), %edx
	0x66, 0x41, 0x89, 0x11, //0x00000839 movw         %dx, (%r9)
	0x49, 0x83, 0xc6, 0x02, //0x0000083d addq         $2, %r14
	0x49, 0x83, 0xc1, 0x02, //0x00000841 addq         $2, %r9
	0x48, 0x83, 0xc3, 0xfe, //0x00000845 addq         $-2, %rbx
	0x4c, 0x89, 0xf2, //0x00000849 movq         %r14, %rdx
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x0000084c leaq         $32(%rsp), %r14
	0x48, 0x85, 0xdb, //0x00000851 testq        %rbx, %rbx
	0x0f, 0x84, 0xc1, 0xfd, 0xff, 0xff, //0x00000854 je           LBB0_39
	//0x0000085a LBB0_63
	0x8a, 0x12, //0x0000085a movb         (%rdx), %dl
	0x41, 0x88, 0x11, //0x0000085c movb         %dl, (%r9)
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x0000085f leaq         $32(%rsp), %r14
	0xe9, 0xb2, 0xfd, 0xff, 0xff, //0x00000864 jmp          LBB0_39
	//0x00000869 LBB0_133
	0x48, 0x8d, 0x51, 0x05, //0x00000869 leaq         $5(%rcx), %rdx
	0x48, 0x3b, 0x57, 0x08, //0x0000086d cmpq         $8(%rdi), %rdx
	0x0f, 0x86, 0x0b, 0xfc, 0xff, 0xff, //0x00000871 jbe          LBB0_136
	0xe9, 0x0c, 0xfc, 0xff, 0xff, //0x00000877 jmp          LBB0_138
	//0x0000087c LBB0_64
	0x48, 0x8b, 0x5f, 0x08, //0x0000087c movq         $8(%rdi), %rbx
	0x48, 0x29, 0xd3, //0x00000880 subq         %rdx, %rbx
	0x49, 0x01, 0xd6, //0x00000883 addq         %rdx, %r14
	0x45, 0x31, 0xdb, //0x00000886 xorl         %r11d, %r11d
	0xc5, 0xfe, 0x6f, 0x05, 0x0f, 0xf8, 0xff, 0xff, //0x00000889 vmovdqu      $-2033(%rip), %ymm0  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xe7, 0xf7, 0xff, 0xff, //0x00000891 vmovdqu      $-2073(%rip), %ymm1  /* LCPI0_7+0(%rip) */
	0xc5, 0xe9, 0x76, 0xd2, //0x00000899 vpcmpeqd     %xmm2, %xmm2, %xmm2
	0xc5, 0xfe, 0x6f, 0x1d, 0x1b, 0xf8, 0xff, 0xff, //0x0000089d vmovdqu      $-2021(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x33, 0xf8, 0xff, 0xff, //0x000008a5 vmovdqu      $-1997(%rip), %ymm4  /* LCPI0_10+0(%rip) */
	0xc4, 0x41, 0x30, 0x57, 0xc9, //0x000008ad vxorps       %xmm9, %xmm9, %xmm9
	0x45, 0x31, 0xe4, //0x000008b2 xorl         %r12d, %r12d
	0x45, 0x31, 0xff, //0x000008b5 xorl         %r15d, %r15d
	0x45, 0x31, 0xc0, //0x000008b8 xorl         %r8d, %r8d
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x000008bb jmp          LBB0_65
	//0x000008c0 LBB0_73
	0x49, 0xc1, 0xfd, 0x3f, //0x000008c0 sarq         $63, %r13
	0xf3, 0x49, 0x0f, 0xb8, 0xd3, //0x000008c4 popcntq      %r11, %rdx
	0x49, 0x01, 0xd7, //0x000008c9 addq         %rdx, %r15
	0x49, 0x83, 0xc6, 0x40, //0x000008cc addq         $64, %r14
	0x48, 0x8b, 0x5c, 0x24, 0x18, //0x000008d0 movq         $24(%rsp), %rbx
	0x48, 0x83, 0xc3, 0xc0, //0x000008d5 addq         $-64, %rbx
	0x4d, 0x89, 0xeb, //0x000008d9 movq         %r13, %r11
	//0x000008dc LBB0_65
	0x48, 0x83, 0xfb, 0x40, //0x000008dc cmpq         $64, %rbx
	0x48, 0x89, 0x5c, 0x24, 0x18, //0x000008e0 movq         %rbx, $24(%rsp)
	0x0f, 0x8c, 0x22, 0x01, 0x00, 0x00, //0x000008e5 jl           LBB0_74
	//0x000008eb LBB0_66
	0xc4, 0xc1, 0x7e, 0x6f, 0x3e, //0x000008eb vmovdqu      (%r14), %ymm7
	0xc4, 0xc1, 0x7e, 0x6f, 0x76, 0x20, //0x000008f0 vmovdqu      $32(%r14), %ymm6
	0xc5, 0x45, 0x74, 0xc0, //0x000008f6 vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc4, 0x41, 0x7d, 0xd7, 0xc8, //0x000008fa vpmovmskb    %ymm8, %r9d
	0xc5, 0x4d, 0x74, 0xc0, //0x000008ff vpcmpeqb     %ymm0, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xd8, //0x00000903 vpmovmskb    %ymm8, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x00000908 shlq         $32, %rbx
	0x49, 0x09, 0xd9, //0x0000090c orq          %rbx, %r9
	0x4c, 0x89, 0xcb, //0x0000090f movq         %r9, %rbx
	0x4c, 0x09, 0xe3, //0x00000912 orq          %r12, %rbx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00000915 jne          LBB0_68
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000091b movq         $-1, %r9
	0x45, 0x31, 0xe4, //0x00000922 xorl         %r12d, %r12d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00000925 jmp          LBB0_69
	//0x0000092a LBB0_68
	0x4c, 0x89, 0xe3, //0x0000092a movq         %r12, %rbx
	0x48, 0xf7, 0xd3, //0x0000092d notq         %rbx
	0x4c, 0x21, 0xcb, //0x00000930 andq         %r9, %rbx
	0x4c, 0x8d, 0x2c, 0x1b, //0x00000933 leaq         (%rbx,%rbx), %r13
	0x4d, 0x09, 0xe5, //0x00000937 orq          %r12, %r13
	0x4d, 0x89, 0xec, //0x0000093a movq         %r13, %r12
	0x49, 0xf7, 0xd4, //0x0000093d notq         %r12
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000940 movabsq      $-6148914691236517206, %rdx
	0x49, 0x21, 0xd1, //0x0000094a andq         %rdx, %r9
	0x4d, 0x21, 0xe1, //0x0000094d andq         %r12, %r9
	0x45, 0x31, 0xe4, //0x00000950 xorl         %r12d, %r12d
	0x49, 0x01, 0xd9, //0x00000953 addq         %rbx, %r9
	0x41, 0x0f, 0x92, 0xc4, //0x00000956 setb         %r12b
	0x4d, 0x01, 0xc9, //0x0000095a addq         %r9, %r9
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000095d movabsq      $6148914691236517205, %rdx
	0x49, 0x31, 0xd1, //0x00000967 xorq         %rdx, %r9
	0x4d, 0x21, 0xe9, //0x0000096a andq         %r13, %r9
	0x49, 0xf7, 0xd1, //0x0000096d notq         %r9
	//0x00000970 LBB0_69
	0xc5, 0x4d, 0x74, 0xc1, //0x00000970 vpcmpeqb     %ymm1, %ymm6, %ymm8
	0xc4, 0x41, 0x7d, 0xd7, 0xe8, //0x00000974 vpmovmskb    %ymm8, %r13d
	0x49, 0xc1, 0xe5, 0x20, //0x00000979 shlq         $32, %r13
	0xc5, 0x45, 0x74, 0xc1, //0x0000097d vpcmpeqb     %ymm1, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xd8, //0x00000981 vpmovmskb    %ymm8, %ebx
	0x4c, 0x09, 0xeb, //0x00000986 orq          %r13, %rbx
	0x4c, 0x21, 0xcb, //0x00000989 andq         %r9, %rbx
	0xc4, 0xe1, 0xf9, 0x6e, 0xeb, //0x0000098c vmovq        %rbx, %xmm5
	0xc4, 0xe3, 0x51, 0x44, 0xea, 0x00, //0x00000991 vpclmulqdq   $0, %xmm2, %xmm5, %xmm5
	0xc4, 0xc1, 0xf9, 0x7e, 0xed, //0x00000997 vmovq        %xmm5, %r13
	0x4d, 0x31, 0xdd, //0x0000099c xorq         %r11, %r13
	0xc5, 0xc5, 0x74, 0xeb, //0x0000099f vpcmpeqb     %ymm3, %ymm7, %ymm5
	0xc5, 0x7d, 0xd7, 0xdd, //0x000009a3 vpmovmskb    %ymm5, %r11d
	0xc5, 0xcd, 0x74, 0xeb, //0x000009a7 vpcmpeqb     %ymm3, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xdd, //0x000009ab vpmovmskb    %ymm5, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x000009af shlq         $32, %rbx
	0x49, 0x09, 0xdb, //0x000009b3 orq          %rbx, %r11
	0x4d, 0x89, 0xe9, //0x000009b6 movq         %r13, %r9
	0x49, 0xf7, 0xd1, //0x000009b9 notq         %r9
	0x4d, 0x21, 0xcb, //0x000009bc andq         %r9, %r11
	0xc5, 0xc5, 0x74, 0xec, //0x000009bf vpcmpeqb     %ymm4, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xdd, //0x000009c3 vpmovmskb    %ymm5, %ebx
	0xc5, 0xcd, 0x74, 0xec, //0x000009c7 vpcmpeqb     %ymm4, %ymm6, %ymm5
	0xc5, 0x7d, 0xd7, 0xd5, //0x000009cb vpmovmskb    %ymm5, %r10d
	0x49, 0xc1, 0xe2, 0x20, //0x000009cf shlq         $32, %r10
	0x4c, 0x09, 0xd3, //0x000009d3 orq          %r10, %rbx
	0x4c, 0x21, 0xcb, //0x000009d6 andq         %r9, %rbx
	0x0f, 0x84, 0xe1, 0xfe, 0xff, 0xff, //0x000009d9 je           LBB0_73
	0x90, //0x000009df .p2align 4, 0x90
	//0x000009e0 LBB0_71
	0x4c, 0x8d, 0x4b, 0xff, //0x000009e0 leaq         $-1(%rbx), %r9
	0x4c, 0x89, 0xca, //0x000009e4 movq         %r9, %rdx
	0x4c, 0x21, 0xda, //0x000009e7 andq         %r11, %rdx
	0xf3, 0x48, 0x0f, 0xb8, 0xd2, //0x000009ea popcntq      %rdx, %rdx
	0x4c, 0x01, 0xfa, //0x000009ef addq         %r15, %rdx
	0x4c, 0x39, 0xc2, //0x000009f2 cmpq         %r8, %rdx
	0x0f, 0x86, 0x3e, 0x01, 0x00, 0x00, //0x000009f5 jbe          LBB0_45
	0x49, 0x83, 0xc0, 0x01, //0x000009fb addq         $1, %r8
	0x4c, 0x21, 0xcb, //0x000009ff andq         %r9, %rbx
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00000a02 jne          LBB0_71
	0xe9, 0xb3, 0xfe, 0xff, 0xff, //0x00000a08 jmp          LBB0_73
	//0x00000a0d LBB0_74
	0x48, 0x85, 0xdb, //0x00000a0d testq        %rbx, %rbx
	0x0f, 0x8e, 0x20, 0x02, 0x00, 0x00, //0x00000a10 jle          LBB0_49
	0xc5, 0x7c, 0x11, 0x4c, 0x24, 0x40, //0x00000a16 vmovups      %ymm9, $64(%rsp)
	0xc5, 0x7c, 0x11, 0x4c, 0x24, 0x20, //0x00000a1c vmovups      %ymm9, $32(%rsp)
	0x44, 0x89, 0xf2, //0x00000a22 movl         %r14d, %edx
	0x81, 0xe2, 0xff, 0x0f, 0x00, 0x00, //0x00000a25 andl         $4095, %edx
	0x81, 0xfa, 0xc1, 0x0f, 0x00, 0x00, //0x00000a2b cmpl         $4033, %edx
	0x0f, 0x82, 0xb4, 0xfe, 0xff, 0xff, //0x00000a31 jb           LBB0_66
	0x48, 0x83, 0x7c, 0x24, 0x18, 0x20, //0x00000a37 cmpq         $32, $24(%rsp)
	0x0f, 0x82, 0x22, 0x00, 0x00, 0x00, //0x00000a3d jb           LBB0_77
	0xc4, 0xc1, 0x7c, 0x10, 0x2e, //0x00000a43 vmovups      (%r14), %ymm5
	0xc5, 0xfc, 0x11, 0x6c, 0x24, 0x20, //0x00000a48 vmovups      %ymm5, $32(%rsp)
	0x49, 0x83, 0xc6, 0x20, //0x00000a4e addq         $32, %r14
	0x48, 0x8b, 0x54, 0x24, 0x18, //0x00000a52 movq         $24(%rsp), %rdx
	0x48, 0x8d, 0x5a, 0xe0, //0x00000a57 leaq         $-32(%rdx), %rbx
	0x4c, 0x8d, 0x4c, 0x24, 0x40, //0x00000a5b leaq         $64(%rsp), %r9
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x00000a60 jmp          LBB0_79
	//0x00000a65 LBB0_77
	0x4c, 0x8d, 0x4c, 0x24, 0x20, //0x00000a65 leaq         $32(%rsp), %r9
	0x48, 0x8b, 0x5c, 0x24, 0x18, //0x00000a6a movq         $24(%rsp), %rbx
	//0x00000a6f LBB0_79
	0x48, 0x83, 0xfb, 0x10, //0x00000a6f cmpq         $16, %rbx
	0x0f, 0x83, 0x34, 0x00, 0x00, 0x00, //0x00000a73 jae          LBB0_80
	0x48, 0x83, 0xfb, 0x08, //0x00000a79 cmpq         $8, %rbx
	0x0f, 0x83, 0x4a, 0x00, 0x00, 0x00, //0x00000a7d jae          LBB0_82
	//0x00000a83 LBB0_83
	0x48, 0x83, 0xfb, 0x04, //0x00000a83 cmpq         $4, %rbx
	0x0f, 0x83, 0x5c, 0x00, 0x00, 0x00, //0x00000a87 jae          LBB0_84
	//0x00000a8d LBB0_85
	0x48, 0x83, 0xfb, 0x02, //0x00000a8d cmpq         $2, %rbx
	0x0f, 0x83, 0x6e, 0x00, 0x00, 0x00, //0x00000a91 jae          LBB0_86
	//0x00000a97 LBB0_87
	0x4c, 0x89, 0xf2, //0x00000a97 movq         %r14, %rdx
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x00000a9a leaq         $32(%rsp), %r14
	0x48, 0x85, 0xdb, //0x00000a9f testq        %rbx, %rbx
	0x0f, 0x85, 0x82, 0x00, 0x00, 0x00, //0x00000aa2 jne          LBB0_88
	0xe9, 0x3e, 0xfe, 0xff, 0xff, //0x00000aa8 jmp          LBB0_66
	//0x00000aad LBB0_80
	0xc4, 0xc1, 0x78, 0x10, 0x2e, //0x00000aad vmovups      (%r14), %xmm5
	0xc4, 0xc1, 0x78, 0x11, 0x29, //0x00000ab2 vmovups      %xmm5, (%r9)
	0x49, 0x83, 0xc6, 0x10, //0x00000ab7 addq         $16, %r14
	0x49, 0x83, 0xc1, 0x10, //0x00000abb addq         $16, %r9
	0x48, 0x83, 0xc3, 0xf0, //0x00000abf addq         $-16, %rbx
	0x48, 0x83, 0xfb, 0x08, //0x00000ac3 cmpq         $8, %rbx
	0x0f, 0x82, 0xb6, 0xff, 0xff, 0xff, //0x00000ac7 jb           LBB0_83
	//0x00000acd LBB0_82
	0x49, 0x8b, 0x16, //0x00000acd movq         (%r14), %rdx
	0x49, 0x89, 0x11, //0x00000ad0 movq         %rdx, (%r9)
	0x49, 0x83, 0xc6, 0x08, //0x00000ad3 addq         $8, %r14
	0x49, 0x83, 0xc1, 0x08, //0x00000ad7 addq         $8, %r9
	0x48, 0x83, 0xc3, 0xf8, //0x00000adb addq         $-8, %rbx
	0x48, 0x83, 0xfb, 0x04, //0x00000adf cmpq         $4, %rbx
	0x0f, 0x82, 0xa4, 0xff, 0xff, 0xff, //0x00000ae3 jb           LBB0_85
	//0x00000ae9 LBB0_84
	0x41, 0x8b, 0x16, //0x00000ae9 movl         (%r14), %edx
	0x41, 0x89, 0x11, //0x00000aec movl         %edx, (%r9)
	0x49, 0x83, 0xc6, 0x04, //0x00000aef addq         $4, %r14
	0x49, 0x83, 0xc1, 0x04, //0x00000af3 addq         $4, %r9
	0x48, 0x83, 0xc3, 0xfc, //0x00000af7 addq         $-4, %rbx
	0x48, 0x83, 0xfb, 0x02, //0x00000afb cmpq         $2, %rbx
	0x0f, 0x82, 0x92, 0xff, 0xff, 0xff, //0x00000aff jb           LBB0_87
	//0x00000b05 LBB0_86
	0x41, 0x0f, 0xb7, 0x16, //0x00000b05 movzwl       (%r14), %edx
	0x66, 0x41, 0x89, 0x11, //0x00000b09 movw         %dx, (%r9)
	0x49, 0x83, 0xc6, 0x02, //0x00000b0d addq         $2, %r14
	0x49, 0x83, 0xc1, 0x02, //0x00000b11 addq         $2, %r9
	0x48, 0x83, 0xc3, 0xfe, //0x00000b15 addq         $-2, %rbx
	0x4c, 0x89, 0xf2, //0x00000b19 movq         %r14, %rdx
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x00000b1c leaq         $32(%rsp), %r14
	0x48, 0x85, 0xdb, //0x00000b21 testq        %rbx, %rbx
	0x0f, 0x84, 0xc1, 0xfd, 0xff, 0xff, //0x00000b24 je           LBB0_66
	//0x00000b2a LBB0_88
	0x8a, 0x12, //0x00000b2a movb         (%rdx), %dl
	0x41, 0x88, 0x11, //0x00000b2c movb         %dl, (%r9)
	0x4c, 0x8d, 0x74, 0x24, 0x20, //0x00000b2f leaq         $32(%rsp), %r14
	0xe9, 0xb2, 0xfd, 0xff, 0xff, //0x00000b34 jmp          LBB0_66
	//0x00000b39 LBB0_45
	0x48, 0x8b, 0x47, 0x08, //0x00000b39 movq         $8(%rdi), %rax
	0x48, 0x0f, 0xbc, 0xd3, //0x00000b3d bsfq         %rbx, %rdx
	0x48, 0x2b, 0x54, 0x24, 0x18, //0x00000b41 subq         $24(%rsp), %rdx
	0x48, 0x01, 0xd0, //0x00000b46 addq         %rdx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00000b49 addq         $1, %rax
	0x48, 0x89, 0x06, //0x00000b4d movq         %rax, (%rsi)
	0x48, 0x8b, 0x57, 0x08, //0x00000b50 movq         $8(%rdi), %rdx
	0x48, 0x39, 0xd0, //0x00000b54 cmpq         %rdx, %rax
	0x48, 0x0f, 0x47, 0xc2, //0x00000b57 cmovaq       %rdx, %rax
	0x48, 0x89, 0x06, //0x00000b5b movq         %rax, (%rsi)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b5e movq         $-1, %rax
	0x48, 0x0f, 0x47, 0xc8, //0x00000b65 cmovaq       %rax, %rcx
	0xe9, 0x17, 0xf9, 0xff, 0xff, //0x00000b69 jmp          LBB0_137
	//0x00000b6e LBB0_97
	0x41, 0x0f, 0xbc, 0xc2, //0x00000b6e bsfl         %r10d, %eax
	0x48, 0x01, 0xc8, //0x00000b72 addq         %rcx, %rax
	0x4c, 0x01, 0xd8, //0x00000b75 addq         %r11, %rax
	0x48, 0x83, 0xc0, 0x02, //0x00000b78 addq         $2, %rax
	0x48, 0x89, 0x06, //0x00000b7c movq         %rax, (%rsi)
	0xe9, 0x01, 0xf9, 0xff, 0xff, //0x00000b7f jmp          LBB0_137
	//0x00000b84 LBB0_108
	0x48, 0xc7, 0xc7, 0xfe, 0xff, 0xff, 0xff, //0x00000b84 movq         $-2, %rdi
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00000b8b movl         $2, %eax
	//0x00000b90 LBB0_109
	0x48, 0x01, 0xc2, //0x00000b90 addq         %rax, %rdx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b93 movq         $-1, %rax
	0x49, 0x01, 0xfb, //0x00000b9a addq         %rdi, %r11
	0x0f, 0x8e, 0xe5, 0xf8, 0xff, 0xff, //0x00000b9d jle          LBB0_138
	//0x00000ba3 LBB0_105
	0x0f, 0xb6, 0x02, //0x00000ba3 movzbl       (%rdx), %eax
	0x3c, 0x5c, //0x00000ba6 cmpb         $92, %al
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00000ba8 je           LBB0_108
	0x3c, 0x22, //0x00000bae cmpb         $34, %al
	0x0f, 0x84, 0x3e, 0x00, 0x00, 0x00, //0x00000bb0 je           LBB0_135
	0x48, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000bb6 movq         $-1, %rdi
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000bbd movl         $1, %eax
	0xe9, 0xc9, 0xff, 0xff, 0xff, //0x00000bc2 jmp          LBB0_109
	//0x00000bc7 LBB0_117
	0x66, 0x0f, 0xbc, 0xc3, //0x00000bc7 bsfw         %bx, %ax
	0x0f, 0xb7, 0xc0, //0x00000bcb movzwl       %ax, %eax
	0x48, 0x29, 0xf8, //0x00000bce subq         %rdi, %rax
	0x48, 0x89, 0x06, //0x00000bd1 movq         %rax, (%rsi)
	0xe9, 0xac, 0xf8, 0xff, 0xff, //0x00000bd4 jmp          LBB0_137
	//0x00000bd9 LBB0_27
	0x4c, 0x29, 0xf1, //0x00000bd9 subq         %r14, %rcx
	0x4c, 0x01, 0xd1, //0x00000bdc addq         %r10, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000bdf movq         $-1, %rax
	0x48, 0x39, 0xd9, //0x00000be6 cmpq         %rbx, %rcx
	0x0f, 0x82, 0x37, 0xf7, 0xff, 0xff, //0x00000be9 jb           LBB0_35
	0xe9, 0x94, 0xf8, 0xff, 0xff, //0x00000bef jmp          LBB0_138
	//0x00000bf4 LBB0_135
	0x4c, 0x29, 0xf2, //0x00000bf4 subq         %r14, %rdx
	0x48, 0x83, 0xc2, 0x01, //0x00000bf7 addq         $1, %rdx
	0xe9, 0x82, 0xf8, 0xff, 0xff, //0x00000bfb jmp          LBB0_136
	//0x00000c00 LBB0_20
	0x4c, 0x01, 0xf1, //0x00000c00 addq         %r14, %rcx
	0x48, 0x85, 0xd2, //0x00000c03 testq        %rdx, %rdx
	0x0f, 0x85, 0x9a, 0xf6, 0xff, 0xff, //0x00000c06 jne          LBB0_24
	0xe9, 0xce, 0xf6, 0xff, 0xff, //0x00000c0c jmp          LBB0_33
	//0x00000c11 LBB0_111
	0x4c, 0x01, 0xf2, //0x00000c11 addq         %r14, %rdx
	0x48, 0x83, 0xf8, 0x10, //0x00000c14 cmpq         $16, %rax
	0x0f, 0x83, 0xc4, 0xf7, 0xff, 0xff, //0x00000c18 jae          LBB0_115
	0xe9, 0x13, 0xf8, 0xff, 0xff, //0x00000c1e jmp          LBB0_122
	//0x00000c23 LBB0_127
	0x4c, 0x29, 0xf2, //0x00000c23 subq         %r14, %rdx
	0x48, 0x01, 0xfa, //0x00000c26 addq         %rdi, %rdx
	0xe9, 0x54, 0xf8, 0xff, 0xff, //0x00000c29 jmp          LBB0_136
	//0x00000c2e LBB0_90
	0x4c, 0x01, 0xf2, //0x00000c2e addq         %r14, %rdx
	0xe9, 0x68, 0xf9, 0xff, 0xff, //0x00000c31 jmp          LBB0_104
	//0x00000c36 LBB0_49
	0x48, 0x8b, 0x4f, 0x08, //0x00000c36 movq         $8(%rdi), %rcx
	0x48, 0x89, 0x0e, //0x00000c3a movq         %rcx, (%rsi)
	0xe9, 0x46, 0xf8, 0xff, 0xff, //0x00000c3d jmp          LBB0_138
	//0x00000c42 LBB0_102
	0x49, 0x8d, 0x50, 0xff, //0x00000c42 leaq         $-1(%r8), %rdx
	0x4c, 0x39, 0xda, //0x00000c46 cmpq         %r11, %rdx
	0x0f, 0x84, 0x39, 0xf8, 0xff, 0xff, //0x00000c49 je           LBB0_138
	0x4b, 0x8d, 0x14, 0x0b, //0x00000c4f leaq         (%r11,%r9), %rdx
	0x48, 0x83, 0xc2, 0x02, //0x00000c53 addq         $2, %rdx
	0x4d, 0x29, 0xd8, //0x00000c57 subq         %r11, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00000c5a addq         $-2, %r8
	0x4d, 0x89, 0xc3, //0x00000c5e movq         %r8, %r11
	0xe9, 0x38, 0xf9, 0xff, 0xff, //0x00000c61 jmp          LBB0_104
	0x90, 0x90, //0x00000c66 .p2align 2, 0x90
	// // .set L0_0_set_138, LBB0_138-LJTI0_0
	// // .set L0_0_set_134, LBB0_134-LJTI0_0
	// // .set L0_0_set_89, LBB0_89-LJTI0_0
	// // .set L0_0_set_110, LBB0_110-LJTI0_0
	// // .set L0_0_set_37, LBB0_37-LJTI0_0
	// // .set L0_0_set_133, LBB0_133-LJTI0_0
	// // .set L0_0_set_131, LBB0_131-LJTI0_0
	// // .set L0_0_set_64, LBB0_64-LJTI0_0
	//0x00000c68 LJTI0_0
	0x20, 0xf8, 0xff, 0xff, //0x00000c68 .long L0_0_set_138
	0x45, 0xf8, 0xff, 0xff, //0x00000c6c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000c70 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000c74 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000c78 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000c7c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000c80 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000c84 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000c88 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000c8c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000c90 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000c94 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000c98 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000c9c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000ca0 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000ca4 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000ca8 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cac .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cb0 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cb4 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cb8 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cbc .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cc0 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cc4 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cc8 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000ccc .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cd0 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cd4 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cd8 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cdc .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000ce0 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000ce4 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000ce8 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cec .long L0_0_set_134
	0x54, 0xf8, 0xff, 0xff, //0x00000cf0 .long L0_0_set_89
	0x45, 0xf8, 0xff, 0xff, //0x00000cf4 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cf8 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000cfc .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d00 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d04 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d08 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d0c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d10 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d14 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d18 .long L0_0_set_134
	0xea, 0xf6, 0xff, 0xff, //0x00000d1c .long L0_0_set_110
	0x45, 0xf8, 0xff, 0xff, //0x00000d20 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d24 .long L0_0_set_134
	0xea, 0xf6, 0xff, 0xff, //0x00000d28 .long L0_0_set_110
	0xea, 0xf6, 0xff, 0xff, //0x00000d2c .long L0_0_set_110
	0xea, 0xf6, 0xff, 0xff, //0x00000d30 .long L0_0_set_110
	0xea, 0xf6, 0xff, 0xff, //0x00000d34 .long L0_0_set_110
	0xea, 0xf6, 0xff, 0xff, //0x00000d38 .long L0_0_set_110
	0xea, 0xf6, 0xff, 0xff, //0x00000d3c .long L0_0_set_110
	0xea, 0xf6, 0xff, 0xff, //0x00000d40 .long L0_0_set_110
	0xea, 0xf6, 0xff, 0xff, //0x00000d44 .long L0_0_set_110
	0xea, 0xf6, 0xff, 0xff, //0x00000d48 .long L0_0_set_110
	0xea, 0xf6, 0xff, 0xff, //0x00000d4c .long L0_0_set_110
	0x45, 0xf8, 0xff, 0xff, //0x00000d50 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d54 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d58 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d5c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d60 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d64 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d68 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d6c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d70 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d74 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d78 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d7c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d80 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d84 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d88 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d8c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d90 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d94 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d98 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000d9c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000da0 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000da4 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000da8 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000dac .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000db0 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000db4 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000db8 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000dbc .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000dc0 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000dc4 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000dc8 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000dcc .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000dd0 .long L0_0_set_134
	0x44, 0xf9, 0xff, 0xff, //0x00000dd4 .long L0_0_set_37
	0x45, 0xf8, 0xff, 0xff, //0x00000dd8 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000ddc .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000de0 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000de4 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000de8 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000dec .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000df0 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000df4 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000df8 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000dfc .long L0_0_set_134
	0x01, 0xfc, 0xff, 0xff, //0x00000e00 .long L0_0_set_133
	0x45, 0xf8, 0xff, 0xff, //0x00000e04 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e08 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e0c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e10 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e14 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e18 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e1c .long L0_0_set_134
	0x32, 0xf8, 0xff, 0xff, //0x00000e20 .long L0_0_set_131
	0x45, 0xf8, 0xff, 0xff, //0x00000e24 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e28 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e2c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e30 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e34 .long L0_0_set_134
	0x32, 0xf8, 0xff, 0xff, //0x00000e38 .long L0_0_set_131
	0x45, 0xf8, 0xff, 0xff, //0x00000e3c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e40 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e44 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e48 .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e4c .long L0_0_set_134
	0x45, 0xf8, 0xff, 0xff, //0x00000e50 .long L0_0_set_134
	0x14, 0xfc, 0xff, 0xff, //0x00000e54 .long L0_0_set_64
	//0x00000e58 .p2align 2, 0x00
	//0x00000e58 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000e58 .long 2
}
 
