// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_vnumber = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x00, 0x00, 0x30, 0x43, // .long 1127219200
	0x00, 0x00, 0x30, 0x45, //0x00000004 .long 1160773632
	0x00, 0x00, 0x00, 0x00, //0x00000008 .long 0
	0x00, 0x00, 0x00, 0x00, //0x0000000c .long 0
	//0x00000010 LCPI0_1
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x43, //0x00000010 .quad 0x4330000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x45, //0x00000018 .quad 0x4530000000000000
	//0x00000020 .p2align 3, 0x00
	//0x00000020 LCPI0_2
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x00000020 .quad 0x430c6bf526340000
	//0x00000028 LCPI0_3
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0xc3, //0x00000028 .quad 0xc30c6bf526340000
	//0x00000030 LCPI0_5
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000030 .quad 1
	//0x00000038 LCPI0_6
	0x10, 0x27, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000038 .quad 10000
	//0x00000040 LCPI0_7
	0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000040 .quad 10
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000048 .p2align 5, 0x00
	//0x00000060 LCPI0_4
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000060 QUAD $0x0000000000000000  // .space 8, '\x00\x00\x00\x00\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000068 .quad 1
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000070 .quad 1
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000078 .quad 1
	//0x00000080 .p2align 4, 0x90
	//0x00000080 _vnumber
	0x55, //0x00000080 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000081 movq         %rsp, %rbp
	0x41, 0x57, //0x00000084 pushq        %r15
	0x41, 0x56, //0x00000086 pushq        %r14
	0x41, 0x55, //0x00000088 pushq        %r13
	0x41, 0x54, //0x0000008a pushq        %r12
	0x53, //0x0000008c pushq        %rbx
	0x48, 0x83, 0xec, 0x58, //0x0000008d subq         $88, %rsp
	0x49, 0x89, 0xd6, //0x00000091 movq         %rdx, %r14
	0x4c, 0x8b, 0x2f, //0x00000094 movq         (%rdi), %r13
	0x48, 0x8b, 0x7f, 0x08, //0x00000097 movq         $8(%rdi), %rdi
	0x48, 0x8b, 0x0e, //0x0000009b movq         (%rsi), %rcx
	0x4c, 0x8b, 0x62, 0x20, //0x0000009e movq         $32(%rdx), %r12
	0x48, 0x8b, 0x5a, 0x28, //0x000000a2 movq         $40(%rdx), %rbx
	0x48, 0xc7, 0x02, 0x09, 0x00, 0x00, 0x00, //0x000000a6 movq         $9, (%rdx)
	0xc5, 0xf9, 0x57, 0xc0, //0x000000ad vxorpd       %xmm0, %xmm0, %xmm0
	0xc5, 0xf9, 0x11, 0x42, 0x08, //0x000000b1 vmovupd      %xmm0, $8(%rdx)
	0x48, 0x8b, 0x06, //0x000000b6 movq         (%rsi), %rax
	0x48, 0x89, 0x42, 0x18, //0x000000b9 movq         %rax, $24(%rdx)
	0x48, 0x39, 0xf9, //0x000000bd cmpq         %rdi, %rcx
	0x0f, 0x83, 0x3d, 0x00, 0x00, 0x00, //0x000000c0 jae          LBB0_4
	0x45, 0x8a, 0x4c, 0x0d, 0x00, //0x000000c6 movb         (%r13,%rcx), %r9b
	0x41, 0xbf, 0x01, 0x00, 0x00, 0x00, //0x000000cb movl         $1, %r15d
	0x41, 0x80, 0xf9, 0x2d, //0x000000d1 cmpb         $45, %r9b
	0x0f, 0x85, 0x37, 0x00, 0x00, 0x00, //0x000000d5 jne          LBB0_5
	0x48, 0x83, 0xc1, 0x01, //0x000000db addq         $1, %rcx
	0x48, 0x39, 0xf9, //0x000000df cmpq         %rdi, %rcx
	0x0f, 0x83, 0x1b, 0x00, 0x00, 0x00, //0x000000e2 jae          LBB0_4
	0x41, 0x8a, 0x54, 0x0d, 0x00, //0x000000e8 movb         (%r13,%rcx), %dl
	0x41, 0xbf, 0xff, 0xff, 0xff, 0xff, //0x000000ed movl         $-1, %r15d
	0x8d, 0x42, 0xc6, //0x000000f3 leal         $-58(%rdx), %eax
	0x3c, 0xf5, //0x000000f6 cmpb         $-11, %al
	0x0f, 0x86, 0x22, 0x00, 0x00, 0x00, //0x000000f8 jbe          LBB0_6
	0xe9, 0x2c, 0x00, 0x00, 0x00, //0x000000fe jmp          LBB0_7
	//0x00000103 LBB0_4
	0x48, 0x89, 0x3e, //0x00000103 movq         %rdi, (%rsi)
	0x49, 0xc7, 0x06, 0xff, 0xff, 0xff, 0xff, //0x00000106 movq         $-1, (%r14)
	0xe9, 0xdb, 0x0b, 0x00, 0x00, //0x0000010d jmp          LBB0_170
	//0x00000112 LBB0_5
	0x44, 0x89, 0xca, //0x00000112 movl         %r9d, %edx
	0x8d, 0x42, 0xc6, //0x00000115 leal         $-58(%rdx), %eax
	0x3c, 0xf5, //0x00000118 cmpb         $-11, %al
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x0000011a ja           LBB0_7
	//0x00000120 LBB0_6
	0x48, 0x89, 0x0e, //0x00000120 movq         %rcx, (%rsi)
	0x49, 0xc7, 0x06, 0xfe, 0xff, 0xff, 0xff, //0x00000123 movq         $-2, (%r14)
	0xe9, 0xbe, 0x0b, 0x00, 0x00, //0x0000012a jmp          LBB0_170
	//0x0000012f LBB0_7
	0x48, 0x89, 0x5d, 0xc0, //0x0000012f movq         %rbx, $-64(%rbp)
	0x80, 0xfa, 0x30, //0x00000133 cmpb         $48, %dl
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00000136 jne          LBB0_11
	0x48, 0x8d, 0x41, 0x01, //0x0000013c leaq         $1(%rcx), %rax
	0x48, 0x39, 0xf9, //0x00000140 cmpq         %rdi, %rcx
	0x0f, 0x83, 0xda, 0x00, 0x00, 0x00, //0x00000143 jae          LBB0_23
	0x41, 0x8a, 0x5c, 0x05, 0x00, //0x00000149 movb         (%r13,%rax), %bl
	0x80, 0xc3, 0xd2, //0x0000014e addb         $-46, %bl
	0x80, 0xfb, 0x37, //0x00000151 cmpb         $55, %bl
	0x0f, 0x87, 0xc9, 0x00, 0x00, 0x00, //0x00000154 ja           LBB0_23
	0x44, 0x0f, 0xb6, 0xc3, //0x0000015a movzbl       %bl, %r8d
	0x48, 0x89, 0xfb, //0x0000015e movq         %rdi, %rbx
	0x48, 0xbf, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000161 movabsq      $36028797027352577, %rdi
	0x4c, 0x0f, 0xa3, 0xc7, //0x0000016b btq          %r8, %rdi
	0x48, 0x89, 0xdf, //0x0000016f movq         %rbx, %rdi
	0x0f, 0x83, 0xab, 0x00, 0x00, 0x00, //0x00000172 jae          LBB0_23
	//0x00000178 LBB0_11
	0x41, 0xb3, 0x01, //0x00000178 movb         $1, %r11b
	0x48, 0x89, 0x7d, 0xb8, //0x0000017b movq         %rdi, $-72(%rbp)
	0x48, 0x39, 0xf9, //0x0000017f cmpq         %rdi, %rcx
	0x0f, 0x83, 0x8f, 0x00, 0x00, 0x00, //0x00000182 jae          LBB0_22
	0x41, 0xb8, 0xd0, 0xff, 0xff, 0xff, //0x00000188 movl         $4294967248, %r8d
	0x48, 0x83, 0xc1, 0x01, //0x0000018e addq         $1, %rcx
	0x45, 0x31, 0xd2, //0x00000192 xorl         %r10d, %r10d
	0x31, 0xdb, //0x00000195 xorl         %ebx, %ebx
	0x31, 0xff, //0x00000197 xorl         %edi, %edi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000199 .p2align 4, 0x90
	//0x000001a0 LBB0_13
	0x83, 0xfb, 0x12, //0x000001a0 cmpl         $18, %ebx
	0x0f, 0x8f, 0x17, 0x00, 0x00, 0x00, //0x000001a3 jg           LBB0_15
	0x48, 0x8d, 0x3c, 0xbf, //0x000001a9 leaq         (%rdi,%rdi,4), %rdi
	0x0f, 0xb6, 0xc2, //0x000001ad movzbl       %dl, %eax
	0x44, 0x01, 0xc0, //0x000001b0 addl         %r8d, %eax
	0x48, 0x8d, 0x3c, 0x78, //0x000001b3 leaq         (%rax,%rdi,2), %rdi
	0x83, 0xc3, 0x01, //0x000001b7 addl         $1, %ebx
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x000001ba jmp          LBB0_16
	0x90, //0x000001bf .p2align 4, 0x90
	//0x000001c0 LBB0_15
	0x41, 0x83, 0xc2, 0x01, //0x000001c0 addl         $1, %r10d
	//0x000001c4 LBB0_16
	0x48, 0x39, 0x4d, 0xb8, //0x000001c4 cmpq         %rcx, $-72(%rbp)
	0x0f, 0x84, 0x5d, 0x00, 0x00, 0x00, //0x000001c8 je           LBB0_24
	0x41, 0x0f, 0xb6, 0x54, 0x0d, 0x00, //0x000001ce movzbl       (%r13,%rcx), %edx
	0x8d, 0x42, 0xd0, //0x000001d4 leal         $-48(%rdx), %eax
	0x48, 0x83, 0xc1, 0x01, //0x000001d7 addq         $1, %rcx
	0x3c, 0x0a, //0x000001db cmpb         $10, %al
	0x0f, 0x82, 0xbd, 0xff, 0xff, 0xff, //0x000001dd jb           LBB0_13
	0x80, 0xfa, 0x2e, //0x000001e3 cmpb         $46, %dl
	0x0f, 0x85, 0x48, 0x00, 0x00, 0x00, //0x000001e6 jne          LBB0_25
	0x49, 0xc7, 0x06, 0x08, 0x00, 0x00, 0x00, //0x000001ec movq         $8, (%r14)
	0x48, 0x8b, 0x45, 0xb8, //0x000001f3 movq         $-72(%rbp), %rax
	0x48, 0x39, 0xc1, //0x000001f7 cmpq         %rax, %rcx
	0x0f, 0x83, 0xf8, 0x02, 0x00, 0x00, //0x000001fa jae          LBB0_69
	0x41, 0x8a, 0x44, 0x0d, 0x00, //0x00000200 movb         (%r13,%rcx), %al
	0x04, 0xc6, //0x00000205 addb         $-58, %al
	0x3c, 0xf5, //0x00000207 cmpb         $-11, %al
	0x0f, 0x86, 0x11, 0xff, 0xff, 0xff, //0x00000209 jbe          LBB0_6
	0x45, 0x31, 0xdb, //0x0000020f xorl         %r11d, %r11d
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x00000212 jmp          LBB0_26
	//0x00000217 LBB0_22
	0x45, 0x31, 0xd2, //0x00000217 xorl         %r10d, %r10d
	0x31, 0xdb, //0x0000021a xorl         %ebx, %ebx
	0x31, 0xff, //0x0000021c xorl         %edi, %edi
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x0000021e jmp          LBB0_26
	//0x00000223 LBB0_23
	0x48, 0x89, 0x06, //0x00000223 movq         %rax, (%rsi)
	0xe9, 0xc2, 0x0a, 0x00, 0x00, //0x00000226 jmp          LBB0_170
	//0x0000022b LBB0_24
	0x48, 0x8b, 0x4d, 0xb8, //0x0000022b movq         $-72(%rbp), %rcx
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x0000022f jmp          LBB0_26
	//0x00000234 LBB0_25
	0x48, 0x83, 0xc1, 0xff, //0x00000234 addq         $-1, %rcx
	//0x00000238 LBB0_26
	0x31, 0xc0, //0x00000238 xorl         %eax, %eax
	0x45, 0x85, 0xd2, //0x0000023a testl        %r10d, %r10d
	0x0f, 0x9f, 0xc0, //0x0000023d setg         %al
	0x89, 0x45, 0xc8, //0x00000240 movl         %eax, $-56(%rbp)
	0x48, 0x85, 0xff, //0x00000243 testq        %rdi, %rdi
	0x48, 0x89, 0x75, 0x90, //0x00000246 movq         %rsi, $-112(%rbp)
	0x0f, 0x85, 0x6f, 0x00, 0x00, 0x00, //0x0000024a jne          LBB0_35
	0x45, 0x85, 0xd2, //0x00000250 testl        %r10d, %r10d
	0x0f, 0x85, 0x66, 0x00, 0x00, 0x00, //0x00000253 jne          LBB0_35
	0x48, 0x8b, 0x45, 0xb8, //0x00000259 movq         $-72(%rbp), %rax
	0x48, 0x39, 0xc1, //0x0000025d cmpq         %rax, %rcx
	0x0f, 0x83, 0x47, 0x00, 0x00, 0x00, //0x00000260 jae          LBB0_33
	0x41, 0x89, 0xc8, //0x00000266 movl         %ecx, %r8d
	0x41, 0x29, 0xc0, //0x00000269 subl         %eax, %r8d
	0x31, 0xdb, //0x0000026c xorl         %ebx, %ebx
	0x45, 0x31, 0xd2, //0x0000026e xorl         %r10d, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000271 .p2align 4, 0x90
	//0x00000280 LBB0_30
	0x41, 0x80, 0x7c, 0x0d, 0x00, 0x30, //0x00000280 cmpb         $48, (%r13,%rcx)
	0x0f, 0x85, 0x2d, 0x00, 0x00, 0x00, //0x00000286 jne          LBB0_34
	0x48, 0x83, 0xc1, 0x01, //0x0000028c addq         $1, %rcx
	0x41, 0x83, 0xc2, 0xff, //0x00000290 addl         $-1, %r10d
	0x48, 0x39, 0xc8, //0x00000294 cmpq         %rcx, %rax
	0x0f, 0x85, 0xe3, 0xff, 0xff, 0xff, //0x00000297 jne          LBB0_30
	0x31, 0xff, //0x0000029d xorl         %edi, %edi
	0x45, 0x84, 0xdb, //0x0000029f testb        %r11b, %r11b
	0x0f, 0x85, 0x35, 0x01, 0x00, 0x00, //0x000002a2 jne          LBB0_54
	0xe9, 0x65, 0x01, 0x00, 0x00, //0x000002a8 jmp          LBB0_58
	//0x000002ad LBB0_33
	0x45, 0x31, 0xd2, //0x000002ad xorl         %r10d, %r10d
	0x31, 0xdb, //0x000002b0 xorl         %ebx, %ebx
	0x31, 0xff, //0x000002b2 xorl         %edi, %edi
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x000002b4 jmp          LBB0_35
	//0x000002b9 LBB0_34
	0x31, 0xff, //0x000002b9 xorl         %edi, %edi
	0x48, 0x8b, 0x75, 0x90, //0x000002bb movq         $-112(%rbp), %rsi
	//0x000002bf LBB0_35
	0x48, 0x3b, 0x4d, 0xb8, //0x000002bf cmpq         $-72(%rbp), %rcx
	0x0f, 0x83, 0x53, 0x00, 0x00, 0x00, //0x000002c3 jae          LBB0_41
	0x83, 0xfb, 0x12, //0x000002c9 cmpl         $18, %ebx
	0x0f, 0x8f, 0x4a, 0x00, 0x00, 0x00, //0x000002cc jg           LBB0_41
	0x41, 0xb8, 0xd0, 0xff, 0xff, 0xff, //0x000002d2 movl         $4294967248, %r8d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002d8 .p2align 4, 0x90
	//0x000002e0 LBB0_38
	0x41, 0x0f, 0xb6, 0x54, 0x0d, 0x00, //0x000002e0 movzbl       (%r13,%rcx), %edx
	0x8d, 0x42, 0xd0, //0x000002e6 leal         $-48(%rdx), %eax
	0x3c, 0x09, //0x000002e9 cmpb         $9, %al
	0x0f, 0x87, 0x2b, 0x00, 0x00, 0x00, //0x000002eb ja           LBB0_41
	0x48, 0x8d, 0x04, 0xbf, //0x000002f1 leaq         (%rdi,%rdi,4), %rax
	0x44, 0x01, 0xc2, //0x000002f5 addl         %r8d, %edx
	0x48, 0x8d, 0x3c, 0x42, //0x000002f8 leaq         (%rdx,%rax,2), %rdi
	0x41, 0x83, 0xc2, 0xff, //0x000002fc addl         $-1, %r10d
	0x48, 0x83, 0xc1, 0x01, //0x00000300 addq         $1, %rcx
	0x48, 0x3b, 0x4d, 0xb8, //0x00000304 cmpq         $-72(%rbp), %rcx
	0x0f, 0x83, 0x0e, 0x00, 0x00, 0x00, //0x00000308 jae          LBB0_41
	0x8d, 0x43, 0x01, //0x0000030e leal         $1(%rbx), %eax
	0x83, 0xfb, 0x12, //0x00000311 cmpl         $18, %ebx
	0x89, 0xc3, //0x00000314 movl         %eax, %ebx
	0x0f, 0x8c, 0xc4, 0xff, 0xff, 0xff, //0x00000316 jl           LBB0_38
	//0x0000031c LBB0_41
	0x48, 0x3b, 0x4d, 0xb8, //0x0000031c cmpq         $-72(%rbp), %rcx
	0x0f, 0x83, 0xa7, 0x00, 0x00, 0x00, //0x00000320 jae          LBB0_53
	0x41, 0x8a, 0x44, 0x0d, 0x00, //0x00000326 movb         (%r13,%rcx), %al
	0x8d, 0x50, 0xd0, //0x0000032b leal         $-48(%rax), %edx
	0x80, 0xfa, 0x09, //0x0000032e cmpb         $9, %dl
	0x0f, 0x87, 0x2f, 0x00, 0x00, 0x00, //0x00000331 ja           LBB0_47
	0x48, 0x8b, 0x45, 0xb8, //0x00000337 movq         $-72(%rbp), %rax
	0x48, 0x8d, 0x50, 0xff, //0x0000033b leaq         $-1(%rax), %rdx
	0x90, //0x0000033f .p2align 4, 0x90
	//0x00000340 LBB0_44
	0x48, 0x39, 0xca, //0x00000340 cmpq         %rcx, %rdx
	0x0f, 0x84, 0x88, 0x01, 0x00, 0x00, //0x00000343 je           LBB0_67
	0x41, 0x0f, 0xb6, 0x44, 0x0d, 0x01, //0x00000349 movzbl       $1(%r13,%rcx), %eax
	0x8d, 0x58, 0xd0, //0x0000034f leal         $-48(%rax), %ebx
	0x48, 0x83, 0xc1, 0x01, //0x00000352 addq         $1, %rcx
	0x80, 0xfb, 0x09, //0x00000356 cmpb         $9, %bl
	0x0f, 0x86, 0xe1, 0xff, 0xff, 0xff, //0x00000359 jbe          LBB0_44
	0xc7, 0x45, 0xc8, 0x01, 0x00, 0x00, 0x00, //0x0000035f movl         $1, $-56(%rbp)
	//0x00000366 LBB0_47
	0x0c, 0x20, //0x00000366 orb          $32, %al
	0x3c, 0x65, //0x00000368 cmpb         $101, %al
	0x0f, 0x85, 0x5d, 0x00, 0x00, 0x00, //0x0000036a jne          LBB0_53
	0x48, 0x8d, 0x41, 0x01, //0x00000370 leaq         $1(%rcx), %rax
	0x49, 0xc7, 0x06, 0x08, 0x00, 0x00, 0x00, //0x00000374 movq         $8, (%r14)
	0x48, 0x8b, 0x55, 0xb8, //0x0000037b movq         $-72(%rbp), %rdx
	0x48, 0x39, 0xd0, //0x0000037f cmpq         %rdx, %rax
	0x0f, 0x83, 0x61, 0x01, 0x00, 0x00, //0x00000382 jae          LBB0_68
	0x41, 0x8a, 0x5c, 0x05, 0x00, //0x00000388 movb         (%r13,%rax), %bl
	0x80, 0xfb, 0x2d, //0x0000038d cmpb         $45, %bl
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x00000390 je           LBB0_51
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00000396 movl         $1, %edx
	0x80, 0xfb, 0x2b, //0x0000039b cmpb         $43, %bl
	0x0f, 0x85, 0xd8, 0x04, 0x00, 0x00, //0x0000039e jne          LBB0_104
	//0x000003a4 LBB0_51
	0x48, 0x83, 0xc1, 0x02, //0x000003a4 addq         $2, %rcx
	0x48, 0x8b, 0x45, 0xb8, //0x000003a8 movq         $-72(%rbp), %rax
	0x48, 0x39, 0xc1, //0x000003ac cmpq         %rax, %rcx
	0x0f, 0x83, 0x43, 0x01, 0x00, 0x00, //0x000003af jae          LBB0_69
	0x31, 0xc0, //0x000003b5 xorl         %eax, %eax
	0x80, 0xfb, 0x2b, //0x000003b7 cmpb         $43, %bl
	0x0f, 0x94, 0xc0, //0x000003ba sete         %al
	0x8d, 0x14, 0x00, //0x000003bd leal         (%rax,%rax), %edx
	0x83, 0xc2, 0xff, //0x000003c0 addl         $-1, %edx
	0x41, 0x8a, 0x5c, 0x0d, 0x00, //0x000003c3 movb         (%r13,%rcx), %bl
	0xe9, 0xb2, 0x04, 0x00, 0x00, //0x000003c8 jmp          LBB0_105
	//0x000003cd LBB0_53
	0x45, 0x89, 0xd0, //0x000003cd movl         %r10d, %r8d
	0x48, 0x89, 0x4d, 0xb8, //0x000003d0 movq         %rcx, $-72(%rbp)
	0x45, 0x84, 0xdb, //0x000003d4 testb        %r11b, %r11b
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x000003d7 je           LBB0_58
	//0x000003dd LBB0_54
	0x45, 0x85, 0xc0, //0x000003dd testl        %r8d, %r8d
	0x0f, 0x85, 0x25, 0x00, 0x00, 0x00, //0x000003e0 jne          LBB0_57
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x000003e6 movabsq      $-9223372036854775808, %rax
	0x49, 0x63, 0xcf, //0x000003f0 movslq       %r15d, %rcx
	0x48, 0x85, 0xff, //0x000003f3 testq        %rdi, %rdi
	0x0f, 0x89, 0xb2, 0x02, 0x00, 0x00, //0x000003f6 jns          LBB0_90
	0x48, 0x89, 0xfa, //0x000003fc movq         %rdi, %rdx
	0x48, 0x21, 0xca, //0x000003ff andq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x00000402 cmpq         %rax, %rdx
	0x0f, 0x84, 0xa3, 0x02, 0x00, 0x00, //0x00000405 je           LBB0_90
	//0x0000040b LBB0_57
	0x49, 0xc7, 0x06, 0x08, 0x00, 0x00, 0x00, //0x0000040b movq         $8, (%r14)
	//0x00000412 LBB0_58
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00000412 movabsq      $4503599627370495, %rax
	0x48, 0x8d, 0x48, 0x01, //0x0000041c leaq         $1(%rax), %rcx
	0x48, 0x39, 0xcf, //0x00000420 cmpq         %rcx, %rdi
	0x0f, 0x83, 0xfd, 0x00, 0x00, 0x00, //0x00000423 jae          LBB0_72
	0xc4, 0xe1, 0xf9, 0x6e, 0xc7, //0x00000429 vmovq        %rdi, %xmm0
	0xc5, 0xf9, 0x62, 0x05, 0xca, 0xfb, 0xff, 0xff, //0x0000042e vpunpckldq   $-1078(%rip), %xmm0, %xmm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0x5c, 0x05, 0xd2, 0xfb, 0xff, 0xff, //0x00000436 vsubpd       $-1070(%rip), %xmm0, %xmm0  /* LCPI0_1+0(%rip) */
	0xc4, 0xe3, 0x79, 0x05, 0xc8, 0x01, //0x0000043e vpermilpd    $1, %xmm0, %xmm1
	0xc5, 0xf3, 0x58, 0xc0, //0x00000444 vaddsd       %xmm0, %xmm1, %xmm0
	0x41, 0xc1, 0xef, 0x1f, //0x00000448 shrl         $31, %r15d
	0x49, 0xc1, 0xe7, 0x3f, //0x0000044c shlq         $63, %r15
	0xc4, 0xc1, 0xf9, 0x6e, 0xcf, //0x00000450 vmovq        %r15, %xmm1
	0xc5, 0xf9, 0x56, 0xc1, //0x00000455 vorpd        %xmm1, %xmm0, %xmm0
	0x48, 0x85, 0xff, //0x00000459 testq        %rdi, %rdi
	0x0f, 0x84, 0x4a, 0x08, 0x00, 0x00, //0x0000045c je           LBB0_166
	0x45, 0x85, 0xc0, //0x00000462 testl        %r8d, %r8d
	0x0f, 0x84, 0x41, 0x08, 0x00, 0x00, //0x00000465 je           LBB0_166
	0x41, 0x8d, 0x40, 0xff, //0x0000046b leal         $-1(%r8), %eax
	0x83, 0xf8, 0x24, //0x0000046f cmpl         $36, %eax
	0x0f, 0x87, 0x8f, 0x00, 0x00, 0x00, //0x00000472 ja           LBB0_70
	0x48, 0x89, 0x4d, 0x98, //0x00000478 movq         %rcx, $-104(%rbp)
	0x44, 0x89, 0xc0, //0x0000047c movl         %r8d, %eax
	0x41, 0x83, 0xf8, 0x17, //0x0000047f cmpl         $23, %r8d
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x00000483 jb           LBB0_64
	0x41, 0x8d, 0x40, 0xea, //0x00000489 leal         $-22(%r8), %eax
	0x48, 0x8d, 0x0d, 0xec, 0x1d, 0x00, 0x00, //0x0000048d leaq         $7660(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xc5, 0xfb, 0x59, 0x04, 0xc1, //0x00000494 vmulsd       (%rcx,%rax,8), %xmm0, %xmm0
	0xb8, 0x16, 0x00, 0x00, 0x00, //0x00000499 movl         $22, %eax
	//0x0000049e LBB0_64
	0xc5, 0xf9, 0x2e, 0x05, 0x7a, 0xfb, 0xff, 0xff, //0x0000049e vucomisd     $-1158(%rip), %xmm0  /* LCPI0_2+0(%rip) */
	0x0f, 0x87, 0x90, 0x00, 0x00, 0x00, //0x000004a6 ja           LBB0_73
	0xc5, 0xfb, 0x10, 0x0d, 0x74, 0xfb, 0xff, 0xff, //0x000004ac vmovsd       $-1164(%rip), %xmm1  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0x2e, 0xc8, //0x000004b4 vucomisd     %xmm0, %xmm1
	0x0f, 0x87, 0x7e, 0x00, 0x00, 0x00, //0x000004b8 ja           LBB0_73
	0x89, 0xc0, //0x000004be movl         %eax, %eax
	0x48, 0x8d, 0x0d, 0xb9, 0x1d, 0x00, 0x00, //0x000004c0 leaq         $7609(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xc5, 0xfb, 0x59, 0x04, 0xc1, //0x000004c7 vmulsd       (%rcx,%rax,8), %xmm0, %xmm0
	0xe9, 0xdb, 0x07, 0x00, 0x00, //0x000004cc jmp          LBB0_166
	//0x000004d1 LBB0_67
	0xc7, 0x45, 0xc8, 0x01, 0x00, 0x00, 0x00, //0x000004d1 movl         $1, $-56(%rbp)
	0x45, 0x89, 0xd0, //0x000004d8 movl         %r10d, %r8d
	0x45, 0x84, 0xdb, //0x000004db testb        %r11b, %r11b
	0x0f, 0x85, 0xf9, 0xfe, 0xff, 0xff, //0x000004de jne          LBB0_54
	0xe9, 0x29, 0xff, 0xff, 0xff, //0x000004e4 jmp          LBB0_58
	//0x000004e9 LBB0_68
	0x48, 0x89, 0x16, //0x000004e9 movq         %rdx, (%rsi)
	0x49, 0xc7, 0x06, 0xff, 0xff, 0xff, 0xff, //0x000004ec movq         $-1, (%r14)
	0xe9, 0xf5, 0x07, 0x00, 0x00, //0x000004f3 jmp          LBB0_170
	//0x000004f8 LBB0_69
	0x48, 0x89, 0x06, //0x000004f8 movq         %rax, (%rsi)
	0x49, 0xc7, 0x06, 0xff, 0xff, 0xff, 0xff, //0x000004fb movq         $-1, (%r14)
	0xe9, 0xe6, 0x07, 0x00, 0x00, //0x00000502 jmp          LBB0_170
	//0x00000507 LBB0_70
	0x41, 0x83, 0xf8, 0xea, //0x00000507 cmpl         $-22, %r8d
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x0000050b jb           LBB0_72
	0x41, 0xf7, 0xd8, //0x00000511 negl         %r8d
	0x48, 0x8d, 0x05, 0x65, 0x1d, 0x00, 0x00, //0x00000514 leaq         $7525(%rip), %rax  /* _P10_TAB+0(%rip) */
	0xc4, 0xa1, 0x7b, 0x5e, 0x04, 0xc0, //0x0000051b vdivsd       (%rax,%r8,8), %xmm0, %xmm0
	0xe9, 0x86, 0x07, 0x00, 0x00, //0x00000521 jmp          LBB0_166
	//0x00000526 LBB0_72
	0x48, 0x89, 0x4d, 0x98, //0x00000526 movq         %rcx, $-104(%rbp)
	0x41, 0x8d, 0x80, 0xa4, 0xfe, 0xff, 0xff, //0x0000052a leal         $-348(%r8), %eax
	0x3d, 0x48, 0xfd, 0xff, 0xff, //0x00000531 cmpl         $-696, %eax
	0x0f, 0x82, 0x1d, 0x01, 0x00, 0x00, //0x00000536 jb           LBB0_85
	//0x0000053c LBB0_73
	0x48, 0x85, 0xff, //0x0000053c testq        %rdi, %rdi
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000053f je           LBB0_75
	0x48, 0x0f, 0xbd, 0xcf, //0x00000545 bsrq         %rdi, %rcx
	0x48, 0x83, 0xf1, 0x3f, //0x00000549 xorq         $63, %rcx
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x0000054d jmp          LBB0_76
	//0x00000552 LBB0_75
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x00000552 movl         $64, %ecx
	//0x00000557 LBB0_76
	0x48, 0x89, 0xfb, //0x00000557 movq         %rdi, %rbx
	0x49, 0x89, 0xca, //0x0000055a movq         %rcx, %r10
	0x48, 0xd3, 0xe3, //0x0000055d shlq         %cl, %rbx
	0x45, 0x8d, 0xb8, 0x5c, 0x01, 0x00, 0x00, //0x00000560 leal         $348(%r8), %r15d
	0x49, 0xc1, 0xe7, 0x04, //0x00000567 shlq         $4, %r15
	0x48, 0x8d, 0x05, 0xce, 0x1d, 0x00, 0x00, //0x0000056b leaq         $7630(%rip), %rax  /* _POW10_M128_TAB+0(%rip) */
	0x49, 0x8b, 0x44, 0x07, 0x08, //0x00000572 movq         $8(%r15,%rax), %rax
	0x48, 0x89, 0x45, 0xa0, //0x00000577 movq         %rax, $-96(%rbp)
	0x48, 0xf7, 0xe3, //0x0000057b mulq         %rbx
	0x48, 0x89, 0xc6, //0x0000057e movq         %rax, %rsi
	0x49, 0x89, 0xd3, //0x00000581 movq         %rdx, %r11
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x00000584 andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x0000058a cmpq         $511, %rdx
	0x0f, 0x85, 0x4d, 0x00, 0x00, 0x00, //0x00000591 jne          LBB0_81
	0x48, 0x89, 0xd9, //0x00000597 movq         %rbx, %rcx
	0x48, 0xf7, 0xd1, //0x0000059a notq         %rcx
	0x48, 0x39, 0xce, //0x0000059d cmpq         %rcx, %rsi
	0x0f, 0x86, 0x3e, 0x00, 0x00, 0x00, //0x000005a0 jbe          LBB0_81
	0x48, 0x89, 0xd8, //0x000005a6 movq         %rbx, %rax
	0x48, 0x8d, 0x15, 0x90, 0x1d, 0x00, 0x00, //0x000005a9 leaq         $7568(%rip), %rdx  /* _POW10_M128_TAB+0(%rip) */
	0x49, 0xf7, 0x24, 0x17, //0x000005b0 mulq         (%r15,%rdx)
	0x48, 0x01, 0xd6, //0x000005b4 addq         %rdx, %rsi
	0x49, 0x83, 0xd3, 0x00, //0x000005b7 adcq         $0, %r11
	0x44, 0x89, 0xda, //0x000005bb movl         %r11d, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x000005be andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x000005c4 cmpq         $511, %rdx
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x000005cb jne          LBB0_81
	0x48, 0x83, 0xfe, 0xff, //0x000005d1 cmpq         $-1, %rsi
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000005d5 jne          LBB0_81
	0x48, 0x39, 0xc8, //0x000005db cmpq         %rcx, %rax
	0x0f, 0x87, 0x75, 0x00, 0x00, 0x00, //0x000005de ja           LBB0_85
	//0x000005e4 LBB0_81
	0x4c, 0x89, 0xd8, //0x000005e4 movq         %r11, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x000005e7 shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x000005eb leal         $9(%rax), %ecx
	0x49, 0xd3, 0xeb, //0x000005ee shrq         %cl, %r11
	0x48, 0x85, 0xf6, //0x000005f1 testq        %rsi, %rsi
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x000005f4 jne          LBB0_84
	0x48, 0x85, 0xd2, //0x000005fa testq        %rdx, %rdx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x000005fd jne          LBB0_84
	0x44, 0x89, 0xd9, //0x00000603 movl         %r11d, %ecx
	0x83, 0xe1, 0x03, //0x00000606 andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x00000609 cmpl         $1, %ecx
	0x0f, 0x84, 0x47, 0x00, 0x00, 0x00, //0x0000060c je           LBB0_85
	//0x00000612 LBB0_84
	0x41, 0x69, 0xc8, 0x6a, 0x52, 0x03, 0x00, //0x00000612 imull        $217706, %r8d, %ecx
	0xc1, 0xf9, 0x10, //0x00000619 sarl         $16, %ecx
	0x81, 0xc1, 0x3f, 0x04, 0x00, 0x00, //0x0000061c addl         $1087, %ecx
	0x48, 0x63, 0xd9, //0x00000622 movslq       %ecx, %rbx
	0x48, 0x89, 0xde, //0x00000625 movq         %rbx, %rsi
	0x4c, 0x29, 0xd6, //0x00000628 subq         %r10, %rsi
	0x44, 0x89, 0xda, //0x0000062b movl         %r11d, %edx
	0x83, 0xe2, 0x01, //0x0000062e andl         $1, %edx
	0x4c, 0x01, 0xda, //0x00000631 addq         %r11, %rdx
	0x48, 0x89, 0xd1, //0x00000634 movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x36, //0x00000637 shrq         $54, %rcx
	0x48, 0x01, 0xc6, //0x0000063b addq         %rax, %rsi
	0x48, 0x83, 0xf9, 0x01, //0x0000063e cmpq         $1, %rcx
	0x48, 0x83, 0xde, 0x00, //0x00000642 sbbq         $0, %rsi
	0x48, 0x8d, 0x86, 0x01, 0xf8, 0xff, 0xff, //0x00000646 leaq         $-2047(%rsi), %rax
	0x48, 0x3d, 0x02, 0xf8, 0xff, 0xff, //0x0000064d cmpq         $-2046, %rax
	0x0f, 0x83, 0x90, 0x00, 0x00, 0x00, //0x00000653 jae          LBB0_91
	//0x00000659 LBB0_85
	0x48, 0x8b, 0x45, 0x90, //0x00000659 movq         $-112(%rbp), %rax
	0x48, 0x8b, 0x08, //0x0000065d movq         (%rax), %rcx
	0x4e, 0x8d, 0x04, 0x29, //0x00000660 leaq         (%rcx,%r13), %r8
	0x4c, 0x8b, 0x4d, 0xb8, //0x00000664 movq         $-72(%rbp), %r9
	0x49, 0x29, 0xc9, //0x00000668 subq         %rcx, %r9
	0x48, 0x8b, 0x5d, 0xc0, //0x0000066b movq         $-64(%rbp), %rbx
	0x48, 0x85, 0xdb, //0x0000066f testq        %rbx, %rbx
	0x0f, 0x84, 0xfa, 0x03, 0x00, 0x00, //0x00000672 je           LBB0_126
	0x41, 0xc6, 0x04, 0x24, 0x00, //0x00000678 movb         $0, (%r12)
	0x48, 0x83, 0xfb, 0x01, //0x0000067d cmpq         $1, %rbx
	0x0f, 0x84, 0xeb, 0x03, 0x00, 0x00, //0x00000681 je           LBB0_126
	0x48, 0x8d, 0x53, 0xff, //0x00000687 leaq         $-1(%rbx), %rdx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000068b movl         $1, %eax
	0x48, 0x83, 0xfa, 0x10, //0x00000690 cmpq         $16, %rdx
	0x0f, 0x82, 0xc6, 0x03, 0x00, 0x00, //0x00000694 jb           LBB0_125
	0x48, 0x81, 0xfa, 0x80, 0x00, 0x00, 0x00, //0x0000069a cmpq         $128, %rdx
	0x0f, 0x83, 0x36, 0x02, 0x00, 0x00, //0x000006a1 jae          LBB0_110
	0x31, 0xff, //0x000006a7 xorl         %edi, %edi
	0xe9, 0x60, 0x03, 0x00, 0x00, //0x000006a9 jmp          LBB0_119
	//0x000006ae LBB0_90
	0xc4, 0xe1, 0xf9, 0x6e, 0xc7, //0x000006ae vmovq        %rdi, %xmm0
	0x48, 0x0f, 0xaf, 0xf9, //0x000006b3 imulq        %rcx, %rdi
	0xc5, 0xf9, 0x62, 0x05, 0x41, 0xf9, 0xff, 0xff, //0x000006b7 vpunpckldq   $-1727(%rip), %xmm0, %xmm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0x5c, 0x05, 0x49, 0xf9, 0xff, 0xff, //0x000006bf vsubpd       $-1719(%rip), %xmm0, %xmm0  /* LCPI0_1+0(%rip) */
	0x49, 0x89, 0x7e, 0x10, //0x000006c7 movq         %rdi, $16(%r14)
	0xc4, 0xe3, 0x79, 0x05, 0xc8, 0x01, //0x000006cb vpermilpd    $1, %xmm0, %xmm1
	0xc5, 0xf3, 0x58, 0xc0, //0x000006d1 vaddsd       %xmm0, %xmm1, %xmm0
	0x48, 0x21, 0xc8, //0x000006d5 andq         %rcx, %rax
	0xc4, 0xe1, 0xf9, 0x7e, 0xc1, //0x000006d8 vmovq        %xmm0, %rcx
	0x48, 0x09, 0xc1, //0x000006dd orq          %rax, %rcx
	0x49, 0x89, 0x4e, 0x08, //0x000006e0 movq         %rcx, $8(%r14)
	0xe9, 0xf9, 0x05, 0x00, 0x00, //0x000006e4 jmp          LBB0_169
	//0x000006e9 LBB0_91
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, //0x000006e9 movabsq      $18014398509481984, %rax
	0x48, 0x39, 0xc2, //0x000006f3 cmpq         %rax, %rdx
	0xb1, 0x02, //0x000006f6 movb         $2, %cl
	0x80, 0xd9, 0x00, //0x000006f8 sbbb         $0, %cl
	0x48, 0xd3, 0xea, //0x000006fb shrq         %cl, %rdx
	0x48, 0xc1, 0xe6, 0x34, //0x000006fe shlq         $52, %rsi
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00000702 movabsq      $4503599627370495, %rax
	0x48, 0x21, 0xc2, //0x0000070c andq         %rax, %rdx
	0x48, 0x09, 0xf2, //0x0000070f orq          %rsi, %rdx
	0x48, 0x89, 0xd0, //0x00000712 movq         %rdx, %rax
	0x48, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00000715 movabsq      $-9223372036854775808, %rcx
	0x48, 0x09, 0xc8, //0x0000071f orq          %rcx, %rax
	0x41, 0x80, 0xf9, 0x2d, //0x00000722 cmpb         $45, %r9b
	0x48, 0x0f, 0x45, 0xc2, //0x00000726 cmovneq      %rdx, %rax
	0xc4, 0xe1, 0xf9, 0x6e, 0xc0, //0x0000072a vmovq        %rax, %xmm0
	0x83, 0x7d, 0xc8, 0x00, //0x0000072f cmpl         $0, $-56(%rbp)
	0x0f, 0x84, 0x73, 0x05, 0x00, 0x00, //0x00000733 je           LBB0_166
	0x41, 0xbb, 0x40, 0x00, 0x00, 0x00, //0x00000739 movl         $64, %r11d
	0x48, 0xff, 0xc7, //0x0000073f incq         %rdi
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x00000742 je           LBB0_94
	0x4c, 0x0f, 0xbd, 0xdf, //0x00000748 bsrq         %rdi, %r11
	0x49, 0x83, 0xf3, 0x3f, //0x0000074c xorq         $63, %r11
	//0x00000750 LBB0_94
	0x44, 0x89, 0xd9, //0x00000750 movl         %r11d, %ecx
	0x48, 0xd3, 0xe7, //0x00000753 shlq         %cl, %rdi
	0x48, 0x8b, 0x45, 0xa0, //0x00000756 movq         $-96(%rbp), %rax
	0x48, 0xf7, 0xe7, //0x0000075a mulq         %rdi
	0x49, 0x89, 0xc0, //0x0000075d movq         %rax, %r8
	0x48, 0x89, 0xd6, //0x00000760 movq         %rdx, %rsi
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x00000763 andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x00000769 cmpq         $511, %rdx
	0x0f, 0x85, 0x4c, 0x00, 0x00, 0x00, //0x00000770 jne          LBB0_99
	0x48, 0x89, 0xf9, //0x00000776 movq         %rdi, %rcx
	0x48, 0xf7, 0xd1, //0x00000779 notq         %rcx
	0x49, 0x39, 0xc8, //0x0000077c cmpq         %rcx, %r8
	0x0f, 0x86, 0x3d, 0x00, 0x00, 0x00, //0x0000077f jbe          LBB0_99
	0x48, 0x89, 0xf8, //0x00000785 movq         %rdi, %rax
	0x48, 0x8d, 0x15, 0xb1, 0x1b, 0x00, 0x00, //0x00000788 leaq         $7089(%rip), %rdx  /* _POW10_M128_TAB+0(%rip) */
	0x49, 0xf7, 0x24, 0x17, //0x0000078f mulq         (%r15,%rdx)
	0x49, 0x01, 0xd0, //0x00000793 addq         %rdx, %r8
	0x48, 0x83, 0xd6, 0x00, //0x00000796 adcq         $0, %rsi
	0x89, 0xf2, //0x0000079a movl         %esi, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x0000079c andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x000007a2 cmpq         $511, %rdx
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x000007a9 jne          LBB0_99
	0x49, 0x83, 0xf8, 0xff, //0x000007af cmpq         $-1, %r8
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000007b3 jne          LBB0_99
	0x48, 0x39, 0xc8, //0x000007b9 cmpq         %rcx, %rax
	0x0f, 0x87, 0x97, 0xfe, 0xff, 0xff, //0x000007bc ja           LBB0_85
	//0x000007c2 LBB0_99
	0x48, 0x89, 0xf0, //0x000007c2 movq         %rsi, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x000007c5 shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x000007c9 leal         $9(%rax), %ecx
	0x48, 0xd3, 0xee, //0x000007cc shrq         %cl, %rsi
	0x4d, 0x85, 0xc0, //0x000007cf testq        %r8, %r8
	0x0f, 0x85, 0x17, 0x00, 0x00, 0x00, //0x000007d2 jne          LBB0_102
	0x48, 0x85, 0xd2, //0x000007d8 testq        %rdx, %rdx
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x000007db jne          LBB0_102
	0x89, 0xf1, //0x000007e1 movl         %esi, %ecx
	0x83, 0xe1, 0x03, //0x000007e3 andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x000007e6 cmpl         $1, %ecx
	0x0f, 0x84, 0x6a, 0xfe, 0xff, 0xff, //0x000007e9 je           LBB0_85
	//0x000007ef LBB0_102
	0x4c, 0x29, 0xdb, //0x000007ef subq         %r11, %rbx
	0x89, 0xf2, //0x000007f2 movl         %esi, %edx
	0x83, 0xe2, 0x01, //0x000007f4 andl         $1, %edx
	0x48, 0x01, 0xf2, //0x000007f7 addq         %rsi, %rdx
	0x48, 0x01, 0xc3, //0x000007fa addq         %rax, %rbx
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, //0x000007fd movabsq      $18014398509481984, %rax
	0x48, 0x39, 0xc2, //0x00000807 cmpq         %rax, %rdx
	0x48, 0x83, 0xdb, 0x00, //0x0000080a sbbq         $0, %rbx
	0x48, 0x8d, 0x83, 0x01, 0xf8, 0xff, 0xff, //0x0000080e leaq         $-2047(%rbx), %rax
	0x48, 0x3d, 0x02, 0xf8, 0xff, 0xff, //0x00000815 cmpq         $-2046, %rax
	0x0f, 0x82, 0x38, 0xfe, 0xff, 0xff, //0x0000081b jb           LBB0_85
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, //0x00000821 movabsq      $18014398509481984, %rax
	0x48, 0x39, 0xc2, //0x0000082b cmpq         %rax, %rdx
	0xb1, 0x02, //0x0000082e movb         $2, %cl
	0x80, 0xd9, 0x00, //0x00000830 sbbb         $0, %cl
	0x48, 0xd3, 0xea, //0x00000833 shrq         %cl, %rdx
	0x48, 0xc1, 0xe3, 0x34, //0x00000836 shlq         $52, %rbx
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x0000083a movabsq      $4503599627370495, %rax
	0x48, 0x21, 0xc2, //0x00000844 andq         %rax, %rdx
	0x48, 0x09, 0xda, //0x00000847 orq          %rbx, %rdx
	0x48, 0x89, 0xd0, //0x0000084a movq         %rdx, %rax
	0x48, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x0000084d movabsq      $-9223372036854775808, %rcx
	0x48, 0x09, 0xc8, //0x00000857 orq          %rcx, %rax
	0x41, 0x80, 0xf9, 0x2d, //0x0000085a cmpb         $45, %r9b
	0x48, 0x0f, 0x45, 0xc2, //0x0000085e cmovneq      %rdx, %rax
	0xc4, 0xe1, 0xf9, 0x6e, 0xc8, //0x00000862 vmovq        %rax, %xmm1
	0xc5, 0xf9, 0x2e, 0xc1, //0x00000867 vucomisd     %xmm1, %xmm0
	0x0f, 0x85, 0xe8, 0xfd, 0xff, 0xff, //0x0000086b jne          LBB0_85
	0x0f, 0x8b, 0x35, 0x04, 0x00, 0x00, //0x00000871 jnp          LBB0_166
	0xe9, 0xdd, 0xfd, 0xff, 0xff, //0x00000877 jmp          LBB0_85
	//0x0000087c LBB0_104
	0x48, 0x89, 0xc1, //0x0000087c movq         %rax, %rcx
	//0x0000087f LBB0_105
	0x8d, 0x43, 0xc6, //0x0000087f leal         $-58(%rbx), %eax
	0x3c, 0xf6, //0x00000882 cmpb         $-10, %al
	0x0f, 0x82, 0x96, 0xf8, 0xff, 0xff, //0x00000884 jb           LBB0_6
	0x45, 0x31, 0xc0, //0x0000088a xorl         %r8d, %r8d
	0x48, 0x8b, 0x45, 0xb8, //0x0000088d movq         $-72(%rbp), %rax
	0x48, 0x39, 0xc1, //0x00000891 cmpq         %rax, %rcx
	0x0f, 0x83, 0xa9, 0x01, 0x00, 0x00, //0x00000894 jae          LBB0_123
	0x4c, 0x8d, 0x58, 0xff, //0x0000089a leaq         $-1(%rax), %r11
	0x45, 0x31, 0xc0, //0x0000089e xorl         %r8d, %r8d
	//0x000008a1 LBB0_108
	0x44, 0x89, 0xc6, //0x000008a1 movl         %r8d, %esi
	0x0f, 0xb6, 0xdb, //0x000008a4 movzbl       %bl, %ebx
	0x41, 0x81, 0xf8, 0x10, 0x27, 0x00, 0x00, //0x000008a7 cmpl         $10000, %r8d
	0x8d, 0x04, 0xb6, //0x000008ae leal         (%rsi,%rsi,4), %eax
	0x44, 0x8d, 0x44, 0x43, 0xd0, //0x000008b1 leal         $-48(%rbx,%rax,2), %r8d
	0x44, 0x0f, 0x4d, 0xc6, //0x000008b6 cmovgel      %esi, %r8d
	0x49, 0x39, 0xcb, //0x000008ba cmpq         %rcx, %r11
	0x0f, 0x84, 0x7c, 0x01, 0x00, 0x00, //0x000008bd je           LBB0_122
	0x41, 0x0f, 0xb6, 0x5c, 0x0d, 0x01, //0x000008c3 movzbl       $1(%r13,%rcx), %ebx
	0x8d, 0x43, 0xd0, //0x000008c9 leal         $-48(%rbx), %eax
	0x48, 0x83, 0xc1, 0x01, //0x000008cc addq         $1, %rcx
	0x3c, 0x0a, //0x000008d0 cmpb         $10, %al
	0x0f, 0x82, 0xc9, 0xff, 0xff, 0xff, //0x000008d2 jb           LBB0_108
	0xe9, 0x66, 0x01, 0x00, 0x00, //0x000008d8 jmp          LBB0_123
	//0x000008dd LBB0_110
	0x48, 0x89, 0xd7, //0x000008dd movq         %rdx, %rdi
	0x48, 0x83, 0xe7, 0x80, //0x000008e0 andq         $-128, %rdi
	0x48, 0x8d, 0x5f, 0x80, //0x000008e4 leaq         $-128(%rdi), %rbx
	0x48, 0x89, 0xd8, //0x000008e8 movq         %rbx, %rax
	0x48, 0xc1, 0xe8, 0x07, //0x000008eb shrq         $7, %rax
	0x48, 0x83, 0xc0, 0x01, //0x000008ef addq         $1, %rax
	0x89, 0xc6, //0x000008f3 movl         %eax, %esi
	0x83, 0xe6, 0x03, //0x000008f5 andl         $3, %esi
	0x48, 0x81, 0xfb, 0x80, 0x01, 0x00, 0x00, //0x000008f8 cmpq         $384, %rbx
	0x0f, 0x83, 0x07, 0x00, 0x00, 0x00, //0x000008ff jae          LBB0_112
	0x31, 0xdb, //0x00000905 xorl         %ebx, %ebx
	0xe9, 0xaf, 0x00, 0x00, 0x00, //0x00000907 jmp          LBB0_114
	//0x0000090c LBB0_112
	0x48, 0x83, 0xe0, 0xfc, //0x0000090c andq         $-4, %rax
	0x31, 0xdb, //0x00000910 xorl         %ebx, %ebx
	0xc5, 0xf9, 0x57, 0xc0, //0x00000912 vxorpd       %xmm0, %xmm0, %xmm0
	//0x00000916 LBB0_113
	0xc4, 0xc1, 0x7d, 0x11, 0x44, 0x1c, 0x01, //0x00000916 vmovupd      %ymm0, $1(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x44, 0x1c, 0x21, //0x0000091d vmovupd      %ymm0, $33(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x44, 0x1c, 0x41, //0x00000924 vmovupd      %ymm0, $65(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x44, 0x1c, 0x61, //0x0000092b vmovupd      %ymm0, $97(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x84, 0x1c, 0x81, 0x00, 0x00, 0x00, //0x00000932 vmovupd      %ymm0, $129(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x84, 0x1c, 0xa1, 0x00, 0x00, 0x00, //0x0000093c vmovupd      %ymm0, $161(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x84, 0x1c, 0xc1, 0x00, 0x00, 0x00, //0x00000946 vmovupd      %ymm0, $193(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x84, 0x1c, 0xe1, 0x00, 0x00, 0x00, //0x00000950 vmovupd      %ymm0, $225(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x84, 0x1c, 0x01, 0x01, 0x00, 0x00, //0x0000095a vmovupd      %ymm0, $257(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x84, 0x1c, 0x21, 0x01, 0x00, 0x00, //0x00000964 vmovupd      %ymm0, $289(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x84, 0x1c, 0x41, 0x01, 0x00, 0x00, //0x0000096e vmovupd      %ymm0, $321(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x84, 0x1c, 0x61, 0x01, 0x00, 0x00, //0x00000978 vmovupd      %ymm0, $353(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x84, 0x1c, 0x81, 0x01, 0x00, 0x00, //0x00000982 vmovupd      %ymm0, $385(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x84, 0x1c, 0xa1, 0x01, 0x00, 0x00, //0x0000098c vmovupd      %ymm0, $417(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x84, 0x1c, 0xc1, 0x01, 0x00, 0x00, //0x00000996 vmovupd      %ymm0, $449(%r12,%rbx)
	0xc4, 0xc1, 0x7d, 0x11, 0x84, 0x1c, 0xe1, 0x01, 0x00, 0x00, //0x000009a0 vmovupd      %ymm0, $481(%r12,%rbx)
	0x48, 0x81, 0xc3, 0x00, 0x02, 0x00, 0x00, //0x000009aa addq         $512, %rbx
	0x48, 0x83, 0xc0, 0xfc, //0x000009b1 addq         $-4, %rax
	0x0f, 0x85, 0x5b, 0xff, 0xff, 0xff, //0x000009b5 jne          LBB0_113
	//0x000009bb LBB0_114
	0x48, 0x85, 0xf6, //0x000009bb testq        %rsi, %rsi
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x000009be je           LBB0_117
	0xc5, 0xf9, 0x57, 0xc0, //0x000009c4 vxorpd       %xmm0, %xmm0, %xmm0
	//0x000009c8 LBB0_116
	0x48, 0x89, 0xd8, //0x000009c8 movq         %rbx, %rax
	0x48, 0x83, 0xc8, 0x01, //0x000009cb orq          $1, %rax
	0xc4, 0xc1, 0x7d, 0x11, 0x04, 0x04, //0x000009cf vmovupd      %ymm0, (%r12,%rax)
	0xc4, 0xc1, 0x7d, 0x11, 0x44, 0x04, 0x20, //0x000009d5 vmovupd      %ymm0, $32(%r12,%rax)
	0xc4, 0xc1, 0x7d, 0x11, 0x44, 0x04, 0x40, //0x000009dc vmovupd      %ymm0, $64(%r12,%rax)
	0xc4, 0xc1, 0x7d, 0x11, 0x44, 0x04, 0x60, //0x000009e3 vmovupd      %ymm0, $96(%r12,%rax)
	0x48, 0x83, 0xeb, 0x80, //0x000009ea subq         $-128, %rbx
	0x48, 0x83, 0xc6, 0xff, //0x000009ee addq         $-1, %rsi
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x000009f2 jne          LBB0_116
	//0x000009f8 LBB0_117
	0x48, 0x39, 0xfa, //0x000009f8 cmpq         %rdi, %rdx
	0x48, 0x8b, 0x5d, 0xc0, //0x000009fb movq         $-64(%rbp), %rbx
	0x0f, 0x84, 0x6d, 0x00, 0x00, 0x00, //0x000009ff je           LBB0_126
	0xf6, 0xc2, 0x70, //0x00000a05 testb        $112, %dl
	0x0f, 0x84, 0x45, 0x00, 0x00, 0x00, //0x00000a08 je           LBB0_124
	//0x00000a0e LBB0_119
	0x48, 0x89, 0xd6, //0x00000a0e movq         %rdx, %rsi
	0x48, 0x83, 0xe6, 0xf0, //0x00000a11 andq         $-16, %rsi
	0x48, 0x8d, 0x46, 0x01, //0x00000a15 leaq         $1(%rsi), %rax
	0xc5, 0xf9, 0x57, 0xc0, //0x00000a19 vxorpd       %xmm0, %xmm0, %xmm0
	//0x00000a1d LBB0_120
	0xc4, 0xc1, 0x79, 0x11, 0x44, 0x3c, 0x01, //0x00000a1d vmovupd      %xmm0, $1(%r12,%rdi)
	0x48, 0x83, 0xc7, 0x10, //0x00000a24 addq         $16, %rdi
	0x48, 0x39, 0xfe, //0x00000a28 cmpq         %rdi, %rsi
	0x0f, 0x85, 0xec, 0xff, 0xff, 0xff, //0x00000a2b jne          LBB0_120
	0x48, 0x39, 0xf2, //0x00000a31 cmpq         %rsi, %rdx
	0x0f, 0x85, 0x26, 0x00, 0x00, 0x00, //0x00000a34 jne          LBB0_125
	0xe9, 0x33, 0x00, 0x00, 0x00, //0x00000a3a jmp          LBB0_126
	//0x00000a3f LBB0_122
	0x48, 0x8b, 0x4d, 0xb8, //0x00000a3f movq         $-72(%rbp), %rcx
	//0x00000a43 LBB0_123
	0x44, 0x0f, 0xaf, 0xc2, //0x00000a43 imull        %edx, %r8d
	0x45, 0x01, 0xd0, //0x00000a47 addl         %r10d, %r8d
	0x48, 0x89, 0x4d, 0xb8, //0x00000a4a movq         %rcx, $-72(%rbp)
	0xe9, 0xbf, 0xf9, 0xff, 0xff, //0x00000a4e jmp          LBB0_58
	//0x00000a53 LBB0_124
	0x48, 0x83, 0xcf, 0x01, //0x00000a53 orq          $1, %rdi
	0x48, 0x89, 0xf8, //0x00000a57 movq         %rdi, %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a5a .p2align 4, 0x90
	//0x00000a60 LBB0_125
	0x41, 0xc6, 0x04, 0x04, 0x00, //0x00000a60 movb         $0, (%r12,%rax)
	0x48, 0x83, 0xc0, 0x01, //0x00000a65 addq         $1, %rax
	0x48, 0x39, 0xc3, //0x00000a69 cmpq         %rax, %rbx
	0x0f, 0x85, 0xee, 0xff, 0xff, 0xff, //0x00000a6c jne          LBB0_125
	//0x00000a72 LBB0_126
	0x41, 0x8a, 0x18, //0x00000a72 movb         (%r8), %bl
	0x31, 0xff, //0x00000a75 xorl         %edi, %edi
	0x80, 0xfb, 0x2d, //0x00000a77 cmpb         $45, %bl
	0x40, 0x0f, 0x94, 0xc7, //0x00000a7a sete         %dil
	0x45, 0x31, 0xff, //0x00000a7e xorl         %r15d, %r15d
	0x49, 0x39, 0xf9, //0x00000a81 cmpq         %rdi, %r9
	0x0f, 0x8e, 0xd3, 0x00, 0x00, 0x00, //0x00000a84 jle          LBB0_141
	0x48, 0x89, 0x4d, 0xc8, //0x00000a8a movq         %rcx, $-56(%rbp)
	0x88, 0x5d, 0xd7, //0x00000a8e movb         %bl, $-41(%rbp)
	0x4c, 0x89, 0x75, 0xb0, //0x00000a91 movq         %r14, $-80(%rbp)
	0x41, 0xb3, 0x01, //0x00000a95 movb         $1, %r11b
	0x45, 0x31, 0xf6, //0x00000a98 xorl         %r14d, %r14d
	0x45, 0x31, 0xd2, //0x00000a9b xorl         %r10d, %r10d
	0x31, 0xc0, //0x00000a9e xorl         %eax, %eax
	0x31, 0xf6, //0x00000aa0 xorl         %esi, %esi
	0xe9, 0x2e, 0x00, 0x00, 0x00, //0x00000aa2 jmp          LBB0_130
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000aa7 .p2align 4, 0x90
	//0x00000ab0 LBB0_128
	0x4c, 0x89, 0xf9, //0x00000ab0 movq         %r15, %rcx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00000ab3 movl         $1, %esi
	0x41, 0x89, 0xc7, //0x00000ab8 movl         %eax, %r15d
	0x80, 0xfa, 0x2e, //0x00000abb cmpb         $46, %dl
	0x0f, 0x85, 0xab, 0x00, 0x00, 0x00, //0x00000abe jne          LBB0_142
	0x48, 0x83, 0xc7, 0x01, //0x00000ac4 addq         $1, %rdi
	0x4c, 0x39, 0xcf, //0x00000ac8 cmpq         %r9, %rdi
	0x41, 0x0f, 0x9c, 0xc3, //0x00000acb setl         %r11b
	0x0f, 0x84, 0x7e, 0x00, 0x00, 0x00, //0x00000acf je           LBB0_140
	//0x00000ad5 LBB0_130
	0x89, 0xf3, //0x00000ad5 movl         %esi, %ebx
	0x41, 0x0f, 0xb6, 0x14, 0x38, //0x00000ad7 movzbl       (%r8,%rdi), %edx
	0x8d, 0x72, 0xd0, //0x00000adc leal         $-48(%rdx), %esi
	0x40, 0x80, 0xfe, 0x09, //0x00000adf cmpb         $9, %sil
	0x0f, 0x87, 0xc7, 0xff, 0xff, 0xff, //0x00000ae3 ja           LBB0_128
	0x80, 0xfa, 0x30, //0x00000ae9 cmpb         $48, %dl
	0x0f, 0x85, 0x1d, 0x00, 0x00, 0x00, //0x00000aec jne          LBB0_135
	0x85, 0xc0, //0x00000af2 testl        %eax, %eax
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x00000af4 je           LBB0_138
	0x49, 0x63, 0xf6, //0x00000afa movslq       %r14d, %rsi
	0x48, 0x39, 0x75, 0xc0, //0x00000afd cmpq         %rsi, $-64(%rbp)
	0x0f, 0x87, 0x15, 0x00, 0x00, 0x00, //0x00000b01 ja           LBB0_136
	0x44, 0x89, 0xf0, //0x00000b07 movl         %r14d, %eax
	0xe9, 0x31, 0x00, 0x00, 0x00, //0x00000b0a jmp          LBB0_139
	//0x00000b0f LBB0_135
	0x48, 0x63, 0xf0, //0x00000b0f movslq       %eax, %rsi
	0x48, 0x39, 0x75, 0xc0, //0x00000b12 cmpq         %rsi, $-64(%rbp)
	0x0f, 0x86, 0x10, 0x00, 0x00, 0x00, //0x00000b16 jbe          LBB0_137
	//0x00000b1c LBB0_136
	0x41, 0x88, 0x14, 0x34, //0x00000b1c movb         %dl, (%r12,%rsi)
	0x41, 0x83, 0xc6, 0x01, //0x00000b20 addl         $1, %r14d
	0x44, 0x89, 0xf0, //0x00000b24 movl         %r14d, %eax
	0xe9, 0x14, 0x00, 0x00, 0x00, //0x00000b27 jmp          LBB0_139
	//0x00000b2c LBB0_137
	0x41, 0xba, 0x01, 0x00, 0x00, 0x00, //0x00000b2c movl         $1, %r10d
	0xe9, 0x09, 0x00, 0x00, 0x00, //0x00000b32 jmp          LBB0_139
	//0x00000b37 LBB0_138
	0x41, 0x83, 0xc7, 0xff, //0x00000b37 addl         $-1, %r15d
	0x31, 0xc0, //0x00000b3b xorl         %eax, %eax
	0x90, 0x90, 0x90, //0x00000b3d .p2align 4, 0x90
	//0x00000b40 LBB0_139
	0x89, 0xde, //0x00000b40 movl         %ebx, %esi
	0x48, 0x83, 0xc7, 0x01, //0x00000b42 addq         $1, %rdi
	0x4c, 0x39, 0xcf, //0x00000b46 cmpq         %r9, %rdi
	0x41, 0x0f, 0x9c, 0xc3, //0x00000b49 setl         %r11b
	0x0f, 0x85, 0x82, 0xff, 0xff, 0xff, //0x00000b4d jne          LBB0_130
	//0x00000b53 LBB0_140
	0x4c, 0x89, 0xcf, //0x00000b53 movq         %r9, %rdi
	0x89, 0xf3, //0x00000b56 movl         %esi, %ebx
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x00000b58 jmp          LBB0_143
	//0x00000b5d LBB0_141
	0x45, 0x31, 0xc0, //0x00000b5d xorl         %r8d, %r8d
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00000b60 movabsq      $4503599627370495, %rcx
	0xe9, 0x1b, 0x01, 0x00, 0x00, //0x00000b6a jmp          LBB0_165
	//0x00000b6f LBB0_142
	0x49, 0x89, 0xcf, //0x00000b6f movq         %rcx, %r15
	//0x00000b72 LBB0_143
	0x85, 0xdb, //0x00000b72 testl        %ebx, %ebx
	0x45, 0x0f, 0x44, 0xfe, //0x00000b74 cmovel       %r14d, %r15d
	0x41, 0xf6, 0xc3, 0x01, //0x00000b78 testb        $1, %r11b
	0x0f, 0x84, 0xb1, 0x00, 0x00, 0x00, //0x00000b7c je           LBB0_158
	0x89, 0xf8, //0x00000b82 movl         %edi, %eax
	0x41, 0x8a, 0x04, 0x00, //0x00000b84 movb         (%r8,%rax), %al
	0x0c, 0x20, //0x00000b88 orb          $32, %al
	0x3c, 0x65, //0x00000b8a cmpb         $101, %al
	0x0f, 0x85, 0xa1, 0x00, 0x00, 0x00, //0x00000b8c jne          LBB0_158
	0x89, 0xfa, //0x00000b92 movl         %edi, %edx
	0x41, 0x8a, 0x5c, 0x10, 0x01, //0x00000b94 movb         $1(%r8,%rdx), %bl
	0x80, 0xfb, 0x2d, //0x00000b99 cmpb         $45, %bl
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00000b9c je           LBB0_148
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00000ba2 movl         $1, %eax
	0x80, 0xfb, 0x2b, //0x00000ba7 cmpb         $43, %bl
	0x48, 0x8b, 0x5d, 0xc8, //0x00000baa movq         $-56(%rbp), %rbx
	0x0f, 0x85, 0x21, 0x00, 0x00, 0x00, //0x00000bae jne          LBB0_149
	0x83, 0xc7, 0x02, //0x00000bb4 addl         $2, %edi
	0x89, 0xfa, //0x00000bb7 movl         %edi, %edx
	0xe9, 0x1b, 0x00, 0x00, 0x00, //0x00000bb9 jmp          LBB0_150
	//0x00000bbe LBB0_148
	0x83, 0xc7, 0x02, //0x00000bbe addl         $2, %edi
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00000bc1 movl         $-1, %eax
	0x89, 0xfa, //0x00000bc6 movl         %edi, %edx
	0x48, 0x8b, 0x7d, 0xb8, //0x00000bc8 movq         $-72(%rbp), %rdi
	0x48, 0x8b, 0x5d, 0xc8, //0x00000bcc movq         $-56(%rbp), %rbx
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00000bd0 jmp          LBB0_151
	//0x00000bd5 LBB0_149
	0x48, 0x83, 0xc2, 0x01, //0x00000bd5 addq         $1, %rdx
	//0x00000bd9 LBB0_150
	0x48, 0x8b, 0x7d, 0xb8, //0x00000bd9 movq         $-72(%rbp), %rdi
	//0x00000bdd LBB0_151
	0x48, 0x63, 0xf2, //0x00000bdd movslq       %edx, %rsi
	0x31, 0xd2, //0x00000be0 xorl         %edx, %edx
	0x49, 0x39, 0xf1, //0x00000be2 cmpq         %rsi, %r9
	0x0f, 0x8e, 0x3f, 0x00, 0x00, 0x00, //0x00000be5 jle          LBB0_157
	0x48, 0x01, 0xf3, //0x00000beb addq         %rsi, %rbx
	0x31, 0xd2, //0x00000bee xorl         %edx, %edx
	//0x00000bf0 LBB0_153
	0x41, 0x0f, 0xbe, 0x4c, 0x1d, 0x00, //0x00000bf0 movsbl       (%r13,%rbx), %ecx
	0x83, 0xf9, 0x30, //0x00000bf6 cmpl         $48, %ecx
	0x0f, 0x8c, 0x2b, 0x00, 0x00, 0x00, //0x00000bf9 jl           LBB0_157
	0x80, 0xf9, 0x39, //0x00000bff cmpb         $57, %cl
	0x0f, 0x87, 0x22, 0x00, 0x00, 0x00, //0x00000c02 ja           LBB0_157
	0x81, 0xfa, 0x0f, 0x27, 0x00, 0x00, //0x00000c08 cmpl         $9999, %edx
	0x0f, 0x8f, 0x16, 0x00, 0x00, 0x00, //0x00000c0e jg           LBB0_157
	0x8d, 0x14, 0x92, //0x00000c14 leal         (%rdx,%rdx,4), %edx
	0x8d, 0x14, 0x51, //0x00000c17 leal         (%rcx,%rdx,2), %edx
	0x83, 0xc2, 0xd0, //0x00000c1a addl         $-48, %edx
	0x48, 0x83, 0xc3, 0x01, //0x00000c1d addq         $1, %rbx
	0x48, 0x39, 0xdf, //0x00000c21 cmpq         %rbx, %rdi
	0x0f, 0x85, 0xc6, 0xff, 0xff, 0xff, //0x00000c24 jne          LBB0_153
	//0x00000c2a LBB0_157
	0x0f, 0xaf, 0xd0, //0x00000c2a imull        %eax, %edx
	0x44, 0x01, 0xfa, //0x00000c2d addl         %r15d, %edx
	0x41, 0x89, 0xd7, //0x00000c30 movl         %edx, %r15d
	//0x00000c33 LBB0_158
	0x45, 0x85, 0xf6, //0x00000c33 testl        %r14d, %r14d
	0x0f, 0x84, 0x1f, 0x00, 0x00, 0x00, //0x00000c36 je           LBB0_161
	0x41, 0x81, 0xff, 0x36, 0x01, 0x00, 0x00, //0x00000c3c cmpl         $310, %r15d
	0x0f, 0x8e, 0x1d, 0x00, 0x00, 0x00, //0x00000c43 jle          LBB0_162
	//0x00000c49 LBB0_160
	0x45, 0x31, 0xc0, //0x00000c49 xorl         %r8d, %r8d
	0x49, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00000c4c movabsq      $9218868437227405312, %r15
	0xe9, 0x1e, 0x00, 0x00, 0x00, //0x00000c56 jmp          LBB0_164
	//0x00000c5b LBB0_161
	0x45, 0x31, 0xff, //0x00000c5b xorl         %r15d, %r15d
	0x45, 0x31, 0xc0, //0x00000c5e xorl         %r8d, %r8d
	0xe9, 0x13, 0x00, 0x00, 0x00, //0x00000c61 jmp          LBB0_164
	//0x00000c66 LBB0_162
	0x45, 0x31, 0xc0, //0x00000c66 xorl         %r8d, %r8d
	0x41, 0x81, 0xff, 0xb6, 0xfe, 0xff, 0xff, //0x00000c69 cmpl         $-330, %r15d
	0x0f, 0x8d, 0x89, 0x00, 0x00, 0x00, //0x00000c70 jge          LBB0_171
	0x45, 0x31, 0xff, //0x00000c76 xorl         %r15d, %r15d
	//0x00000c79 LBB0_164
	0x4c, 0x8b, 0x75, 0xb0, //0x00000c79 movq         $-80(%rbp), %r14
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00000c7d movabsq      $4503599627370495, %rcx
	0x8a, 0x5d, 0xd7, //0x00000c87 movb         $-41(%rbp), %bl
	//0x00000c8a LBB0_165
	0x49, 0x21, 0xc8, //0x00000c8a andq         %rcx, %r8
	0x4d, 0x09, 0xf8, //0x00000c8d orq          %r15, %r8
	0x4c, 0x89, 0xc0, //0x00000c90 movq         %r8, %rax
	0x48, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00000c93 movabsq      $-9223372036854775808, %rcx
	0x48, 0x09, 0xc8, //0x00000c9d orq          %rcx, %rax
	0x80, 0xfb, 0x2d, //0x00000ca0 cmpb         $45, %bl
	0x49, 0x0f, 0x45, 0xc0, //0x00000ca3 cmovneq      %r8, %rax
	0xc4, 0xe1, 0xf9, 0x6e, 0xc0, //0x00000ca7 vmovq        %rax, %xmm0
	//0x00000cac LBB0_166
	0xc4, 0xe1, 0xf9, 0x7e, 0xc0, //0x00000cac vmovq        %xmm0, %rax
	0x48, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00000cb1 movabsq      $-9223372036854775808, %rcx
	0x48, 0x83, 0xc1, 0xff, //0x00000cbb addq         $-1, %rcx
	0x48, 0x21, 0xc1, //0x00000cbf andq         %rax, %rcx
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00000cc2 movabsq      $9218868437227405312, %rax
	0x48, 0x39, 0xc1, //0x00000ccc cmpq         %rax, %rcx
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00000ccf jne          LBB0_168
	0x49, 0xc7, 0x06, 0xf8, 0xff, 0xff, 0xff, //0x00000cd5 movq         $-8, (%r14)
	//0x00000cdc LBB0_168
	0xc4, 0xc1, 0x7b, 0x11, 0x46, 0x08, //0x00000cdc vmovsd       %xmm0, $8(%r14)
	//0x00000ce2 LBB0_169
	0x48, 0x8b, 0x45, 0x90, //0x00000ce2 movq         $-112(%rbp), %rax
	0x48, 0x8b, 0x4d, 0xb8, //0x00000ce6 movq         $-72(%rbp), %rcx
	0x48, 0x89, 0x08, //0x00000cea movq         %rcx, (%rax)
	//0x00000ced LBB0_170
	0x48, 0x83, 0xc4, 0x58, //0x00000ced addq         $88, %rsp
	0x5b, //0x00000cf1 popq         %rbx
	0x41, 0x5c, //0x00000cf2 popq         %r12
	0x41, 0x5d, //0x00000cf4 popq         %r13
	0x41, 0x5e, //0x00000cf6 popq         %r14
	0x41, 0x5f, //0x00000cf8 popq         %r15
	0x5d, //0x00000cfa popq         %rbp
	0xc5, 0xf8, 0x77, //0x00000cfb vzeroupper   
	0xc3, //0x00000cfe retq         
	//0x00000cff LBB0_171
	0x49, 0xbd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, //0x00000cff movabsq      $1152921504606846976, %r13
	0x45, 0x85, 0xff, //0x00000d09 testl        %r15d, %r15d
	0x0f, 0x8e, 0x11, 0x04, 0x00, 0x00, //0x00000d0c jle          LBB0_236
	0x45, 0x31, 0xdb, //0x00000d12 xorl         %r11d, %r11d
	0x44, 0x89, 0xf3, //0x00000d15 movl         %r14d, %ebx
	0x45, 0x89, 0xf1, //0x00000d18 movl         %r14d, %r9d
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00000d1b jmp          LBB0_175
	//0x00000d20 LBB0_173
	0x45, 0x31, 0xc9, //0x00000d20 xorl         %r9d, %r9d
	//0x00000d23 LBB0_174
	0x45, 0x01, 0xd8, //0x00000d23 addl         %r11d, %r8d
	0x45, 0x89, 0xc3, //0x00000d26 movl         %r8d, %r11d
	0x45, 0x85, 0xff, //0x00000d29 testl        %r15d, %r15d
	0x0f, 0x8e, 0xf4, 0x03, 0x00, 0x00, //0x00000d2c jle          LBB0_237
	//0x00000d32 LBB0_175
	0x41, 0x83, 0xff, 0x08, //0x00000d32 cmpl         $8, %r15d
	0x0f, 0x86, 0x1d, 0x00, 0x00, 0x00, //0x00000d36 jbe          LBB0_178
	0xb9, 0xe5, 0xff, 0xff, 0xff, //0x00000d3c movl         $-27, %ecx
	0x41, 0xb8, 0x1b, 0x00, 0x00, 0x00, //0x00000d41 movl         $27, %r8d
	0x45, 0x85, 0xc9, //0x00000d47 testl        %r9d, %r9d
	0x0f, 0x84, 0xd0, 0xff, 0xff, 0xff, //0x00000d4a je           LBB0_173
	0x4c, 0x89, 0x5d, 0xa8, //0x00000d50 movq         %r11, $-88(%rbp)
	0xe9, 0xef, 0x01, 0x00, 0x00, //0x00000d54 jmp          LBB0_208
	//0x00000d59 LBB0_178
	0x44, 0x89, 0xf8, //0x00000d59 movl         %r15d, %eax
	0x48, 0x8d, 0x0d, 0x6d, 0x41, 0x00, 0x00, //0x00000d5c leaq         $16749(%rip), %rcx  /* _POW_TAB+0(%rip) */
	0x44, 0x8b, 0x04, 0x81, //0x00000d63 movl         (%rcx,%rax,4), %r8d
	0x45, 0x85, 0xc9, //0x00000d67 testl        %r9d, %r9d
	0x0f, 0x84, 0xb0, 0xff, 0xff, 0xff, //0x00000d6a je           LBB0_173
	0x4c, 0x89, 0x5d, 0xa8, //0x00000d70 movq         %r11, $-88(%rbp)
	0x44, 0x89, 0xc1, //0x00000d74 movl         %r8d, %ecx
	0xf7, 0xd9, //0x00000d77 negl         %ecx
	0x83, 0xf9, 0xc3, //0x00000d79 cmpl         $-61, %ecx
	0x0f, 0x87, 0xc6, 0x01, 0x00, 0x00, //0x00000d7c ja           LBB0_208
	0x48, 0x8b, 0x5d, 0xc0, //0x00000d82 movq         $-64(%rbp), %rbx
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x00000d86 movl         $1, %edi
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00000d8b jmp          LBB0_183
	//0x00000d90 LBB0_205
	0x45, 0x31, 0xff, //0x00000d90 xorl         %r15d, %r15d
	//0x00000d93 LBB0_181
	0x45, 0x31, 0xf6, //0x00000d93 xorl         %r14d, %r14d
	//0x00000d96 LBB0_182
	0x41, 0x8d, 0x4b, 0x3c, //0x00000d96 leal         $60(%r11), %ecx
	0x45, 0x89, 0xf1, //0x00000d9a movl         %r14d, %r9d
	0x41, 0x83, 0xfb, 0x88, //0x00000d9d cmpl         $-120, %r11d
	0x0f, 0x8d, 0x9e, 0x01, 0x00, 0x00, //0x00000da1 jge          LBB0_207
	//0x00000da7 LBB0_183
	0x41, 0x89, 0xcb, //0x00000da7 movl         %ecx, %r11d
	0x45, 0x85, 0xc9, //0x00000daa testl        %r9d, %r9d
	0xb9, 0x00, 0x00, 0x00, 0x00, //0x00000dad movl         $0, %ecx
	0x41, 0x0f, 0x4f, 0xc9, //0x00000db2 cmovgl       %r9d, %ecx
	0x31, 0xd2, //0x00000db6 xorl         %edx, %edx
	0x31, 0xc0, //0x00000db8 xorl         %eax, %eax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000dba .p2align 4, 0x90
	//0x00000dc0 LBB0_184
	0x48, 0x39, 0xd1, //0x00000dc0 cmpq         %rdx, %rcx
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00000dc3 je           LBB0_187
	0x48, 0x8d, 0x04, 0x80, //0x00000dc9 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x34, 0x14, //0x00000dcd movsbq       (%r12,%rdx), %rsi
	0x48, 0x8d, 0x04, 0x46, //0x00000dd2 leaq         (%rsi,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00000dd6 addq         $-48, %rax
	0x48, 0x83, 0xc2, 0x01, //0x00000dda addq         $1, %rdx
	0x4c, 0x39, 0xe8, //0x00000dde cmpq         %r13, %rax
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x00000de1 jb           LBB0_184
	0x89, 0xd1, //0x00000de7 movl         %edx, %ecx
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00000de9 jmp          LBB0_189
	//0x00000dee LBB0_187
	0x48, 0x85, 0xc0, //0x00000dee testq        %rax, %rax
	0x0f, 0x84, 0x9c, 0xff, 0xff, 0xff, //0x00000df1 je           LBB0_181
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000df7 .p2align 4, 0x90
	//0x00000e00 LBB0_188
	0x48, 0x01, 0xc0, //0x00000e00 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00000e03 leaq         (%rax,%rax,4), %rax
	0x83, 0xc1, 0x01, //0x00000e07 addl         $1, %ecx
	0x4c, 0x39, 0xe8, //0x00000e0a cmpq         %r13, %rax
	0x0f, 0x82, 0xed, 0xff, 0xff, 0xff, //0x00000e0d jb           LBB0_188
	//0x00000e13 LBB0_189
	0x41, 0x29, 0xcf, //0x00000e13 subl         %ecx, %r15d
	0x31, 0xd2, //0x00000e16 xorl         %edx, %edx
	0x44, 0x39, 0xc9, //0x00000e18 cmpl         %r9d, %ecx
	0x0f, 0x8d, 0x5b, 0x00, 0x00, 0x00, //0x00000e1b jge          LBB0_194
	0x48, 0x63, 0xc9, //0x00000e21 movslq       %ecx, %rcx
	0x49, 0x63, 0xd6, //0x00000e24 movslq       %r14d, %rdx
	0x49, 0x8d, 0x34, 0x0c, //0x00000e27 leaq         (%r12,%rcx), %rsi
	0x45, 0x31, 0xf6, //0x00000e2b xorl         %r14d, %r14d
	0x90, 0x90, //0x00000e2e .p2align 4, 0x90
	//0x00000e30 LBB0_191
	0x49, 0x8d, 0x7d, 0xff, //0x00000e30 leaq         $-1(%r13), %rdi
	0x48, 0x21, 0xc7, //0x00000e34 andq         %rax, %rdi
	0x48, 0xc1, 0xe8, 0x3c, //0x00000e37 shrq         $60, %rax
	0x0c, 0x30, //0x00000e3b orb          $48, %al
	0x43, 0x88, 0x04, 0x34, //0x00000e3d movb         %al, (%r12,%r14)
	0x4a, 0x0f, 0xbe, 0x04, 0x36, //0x00000e41 movsbq       (%rsi,%r14), %rax
	0x4a, 0x8d, 0x1c, 0x31, //0x00000e46 leaq         (%rcx,%r14), %rbx
	0x48, 0x83, 0xc3, 0x01, //0x00000e4a addq         $1, %rbx
	0x49, 0x83, 0xc6, 0x01, //0x00000e4e addq         $1, %r14
	0x48, 0x8d, 0x3c, 0xbf, //0x00000e52 leaq         (%rdi,%rdi,4), %rdi
	0x48, 0x8d, 0x04, 0x78, //0x00000e56 leaq         (%rax,%rdi,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00000e5a addq         $-48, %rax
	0x48, 0x39, 0xd3, //0x00000e5e cmpq         %rdx, %rbx
	0x0f, 0x8c, 0xc9, 0xff, 0xff, 0xff, //0x00000e61 jl           LBB0_191
	0x48, 0x85, 0xc0, //0x00000e67 testq        %rax, %rax
	0x0f, 0x84, 0xb4, 0x00, 0x00, 0x00, //0x00000e6a je           LBB0_203
	0x44, 0x89, 0xf2, //0x00000e70 movl         %r14d, %edx
	0x48, 0x8b, 0x5d, 0xc0, //0x00000e73 movq         $-64(%rbp), %rbx
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x00000e77 movl         $1, %edi
	//0x00000e7c LBB0_194
	0x41, 0x89, 0xd6, //0x00000e7c movl         %edx, %r14d
	0xe9, 0x24, 0x00, 0x00, 0x00, //0x00000e7f jmp          LBB0_196
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e84 .p2align 4, 0x90
	//0x00000e90 LBB0_195
	0x4c, 0x39, 0xe8, //0x00000e90 cmpq         %r13, %rax
	0x44, 0x0f, 0x43, 0xd7, //0x00000e93 cmovael      %edi, %r10d
	0x48, 0x8d, 0x04, 0x09, //0x00000e97 leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x04, 0x80, //0x00000e9b leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc9, //0x00000e9f testq        %rcx, %rcx
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x00000ea2 je           LBB0_198
	//0x00000ea8 LBB0_196
	0x49, 0x8d, 0x4d, 0xff, //0x00000ea8 leaq         $-1(%r13), %rcx
	0x48, 0x21, 0xc1, //0x00000eac andq         %rax, %rcx
	0x49, 0x63, 0xd6, //0x00000eaf movslq       %r14d, %rdx
	0x48, 0x39, 0xd3, //0x00000eb2 cmpq         %rdx, %rbx
	0x0f, 0x86, 0xd5, 0xff, 0xff, 0xff, //0x00000eb5 jbe          LBB0_195
	0x48, 0xc1, 0xe8, 0x3c, //0x00000ebb shrq         $60, %rax
	0x0c, 0x30, //0x00000ebf orb          $48, %al
	0x41, 0x88, 0x04, 0x14, //0x00000ec1 movb         %al, (%r12,%rdx)
	0x83, 0xc2, 0x01, //0x00000ec5 addl         $1, %edx
	0x41, 0x89, 0xd6, //0x00000ec8 movl         %edx, %r14d
	0x48, 0x8d, 0x04, 0x09, //0x00000ecb leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x04, 0x80, //0x00000ecf leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc9, //0x00000ed3 testq        %rcx, %rcx
	0x0f, 0x85, 0xcc, 0xff, 0xff, 0xff, //0x00000ed6 jne          LBB0_196
	//0x00000edc LBB0_198
	0x41, 0x83, 0xc7, 0x01, //0x00000edc addl         $1, %r15d
	0x45, 0x85, 0xf6, //0x00000ee0 testl        %r14d, %r14d
	0x0f, 0x8e, 0x51, 0x00, 0x00, 0x00, //0x00000ee3 jle          LBB0_204
	//0x00000ee9 LBB0_199
	0x44, 0x89, 0xf0, //0x00000ee9 movl         %r14d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00000eec cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x9e, 0xfe, 0xff, 0xff, //0x00000ef2 jne          LBB0_182
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000ef8 .p2align 4, 0x90
	//0x00000f00 LBB0_200
	0x48, 0x83, 0xf8, 0x01, //0x00000f00 cmpq         $1, %rax
	0x0f, 0x86, 0x86, 0xfe, 0xff, 0xff, //0x00000f04 jbe          LBB0_205
	0x8d, 0x48, 0xfe, //0x00000f0a leal         $-2(%rax), %ecx
	0x48, 0x83, 0xc0, 0xff, //0x00000f0d addq         $-1, %rax
	0x41, 0x80, 0x3c, 0x0c, 0x30, //0x00000f11 cmpb         $48, (%r12,%rcx)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00000f16 je           LBB0_200
	0x41, 0x89, 0xc6, //0x00000f1c movl         %eax, %r14d
	0xe9, 0x72, 0xfe, 0xff, 0xff, //0x00000f1f jmp          LBB0_182
	//0x00000f24 LBB0_203
	0x48, 0x8b, 0x5d, 0xc0, //0x00000f24 movq         $-64(%rbp), %rbx
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x00000f28 movl         $1, %edi
	0x41, 0x83, 0xc7, 0x01, //0x00000f2d addl         $1, %r15d
	0x45, 0x85, 0xf6, //0x00000f31 testl        %r14d, %r14d
	0x0f, 0x8f, 0xaf, 0xff, 0xff, 0xff, //0x00000f34 jg           LBB0_199
	//0x00000f3a LBB0_204
	0x0f, 0x85, 0x56, 0xfe, 0xff, 0xff, //0x00000f3a jne          LBB0_182
	0xe9, 0x4b, 0xfe, 0xff, 0xff, //0x00000f40 jmp          LBB0_205
	//0x00000f45 LBB0_207
	0x45, 0x89, 0xf1, //0x00000f45 movl         %r14d, %r9d
	//0x00000f48 LBB0_208
	0xf7, 0xd9, //0x00000f48 negl         %ecx
	0x45, 0x85, 0xc9, //0x00000f4a testl        %r9d, %r9d
	0x41, 0xbb, 0x00, 0x00, 0x00, 0x00, //0x00000f4d movl         $0, %r11d
	0x45, 0x0f, 0x4f, 0xd9, //0x00000f53 cmovgl       %r9d, %r11d
	0x31, 0xc0, //0x00000f57 xorl         %eax, %eax
	0x31, 0xf6, //0x00000f59 xorl         %esi, %esi
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00000f5b .p2align 4, 0x90
	//0x00000f60 LBB0_209
	0x49, 0x39, 0xc3, //0x00000f60 cmpq         %rax, %r11
	0x0f, 0x84, 0xab, 0x00, 0x00, 0x00, //0x00000f63 je           LBB0_218
	0x48, 0x8d, 0x14, 0xb6, //0x00000f69 leaq         (%rsi,%rsi,4), %rdx
	0x49, 0x0f, 0xbe, 0x34, 0x04, //0x00000f6d movsbq       (%r12,%rax), %rsi
	0x48, 0x8d, 0x34, 0x56, //0x00000f72 leaq         (%rsi,%rdx,2), %rsi
	0x48, 0x83, 0xc6, 0xd0, //0x00000f76 addq         $-48, %rsi
	0x48, 0x83, 0xc0, 0x01, //0x00000f7a addq         $1, %rax
	0x48, 0x89, 0xf2, //0x00000f7e movq         %rsi, %rdx
	0x48, 0xd3, 0xea, //0x00000f81 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00000f84 testq        %rdx, %rdx
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00000f87 je           LBB0_209
	0x41, 0x89, 0xc3, //0x00000f8d movl         %eax, %r11d
	//0x00000f90 LBB0_212
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000f90 movq         $-1, %rax
	0x48, 0xd3, 0xe0, //0x00000f97 shlq         %cl, %rax
	0x48, 0xf7, 0xd0, //0x00000f9a notq         %rax
	0x31, 0xdb, //0x00000f9d xorl         %ebx, %ebx
	0x45, 0x39, 0xcb, //0x00000f9f cmpl         %r9d, %r11d
	0x0f, 0x8d, 0x53, 0x00, 0x00, 0x00, //0x00000fa2 jge          LBB0_216
	0x4c, 0x89, 0x45, 0xa0, //0x00000fa8 movq         %r8, $-96(%rbp)
	0x4c, 0x89, 0x7d, 0xc8, //0x00000fac movq         %r15, $-56(%rbp)
	0x4d, 0x63, 0xfb, //0x00000fb0 movslq       %r11d, %r15
	0x4d, 0x63, 0xce, //0x00000fb3 movslq       %r14d, %r9
	0x4b, 0x8d, 0x14, 0x3c, //0x00000fb6 leaq         (%r12,%r15), %rdx
	0x31, 0xdb, //0x00000fba xorl         %ebx, %ebx
	//0x00000fbc LBB0_214
	0x48, 0x89, 0xf7, //0x00000fbc movq         %rsi, %rdi
	0x48, 0xd3, 0xef, //0x00000fbf shrq         %cl, %rdi
	0x48, 0x21, 0xc6, //0x00000fc2 andq         %rax, %rsi
	0x40, 0x80, 0xc7, 0x30, //0x00000fc5 addb         $48, %dil
	0x41, 0x88, 0x3c, 0x1c, //0x00000fc9 movb         %dil, (%r12,%rbx)
	0x48, 0x0f, 0xbe, 0x3c, 0x1a, //0x00000fcd movsbq       (%rdx,%rbx), %rdi
	0x4d, 0x8d, 0x04, 0x1f, //0x00000fd2 leaq         (%r15,%rbx), %r8
	0x49, 0x83, 0xc0, 0x01, //0x00000fd6 addq         $1, %r8
	0x48, 0x83, 0xc3, 0x01, //0x00000fda addq         $1, %rbx
	0x48, 0x8d, 0x34, 0xb6, //0x00000fde leaq         (%rsi,%rsi,4), %rsi
	0x48, 0x8d, 0x34, 0x77, //0x00000fe2 leaq         (%rdi,%rsi,2), %rsi
	0x48, 0x83, 0xc6, 0xd0, //0x00000fe6 addq         $-48, %rsi
	0x4d, 0x39, 0xc8, //0x00000fea cmpq         %r9, %r8
	0x0f, 0x8c, 0xc9, 0xff, 0xff, 0xff, //0x00000fed jl           LBB0_214
	0x4c, 0x8b, 0x7d, 0xc8, //0x00000ff3 movq         $-56(%rbp), %r15
	0x4c, 0x8b, 0x45, 0xa0, //0x00000ff7 movq         $-96(%rbp), %r8
	//0x00000ffb LBB0_216
	0x45, 0x29, 0xdf, //0x00000ffb subl         %r11d, %r15d
	0x41, 0x83, 0xc7, 0x01, //0x00000ffe addl         $1, %r15d
	0x48, 0x85, 0xf6, //0x00001002 testq        %rsi, %rsi
	0x0f, 0x85, 0x37, 0x00, 0x00, 0x00, //0x00001005 jne          LBB0_221
	0x4c, 0x8b, 0x5d, 0xa8, //0x0000100b movq         $-88(%rbp), %r11
	0xe9, 0x85, 0x00, 0x00, 0x00, //0x0000100f jmp          LBB0_225
	//0x00001014 LBB0_218
	0x48, 0x85, 0xf6, //0x00001014 testq        %rsi, %rsi
	0x0f, 0x84, 0xeb, 0x00, 0x00, 0x00, //0x00001017 je           LBB0_233
	0x48, 0x89, 0xf0, //0x0000101d movq         %rsi, %rax
	0x48, 0xd3, 0xe8, //0x00001020 shrq         %cl, %rax
	0x48, 0x85, 0xc0, //0x00001023 testq        %rax, %rax
	0x0f, 0x84, 0xac, 0x00, 0x00, 0x00, //0x00001026 je           LBB0_230
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000102c movq         $-1, %rax
	0x48, 0xd3, 0xe0, //0x00001033 shlq         %cl, %rax
	0x48, 0xf7, 0xd0, //0x00001036 notq         %rax
	0x45, 0x29, 0xdf, //0x00001039 subl         %r11d, %r15d
	0x41, 0x83, 0xc7, 0x01, //0x0000103c addl         $1, %r15d
	0x31, 0xdb, //0x00001040 xorl         %ebx, %ebx
	//0x00001042 LBB0_221
	0x4c, 0x8b, 0x5d, 0xa8, //0x00001042 movq         $-88(%rbp), %r11
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00001046 jmp          LBB0_223
	//0x0000104b LBB0_222
	0x48, 0x85, 0xd2, //0x0000104b testq        %rdx, %rdx
	0xba, 0x01, 0x00, 0x00, 0x00, //0x0000104e movl         $1, %edx
	0x44, 0x0f, 0x45, 0xd2, //0x00001053 cmovnel      %edx, %r10d
	0x48, 0x01, 0xf6, //0x00001057 addq         %rsi, %rsi
	0x48, 0x8d, 0x34, 0xb6, //0x0000105a leaq         (%rsi,%rsi,4), %rsi
	0x48, 0x85, 0xf6, //0x0000105e testq        %rsi, %rsi
	0x0f, 0x84, 0x32, 0x00, 0x00, 0x00, //0x00001061 je           LBB0_225
	//0x00001067 LBB0_223
	0x48, 0x89, 0xf2, //0x00001067 movq         %rsi, %rdx
	0x48, 0xd3, 0xea, //0x0000106a shrq         %cl, %rdx
	0x48, 0x21, 0xc6, //0x0000106d andq         %rax, %rsi
	0x48, 0x63, 0xfb, //0x00001070 movslq       %ebx, %rdi
	0x48, 0x39, 0x7d, 0xc0, //0x00001073 cmpq         %rdi, $-64(%rbp)
	0x0f, 0x86, 0xce, 0xff, 0xff, 0xff, //0x00001077 jbe          LBB0_222
	0x80, 0xc2, 0x30, //0x0000107d addb         $48, %dl
	0x41, 0x88, 0x14, 0x3c, //0x00001080 movb         %dl, (%r12,%rdi)
	0x83, 0xc7, 0x01, //0x00001084 addl         $1, %edi
	0x89, 0xfb, //0x00001087 movl         %edi, %ebx
	0x48, 0x01, 0xf6, //0x00001089 addq         %rsi, %rsi
	0x48, 0x8d, 0x34, 0xb6, //0x0000108c leaq         (%rsi,%rsi,4), %rsi
	0x48, 0x85, 0xf6, //0x00001090 testq        %rsi, %rsi
	0x0f, 0x85, 0xce, 0xff, 0xff, 0xff, //0x00001093 jne          LBB0_223
	//0x00001099 LBB0_225
	0x85, 0xdb, //0x00001099 testl        %ebx, %ebx
	0x0f, 0x8e, 0x56, 0x00, 0x00, 0x00, //0x0000109b jle          LBB0_231
	0x41, 0x89, 0xde, //0x000010a1 movl         %ebx, %r14d
	0x43, 0x80, 0x7c, 0x26, 0xff, 0x30, //0x000010a4 cmpb         $48, $-1(%r14,%r12)
	0x0f, 0x85, 0x4d, 0x00, 0x00, 0x00, //0x000010aa jne          LBB0_232
	//0x000010b0 LBB0_227
	0x49, 0x83, 0xfe, 0x01, //0x000010b0 cmpq         $1, %r14
	0x0f, 0x86, 0x5f, 0x00, 0x00, 0x00, //0x000010b4 jbe          LBB0_234
	0x41, 0x8d, 0x46, 0xfe, //0x000010ba leal         $-2(%r14), %eax
	0x49, 0x83, 0xc6, 0xff, //0x000010be addq         $-1, %r14
	0x41, 0x80, 0x3c, 0x04, 0x30, //0x000010c2 cmpb         $48, (%r12,%rax)
	0x0f, 0x84, 0xe3, 0xff, 0xff, 0xff, //0x000010c7 je           LBB0_227
	0x44, 0x89, 0xf3, //0x000010cd movl         %r14d, %ebx
	0x45, 0x89, 0xf1, //0x000010d0 movl         %r14d, %r9d
	0xe9, 0x4b, 0xfc, 0xff, 0xff, //0x000010d3 jmp          LBB0_174
	//0x000010d8 LBB0_230
	0x48, 0x01, 0xf6, //0x000010d8 addq         %rsi, %rsi
	0x48, 0x8d, 0x34, 0xb6, //0x000010db leaq         (%rsi,%rsi,4), %rsi
	0x41, 0x83, 0xc3, 0x01, //0x000010df addl         $1, %r11d
	0x48, 0x89, 0xf0, //0x000010e3 movq         %rsi, %rax
	0x48, 0xd3, 0xe8, //0x000010e6 shrq         %cl, %rax
	0x48, 0x85, 0xc0, //0x000010e9 testq        %rax, %rax
	0x0f, 0x84, 0xe6, 0xff, 0xff, 0xff, //0x000010ec je           LBB0_230
	0xe9, 0x99, 0xfe, 0xff, 0xff, //0x000010f2 jmp          LBB0_212
	//0x000010f7 LBB0_231
	0x0f, 0x84, 0x74, 0x11, 0x00, 0x00, //0x000010f7 je           LBB0_472
	//0x000010fd LBB0_232
	0x41, 0x89, 0xde, //0x000010fd movl         %ebx, %r14d
	0x41, 0x89, 0xd9, //0x00001100 movl         %ebx, %r9d
	0xe9, 0x1b, 0xfc, 0xff, 0xff, //0x00001103 jmp          LBB0_174
	//0x00001108 LBB0_233
	0x45, 0x31, 0xf6, //0x00001108 xorl         %r14d, %r14d
	0x31, 0xdb, //0x0000110b xorl         %ebx, %ebx
	0x45, 0x31, 0xc9, //0x0000110d xorl         %r9d, %r9d
	0x4c, 0x8b, 0x5d, 0xa8, //0x00001110 movq         $-88(%rbp), %r11
	0xe9, 0x0a, 0xfc, 0xff, 0xff, //0x00001114 jmp          LBB0_174
	//0x00001119 LBB0_234
	0x41, 0x83, 0xc6, 0xff, //0x00001119 addl         $-1, %r14d
	//0x0000111d LBB0_235
	0x45, 0x01, 0xd8, //0x0000111d addl         %r11d, %r8d
	0x45, 0x31, 0xff, //0x00001120 xorl         %r15d, %r15d
	//0x00001123 LBB0_236
	0x44, 0x89, 0xf3, //0x00001123 movl         %r14d, %ebx
	//0x00001126 LBB0_237
	0x4c, 0x89, 0x45, 0xa0, //0x00001126 movq         %r8, $-96(%rbp)
	0x49, 0x8d, 0x44, 0x24, 0x01, //0x0000112a leaq         $1(%r12), %rax
	0x48, 0x89, 0x45, 0x80, //0x0000112f movq         %rax, $-128(%rbp)
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001133 movl         $1, %r11d
	0x41, 0x89, 0xd8, //0x00001139 movl         %ebx, %r8d
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x0000113c jmp          LBB0_243
	//0x00001141 LBB0_238
	0x83, 0xc0, 0xff, //0x00001141 addl         $-1, %eax
	0x41, 0x89, 0xc6, //0x00001144 movl         %eax, %r14d
	//0x00001147 LBB0_239
	0x45, 0x31, 0xff, //0x00001147 xorl         %r15d, %r15d
	//0x0000114a LBB0_240
	0x45, 0x85, 0xc9, //0x0000114a testl        %r9d, %r9d
	0x0f, 0x88, 0xa1, 0x01, 0x00, 0x00, //0x0000114d js           LBB0_265
	//0x00001153 LBB0_241
	0x44, 0x89, 0xf3, //0x00001153 movl         %r14d, %ebx
	0x45, 0x89, 0xf0, //0x00001156 movl         %r14d, %r8d
	//0x00001159 LBB0_242
	0x48, 0x8b, 0x45, 0xa0, //0x00001159 movq         $-96(%rbp), %rax
	0x44, 0x29, 0xc8, //0x0000115d subl         %r9d, %eax
	0x48, 0x89, 0x45, 0xa0, //0x00001160 movq         %rax, $-96(%rbp)
	//0x00001164 LBB0_243
	0x45, 0x85, 0xff, //0x00001164 testl        %r15d, %r15d
	0x0f, 0x88, 0x16, 0x00, 0x00, 0x00, //0x00001167 js           LBB0_246
	0x0f, 0x85, 0xf2, 0x06, 0x00, 0x00, //0x0000116d jne          LBB0_341
	0x41, 0x80, 0x3c, 0x24, 0x35, //0x00001173 cmpb         $53, (%r12)
	0x0f, 0x8c, 0x25, 0x00, 0x00, 0x00, //0x00001178 jl           LBB0_249
	0xe9, 0xe2, 0x06, 0x00, 0x00, //0x0000117e jmp          LBB0_341
	//0x00001183 LBB0_246
	0x41, 0x83, 0xff, 0xf8, //0x00001183 cmpl         $-8, %r15d
	0x0f, 0x83, 0x16, 0x00, 0x00, 0x00, //0x00001187 jae          LBB0_249
	0x41, 0xb9, 0x1b, 0x00, 0x00, 0x00, //0x0000118d movl         $27, %r9d
	0x85, 0xdb, //0x00001193 testl        %ebx, %ebx
	0x0f, 0x84, 0x49, 0x05, 0x00, 0x00, //0x00001195 je           LBB0_318
	0x41, 0x89, 0xd8, //0x0000119b movl         %ebx, %r8d
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x0000119e jmp          LBB0_250
	//0x000011a3 LBB0_249
	0x44, 0x89, 0xf8, //0x000011a3 movl         %r15d, %eax
	0xf7, 0xd8, //0x000011a6 negl         %eax
	0x48, 0x8d, 0x0d, 0x21, 0x3d, 0x00, 0x00, //0x000011a8 leaq         $15649(%rip), %rcx  /* _POW_TAB+0(%rip) */
	0x44, 0x8b, 0x0c, 0x81, //0x000011af movl         (%rcx,%rax,4), %r9d
	0x45, 0x85, 0xc0, //0x000011b3 testl        %r8d, %r8d
	0x0f, 0x84, 0x30, 0x01, 0x00, 0x00, //0x000011b6 je           LBB0_264
	//0x000011bc LBB0_250
	0x44, 0x89, 0x4d, 0xa8, //0x000011bc movl         %r9d, $-88(%rbp)
	0x44, 0x89, 0xc9, //0x000011c0 movl         %r9d, %ecx
	0x48, 0x6b, 0xd1, 0x68, //0x000011c3 imulq        $104, %rcx, %rdx
	0x48, 0x8d, 0x05, 0x32, 0x3d, 0x00, 0x00, //0x000011c7 leaq         $15666(%rip), %rax  /* _LSHIFT_TAB+0(%rip) */
	0x44, 0x8b, 0x0c, 0x02, //0x000011ce movl         (%rdx,%rax), %r9d
	0x49, 0x63, 0xf0, //0x000011d2 movslq       %r8d, %rsi
	0x48, 0x8d, 0x3c, 0x02, //0x000011d5 leaq         (%rdx,%rax), %rdi
	0x48, 0x83, 0xc7, 0x04, //0x000011d9 addq         $4, %rdi
	0x31, 0xdb, //0x000011dd xorl         %ebx, %ebx
	0x4c, 0x89, 0x7d, 0xc8, //0x000011df movq         %r15, $-56(%rbp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000011e3 .p2align 4, 0x90
	//0x000011f0 LBB0_251
	0x0f, 0xb6, 0x04, 0x1f, //0x000011f0 movzbl       (%rdi,%rbx), %eax
	0x84, 0xc0, //0x000011f4 testb        %al, %al
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x000011f6 je           LBB0_256
	0x41, 0x38, 0x04, 0x1c, //0x000011fc cmpb         %al, (%r12,%rbx)
	0x0f, 0x85, 0x12, 0x03, 0x00, 0x00, //0x00001200 jne          LBB0_294
	0x48, 0x83, 0xc3, 0x01, //0x00001206 addq         $1, %rbx
	0x48, 0x39, 0xde, //0x0000120a cmpq         %rbx, %rsi
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x0000120d jne          LBB0_251
	0x44, 0x89, 0xc0, //0x00001213 movl         %r8d, %eax
	0x48, 0x8d, 0x35, 0xe3, 0x3c, 0x00, 0x00, //0x00001216 leaq         $15587(%rip), %rsi  /* _LSHIFT_TAB+0(%rip) */
	0x48, 0x01, 0xf2, //0x0000121d addq         %rsi, %rdx
	0x80, 0x7c, 0x10, 0x04, 0x00, //0x00001220 cmpb         $0, $4(%rax,%rdx)
	0x0f, 0x84, 0x04, 0x00, 0x00, 0x00, //0x00001225 je           LBB0_256
	//0x0000122b LBB0_255
	0x41, 0x83, 0xc1, 0xff, //0x0000122b addl         $-1, %r9d
	//0x0000122f LBB0_256
	0x45, 0x85, 0xc0, //0x0000122f testl        %r8d, %r8d
	0x0f, 0x8e, 0xd7, 0x02, 0x00, 0x00, //0x00001232 jle          LBB0_293
	0x4c, 0x89, 0x4d, 0x88, //0x00001238 movq         %r9, $-120(%rbp)
	0x43, 0x8d, 0x04, 0x01, //0x0000123c leal         (%r9,%r8), %eax
	0x44, 0x89, 0xc6, //0x00001240 movl         %r8d, %esi
	0x48, 0x98, //0x00001243 cltq         
	0x48, 0x89, 0xc7, //0x00001245 movq         %rax, %rdi
	0x48, 0xc1, 0xe7, 0x20, //0x00001248 shlq         $32, %rdi
	0x48, 0x83, 0xc0, 0xff, //0x0000124c addq         $-1, %rax
	0x48, 0x83, 0xc6, 0x01, //0x00001250 addq         $1, %rsi
	0x45, 0x31, 0xff, //0x00001254 xorl         %r15d, %r15d
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00001257 jmp          LBB0_260
	0x90, 0x90, 0x90, 0x90, //0x0000125c .p2align 4, 0x90
	//0x00001260 LBB0_258
	0x48, 0x85, 0xc0, //0x00001260 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xd3, //0x00001263 cmovnel      %r11d, %r10d
	//0x00001267 LBB0_259
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00001267 movabsq      $-4294967296, %rax
	0x48, 0x01, 0xc7, //0x00001271 addq         %rax, %rdi
	0x49, 0x8d, 0x40, 0xff, //0x00001274 leaq         $-1(%r8), %rax
	0x48, 0x83, 0xc6, 0xff, //0x00001278 addq         $-1, %rsi
	0x48, 0x83, 0xfe, 0x01, //0x0000127c cmpq         $1, %rsi
	0x0f, 0x86, 0x4f, 0x00, 0x00, 0x00, //0x00001280 jbe          LBB0_262
	//0x00001286 LBB0_260
	0x49, 0x89, 0xc0, //0x00001286 movq         %rax, %r8
	0x8d, 0x46, 0xfe, //0x00001289 leal         $-2(%rsi), %eax
	0x49, 0x0f, 0xbe, 0x1c, 0x04, //0x0000128c movsbq       (%r12,%rax), %rbx
	0x48, 0x83, 0xc3, 0xd0, //0x00001291 addq         $-48, %rbx
	0x48, 0xd3, 0xe3, //0x00001295 shlq         %cl, %rbx
	0x4c, 0x01, 0xfb, //0x00001298 addq         %r15, %rbx
	0x48, 0x89, 0xd8, //0x0000129b movq         %rbx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x0000129e movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x000012a8 mulq         %rdx
	0x49, 0x89, 0xd7, //0x000012ab movq         %rdx, %r15
	0x49, 0xc1, 0xef, 0x03, //0x000012ae shrq         $3, %r15
	0x4b, 0x8d, 0x04, 0x3f, //0x000012b2 leaq         (%r15,%r15), %rax
	0x48, 0x8d, 0x14, 0x80, //0x000012b6 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xd8, //0x000012ba movq         %rbx, %rax
	0x48, 0x29, 0xd0, //0x000012bd subq         %rdx, %rax
	0x4c, 0x3b, 0x45, 0xc0, //0x000012c0 cmpq         $-64(%rbp), %r8
	0x0f, 0x83, 0x96, 0xff, 0xff, 0xff, //0x000012c4 jae          LBB0_258
	0x04, 0x30, //0x000012ca addb         $48, %al
	0x43, 0x88, 0x04, 0x04, //0x000012cc movb         %al, (%r12,%r8)
	0xe9, 0x92, 0xff, 0xff, 0xff, //0x000012d0 jmp          LBB0_259
	//0x000012d5 LBB0_262
	0x48, 0x83, 0xfb, 0x0a, //0x000012d5 cmpq         $10, %rbx
	0x0f, 0x83, 0x44, 0x02, 0x00, 0x00, //0x000012d9 jae          LBB0_295
	0x48, 0x8b, 0x5d, 0xc0, //0x000012df movq         $-64(%rbp), %rbx
	0x4c, 0x8b, 0x4d, 0x88, //0x000012e3 movq         $-120(%rbp), %r9
	0xe9, 0xae, 0x02, 0x00, 0x00, //0x000012e7 jmp          LBB0_299
	//0x000012ec LBB0_264
	0x45, 0x31, 0xc0, //0x000012ec xorl         %r8d, %r8d
	0xe9, 0x65, 0xfe, 0xff, 0xff, //0x000012ef jmp          LBB0_242
	//0x000012f4 LBB0_265
	0x41, 0x83, 0xf9, 0xc3, //0x000012f4 cmpl         $-61, %r9d
	0x0f, 0x87, 0x08, 0x03, 0x00, 0x00, //0x000012f8 ja           LBB0_306
	0x45, 0x89, 0xc8, //0x000012fe movl         %r9d, %r8d
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00001301 jmp          LBB0_269
	//0x00001306 LBB0_292
	0x45, 0x31, 0xff, //0x00001306 xorl         %r15d, %r15d
	//0x00001309 LBB0_267
	0x45, 0x31, 0xc9, //0x00001309 xorl         %r9d, %r9d
	//0x0000130c LBB0_268
	0x41, 0x8d, 0x48, 0x3c, //0x0000130c leal         $60(%r8), %ecx
	0x45, 0x89, 0xce, //0x00001310 movl         %r9d, %r14d
	0x41, 0x83, 0xf8, 0x88, //0x00001313 cmpl         $-120, %r8d
	0x41, 0x89, 0xc8, //0x00001317 movl         %ecx, %r8d
	0x0f, 0x8d, 0xee, 0x02, 0x00, 0x00, //0x0000131a jge          LBB0_307
	//0x00001320 LBB0_269
	0x45, 0x85, 0xf6, //0x00001320 testl        %r14d, %r14d
	0xbe, 0x00, 0x00, 0x00, 0x00, //0x00001323 movl         $0, %esi
	0x41, 0x0f, 0x4f, 0xf6, //0x00001328 cmovgl       %r14d, %esi
	0x31, 0xc0, //0x0000132c xorl         %eax, %eax
	0x31, 0xc9, //0x0000132e xorl         %ecx, %ecx
	//0x00001330 .p2align 4, 0x90
	//0x00001330 LBB0_270
	0x48, 0x39, 0xc6, //0x00001330 cmpq         %rax, %rsi
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x00001333 je           LBB0_273
	0x48, 0x8d, 0x0c, 0x89, //0x00001339 leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x14, 0x04, //0x0000133d movsbq       (%r12,%rax), %rdx
	0x48, 0x8d, 0x0c, 0x4a, //0x00001342 leaq         (%rdx,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00001346 addq         $-48, %rcx
	0x48, 0x83, 0xc0, 0x01, //0x0000134a addq         $1, %rax
	0x4c, 0x39, 0xe9, //0x0000134e cmpq         %r13, %rcx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x00001351 jb           LBB0_270
	0x89, 0xc6, //0x00001357 movl         %eax, %esi
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00001359 jmp          LBB0_275
	//0x0000135e LBB0_273
	0x48, 0x85, 0xc9, //0x0000135e testq        %rcx, %rcx
	0x0f, 0x84, 0xa2, 0xff, 0xff, 0xff, //0x00001361 je           LBB0_267
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001367 .p2align 4, 0x90
	//0x00001370 LBB0_274
	0x48, 0x01, 0xc9, //0x00001370 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00001373 leaq         (%rcx,%rcx,4), %rcx
	0x83, 0xc6, 0x01, //0x00001377 addl         $1, %esi
	0x4c, 0x39, 0xe9, //0x0000137a cmpq         %r13, %rcx
	0x0f, 0x82, 0xed, 0xff, 0xff, 0xff, //0x0000137d jb           LBB0_274
	//0x00001383 LBB0_275
	0x41, 0x29, 0xf7, //0x00001383 subl         %esi, %r15d
	0x45, 0x31, 0xc9, //0x00001386 xorl         %r9d, %r9d
	0x44, 0x39, 0xf6, //0x00001389 cmpl         %r14d, %esi
	0x0f, 0x8d, 0xf6, 0x00, 0x00, 0x00, //0x0000138c jge          LBB0_284
	0x48, 0x63, 0xf6, //0x00001392 movslq       %esi, %rsi
	0x49, 0x63, 0xc6, //0x00001395 movslq       %r14d, %rax
	0x49, 0x89, 0xc1, //0x00001398 movq         %rax, %r9
	0x49, 0x29, 0xf1, //0x0000139b subq         %rsi, %r9
	0x48, 0x89, 0xf7, //0x0000139e movq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x000013a1 notq         %rdi
	0x48, 0x01, 0xc7, //0x000013a4 addq         %rax, %rdi
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x000013a7 jne          LBB0_278
	0x31, 0xff, //0x000013ad xorl         %edi, %edi
	0x41, 0xf6, 0xc1, 0x01, //0x000013af testb        $1, %r9b
	0x0f, 0x85, 0x82, 0x00, 0x00, 0x00, //0x000013b3 jne          LBB0_281
	0xe9, 0xa0, 0x00, 0x00, 0x00, //0x000013b9 jmp          LBB0_282
	//0x000013be LBB0_278
	0x4d, 0x89, 0xce, //0x000013be movq         %r9, %r14
	0x49, 0x83, 0xe6, 0xfe, //0x000013c1 andq         $-2, %r14
	0x49, 0xf7, 0xde, //0x000013c5 negq         %r14
	0x31, 0xff, //0x000013c8 xorl         %edi, %edi
	0x48, 0x8b, 0x45, 0x80, //0x000013ca movq         $-128(%rbp), %rax
	0x90, 0x90, //0x000013ce .p2align 4, 0x90
	//0x000013d0 LBB0_279
	0x48, 0x89, 0xcb, //0x000013d0 movq         %rcx, %rbx
	0x48, 0xc1, 0xeb, 0x3c, //0x000013d3 shrq         $60, %rbx
	0x49, 0x8d, 0x55, 0xff, //0x000013d7 leaq         $-1(%r13), %rdx
	0x48, 0x21, 0xd1, //0x000013db andq         %rdx, %rcx
	0x80, 0xcb, 0x30, //0x000013de orb          $48, %bl
	0x88, 0x58, 0xff, //0x000013e1 movb         %bl, $-1(%rax)
	0x48, 0x8d, 0x0c, 0x89, //0x000013e4 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x0f, 0xbe, 0x5c, 0x30, 0xff, //0x000013e8 movsbq       $-1(%rax,%rsi), %rbx
	0x48, 0x8d, 0x0c, 0x4b, //0x000013ee leaq         (%rbx,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x000013f2 addq         $-48, %rcx
	0x48, 0x89, 0xcb, //0x000013f6 movq         %rcx, %rbx
	0x48, 0xc1, 0xeb, 0x3c, //0x000013f9 shrq         $60, %rbx
	0x48, 0x21, 0xd1, //0x000013fd andq         %rdx, %rcx
	0x80, 0xcb, 0x30, //0x00001400 orb          $48, %bl
	0x88, 0x18, //0x00001403 movb         %bl, (%rax)
	0x48, 0x8d, 0x0c, 0x89, //0x00001405 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x0f, 0xbe, 0x14, 0x30, //0x00001409 movsbq       (%rax,%rsi), %rdx
	0x48, 0x8d, 0x0c, 0x4a, //0x0000140e leaq         (%rdx,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00001412 addq         $-48, %rcx
	0x48, 0x83, 0xc0, 0x02, //0x00001416 addq         $2, %rax
	0x48, 0x83, 0xc7, 0xfe, //0x0000141a addq         $-2, %rdi
	0x49, 0x39, 0xfe, //0x0000141e cmpq         %rdi, %r14
	0x0f, 0x85, 0xa9, 0xff, 0xff, 0xff, //0x00001421 jne          LBB0_279
	0x48, 0x29, 0xfe, //0x00001427 subq         %rdi, %rsi
	0x48, 0xf7, 0xdf, //0x0000142a negq         %rdi
	0x48, 0x8b, 0x5d, 0xc0, //0x0000142d movq         $-64(%rbp), %rbx
	0x41, 0xf6, 0xc1, 0x01, //0x00001431 testb        $1, %r9b
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x00001435 je           LBB0_282
	//0x0000143b LBB0_281
	0x49, 0x8d, 0x45, 0xff, //0x0000143b leaq         $-1(%r13), %rax
	0x48, 0x21, 0xc8, //0x0000143f andq         %rcx, %rax
	0x48, 0xc1, 0xe9, 0x3c, //0x00001442 shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x00001446 orb          $48, %cl
	0x41, 0x88, 0x0c, 0x3c, //0x00001449 movb         %cl, (%r12,%rdi)
	0x48, 0x8d, 0x04, 0x80, //0x0000144d leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x34, //0x00001451 movsbq       (%r12,%rsi), %rcx
	0x48, 0x8d, 0x0c, 0x41, //0x00001456 leaq         (%rcx,%rax,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x0000145a addq         $-48, %rcx
	//0x0000145e LBB0_282
	0x48, 0x85, 0xc9, //0x0000145e testq        %rcx, %rcx
	0x0f, 0x85, 0x21, 0x00, 0x00, 0x00, //0x00001461 jne          LBB0_284
	0xe9, 0x51, 0x00, 0x00, 0x00, //0x00001467 jmp          LBB0_286
	0x90, 0x90, 0x90, 0x90, //0x0000146c .p2align 4, 0x90
	//0x00001470 LBB0_283
	0x4c, 0x39, 0xe9, //0x00001470 cmpq         %r13, %rcx
	0x45, 0x0f, 0x43, 0xd3, //0x00001473 cmovael      %r11d, %r10d
	0x48, 0x8d, 0x0c, 0x00, //0x00001477 leaq         (%rax,%rax), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x0000147b leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc0, //0x0000147f testq        %rax, %rax
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00001482 je           LBB0_286
	//0x00001488 LBB0_284
	0x49, 0x8d, 0x45, 0xff, //0x00001488 leaq         $-1(%r13), %rax
	0x48, 0x21, 0xc8, //0x0000148c andq         %rcx, %rax
	0x49, 0x63, 0xf1, //0x0000148f movslq       %r9d, %rsi
	0x48, 0x39, 0xf3, //0x00001492 cmpq         %rsi, %rbx
	0x0f, 0x86, 0xd5, 0xff, 0xff, 0xff, //0x00001495 jbe          LBB0_283
	0x48, 0xc1, 0xe9, 0x3c, //0x0000149b shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x0000149f orb          $48, %cl
	0x41, 0x88, 0x0c, 0x34, //0x000014a2 movb         %cl, (%r12,%rsi)
	0x83, 0xc6, 0x01, //0x000014a6 addl         $1, %esi
	0x41, 0x89, 0xf1, //0x000014a9 movl         %esi, %r9d
	0x48, 0x8d, 0x0c, 0x00, //0x000014ac leaq         (%rax,%rax), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000014b0 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc0, //0x000014b4 testq        %rax, %rax
	0x0f, 0x85, 0xcb, 0xff, 0xff, 0xff, //0x000014b7 jne          LBB0_284
	//0x000014bd LBB0_286
	0x41, 0x83, 0xc7, 0x01, //0x000014bd addl         $1, %r15d
	0x45, 0x85, 0xc9, //0x000014c1 testl        %r9d, %r9d
	0x0f, 0x8e, 0x3a, 0x00, 0x00, 0x00, //0x000014c4 jle          LBB0_291
	0x44, 0x89, 0xc8, //0x000014ca movl         %r9d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x000014cd cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x33, 0xfe, 0xff, 0xff, //0x000014d3 jne          LBB0_268
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000014d9 .p2align 4, 0x90
	//0x000014e0 LBB0_288
	0x48, 0x83, 0xf8, 0x01, //0x000014e0 cmpq         $1, %rax
	0x0f, 0x86, 0x1c, 0xfe, 0xff, 0xff, //0x000014e4 jbe          LBB0_292
	0x8d, 0x48, 0xfe, //0x000014ea leal         $-2(%rax), %ecx
	0x48, 0x83, 0xc0, 0xff, //0x000014ed addq         $-1, %rax
	0x41, 0x80, 0x3c, 0x0c, 0x30, //0x000014f1 cmpb         $48, (%r12,%rcx)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x000014f6 je           LBB0_288
	0x41, 0x89, 0xc1, //0x000014fc movl         %eax, %r9d
	0xe9, 0x08, 0xfe, 0xff, 0xff, //0x000014ff jmp          LBB0_268
	//0x00001504 LBB0_291
	0x0f, 0x85, 0x02, 0xfe, 0xff, 0xff, //0x00001504 jne          LBB0_268
	0xe9, 0xf7, 0xfd, 0xff, 0xff, //0x0000150a jmp          LBB0_292
	//0x0000150f LBB0_293
	0x48, 0x8b, 0x5d, 0xc0, //0x0000150f movq         $-64(%rbp), %rbx
	0xe9, 0x82, 0x00, 0x00, 0x00, //0x00001513 jmp          LBB0_299
	//0x00001518 LBB0_294
	0x0f, 0x8c, 0x0d, 0xfd, 0xff, 0xff, //0x00001518 jl           LBB0_255
	0xe9, 0x0c, 0xfd, 0xff, 0xff, //0x0000151e jmp          LBB0_256
	//0x00001523 LBB0_295
	0x49, 0x63, 0xc8, //0x00001523 movslq       %r8d, %rcx
	0x48, 0x83, 0xc1, 0xff, //0x00001526 addq         $-1, %rcx
	0x48, 0x8b, 0x5d, 0xc0, //0x0000152a movq         $-64(%rbp), %rbx
	0x4c, 0x8b, 0x4d, 0x88, //0x0000152e movq         $-120(%rbp), %r9
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x00001532 jmp          LBB0_297
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001537 .p2align 4, 0x90
	//0x00001540 LBB0_296
	0x48, 0x85, 0xc0, //0x00001540 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xd3, //0x00001543 cmovnel      %r11d, %r10d
	0x48, 0x83, 0xc1, 0xff, //0x00001547 addq         $-1, %rcx
	0x49, 0x83, 0xff, 0x09, //0x0000154b cmpq         $9, %r15
	0x49, 0x89, 0xd7, //0x0000154f movq         %rdx, %r15
	0x0f, 0x86, 0x42, 0x00, 0x00, 0x00, //0x00001552 jbe          LBB0_299
	//0x00001558 LBB0_297
	0x4c, 0x89, 0xf8, //0x00001558 movq         %r15, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x0000155b movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00001565 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00001568 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x0000156c leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x34, 0x80, //0x00001570 leaq         (%rax,%rax,4), %rsi
	0x4c, 0x89, 0xf8, //0x00001574 movq         %r15, %rax
	0x48, 0x29, 0xf0, //0x00001577 subq         %rsi, %rax
	0x48, 0x39, 0xd9, //0x0000157a cmpq         %rbx, %rcx
	0x0f, 0x83, 0xbd, 0xff, 0xff, 0xff, //0x0000157d jae          LBB0_296
	0x04, 0x30, //0x00001583 addb         $48, %al
	0x41, 0x88, 0x04, 0x0c, //0x00001585 movb         %al, (%r12,%rcx)
	0x48, 0x83, 0xc1, 0xff, //0x00001589 addq         $-1, %rcx
	0x49, 0x83, 0xff, 0x09, //0x0000158d cmpq         $9, %r15
	0x49, 0x89, 0xd7, //0x00001591 movq         %rdx, %r15
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x00001594 ja           LBB0_297
	//0x0000159a LBB0_299
	0x45, 0x01, 0xce, //0x0000159a addl         %r9d, %r14d
	0x4d, 0x63, 0xf6, //0x0000159d movslq       %r14d, %r14
	0x4c, 0x39, 0xf3, //0x000015a0 cmpq         %r14, %rbx
	0x44, 0x0f, 0x46, 0xf3, //0x000015a3 cmovbel      %ebx, %r14d
	0x4c, 0x8b, 0x7d, 0xc8, //0x000015a7 movq         $-56(%rbp), %r15
	0x45, 0x01, 0xcf, //0x000015ab addl         %r9d, %r15d
	0x45, 0x85, 0xf6, //0x000015ae testl        %r14d, %r14d
	0x0f, 0x8e, 0x3d, 0x00, 0x00, 0x00, //0x000015b1 jle          LBB0_304
	0x44, 0x89, 0xf0, //0x000015b7 movl         %r14d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x000015ba cmpb         $48, $-1(%rax,%r12)
	0x44, 0x8b, 0x4d, 0xa8, //0x000015c0 movl         $-88(%rbp), %r9d
	0x0f, 0x85, 0x80, 0xfb, 0xff, 0xff, //0x000015c4 jne          LBB0_240
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000015ca .p2align 4, 0x90
	//0x000015d0 LBB0_301
	0x48, 0x83, 0xf8, 0x01, //0x000015d0 cmpq         $1, %rax
	0x0f, 0x86, 0x67, 0xfb, 0xff, 0xff, //0x000015d4 jbe          LBB0_238
	0x8d, 0x48, 0xfe, //0x000015da leal         $-2(%rax), %ecx
	0x48, 0x83, 0xc0, 0xff, //0x000015dd addq         $-1, %rax
	0x41, 0x80, 0x3c, 0x0c, 0x30, //0x000015e1 cmpb         $48, (%r12,%rcx)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x000015e6 je           LBB0_301
	0x41, 0x89, 0xc6, //0x000015ec movl         %eax, %r14d
	0xe9, 0x56, 0xfb, 0xff, 0xff, //0x000015ef jmp          LBB0_240
	//0x000015f4 LBB0_304
	0x44, 0x8b, 0x4d, 0xa8, //0x000015f4 movl         $-88(%rbp), %r9d
	0x0f, 0x85, 0x4c, 0xfb, 0xff, 0xff, //0x000015f8 jne          LBB0_240
	0x45, 0x31, 0xf6, //0x000015fe xorl         %r14d, %r14d
	0xe9, 0x41, 0xfb, 0xff, 0xff, //0x00001601 jmp          LBB0_239
	//0x00001606 LBB0_306
	0x44, 0x89, 0xc8, //0x00001606 movl         %r9d, %eax
	0x45, 0x89, 0xf1, //0x00001609 movl         %r14d, %r9d
	0x89, 0xc1, //0x0000160c movl         %eax, %ecx
	//0x0000160e LBB0_307
	0xf7, 0xd9, //0x0000160e negl         %ecx
	0x45, 0x85, 0xc9, //0x00001610 testl        %r9d, %r9d
	0xbf, 0x00, 0x00, 0x00, 0x00, //0x00001613 movl         $0, %edi
	0x41, 0x0f, 0x4f, 0xf9, //0x00001618 cmovgl       %r9d, %edi
	0x31, 0xf6, //0x0000161c xorl         %esi, %esi
	0x31, 0xc0, //0x0000161e xorl         %eax, %eax
	//0x00001620 .p2align 4, 0x90
	//0x00001620 LBB0_308
	0x48, 0x39, 0xf7, //0x00001620 cmpq         %rsi, %rdi
	0x0f, 0x84, 0x65, 0x00, 0x00, 0x00, //0x00001623 je           LBB0_314
	0x48, 0x8d, 0x04, 0x80, //0x00001629 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x14, 0x34, //0x0000162d movsbq       (%r12,%rsi), %rdx
	0x48, 0x8d, 0x04, 0x42, //0x00001632 leaq         (%rdx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00001636 addq         $-48, %rax
	0x48, 0x83, 0xc6, 0x01, //0x0000163a addq         $1, %rsi
	0x48, 0x89, 0xc2, //0x0000163e movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00001641 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00001644 testq        %rdx, %rdx
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00001647 je           LBB0_308
	0x89, 0xf7, //0x0000164d movl         %esi, %edi
	//0x0000164f LBB0_311
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000164f movq         $-1, %r8
	0x49, 0xd3, 0xe0, //0x00001656 shlq         %cl, %r8
	0x49, 0xf7, 0xd0, //0x00001659 notq         %r8
	0x45, 0x31, 0xf6, //0x0000165c xorl         %r14d, %r14d
	0x44, 0x39, 0xcf, //0x0000165f cmpl         %r9d, %edi
	0x0f, 0x8d, 0x86, 0x00, 0x00, 0x00, //0x00001662 jge          LBB0_319
	0x4c, 0x89, 0x7d, 0xc8, //0x00001668 movq         %r15, $-56(%rbp)
	0x4c, 0x63, 0xff, //0x0000166c movslq       %edi, %r15
	0x49, 0x63, 0xd1, //0x0000166f movslq       %r9d, %rdx
	0x49, 0x89, 0xd6, //0x00001672 movq         %rdx, %r14
	0x4d, 0x29, 0xfe, //0x00001675 subq         %r15, %r14
	0x4c, 0x89, 0xfe, //0x00001678 movq         %r15, %rsi
	0x48, 0xf7, 0xd6, //0x0000167b notq         %rsi
	0x48, 0x01, 0xd6, //0x0000167e addq         %rdx, %rsi
	0x0f, 0x85, 0x81, 0x00, 0x00, 0x00, //0x00001681 jne          LBB0_321
	0x31, 0xd2, //0x00001687 xorl         %edx, %edx
	0xe9, 0xe5, 0x00, 0x00, 0x00, //0x00001689 jmp          LBB0_324
	//0x0000168e LBB0_314
	0x48, 0x85, 0xc0, //0x0000168e testq        %rax, %rax
	0x0f, 0x84, 0x60, 0x00, 0x00, 0x00, //0x00001691 je           LBB0_320
	0x48, 0x89, 0xc2, //0x00001697 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x0000169a shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x0000169d testq        %rdx, %rdx
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x000016a0 je           LBB0_317
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000016a6 movq         $-1, %r8
	0x49, 0xd3, 0xe0, //0x000016ad shlq         %cl, %r8
	0x49, 0xf7, 0xd0, //0x000016b0 notq         %r8
	0x41, 0x29, 0xff, //0x000016b3 subl         %edi, %r15d
	0x41, 0x83, 0xc7, 0x01, //0x000016b6 addl         $1, %r15d
	0x45, 0x31, 0xf6, //0x000016ba xorl         %r14d, %r14d
	0x44, 0x8b, 0x4d, 0xa8, //0x000016bd movl         $-88(%rbp), %r9d
	0xe9, 0xfa, 0x00, 0x00, 0x00, //0x000016c1 jmp          LBB0_328
	//0x000016c6 LBB0_317
	0x48, 0x01, 0xc0, //0x000016c6 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x000016c9 leaq         (%rax,%rax,4), %rax
	0x83, 0xc7, 0x01, //0x000016cd addl         $1, %edi
	0x48, 0x89, 0xc2, //0x000016d0 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x000016d3 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x000016d6 testq        %rdx, %rdx
	0x0f, 0x84, 0xe7, 0xff, 0xff, 0xff, //0x000016d9 je           LBB0_317
	0xe9, 0x6b, 0xff, 0xff, 0xff, //0x000016df jmp          LBB0_311
	//0x000016e4 LBB0_318
	0x31, 0xdb, //0x000016e4 xorl         %ebx, %ebx
	0x45, 0x31, 0xc0, //0x000016e6 xorl         %r8d, %r8d
	0xe9, 0x6b, 0xfa, 0xff, 0xff, //0x000016e9 jmp          LBB0_242
	//0x000016ee LBB0_319
	0x44, 0x8b, 0x4d, 0xa8, //0x000016ee movl         $-88(%rbp), %r9d
	0xe9, 0xb0, 0x00, 0x00, 0x00, //0x000016f2 jmp          LBB0_327
	//0x000016f7 LBB0_320
	0x45, 0x31, 0xf6, //0x000016f7 xorl         %r14d, %r14d
	0x31, 0xdb, //0x000016fa xorl         %ebx, %ebx
	0x45, 0x31, 0xc0, //0x000016fc xorl         %r8d, %r8d
	0x44, 0x8b, 0x4d, 0xa8, //0x000016ff movl         $-88(%rbp), %r9d
	0xe9, 0x51, 0xfa, 0xff, 0xff, //0x00001703 jmp          LBB0_242
	//0x00001708 LBB0_321
	0x4d, 0x89, 0xf1, //0x00001708 movq         %r14, %r9
	0x49, 0x83, 0xe1, 0xfe, //0x0000170b andq         $-2, %r9
	0x49, 0xf7, 0xd9, //0x0000170f negq         %r9
	0x31, 0xd2, //0x00001712 xorl         %edx, %edx
	0x48, 0x8b, 0x75, 0x80, //0x00001714 movq         $-128(%rbp), %rsi
	//0x00001718 LBB0_322
	0x48, 0x89, 0xc3, //0x00001718 movq         %rax, %rbx
	0x48, 0xd3, 0xeb, //0x0000171b shrq         %cl, %rbx
	0x4c, 0x21, 0xc0, //0x0000171e andq         %r8, %rax
	0x80, 0xc3, 0x30, //0x00001721 addb         $48, %bl
	0x88, 0x5e, 0xff, //0x00001724 movb         %bl, $-1(%rsi)
	0x48, 0x8d, 0x04, 0x80, //0x00001727 leaq         (%rax,%rax,4), %rax
	0x4a, 0x0f, 0xbe, 0x5c, 0x3e, 0xff, //0x0000172b movsbq       $-1(%rsi,%r15), %rbx
	0x48, 0x8d, 0x04, 0x43, //0x00001731 leaq         (%rbx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00001735 addq         $-48, %rax
	0x48, 0x89, 0xc3, //0x00001739 movq         %rax, %rbx
	0x48, 0xd3, 0xeb, //0x0000173c shrq         %cl, %rbx
	0x4c, 0x21, 0xc0, //0x0000173f andq         %r8, %rax
	0x80, 0xc3, 0x30, //0x00001742 addb         $48, %bl
	0x88, 0x1e, //0x00001745 movb         %bl, (%rsi)
	0x48, 0x8d, 0x04, 0x80, //0x00001747 leaq         (%rax,%rax,4), %rax
	0x4a, 0x0f, 0xbe, 0x1c, 0x3e, //0x0000174b movsbq       (%rsi,%r15), %rbx
	0x48, 0x8d, 0x04, 0x43, //0x00001750 leaq         (%rbx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00001754 addq         $-48, %rax
	0x48, 0x83, 0xc6, 0x02, //0x00001758 addq         $2, %rsi
	0x48, 0x83, 0xc2, 0xfe, //0x0000175c addq         $-2, %rdx
	0x49, 0x39, 0xd1, //0x00001760 cmpq         %rdx, %r9
	0x0f, 0x85, 0xaf, 0xff, 0xff, 0xff, //0x00001763 jne          LBB0_322
	0x49, 0x29, 0xd7, //0x00001769 subq         %rdx, %r15
	0x48, 0xf7, 0xda, //0x0000176c negq         %rdx
	0x48, 0x8b, 0x5d, 0xc0, //0x0000176f movq         $-64(%rbp), %rbx
	//0x00001773 LBB0_324
	0x41, 0xf6, 0xc6, 0x01, //0x00001773 testb        $1, %r14b
	0x44, 0x8b, 0x4d, 0xa8, //0x00001777 movl         $-88(%rbp), %r9d
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x0000177b je           LBB0_326
	0x48, 0x89, 0xc6, //0x00001781 movq         %rax, %rsi
	0x48, 0xd3, 0xee, //0x00001784 shrq         %cl, %rsi
	0x4c, 0x21, 0xc0, //0x00001787 andq         %r8, %rax
	0x40, 0x80, 0xc6, 0x30, //0x0000178a addb         $48, %sil
	0x41, 0x88, 0x34, 0x14, //0x0000178e movb         %sil, (%r12,%rdx)
	0x48, 0x8d, 0x04, 0x80, //0x00001792 leaq         (%rax,%rax,4), %rax
	0x4b, 0x0f, 0xbe, 0x14, 0x3c, //0x00001796 movsbq       (%r12,%r15), %rdx
	0x48, 0x8d, 0x04, 0x42, //0x0000179b leaq         (%rdx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x0000179f addq         $-48, %rax
	//0x000017a3 LBB0_326
	0x4c, 0x8b, 0x7d, 0xc8, //0x000017a3 movq         $-56(%rbp), %r15
	//0x000017a7 LBB0_327
	0x41, 0x29, 0xff, //0x000017a7 subl         %edi, %r15d
	0x41, 0x83, 0xc7, 0x01, //0x000017aa addl         $1, %r15d
	0xe9, 0x4b, 0x00, 0x00, 0x00, //0x000017ae jmp          LBB0_332
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000017b3 .p2align 4, 0x90
	//0x000017c0 LBB0_328
	0x48, 0x89, 0xc2, //0x000017c0 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x000017c3 shrq         %cl, %rdx
	0x4c, 0x21, 0xc0, //0x000017c6 andq         %r8, %rax
	0x49, 0x63, 0xf6, //0x000017c9 movslq       %r14d, %rsi
	0x48, 0x39, 0xf3, //0x000017cc cmpq         %rsi, %rbx
	0x0f, 0x86, 0x1b, 0x00, 0x00, 0x00, //0x000017cf jbe          LBB0_330
	0x80, 0xc2, 0x30, //0x000017d5 addb         $48, %dl
	0x41, 0x88, 0x14, 0x34, //0x000017d8 movb         %dl, (%r12,%rsi)
	0x83, 0xc6, 0x01, //0x000017dc addl         $1, %esi
	0x41, 0x89, 0xf6, //0x000017df movl         %esi, %r14d
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x000017e2 jmp          LBB0_331
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000017e7 .p2align 4, 0x90
	//0x000017f0 LBB0_330
	0x48, 0x85, 0xd2, //0x000017f0 testq        %rdx, %rdx
	0x45, 0x0f, 0x45, 0xd3, //0x000017f3 cmovnel      %r11d, %r10d
	//0x000017f7 LBB0_331
	0x48, 0x01, 0xc0, //0x000017f7 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x000017fa leaq         (%rax,%rax,4), %rax
	//0x000017fe LBB0_332
	0x48, 0x85, 0xc0, //0x000017fe testq        %rax, %rax
	0x0f, 0x85, 0xb9, 0xff, 0xff, 0xff, //0x00001801 jne          LBB0_328
	0x45, 0x85, 0xf6, //0x00001807 testl        %r14d, %r14d
	0x0f, 0x8e, 0x36, 0x00, 0x00, 0x00, //0x0000180a jle          LBB0_338
	0x44, 0x89, 0xf3, //0x00001810 movl         %r14d, %ebx
	0x42, 0x80, 0x7c, 0x23, 0xff, 0x30, //0x00001813 cmpb         $48, $-1(%rbx,%r12)
	0x0f, 0x85, 0x34, 0xf9, 0xff, 0xff, //0x00001819 jne          LBB0_241
	//0x0000181f LBB0_335
	0x48, 0x83, 0xfb, 0x01, //0x0000181f cmpq         $1, %rbx
	0x0f, 0x86, 0x2e, 0x00, 0x00, 0x00, //0x00001823 jbe          LBB0_340
	0x8d, 0x43, 0xfe, //0x00001829 leal         $-2(%rbx), %eax
	0x48, 0x83, 0xc3, 0xff, //0x0000182c addq         $-1, %rbx
	0x41, 0x80, 0x3c, 0x04, 0x30, //0x00001830 cmpb         $48, (%r12,%rax)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00001835 je           LBB0_335
	0x41, 0x89, 0xde, //0x0000183b movl         %ebx, %r14d
	0x41, 0x89, 0xd8, //0x0000183e movl         %ebx, %r8d
	0xe9, 0x13, 0xf9, 0xff, 0xff, //0x00001841 jmp          LBB0_242
	//0x00001846 LBB0_338
	0x0f, 0x85, 0x07, 0xf9, 0xff, 0xff, //0x00001846 jne          LBB0_241
	0x45, 0x31, 0xf6, //0x0000184c xorl         %r14d, %r14d
	0x45, 0x31, 0xff, //0x0000184f xorl         %r15d, %r15d
	0xe9, 0xfc, 0xf8, 0xff, 0xff, //0x00001852 jmp          LBB0_241
	//0x00001857 LBB0_340
	0x83, 0xc3, 0xff, //0x00001857 addl         $-1, %ebx
	0x41, 0x89, 0xde, //0x0000185a movl         %ebx, %r14d
	0x45, 0x31, 0xff, //0x0000185d xorl         %r15d, %r15d
	0xe9, 0xee, 0xf8, 0xff, 0xff, //0x00001860 jmp          LBB0_241
	//0x00001865 LBB0_341
	0x48, 0x8b, 0x45, 0xa0, //0x00001865 movq         $-96(%rbp), %rax
	0x3d, 0x02, 0xfc, 0xff, 0xff, //0x00001869 cmpl         $-1022, %eax
	0x0f, 0x8f, 0xf3, 0x01, 0x00, 0x00, //0x0000186e jg           LBB0_371
	0x4c, 0x89, 0x7d, 0xc8, //0x00001874 movq         %r15, $-56(%rbp)
	0x41, 0xbf, 0x02, 0xfc, 0xff, 0xff, //0x00001878 movl         $-1022, %r15d
	0x45, 0x85, 0xc0, //0x0000187e testl        %r8d, %r8d
	0x0f, 0x84, 0x72, 0x04, 0x00, 0x00, //0x00001881 je           LBB0_410
	0x48, 0x8b, 0x45, 0xa0, //0x00001887 movq         $-96(%rbp), %rax
	0x8d, 0xb8, 0xfd, 0x03, 0x00, 0x00, //0x0000188b leal         $1021(%rax), %edi
	0x3d, 0xc6, 0xfb, 0xff, 0xff, //0x00001891 cmpl         $-1082, %eax
	0x0f, 0x87, 0xe5, 0x01, 0x00, 0x00, //0x00001896 ja           LBB0_373
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000189c movl         $1, %r9d
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x000018a2 jmp          LBB0_347
	//0x000018a7 LBB0_370
	0x31, 0xc0, //0x000018a7 xorl         %eax, %eax
	0x48, 0x89, 0x45, 0xc8, //0x000018a9 movq         %rax, $-56(%rbp)
	//0x000018ad LBB0_345
	0x45, 0x31, 0xf6, //0x000018ad xorl         %r14d, %r14d
	//0x000018b0 LBB0_346
	0x8d, 0x4f, 0x3c, //0x000018b0 leal         $60(%rdi), %ecx
	0x45, 0x89, 0xf0, //0x000018b3 movl         %r14d, %r8d
	0x45, 0x89, 0xf3, //0x000018b6 movl         %r14d, %r11d
	0x83, 0xff, 0x88, //0x000018b9 cmpl         $-120, %edi
	0x89, 0xcf, //0x000018bc movl         %ecx, %edi
	0x0f, 0x8d, 0xc2, 0x01, 0x00, 0x00, //0x000018be jge          LBB0_374
	//0x000018c4 LBB0_347
	0x45, 0x85, 0xc0, //0x000018c4 testl        %r8d, %r8d
	0xbb, 0x00, 0x00, 0x00, 0x00, //0x000018c7 movl         $0, %ebx
	0x41, 0x0f, 0x4f, 0xd8, //0x000018cc cmovgl       %r8d, %ebx
	0x31, 0xf6, //0x000018d0 xorl         %esi, %esi
	0x31, 0xc9, //0x000018d2 xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000018d4 .p2align 4, 0x90
	//0x000018e0 LBB0_348
	0x48, 0x39, 0xf3, //0x000018e0 cmpq         %rsi, %rbx
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x000018e3 je           LBB0_351
	0x48, 0x8d, 0x04, 0x89, //0x000018e9 leaq         (%rcx,%rcx,4), %rax
	0x49, 0x0f, 0xbe, 0x0c, 0x34, //0x000018ed movsbq       (%r12,%rsi), %rcx
	0x48, 0x8d, 0x0c, 0x41, //0x000018f2 leaq         (%rcx,%rax,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x000018f6 addq         $-48, %rcx
	0x48, 0x83, 0xc6, 0x01, //0x000018fa addq         $1, %rsi
	0x4c, 0x39, 0xe9, //0x000018fe cmpq         %r13, %rcx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x00001901 jb           LBB0_348
	0x89, 0xf3, //0x00001907 movl         %esi, %ebx
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00001909 jmp          LBB0_353
	//0x0000190e LBB0_351
	0x48, 0x85, 0xc9, //0x0000190e testq        %rcx, %rcx
	0x0f, 0x84, 0x96, 0xff, 0xff, 0xff, //0x00001911 je           LBB0_345
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001917 .p2align 4, 0x90
	//0x00001920 LBB0_352
	0x48, 0x01, 0xc9, //0x00001920 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00001923 leaq         (%rcx,%rcx,4), %rcx
	0x83, 0xc3, 0x01, //0x00001927 addl         $1, %ebx
	0x4c, 0x39, 0xe9, //0x0000192a cmpq         %r13, %rcx
	0x0f, 0x82, 0xed, 0xff, 0xff, 0xff, //0x0000192d jb           LBB0_352
	//0x00001933 LBB0_353
	0x48, 0x8b, 0x45, 0xc8, //0x00001933 movq         $-56(%rbp), %rax
	0x29, 0xd8, //0x00001937 subl         %ebx, %eax
	0x48, 0x89, 0x45, 0xc8, //0x00001939 movq         %rax, $-56(%rbp)
	0x31, 0xf6, //0x0000193d xorl         %esi, %esi
	0x44, 0x39, 0xc3, //0x0000193f cmpl         %r8d, %ebx
	0x0f, 0x8d, 0x68, 0x00, 0x00, 0x00, //0x00001942 jge          LBB0_358
	0x49, 0x89, 0xf8, //0x00001948 movq         %rdi, %r8
	0x48, 0x63, 0xc3, //0x0000194b movslq       %ebx, %rax
	0x49, 0x63, 0xf6, //0x0000194e movslq       %r14d, %rsi
	0x49, 0x8d, 0x1c, 0x04, //0x00001951 leaq         (%r12,%rax), %rbx
	0x45, 0x31, 0xf6, //0x00001955 xorl         %r14d, %r14d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001958 .p2align 4, 0x90
	//0x00001960 LBB0_355
	0x49, 0x8d, 0x55, 0xff, //0x00001960 leaq         $-1(%r13), %rdx
	0x48, 0x21, 0xca, //0x00001964 andq         %rcx, %rdx
	0x48, 0xc1, 0xe9, 0x3c, //0x00001967 shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x0000196b orb          $48, %cl
	0x43, 0x88, 0x0c, 0x34, //0x0000196e movb         %cl, (%r12,%r14)
	0x4a, 0x0f, 0xbe, 0x0c, 0x33, //0x00001972 movsbq       (%rbx,%r14), %rcx
	0x4a, 0x8d, 0x3c, 0x30, //0x00001977 leaq         (%rax,%r14), %rdi
	0x48, 0x83, 0xc7, 0x01, //0x0000197b addq         $1, %rdi
	0x49, 0x83, 0xc6, 0x01, //0x0000197f addq         $1, %r14
	0x48, 0x8d, 0x14, 0x92, //0x00001983 leaq         (%rdx,%rdx,4), %rdx
	0x48, 0x8d, 0x0c, 0x51, //0x00001987 leaq         (%rcx,%rdx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x0000198b addq         $-48, %rcx
	0x48, 0x39, 0xf7, //0x0000198f cmpq         %rsi, %rdi
	0x0f, 0x8c, 0xc8, 0xff, 0xff, 0xff, //0x00001992 jl           LBB0_355
	0x48, 0x85, 0xc9, //0x00001998 testq        %rcx, %rcx
	0x0f, 0x84, 0x71, 0x00, 0x00, 0x00, //0x0000199b je           LBB0_363
	0x44, 0x89, 0xf6, //0x000019a1 movl         %r14d, %esi
	0x48, 0x8b, 0x55, 0xc0, //0x000019a4 movq         $-64(%rbp), %rdx
	0x4c, 0x89, 0xc7, //0x000019a8 movq         %r8, %rdi
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x000019ab jmp          LBB0_359
	//0x000019b0 LBB0_358
	0x48, 0x8b, 0x55, 0xc0, //0x000019b0 movq         $-64(%rbp), %rdx
	//0x000019b4 LBB0_359
	0x41, 0x89, 0xf6, //0x000019b4 movl         %esi, %r14d
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x000019b7 jmp          LBB0_361
	0x90, 0x90, 0x90, 0x90, //0x000019bc .p2align 4, 0x90
	//0x000019c0 LBB0_360
	0x4c, 0x39, 0xe9, //0x000019c0 cmpq         %r13, %rcx
	0x45, 0x0f, 0x43, 0xd1, //0x000019c3 cmovael      %r9d, %r10d
	0x48, 0x8d, 0x0c, 0x00, //0x000019c7 leaq         (%rax,%rax), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000019cb leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc0, //0x000019cf testq        %rax, %rax
	0x0f, 0x84, 0x3d, 0x00, 0x00, 0x00, //0x000019d2 je           LBB0_364
	//0x000019d8 LBB0_361
	0x49, 0x8d, 0x45, 0xff, //0x000019d8 leaq         $-1(%r13), %rax
	0x48, 0x21, 0xc8, //0x000019dc andq         %rcx, %rax
	0x49, 0x63, 0xf6, //0x000019df movslq       %r14d, %rsi
	0x48, 0x39, 0xf2, //0x000019e2 cmpq         %rsi, %rdx
	0x0f, 0x86, 0xd5, 0xff, 0xff, 0xff, //0x000019e5 jbe          LBB0_360
	0x48, 0xc1, 0xe9, 0x3c, //0x000019eb shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x000019ef orb          $48, %cl
	0x41, 0x88, 0x0c, 0x34, //0x000019f2 movb         %cl, (%r12,%rsi)
	0x83, 0xc6, 0x01, //0x000019f6 addl         $1, %esi
	0x41, 0x89, 0xf6, //0x000019f9 movl         %esi, %r14d
	0x48, 0x8d, 0x0c, 0x00, //0x000019fc leaq         (%rax,%rax), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00001a00 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xc0, //0x00001a04 testq        %rax, %rax
	0x0f, 0x85, 0xcb, 0xff, 0xff, 0xff, //0x00001a07 jne          LBB0_361
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00001a0d jmp          LBB0_364
	//0x00001a12 LBB0_363
	0x4c, 0x89, 0xc7, //0x00001a12 movq         %r8, %rdi
	//0x00001a15 LBB0_364
	0x48, 0x8b, 0x45, 0xc8, //0x00001a15 movq         $-56(%rbp), %rax
	0x83, 0xc0, 0x01, //0x00001a19 addl         $1, %eax
	0x48, 0x89, 0x45, 0xc8, //0x00001a1c movq         %rax, $-56(%rbp)
	0x45, 0x85, 0xf6, //0x00001a20 testl        %r14d, %r14d
	0x0f, 0x8e, 0x33, 0x00, 0x00, 0x00, //0x00001a23 jle          LBB0_369
	0x44, 0x89, 0xf0, //0x00001a29 movl         %r14d, %eax
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00001a2c cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x78, 0xfe, 0xff, 0xff, //0x00001a32 jne          LBB0_346
	//0x00001a38 LBB0_366
	0x48, 0x83, 0xf8, 0x01, //0x00001a38 cmpq         $1, %rax
	0x0f, 0x86, 0x65, 0xfe, 0xff, 0xff, //0x00001a3c jbe          LBB0_370
	0x8d, 0x48, 0xfe, //0x00001a42 leal         $-2(%rax), %ecx
	0x48, 0x83, 0xc0, 0xff, //0x00001a45 addq         $-1, %rax
	0x41, 0x80, 0x3c, 0x0c, 0x30, //0x00001a49 cmpb         $48, (%r12,%rcx)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00001a4e je           LBB0_366
	0x41, 0x89, 0xc6, //0x00001a54 movl         %eax, %r14d
	0xe9, 0x54, 0xfe, 0xff, 0xff, //0x00001a57 jmp          LBB0_346
	//0x00001a5c LBB0_369
	0x0f, 0x85, 0x4e, 0xfe, 0xff, 0xff, //0x00001a5c jne          LBB0_346
	0xe9, 0x40, 0xfe, 0xff, 0xff, //0x00001a62 jmp          LBB0_370
	//0x00001a67 LBB0_371
	0x3d, 0x00, 0x04, 0x00, 0x00, //0x00001a67 cmpl         $1024, %eax
	0x0f, 0x8f, 0xd7, 0xf1, 0xff, 0xff, //0x00001a6c jg           LBB0_160
	0x4c, 0x89, 0x7d, 0xc8, //0x00001a72 movq         %r15, $-56(%rbp)
	0x83, 0xc0, 0xff, //0x00001a76 addl         $-1, %eax
	0x41, 0x89, 0xc7, //0x00001a79 movl         %eax, %r15d
	0xe9, 0xba, 0x01, 0x00, 0x00, //0x00001a7c jmp          LBB0_398
	//0x00001a81 LBB0_373
	0x45, 0x89, 0xc3, //0x00001a81 movl         %r8d, %r11d
	0x89, 0xf9, //0x00001a84 movl         %edi, %ecx
	//0x00001a86 LBB0_374
	0xf7, 0xd9, //0x00001a86 negl         %ecx
	0x31, 0xd2, //0x00001a88 xorl         %edx, %edx
	0x45, 0x85, 0xdb, //0x00001a8a testl        %r11d, %r11d
	0xbe, 0x00, 0x00, 0x00, 0x00, //0x00001a8d movl         $0, %esi
	0x41, 0x0f, 0x4f, 0xf3, //0x00001a92 cmovgl       %r11d, %esi
	0x31, 0xc0, //0x00001a96 xorl         %eax, %eax
	//0x00001a98 LBB0_375
	0x48, 0x39, 0xd6, //0x00001a98 cmpq         %rdx, %rsi
	0x0f, 0x84, 0xa0, 0x00, 0x00, 0x00, //0x00001a9b je           LBB0_382
	0x48, 0x8d, 0x04, 0x80, //0x00001aa1 leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x3c, 0x14, //0x00001aa5 movsbq       (%r12,%rdx), %rdi
	0x48, 0x8d, 0x04, 0x47, //0x00001aaa leaq         (%rdi,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00001aae addq         $-48, %rax
	0x48, 0x83, 0xc2, 0x01, //0x00001ab2 addq         $1, %rdx
	0x48, 0x89, 0xc7, //0x00001ab6 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00001ab9 shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x00001abc testq        %rdi, %rdi
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00001abf je           LBB0_375
	0x89, 0xd6, //0x00001ac5 movl         %edx, %esi
	0x4c, 0x8b, 0x7d, 0xc8, //0x00001ac7 movq         $-56(%rbp), %r15
	//0x00001acb LBB0_378
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00001acb movq         $-1, %rdx
	0x48, 0xd3, 0xe2, //0x00001ad2 shlq         %cl, %rdx
	0x48, 0xf7, 0xd2, //0x00001ad5 notq         %rdx
	0x45, 0x31, 0xed, //0x00001ad8 xorl         %r13d, %r13d
	0x44, 0x39, 0xde, //0x00001adb cmpl         %r11d, %esi
	0x0f, 0x8d, 0x44, 0x00, 0x00, 0x00, //0x00001ade jge          LBB0_381
	0x4c, 0x63, 0xce, //0x00001ae4 movslq       %esi, %r9
	0x4d, 0x63, 0xc6, //0x00001ae7 movslq       %r14d, %r8
	0x4f, 0x8d, 0x1c, 0x0c, //0x00001aea leaq         (%r12,%r9), %r11
	0x45, 0x31, 0xed, //0x00001aee xorl         %r13d, %r13d
	//0x00001af1 LBB0_380
	0x48, 0x89, 0xc7, //0x00001af1 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00001af4 shrq         %cl, %rdi
	0x48, 0x21, 0xd0, //0x00001af7 andq         %rdx, %rax
	0x40, 0x80, 0xc7, 0x30, //0x00001afa addb         $48, %dil
	0x43, 0x88, 0x3c, 0x2c, //0x00001afe movb         %dil, (%r12,%r13)
	0x4b, 0x0f, 0xbe, 0x3c, 0x2b, //0x00001b02 movsbq       (%r11,%r13), %rdi
	0x4b, 0x8d, 0x1c, 0x29, //0x00001b07 leaq         (%r9,%r13), %rbx
	0x48, 0x83, 0xc3, 0x01, //0x00001b0b addq         $1, %rbx
	0x49, 0x83, 0xc5, 0x01, //0x00001b0f addq         $1, %r13
	0x48, 0x8d, 0x04, 0x80, //0x00001b13 leaq         (%rax,%rax,4), %rax
	0x48, 0x8d, 0x04, 0x47, //0x00001b17 leaq         (%rdi,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x00001b1b addq         $-48, %rax
	0x4c, 0x39, 0xc3, //0x00001b1f cmpq         %r8, %rbx
	0x0f, 0x8c, 0xc9, 0xff, 0xff, 0xff, //0x00001b22 jl           LBB0_380
	//0x00001b28 LBB0_381
	0x4c, 0x8b, 0x75, 0xb0, //0x00001b28 movq         $-80(%rbp), %r14
	0x41, 0x29, 0xf7, //0x00001b2c subl         %esi, %r15d
	0x41, 0x83, 0xc7, 0x01, //0x00001b2f addl         $1, %r15d
	0x48, 0x85, 0xc0, //0x00001b33 testq        %rax, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00001b36 jne          LBB0_385
	0xe9, 0x8c, 0x00, 0x00, 0x00, //0x00001b3c jmp          LBB0_389
	//0x00001b41 LBB0_382
	0x48, 0x85, 0xc0, //0x00001b41 testq        %rax, %rax
	0x0f, 0x84, 0xaf, 0x01, 0x00, 0x00, //0x00001b44 je           LBB0_410
	0x48, 0x89, 0xc2, //0x00001b4a movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00001b4d shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00001b50 testq        %rdx, %rdx
	0x0f, 0x84, 0xee, 0x06, 0x00, 0x00, //0x00001b53 je           LBB0_469
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00001b59 movq         $-1, %rdx
	0x48, 0xd3, 0xe2, //0x00001b60 shlq         %cl, %rdx
	0x48, 0xf7, 0xd2, //0x00001b63 notq         %rdx
	0x4c, 0x8b, 0x7d, 0xc8, //0x00001b66 movq         $-56(%rbp), %r15
	0x41, 0x29, 0xf7, //0x00001b6a subl         %esi, %r15d
	0x41, 0x83, 0xc7, 0x01, //0x00001b6d addl         $1, %r15d
	0x45, 0x31, 0xed, //0x00001b71 xorl         %r13d, %r13d
	0x4c, 0x8b, 0x75, 0xb0, //0x00001b74 movq         $-80(%rbp), %r14
	//0x00001b78 LBB0_385
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00001b78 movl         $1, %esi
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00001b7d jmp          LBB0_387
	//0x00001b82 LBB0_386
	0x48, 0x85, 0xff, //0x00001b82 testq        %rdi, %rdi
	0x44, 0x0f, 0x45, 0xd6, //0x00001b85 cmovnel      %esi, %r10d
	0x48, 0x01, 0xc0, //0x00001b89 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001b8c leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x00001b90 testq        %rax, %rax
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x00001b93 je           LBB0_389
	//0x00001b99 LBB0_387
	0x48, 0x89, 0xc7, //0x00001b99 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00001b9c shrq         %cl, %rdi
	0x48, 0x21, 0xd0, //0x00001b9f andq         %rdx, %rax
	0x49, 0x63, 0xdd, //0x00001ba2 movslq       %r13d, %rbx
	0x48, 0x39, 0x5d, 0xc0, //0x00001ba5 cmpq         %rbx, $-64(%rbp)
	0x0f, 0x86, 0xd3, 0xff, 0xff, 0xff, //0x00001ba9 jbe          LBB0_386
	0x40, 0x80, 0xc7, 0x30, //0x00001baf addb         $48, %dil
	0x41, 0x88, 0x3c, 0x1c, //0x00001bb3 movb         %dil, (%r12,%rbx)
	0x83, 0xc3, 0x01, //0x00001bb7 addl         $1, %ebx
	0x41, 0x89, 0xdd, //0x00001bba movl         %ebx, %r13d
	0x48, 0x01, 0xc0, //0x00001bbd addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x00001bc0 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x00001bc4 testq        %rax, %rax
	0x0f, 0x85, 0xcc, 0xff, 0xff, 0xff, //0x00001bc7 jne          LBB0_387
	//0x00001bcd LBB0_389
	0x4c, 0x89, 0x7d, 0xc8, //0x00001bcd movq         %r15, $-56(%rbp)
	0x45, 0x85, 0xed, //0x00001bd1 testl        %r13d, %r13d
	0x0f, 0x8e, 0x3a, 0x00, 0x00, 0x00, //0x00001bd4 jle          LBB0_393
	0x44, 0x89, 0xe8, //0x00001bda movl         %r13d, %eax
	0x41, 0xbf, 0x02, 0xfc, 0xff, 0xff, //0x00001bdd movl         $-1022, %r15d
	0x42, 0x80, 0x7c, 0x20, 0xff, 0x30, //0x00001be3 cmpb         $48, $-1(%rax,%r12)
	0x0f, 0x85, 0x31, 0x00, 0x00, 0x00, //0x00001be9 jne          LBB0_395
	//0x00001bef LBB0_391
	0x49, 0x89, 0xc6, //0x00001bef movq         %rax, %r14
	0x48, 0x83, 0xf8, 0x01, //0x00001bf2 cmpq         $1, %rax
	0x0f, 0x86, 0x2c, 0x00, 0x00, 0x00, //0x00001bf6 jbe          LBB0_396
	0x41, 0x8d, 0x4e, 0xfe, //0x00001bfc leal         $-2(%r14), %ecx
	0x49, 0x8d, 0x46, 0xff, //0x00001c00 leaq         $-1(%r14), %rax
	0x41, 0x80, 0x3c, 0x0c, 0x30, //0x00001c04 cmpb         $48, (%r12,%rcx)
	0x0f, 0x84, 0xe0, 0xff, 0xff, 0xff, //0x00001c09 je           LBB0_391
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00001c0f jmp          LBB0_397
	//0x00001c14 LBB0_393
	0x41, 0xbf, 0x02, 0xfc, 0xff, 0xff, //0x00001c14 movl         $-1022, %r15d
	0x0f, 0x84, 0x49, 0x06, 0x00, 0x00, //0x00001c1a je           LBB0_471
	//0x00001c20 LBB0_395
	0x45, 0x89, 0xe8, //0x00001c20 movl         %r13d, %r8d
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x00001c23 jmp          LBB0_400
	//0x00001c28 LBB0_396
	0x31, 0xc0, //0x00001c28 xorl         %eax, %eax
	0x48, 0x89, 0x45, 0xc8, //0x00001c2a movq         %rax, $-56(%rbp)
	//0x00001c2e LBB0_397
	0x41, 0x83, 0xc6, 0xff, //0x00001c2e addl         $-1, %r14d
	0x41, 0xbf, 0x02, 0xfc, 0xff, 0xff, //0x00001c32 movl         $-1022, %r15d
	0x45, 0x89, 0xf0, //0x00001c38 movl         %r14d, %r8d
	//0x00001c3b LBB0_398
	0x45, 0x85, 0xc0, //0x00001c3b testl        %r8d, %r8d
	0x0f, 0x84, 0xb5, 0x00, 0x00, 0x00, //0x00001c3e je           LBB0_410
	0x45, 0x89, 0xf5, //0x00001c44 movl         %r14d, %r13d
	0x4c, 0x8b, 0x75, 0xb0, //0x00001c47 movq         $-80(%rbp), %r14
	//0x00001c4b LBB0_400
	0x49, 0x63, 0xc8, //0x00001c4b movslq       %r8d, %rcx
	0x48, 0x8d, 0x41, 0xfe, //0x00001c4e leaq         $-2(%rcx), %rax
	0x48, 0x8d, 0x71, 0xff, //0x00001c52 leaq         $-1(%rcx), %rsi
	0x31, 0xff, //0x00001c56 xorl         %edi, %edi
	//0x00001c58 LBB0_401
	0x48, 0x8d, 0x15, 0xa1, 0x32, 0x00, 0x00, //0x00001c58 leaq         $12961(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x9c, 0x17, 0x8c, 0x15, 0x00, 0x00, //0x00001c5f movzbl       $5516(%rdi,%rdx), %ebx
	0x41, 0x0f, 0xb6, 0x14, 0x3c, //0x00001c67 movzbl       (%r12,%rdi), %edx
	0x38, 0xda, //0x00001c6c cmpb         %bl, %dl
	0x0f, 0x85, 0x90, 0x00, 0x00, 0x00, //0x00001c6e jne          LBB0_411
	0x48, 0x39, 0xfe, //0x00001c74 cmpq         %rdi, %rsi
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x00001c77 je           LBB0_408
	0x48, 0x8d, 0x15, 0x7c, 0x32, 0x00, 0x00, //0x00001c7d leaq         $12924(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x9c, 0x17, 0x8d, 0x15, 0x00, 0x00, //0x00001c84 movzbl       $5517(%rdi,%rdx), %ebx
	0x41, 0x0f, 0xb6, 0x54, 0x3c, 0x01, //0x00001c8c movzbl       $1(%r12,%rdi), %edx
	0x38, 0xda, //0x00001c92 cmpb         %bl, %dl
	0x0f, 0x85, 0x6a, 0x00, 0x00, 0x00, //0x00001c94 jne          LBB0_411
	0x48, 0x39, 0xf8, //0x00001c9a cmpq         %rdi, %rax
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x00001c9d je           LBB0_408
	0x48, 0x83, 0xff, 0x24, //0x00001ca3 cmpq         $36, %rdi
	0x0f, 0x84, 0x42, 0x00, 0x00, 0x00, //0x00001ca7 je           LBB0_409
	0x48, 0x8d, 0x15, 0x4c, 0x32, 0x00, 0x00, //0x00001cad leaq         $12876(%rip), %rdx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x9c, 0x17, 0x8e, 0x15, 0x00, 0x00, //0x00001cb4 movzbl       $5518(%rdi,%rdx), %ebx
	0x41, 0x0f, 0xb6, 0x54, 0x3c, 0x02, //0x00001cbc movzbl       $2(%r12,%rdi), %edx
	0x38, 0xda, //0x00001cc2 cmpb         %bl, %dl
	0x0f, 0x85, 0x3a, 0x00, 0x00, 0x00, //0x00001cc4 jne          LBB0_411
	0x48, 0x83, 0xc7, 0x03, //0x00001cca addq         $3, %rdi
	0x48, 0x39, 0xf9, //0x00001cce cmpq         %rdi, %rcx
	0x0f, 0x85, 0x81, 0xff, 0xff, 0xff, //0x00001cd1 jne          LBB0_401
	//0x00001cd7 LBB0_408
	0x44, 0x89, 0xc0, //0x00001cd7 movl         %r8d, %eax
	0x48, 0x8d, 0x0d, 0x1f, 0x32, 0x00, 0x00, //0x00001cda leaq         $12831(%rip), %rcx  /* _LSHIFT_TAB+0(%rip) */
	0x80, 0xbc, 0x08, 0x8c, 0x15, 0x00, 0x00, 0x00, //0x00001ce1 cmpb         $0, $5516(%rax,%rcx)
	0x0f, 0x85, 0x22, 0x00, 0x00, 0x00, //0x00001ce9 jne          LBB0_412
	//0x00001cef LBB0_409
	0xbf, 0x10, 0x00, 0x00, 0x00, //0x00001cef movl         $16, %edi
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x00001cf4 jmp          LBB0_413
	//0x00001cf9 LBB0_410
	0x31, 0xc0, //0x00001cf9 xorl         %eax, %eax
	0x4c, 0x8b, 0x75, 0xb0, //0x00001cfb movq         $-80(%rbp), %r14
	0xe9, 0x9a, 0x01, 0x00, 0x00, //0x00001cff jmp          LBB0_432
	//0x00001d04 LBB0_411
	0xbf, 0x10, 0x00, 0x00, 0x00, //0x00001d04 movl         $16, %edi
	0x38, 0xda, //0x00001d09 cmpb         %bl, %dl
	0x0f, 0x8d, 0x05, 0x00, 0x00, 0x00, //0x00001d0b jge          LBB0_413
	//0x00001d11 LBB0_412
	0xbf, 0x0f, 0x00, 0x00, 0x00, //0x00001d11 movl         $15, %edi
	//0x00001d16 LBB0_413
	0x45, 0x85, 0xc0, //0x00001d16 testl        %r8d, %r8d
	0x0f, 0x8e, 0xad, 0x00, 0x00, 0x00, //0x00001d19 jle          LBB0_421
	0x48, 0x89, 0x7d, 0xa0, //0x00001d1f movq         %rdi, $-96(%rbp)
	0x46, 0x8d, 0x1c, 0x07, //0x00001d23 leal         (%rdi,%r8), %r11d
	0x44, 0x89, 0xc3, //0x00001d27 movl         %r8d, %ebx
	0x49, 0x63, 0xf3, //0x00001d2a movslq       %r11d, %rsi
	0x48, 0x83, 0xc6, 0xff, //0x00001d2d addq         $-1, %rsi
	0x48, 0x83, 0xc3, 0x01, //0x00001d31 addq         $1, %rbx
	0x31, 0xc9, //0x00001d35 xorl         %ecx, %ecx
	0x49, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00001d37 movabsq      $-432345564227567616, %r9
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001d41 movl         $1, %r8d
	0xe9, 0x1d, 0x00, 0x00, 0x00, //0x00001d47 jmp          LBB0_417
	//0x00001d4c LBB0_415
	0x48, 0x85, 0xc0, //0x00001d4c testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xd0, //0x00001d4f cmovnel      %r8d, %r10d
	//0x00001d53 LBB0_416
	0x41, 0x83, 0xc3, 0xff, //0x00001d53 addl         $-1, %r11d
	0x48, 0x83, 0xc6, 0xff, //0x00001d57 addq         $-1, %rsi
	0x48, 0x83, 0xc3, 0xff, //0x00001d5b addq         $-1, %rbx
	0x48, 0x83, 0xfb, 0x01, //0x00001d5f cmpq         $1, %rbx
	0x0f, 0x86, 0x4c, 0x00, 0x00, 0x00, //0x00001d63 jbe          LBB0_419
	//0x00001d69 LBB0_417
	0x8d, 0x43, 0xfe, //0x00001d69 leal         $-2(%rbx), %eax
	0x49, 0x0f, 0xbe, 0x3c, 0x04, //0x00001d6c movsbq       (%r12,%rax), %rdi
	0x48, 0xc1, 0xe7, 0x35, //0x00001d71 shlq         $53, %rdi
	0x48, 0x01, 0xcf, //0x00001d75 addq         %rcx, %rdi
	0x4c, 0x01, 0xcf, //0x00001d78 addq         %r9, %rdi
	0x48, 0x89, 0xf8, //0x00001d7b movq         %rdi, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001d7e movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x00001d88 mulq         %rcx
	0x48, 0x89, 0xd1, //0x00001d8b movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x00001d8e shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x00001d92 leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00001d96 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf8, //0x00001d9a movq         %rdi, %rax
	0x48, 0x29, 0xd0, //0x00001d9d subq         %rdx, %rax
	0x48, 0x3b, 0x75, 0xc0, //0x00001da0 cmpq         $-64(%rbp), %rsi
	0x0f, 0x83, 0xa2, 0xff, 0xff, 0xff, //0x00001da4 jae          LBB0_415
	0x04, 0x30, //0x00001daa addb         $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00001dac movb         %al, (%r12,%rsi)
	0xe9, 0x9e, 0xff, 0xff, 0xff, //0x00001db0 jmp          LBB0_416
	//0x00001db5 LBB0_419
	0x48, 0x83, 0xff, 0x0a, //0x00001db5 cmpq         $10, %rdi
	0x0f, 0x83, 0x16, 0x00, 0x00, 0x00, //0x00001db9 jae          LBB0_422
	0x48, 0x8b, 0x5d, 0xc0, //0x00001dbf movq         $-64(%rbp), %rbx
	0x48, 0x8b, 0x7d, 0xa0, //0x00001dc3 movq         $-96(%rbp), %rdi
	0xe9, 0x7d, 0x00, 0x00, 0x00, //0x00001dc7 jmp          LBB0_426
	//0x00001dcc LBB0_421
	0x48, 0x8b, 0x5d, 0xc0, //0x00001dcc movq         $-64(%rbp), %rbx
	0xe9, 0x74, 0x00, 0x00, 0x00, //0x00001dd0 jmp          LBB0_426
	//0x00001dd5 LBB0_422
	0x49, 0x63, 0xf3, //0x00001dd5 movslq       %r11d, %rsi
	0x48, 0x83, 0xc6, 0xff, //0x00001dd8 addq         $-1, %rsi
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001ddc movl         $1, %r8d
	0x48, 0x8b, 0x7d, 0xa0, //0x00001de2 movq         $-96(%rbp), %rdi
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00001de6 jmp          LBB0_424
	//0x00001deb LBB0_423
	0x48, 0x85, 0xc0, //0x00001deb testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xd0, //0x00001dee cmovnel      %r8d, %r10d
	0x48, 0x83, 0xc6, 0xff, //0x00001df2 addq         $-1, %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00001df6 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00001dfa movq         %rdx, %rcx
	0x0f, 0x86, 0x46, 0x00, 0x00, 0x00, //0x00001dfd jbe          LBB0_426
	//0x00001e03 LBB0_424
	0x48, 0x89, 0xc8, //0x00001e03 movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001e06 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00001e10 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00001e13 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00001e17 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x1c, 0x80, //0x00001e1b leaq         (%rax,%rax,4), %rbx
	0x48, 0x89, 0xc8, //0x00001e1f movq         %rcx, %rax
	0x48, 0x29, 0xd8, //0x00001e22 subq         %rbx, %rax
	0x48, 0x8b, 0x5d, 0xc0, //0x00001e25 movq         $-64(%rbp), %rbx
	0x48, 0x39, 0xde, //0x00001e29 cmpq         %rbx, %rsi
	0x0f, 0x83, 0xb9, 0xff, 0xff, 0xff, //0x00001e2c jae          LBB0_423
	0x04, 0x30, //0x00001e32 addb         $48, %al
	0x41, 0x88, 0x04, 0x34, //0x00001e34 movb         %al, (%r12,%rsi)
	0x48, 0x83, 0xc6, 0xff, //0x00001e38 addq         $-1, %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00001e3c cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00001e40 movq         %rdx, %rcx
	0x0f, 0x87, 0xba, 0xff, 0xff, 0xff, //0x00001e43 ja           LBB0_424
	//0x00001e49 LBB0_426
	0x41, 0x01, 0xfd, //0x00001e49 addl         %edi, %r13d
	0x49, 0x63, 0xc5, //0x00001e4c movslq       %r13d, %rax
	0x48, 0x39, 0xc3, //0x00001e4f cmpq         %rax, %rbx
	0x0f, 0x46, 0xc3, //0x00001e52 cmovbel      %ebx, %eax
	0x48, 0x8b, 0x4d, 0xc8, //0x00001e55 movq         $-56(%rbp), %rcx
	0x01, 0xf9, //0x00001e59 addl         %edi, %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x00001e5b movq         %rcx, $-56(%rbp)
	0x85, 0xc0, //0x00001e5f testl        %eax, %eax
	0x0f, 0x8e, 0x31, 0x00, 0x00, 0x00, //0x00001e61 jle          LBB0_431
	0x89, 0xc1, //0x00001e67 movl         %eax, %ecx
	0x42, 0x80, 0x7c, 0x21, 0xff, 0x30, //0x00001e69 cmpb         $48, $-1(%rcx,%r12)
	0x0f, 0x85, 0x29, 0x00, 0x00, 0x00, //0x00001e6f jne          LBB0_432
	//0x00001e75 LBB0_428
	0x48, 0x83, 0xf9, 0x01, //0x00001e75 cmpq         $1, %rcx
	0x0f, 0x86, 0x08, 0x01, 0x00, 0x00, //0x00001e79 jbe          LBB0_444
	0x8d, 0x41, 0xfe, //0x00001e7f leal         $-2(%rcx), %eax
	0x48, 0x83, 0xc1, 0xff, //0x00001e82 addq         $-1, %rcx
	0x41, 0x80, 0x3c, 0x04, 0x30, //0x00001e86 cmpb         $48, (%r12,%rax)
	0x0f, 0x84, 0xe4, 0xff, 0xff, 0xff, //0x00001e8b je           LBB0_428
	0x89, 0xc8, //0x00001e91 movl         %ecx, %eax
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00001e93 jmp          LBB0_432
	//0x00001e98 LBB0_431
	0x0f, 0x84, 0xfb, 0x00, 0x00, 0x00, //0x00001e98 je           LBB0_445
	//0x00001e9e LBB0_432
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001e9e movq         $-1, %r8
	0x48, 0x8b, 0x4d, 0xc8, //0x00001ea5 movq         $-56(%rbp), %rcx
	0x83, 0xf9, 0x14, //0x00001ea9 cmpl         $20, %ecx
	0x0f, 0x8e, 0x16, 0x00, 0x00, 0x00, //0x00001eac jle          LBB0_434
	//0x00001eb2 LBB0_433
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001eb2 movabsq      $4503599627370495, %rcx
	0x48, 0x8b, 0x55, 0x98, //0x00001ebc movq         $-104(%rbp), %rdx
	0x8a, 0x5d, 0xd7, //0x00001ec0 movb         $-41(%rbp), %bl
	0xe9, 0x5e, 0x03, 0x00, 0x00, //0x00001ec3 jmp          LBB0_468
	//0x00001ec8 LBB0_434
	0x85, 0xc9, //0x00001ec8 testl        %ecx, %ecx
	0x0f, 0x8e, 0x4e, 0x00, 0x00, 0x00, //0x00001eca jle          LBB0_439
	0x31, 0xd2, //0x00001ed0 xorl         %edx, %edx
	0x85, 0xc0, //0x00001ed2 testl        %eax, %eax
	0xbf, 0x00, 0x00, 0x00, 0x00, //0x00001ed4 movl         $0, %edi
	0x0f, 0x4f, 0xf8, //0x00001ed9 cmovgl       %eax, %edi
	0x89, 0xcb, //0x00001edc movl         %ecx, %ebx
	0x4c, 0x8d, 0x43, 0xff, //0x00001ede leaq         $-1(%rbx), %r8
	0x49, 0x39, 0xf8, //0x00001ee2 cmpq         %rdi, %r8
	0x4c, 0x0f, 0x43, 0xc7, //0x00001ee5 cmovaeq      %rdi, %r8
	0x4d, 0x8d, 0x48, 0x01, //0x00001ee9 leaq         $1(%r8), %r9
	0x31, 0xf6, //0x00001eed xorl         %esi, %esi
	//0x00001eef LBB0_436
	0x48, 0x39, 0xd7, //0x00001eef cmpq         %rdx, %rdi
	0x0f, 0x84, 0x2b, 0x00, 0x00, 0x00, //0x00001ef2 je           LBB0_440
	0x48, 0x8d, 0x34, 0xb6, //0x00001ef8 leaq         (%rsi,%rsi,4), %rsi
	0x49, 0x0f, 0xbe, 0x0c, 0x14, //0x00001efc movsbq       (%r12,%rdx), %rcx
	0x48, 0x8d, 0x34, 0x71, //0x00001f01 leaq         (%rcx,%rsi,2), %rsi
	0x48, 0x83, 0xc6, 0xd0, //0x00001f05 addq         $-48, %rsi
	0x48, 0x83, 0xc2, 0x01, //0x00001f09 addq         $1, %rdx
	0x48, 0x39, 0xd3, //0x00001f0d cmpq         %rdx, %rbx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00001f10 jne          LBB0_436
	0x4d, 0x89, 0xc8, //0x00001f16 movq         %r9, %r8
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00001f19 jmp          LBB0_440
	//0x00001f1e LBB0_439
	0x45, 0x31, 0xc0, //0x00001f1e xorl         %r8d, %r8d
	0x31, 0xf6, //0x00001f21 xorl         %esi, %esi
	//0x00001f23 LBB0_440
	0x48, 0x8b, 0x4d, 0xc8, //0x00001f23 movq         $-56(%rbp), %rcx
	0x89, 0xca, //0x00001f27 movl         %ecx, %edx
	0x44, 0x29, 0xc2, //0x00001f29 subl         %r8d, %edx
	0x0f, 0x8e, 0x41, 0x02, 0x00, 0x00, //0x00001f2c jle          LBB0_455
	0x83, 0xfa, 0x10, //0x00001f32 cmpl         $16, %edx
	0x0f, 0x82, 0x21, 0x02, 0x00, 0x00, //0x00001f35 jb           LBB0_453
	0x89, 0xd7, //0x00001f3b movl         %edx, %edi
	0xc5, 0xfa, 0x6f, 0x05, 0x1b, 0xe1, 0xff, 0xff, //0x00001f3d vmovdqu      $-7909(%rip), %xmm0  /* LCPI0_4+0(%rip) */
	0xc4, 0xe3, 0xf9, 0x22, 0xc6, 0x00, //0x00001f45 vpinsrq      $0, %rsi, %xmm0, %xmm0
	0x83, 0xe7, 0xf0, //0x00001f4b andl         $-16, %edi
	0xc4, 0xe3, 0x7d, 0x02, 0x05, 0x08, 0xe1, 0xff, 0xff, 0xf0, //0x00001f4e vpblendd     $240, $-7928(%rip), %ymm0, %ymm0  /* LCPI0_4+0(%rip) */
	0x8d, 0x4f, 0xf0, //0x00001f58 leal         $-16(%rdi), %ecx
	0x89, 0xce, //0x00001f5b movl         %ecx, %esi
	0xc1, 0xee, 0x04, //0x00001f5d shrl         $4, %esi
	0x83, 0xc6, 0x01, //0x00001f60 addl         $1, %esi
	0x89, 0xf3, //0x00001f63 movl         %esi, %ebx
	0x83, 0xe3, 0x03, //0x00001f65 andl         $3, %ebx
	0x83, 0xf9, 0x30, //0x00001f68 cmpl         $48, %ecx
	0x0f, 0x83, 0x31, 0x00, 0x00, 0x00, //0x00001f6b jae          LBB0_446
	0xc4, 0xe2, 0x7d, 0x59, 0x15, 0xb6, 0xe0, 0xff, 0xff, //0x00001f71 vpbroadcastq $-8010(%rip), %ymm2  /* LCPI0_5+0(%rip) */
	0xc5, 0xfd, 0x6f, 0xda, //0x00001f7a vmovdqa      %ymm2, %ymm3
	0xc5, 0xfd, 0x6f, 0xca, //0x00001f7e vmovdqa      %ymm2, %ymm1
	0xe9, 0x99, 0x00, 0x00, 0x00, //0x00001f82 jmp          LBB0_448
	//0x00001f87 LBB0_444
	0x83, 0xc1, 0xff, //0x00001f87 addl         $-1, %ecx
	0x31, 0xf6, //0x00001f8a xorl         %esi, %esi
	0x89, 0xc8, //0x00001f8c movl         %ecx, %eax
	0x31, 0xc9, //0x00001f8e xorl         %ecx, %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x00001f90 movq         %rcx, $-56(%rbp)
	0xe9, 0xe4, 0x01, 0x00, 0x00, //0x00001f94 jmp          LBB0_456
	//0x00001f99 LBB0_445
	0x31, 0xf6, //0x00001f99 xorl         %esi, %esi
	0x31, 0xc0, //0x00001f9b xorl         %eax, %eax
	0xe9, 0x33, 0x02, 0x00, 0x00, //0x00001f9d jmp          LBB0_464
	//0x00001fa2 LBB0_446
	0x83, 0xe6, 0xfc, //0x00001fa2 andl         $-4, %esi
	0xc4, 0xe2, 0x7d, 0x59, 0x15, 0x82, 0xe0, 0xff, 0xff, //0x00001fa5 vpbroadcastq $-8062(%rip), %ymm2  /* LCPI0_5+0(%rip) */
	0xc4, 0xe2, 0x7d, 0x59, 0x25, 0x81, 0xe0, 0xff, 0xff, //0x00001fae vpbroadcastq $-8063(%rip), %ymm4  /* LCPI0_6+0(%rip) */
	0xc5, 0xfd, 0x6f, 0xda, //0x00001fb7 vmovdqa      %ymm2, %ymm3
	0xc5, 0xfd, 0x6f, 0xca, //0x00001fbb vmovdqa      %ymm2, %ymm1
	//0x00001fbf LBB0_447
	0xc5, 0xfd, 0xf4, 0xec, //0x00001fbf vpmuludq     %ymm4, %ymm0, %ymm5
	0xc5, 0xfd, 0x73, 0xd0, 0x20, //0x00001fc3 vpsrlq       $32, %ymm0, %ymm0
	0xc5, 0xfd, 0xf4, 0xc4, //0x00001fc8 vpmuludq     %ymm4, %ymm0, %ymm0
	0xc5, 0xfd, 0x73, 0xf0, 0x20, //0x00001fcc vpsllq       $32, %ymm0, %ymm0
	0xc5, 0xd5, 0xd4, 0xc0, //0x00001fd1 vpaddq       %ymm0, %ymm5, %ymm0
	0xc5, 0xed, 0xf4, 0xec, //0x00001fd5 vpmuludq     %ymm4, %ymm2, %ymm5
	0xc5, 0xed, 0x73, 0xd2, 0x20, //0x00001fd9 vpsrlq       $32, %ymm2, %ymm2
	0xc5, 0xed, 0xf4, 0xd4, //0x00001fde vpmuludq     %ymm4, %ymm2, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x00001fe2 vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xd5, 0xd4, 0xd2, //0x00001fe7 vpaddq       %ymm2, %ymm5, %ymm2
	0xc5, 0xe5, 0xf4, 0xec, //0x00001feb vpmuludq     %ymm4, %ymm3, %ymm5
	0xc5, 0xe5, 0x73, 0xd3, 0x20, //0x00001fef vpsrlq       $32, %ymm3, %ymm3
	0xc5, 0xe5, 0xf4, 0xdc, //0x00001ff4 vpmuludq     %ymm4, %ymm3, %ymm3
	0xc5, 0xe5, 0x73, 0xf3, 0x20, //0x00001ff8 vpsllq       $32, %ymm3, %ymm3
	0xc5, 0xd5, 0xd4, 0xdb, //0x00001ffd vpaddq       %ymm3, %ymm5, %ymm3
	0xc5, 0xf5, 0xf4, 0xec, //0x00002001 vpmuludq     %ymm4, %ymm1, %ymm5
	0xc5, 0xf5, 0x73, 0xd1, 0x20, //0x00002005 vpsrlq       $32, %ymm1, %ymm1
	0xc5, 0xf5, 0xf4, 0xcc, //0x0000200a vpmuludq     %ymm4, %ymm1, %ymm1
	0xc5, 0xf5, 0x73, 0xf1, 0x20, //0x0000200e vpsllq       $32, %ymm1, %ymm1
	0xc5, 0xd5, 0xd4, 0xc9, //0x00002013 vpaddq       %ymm1, %ymm5, %ymm1
	0x83, 0xc6, 0xfc, //0x00002017 addl         $-4, %esi
	0x0f, 0x85, 0x9f, 0xff, 0xff, 0xff, //0x0000201a jne          LBB0_447
	//0x00002020 LBB0_448
	0x85, 0xdb, //0x00002020 testl        %ebx, %ebx
	0x0f, 0x84, 0x6a, 0x00, 0x00, 0x00, //0x00002022 je           LBB0_451
	0xc4, 0xe2, 0x7d, 0x59, 0x25, 0x0f, 0xe0, 0xff, 0xff, //0x00002028 vpbroadcastq $-8177(%rip), %ymm4  /* LCPI0_7+0(%rip) */
	//0x00002031 LBB0_450
	0xc5, 0xfd, 0xf4, 0xec, //0x00002031 vpmuludq     %ymm4, %ymm0, %ymm5
	0xc5, 0xfd, 0x73, 0xd0, 0x20, //0x00002035 vpsrlq       $32, %ymm0, %ymm0
	0xc5, 0xfd, 0xf4, 0xc4, //0x0000203a vpmuludq     %ymm4, %ymm0, %ymm0
	0xc5, 0xfd, 0x73, 0xf0, 0x20, //0x0000203e vpsllq       $32, %ymm0, %ymm0
	0xc5, 0xd5, 0xd4, 0xc0, //0x00002043 vpaddq       %ymm0, %ymm5, %ymm0
	0xc5, 0xed, 0xf4, 0xec, //0x00002047 vpmuludq     %ymm4, %ymm2, %ymm5
	0xc5, 0xed, 0x73, 0xd2, 0x20, //0x0000204b vpsrlq       $32, %ymm2, %ymm2
	0xc5, 0xed, 0xf4, 0xd4, //0x00002050 vpmuludq     %ymm4, %ymm2, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x00002054 vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xd5, 0xd4, 0xd2, //0x00002059 vpaddq       %ymm2, %ymm5, %ymm2
	0xc5, 0xe5, 0xf4, 0xec, //0x0000205d vpmuludq     %ymm4, %ymm3, %ymm5
	0xc5, 0xe5, 0x73, 0xd3, 0x20, //0x00002061 vpsrlq       $32, %ymm3, %ymm3
	0xc5, 0xe5, 0xf4, 0xdc, //0x00002066 vpmuludq     %ymm4, %ymm3, %ymm3
	0xc5, 0xe5, 0x73, 0xf3, 0x20, //0x0000206a vpsllq       $32, %ymm3, %ymm3
	0xc5, 0xd5, 0xd4, 0xdb, //0x0000206f vpaddq       %ymm3, %ymm5, %ymm3
	0xc5, 0xf5, 0xf4, 0xec, //0x00002073 vpmuludq     %ymm4, %ymm1, %ymm5
	0xc5, 0xf5, 0x73, 0xd1, 0x20, //0x00002077 vpsrlq       $32, %ymm1, %ymm1
	0xc5, 0xf5, 0xf4, 0xcc, //0x0000207c vpmuludq     %ymm4, %ymm1, %ymm1
	0xc5, 0xf5, 0x73, 0xf1, 0x20, //0x00002080 vpsllq       $32, %ymm1, %ymm1
	0xc5, 0xd5, 0xd4, 0xc9, //0x00002085 vpaddq       %ymm1, %ymm5, %ymm1
	0x83, 0xc3, 0xff, //0x00002089 addl         $-1, %ebx
	0x0f, 0x85, 0x9f, 0xff, 0xff, 0xff, //0x0000208c jne          LBB0_450
	//0x00002092 LBB0_451
	0xc5, 0xdd, 0x73, 0xd2, 0x20, //0x00002092 vpsrlq       $32, %ymm2, %ymm4
	0xc5, 0xdd, 0xf4, 0xe0, //0x00002097 vpmuludq     %ymm0, %ymm4, %ymm4
	0xc5, 0xd5, 0x73, 0xd0, 0x20, //0x0000209b vpsrlq       $32, %ymm0, %ymm5
	0xc5, 0xed, 0xf4, 0xed, //0x000020a0 vpmuludq     %ymm5, %ymm2, %ymm5
	0xc5, 0xd5, 0xd4, 0xe4, //0x000020a4 vpaddq       %ymm4, %ymm5, %ymm4
	0xc5, 0xdd, 0x73, 0xf4, 0x20, //0x000020a8 vpsllq       $32, %ymm4, %ymm4
	0xc5, 0xed, 0xf4, 0xc0, //0x000020ad vpmuludq     %ymm0, %ymm2, %ymm0
	0xc5, 0xfd, 0xd4, 0xc4, //0x000020b1 vpaddq       %ymm4, %ymm0, %ymm0
	0xc5, 0xed, 0x73, 0xd3, 0x20, //0x000020b5 vpsrlq       $32, %ymm3, %ymm2
	0xc5, 0xed, 0xf4, 0xd0, //0x000020ba vpmuludq     %ymm0, %ymm2, %ymm2
	0xc5, 0xdd, 0x73, 0xd0, 0x20, //0x000020be vpsrlq       $32, %ymm0, %ymm4
	0xc5, 0xe5, 0xf4, 0xe4, //0x000020c3 vpmuludq     %ymm4, %ymm3, %ymm4
	0xc5, 0xdd, 0xd4, 0xd2, //0x000020c7 vpaddq       %ymm2, %ymm4, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x000020cb vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xe5, 0xf4, 0xc0, //0x000020d0 vpmuludq     %ymm0, %ymm3, %ymm0
	0xc5, 0xfd, 0xd4, 0xc2, //0x000020d4 vpaddq       %ymm2, %ymm0, %ymm0
	0xc5, 0xed, 0x73, 0xd1, 0x20, //0x000020d8 vpsrlq       $32, %ymm1, %ymm2
	0xc5, 0xed, 0xf4, 0xd0, //0x000020dd vpmuludq     %ymm0, %ymm2, %ymm2
	0xc5, 0xe5, 0x73, 0xd0, 0x20, //0x000020e1 vpsrlq       $32, %ymm0, %ymm3
	0xc5, 0xf5, 0xf4, 0xdb, //0x000020e6 vpmuludq     %ymm3, %ymm1, %ymm3
	0xc5, 0xe5, 0xd4, 0xd2, //0x000020ea vpaddq       %ymm2, %ymm3, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x000020ee vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xf5, 0xf4, 0xc0, //0x000020f3 vpmuludq     %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd4, 0xc2, //0x000020f7 vpaddq       %ymm2, %ymm0, %ymm0
	0xc4, 0xe3, 0x7d, 0x39, 0xc1, 0x01, //0x000020fb vextracti128 $1, %ymm0, %xmm1
	0xc5, 0xe9, 0x73, 0xd0, 0x20, //0x00002101 vpsrlq       $32, %xmm0, %xmm2
	0xc5, 0xe9, 0xf4, 0xd1, //0x00002106 vpmuludq     %xmm1, %xmm2, %xmm2
	0xc5, 0xe1, 0x73, 0xd1, 0x20, //0x0000210a vpsrlq       $32, %xmm1, %xmm3
	0xc5, 0xf9, 0xf4, 0xdb, //0x0000210f vpmuludq     %xmm3, %xmm0, %xmm3
	0xc5, 0xe1, 0xd4, 0xd2, //0x00002113 vpaddq       %xmm2, %xmm3, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x20, //0x00002117 vpsllq       $32, %xmm2, %xmm2
	0xc5, 0xf9, 0xf4, 0xc1, //0x0000211c vpmuludq     %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xd4, 0xc2, //0x00002120 vpaddq       %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc8, 0xee, //0x00002124 vpshufd      $238, %xmm0, %xmm1
	0xc5, 0xe9, 0x73, 0xd0, 0x20, //0x00002129 vpsrlq       $32, %xmm0, %xmm2
	0xc5, 0xe9, 0xf4, 0xd1, //0x0000212e vpmuludq     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0x70, 0xd8, 0xff, //0x00002132 vpshufd      $255, %xmm0, %xmm3
	0xc5, 0xf9, 0xf4, 0xdb, //0x00002137 vpmuludq     %xmm3, %xmm0, %xmm3
	0xc5, 0xe1, 0xd4, 0xd2, //0x0000213b vpaddq       %xmm2, %xmm3, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x20, //0x0000213f vpsllq       $32, %xmm2, %xmm2
	0xc5, 0xf9, 0xf4, 0xc1, //0x00002144 vpmuludq     %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xd4, 0xc2, //0x00002148 vpaddq       %xmm2, %xmm0, %xmm0
	0xc4, 0xe1, 0xf9, 0x7e, 0xc6, //0x0000214c vmovq        %xmm0, %rsi
	0x39, 0xfa, //0x00002151 cmpl         %edi, %edx
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00002153 je           LBB0_455
	0x41, 0x01, 0xf8, //0x00002159 addl         %edi, %r8d
	//0x0000215c LBB0_453
	0x48, 0x8b, 0x4d, 0xc8, //0x0000215c movq         $-56(%rbp), %rcx
	0x44, 0x29, 0xc1, //0x00002160 subl         %r8d, %ecx
	//0x00002163 LBB0_454
	0x48, 0x01, 0xf6, //0x00002163 addq         %rsi, %rsi
	0x48, 0x8d, 0x34, 0xb6, //0x00002166 leaq         (%rsi,%rsi,4), %rsi
	0x83, 0xc1, 0xff, //0x0000216a addl         $-1, %ecx
	0x0f, 0x85, 0xf0, 0xff, 0xff, 0xff, //0x0000216d jne          LBB0_454
	//0x00002173 LBB0_455
	0x83, 0x7d, 0xc8, 0x00, //0x00002173 cmpl         $0, $-56(%rbp)
	0x0f, 0x88, 0x4b, 0x00, 0x00, 0x00, //0x00002177 js           LBB0_462
	//0x0000217d LBB0_456
	0x48, 0x8b, 0x55, 0xc8, //0x0000217d movq         $-56(%rbp), %rdx
	0x39, 0xd0, //0x00002181 cmpl         %edx, %eax
	0x0f, 0x8e, 0x3f, 0x00, 0x00, 0x00, //0x00002183 jle          LBB0_462
	0x89, 0xd1, //0x00002189 movl         %edx, %ecx
	0x41, 0x8a, 0x0c, 0x0c, //0x0000218b movb         (%r12,%rcx), %cl
	0x80, 0xf9, 0x35, //0x0000218f cmpb         $53, %cl
	0x0f, 0x85, 0x37, 0x00, 0x00, 0x00, //0x00002192 jne          LBB0_463
	0x83, 0xc2, 0x01, //0x00002198 addl         $1, %edx
	0x39, 0xc2, //0x0000219b cmpl         %eax, %edx
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x0000219d jne          LBB0_463
	0xb0, 0x01, //0x000021a3 movb         $1, %al
	0x45, 0x85, 0xd2, //0x000021a5 testl        %r10d, %r10d
	0x0f, 0x85, 0x27, 0x00, 0x00, 0x00, //0x000021a8 jne          LBB0_464
	0x48, 0x8b, 0x45, 0xc8, //0x000021ae movq         $-56(%rbp), %rax
	0x85, 0xc0, //0x000021b2 testl        %eax, %eax
	0x0f, 0x84, 0x0e, 0x00, 0x00, 0x00, //0x000021b4 je           LBB0_462
	0x83, 0xc0, 0xff, //0x000021ba addl         $-1, %eax
	0x41, 0x8a, 0x04, 0x04, //0x000021bd movb         (%r12,%rax), %al
	0x24, 0x01, //0x000021c1 andb         $1, %al
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x000021c3 jmp          LBB0_464
	//0x000021c8 LBB0_462
	0x31, 0xc0, //0x000021c8 xorl         %eax, %eax
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x000021ca jmp          LBB0_464
	//0x000021cf LBB0_463
	0x80, 0xf9, 0x35, //0x000021cf cmpb         $53, %cl
	0x0f, 0x9d, 0xc0, //0x000021d2 setge        %al
	//0x000021d5 LBB0_464
	0x48, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000021d5 movabsq      $4503599627370495, %rcx
	0x48, 0x8b, 0x55, 0x98, //0x000021df movq         $-104(%rbp), %rdx
	0x8a, 0x5d, 0xd7, //0x000021e3 movb         $-41(%rbp), %bl
	0x44, 0x0f, 0xb6, 0xc0, //0x000021e6 movzbl       %al, %r8d
	0x49, 0x01, 0xf0, //0x000021ea addq         %rsi, %r8
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, //0x000021ed movabsq      $9007199254740992, %rax
	0x49, 0x39, 0xc0, //0x000021f7 cmpq         %rax, %r8
	0x0f, 0x85, 0x26, 0x00, 0x00, 0x00, //0x000021fa jne          LBB0_468
	0x41, 0x81, 0xff, 0xfe, 0x03, 0x00, 0x00, //0x00002200 cmpl         $1022, %r15d
	0x0f, 0x8e, 0x12, 0x00, 0x00, 0x00, //0x00002207 jle          LBB0_467
	0x45, 0x31, 0xc0, //0x0000220d xorl         %r8d, %r8d
	0x49, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00002210 movabsq      $9218868437227405312, %r15
	0xe9, 0x6b, 0xea, 0xff, 0xff, //0x0000221a jmp          LBB0_165
	//0x0000221f LBB0_467
	0x41, 0x83, 0xc7, 0x01, //0x0000221f addl         $1, %r15d
	0x49, 0x89, 0xd0, //0x00002223 movq         %rdx, %r8
	//0x00002226 LBB0_468
	0x4c, 0x21, 0xc2, //0x00002226 andq         %r8, %rdx
	0x41, 0x81, 0xc7, 0xff, 0x03, 0x00, 0x00, //0x00002229 addl         $1023, %r15d
	0x41, 0x81, 0xe7, 0xff, 0x07, 0x00, 0x00, //0x00002230 andl         $2047, %r15d
	0x49, 0xc1, 0xe7, 0x34, //0x00002237 shlq         $52, %r15
	0x48, 0x85, 0xd2, //0x0000223b testq        %rdx, %rdx
	0x4c, 0x0f, 0x44, 0xfa, //0x0000223e cmoveq       %rdx, %r15
	0xe9, 0x43, 0xea, 0xff, 0xff, //0x00002242 jmp          LBB0_165
	//0x00002247 LBB0_469
	0x4c, 0x8b, 0x7d, 0xc8, //0x00002247 movq         $-56(%rbp), %r15
	//0x0000224b LBB0_470
	0x48, 0x01, 0xc0, //0x0000224b addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x0000224e leaq         (%rax,%rax,4), %rax
	0x83, 0xc6, 0x01, //0x00002252 addl         $1, %esi
	0x48, 0x89, 0xc2, //0x00002255 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00002258 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x0000225b testq        %rdx, %rdx
	0x0f, 0x84, 0xe7, 0xff, 0xff, 0xff, //0x0000225e je           LBB0_470
	0xe9, 0x62, 0xf8, 0xff, 0xff, //0x00002264 jmp          LBB0_378
	//0x00002269 LBB0_471
	0x45, 0x31, 0xc0, //0x00002269 xorl         %r8d, %r8d
	0xe9, 0x41, 0xfc, 0xff, 0xff, //0x0000226c jmp          LBB0_433
	//0x00002271 LBB0_472
	0x45, 0x31, 0xf6, //0x00002271 xorl         %r14d, %r14d
	0xe9, 0xa4, 0xee, 0xff, 0xff, //0x00002274 jmp          LBB0_235
	0x00, 0x00, 0x00, //0x00002279 .p2align 2, 0x00
	//0x0000227c _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x0000227c .long 2
	//0x00002280 .p2align 4, 0x00
	//0x00002280 _P10_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, //0x00002280 .quad 0x3ff0000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x40, //0x00002288 .quad 0x4024000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x40, //0x00002290 .quad 0x4059000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x8f, 0x40, //0x00002298 .quad 0x408f400000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0xc3, 0x40, //0x000022a0 .quad 0x40c3880000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x6a, 0xf8, 0x40, //0x000022a8 .quad 0x40f86a0000000000
	0x00, 0x00, 0x00, 0x00, 0x80, 0x84, 0x2e, 0x41, //0x000022b0 .quad 0x412e848000000000
	0x00, 0x00, 0x00, 0x00, 0xd0, 0x12, 0x63, 0x41, //0x000022b8 .quad 0x416312d000000000
	0x00, 0x00, 0x00, 0x00, 0x84, 0xd7, 0x97, 0x41, //0x000022c0 .quad 0x4197d78400000000
	0x00, 0x00, 0x00, 0x00, 0x65, 0xcd, 0xcd, 0x41, //0x000022c8 .quad 0x41cdcd6500000000
	0x00, 0x00, 0x00, 0x20, 0x5f, 0xa0, 0x02, 0x42, //0x000022d0 .quad 0x4202a05f20000000
	0x00, 0x00, 0x00, 0xe8, 0x76, 0x48, 0x37, 0x42, //0x000022d8 .quad 0x42374876e8000000
	0x00, 0x00, 0x00, 0xa2, 0x94, 0x1a, 0x6d, 0x42, //0x000022e0 .quad 0x426d1a94a2000000
	0x00, 0x00, 0x40, 0xe5, 0x9c, 0x30, 0xa2, 0x42, //0x000022e8 .quad 0x42a2309ce5400000
	0x00, 0x00, 0x90, 0x1e, 0xc4, 0xbc, 0xd6, 0x42, //0x000022f0 .quad 0x42d6bcc41e900000
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x000022f8 .quad 0x430c6bf526340000
	0x00, 0x80, 0xe0, 0x37, 0x79, 0xc3, 0x41, 0x43, //0x00002300 .quad 0x4341c37937e08000
	0x00, 0xa0, 0xd8, 0x85, 0x57, 0x34, 0x76, 0x43, //0x00002308 .quad 0x4376345785d8a000
	0x00, 0xc8, 0x4e, 0x67, 0x6d, 0xc1, 0xab, 0x43, //0x00002310 .quad 0x43abc16d674ec800
	0x00, 0x3d, 0x91, 0x60, 0xe4, 0x58, 0xe1, 0x43, //0x00002318 .quad 0x43e158e460913d00
	0x40, 0x8c, 0xb5, 0x78, 0x1d, 0xaf, 0x15, 0x44, //0x00002320 .quad 0x4415af1d78b58c40
	0x50, 0xef, 0xe2, 0xd6, 0xe4, 0x1a, 0x4b, 0x44, //0x00002328 .quad 0x444b1ae4d6e2ef50
	0x92, 0xd5, 0x4d, 0x06, 0xcf, 0xf0, 0x80, 0x44, //0x00002330 .quad 0x4480f0cf064dd592
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00002338 .p2align 4, 0x00
	//0x00002340 _POW10_M128_TAB
	0x53, 0xe4, 0x60, 0xcd, 0x69, 0xc8, 0x32, 0x17, //0x00002340 .quad 1671618768450675795
	0x88, 0x02, 0x1c, 0x08, 0xa0, 0xd5, 0x8f, 0xfa, //0x00002348 .quad -391859759250406776
	0xb4, 0x8e, 0x5c, 0x20, 0x42, 0xbd, 0x7f, 0x0e, //0x00002350 .quad 1044761730281672372
	0x95, 0x81, 0x11, 0x05, 0x84, 0xe5, 0x99, 0x9c, //0x00002358 .quad -7162441377172586091
	0x61, 0xb2, 0x73, 0xa8, 0x92, 0xac, 0x1f, 0x52, //0x00002360 .quad 5917638181279478369
	0xfa, 0xe1, 0x55, 0x06, 0xe5, 0x5e, 0xc0, 0xc3, //0x00002368 .quad -4341365703038344710
	0xf9, 0x9e, 0x90, 0x52, 0xb7, 0x97, 0xa7, 0xe6, //0x00002370 .quad -1826324310255427847
	0x78, 0x5a, 0xeb, 0x47, 0x9e, 0x76, 0xb0, 0xf4, //0x00002378 .quad -815021110370542984
	0x5c, 0x63, 0x9a, 0x93, 0xd2, 0xbe, 0x28, 0x90, //0x00002380 .quad -8058981721550724260
	0x8b, 0x18, 0xf3, 0xec, 0x22, 0x4a, 0xee, 0x98, //0x00002388 .quad -7426917221622671221
	0x33, 0xfc, 0x80, 0x38, 0x87, 0xee, 0x32, 0x74, //0x00002390 .quad 8373016921771146291
	0xae, 0xde, 0x2f, 0xa8, 0xab, 0xdc, 0x29, 0xbf, //0x00002398 .quad -4671960508600951122
	0x3f, 0x3b, 0xa1, 0x06, 0x29, 0xaa, 0x3f, 0x11, //0x000023a0 .quad 1242899115359157055
	0x5a, 0xd6, 0x3b, 0x92, 0xd6, 0x53, 0xf4, 0xee, //0x000023a8 .quad -1228264617323800998
	0x07, 0xc5, 0x24, 0xa4, 0x59, 0xca, 0xc7, 0x4a, //0x000023b0 .quad 5388497965526861063
	0xf8, 0x65, 0x65, 0x1b, 0x66, 0xb4, 0x58, 0x95, //0x000023b8 .quad -7685194413468457480
	0x49, 0xf6, 0x2d, 0x0d, 0xf0, 0xbc, 0x79, 0x5d, //0x000023c0 .quad 6735622456908576329
	0x76, 0xbf, 0x3e, 0xa2, 0x7f, 0xe1, 0xae, 0xba, //0x000023c8 .quad -4994806998408183946
	0xdc, 0x73, 0x79, 0x10, 0x2c, 0x2c, 0xd8, 0xf4, //0x000023d0 .quad -803843965719055396
	0x53, 0x6f, 0xce, 0x8a, 0xdf, 0x99, 0x5a, 0xe9, //0x000023d8 .quad -1631822729582842029
	0x69, 0xe8, 0x4b, 0x8a, 0x9b, 0x1b, 0x07, 0x79, //0x000023e0 .quad 8720969558280366185
	0x94, 0x05, 0xc1, 0xb6, 0x2b, 0xa0, 0xd8, 0x91, //0x000023e8 .quad -7937418233630358124
	0x84, 0xe2, 0xde, 0x6c, 0x82, 0xe2, 0x48, 0x97, //0x000023f0 .quad -7545532125859093884
	0xf9, 0x46, 0x71, 0xa4, 0x36, 0xc8, 0x4e, 0xb6, //0x000023f8 .quad -5310086773610559751
	0x25, 0x9b, 0x16, 0x08, 0x23, 0x1b, 0x1b, 0xfd, //0x00002400 .quad -208543120469091547
	0xb7, 0x98, 0x8d, 0x4d, 0x44, 0x7a, 0xe2, 0xe3, //0x00002408 .quad -2025922448585811785
	0xf7, 0x20, 0x0e, 0xe5, 0xf5, 0xf0, 0x30, 0xfe, //0x00002410 .quad -130339450293182217
	0x72, 0x7f, 0x78, 0xb0, 0x6a, 0x8c, 0x6d, 0x8e, //0x00002418 .quad -8183730558007214222
	0x35, 0xa9, 0x51, 0x5e, 0x33, 0x2d, 0xbd, 0xbd, //0x00002420 .quad -4774610331293865675
	0x4f, 0x9f, 0x96, 0x5c, 0x85, 0xef, 0x08, 0xb2, //0x00002428 .quad -5617977179081629873
	0x82, 0x13, 0xe6, 0x35, 0x80, 0x78, 0x2c, 0xad, //0x00002430 .quad -5968262914117332094
	0x23, 0x47, 0xbc, 0xb3, 0x66, 0x2b, 0x8b, 0xde, //0x00002438 .quad -2410785455424649437
	0x31, 0xcc, 0xaf, 0x21, 0x50, 0xcb, 0x3b, 0x4c, //0x00002440 .quad 5493207715531443249
	0x76, 0xac, 0x55, 0x30, 0x20, 0xfb, 0x16, 0x8b, //0x00002448 .quad -8424269937281487754
	0x3d, 0xbf, 0x1b, 0x2a, 0x24, 0xbe, 0x4a, 0xdf, //0x00002450 .quad -2356862392440471747
	0x93, 0x17, 0x6b, 0x3c, 0xe8, 0xb9, 0xdc, 0xad, //0x00002458 .quad -5918651403174471789
	0x0d, 0xaf, 0xa2, 0x34, 0xad, 0x6d, 0x1d, 0xd7, //0x00002460 .quad -2946077990550589683
	0x78, 0xdd, 0x85, 0x4b, 0x62, 0xe8, 0x53, 0xd9, //0x00002468 .quad -2786628235540701832
	0x68, 0xad, 0xe5, 0x40, 0x8c, 0x64, 0x72, 0x86, //0x00002470 .quad -8758827771735200408
	0x6b, 0xaa, 0x33, 0x6f, 0x3d, 0x71, 0xd4, 0x87, //0x00002478 .quad -8659171674854020501
	0xc2, 0x18, 0x1f, 0x51, 0xaf, 0xfd, 0x0e, 0x68, //0x00002480 .quad 7498209359040551106
	0x06, 0x95, 0x00, 0xcb, 0x8c, 0x8d, 0xc9, 0xa9, //0x00002488 .quad -6212278575140137722
	0xf2, 0xde, 0x66, 0x25, 0x1b, 0xbd, 0x12, 0x02, //0x00002490 .quad 149389661945913074
	0x48, 0xba, 0xc0, 0xfd, 0xef, 0xf0, 0x3b, 0xd4, //0x00002498 .quad -3153662200497784248
	0x57, 0x4b, 0x60, 0xf7, 0x30, 0xb6, 0x4b, 0x01, //0x000024a0 .quad 93368538716195671
	0x6d, 0x74, 0x98, 0xfe, 0x95, 0x76, 0xa5, 0x84, //0x000024a8 .quad -8888567902952197011
	0x2d, 0x5e, 0x38, 0x35, 0xbd, 0xa3, 0x9e, 0x41, //0x000024b0 .quad 4728396691822632493
	0x88, 0x91, 0x3e, 0x7e, 0x3b, 0xd4, 0xce, 0xa5, //0x000024b8 .quad -6499023860262858360
	0xb9, 0x75, 0x86, 0x82, 0xac, 0x4c, 0x06, 0x52, //0x000024c0 .quad 5910495864778290617
	0xea, 0x35, 0xce, 0x5d, 0x4a, 0x89, 0x42, 0xcf, //0x000024c8 .quad -3512093806901185046
	0x93, 0x09, 0x94, 0xd1, 0xeb, 0xef, 0x43, 0x73, //0x000024d0 .quad 8305745933913819539
	0xb2, 0xe1, 0xa0, 0x7a, 0xce, 0x95, 0x89, 0x81, //0x000024d8 .quad -9112587656954322510
	0xf8, 0x0b, 0xf9, 0xc5, 0xe6, 0xeb, 0x14, 0x10, //0x000024e0 .quad 1158810380537498616
	0x1f, 0x1a, 0x49, 0x19, 0x42, 0xfb, 0xeb, 0xa1, //0x000024e8 .quad -6779048552765515233
	0xf6, 0x4e, 0x77, 0x77, 0xe0, 0x26, 0x1a, 0xd4, //0x000024f0 .quad -3163173042755514634
	0xa6, 0x60, 0x9b, 0x9f, 0x12, 0xfa, 0x66, 0xca, //0x000024f8 .quad -3862124672529506138
	0xb4, 0x22, 0x55, 0x95, 0x98, 0xb0, 0x20, 0x89, //0x00002500 .quad -8565652321871781196
	0xd0, 0x38, 0x82, 0x47, 0x97, 0xb8, 0x00, 0xfd, //0x00002508 .quad -215969822234494768
	0xb0, 0x35, 0x55, 0x5d, 0x5f, 0x6e, 0xb4, 0x55, //0x00002510 .quad 6175682344898606512
	0x82, 0x63, 0xb1, 0x8c, 0x5e, 0x73, 0x20, 0x9e, //0x00002518 .quad -7052510166537641086
	0x1d, 0x83, 0xaa, 0x34, 0xf7, 0x89, 0x21, 0xeb, //0x00002520 .quad -1503769105731517667
	0x62, 0xbc, 0xdd, 0x2f, 0x36, 0x90, 0xa8, 0xc5, //0x00002528 .quad -4203951689744663454
	0xe4, 0x23, 0xd5, 0x01, 0x75, 0xec, 0xe9, 0xa5, //0x00002530 .quad -6491397400591784988
	0x7b, 0x2b, 0xd5, 0xbb, 0x43, 0xb4, 0x12, 0xf7, //0x00002538 .quad -643253593753441413
	0x6e, 0x36, 0x25, 0x21, 0xc9, 0x33, 0xb2, 0x47, //0x00002540 .quad 5166248661484910190
	0x2d, 0x3b, 0x65, 0x55, 0xaa, 0xb0, 0x6b, 0x9a, //0x00002548 .quad -7319562523736982739
	0x0a, 0x84, 0x6e, 0x69, 0xbb, 0xc0, 0x9e, 0x99, //0x00002550 .quad -7377247228426025974
	0xf8, 0x89, 0xbe, 0xea, 0xd4, 0x9c, 0x06, 0xc1, //0x00002558 .quad -4537767136243840520
	0x0d, 0x25, 0xca, 0x43, 0xea, 0x70, 0x06, 0xc0, //0x00002560 .quad -4609873017105144563
	0x76, 0x2c, 0x6e, 0x25, 0x0a, 0x44, 0x48, 0xf1, //0x00002568 .quad -1060522901877412746
	0x28, 0x57, 0x5e, 0x6a, 0x92, 0x06, 0x04, 0x38, //0x00002570 .quad 4036358391950366504
	0xca, 0xdb, 0x64, 0x57, 0x86, 0x2a, 0xcd, 0x96, //0x00002578 .quad -7580355841314464822
	0xf2, 0xec, 0xf5, 0x04, 0x37, 0x08, 0x05, 0xc6, //0x00002580 .quad -4177924046916817678
	0xbc, 0x12, 0x3e, 0xed, 0x27, 0x75, 0x80, 0xbc, //0x00002588 .quad -4863758783215693124
	0x2e, 0x68, 0x33, 0xc6, 0x44, 0x4a, 0x86, 0xf7, //0x00002590 .quad -610719040218634194
	0x6b, 0x97, 0x8d, 0xe8, 0x71, 0x92, 0xa0, 0xeb, //0x00002598 .quad -1468012460592228501
	0x1d, 0x21, 0xe0, 0xfb, 0x6a, 0xee, 0xb3, 0x7a, //0x000025a0 .quad 8841672636718129437
	0xa3, 0x7e, 0x58, 0x31, 0x87, 0x5b, 0x44, 0x93, //0x000025a8 .quad -7835036815511224669
	0x64, 0x29, 0xd8, 0xba, 0x05, 0xea, 0x60, 0x59, //0x000025b0 .quad 6440404777470273892
	0x4c, 0x9e, 0xae, 0xfd, 0x68, 0x72, 0x15, 0xb8, //0x000025b8 .quad -5182110000961642932
	0xbd, 0x33, 0x8e, 0x29, 0x87, 0x24, 0xb9, 0x6f, //0x000025c0 .quad 8050505971837842365
	0xdf, 0x45, 0x1a, 0x3d, 0x03, 0xcf, 0x1a, 0xe6, //0x000025c8 .quad -1865951482774665761
	0x56, 0xe0, 0xf8, 0x79, 0xd4, 0xb6, 0xd3, 0xa5, //0x000025d0 .quad -6497648813669818282
	0xab, 0x6b, 0x30, 0x06, 0x62, 0xc1, 0xd0, 0x8f, //0x000025d8 .quad -8083748704375247957
	0x6c, 0x18, 0x77, 0x98, 0x89, 0xa4, 0x48, 0x8f, //0x000025e0 .quad -8122061017087272852
	0x96, 0x86, 0xbc, 0x87, 0xba, 0xf1, 0xc4, 0xb3, //0x000025e8 .quad -5492999862041672042
	0x87, 0xde, 0x94, 0xfe, 0xab, 0xcd, 0x1a, 0x33, //0x000025f0 .quad 3682481783923072647
	0x3c, 0xa8, 0xab, 0x29, 0x29, 0x2e, 0xb6, 0xe0, //0x000025f8 .quad -2254563809124702148
	0x14, 0x0b, 0x1d, 0x7f, 0x8b, 0xc0, 0xf0, 0x9f, //0x00002600 .quad -6921820921902855404
	0x25, 0x49, 0x0b, 0xba, 0xd9, 0xdc, 0x71, 0x8c, //0x00002608 .quad -8326631408344020699
	0xd9, 0x4d, 0xe4, 0x5e, 0xae, 0xf0, 0xec, 0x07, //0x00002610 .quad 571095884476206553
	0x6f, 0x1b, 0x8e, 0x28, 0x10, 0x54, 0x8e, 0xaf, //0x00002618 .quad -5796603242002637969
	0x50, 0x61, 0x9d, 0xf6, 0xd9, 0x2c, 0xe8, 0xc9, //0x00002620 .quad -3897816162832129712
	0x4a, 0xa2, 0xb1, 0x32, 0x14, 0xe9, 0x71, 0xdb, //0x00002628 .quad -2634068034075909558
	0xd2, 0x5c, 0x22, 0x3a, 0x08, 0x1c, 0x31, 0xbe, //0x00002630 .quad -4741978110983775022
	0x6e, 0x05, 0xaf, 0x9f, 0xac, 0x31, 0x27, 0x89, //0x00002638 .quad -8563821548938525330
	0x06, 0xf4, 0xaa, 0x48, 0x0a, 0x63, 0xbd, 0x6d, //0x00002640 .quad 7907585416552444934
	0xca, 0xc6, 0x9a, 0xc7, 0x17, 0xfe, 0x70, 0xab, //0x00002648 .quad -6093090917745768758
	0x08, 0xb1, 0xd5, 0xda, 0xcc, 0xbb, 0x2c, 0x09, //0x00002650 .quad 661109733835780360
	0x7d, 0x78, 0x81, 0xb9, 0x9d, 0x3d, 0x4d, 0xd6, //0x00002658 .quad -3004677628754823043
	0xa5, 0x8e, 0xc5, 0x08, 0x60, 0xf5, 0xbb, 0x25, //0x00002660 .quad 2719036592861056677
	0x4e, 0xeb, 0xf0, 0x93, 0x82, 0x46, 0xf0, 0x85, //0x00002668 .quad -8795452545612846258
	0x4e, 0xf2, 0xf6, 0x0a, 0xb8, 0xf2, 0x2a, 0xaf, //0x00002670 .quad -5824576295778454962
	0x21, 0x26, 0xed, 0x38, 0x23, 0x58, 0x6c, 0xa7, //0x00002678 .quad -6382629663588669919
	0xe1, 0xae, 0xb4, 0x0d, 0x66, 0xaf, 0xf5, 0x1a, //0x00002680 .quad 1942651667131707105
	0xaa, 0x6f, 0x28, 0x07, 0x2c, 0x6e, 0x47, 0xd1, //0x00002688 .quad -3366601061058449494
	0x4d, 0xed, 0x90, 0xc8, 0x9f, 0x8d, 0xd9, 0x50, //0x00002690 .quad 5825843310384704845
	0xca, 0x45, 0x79, 0x84, 0xdb, 0xa4, 0xcc, 0x82, //0x00002698 .quad -9021654690802612790
	0xa0, 0x28, 0xb5, 0xba, 0x07, 0xf1, 0x0f, 0xe5, //0x000026a0 .quad -1941067898873894752
	0x3c, 0x97, 0x97, 0x65, 0x12, 0xce, 0x7f, 0xa3, //0x000026a8 .quad -6665382345075878084
	0xc8, 0x72, 0x62, 0xa9, 0x49, 0xed, 0x53, 0x1e, //0x000026b0 .quad 2185351144835019464
	0x0c, 0x7d, 0xfd, 0xfe, 0x96, 0xc1, 0x5f, 0xcc, //0x000026b8 .quad -3720041912917459700
	0x7a, 0x0f, 0xbb, 0x13, 0x9c, 0xe8, 0xe8, 0x25, //0x000026c0 .quad 2731688931043774330
	0x4f, 0xdc, 0xbc, 0xbe, 0xfc, 0xb1, 0x77, 0xff, //0x000026c8 .quad -38366372719436721
	0xac, 0xe9, 0x54, 0x8c, 0x61, 0x91, 0xb1, 0x77, //0x000026d0 .quad 8624834609543440812
	0xb1, 0x09, 0x36, 0xf7, 0x3d, 0xcf, 0xaa, 0x9f, //0x000026d8 .quad -6941508010590729807
	0x17, 0x24, 0x6a, 0xef, 0xb9, 0xf5, 0x9d, 0xd5, //0x000026e0 .quad -3054014793352862697
	0x1d, 0x8c, 0x03, 0x75, 0x0d, 0x83, 0x95, 0xc7, //0x000026e8 .quad -4065198994811024355
	0x1d, 0xad, 0x44, 0x6b, 0x28, 0x73, 0x05, 0x4b, //0x000026f0 .quad 5405853545163697437
	0x25, 0x6f, 0x44, 0xd2, 0xd0, 0xe3, 0x7a, 0xf9, //0x000026f8 .quad -469812725086392539
	0x32, 0xec, 0x0a, 0x43, 0xf9, 0x67, 0xe3, 0x4e, //0x00002700 .quad 5684501474941004850
	0x77, 0xc5, 0x6a, 0x83, 0x62, 0xce, 0xec, 0x9b, //0x00002708 .quad -7211161980820077193
	0x3f, 0xa7, 0xcd, 0x93, 0xf7, 0x41, 0x9c, 0x22, //0x00002710 .quad 2493940825248868159
	0xd5, 0x76, 0x45, 0x24, 0xfb, 0x01, 0xe8, 0xc2, //0x00002718 .quad -4402266457597708587
	0x0f, 0x11, 0xc1, 0x78, 0x75, 0x52, 0x43, 0x6b, //0x00002720 .quad 7729112049988473103
	0x8a, 0xd4, 0x56, 0xed, 0x79, 0x02, 0xa2, 0xf3, //0x00002728 .quad -891147053569747830
	0xa9, 0xaa, 0x78, 0x6b, 0x89, 0x13, 0x0a, 0x83, //0x00002730 .quad -9004363024039368023
	0xd6, 0x44, 0x56, 0x34, 0x8c, 0x41, 0x45, 0x98, //0x00002738 .quad -7474495936122174250
	0x53, 0xd5, 0x56, 0xc6, 0x6b, 0x98, 0xcc, 0x23, //0x00002740 .quad 2579604275232953683
	0x0c, 0xd6, 0x6b, 0x41, 0xef, 0x91, 0x56, 0xbe, //0x00002748 .quad -4731433901725329908
	0xa8, 0x8a, 0xec, 0xb7, 0x86, 0xbe, 0xbf, 0x2c, //0x00002750 .quad 3224505344041192104
	0x8f, 0xcb, 0xc6, 0x11, 0x6b, 0x36, 0xec, 0xed, //0x00002758 .quad -1302606358729274481
	0xa9, 0xd6, 0xf3, 0x32, 0x14, 0xd7, 0xf7, 0x7b, //0x00002760 .quad 8932844867666826921
	0x39, 0x3f, 0x1c, 0xeb, 0x02, 0xa2, 0xb3, 0x94, //0x00002768 .quad -7731658001846878407
	0x53, 0xcc, 0xb0, 0x3f, 0xd9, 0xcc, 0xf5, 0xda, //0x00002770 .quad -2669001970698630061
	0x07, 0x4f, 0xe3, 0xa5, 0x83, 0x8a, 0xe0, 0xb9, //0x00002778 .quad -5052886483881210105
	0x68, 0xff, 0x9c, 0x8f, 0x0f, 0x40, 0xb3, 0xd1, //0x00002780 .quad -3336252463373287576
	0xc9, 0x22, 0x5c, 0x8f, 0x24, 0xad, 0x58, 0xe8, //0x00002788 .quad -1704422086424124727
	0xa1, 0x1f, 0xc2, 0xb9, 0x09, 0x08, 0x10, 0x23, //0x00002790 .quad 2526528228819083169
	0xbe, 0x95, 0x99, 0xd9, 0x36, 0x6c, 0x37, 0x91, //0x00002798 .quad -7982792831656159810
	0x8a, 0xa7, 0x32, 0x28, 0x0c, 0x0a, 0xd4, 0xab, //0x000027a0 .quad -6065211750830921846
	0x2d, 0xfb, 0xff, 0x8f, 0x44, 0x47, 0x85, 0xb5, //0x000027a8 .quad -5366805021142811859
	0x6c, 0x51, 0x3f, 0x32, 0x8f, 0x0c, 0xc9, 0x16, //0x000027b0 .quad 1641857348316123500
	0xf9, 0xf9, 0xff, 0xb3, 0x15, 0x99, 0xe6, 0xe2, //0x000027b8 .quad -2096820258001126919
	0xe3, 0x92, 0x67, 0x7f, 0xd9, 0xa7, 0x3d, 0xae, //0x000027c0 .quad -5891368184943504669
	0x3b, 0xfc, 0x7f, 0x90, 0xad, 0x1f, 0xd0, 0x8d, //0x000027c8 .quad -8228041688891786181
	0x9c, 0x77, 0x41, 0xdf, 0xcf, 0x11, 0xcd, 0x99, //0x000027d0 .quad -7364210231179380836
	0x4a, 0xfb, 0x9f, 0xf4, 0x98, 0x27, 0x44, 0xb1, //0x000027d8 .quad -5673366092687344822
	0x83, 0xd5, 0x11, 0xd7, 0x43, 0x56, 0x40, 0x40, //0x000027e0 .quad 4629795266307937667
	0x1d, 0xfa, 0xc7, 0x31, 0x7f, 0x31, 0x95, 0xdd, //0x000027e8 .quad -2480021597431793123
	0x72, 0x25, 0x6b, 0x66, 0xea, 0x35, 0x28, 0x48, //0x000027f0 .quad 5199465050656154994
	0x52, 0xfc, 0x1c, 0x7f, 0xef, 0x3e, 0x7d, 0x8a, //0x000027f8 .quad -8467542526035952558
	0xcf, 0xee, 0x05, 0x00, 0x65, 0x43, 0x32, 0xda, //0x00002800 .quad -2724040723534582065
	0x66, 0x3b, 0xe4, 0x5e, 0xab, 0x8e, 0x1c, 0xad, //0x00002808 .quad -5972742139117552794
	0x82, 0x6a, 0x07, 0x40, 0x3e, 0xd4, 0xbe, 0x90, //0x00002810 .quad -8016736922845615486
	0x40, 0x4a, 0x9d, 0x36, 0x56, 0xb2, 0x63, 0xd8, //0x00002818 .quad -2854241655469553088
	0x91, 0xa2, 0x04, 0xe8, 0xa6, 0x44, 0x77, 0x5a, //0x00002820 .quad 6518754469289960081
	0x68, 0x4e, 0x22, 0xe2, 0x75, 0x4f, 0x3e, 0x87, //0x00002828 .quad -8701430062309552536
	0x36, 0xcb, 0x05, 0xa2, 0xd0, 0x15, 0x15, 0x71, //0x00002830 .quad 8148443086612450102
	0x02, 0xe2, 0xaa, 0x5a, 0x53, 0xe3, 0x0d, 0xa9, //0x00002838 .quad -6265101559459552766
	0x03, 0x3e, 0x87, 0xca, 0x44, 0x5b, 0x5a, 0x0d, //0x00002840 .quad 962181821410786819
	0x83, 0x9a, 0x55, 0x31, 0x28, 0x5c, 0x51, 0xd3, //0x00002848 .quad -3219690930897053053
	0xc2, 0x86, 0x94, 0xfe, 0x0a, 0x79, 0x58, 0xe8, //0x00002850 .quad -1704479370831952190
	0x91, 0x80, 0xd5, 0x1e, 0x99, 0xd9, 0x12, 0x84, //0x00002858 .quad -8929835859451740015
	0x72, 0xa8, 0x39, 0xbe, 0x4d, 0x97, 0x6e, 0x62, //0x00002860 .quad 7092772823314835570
	0xb6, 0xe0, 0x8a, 0x66, 0xff, 0x8f, 0x17, 0xa5, //0x00002868 .quad -6550608805887287114
	0x8f, 0x12, 0xc8, 0x2d, 0x21, 0x3d, 0x0a, 0xfb, //0x00002870 .quad -357406007711231345
	0xe3, 0x98, 0x2d, 0x40, 0xff, 0x73, 0x5d, 0xce, //0x00002878 .quad -3576574988931720989
	0x99, 0x0b, 0x9d, 0xbc, 0x34, 0x66, 0xe6, 0x7c, //0x00002880 .quad 8999993282035256217
	0x8e, 0x7f, 0x1c, 0x88, 0x7f, 0x68, 0xfa, 0x80, //0x00002888 .quad -9152888395723407474
	0x80, 0x4e, 0xc4, 0xeb, 0xc1, 0xff, 0x1f, 0x1c, //0x00002890 .quad 2026619565689294464
	0x72, 0x9f, 0x23, 0x6a, 0x9f, 0x02, 0x39, 0xa1, //0x00002898 .quad -6829424476226871438
	0x20, 0x62, 0xb5, 0x66, 0xb2, 0xff, 0x27, 0xa3, //0x000028a0 .quad -6690097579743157728
	0x4e, 0x87, 0xac, 0x44, 0x47, 0x43, 0x87, 0xc9, //0x000028a8 .quad -3925094576856201394
	0xa8, 0xba, 0x62, 0x00, 0x9f, 0xff, 0xf1, 0x4b, //0x000028b0 .quad 5472436080603216552
	0x22, 0xa9, 0xd7, 0x15, 0x19, 0x14, 0xe9, 0xfb, //0x000028b8 .quad -294682202642863838
	0xa9, 0xb4, 0x3d, 0x60, 0xc3, 0x3f, 0x77, 0x6f, //0x000028c0 .quad 8031958568804398249
	0xb5, 0xc9, 0xa6, 0xad, 0x8f, 0xac, 0x71, 0x9d, //0x000028c8 .quad -7101705404292871755
	0xd3, 0x21, 0x4d, 0x38, 0xb4, 0x0f, 0x55, 0xcb, //0x000028d0 .quad -3795109844276665901
	0x22, 0x7c, 0x10, 0x99, 0xb3, 0x17, 0xce, 0xc4, //0x000028d8 .quad -4265445736938701790
	0x48, 0x6a, 0x60, 0x46, 0xa1, 0x53, 0x2a, 0x7e, //0x000028e0 .quad 9091170749936331336
	0x2b, 0x9b, 0x54, 0x7f, 0xa0, 0x9d, 0x01, 0xf6, //0x000028e8 .quad -720121152745989333
	0x6d, 0x42, 0xfc, 0xcb, 0x44, 0x74, 0xda, 0x2e, //0x000028f0 .quad 3376138709496513133
	0xfb, 0xe0, 0x94, 0x4f, 0x84, 0x02, 0xc1, 0x99, //0x000028f8 .quad -7367604748107325189
	0x08, 0x53, 0xfb, 0xfe, 0x55, 0x11, 0x91, 0xfa, //0x00002900 .quad -391512631556746488
	0x39, 0x19, 0x7a, 0x63, 0x25, 0x43, 0x31, 0xc0, //0x00002908 .quad -4597819916706768583
	0xca, 0x27, 0xba, 0x7e, 0xab, 0x55, 0x35, 0x79, //0x00002910 .quad 8733981247408842698
	0x88, 0x9f, 0x58, 0xbc, 0xee, 0x93, 0x3d, 0xf0, //0x00002918 .quad -1135588877456072824
	0xde, 0x58, 0x34, 0x2f, 0x8b, 0x55, 0xc1, 0x4b, //0x00002920 .quad 5458738279630526686
	0xb5, 0x63, 0xb7, 0x35, 0x75, 0x7c, 0x26, 0x96, //0x00002928 .quad -7627272076051127371
	0x16, 0x6f, 0x01, 0xfb, 0xed, 0xaa, 0xb1, 0x9e, //0x00002930 .quad -7011635205744005354
	0xa2, 0x3c, 0x25, 0x83, 0x92, 0x1b, 0xb0, 0xbb, //0x00002938 .quad -4922404076636521310
	0xdc, 0xca, 0xc1, 0x79, 0xa9, 0x15, 0x5e, 0x46, //0x00002940 .quad 5070514048102157020
	0xcb, 0x8b, 0xee, 0x23, 0x77, 0x22, 0x9c, 0xea, //0x00002948 .quad -1541319077368263733
	0xc9, 0x1e, 0x19, 0xec, 0x89, 0xcd, 0xfa, 0x0b, //0x00002950 .quad 863228270850154185
	0x5f, 0x17, 0x75, 0x76, 0x8a, 0x95, 0xa1, 0x92, //0x00002958 .quad -7880853450996246689
	0x7b, 0x66, 0x1f, 0x67, 0xec, 0x80, 0xf9, 0xce, //0x00002960 .quad -3532650679864695173
	0x36, 0x5d, 0x12, 0x14, 0xed, 0xfa, 0x49, 0xb7, //0x00002968 .quad -5239380795317920458
	0x1a, 0x40, 0xe7, 0x80, 0x27, 0xe1, 0xb7, 0x82, //0x00002970 .quad -9027499368258256870
	0x84, 0xf4, 0x16, 0x59, 0xa8, 0x79, 0x1c, 0xe5, //0x00002978 .quad -1937539975720012668
	0x10, 0x88, 0x90, 0xb0, 0xb8, 0xec, 0xb2, 0xd1, //0x00002980 .quad -3336344095947716592
	0xd2, 0x58, 0xae, 0x37, 0x09, 0xcc, 0x31, 0x8f, //0x00002988 .quad -8128491512466089774
	0x15, 0xaa, 0xb4, 0xdc, 0xe6, 0xa7, 0x1f, 0x86, //0x00002990 .quad -8782116138362033643
	0x07, 0xef, 0x99, 0x85, 0x0b, 0x3f, 0xfe, 0xb2, //0x00002998 .quad -5548928372155224313
	0x9a, 0xd4, 0xe1, 0x93, 0xe0, 0x91, 0xa7, 0x67, //0x000029a0 .quad 7469098900757009562
	0xc9, 0x6a, 0x00, 0x67, 0xce, 0xce, 0xbd, 0xdf, //0x000029a8 .quad -2324474446766642487
	0xe0, 0x24, 0x6d, 0x5c, 0x2c, 0xbb, 0xc8, 0xe0, //0x000029b0 .quad -2249342214667950880
	0xbd, 0x42, 0x60, 0x00, 0x41, 0xa1, 0xd6, 0x8b, //0x000029b8 .quad -8370325556870233411
	0x18, 0x6e, 0x88, 0x73, 0xf7, 0xe9, 0xfa, 0x58, //0x000029c0 .quad 6411694268519837208
	0x6d, 0x53, 0x78, 0x40, 0x91, 0x49, 0xcc, 0xae, //0x000029c8 .quad -5851220927660403859
	0x9e, 0x89, 0x6a, 0x50, 0x75, 0xa4, 0x39, 0xaf, //0x000029d0 .quad -5820440219632367202
	0x48, 0x68, 0x96, 0x90, 0xf5, 0x5b, 0x7f, 0xda, //0x000029d8 .quad -2702340141148116920
	0x03, 0x96, 0x42, 0x52, 0xc9, 0x06, 0x84, 0x6d, //0x000029e0 .quad 7891439908798240259
	0x2d, 0x01, 0x5e, 0x7a, 0x79, 0x99, 0x8f, 0x88, //0x000029e8 .quad -8606491615858654931
	0x83, 0x3b, 0xd3, 0xa6, 0x7b, 0x08, 0xe5, 0xc8, //0x000029f0 .quad -3970758169284363389
	0x78, 0x81, 0xf5, 0xd8, 0xd7, 0x7f, 0xb3, 0xaa, //0x000029f8 .quad -6146428501395930760
	0x64, 0x0a, 0x88, 0x90, 0x9a, 0x4a, 0x1e, 0xfb, //0x00002a00 .quad -351761693178066332
	0xd6, 0xe1, 0x32, 0xcf, 0xcd, 0x5f, 0x60, 0xd5, //0x00002a08 .quad -3071349608317525546
	0x7f, 0x06, 0x55, 0x9a, 0xa0, 0xee, 0xf2, 0x5c, //0x00002a10 .quad 6697677969404790399
	0x26, 0xcd, 0x7f, 0xa1, 0xe0, 0x3b, 0x5c, 0x85, //0x00002a18 .quad -8837122532839535322
	0x1e, 0x48, 0xea, 0xc0, 0x48, 0xaa, 0x2f, 0xf4, //0x00002a20 .quad -851274575098787810
	0x6f, 0xc0, 0xdf, 0xc9, 0xd8, 0x4a, 0xb3, 0xa6, //0x00002a28 .quad -6434717147622031249
	0x26, 0xda, 0x24, 0xf1, 0xda, 0x94, 0x3b, 0xf1, //0x00002a30 .quad -1064093218873484762
	0x8b, 0xb0, 0x57, 0xfc, 0x8e, 0x1d, 0x60, 0xd0, //0x00002a38 .quad -3431710416100151157
	0x58, 0x08, 0xb7, 0xd6, 0x08, 0x3d, 0xc5, 0x76, //0x00002a40 .quad 8558313775058847832
	0x57, 0xce, 0xb6, 0x5d, 0x79, 0x12, 0x3c, 0x82, //0x00002a48 .quad -9062348037703676329
	0x6e, 0xca, 0x64, 0x0c, 0x4b, 0x8c, 0x76, 0x54, //0x00002a50 .quad 6086206200396171886
	0xed, 0x81, 0x24, 0xb5, 0x17, 0x17, 0xcb, 0xa2, //0x00002a58 .quad -6716249028702207507
	0x09, 0xfd, 0x7d, 0xcf, 0x5d, 0x2f, 0x94, 0xa9, //0x00002a60 .quad -6227300304786948855
	0x68, 0xa2, 0x6d, 0xa2, 0xdd, 0xdc, 0x7d, 0xcb, //0x00002a68 .quad -3783625267450371480
	0x4c, 0x7c, 0x5d, 0x43, 0x35, 0x3b, 0xf9, 0xd3, //0x00002a70 .quad -3172439362556298164
	0x02, 0x0b, 0x09, 0x0b, 0x15, 0x54, 0x5d, 0xfe, //0x00002a78 .quad -117845565885576446
	0xaf, 0x6d, 0x1a, 0x4a, 0x01, 0xc5, 0x7b, 0xc4, //0x00002a80 .quad -4288617610811380305
	0xe1, 0xa6, 0xe5, 0x26, 0x8d, 0x54, 0xfa, 0x9e, //0x00002a88 .quad -6991182506319567135
	0x1b, 0x09, 0xa1, 0x9c, 0x41, 0xb6, 0x9a, 0x35, //0x00002a90 .quad 3862600023340550427
	0x9a, 0x10, 0x9f, 0x70, 0xb0, 0xe9, 0xb8, 0xc6, //0x00002a98 .quad -4127292114472071014
	0x62, 0x4b, 0xc9, 0x03, 0xd2, 0x63, 0x01, 0xc3, //0x00002aa0 .quad -4395122007679087774
	0xc0, 0xd4, 0xc6, 0x8c, 0x1c, 0x24, 0x67, 0xf8, //0x00002aa8 .quad -547429124662700864
	0x1d, 0xcf, 0x5d, 0x42, 0x63, 0xde, 0xe0, 0x79, //0x00002ab0 .quad 8782263791269039901
	0xf8, 0x44, 0xfc, 0xd7, 0x91, 0x76, 0x40, 0x9b, //0x00002ab8 .quad -7259672230555269896
	0xe4, 0x42, 0xf5, 0x12, 0xfc, 0x15, 0x59, 0x98, //0x00002ac0 .quad -7468914334623251740
	0x36, 0x56, 0xfb, 0x4d, 0x36, 0x94, 0x10, 0xc2, //0x00002ac8 .quad -4462904269766699466
	0x9d, 0x93, 0xb2, 0x17, 0x7b, 0x5b, 0x6f, 0x3e, //0x00002ad0 .quad 4498915137003099037
	0xc4, 0x2b, 0x7a, 0xe1, 0x43, 0xb9, 0x94, 0xf2, //0x00002ad8 .quad -966944318780986428
	0x42, 0x9c, 0xcf, 0xee, 0x2c, 0x99, 0x05, 0xa7, //0x00002ae0 .quad -6411550076227838910
	0x5a, 0x5b, 0xec, 0x6c, 0xca, 0xf3, 0x9c, 0x97, //0x00002ae8 .quad -7521869226879198374
	0x53, 0x83, 0x83, 0x2a, 0x78, 0xff, 0xc6, 0x50, //0x00002af0 .quad 5820620459997365075
	0x31, 0x72, 0x27, 0x08, 0xbd, 0x30, 0x84, 0xbd, //0x00002af8 .quad -4790650515171610063
	0x28, 0x64, 0x24, 0x35, 0x56, 0xbf, 0xf8, 0xa4, //0x00002b00 .quad -6559282480285457368
	0xbd, 0x4e, 0x31, 0x4a, 0xec, 0x3c, 0xe5, 0xec, //0x00002b08 .quad -1376627125537124675
	0x99, 0xbe, 0x36, 0xe1, 0x95, 0x77, 0x1b, 0x87, //0x00002b10 .quad -8711237568605798759
	0x36, 0xd1, 0x5e, 0xae, 0x13, 0x46, 0x0f, 0x94, //0x00002b18 .quad -7777920981101784778
	0x3f, 0x6e, 0x84, 0x59, 0x7b, 0x55, 0xe2, 0x28, //0x00002b20 .quad 2946011094524915263
	0x84, 0x85, 0xf6, 0x99, 0x98, 0x17, 0x13, 0xb9, //0x00002b28 .quad -5110715207949843068
	0xcf, 0x89, 0xe5, 0x2f, 0xda, 0xea, 0x1a, 0x33, //0x00002b30 .quad 3682513868156144079
	0xe5, 0x26, 0x74, 0xc0, 0x7e, 0xdd, 0x57, 0xe7, //0x00002b38 .quad -1776707991509915931
	0x21, 0x76, 0xef, 0x5d, 0xc8, 0xd2, 0xf0, 0x3f, //0x00002b40 .quad 4607414176811284001
	0x4f, 0x98, 0x48, 0x38, 0x6f, 0xea, 0x96, 0x90, //0x00002b48 .quad -8027971522334779313
	0xa9, 0x53, 0x6b, 0x75, 0x7a, 0x07, 0xed, 0x0f, //0x00002b50 .quad 1147581702586717097
	0x63, 0xbe, 0x5a, 0x06, 0x0b, 0xa5, 0xbc, 0xb4, //0x00002b58 .quad -5423278384491086237
	0x94, 0x28, 0xc6, 0x12, 0x59, 0x49, 0xe8, 0xd3, //0x00002b60 .quad -3177208890193991532
	0xfb, 0x6d, 0xf1, 0xc7, 0x4d, 0xce, 0xeb, 0xe1, //0x00002b68 .quad -2167411962186469893
	0x5c, 0xd9, 0xbb, 0xab, 0xd7, 0x2d, 0x71, 0x64, //0x00002b70 .quad 7237616480483531100
	0xbd, 0xe4, 0xf6, 0x9c, 0xf0, 0x60, 0x33, 0x8d, //0x00002b78 .quad -8272161504007625539
	0xb3, 0xcf, 0xaa, 0x96, 0x4d, 0x79, 0x8d, 0xbd, //0x00002b80 .quad -4788037454677749837
	0xec, 0x9d, 0x34, 0xc4, 0x2c, 0x39, 0x80, 0xb0, //0x00002b88 .quad -5728515861582144020
	0xa0, 0x83, 0x55, 0xfc, 0xa0, 0xd7, 0xf0, 0xec, //0x00002b90 .quad -1373360799919799392
	0x67, 0xc5, 0x41, 0xf5, 0x77, 0x47, 0xa0, 0xdc, //0x00002b98 .quad -2548958808550292121
	0x44, 0x72, 0xb5, 0x9d, 0xc4, 0x86, 0x16, 0xf4, //0x00002ba0 .quad -858350499949874620
	0x60, 0x1b, 0x49, 0xf9, 0xaa, 0x2c, 0xe4, 0x89, //0x00002ba8 .quad -8510628282985014432
	0xd5, 0xce, 0x22, 0xc5, 0x75, 0x28, 0x1c, 0x31, //0x00002bb0 .quad 3538747893490044629
	0x39, 0x62, 0x9b, 0xb7, 0xd5, 0x37, 0x5d, 0xac, //0x00002bb8 .quad -6026599335303880135
	0x8b, 0x82, 0x6b, 0x36, 0x93, 0x32, 0x63, 0x7d, //0x00002bc0 .quad 9035120885289943691
	0xc7, 0x3a, 0x82, 0x25, 0xcb, 0x85, 0x74, 0xd7, //0x00002bc8 .quad -2921563150702462265
	0x97, 0x31, 0x03, 0x02, 0x9c, 0xff, 0x5d, 0xae, //0x00002bd0 .quad -5882264492762254953
	0xbc, 0x64, 0x71, 0xf7, 0x9e, 0xd3, 0xa8, 0x86, //0x00002bd8 .quad -8743505996830120772
	0xfc, 0xfd, 0x83, 0x02, 0x83, 0x7f, 0xf5, 0xd9, //0x00002be0 .quad -2741144597525430788
	0xeb, 0xbd, 0x4d, 0xb5, 0x86, 0x08, 0x53, 0xa8, //0x00002be8 .quad -6317696477610263061
	0x7b, 0xfd, 0x24, 0xc3, 0x63, 0xdf, 0x72, 0xd0, //0x00002bf0 .quad -3426430746906788485
	0x66, 0x2d, 0xa1, 0x62, 0xa8, 0xca, 0x67, 0xd2, //0x00002bf8 .quad -3285434578585440922
	0x6d, 0x1e, 0xf7, 0x59, 0x9e, 0xcb, 0x47, 0x42, //0x00002c00 .quad 4776009810824339053
	0x60, 0xbc, 0xa4, 0x3d, 0xa9, 0xde, 0x80, 0x83, //0x00002c08 .quad -8970925639256982432
	0x08, 0xe6, 0x74, 0xf0, 0x85, 0xbe, 0xd9, 0x52, //0x00002c10 .quad 5970012263530423816
	0x78, 0xeb, 0x0d, 0x8d, 0x53, 0x16, 0x61, 0xa4, //0x00002c18 .quad -6601971030643840136
	0x8b, 0x1f, 0x92, 0x6c, 0x27, 0x2e, 0x90, 0x67, //0x00002c20 .quad 7462515329413029771
	0x56, 0x66, 0x51, 0x70, 0xe8, 0x5b, 0x79, 0xcd, //0x00002c28 .quad -3640777769877412266
	0xb6, 0x53, 0xdb, 0xa3, 0xd8, 0x1c, 0xba, 0x00, //0x00002c30 .quad 52386062455755702
	0xf6, 0xdf, 0x32, 0x46, 0x71, 0xd9, 0x6b, 0x80, //0x00002c38 .quad -9193015133814464522
	0xa4, 0x28, 0xd2, 0xcc, 0x0e, 0xa4, 0xe8, 0x80, //0x00002c40 .quad -9157889458785081180
	0xf3, 0x97, 0xbf, 0x97, 0xcd, 0xcf, 0x86, 0xa0, //0x00002c48 .quad -6879582898840692749
	0xcd, 0xb2, 0x06, 0x80, 0x12, 0xcd, 0x22, 0x61, //0x00002c50 .quad 6999382250228200141
	0xf0, 0x7d, 0xaf, 0xfd, 0xc0, 0x83, 0xa8, 0xc8, //0x00002c58 .quad -3987792605123478032
	0x81, 0x5f, 0x08, 0x20, 0x57, 0x80, 0x6b, 0x79, //0x00002c60 .quad 8749227812785250177
	0x6c, 0x5d, 0x1b, 0x3d, 0xb1, 0xa4, 0xd2, 0xfa, //0x00002c68 .quad -373054737976959636
	0xb0, 0x3b, 0x05, 0x74, 0x36, 0x30, 0xe3, 0xcb, //0x00002c70 .quad -3755104653863994448
	0x63, 0x1a, 0x31, 0xc6, 0xee, 0xa6, 0xc3, 0x9c, //0x00002c78 .quad -7150688238876681629
	0x9c, 0x8a, 0x06, 0x11, 0x44, 0xfc, 0xdb, 0xbe, //0x00002c80 .quad -4693880817329993060
	0xfc, 0x60, 0xbd, 0x77, 0xaa, 0x90, 0xf4, 0xc3, //0x00002c88 .quad -4326674280168464132
	0x44, 0x2d, 0x48, 0x15, 0x55, 0xfb, 0x92, 0xee, //0x00002c90 .quad -1255665003235103420
	0x3b, 0xb9, 0xac, 0x15, 0xd5, 0xb4, 0xf1, 0xf4, //0x00002c98 .quad -796656831783192261
	0x4a, 0x1c, 0x4d, 0x2d, 0x15, 0xdd, 0x1b, 0x75, //0x00002ca0 .quad 8438581409832836170
	0xc5, 0xf3, 0x8b, 0x2d, 0x05, 0x11, 0x17, 0x99, //0x00002ca8 .quad -7415439547505577019
	0x5d, 0x63, 0xa0, 0x78, 0x5a, 0xd4, 0x62, 0xd2, //0x00002cb0 .quad -3286831292991118499
	0xb6, 0xf0, 0xee, 0x78, 0x46, 0xd5, 0x5c, 0xbf, //0x00002cb8 .quad -4657613415954583370
	0x34, 0x7c, 0xc8, 0x16, 0x71, 0x89, 0xfb, 0x86, //0x00002cc0 .quad -8720225134666286028
	0xe4, 0xac, 0x2a, 0x17, 0x98, 0x0a, 0x34, 0xef, //0x00002cc8 .quad -1210330751515841308
	0xa0, 0x4d, 0x3d, 0xae, 0xe6, 0x35, 0x5d, 0xd4, //0x00002cd0 .quad -3144297699952734816
	0x0e, 0xac, 0x7a, 0x0e, 0x9f, 0x86, 0x80, 0x95, //0x00002cd8 .quad -7673985747338482674
	0x09, 0xa1, 0xcc, 0x59, 0x60, 0x83, 0x74, 0x89, //0x00002ce0 .quad -8542058143368306423
	0x12, 0x57, 0x19, 0xd2, 0x46, 0xa8, 0xe0, 0xba, //0x00002ce8 .quad -4980796165745715438
	0x4b, 0xc9, 0x3f, 0x70, 0x38, 0xa4, 0xd1, 0x2b, //0x00002cf0 .quad 3157485376071780683
	0xd7, 0xac, 0x9f, 0x86, 0x58, 0xd2, 0x98, 0xe9, //0x00002cf8 .quad -1614309188754756393
	0xcf, 0xdd, 0x27, 0x46, 0xa3, 0x06, 0x63, 0x7b, //0x00002d00 .quad 8890957387685944783
	0x06, 0xcc, 0x23, 0x54, 0x77, 0x83, 0xff, 0x91, //0x00002d08 .quad -7926472270612804602
	0x42, 0xd5, 0xb1, 0x17, 0x4c, 0xc8, 0x3b, 0x1a, //0x00002d10 .quad 1890324697752655170
	0x08, 0xbf, 0x2c, 0x29, 0x55, 0x64, 0x7f, 0xb6, //0x00002d18 .quad -5296404319838617848
	0x93, 0x4a, 0x9e, 0x1d, 0x5f, 0xba, 0xca, 0x20, //0x00002d20 .quad 2362905872190818963
	0xca, 0xee, 0x77, 0x73, 0x6a, 0x3d, 0x1f, 0xe4, //0x00002d28 .quad -2008819381370884406
	0x9c, 0xee, 0x82, 0x72, 0x7b, 0xb4, 0x7e, 0x54, //0x00002d30 .quad 6088502188546649756
	0x3e, 0xf5, 0x2a, 0x88, 0x62, 0x86, 0x93, 0x8e, //0x00002d38 .quad -8173041140997884610
	0x43, 0xaa, 0x23, 0x4f, 0x9a, 0x61, 0x9e, 0xe9, //0x00002d40 .quad -1612744301171463613
	0x8d, 0xb2, 0x35, 0x2a, 0xfb, 0x67, 0x38, 0xb2, //0x00002d48 .quad -5604615407819967859
	0xd4, 0x94, 0xec, 0xe2, 0x00, 0xfa, 0x05, 0x64, //0x00002d50 .quad 7207441660390446292
	0x31, 0x1f, 0xc3, 0xf4, 0xf9, 0x81, 0xc6, 0xde, //0x00002d58 .quad -2394083241347571919
	0x04, 0xdd, 0xd3, 0x8d, 0x40, 0xbc, 0x83, 0xde, //0x00002d60 .quad -2412877989897052924
	0x7e, 0xf3, 0xf9, 0x38, 0x3c, 0x11, 0x3c, 0x8b, //0x00002d68 .quad -8413831053483314306
	0x45, 0xd4, 0x48, 0xb1, 0x50, 0xab, 0x24, 0x96, //0x00002d70 .quad -7627783505798704059
	0x5e, 0x70, 0x38, 0x47, 0x8b, 0x15, 0x0b, 0xae, //0x00002d78 .quad -5905602798426754978
	0x57, 0x09, 0x9b, 0xdd, 0x24, 0xd6, 0xad, 0x3b, //0x00002d80 .quad 4300328673033783639
	0x76, 0x8c, 0x06, 0x19, 0xee, 0xda, 0x8d, 0xd9, //0x00002d88 .quad -2770317479606055818
	0xd6, 0xe5, 0x80, 0x0a, 0xd7, 0xa5, 0x4c, 0xe5, //0x00002d90 .quad -1923980597781273130
	0xc9, 0x17, 0xa4, 0xcf, 0xd4, 0xa8, 0xf8, 0x87, //0x00002d98 .quad -8648977452394866743
	0x4c, 0x1f, 0x21, 0xcd, 0x4c, 0xcf, 0x9f, 0x5e, //0x00002da0 .quad 6818396289628184396
	0xbc, 0x1d, 0x8d, 0x03, 0x0a, 0xd3, 0xf6, 0xa9, //0x00002da8 .quad -6199535797066195524
	0x1f, 0x67, 0x69, 0x00, 0x20, 0xc3, 0x47, 0x76, //0x00002db0 .quad 8522995362035230495
	0x2b, 0x65, 0x70, 0x84, 0xcc, 0x87, 0x74, 0xd4, //0x00002db8 .quad -3137733727905356501
	0x73, 0xe0, 0x41, 0x00, 0xf4, 0xd9, 0xec, 0x29, //0x00002dc0 .quad 3021029092058325107
	0x3b, 0x3f, 0xc6, 0xd2, 0xdf, 0xd4, 0xc8, 0x84, //0x00002dc8 .quad -8878612607581929669
	0x90, 0x58, 0x52, 0x00, 0x71, 0x10, 0x68, 0xf4, //0x00002dd0 .quad -835399653354481520
	0x09, 0xcf, 0x77, 0xc7, 0x17, 0x0a, 0xfb, 0xa5, //0x00002dd8 .quad -6486579741050024183
	0xb4, 0xee, 0x66, 0x40, 0x8d, 0x14, 0x82, 0x71, //0x00002de0 .quad 8179122470161673908
	0xcc, 0xc2, 0x55, 0xb9, 0x9d, 0xcc, 0x79, 0xcf, //0x00002de8 .quad -3496538657885142324
	0x30, 0x55, 0x40, 0x48, 0xd8, 0x4c, 0xf1, 0xc6, //0x00002df0 .quad -4111420493003729616
	0xbf, 0x99, 0xd5, 0x93, 0xe2, 0x1f, 0xac, 0x81, //0x00002df8 .quad -9102865688819295809
	0x7c, 0x6a, 0x50, 0x5a, 0x0e, 0xa0, 0xad, 0xb8, //0x00002e00 .quad -5139275616254662020
	0x2f, 0x00, 0xcb, 0x38, 0xdb, 0x27, 0x17, 0xa2, //0x00002e08 .quad -6766896092596731857
	0x1c, 0x85, 0xe4, 0xf0, 0x11, 0x08, 0xd9, 0xa6, //0x00002e10 .quad -6424094520318327524
	0x3b, 0xc0, 0xfd, 0x06, 0xd2, 0xf1, 0x9c, 0xca, //0x00002e18 .quad -3846934097318526917
	0x63, 0xa6, 0x1d, 0x6d, 0x16, 0x4a, 0x8f, 0x90, //0x00002e20 .quad -8030118150397909405
	0x4a, 0x30, 0xbd, 0x88, 0x46, 0x2e, 0x44, 0xfd, //0x00002e28 .quad -196981603220770742
	0xfe, 0x87, 0x32, 0x04, 0x4e, 0x8e, 0x59, 0x9a, //0x00002e30 .quad -7324666853212387330
	0x2e, 0x3e, 0x76, 0x15, 0xec, 0x9c, 0x4a, 0x9e, //0x00002e38 .quad -7040642529654063570
	0xfd, 0x29, 0x3f, 0x85, 0xe1, 0xf1, 0xef, 0x40, //0x00002e40 .quad 4679224488766679549
	0xba, 0xcd, 0xd3, 0x1a, 0x27, 0x44, 0xdd, 0xc5, //0x00002e48 .quad -4189117143640191558
	0x7c, 0xf4, 0x8e, 0xe6, 0x59, 0xee, 0x2b, 0xd1, //0x00002e50 .quad -3374341425896426372
	0x28, 0xc1, 0x88, 0xe1, 0x30, 0x95, 0x54, 0xf7, //0x00002e58 .quad -624710411122851544
	0xce, 0x58, 0x19, 0x30, 0xf8, 0x74, 0xbb, 0x82, //0x00002e60 .quad -9026492418826348338
	0xb9, 0x78, 0xf5, 0x8c, 0x3e, 0xdd, 0x94, 0x9a, //0x00002e68 .quad -7307973034592864071
	0x01, 0xaf, 0x1f, 0x3c, 0x36, 0x52, 0x6a, 0xe3, //0x00002e70 .quad -2059743486678159615
	0xe7, 0xd6, 0x32, 0x30, 0x8e, 0x14, 0x3a, 0xc1, //0x00002e78 .quad -4523280274813692185
	0xc1, 0x9a, 0x27, 0xcb, 0xc3, 0xe6, 0x44, 0xdc, //0x00002e80 .quad -2574679358347699519
	0xa1, 0x8c, 0x3f, 0xbc, 0xb1, 0x99, 0x88, 0xf1, //0x00002e88 .quad -1042414325089727327
	0xb9, 0xc0, 0xf8, 0x5e, 0x3a, 0x10, 0xab, 0x29, //0x00002e90 .quad 3002511419460075705
	0xe5, 0xb7, 0xa7, 0x15, 0x0f, 0x60, 0xf5, 0x96, //0x00002e98 .quad -7569037980822161435
	0xe7, 0xf0, 0xb6, 0xf6, 0x48, 0xd4, 0x15, 0x74, //0x00002ea0 .quad 8364825292752482535
	0xde, 0xa5, 0x11, 0xdb, 0x12, 0xb8, 0xb2, 0xbc, //0x00002ea8 .quad -4849611457600313890
	0x21, 0xad, 0x64, 0x34, 0x5b, 0x49, 0x1b, 0x11, //0x00002eb0 .quad 1232659579085827361
	0x56, 0x0f, 0xd6, 0x91, 0x17, 0x66, 0xdf, 0xeb, //0x00002eb8 .quad -1450328303573004458
	0x34, 0xec, 0xbe, 0x00, 0xd9, 0x0d, 0xb1, 0xca, //0x00002ec0 .quad -3841273781498745804
	0x95, 0xc9, 0x25, 0xbb, 0xce, 0x9f, 0x6b, 0x93, //0x00002ec8 .quad -7823984217374209643
	0x42, 0xa7, 0xee, 0x40, 0x4f, 0x51, 0x5d, 0x3d, //0x00002ed0 .quad 4421779809981343554
	0xfb, 0x3b, 0xef, 0x69, 0xc2, 0x87, 0x46, 0xb8, //0x00002ed8 .quad -5168294253290374149
	0x12, 0x51, 0x2a, 0x11, 0xa3, 0xa5, 0xb4, 0x0c, //0x00002ee0 .quad 915538744049291538
	0xfa, 0x0a, 0x6b, 0x04, 0xb3, 0x29, 0x58, 0xe6, //0x00002ee8 .quad -1848681798185579782
	0xab, 0x72, 0xba, 0xea, 0x85, 0xe7, 0xf0, 0x47, //0x00002ef0 .quad 5183897733458195115
	0xdc, 0xe6, 0xc2, 0xe2, 0x0f, 0x1a, 0xf7, 0x8f, //0x00002ef8 .quad -8072955151507069220
	0x56, 0x0f, 0x69, 0x65, 0x67, 0x21, 0xed, 0x59, //0x00002f00 .quad 6479872166822743894
	0x93, 0xa0, 0x73, 0xdb, 0x93, 0xe0, 0xf4, 0xb3, //0x00002f08 .quad -5479507920956448621
	0x2c, 0x53, 0xc3, 0x3e, 0xc1, 0x69, 0x68, 0x30, //0x00002f10 .quad 3488154190101041964
	0xb8, 0x88, 0x50, 0xd2, 0xb8, 0x18, 0xf2, 0xe0, //0x00002f18 .quad -2237698882768172872
	0xfb, 0x13, 0x3a, 0xc7, 0x18, 0x42, 0x41, 0x1e, //0x00002f20 .quad 2180096368813151227
	0x73, 0x55, 0x72, 0x83, 0x73, 0x4f, 0x97, 0x8c, //0x00002f28 .quad -8316090829371189901
	0xfa, 0x98, 0x08, 0xf9, 0x9e, 0x92, 0xd1, 0xe5, //0x00002f30 .quad -1886565557410948870
	0xcf, 0xea, 0x4e, 0x64, 0x50, 0x23, 0xbd, 0xaf, //0x00002f38 .quad -5783427518286599473
	0x39, 0xbf, 0x4a, 0xb7, 0x46, 0xf7, 0x45, 0xdf, //0x00002f40 .quad -2358206946763686087
	0x83, 0xa5, 0x62, 0x7d, 0x24, 0x6c, 0xac, 0xdb, //0x00002f48 .quad -2617598379430861437
	0x83, 0xb7, 0x8e, 0x32, 0x8c, 0xba, 0x8b, 0x6b, //0x00002f50 .quad 7749492695127472003
	0x72, 0xa7, 0x5d, 0xce, 0x96, 0xc3, 0x4b, 0x89, //0x00002f58 .quad -8553528014785370254
	0x64, 0x65, 0x32, 0x3f, 0x2f, 0xa9, 0x6e, 0x06, //0x00002f60 .quad 463493832054564196
	0x4f, 0x11, 0xf5, 0x81, 0x7c, 0xb4, 0x9e, 0xab, //0x00002f68 .quad -6080224000054324913
	0xbd, 0xfe, 0xfe, 0x0e, 0x7b, 0x53, 0x0a, 0xc8, //0x00002f70 .quad -4032318728359182659
	0xa2, 0x55, 0x72, 0xa2, 0x9b, 0x61, 0x86, 0xd6, //0x00002f78 .quad -2988593981640518238
	0x36, 0x5f, 0x5f, 0xe9, 0x2c, 0x74, 0x06, 0xbd, //0x00002f80 .quad -4826042214438183114
	0x85, 0x75, 0x87, 0x45, 0x01, 0xfd, 0x13, 0x86, //0x00002f88 .quad -8785400266166405755
	0x04, 0x37, 0xb7, 0x23, 0x38, 0x11, 0x48, 0x2c, //0x00002f90 .quad 3190819268807046916
	0xe7, 0x52, 0xe9, 0x96, 0x41, 0xfc, 0x98, 0xa7, //0x00002f98 .quad -6370064314280619289
	0xc5, 0x04, 0xa5, 0x2c, 0x86, 0x15, 0x5a, 0xf7, //0x00002fa0 .quad -623161932418579259
	0xa0, 0xa7, 0xa3, 0xfc, 0x51, 0x3b, 0x7f, 0xd1, //0x00002fa8 .quad -3350894374423386208
	0xfb, 0x22, 0xe7, 0xdb, 0x73, 0x4d, 0x98, 0x9a, //0x00002fb0 .quad -7307005235402693893
	0xc4, 0x48, 0xe6, 0x3d, 0x13, 0x85, 0xef, 0x82, //0x00002fb8 .quad -9011838011655698236
	0xba, 0xeb, 0xe0, 0xd2, 0xd0, 0x60, 0x3e, 0xc1, //0x00002fc0 .quad -4522070525825979462
	0xf5, 0xda, 0x5f, 0x0d, 0x58, 0x66, 0xab, 0xa3, //0x00002fc8 .quad -6653111496142234891
	0xa8, 0x26, 0x99, 0x07, 0x05, 0xf9, 0x8d, 0x31, //0x00002fd0 .quad 3570783879572301480
	0xb3, 0xd1, 0xb7, 0x10, 0xee, 0x3f, 0x96, 0xcc, //0x00002fd8 .quad -3704703351750405709
	0x52, 0x70, 0x7f, 0x49, 0x46, 0x77, 0xf1, 0xfd, //0x00002fe0 .quad -148206168962011054
	0x1f, 0xc6, 0xe5, 0x94, 0xe9, 0xcf, 0xbb, 0xff, //0x00002fe8 .quad -19193171260619233
	0x33, 0xa6, 0xef, 0xed, 0x8b, 0xea, 0xb6, 0xfe, //0x00002ff0 .quad -92628855601256909
	0xd3, 0x9b, 0x0f, 0xfd, 0xf1, 0x61, 0xd5, 0x9f, //0x00002ff8 .quad -6929524759678968877
	0xc0, 0x8f, 0x6b, 0xe9, 0x2e, 0xa5, 0x64, 0xfe, //0x00003000 .quad -115786069501571136
	0xc8, 0x82, 0x53, 0x7c, 0x6e, 0xba, 0xca, 0xc7, //0x00003008 .quad -4050219931171323192
	0xb0, 0x73, 0xc6, 0xa3, 0x7a, 0xce, 0xfd, 0x3d, //0x00003010 .quad 4466953431550423984
	0x7b, 0x63, 0x68, 0x1b, 0x0a, 0x69, 0xbd, 0xf9, //0x00003018 .quad -451088895536766085
	0x4e, 0x08, 0x5c, 0xa6, 0x0c, 0xa1, 0xbe, 0x06, //0x00003020 .quad 486002885505321038
	0x2d, 0x3e, 0x21, 0x51, 0xa6, 0x61, 0x16, 0x9c, //0x00003028 .quad -7199459587351560659
	0x62, 0x0a, 0xf3, 0xcf, 0x4f, 0x49, 0x6e, 0x48, //0x00003030 .quad 5219189625309039202
	0xb8, 0x8d, 0x69, 0xe5, 0x0f, 0xfa, 0x1b, 0xc3, //0x00003038 .quad -4387638465762062920
	0xfa, 0xcc, 0xef, 0xc3, 0xa3, 0xdb, 0x89, 0x5a, //0x00003040 .quad 6523987031636299002
	0x26, 0xf1, 0xc3, 0xde, 0x93, 0xf8, 0xe2, 0xf3, //0x00003048 .quad -872862063775190746
	0x1c, 0xe0, 0x75, 0x5a, 0x46, 0x29, 0x96, 0xf8, //0x00003050 .quad -534194123654701028
	0xb7, 0x76, 0x3a, 0x6b, 0x5c, 0xdb, 0x6d, 0x98, //0x00003058 .quad -7463067817500576073
	0x23, 0x58, 0x13, 0xf1, 0x97, 0xb3, 0xbb, 0xf6, //0x00003060 .quad -667742654568376285
	0x65, 0x14, 0x09, 0x86, 0x33, 0x52, 0x89, 0xbe, //0x00003068 .quad -4717148753448332187
	0x2c, 0x2e, 0x58, 0xed, 0x7d, 0xa0, 0x6a, 0x74, //0x00003070 .quad 8388693718644305452
	0x7f, 0x59, 0x8b, 0x67, 0xc0, 0xa6, 0x2b, 0xee, //0x00003078 .quad -1284749923383027329
	0xdc, 0x1c, 0x57, 0xb4, 0x4e, 0xa4, 0xc2, 0xa8, //0x00003080 .quad -6286281471915778852
	0xef, 0x17, 0xb7, 0x40, 0x38, 0x48, 0xdb, 0x94, //0x00003088 .quad -7720497729755473937
	0x13, 0xe4, 0x6c, 0x61, 0x62, 0x4d, 0xf3, 0x92, //0x00003090 .quad -7857851839894723565
	0xeb, 0xdd, 0xe4, 0x50, 0x46, 0x1a, 0x12, 0xba, //0x00003098 .quad -5038936143766954517
	0x17, 0x1d, 0xc8, 0xf9, 0xba, 0x20, 0xb0, 0x77, //0x000030a0 .quad 8624429273841147159
	0x66, 0x15, 0x1e, 0xe5, 0xd7, 0xa0, 0x96, 0xe8, //0x000030a8 .quad -1686984161281305242
	0x2e, 0x12, 0x1d, 0xdc, 0x74, 0x14, 0xce, 0x0a, //0x000030b0 .quad 778582277723329070
	0x60, 0xcd, 0x32, 0xef, 0x86, 0x24, 0x5e, 0x91, //0x000030b8 .quad -7971894128441897632
	0xba, 0x56, 0x24, 0x13, 0x92, 0x99, 0x81, 0x0d, //0x000030c0 .quad 973227847154161338
	0xb8, 0x80, 0xff, 0xaa, 0xa8, 0xad, 0xb5, 0xb5, //0x000030c8 .quad -5353181642124984136
	0x69, 0x6c, 0xed, 0x97, 0xf6, 0xff, 0xe1, 0x10, //0x000030d0 .quad 1216534808942701673
	0xe6, 0x60, 0xbf, 0xd5, 0x12, 0x19, 0x23, 0xe3, //0x000030d8 .quad -2079791034228842266
	0xc1, 0x63, 0xf4, 0x1e, 0xfa, 0x3f, 0x8d, 0xca, //0x000030e0 .quad -3851351762838199359
	0x8f, 0x9c, 0x97, 0xc5, 0xab, 0xef, 0xf5, 0x8d, //0x000030e8 .quad -8217398424034108273
	0xb2, 0x7c, 0xb1, 0xa6, 0xf8, 0x8f, 0x30, 0xbd, //0x000030f0 .quad -4814189703547749198
	0xb3, 0x83, 0xfd, 0xb6, 0x96, 0x6b, 0x73, 0xb1, //0x000030f8 .quad -5660062011615247437
	0xde, 0xdb, 0x5d, 0xd0, 0xf6, 0xb3, 0x7c, 0xac, //0x00003100 .quad -6017737129434686498
	0xa0, 0xe4, 0xbc, 0x64, 0x7c, 0x46, 0xd0, 0xdd, //0x00003108 .quad -2463391496091671392
	0x6b, 0xa9, 0x3a, 0x42, 0x7a, 0xf0, 0xcd, 0x6b, //0x00003110 .quad 7768129340171790699
	0xe4, 0x0e, 0xf6, 0xbe, 0x0d, 0x2c, 0xa2, 0x8a, //0x00003118 .quad -8457148712698376476
	0xc6, 0x53, 0xc9, 0xd2, 0x98, 0x6c, 0xc1, 0x86, //0x00003120 .quad -8736582398494813242
	0x9d, 0x92, 0xb3, 0x2e, 0x11, 0xb7, 0x4a, 0xad, //0x00003128 .quad -5959749872445582691
	0xb7, 0xa8, 0x7b, 0x07, 0xbf, 0xc7, 0x71, 0xe8, //0x00003130 .quad -1697355961263740745
	0x44, 0x77, 0x60, 0x7a, 0xd5, 0x64, 0x9d, 0xd8, //0x00003138 .quad -2838001322129590460
	0x72, 0x49, 0xad, 0x64, 0xd7, 0x1c, 0x47, 0x11, //0x00003140 .quad 1244995533423855986
	0x8b, 0x4a, 0x7c, 0x6c, 0x05, 0x5f, 0x62, 0x87, //0x00003148 .quad -8691279853972075893
	0xcf, 0x9b, 0xd8, 0x3d, 0x0d, 0xe4, 0x98, 0xd5, //0x00003150 .quad -3055441601647567921
	0x2d, 0x5d, 0x9b, 0xc7, 0xc6, 0xf6, 0x3a, 0xa9, //0x00003158 .quad -6252413799037706963
	0xc3, 0xc2, 0x4e, 0x8d, 0x10, 0x1d, 0xff, 0x4a, //0x00003160 .quad 5404070034795315907
	0x79, 0x34, 0x82, 0x79, 0x78, 0xb4, 0x89, 0xd3, //0x00003168 .quad -3203831230369745799
	0xba, 0x39, 0x51, 0x58, 0x2a, 0x72, 0xdf, 0xce, //0x00003170 .quad -3539985255894009414
	0xcb, 0x60, 0xf1, 0x4b, 0xcb, 0x10, 0x36, 0x84, //0x00003178 .quad -8919923546622172981
	0x28, 0x88, 0x65, 0xee, 0xb4, 0x4e, 0x97, 0xc2, //0x00003180 .quad -4424981569867511768
	0xfe, 0xb8, 0xed, 0x1e, 0xfe, 0x94, 0x43, 0xa5, //0x00003188 .quad -6538218414850328322
	0x32, 0xea, 0xfe, 0x29, 0x62, 0x22, 0x3d, 0x73, //0x00003190 .quad 8303831092947774002
	0x3e, 0x27, 0xa9, 0xa6, 0x3d, 0x7a, 0x94, 0xce, //0x00003198 .quad -3561087000135522498
	0x5f, 0x52, 0x3f, 0x5a, 0x7d, 0x35, 0x06, 0x08, //0x000031a0 .quad 578208414664970847
	0x87, 0xb8, 0x29, 0x88, 0x66, 0xcc, 0x1c, 0x81, //0x000031a8 .quad -9143208402725783417
	0xf7, 0x26, 0xcf, 0xb0, 0xdc, 0xc2, 0x07, 0xca, //0x000031b0 .quad -3888925500096174345
	0xa8, 0x26, 0x34, 0x2a, 0x80, 0xff, 0x63, 0xa1, //0x000031b8 .quad -6817324484979841368
	0xb5, 0xf0, 0x02, 0xdd, 0x93, 0xb3, 0x89, 0xfc, //0x000031c0 .quad -249470856692830027
	0x52, 0x30, 0xc1, 0x34, 0x60, 0xff, 0xbc, 0xc9, //0x000031c8 .quad -3909969587797413806
	0xe2, 0xac, 0x43, 0xd4, 0x78, 0x20, 0xac, 0xbb, //0x000031d0 .quad -4923524589293425438
	0x67, 0x7c, 0xf1, 0x41, 0x38, 0x3f, 0x2c, 0xfc, //0x000031d8 .quad -275775966319379353
	0x0d, 0x4c, 0xaa, 0x84, 0x4b, 0x94, 0x4b, 0xd5, //0x000031e0 .quad -3077202868308390899
	0xc0, 0xed, 0x36, 0x29, 0x83, 0xa7, 0x9b, 0x9d, //0x000031e8 .quad -7089889006590693952
	0x11, 0xdf, 0xd4, 0x65, 0x5e, 0x79, 0x9e, 0x0a, //0x000031f0 .quad 765182433041899281
	0x31, 0xa9, 0x84, 0xf3, 0x63, 0x91, 0x02, 0xc5, //0x000031f8 .quad -4250675239810979535
	0xd5, 0x16, 0x4a, 0xff, 0xb5, 0x17, 0x46, 0x4d, //0x00003200 .quad 5568164059729762005
	0x7d, 0xd3, 0x65, 0xf0, 0xbc, 0x35, 0x43, 0xf6, //0x00003208 .quad -701658031336336515
	0x45, 0x4e, 0x8e, 0xbf, 0xd1, 0xce, 0x4b, 0x50, //0x00003210 .quad 5785945546544795205
	0x2e, 0xa4, 0x3f, 0x16, 0x96, 0x01, 0xea, 0x99, //0x00003218 .quad -7356065297226292178
	0xd6, 0xe1, 0x71, 0x2f, 0x86, 0xc2, 0x5e, 0xe4, //0x00003220 .quad -1990940103673781802
	0x39, 0x8d, 0xcf, 0x9b, 0xfb, 0x81, 0x64, 0xc0, //0x00003228 .quad -4583395603105477319
	0x4c, 0x5a, 0x4e, 0xbb, 0x27, 0x73, 0x76, 0x5d, //0x00003230 .quad 6734696907262548556
	0x88, 0x70, 0xc3, 0x82, 0x7a, 0xa2, 0x7d, 0xf0, //0x00003238 .quad -1117558485454458744
	0x6f, 0xf8, 0x10, 0xd5, 0xf8, 0x07, 0x6a, 0x3a, //0x00003240 .quad 4209185567039092847
	0x55, 0x26, 0xba, 0x91, 0x8c, 0x85, 0x4e, 0x96, //0x00003248 .quad -7616003081050118571
	0x8b, 0x36, 0x55, 0x0a, 0xf7, 0x89, 0x04, 0x89, //0x00003250 .quad -8573576096483297653
	0xea, 0xaf, 0x28, 0xb6, 0xef, 0x26, 0xe2, 0xbb, //0x00003258 .quad -4908317832885260310
	0x2e, 0x84, 0xea, 0xcc, 0x74, 0xac, 0x45, 0x2b, //0x00003260 .quad 3118087934678041646
	0xe5, 0xdb, 0xb2, 0xa3, 0xab, 0xb0, 0xda, 0xea, //0x00003268 .quad -1523711272679187483
	0x9d, 0x92, 0x12, 0x00, 0xc9, 0x8b, 0x0b, 0x3b, //0x00003270 .quad 4254647968387469981
	0x6f, 0xc9, 0x4f, 0x46, 0x6b, 0xae, 0xc8, 0x92, //0x00003278 .quad -7869848573065574033
	0x44, 0x37, 0x17, 0x40, 0xbb, 0x6e, 0xce, 0x09, //0x00003280 .quad 706623942056949572
	0xcb, 0xbb, 0xe3, 0x17, 0x06, 0xda, 0x7a, 0xb7, //0x00003288 .quad -5225624697904579637
	0x15, 0x05, 0x1d, 0x10, 0x6a, 0x0a, 0x42, 0xcc, //0x00003290 .quad -3728406090856200939
	0xbd, 0xaa, 0xdc, 0x9d, 0x87, 0x90, 0x59, 0xe5, //0x00003298 .quad -1920344853953336643
	0x2d, 0x23, 0x12, 0x4a, 0x82, 0x46, 0xa9, 0x9f, //0x000032a0 .quad -6941939825212513491
	0xb6, 0xea, 0xa9, 0xc2, 0x54, 0xfa, 0x57, 0x8f, //0x000032a8 .quad -8117744561361917258
	0xf9, 0xab, 0x96, 0xdc, 0x22, 0x98, 0x93, 0x47, //0x000032b0 .quad 5157633273766521849
	0x64, 0x65, 0x54, 0xf3, 0xe9, 0xf8, 0x2d, 0xb3, //0x000032b8 .quad -5535494683275008668
	0xf7, 0x56, 0xbc, 0x93, 0x2b, 0x7e, 0x78, 0x59, //0x000032c0 .quad 6447041592208152311
	0xbd, 0x7e, 0x29, 0x70, 0x24, 0x77, 0xf9, 0xdf, //0x000032c8 .quad -2307682335666372931
	0x5a, 0xb6, 0x55, 0x3c, 0xdb, 0x4e, 0xeb, 0x57, //0x000032d0 .quad 6335244004343789146
	0x36, 0xef, 0x19, 0xc6, 0x76, 0xea, 0xfb, 0x8b, //0x000032d8 .quad -8359830487432564938
	0xf1, 0x23, 0x6b, 0x0b, 0x92, 0x22, 0xe6, 0xed, //0x000032e0 .quad -1304317031425039375
	0x03, 0x6b, 0xa0, 0x77, 0x14, 0xe5, 0xfa, 0xae, //0x000032e8 .quad -5838102090863318269
	0xed, 0xec, 0x45, 0x8e, 0x36, 0xab, 0x5f, 0xe9, //0x000032f0 .quad -1630396289281299219
	0xc4, 0x85, 0x88, 0x95, 0x59, 0x9e, 0xb9, 0xda, //0x000032f8 .quad -2685941595151759932
	0x14, 0xb4, 0xeb, 0x18, 0x02, 0xcb, 0xdb, 0x11, //0x00003300 .quad 1286845328412881940
	0x9b, 0x53, 0x75, 0xfd, 0xf7, 0x02, 0xb4, 0x88, //0x00003308 .quad -8596242524610931813
	0x19, 0xa1, 0x26, 0x9f, 0xc2, 0xbd, 0x52, 0xd6, //0x00003310 .quad -3003129357911285479
	0x81, 0xa8, 0xd2, 0xfc, 0xb5, 0x03, 0xe1, 0xaa, //0x00003318 .quad -6133617137336276863
	0x5f, 0x49, 0xf0, 0x46, 0x33, 0x6d, 0xe7, 0x4b, //0x00003320 .quad 5469460339465668959
	0xa2, 0x52, 0x07, 0x7c, 0xa3, 0x44, 0x99, 0xd5, //0x00003328 .quad -3055335403242958174
	0xdb, 0x2d, 0x56, 0x0c, 0x40, 0xa4, 0x70, 0x6f, //0x00003330 .quad 8030098730593431003
	0xa5, 0x93, 0x84, 0x2d, 0xe6, 0xca, 0x7f, 0x85, //0x00003338 .quad -8827113654667930715
	0x52, 0xb9, 0x6b, 0x0f, 0x50, 0xcd, 0x4c, 0xcb, //0x00003340 .quad -3797434642040374958
	0x8e, 0xb8, 0xe5, 0xb8, 0x9f, 0xbd, 0xdf, 0xa6, //0x00003348 .quad -6422206049907525490
	0xa7, 0xa7, 0x46, 0x13, 0xa4, 0x00, 0x20, 0x7e, //0x00003350 .quad 9088264752731695015
	0xb2, 0x26, 0x1f, 0xa7, 0x07, 0xad, 0x97, 0xd0, //0x00003358 .quad -3416071543957018958
	0xc8, 0x28, 0x0c, 0x8c, 0x66, 0x00, 0xd4, 0x8e, //0x00003360 .quad -8154892584824854328
	0x2f, 0x78, 0x73, 0xc8, 0x24, 0xcc, 0x5e, 0x82, //0x00003368 .quad -9052573742614218705
	0xfa, 0x32, 0x0f, 0x2f, 0x80, 0x00, 0x89, 0x72, //0x00003370 .quad 8253128342678483706
	0x3b, 0x56, 0x90, 0xfa, 0x2d, 0x7f, 0xf6, 0xa2, //0x00003378 .quad -6704031159840385477
	0xb9, 0xff, 0xd2, 0x3a, 0xa0, 0x40, 0x2b, 0x4f, //0x00003380 .quad 5704724409920716729
	0xca, 0x6b, 0x34, 0x79, 0xf9, 0x1e, 0xb4, 0xcb, //0x00003388 .quad -3768352931373093942
	0xa8, 0xbf, 0x87, 0x49, 0xc8, 0x10, 0xf6, 0xe2, //0x00003390 .quad -2092466524453879896
	0xbc, 0x86, 0x81, 0xd7, 0xb7, 0x26, 0xa1, 0xfe, //0x00003398 .quad -98755145788979524
	0xc9, 0xd7, 0xf4, 0x2d, 0x7d, 0xca, 0xd9, 0x0d, //0x000033a0 .quad 998051431430019017
	0x36, 0xf4, 0xb0, 0xe6, 0x32, 0xb8, 0x24, 0x9f, //0x000033a8 .quad -6979250993759194058
	0xbb, 0x0d, 0x72, 0x79, 0x1c, 0x3d, 0x50, 0x91, //0x000033b0 .quad -7975807747567252037
	0x43, 0x31, 0x5d, 0xa0, 0x3f, 0xe6, 0xed, 0xc6, //0x000033b8 .quad -4112377723771604669
	0x2a, 0x91, 0xce, 0x97, 0x63, 0x4c, 0xa4, 0x75, //0x000033c0 .quad 8476984389250486570
	0x94, 0x7d, 0x74, 0x88, 0xcf, 0x5f, 0xa9, 0xf8, //0x000033c8 .quad -528786136287117932
	0xba, 0x1a, 0xe1, 0x3e, 0xbe, 0xaf, 0x86, 0xc9, //0x000033d0 .quad -3925256793573221702
	0x7c, 0xce, 0x48, 0xb5, 0xe1, 0xdb, 0x69, 0x9b, //0x000033d8 .quad -7248020362820530564
	0x68, 0x61, 0x99, 0xce, 0xad, 0x5b, 0xe8, 0xfb, //0x000033e0 .quad -294884973539139224
	0x1b, 0x02, 0x9b, 0x22, 0xda, 0x52, 0x44, 0xc2, //0x000033e8 .quad -4448339435098275301
	0xc3, 0xb9, 0x3f, 0x42, 0x99, 0x72, 0xe2, 0xfa, //0x000033f0 .quad -368606216923924029
	0xa2, 0xc2, 0x41, 0xab, 0x90, 0x67, 0xd5, 0xf2, //0x000033f8 .quad -948738275445456222
	0x1a, 0xd4, 0x67, 0xc9, 0x9f, 0x87, 0xcd, 0xdc, //0x00003400 .quad -2536221894791146470
	0xa5, 0x19, 0x09, 0x6b, 0xba, 0x60, 0xc5, 0x97, //0x00003408 .quad -7510490449794491995
	0x20, 0xc9, 0xc1, 0xbb, 0x87, 0xe9, 0x00, 0x54, //0x00003410 .quad 6053094668365842720
	0x0f, 0x60, 0xcb, 0x05, 0xe9, 0xb8, 0xb6, 0xbd, //0x00003418 .quad -4776427043815727089
	0x68, 0x3b, 0xb2, 0xaa, 0xe9, 0x23, 0x01, 0x29, //0x00003420 .quad 2954682317029915496
	0x13, 0x38, 0x3e, 0x47, 0x23, 0x67, 0x24, 0xed, //0x00003428 .quad -1358847786342270957
	0x21, 0x65, 0xaf, 0x0a, 0x72, 0xb6, 0xa0, 0xf9, //0x00003430 .quad -459166561069996767
	0x0b, 0xe3, 0x86, 0x0c, 0x76, 0xc0, 0x36, 0x94, //0x00003438 .quad -7766808894105001205
	0x69, 0x3e, 0x5b, 0x8d, 0x0e, 0xe4, 0x08, 0xf8, //0x00003440 .quad -573958201337495959
	0xce, 0x9b, 0xa8, 0x8f, 0x93, 0x70, 0x44, 0xb9, //0x00003448 .quad -5096825099203863602
	0x04, 0x0e, 0xb2, 0x30, 0x12, 0x1d, 0x0b, 0xb6, //0x00003450 .quad -5329133770099257852
	0xc2, 0xc2, 0x92, 0x73, 0xb8, 0x8c, 0x95, 0xe7, //0x00003458 .quad -1759345355577441598
	0xc2, 0x48, 0x6f, 0x5e, 0x2b, 0xf2, 0xc6, 0xb1, //0x00003460 .quad -5636551615525730110
	0xb9, 0xb9, 0x3b, 0x48, 0xf3, 0x77, 0xbd, 0x90, //0x00003468 .quad -8017119874876982855
	0xf3, 0x1a, 0x0b, 0x36, 0xb6, 0xae, 0x38, 0x1e, //0x00003470 .quad 2177682517447613171
	0x28, 0xa8, 0x4a, 0x1a, 0xf0, 0xd5, 0xec, 0xb4, //0x00003478 .quad -5409713825168840664
	0xb0, 0xe1, 0x8d, 0xc3, 0x63, 0xda, 0xc6, 0x25, //0x00003480 .quad 2722103146809516464
	0x32, 0x52, 0xdd, 0x20, 0x6c, 0x0b, 0x28, 0xe2, //0x00003488 .quad -2150456263033662926
	0x0e, 0xad, 0x38, 0x5a, 0x7e, 0x48, 0x9c, 0x57, //0x00003490 .quad 6313000485183335694
	0x5f, 0x53, 0x8a, 0x94, 0x23, 0x07, 0x59, 0x8d, //0x00003498 .quad -8261564192037121185
	0x51, 0xd8, 0xc6, 0xf0, 0x9d, 0x5a, 0x83, 0x2d, //0x000034a0 .quad 3279564588051781713
	0x37, 0xe8, 0xac, 0x79, 0xec, 0x48, 0xaf, 0xb0, //0x000034a8 .quad -5715269221619013577
	0x65, 0x8e, 0xf8, 0x6c, 0x45, 0x31, 0xe4, 0xf8, //0x000034b0 .quad -512230283362660763
	0x44, 0x22, 0x18, 0x98, 0x27, 0x1b, 0xdb, 0xdc, //0x000034b8 .quad -2532400508596379068
	0xff, 0x58, 0x1b, 0x64, 0xcb, 0x9e, 0x8e, 0x1b, //0x000034c0 .quad 1985699082112030975
	0x6b, 0x15, 0x0f, 0xbf, 0xf8, 0xf0, 0x08, 0x8a, //0x000034c8 .quad -8500279345513818773
	0x3f, 0x2f, 0x22, 0x3d, 0x7e, 0x46, 0x72, 0xe2, //0x000034d0 .quad -2129562165787349185
	0xc5, 0xda, 0xd2, 0xee, 0x36, 0x2d, 0x8b, 0xac, //0x000034d8 .quad -6013663163464885563
	0x0f, 0xbb, 0x6a, 0xcc, 0x1d, 0xd8, 0x0e, 0x5b, //0x000034e0 .quad 6561419329620589327
	0x77, 0x91, 0x87, 0xaa, 0x84, 0xf8, 0xad, 0xd7, //0x000034e8 .quad -2905392935903719049
	0xe9, 0xb4, 0xc2, 0x9f, 0x12, 0x47, 0xe9, 0x98, //0x000034f0 .quad -7428327965055601431
	0xea, 0xba, 0x94, 0xea, 0x52, 0xbb, 0xcc, 0x86, //0x000034f8 .quad -8733399612580906262
	0x24, 0x62, 0xb3, 0x47, 0xd7, 0x98, 0x23, 0x3f, //0x00003500 .quad 4549648098962661924
	0xa5, 0xe9, 0x39, 0xa5, 0x27, 0xea, 0x7f, 0xa8, //0x00003508 .quad -6305063497298744923
	0xad, 0x3a, 0xa0, 0x19, 0x0d, 0x7f, 0xec, 0x8e, //0x00003510 .quad -8147997931578836307
	0x0e, 0x64, 0x88, 0x8e, 0xb1, 0xe4, 0x9f, 0xd2, //0x00003518 .quad -3269643353196043250
	0xac, 0x24, 0x04, 0x30, 0x68, 0xcf, 0x53, 0x19, //0x00003520 .quad 1825030320404309164
	0x89, 0x3e, 0x15, 0xf9, 0xee, 0xee, 0xa3, 0x83, //0x00003528 .quad -8961056123388608887
	0xd7, 0x2d, 0x05, 0x3c, 0x42, 0xc3, 0xa8, 0x5f, //0x00003530 .quad 6892973918932774359
	0x2b, 0x8e, 0x5a, 0xb7, 0xaa, 0xea, 0x8c, 0xa4, //0x00003538 .quad -6589634135808373205
	0x4d, 0x79, 0x06, 0xcb, 0x12, 0xf4, 0x92, 0x37, //0x00003540 .quad 4004531380238580045
	0xb6, 0x31, 0x31, 0x65, 0x55, 0x25, 0xb0, 0xcd, //0x00003548 .quad -3625356651333078602
	0xd0, 0x0b, 0xe4, 0xbe, 0x8b, 0xd8, 0xbb, 0xe2, //0x00003550 .quad -2108853905778275376
	0x11, 0xbf, 0x3e, 0x5f, 0x55, 0x17, 0x8e, 0x80, //0x00003558 .quad -9183376934724255983
	0xc4, 0x0e, 0x9d, 0xae, 0xae, 0xce, 0x6a, 0x5b, //0x00003560 .quad 6587304654631931588
	0xd6, 0x6e, 0x0e, 0xb7, 0x2a, 0x9d, 0xb1, 0xa0, //0x00003568 .quad -6867535149977932074
	0x75, 0x52, 0x44, 0x5a, 0x5a, 0x82, 0x45, 0xf2, //0x00003570 .quad -989241218564861323
	0x8b, 0x0a, 0xd2, 0x64, 0x75, 0x04, 0xde, 0xc8, //0x00003578 .quad -3972732919045027189
	0x12, 0x67, 0xd5, 0xf0, 0xf0, 0xe2, 0xd6, 0xee, //0x00003580 .quad -1236551523206076654
	0x2e, 0x8d, 0x06, 0xbe, 0x92, 0x85, 0x15, 0xfb, //0x00003588 .quad -354230130378896082
	0x6b, 0x60, 0x85, 0x96, 0xd6, 0x4d, 0x46, 0x55, //0x00003590 .quad 6144684325637283947
	0x3d, 0x18, 0xc4, 0xb6, 0x7b, 0x73, 0xed, 0x9c, //0x00003598 .quad -7138922859127891907
	0x86, 0xb8, 0x26, 0x3c, 0x4c, 0xe1, 0x97, 0xaa, //0x000035a0 .quad -6154202648235558778
	0x4c, 0x1e, 0x75, 0xa4, 0x5a, 0xd0, 0x28, 0xc4, //0x000035a8 .quad -4311967555482476980
	0xa8, 0x66, 0x30, 0x4b, 0x9f, 0xd9, 0x3d, 0xd5, //0x000035b0 .quad -3081067291867060568
	0xdf, 0x65, 0x92, 0x4d, 0x71, 0x04, 0x33, 0xf5, //0x000035b8 .quad -778273425925708321
	0x29, 0x40, 0xfe, 0x8e, 0x03, 0xa8, 0x46, 0xe5, //0x000035c0 .quad -1925667057416912855
	0xab, 0x7f, 0x7b, 0xd0, 0xc6, 0xe2, 0x3f, 0x99, //0x000035c8 .quad -7403949918844649557
	0x33, 0xd0, 0xbd, 0x72, 0x04, 0x52, 0x98, 0xde, //0x000035d0 .quad -2407083821771141069
	0x96, 0x5f, 0x9a, 0x84, 0x78, 0xdb, 0x8f, 0xbf, //0x000035d8 .quad -4643251380128424042
	0x40, 0x44, 0x6d, 0x8f, 0x85, 0x66, 0x3e, 0x96, //0x000035e0 .quad -7620540795641314240
	0x7c, 0xf7, 0xc0, 0xa5, 0x56, 0xd2, 0x73, 0xef, //0x000035e8 .quad -1192378206733142148
	0xa8, 0x4a, 0xa4, 0x79, 0x13, 0x00, 0xe7, 0xdd, //0x000035f0 .quad -2456994988062127448
	0xad, 0x9a, 0x98, 0x27, 0x76, 0x63, 0xa8, 0x95, //0x000035f8 .quad -7662765406849295699
	0x52, 0x5d, 0x0d, 0x58, 0x18, 0xc0, 0x60, 0x55, //0x00003600 .quad 6152128301777116498
	0x59, 0xc1, 0x7e, 0xb1, 0x53, 0x7c, 0x12, 0xbb, //0x00003608 .quad -4966770740134231719
	0xa6, 0xb4, 0x10, 0x6e, 0x1e, 0xf0, 0xb8, 0xaa, //0x00003610 .quad -6144897678060768090
	0xaf, 0x71, 0xde, 0x9d, 0x68, 0x1b, 0xd7, 0xe9, //0x00003618 .quad -1596777406740401745
	0xe8, 0x70, 0xca, 0x04, 0x13, 0x96, 0xb3, 0xca, //0x00003620 .quad -3840561048787980056
	0x0d, 0x07, 0xab, 0x62, 0x21, 0x71, 0x26, 0x92, //0x00003628 .quad -7915514906853832947
	0x22, 0x0d, 0xfd, 0xc5, 0x97, 0x7b, 0x60, 0x3d, //0x00003630 .quad 4422670725869800738
	0xd1, 0xc8, 0x55, 0xbb, 0x69, 0x0d, 0xb0, 0xb6, //0x00003638 .quad -5282707615139903279
	0x6a, 0x50, 0x7c, 0xb7, 0x7d, 0x9a, 0xb8, 0x8c, //0x00003640 .quad -8306719647944912790
	0x05, 0x3b, 0x2b, 0x2a, 0xc4, 0x10, 0x5c, 0xe4, //0x00003648 .quad -1991698500497491195
	0x42, 0xb2, 0xad, 0x92, 0x8e, 0x60, 0xf3, 0x77, //0x00003650 .quad 8643358275316593218
	0xe3, 0x04, 0x5b, 0x9a, 0x7a, 0x8a, 0xb9, 0x8e, //0x00003658 .quad -8162340590452013853
	0xd3, 0x1e, 0x59, 0x37, 0xb2, 0x38, 0xf0, 0x55, //0x00003660 .quad 6192511825718353619
	0x1c, 0xc6, 0xf1, 0x40, 0x19, 0xed, 0x67, 0xb2, //0x00003668 .quad -5591239719637629412
	0x88, 0x66, 0x2f, 0xc5, 0xde, 0x46, 0x6c, 0x6b, //0x00003670 .quad 7740639782147942024
	0xa3, 0x37, 0x2e, 0x91, 0x5f, 0xe8, 0x01, 0xdf, //0x00003678 .quad -2377363631119648861
	0x15, 0xa0, 0x3d, 0x3b, 0x4b, 0xac, 0x23, 0x23, //0x00003680 .quad 2532056854628769813
	0xc6, 0xe2, 0xbc, 0xba, 0x3b, 0x31, 0x61, 0x8b, //0x00003688 .quad -8403381297090862394
	0x1a, 0x08, 0x0d, 0x0a, 0x5e, 0x97, 0xec, 0xab, //0x00003690 .quad -6058300968568813542
	0x77, 0x1b, 0x6c, 0xa9, 0x8a, 0x7d, 0x39, 0xae, //0x00003698 .quad -5892540602936190089
	0x21, 0x4a, 0x90, 0x8c, 0x35, 0xbd, 0xe7, 0x96, //0x000036a0 .quad -7572876210711016927
	0x55, 0x22, 0xc7, 0x53, 0xed, 0xdc, 0xc7, 0xd9, //0x000036a8 .quad -2753989735242849707
	0x54, 0x2e, 0xda, 0x77, 0x41, 0xd6, 0x50, 0x7e, //0x000036b0 .quad 9102010423587778132
	0x75, 0x75, 0x5c, 0x54, 0x14, 0xea, 0x1c, 0x88, //0x000036b8 .quad -8638772612167862923
	0xe9, 0xb9, 0xd0, 0xd5, 0xd1, 0x0b, 0xe5, 0xdd, //0x000036c0 .quad -2457545025797441047
	0xd2, 0x92, 0x73, 0x69, 0x99, 0x24, 0x24, 0xaa, //0x000036c8 .quad -6186779746782440750
	0x64, 0xe8, 0x44, 0x4b, 0xc6, 0x4e, 0x5e, 0x95, //0x000036d0 .quad -7683617300674189212
	0x87, 0x77, 0xd0, 0xc3, 0xbf, 0x2d, 0xad, 0xd4, //0x000036d8 .quad -3121788665050663033
	0x3e, 0x11, 0x0b, 0xef, 0x3b, 0xf1, 0x5a, 0xbd, //0x000036e0 .quad -4802260812921368258
	0xb4, 0x4a, 0x62, 0xda, 0x97, 0x3c, 0xec, 0x84, //0x000036e8 .quad -8868646943297746252
	0x8e, 0xd5, 0xcd, 0xea, 0x8a, 0xad, 0xb1, 0xec, //0x000036f0 .quad -1391139997724322418
	0x61, 0xdd, 0xfa, 0xd0, 0xbd, 0x4b, 0x27, 0xa6, //0x000036f8 .quad -6474122660694794911
	0xf2, 0x4a, 0x81, 0xa5, 0xed, 0x18, 0xde, 0x67, //0x00003700 .quad 7484447039699372786
	0xba, 0x94, 0x39, 0x45, 0xad, 0x1e, 0xb1, 0xcf, //0x00003708 .quad -3480967307441105734
	0xd7, 0xce, 0x70, 0x87, 0x94, 0xcf, 0xea, 0x80, //0x00003710 .quad -9157278655470055721
	0xf4, 0xfc, 0x43, 0x4b, 0x2c, 0xb3, 0xce, 0x81, //0x00003718 .quad -9093133594791772940
	0x8d, 0x02, 0x4d, 0xa9, 0x79, 0x83, 0x25, 0xa1, //0x00003720 .quad -6834912300910181747
	0x31, 0xfc, 0x14, 0x5e, 0xf7, 0x5f, 0x42, 0xa2, //0x00003728 .quad -6754730975062328271
	0x30, 0x43, 0xa0, 0x13, 0x58, 0xe4, 0x6e, 0x09, //0x00003730 .quad 679731660717048624
	0x3e, 0x3b, 0x9a, 0x35, 0xf5, 0xf7, 0xd2, 0xca, //0x00003738 .quad -3831727700400522434
	0xfc, 0x53, 0x88, 0x18, 0x6e, 0x9d, 0xca, 0x8b, //0x00003740 .quad -8373707460958465028
	0x0d, 0xca, 0x00, 0x83, 0xf2, 0xb5, 0x87, 0xfd, //0x00003748 .quad -177973607073265139
	0x7d, 0x34, 0x55, 0xcf, 0x64, 0xa2, 0x5e, 0x77, //0x00003750 .quad 8601490892183123069
	0x48, 0x7e, 0xe0, 0x91, 0xb7, 0xd1, 0x74, 0x9e, //0x00003758 .quad -7028762532061872568
	0x9d, 0x81, 0x2a, 0x03, 0xfe, 0x4a, 0x36, 0x95, //0x00003760 .quad -7694880458480647779
	0xda, 0x9d, 0x58, 0x76, 0x25, 0x06, 0x12, 0xc6, //0x00003768 .quad -4174267146649952806
	0x04, 0x22, 0xf5, 0x83, 0xbd, 0xdd, 0x83, 0x3a, //0x00003770 .quad 4216457482181353988
	0x51, 0xc5, 0xee, 0xd3, 0xae, 0x87, 0x96, 0xf7, //0x00003778 .quad -606147914885053103
	0x42, 0x35, 0x79, 0x72, 0x96, 0x6a, 0x92, 0xc4, //0x00003780 .quad -4282243101277735614
	0x52, 0x3b, 0x75, 0x44, 0xcd, 0x14, 0xbe, 0x9a, //0x00003788 .quad -7296371474444240046
	0x93, 0x82, 0x17, 0x0f, 0x3c, 0x05, 0xb7, 0x75, //0x00003790 .quad 8482254178684994195
	0x27, 0x8a, 0x92, 0x95, 0x00, 0x9a, 0x6d, 0xc1, //0x00003798 .quad -4508778324627912153
	0x38, 0x63, 0xdd, 0x12, 0x8b, 0xc6, 0x24, 0x53, //0x000037a0 .quad 5991131704928854840
	0xb1, 0x2c, 0xf7, 0xba, 0x80, 0x00, 0xc9, 0xf1, //0x000037a8 .quad -1024286887357502287
	0x03, 0x5e, 0xca, 0xeb, 0x16, 0xfc, 0xf6, 0xd3, //0x000037b0 .quad -3173071712060547581
	0xee, 0x7b, 0xda, 0x74, 0x50, 0xa0, 0x1d, 0x97, //0x000037b8 .quad -7557708332239520786
	0x84, 0xf5, 0xbc, 0xa6, 0x1c, 0xbb, 0xf4, 0x88, //0x000037c0 .quad -8578025658503072380
	0xea, 0x1a, 0x11, 0x92, 0x64, 0x08, 0xe5, 0xbc, //0x000037c8 .quad -4835449396872013078
	0xe5, 0x32, 0x6c, 0xd0, 0xe3, 0xe9, 0x31, 0x2b, //0x000037d0 .quad 3112525982153323237
	0xa5, 0x61, 0x95, 0xb6, 0x7d, 0x4a, 0x1e, 0xec, //0x000037d8 .quad -1432625727662628443
	0xcf, 0x9f, 0x43, 0x62, 0x2e, 0x32, 0xff, 0x3a, //0x000037e0 .quad 4251171748059520975
	0x07, 0x5d, 0x1d, 0x92, 0x8e, 0xee, 0x92, 0x93, //0x000037e8 .quad -7812920107430224633
	0xc2, 0x87, 0xd4, 0xfa, 0xb9, 0xfe, 0xbe, 0x09, //0x000037f0 .quad 702278666647013314
	0x49, 0xb4, 0xa4, 0x36, 0x32, 0xaa, 0x77, 0xb8, //0x000037f8 .quad -5154464115860392887
	0xb3, 0xa9, 0x89, 0x79, 0x68, 0xbe, 0x2e, 0x4c, //0x00003800 .quad 5489534351736154547
	0x5b, 0xe1, 0x4d, 0xc4, 0xbe, 0x94, 0x95, 0xe6, //0x00003808 .quad -1831394126398103205
	0x10, 0x0a, 0xf6, 0x4b, 0x01, 0x37, 0x9d, 0x0f, //0x00003810 .quad 1125115960621402640
	0xd9, 0xac, 0xb0, 0x3a, 0xf7, 0x7c, 0x1d, 0x90, //0x00003818 .quad -8062150356639896359
	0x94, 0x8c, 0xf3, 0x9e, 0xc1, 0x84, 0x84, 0x53, //0x00003820 .quad 6018080969204141204
	0x0f, 0xd8, 0x5c, 0x09, 0x35, 0xdc, 0x24, 0xb4, //0x00003828 .quad -5466001927372482545
	0xb9, 0x6f, 0xb0, 0x06, 0xf2, 0xa5, 0x65, 0x28, //0x00003830 .quad 2910915193077788601
	0x13, 0x0e, 0xb4, 0x4b, 0x42, 0x13, 0x2e, 0xe1, //0x00003838 .quad -2220816390788215277
	0xd3, 0x45, 0x2e, 0x44, 0xb7, 0x87, 0x3f, 0xf9, //0x00003840 .quad -486521013540076077
	0xcb, 0x88, 0x50, 0x6f, 0x09, 0xcc, 0xbc, 0x8c, //0x00003848 .quad -8305539271883716405
	0x48, 0xd7, 0x39, 0x15, 0xa5, 0x69, 0x8f, 0xf7, //0x00003850 .quad -608151266925095096
	0xfe, 0xaa, 0x24, 0xcb, 0x0b, 0xff, 0xeb, 0xaf, //0x00003858 .quad -5770238071427257602
	0x1b, 0x4d, 0x88, 0x5a, 0x0e, 0x44, 0x73, 0xb5, //0x00003860 .quad -5371875102083756773
	0xbe, 0xd5, 0xed, 0xbd, 0xce, 0xfe, 0xe6, 0xdb, //0x00003868 .quad -2601111570856684098
	0x30, 0x30, 0x95, 0xf8, 0x88, 0x0a, 0x68, 0x31, //0x00003870 .quad 3560107088838733872
	0x97, 0xa5, 0xb4, 0x36, 0x41, 0x5f, 0x70, 0x89, //0x00003878 .quad -8543223759426509417
	0x3d, 0x7c, 0xba, 0x36, 0x2b, 0x0d, 0xc2, 0xfd, //0x00003880 .quad -161552157378970563
	0xfc, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00003888 .quad -6067343680855748868
	0x4c, 0x1b, 0x69, 0x04, 0x76, 0x90, 0x32, 0x3d, //0x00003890 .quad 4409745821703674700
	0xbc, 0x42, 0x7a, 0xe5, 0xd5, 0x94, 0xbf, 0xd6, //0x00003898 .quad -2972493582642298180
	0x0f, 0xb1, 0xc1, 0xc2, 0x49, 0x9a, 0x3f, 0xa6, //0x000038a0 .quad -6467280898289979121
	0xb5, 0x69, 0x6c, 0xaf, 0x05, 0xbd, 0x37, 0x86, //0x000038a8 .quad -8775337516792518219
	0x53, 0x1d, 0x72, 0x33, 0xdc, 0x80, 0xcf, 0x0f, //0x000038b0 .quad 1139270913992301907
	0x23, 0x84, 0x47, 0x1b, 0x47, 0xac, 0xc5, 0xa7, //0x000038b8 .quad -6357485877563259869
	0xa8, 0xa4, 0x4e, 0x40, 0x13, 0x61, 0xc3, 0xd3, //0x000038c0 .quad -3187597375937010520
	0x2b, 0x65, 0x19, 0xe2, 0x58, 0x17, 0xb7, 0xd1, //0x000038c8 .quad -3335171328526686933
	0xe9, 0x26, 0x31, 0x08, 0xac, 0x1c, 0x5a, 0x64, //0x000038d0 .quad 7231123676894144233
	0x3b, 0xdf, 0x4f, 0x8d, 0x97, 0x6e, 0x12, 0x83, //0x000038d8 .quad -9002011107970261189
	0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, 0x70, 0x3d, //0x000038e0 .quad 4427218577690292387
	0x0a, 0xd7, 0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, //0x000038e8 .quad -6640827866535438582
	0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x000038f0 QUAD $0xcccccccccccccccc; QUAD $0xcccccccccccccccc  // .space 16, '\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003900 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00003908 .quad -9223372036854775808
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003910 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, //0x00003918 .quad -6917529027641081856
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003920 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, //0x00003928 .quad -4035225266123964416
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003930 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00003938 .quad -432345564227567616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003940 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x9c, //0x00003948 .quad -7187745005283311616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003950 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xc3, //0x00003958 .quad -4372995238176751616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003960 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xf4, //0x00003968 .quad -854558029293551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003970 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x96, 0x98, //0x00003978 .quad -7451627795949551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003980 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xbc, 0xbe, //0x00003988 .quad -4702848726509551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003990 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6b, 0xee, //0x00003998 .quad -1266874889709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000039a0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x02, 0x95, //0x000039a8 .quad -7709325833709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000039b0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x40, 0xb7, 0x43, 0xba, //0x000039b8 .quad -5024971273709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000039c0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x10, 0xa5, 0xd4, 0xe8, //0x000039c8 .quad -1669528073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000039d0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x2a, 0xe7, 0x84, 0x91, //0x000039d8 .quad -7960984073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000039e0 .quad 0
	0x00, 0x00, 0x00, 0x80, 0xf4, 0x20, 0xe6, 0xb5, //0x000039e8 .quad -5339544073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000039f0 .quad 0
	0x00, 0x00, 0x00, 0xa0, 0x31, 0xa9, 0x5f, 0xe3, //0x000039f8 .quad -2062744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a00 .quad 0
	0x00, 0x00, 0x00, 0x04, 0xbf, 0xc9, 0x1b, 0x8e, //0x00003a08 .quad -8206744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a10 .quad 0
	0x00, 0x00, 0x00, 0xc5, 0x2e, 0xbc, 0xa2, 0xb1, //0x00003a18 .quad -5646744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a20 .quad 0
	0x00, 0x00, 0x40, 0x76, 0x3a, 0x6b, 0x0b, 0xde, //0x00003a28 .quad -2446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a30 .quad 0
	0x00, 0x00, 0xe8, 0x89, 0x04, 0x23, 0xc7, 0x8a, //0x00003a38 .quad -8446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a40 .quad 0
	0x00, 0x00, 0x62, 0xac, 0xc5, 0xeb, 0x78, 0xad, //0x00003a48 .quad -5946744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a50 .quad 0
	0x00, 0x80, 0x7a, 0x17, 0xb7, 0x26, 0xd7, 0xd8, //0x00003a58 .quad -2821744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a60 .quad 0
	0x00, 0x90, 0xac, 0x6e, 0x32, 0x78, 0x86, 0x87, //0x00003a68 .quad -8681119073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a70 .quad 0
	0x00, 0xb4, 0x57, 0x0a, 0x3f, 0x16, 0x68, 0xa9, //0x00003a78 .quad -6239712823709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a80 .quad 0
	0x00, 0xa1, 0xed, 0xcc, 0xce, 0x1b, 0xc2, 0xd3, //0x00003a88 .quad -3187955011209551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003a90 .quad 0
	0xa0, 0x84, 0x14, 0x40, 0x61, 0x51, 0x59, 0x84, //0x00003a98 .quad -8910000909647051616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003aa0 .quad 0
	0xc8, 0xa5, 0x19, 0x90, 0xb9, 0xa5, 0x6f, 0xa5, //0x00003aa8 .quad -6525815118631426616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003ab0 .quad 0
	0x3a, 0x0f, 0x20, 0xf4, 0x27, 0x8f, 0xcb, 0xce, //0x00003ab8 .quad -3545582879861895366
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, //0x00003ac0 .quad 4611686018427387904
	0x84, 0x09, 0x94, 0xf8, 0x78, 0x39, 0x3f, 0x81, //0x00003ac8 .quad -9133518327554766460
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, //0x00003ad0 .quad 5764607523034234880
	0xe5, 0x0b, 0xb9, 0x36, 0xd7, 0x07, 0x8f, 0xa1, //0x00003ad8 .quad -6805211891016070171
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa4, //0x00003ae0 .quad -6629298651489370112
	0xde, 0x4e, 0x67, 0x04, 0xcd, 0xc9, 0xf2, 0xc9, //0x00003ae8 .quad -3894828845342699810
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4d, //0x00003af0 .quad 5548434740920451072
	0x96, 0x22, 0x81, 0x45, 0x40, 0x7c, 0x6f, 0xfc, //0x00003af8 .quad -256850038250986858
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xf0, //0x00003b00 .quad -1143914305352105984
	0x9d, 0xb5, 0x70, 0x2b, 0xa8, 0xad, 0xc5, 0x9d, //0x00003b08 .quad -7078060301547948643
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6c, //0x00003b10 .quad 7793479155164643328
	0x05, 0xe3, 0x4c, 0x36, 0x12, 0x19, 0x37, 0xc5, //0x00003b18 .quad -4235889358507547899
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0xc7, //0x00003b20 .quad -4093209111326359552
	0xc6, 0x1b, 0xe0, 0xc3, 0x56, 0xdf, 0x84, 0xf6, //0x00003b28 .quad -683175679707046970
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x7f, 0x3c, //0x00003b30 .quad 4359273333062107136
	0x5c, 0x11, 0x6c, 0x3a, 0x96, 0x0b, 0x13, 0x9a, //0x00003b38 .quad -7344513827457986212
	0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x9f, 0x4b, //0x00003b40 .quad 5449091666327633920
	0xb3, 0x15, 0x07, 0xc9, 0x7b, 0xce, 0x97, 0xc0, //0x00003b48 .quad -4568956265895094861
	0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x86, 0x1e, //0x00003b50 .quad 2199678564482154496
	0x20, 0xdb, 0x48, 0xbb, 0x1a, 0xc2, 0xbd, 0xf0, //0x00003b58 .quad -1099509313941480672
	0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x14, 0x13, //0x00003b60 .quad 1374799102801346560
	0xf4, 0x88, 0x0d, 0xb5, 0x50, 0x99, 0x76, 0x96, //0x00003b68 .quad -7604722348854507276
	0x00, 0x00, 0x00, 0x00, 0xa0, 0x55, 0xd9, 0x17, //0x00003b70 .quad 1718498878501683200
	0x31, 0xeb, 0x50, 0xe2, 0xa4, 0x3f, 0x14, 0xbc, //0x00003b78 .quad -4894216917640746191
	0x00, 0x00, 0x00, 0x00, 0x08, 0xab, 0xcf, 0x5d, //0x00003b80 .quad 6759809616554491904
	0xfd, 0x25, 0xe5, 0x1a, 0x8e, 0x4f, 0x19, 0xeb, //0x00003b88 .quad -1506085128623544835
	0x00, 0x00, 0x00, 0x00, 0xe5, 0xca, 0xa1, 0x5a, //0x00003b90 .quad 6530724019560251392
	0xbe, 0x37, 0xcf, 0xd0, 0xb8, 0xd1, 0xef, 0x92, //0x00003b98 .quad -7858832233030797378
	0x00, 0x00, 0x00, 0x40, 0x9e, 0x3d, 0x4a, 0xf1, //0x00003ba0 .quad -1059967012404461568
	0xad, 0x05, 0x03, 0x05, 0x27, 0xc6, 0xab, 0xb7, //0x00003ba8 .quad -5211854272861108819
	0x00, 0x00, 0x00, 0xd0, 0x05, 0xcd, 0x9c, 0x6d, //0x00003bb0 .quad 7898413271349198848
	0x19, 0xc7, 0x43, 0xc6, 0xb0, 0xb7, 0x96, 0xe5, //0x00003bb8 .quad -1903131822648998119
	0x00, 0x00, 0x00, 0xa2, 0x23, 0x00, 0x82, 0xe4, //0x00003bc0 .quad -1981020733047832576
	0x6f, 0x5c, 0xea, 0x7b, 0xce, 0x32, 0x7e, 0x8f, //0x00003bc8 .quad -8106986416796705681
	0x00, 0x00, 0x80, 0x8a, 0x2c, 0x80, 0xa2, 0xdd, //0x00003bd0 .quad -2476275916309790720
	0x8b, 0xf3, 0xe4, 0x1a, 0x82, 0xbf, 0x5d, 0xb3, //0x00003bd8 .quad -5522047002568494197
	0x00, 0x00, 0x20, 0xad, 0x37, 0x20, 0x0b, 0xd5, //0x00003be0 .quad -3095344895387238400
	0x6e, 0x30, 0x9e, 0xa1, 0x62, 0x2f, 0x35, 0xe0, //0x00003be8 .quad -2290872734783229842
	0x00, 0x00, 0x34, 0xcc, 0x22, 0xf4, 0x26, 0x45, //0x00003bf0 .quad 4982938468024057856
	0x45, 0xde, 0x02, 0xa5, 0x9d, 0x3d, 0x21, 0x8c, //0x00003bf8 .quad -8349324486880600507
	0x00, 0x00, 0x41, 0x7f, 0x2b, 0xb1, 0x70, 0x96, //0x00003c00 .quad -7606384970252091392
	0xd6, 0x95, 0x43, 0x0e, 0x05, 0x8d, 0x29, 0xaf, //0x00003c08 .quad -5824969590173362730
	0x00, 0x40, 0x11, 0x5f, 0x76, 0xdd, 0x0c, 0x3c, //0x00003c10 .quad 4327076842467049472
	0x4c, 0x7b, 0xd4, 0x51, 0x46, 0xf0, 0xf3, 0xda, //0x00003c18 .quad -2669525969289315508
	0x00, 0xc8, 0x6a, 0xfb, 0x69, 0x0a, 0x88, 0xa5, //0x00003c20 .quad -6518949010312869888
	0x0f, 0xcd, 0x24, 0xf3, 0x2b, 0x76, 0xd8, 0x88, //0x00003c28 .quad -8585982758446904049
	0x00, 0x7a, 0x45, 0x7a, 0x04, 0x0d, 0xea, 0x8e, //0x00003c30 .quad -8148686262891087360
	0x53, 0x00, 0xee, 0xef, 0xb6, 0x93, 0x0e, 0xab, //0x00003c38 .quad -6120792429631242157
	0x80, 0xd8, 0xd6, 0x98, 0x45, 0x90, 0xa4, 0x72, //0x00003c40 .quad 8260886245095692416
	0x68, 0x80, 0xe9, 0xab, 0xa4, 0x38, 0xd2, 0xd5, //0x00003c48 .quad -3039304518611664792
	0x50, 0x47, 0x86, 0x7f, 0x2b, 0xda, 0xa6, 0x47, //0x00003c50 .quad 5163053903184807760
	0x41, 0xf0, 0x71, 0xeb, 0x66, 0x63, 0xa3, 0x85, //0x00003c58 .quad -8817094351773372351
	0x24, 0xd9, 0x67, 0x5f, 0xb6, 0x90, 0x90, 0x99, //0x00003c60 .quad -7381240676301154012
	0x51, 0x6c, 0x4e, 0xa6, 0x40, 0x3c, 0x0c, 0xa7, //0x00003c68 .quad -6409681921289327535
	0x6d, 0xcf, 0x41, 0xf7, 0xe3, 0xb4, 0xf4, 0xff, //0x00003c70 .quad -3178808521666707
	0x65, 0x07, 0xe2, 0xcf, 0x50, 0x4b, 0xcf, 0xd0, //0x00003c78 .quad -3400416383184271515
	0xa4, 0x21, 0x89, 0x7a, 0x0e, 0xf1, 0xf8, 0xbf, //0x00003c80 .quad -4613672773753429596
	0x9f, 0x44, 0xed, 0x81, 0x12, 0x8f, 0x81, 0x82, //0x00003c88 .quad -9042789267131251553
	0x0d, 0x6a, 0x2b, 0x19, 0x52, 0x2d, 0xf7, 0xaf, //0x00003c90 .quad -5767090967191786995
	0xc7, 0x95, 0x68, 0x22, 0xd7, 0xf2, 0x21, 0xa3, //0x00003c98 .quad -6691800565486676537
	0x90, 0x44, 0x76, 0x9f, 0xa6, 0xf8, 0xf4, 0x9b, //0x00003ca0 .quad -7208863708989733744
	0x39, 0xbb, 0x02, 0xeb, 0x8c, 0x6f, 0xea, 0xcb, //0x00003ca8 .quad -3753064688430957767
	0xb4, 0xd5, 0x53, 0x47, 0xd0, 0x36, 0xf2, 0x02, //0x00003cb0 .quad 212292400617608628
	0x08, 0x6a, 0xc3, 0x25, 0x70, 0x0b, 0xe5, 0xfe, //0x00003cb8 .quad -79644842111309304
	0x90, 0x65, 0x94, 0x2c, 0x42, 0x62, 0xd7, 0x01, //0x00003cc0 .quad 132682750386005392
	0x45, 0x22, 0x9a, 0x17, 0x26, 0x27, 0x4f, 0x9f, //0x00003cc8 .quad -6967307053960650171
	0xf5, 0x7e, 0xb9, 0xb7, 0xd2, 0x3a, 0x4d, 0x42, //0x00003cd0 .quad 4777539456409894645
	0xd6, 0xaa, 0x80, 0x9d, 0xef, 0xf0, 0x22, 0xc7, //0x00003cd8 .quad -4097447799023424810
	0xb2, 0xde, 0xa7, 0x65, 0x87, 0x89, 0xe0, 0xd2, //0x00003ce0 .quad -3251447716342407502
	0x8b, 0xd5, 0xe0, 0x84, 0x2b, 0xad, 0xeb, 0xf8, //0x00003ce8 .quad -510123730351893109
	0x2f, 0xeb, 0x88, 0x9f, 0xf4, 0x55, 0xcc, 0x63, //0x00003cf0 .quad 7191217214140771119
	0x77, 0x85, 0x0c, 0x33, 0x3b, 0x4c, 0x93, 0x9b, //0x00003cf8 .quad -7236356359111015049
	0xfb, 0x25, 0x6b, 0xc7, 0x71, 0x6b, 0xbf, 0x3c, //0x00003d00 .quad 4377335499248575995
	0xd5, 0xa6, 0xcf, 0xff, 0x49, 0x1f, 0x78, 0xc2, //0x00003d08 .quad -4433759430461380907
	0x7a, 0xef, 0x45, 0x39, 0x4e, 0x46, 0xef, 0x8b, //0x00003d10 .quad -8363388681221443718
	0x8a, 0x90, 0xc3, 0x7f, 0x1c, 0x27, 0x16, 0xf3, //0x00003d18 .quad -930513269649338230
	0xac, 0xb5, 0xcb, 0xe3, 0xf0, 0x8b, 0x75, 0x97, //0x00003d20 .quad -7532960934977096276
	0x56, 0x3a, 0xda, 0xcf, 0x71, 0xd8, 0xed, 0x97, //0x00003d28 .quad -7499099821171918250
	0x17, 0xa3, 0xbe, 0x1c, 0xed, 0xee, 0x52, 0x3d, //0x00003d30 .quad 4418856886560793367
	0xec, 0xc8, 0xd0, 0x43, 0x8e, 0x4e, 0xe9, 0xbd, //0x00003d38 .quad -4762188758037509908
	0xdd, 0x4b, 0xee, 0x63, 0xa8, 0xaa, 0xa7, 0x4c, //0x00003d40 .quad 5523571108200991709
	0x27, 0xfb, 0xc4, 0xd4, 0x31, 0xa2, 0x63, 0xed, //0x00003d48 .quad -1341049929119499481
	0x6a, 0xef, 0x74, 0x3e, 0xa9, 0xca, 0xe8, 0x8f, //0x00003d50 .quad -8076983103442849942
	0xf8, 0x1c, 0xfb, 0x24, 0x5f, 0x45, 0x5e, 0x94, //0x00003d58 .quad -7755685233340769032
	0x44, 0x2b, 0x12, 0x8e, 0x53, 0xfd, 0xe2, 0xb3, //0x00003d60 .quad -5484542860876174524
	0x36, 0xe4, 0x39, 0xee, 0xb6, 0xd6, 0x75, 0xb9, //0x00003d68 .quad -5082920523248573386
	0x16, 0xb6, 0x96, 0x71, 0xa8, 0xbc, 0xdb, 0x60, //0x00003d70 .quad 6979379479186945558
	0x44, 0x5d, 0xc8, 0xa9, 0x64, 0x4c, 0xd3, 0xe7, //0x00003d78 .quad -1741964635633328828
	0xcd, 0x31, 0xfe, 0x46, 0xe9, 0x55, 0x89, 0xbc, //0x00003d80 .quad -4861259862362934835
	0x4a, 0x3a, 0x1d, 0xea, 0xbe, 0x0f, 0xe4, 0x90, //0x00003d88 .quad -8006256924911912374
	0x41, 0xbe, 0xbd, 0x98, 0x63, 0xab, 0xab, 0x6b, //0x00003d90 .quad 7758483227328495169
	0xdd, 0x88, 0xa4, 0xa4, 0xae, 0x13, 0x1d, 0xb5, //0x00003d98 .quad -5396135137712502563
	0xd1, 0x2d, 0xed, 0x7e, 0x3c, 0x96, 0x96, 0xc6, //0x00003da0 .quad -4136954021121544751
	0x14, 0xab, 0xcd, 0x4d, 0x9a, 0x58, 0x64, 0xe2, //0x00003da8 .quad -2133482903713240300
	0xa2, 0x3c, 0x54, 0xcf, 0xe5, 0x1d, 0x1e, 0xfc, //0x00003db0 .quad -279753253987271518
	0xec, 0x8a, 0xa0, 0x70, 0x60, 0xb7, 0x7e, 0x8d, //0x00003db8 .quad -8250955842461857044
	0xcb, 0x4b, 0x29, 0x43, 0x5f, 0xa5, 0x25, 0x3b, //0x00003dc0 .quad 4261994450943298507
	0xa8, 0xad, 0xc8, 0x8c, 0x38, 0x65, 0xde, 0xb0, //0x00003dc8 .quad -5702008784649933400
	0xbe, 0x9e, 0xf3, 0x13, 0xb7, 0x0e, 0xef, 0x49, //0x00003dd0 .quad 5327493063679123134
	0x12, 0xd9, 0xfa, 0xaf, 0x86, 0xfe, 0x15, 0xdd, //0x00003dd8 .quad -2515824962385028846
	0x37, 0x43, 0x78, 0x6c, 0x32, 0x69, 0x35, 0x6e, //0x00003de0 .quad 7941369183226839863
	0xab, 0xc7, 0xfc, 0x2d, 0x14, 0xbf, 0x2d, 0x8a, //0x00003de8 .quad -8489919629131724885
	0x04, 0x54, 0x96, 0x07, 0x7f, 0xc3, 0xc2, 0x49, //0x00003df0 .quad 5315025460606161924
	0x96, 0xf9, 0x7b, 0x39, 0xd9, 0x2e, 0xb9, 0xac, //0x00003df8 .quad -6000713517987268202
	0x06, 0xe9, 0x7b, 0xc9, 0x5e, 0x74, 0x33, 0xdc, //0x00003e00 .quad -2579590211097073402
	0xfb, 0xf7, 0xda, 0x87, 0x8f, 0x7a, 0xe7, 0xd7, //0x00003e08 .quad -2889205879056697349
	0xa3, 0x71, 0xed, 0x3d, 0xbb, 0x28, 0xa0, 0x69, //0x00003e10 .quad 7611128154919104931
	0xfd, 0xda, 0xe8, 0xb4, 0x99, 0xac, 0xf0, 0x86, //0x00003e18 .quad -8723282702051517699
	0x0c, 0xce, 0x68, 0x0d, 0xea, 0x32, 0x08, 0xc4, //0x00003e20 .quad -4321147861633282548
	0xbc, 0x11, 0x23, 0x22, 0xc0, 0xd7, 0xac, 0xa8, //0x00003e28 .quad -6292417359137009220
	0x90, 0x01, 0xc3, 0x90, 0xa4, 0x3f, 0x0a, 0xf5, //0x00003e30 .quad -789748808614215280
	0x2b, 0xd6, 0xab, 0x2a, 0xb0, 0x0d, 0xd8, 0xd2, //0x00003e38 .quad -3253835680493873621
	0xfa, 0xe0, 0x79, 0xda, 0xc6, 0x67, 0x26, 0x79, //0x00003e40 .quad 8729779031470891258
	0xdb, 0x65, 0xab, 0x1a, 0x8e, 0x08, 0xc7, 0x83, //0x00003e48 .quad -8951176327949752869
	0x38, 0x59, 0x18, 0x91, 0xb8, 0x01, 0x70, 0x57, //0x00003e50 .quad 6300537770911226168
	0x52, 0x3f, 0x56, 0xa1, 0xb1, 0xca, 0xb8, 0xa4, //0x00003e58 .quad -6577284391509803182
	0x86, 0x6f, 0x5e, 0xb5, 0x26, 0x02, 0x4c, 0xed, //0x00003e60 .quad -1347699823215743098
	0x26, 0xcf, 0xab, 0x09, 0x5e, 0xfd, 0xe6, 0xcd, //0x00003e68 .quad -3609919470959866074
	0xb4, 0x05, 0x5b, 0x31, 0x58, 0x81, 0x4f, 0x54, //0x00003e70 .quad 6075216638131242420
	0x78, 0x61, 0x0b, 0xc6, 0x5a, 0x5e, 0xb0, 0x80, //0x00003e78 .quad -9173728696990998152
	0x21, 0xc7, 0xb1, 0x3d, 0xae, 0x61, 0x63, 0x69, //0x00003e80 .quad 7594020797664053025
	0xd6, 0x39, 0x8e, 0x77, 0xf1, 0x75, 0xdc, 0xa0, //0x00003e88 .quad -6855474852811359786
	0xe9, 0x38, 0x1e, 0xcd, 0x19, 0x3a, 0xbc, 0x03, //0x00003e90 .quad 269153960225290473
	0x4c, 0xc8, 0x71, 0xd5, 0x6d, 0x93, 0x13, 0xc9, //0x00003e98 .quad -3957657547586811828
	0x23, 0xc7, 0x65, 0x40, 0xa0, 0x48, 0xab, 0x04, //0x00003ea0 .quad 336442450281613091
	0x5f, 0x3a, 0xce, 0x4a, 0x49, 0x78, 0x58, 0xfb, //0x00003ea8 .quad -335385916056126881
	0x76, 0x9c, 0x3f, 0x28, 0x64, 0x0d, 0xeb, 0x62, //0x00003eb0 .quad 7127805559067090038
	0x7b, 0xe4, 0xc0, 0xce, 0x2d, 0x4b, 0x17, 0x9d, //0x00003eb8 .quad -7127145225176161157
	0x94, 0x83, 0x4f, 0x32, 0xbd, 0xd0, 0xa5, 0x3b, //0x00003ec0 .quad 4298070930406474644
	0x9a, 0x1d, 0x71, 0x42, 0xf9, 0x1d, 0x5d, 0xc4, //0x00003ec8 .quad -4297245513042813542
	0x79, 0x64, 0xe3, 0x7e, 0xec, 0x44, 0x8f, 0xca, //0x00003ed0 .quad -3850783373846682503
	0x00, 0x65, 0x0d, 0x93, 0x77, 0x65, 0x74, 0xf5, //0x00003ed8 .quad -759870872876129024
	0xcb, 0x1e, 0x4e, 0xcf, 0x13, 0x8b, 0x99, 0x7e, //0x00003ee0 .quad 9122475437414293195
	0x20, 0x5f, 0xe8, 0xbb, 0x6a, 0xbf, 0x68, 0x99, //0x00003ee8 .quad -7392448323188662496
	0x7e, 0xa6, 0x21, 0xc3, 0xd8, 0xed, 0x3f, 0x9e, //0x00003ef0 .quad -7043649776941685122
	0xe8, 0x76, 0xe2, 0x6a, 0x45, 0xef, 0xc2, 0xbf, //0x00003ef8 .quad -4628874385558440216
	0x1e, 0x10, 0xea, 0xf3, 0x4e, 0xe9, 0xcf, 0xc5, //0x00003f00 .quad -4192876202749718498
	0xa2, 0x14, 0x9b, 0xc5, 0x16, 0xab, 0xb3, 0xef, //0x00003f08 .quad -1174406963520662366
	0x12, 0x4a, 0x72, 0x58, 0xd1, 0xf1, 0xa1, 0xbb, //0x00003f10 .quad -4926390635932268014
	0xe5, 0xec, 0x80, 0x3b, 0xee, 0x4a, 0xd0, 0x95, //0x00003f18 .quad -7651533379841495835
	0x97, 0xdc, 0x8e, 0xae, 0x45, 0x6e, 0x8a, 0x2a, //0x00003f20 .quad 3065383741939440791
	0x1f, 0x28, 0x61, 0xca, 0xa9, 0x5d, 0x44, 0xbb, //0x00003f28 .quad -4952730706374481889
	0xbd, 0x93, 0x32, 0x1a, 0xd7, 0x09, 0x2d, 0xf5, //0x00003f30 .quad -779956341003086915
	0x26, 0x72, 0xf9, 0x3c, 0x14, 0x75, 0x15, 0xea, //0x00003f38 .quad -1579227364540714458
	0x56, 0x9c, 0x5f, 0x70, 0x26, 0x26, 0x3c, 0x59, //0x00003f40 .quad 6430056314514152534
	0x58, 0xe7, 0x1b, 0xa6, 0x2c, 0x69, 0x4d, 0x92, //0x00003f48 .quad -7904546130479028392
	0x6c, 0x83, 0x77, 0x0c, 0xb0, 0x2f, 0x8b, 0x6f, //0x00003f50 .quad 8037570393142690668
	0x2e, 0xe1, 0xa2, 0xcf, 0x77, 0xc3, 0xe0, 0xb6, //0x00003f58 .quad -5268996644671397586
	0x47, 0x64, 0x95, 0x0f, 0x9c, 0xfb, 0x6d, 0x0b, //0x00003f60 .quad 823590954573587527
	0x7a, 0x99, 0x8b, 0xc3, 0x55, 0xf4, 0x98, 0xe4, //0x00003f68 .quad -1974559787411859078
	0xac, 0x5e, 0xbd, 0x89, 0x41, 0xbd, 0x24, 0x47, //0x00003f70 .quad 5126430365035880108
	0xec, 0x3f, 0x37, 0x9a, 0xb5, 0x98, 0xdf, 0x8e, //0x00003f78 .quad -8151628894773493780
	0x57, 0xb6, 0x2c, 0xec, 0x91, 0xec, 0xed, 0x58, //0x00003f80 .quad 6408037956294850135
	0xe7, 0x0f, 0xc5, 0x00, 0xe3, 0x7e, 0x97, 0xb2, //0x00003f88 .quad -5577850100039479321
	0xed, 0xe3, 0x37, 0x67, 0xb6, 0x67, 0x29, 0x2f, //0x00003f90 .quad 3398361426941174765
	0xe1, 0x53, 0xf6, 0xc0, 0x9b, 0x5e, 0x3d, 0xdf, //0x00003f98 .quad -2360626606621961247
	0x74, 0xee, 0x82, 0x00, 0xd2, 0xe0, 0x79, 0xbd, //0x00003fa0 .quad -4793553135802847628
	0x6c, 0xf4, 0x99, 0x58, 0x21, 0x5b, 0x86, 0x8b, //0x00003fa8 .quad -8392920656779807636
	0x11, 0xaa, 0xa3, 0x80, 0x06, 0x59, 0xd8, 0xec, //0x00003fb0 .quad -1380255401326171631
	0x87, 0x71, 0xc0, 0xae, 0xe9, 0xf1, 0x67, 0xae, //0x00003fb8 .quad -5879464802547371641
	0x95, 0x94, 0xcc, 0x20, 0x48, 0x6f, 0x0e, 0xe8, //0x00003fc0 .quad -1725319251657714539
	0xe9, 0x8d, 0x70, 0x1a, 0x64, 0xee, 0x01, 0xda, //0x00003fc8 .quad -2737644984756826647
	0xdd, 0xdc, 0x7f, 0x14, 0x8d, 0x05, 0x09, 0x31, //0x00003fd0 .quad 3533361486141316317
	0xb2, 0x58, 0x86, 0x90, 0xfe, 0x34, 0x41, 0x88, //0x00003fd8 .quad -8628557143114098510
	0x15, 0xd4, 0x9f, 0x59, 0xf0, 0x46, 0x4b, 0xbd, //0x00003fe0 .quad -4806670179178130411
	0xde, 0xee, 0xa7, 0x34, 0x3e, 0x82, 0x51, 0xaa, //0x00003fe8 .quad -6174010410465235234
	0x1a, 0xc9, 0x07, 0x70, 0xac, 0x18, 0x9e, 0x6c, //0x00003ff0 .quad 7826720331309500698
	0x96, 0xea, 0xd1, 0xc1, 0xcd, 0xe2, 0xe5, 0xd4, //0x00003ff8 .quad -3105826994654156138
	0xb0, 0xdd, 0x04, 0xc6, 0x6b, 0xcf, 0xe2, 0x03, //0x00004000 .quad 280014188641050032
	0x9e, 0x32, 0x23, 0x99, 0xc0, 0xad, 0x0f, 0x85, //0x00004008 .quad -8858670899299929442
	0x1c, 0x15, 0x86, 0xb7, 0x46, 0x83, 0xdb, 0x84, //0x00004010 .quad -8873354301053463268
	0x45, 0xff, 0x6b, 0xbf, 0x30, 0x99, 0x53, 0xa6, //0x00004018 .quad -6461652605697523899
	0x63, 0x9a, 0x67, 0x65, 0x18, 0x64, 0x12, 0xe6, //0x00004020 .quad -1868320839462053277
	0x16, 0xff, 0x46, 0xef, 0x7c, 0x7f, 0xe8, 0xcf, //0x00004028 .quad -3465379738694516970
	0x7e, 0xc0, 0x60, 0x3f, 0x8f, 0x7e, 0xcb, 0x4f, //0x00004030 .quad 5749828502977298558
	0x6e, 0x5f, 0x8c, 0x15, 0xae, 0x4f, 0xf1, 0x81, //0x00004038 .quad -9083391364325154962
	0x9d, 0xf0, 0x38, 0x0f, 0x33, 0x5e, 0xbe, 0xe3, //0x00004040 .quad -2036086408133152611
	0x49, 0x77, 0xef, 0x9a, 0x99, 0xa3, 0x6d, 0xa2, //0x00004048 .quad -6742553186979055799
	0xc5, 0x2c, 0x07, 0xd3, 0xbf, 0xf5, 0xad, 0x5c, //0x00004050 .quad 6678264026688335045
	0x1c, 0x55, 0xab, 0x01, 0x80, 0x0c, 0x09, 0xcb, //0x00004058 .quad -3816505465296431844
	0xf6, 0xf7, 0xc8, 0xc7, 0x2f, 0x73, 0xd9, 0x73, //0x00004060 .quad 8347830033360418806
	0x63, 0x2a, 0x16, 0x02, 0xa0, 0x4f, 0xcb, 0xfd, //0x00004068 .quad -158945813193151901
	0xfa, 0x9a, 0xdd, 0xdc, 0xfd, 0xe7, 0x67, 0x28, //0x00004070 .quad 2911550761636567802
	0x7e, 0xda, 0x4d, 0x01, 0xc4, 0x11, 0x9f, 0x9e, //0x00004078 .quad -7016870160886801794
	0xb8, 0x01, 0x15, 0x54, 0xfd, 0xe1, 0x81, 0xb2, //0x00004080 .quad -5583933584809066056
	0x1d, 0x51, 0xa1, 0x01, 0x35, 0xd6, 0x46, 0xc6, //0x00004088 .quad -4159401682681114339
	0x26, 0x42, 0x1a, 0xa9, 0x7c, 0x5a, 0x22, 0x1f, //0x00004090 .quad 2243455055843443238
	0x65, 0xa5, 0x09, 0x42, 0xc2, 0x8b, 0xd8, 0xf7, //0x00004098 .quad -587566084924005019
	0x58, 0x69, 0xb0, 0xe9, 0x8d, 0x78, 0x75, 0x33, //0x000040a0 .quad 3708002419115845976
	0x5f, 0x07, 0x46, 0x69, 0x59, 0x57, 0xe7, 0x9a, //0x000040a8 .quad -7284757830718584993
	0xae, 0x83, 0x1c, 0x64, 0xb1, 0xd6, 0x52, 0x00, //0x000040b0 .quad 23317005467419566
	0x37, 0x89, 0x97, 0xc3, 0x2f, 0x2d, 0xa1, 0xc1, //0x000040b8 .quad -4494261269970843337
	0x9a, 0xa4, 0x23, 0xbd, 0x5d, 0x8c, 0x67, 0xc0, //0x000040c0 .quad -4582539761593113446
	0x84, 0x6b, 0x7d, 0xb4, 0x7b, 0x78, 0x09, 0xf2, //0x000040c8 .quad -1006140569036166268
	0xe0, 0x46, 0x36, 0x96, 0xba, 0xb7, 0x40, 0xf8, //0x000040d0 .quad -558244341782001952
	0x32, 0x63, 0xce, 0x50, 0x4d, 0xeb, 0x45, 0x97, //0x000040d8 .quad -7546366883288685774
	0x98, 0xd8, 0xc3, 0x3b, 0xa9, 0xe5, 0x50, 0xb6, //0x000040e0 .quad -5309491445654890344
	0xff, 0xfb, 0x01, 0xa5, 0x20, 0x66, 0x17, 0xbd, //0x000040e8 .quad -4821272585683469313
	0xbe, 0xce, 0xb4, 0x8a, 0x13, 0x1f, 0xe5, 0xa3, //0x000040f0 .quad -6636864307068612930
	0xff, 0x7a, 0x42, 0xce, 0xa8, 0x3f, 0x5d, 0xec, //0x000040f8 .quad -1414904713676948737
	0x37, 0x01, 0xb1, 0x36, 0x6c, 0x33, 0x6f, 0xc6, //0x00004100 .quad -4148040191917883081
	0xdf, 0x8c, 0xe9, 0x80, 0xc9, 0x47, 0xba, 0x93, //0x00004108 .quad -7801844473689174817
	0x84, 0x41, 0x5d, 0x44, 0x47, 0x00, 0x0b, 0xb8, //0x00004110 .quad -5185050239897353852
	0x17, 0xf0, 0x23, 0xe1, 0xbb, 0xd9, 0xa8, 0xb8, //0x00004118 .quad -5140619573684080617
	0xe5, 0x91, 0x74, 0x15, 0x59, 0xc0, 0x0d, 0xa6, //0x00004120 .quad -6481312799871692315
	0x1d, 0xec, 0x6c, 0xd9, 0x2a, 0x10, 0xd3, 0xe6, //0x00004128 .quad -1814088448677712867
	0x2f, 0xdb, 0x68, 0xad, 0x37, 0x98, 0xc8, 0x87, //0x00004130 .quad -8662506518347195601
	0x92, 0x13, 0xe4, 0xc7, 0x1a, 0xea, 0x43, 0x90, //0x00004138 .quad -8051334308064652398
	0xfb, 0x11, 0xc3, 0x98, 0x45, 0xbe, 0xba, 0x29, //0x00004140 .quad 3006924907348169211
	0x77, 0x18, 0xdd, 0x79, 0xa1, 0xe4, 0x54, 0xb4, //0x00004148 .quad -5452481866653427593
	0x7a, 0xd6, 0xf3, 0xfe, 0xd6, 0x6d, 0x29, 0xf4, //0x00004150 .quad -853029884242176390
	0x94, 0x5e, 0x54, 0xd8, 0xc9, 0x1d, 0x6a, 0xe1, //0x00004158 .quad -2203916314889396588
	0x0c, 0x66, 0x58, 0x5f, 0xa6, 0xe4, 0x99, 0x18, //0x00004160 .quad 1772699331562333708
	0x1d, 0xbb, 0x34, 0x27, 0x9e, 0x52, 0xe2, 0x8c, //0x00004168 .quad -8294976724446954723
	0x8f, 0x7f, 0x2e, 0xf7, 0xcf, 0x5d, 0xc0, 0x5e, //0x00004170 .quad 6827560182880305039
	0xe4, 0xe9, 0x01, 0xb1, 0x45, 0xe7, 0x1a, 0xb0, //0x00004178 .quad -5757034887131305500
	0x73, 0x1f, 0xfa, 0xf4, 0x43, 0x75, 0x70, 0x76, //0x00004180 .quad 8534450228600381299
	0x5d, 0x64, 0x42, 0x1d, 0x17, 0xa1, 0x21, 0xdc, //0x00004188 .quad -2584607590486743971
	0xa8, 0x53, 0x1c, 0x79, 0x4a, 0x49, 0x06, 0x6a, //0x00004190 .quad 7639874402088932264
	0xba, 0x7e, 0x49, 0x72, 0xae, 0x04, 0x95, 0x89, //0x00004198 .quad -8532908771695296838
	0x92, 0x68, 0x63, 0x17, 0x9d, 0xdb, 0x87, 0x04, //0x000041a0 .quad 326470965756389522
	0x69, 0xde, 0xdb, 0x0e, 0xda, 0x45, 0xfa, 0xab, //0x000041a8 .quad -6054449946191733143
	0xb6, 0x42, 0x3c, 0x5d, 0x84, 0xd2, 0xa9, 0x45, //0x000041b0 .quad 5019774725622874806
	0x03, 0xd6, 0x92, 0x92, 0x50, 0xd7, 0xf8, 0xd6, //0x000041b8 .quad -2956376414312278525
	0xb2, 0xa9, 0x45, 0xba, 0x92, 0x23, 0x8a, 0x0b, //0x000041c0 .quad 831516194300602802
	0xc2, 0xc5, 0x9b, 0x5b, 0x92, 0x86, 0x5b, 0x86, //0x000041c8 .quad -8765264286586255934
	0x1e, 0x14, 0xd7, 0x68, 0x77, 0xac, 0x6c, 0x8e, //0x000041d0 .quad -8183976793979022306
	0x32, 0xb7, 0x82, 0xf2, 0x36, 0x68, 0xf2, 0xa7, //0x000041d8 .quad -6344894339805432014
	0x26, 0xd9, 0x0c, 0x43, 0x95, 0xd7, 0x07, 0x32, //0x000041e0 .quad 3605087062808385830
	0xff, 0x64, 0x23, 0xaf, 0x44, 0x02, 0xef, 0xd1, //0x000041e8 .quad -3319431906329402113
	0xb8, 0x07, 0xe8, 0x49, 0xbd, 0xe6, 0x44, 0x7f, //0x000041f0 .quad 9170708441896323000
	0x1f, 0x1f, 0x76, 0xed, 0x6a, 0x61, 0x35, 0x83, //0x000041f8 .quad -8992173969096958177
	0xa6, 0x09, 0x62, 0x9c, 0x6c, 0x20, 0x16, 0x5f, //0x00004200 .quad 6851699533943015846
	0xe7, 0xa6, 0xd3, 0xa8, 0xc5, 0xb9, 0x02, 0xa4, //0x00004208 .quad -6628531442943809817
	0x0f, 0x8c, 0x7a, 0xc3, 0x87, 0xa8, 0xdb, 0x36, //0x00004210 .quad 3952938399001381903
	0xa1, 0x90, 0x08, 0x13, 0x37, 0x68, 0x03, 0xcd, //0x00004218 .quad -3673978285252374367
	0x89, 0x97, 0x2c, 0xda, 0x54, 0x49, 0x49, 0xc2, //0x00004220 .quad -4446942528265218167
	0x64, 0x5a, 0xe5, 0x6b, 0x22, 0x21, 0x22, 0x80, //0x00004228 .quad -9213765455923815836
	0x6c, 0xbd, 0xb7, 0x10, 0xaa, 0x9b, 0xdb, 0xf2, //0x00004230 .quad -946992141904134804
	0xfd, 0xb0, 0xde, 0x06, 0x6b, 0xa9, 0x2a, 0xa0, //0x00004238 .quad -6905520801477381891
	0xc7, 0xac, 0xe5, 0x94, 0x94, 0x82, 0x92, 0x6f, //0x00004240 .quad 8039631859474607303
	0x3d, 0x5d, 0x96, 0xc8, 0xc5, 0x53, 0x35, 0xc8, //0x00004248 .quad -4020214983419339459
	0xf9, 0x17, 0x1f, 0xba, 0x39, 0x23, 0x77, 0xcb, //0x00004250 .quad -3785518230938904583
	0x8c, 0xf4, 0xbb, 0x3a, 0xb7, 0xa8, 0x42, 0xfa, //0x00004258 .quad -413582710846786420
	0xfb, 0x6e, 0x53, 0x14, 0x04, 0x76, 0x2a, 0xff, //0x00004260 .quad -60105885123121413
	0xd7, 0x78, 0xb5, 0x84, 0x72, 0xa9, 0x69, 0x9c, //0x00004268 .quad -7176018221920323369
	0xba, 0x4a, 0x68, 0x19, 0x85, 0x13, 0xf5, 0xfe, //0x00004270 .quad -75132356403901766
	0x0d, 0xd7, 0xe2, 0x25, 0xcf, 0x13, 0x84, 0xc3, //0x00004278 .quad -4358336758973016307
	0x69, 0x5d, 0xc2, 0x5f, 0x66, 0x58, 0xb2, 0x7e, //0x00004280 .quad 9129456591349898601
	0xd1, 0x8c, 0x5b, 0xef, 0xc2, 0x18, 0x65, 0xf4, //0x00004288 .quad -836234930288882479
	0x61, 0x7a, 0xd9, 0xfb, 0x3f, 0x77, 0x2f, 0xef, //0x00004290 .quad -1211618658047395231
	0x02, 0x38, 0x99, 0xd5, 0x79, 0x2f, 0xbf, 0x98, //0x00004298 .quad -7440175859071633406
	0xfa, 0xd8, 0xcf, 0xfa, 0x0f, 0x55, 0xfb, 0xaa, //0x000042a0 .quad -6126209340986631942
	0x03, 0x86, 0xff, 0x4a, 0x58, 0xfb, 0xee, 0xbe, //0x000042a8 .quad -4688533805412153853
	0x38, 0xcf, 0x83, 0xf9, 0x53, 0x2a, 0xba, 0x95, //0x000042b0 .quad -7657761676233289928
	0x84, 0x67, 0xbf, 0x5d, 0x2e, 0xba, 0xaa, 0xee, //0x000042b8 .quad -1248981238337804412
	0x83, 0x61, 0xf2, 0x7b, 0x74, 0x5a, 0x94, 0xdd, //0x000042c0 .quad -2480258038432112253
	0xb2, 0xa0, 0x97, 0xfa, 0x5c, 0xb4, 0x2a, 0x95, //0x000042c8 .quad -7698142301602209614
	0xe4, 0xf9, 0xee, 0x9a, 0x11, 0x71, 0xf9, 0x94, //0x000042d0 .quad -7712008566467528220
	0xdf, 0x88, 0x3d, 0x39, 0x74, 0x61, 0x75, 0xba, //0x000042d8 .quad -5010991858575374113
	0x5d, 0xb8, 0xaa, 0x01, 0x56, 0xcd, 0x37, 0x7a, //0x000042e0 .quad 8806733365625141341
	0x17, 0xeb, 0x8c, 0x47, 0xd1, 0xb9, 0x12, 0xe9, //0x000042e8 .quad -1652053804791829737
	0x3a, 0xb3, 0x0a, 0xc1, 0x55, 0xe0, 0x62, 0xac, //0x000042f0 .quad -6025006692552756422
	0xee, 0x12, 0xb8, 0xcc, 0x22, 0xb4, 0xab, 0x91, //0x000042f8 .quad -7950062655635975442
	0x09, 0x60, 0x4d, 0x31, 0x6b, 0x98, 0x7b, 0x57, //0x00004300 .quad 6303799689591218185
	0xaa, 0x17, 0xe6, 0x7f, 0x2b, 0xa1, 0x16, 0xb6, //0x00004308 .quad -5325892301117581398
	0x0b, 0xb8, 0xa0, 0xfd, 0x85, 0x7e, 0x5a, 0xed, //0x00004310 .quad -1343622424865753077
	0x94, 0x9d, 0xdf, 0x5f, 0x76, 0x49, 0x9c, 0xe3, //0x00004318 .quad -2045679357969588844
	0x07, 0x73, 0x84, 0xbe, 0x13, 0x8f, 0x58, 0x14, //0x00004320 .quad 1466078993672598279
	0x7d, 0xc2, 0xeb, 0xfb, 0xe9, 0xad, 0x41, 0x8e, //0x00004328 .quad -8196078626372074883
	0xc8, 0x8f, 0x25, 0xae, 0xd8, 0xb2, 0x6e, 0x59, //0x00004330 .quad 6444284760518135752
	0x1c, 0xb3, 0xe6, 0x7a, 0x64, 0x19, 0xd2, 0xb1, //0x00004338 .quad -5633412264537705700
	0xbb, 0xf3, 0xae, 0xd9, 0x8e, 0x5f, 0xca, 0x6f, //0x00004340 .quad 8055355950647669691
	0xe3, 0x5f, 0xa0, 0x99, 0xbd, 0x9f, 0x46, 0xde, //0x00004348 .quad -2430079312244744221
	0x54, 0x58, 0x0d, 0x48, 0xb9, 0x7b, 0xde, 0x25, //0x00004350 .quad 2728754459941099604
	0xee, 0x3b, 0x04, 0x80, 0xd6, 0x23, 0xec, 0x8a, //0x00004358 .quad -8436328597794046994
	0x6a, 0xae, 0x10, 0x9a, 0xa7, 0x1a, 0x56, 0xaf, //0x00004360 .quad -5812428961928401302
	0xe9, 0x4a, 0x05, 0x20, 0xcc, 0x2c, 0xa7, 0xad, //0x00004368 .quad -5933724728815170839
	0x04, 0xda, 0x94, 0x80, 0x51, 0xa1, 0x2b, 0x1b, //0x00004370 .quad 1957835834444274180
	0xa4, 0x9d, 0x06, 0x28, 0xff, 0xf7, 0x10, 0xd9, //0x00004378 .quad -2805469892591575644
	0x42, 0x08, 0x5d, 0xf0, 0xd2, 0x44, 0xfb, 0x90, //0x00004380 .quad -7999724640327104446
	0x86, 0x22, 0x04, 0x79, 0xff, 0x9a, 0xaa, 0x87, //0x00004388 .quad -8670947710510816634
	0x53, 0x4a, 0x74, 0xac, 0x07, 0x16, 0x3a, 0x35, //0x00004390 .quad 3835402254873283155
	0x28, 0x2b, 0x45, 0x57, 0xbf, 0x41, 0x95, 0xa9, //0x00004398 .quad -6226998619711132888
	0xe8, 0x5c, 0x91, 0x97, 0x89, 0x9b, 0x88, 0x42, //0x000043a0 .quad 4794252818591603944
	0xf2, 0x75, 0x16, 0x2d, 0x2f, 0x92, 0xfa, 0xd3, //0x000043a8 .quad -3172062256211528206
	0x11, 0xda, 0xba, 0xfe, 0x35, 0x61, 0x95, 0x69, //0x000043b0 .quad 7608094030047140369
	0xb7, 0x09, 0x2e, 0x7c, 0x5d, 0x9b, 0x7c, 0x84, //0x000043b8 .quad -8900067937773286985
	0x95, 0x90, 0x69, 0x7e, 0x83, 0xb9, 0xfa, 0x43, //0x000043c0 .quad 4898431519131537557
	0x25, 0x8c, 0x39, 0xdb, 0x34, 0xc2, 0x9b, 0xa5, //0x000043c8 .quad -6513398903789220827
	0xbb, 0xf4, 0x03, 0x5e, 0xe4, 0x67, 0xf9, 0x94, //0x000043d0 .quad -7712018656367741765
	0x2e, 0xef, 0x07, 0x12, 0xc2, 0xb2, 0x02, 0xcf, //0x000043d8 .quad -3530062611309138130
	0xf5, 0x78, 0xc2, 0xba, 0xee, 0xe0, 0x1b, 0x1d, //0x000043e0 .quad 2097517367411243253
	0x7d, 0xf5, 0x44, 0x4b, 0xb9, 0xaf, 0x61, 0x81, //0x000043e8 .quad -9123818159709293187
	0x32, 0x17, 0x73, 0x69, 0x2a, 0xd9, 0x62, 0x64, //0x000043f0 .quad 7233582727691441970
	0xdc, 0x32, 0x16, 0x9e, 0xa7, 0x1b, 0xba, 0xa1, //0x000043f8 .quad -6793086681209228580
	0xfe, 0xdc, 0xcf, 0x03, 0x75, 0x8f, 0x7b, 0x7d, //0x00004400 .quad 9041978409614302462
	0x93, 0xbf, 0x9b, 0x85, 0x91, 0xa2, 0x28, 0xca, //0x00004408 .quad -3879672333084147821
	0x3e, 0xd4, 0xc3, 0x44, 0x52, 0x73, 0xda, 0x5c, //0x00004410 .quad 6690786993590490174
	0x78, 0xaf, 0x02, 0xe7, 0x35, 0xcb, 0xb2, 0xfc, //0x00004418 .quad -237904397927796872
	0xa7, 0x64, 0xfa, 0x6a, 0x13, 0x88, 0x08, 0x3a, //0x00004420 .quad 4181741870994056359
	0xab, 0xad, 0x61, 0xb0, 0x01, 0xbf, 0xef, 0x9d, //0x00004428 .quad -7066219276345954901
	0xd0, 0xfd, 0xb8, 0x45, 0x18, 0xaa, 0x8a, 0x08, //0x00004430 .quad 615491320315182544
	0x16, 0x19, 0x7a, 0x1c, 0xc2, 0xae, 0x6b, 0xc5, //0x00004438 .quad -4221088077005055722
	0x45, 0x3d, 0x27, 0x57, 0x9e, 0x54, 0xad, 0x8a, //0x00004440 .quad -8454007886460797627
	0x5b, 0x9f, 0x98, 0xa3, 0x72, 0x9a, 0xc6, 0xf6, //0x00004448 .quad -664674077828931749
	0x4b, 0x86, 0x78, 0xf6, 0xe2, 0x54, 0xac, 0x36, //0x00004450 .quad 3939617107816777291
	0x99, 0x63, 0x3f, 0xa6, 0x87, 0x20, 0x3c, 0x9a, //0x00004458 .quad -7332950326284164199
	0xdd, 0xa7, 0x16, 0xb4, 0x1b, 0x6a, 0x57, 0x84, //0x00004460 .quad -8910536670511192099
	0x7f, 0x3c, 0xcf, 0x8f, 0xa9, 0x28, 0xcb, 0xc0, //0x00004468 .quad -4554501889427817345
	0xd5, 0x51, 0x1c, 0xa1, 0xa2, 0x44, 0x6d, 0x65, //0x00004470 .quad 7308573235570561493
	0x9f, 0x0b, 0xc3, 0xf3, 0xd3, 0xf2, 0xfd, 0xf0, //0x00004478 .quad -1081441343357383777
	0x25, 0xb3, 0xb1, 0xa4, 0xe5, 0x4a, 0x64, 0x9f, //0x00004480 .quad -6961356773836868827
	0x43, 0xe7, 0x59, 0x78, 0xc4, 0xb7, 0x9e, 0x96, //0x00004488 .quad -7593429867239446717
	0xee, 0x1f, 0xde, 0x0d, 0x9f, 0x5d, 0x3d, 0x87, //0x00004490 .quad -8701695967296086034
	0x14, 0x61, 0x70, 0x96, 0xb5, 0x65, 0x46, 0xbc, //0x00004498 .quad -4880101315621920492
	0xea, 0xa7, 0x55, 0xd1, 0x06, 0xb5, 0x0c, 0xa9, //0x000044a0 .quad -6265433940692719638
	0x59, 0x79, 0x0c, 0xfc, 0x22, 0xff, 0x57, 0xeb, //0x000044a8 .quad -1488440626100012711
	0xf2, 0x88, 0xd5, 0x42, 0x24, 0xf1, 0xa7, 0x09, //0x000044b0 .quad 695789805494438130
	0xd8, 0xcb, 0x87, 0xdd, 0x75, 0xff, 0x16, 0x93, //0x000044b8 .quad -7847804418953589800
	0x2f, 0xeb, 0x8a, 0x53, 0x6d, 0xed, 0x11, 0x0c, //0x000044c0 .quad 869737256868047663
	0xce, 0xbe, 0xe9, 0x54, 0x53, 0xbf, 0xdc, 0xb7, //0x000044c8 .quad -5198069505264599346
	0xfa, 0xa5, 0x6d, 0xa8, 0xc8, 0x68, 0x16, 0x8f, //0x000044d0 .quad -8136200465769716230
	0x81, 0x2e, 0x24, 0x2a, 0x28, 0xef, 0xd3, 0xe5, //0x000044d8 .quad -1885900863153361279
	0xbc, 0x87, 0x44, 0x69, 0x7d, 0x01, 0x6e, 0xf9, //0x000044e0 .quad -473439272678684740
	0x10, 0x9d, 0x56, 0x1a, 0x79, 0x75, 0xa4, 0x8f, //0x000044e8 .quad -8096217067111932656
	0xac, 0xa9, 0x95, 0xc3, 0xdc, 0x81, 0xc9, 0x37, //0x000044f0 .quad 4019886927579031980
	0x55, 0x44, 0xec, 0x60, 0xd7, 0x92, 0x8d, 0xb3, //0x000044f8 .quad -5508585315462527915
	0x17, 0x14, 0x7b, 0xf4, 0x53, 0xe2, 0xbb, 0x85, //0x00004500 .quad -8810199395808373737
	0x6a, 0x55, 0x27, 0x39, 0x8d, 0xf7, 0x70, 0xe0, //0x00004508 .quad -2274045625900771990
	0x8e, 0xec, 0xcc, 0x78, 0x74, 0x6d, 0x95, 0x93, //0x00004510 .quad -7812217631593927538
	0x62, 0x95, 0xb8, 0x43, 0xb8, 0x9a, 0x46, 0x8c, //0x00004518 .quad -8338807543829064350
	0xb2, 0x27, 0x00, 0x97, 0xd1, 0xc8, 0x7a, 0x38, //0x00004520 .quad 4069786015789754290
	0xbb, 0xba, 0xa6, 0x54, 0x66, 0x41, 0x58, 0xaf, //0x00004528 .quad -5811823411358942533
	0x9e, 0x31, 0xc0, 0xfc, 0x05, 0x7b, 0x99, 0x06, //0x00004530 .quad 475546501309804958
	0x6a, 0x69, 0xd0, 0xe9, 0xbf, 0x51, 0x2e, 0xdb, //0x00004538 .quad -2653093245771290262
	0x03, 0x1f, 0xf8, 0xbd, 0xe3, 0xec, 0x1f, 0x44, //0x00004540 .quad 4908902581746016003
	0xe2, 0x41, 0x22, 0xf2, 0x17, 0xf3, 0xfc, 0x88, //0x00004548 .quad -8575712306248138270
	0xc3, 0x26, 0x76, 0xad, 0x1c, 0xe8, 0x27, 0xd5, //0x00004550 .quad -3087243809672255805
	0x5a, 0xd2, 0xaa, 0xee, 0xdd, 0x2f, 0x3c, 0xab, //0x00004558 .quad -6107954364382784934
	0x74, 0xb0, 0xd3, 0xd8, 0x23, 0xe2, 0x71, 0x8a, //0x00004560 .quad -8470740780517707660
	0xf1, 0x86, 0x55, 0x6a, 0xd5, 0x3b, 0x0b, 0xd6, //0x00004568 .quad -3023256937051093263
	0x49, 0x4e, 0x84, 0x67, 0x56, 0x2d, 0x87, 0xf6, //0x00004570 .quad -682526969396179383
	0x56, 0x74, 0x75, 0x62, 0x65, 0x05, 0xc7, 0x85, //0x00004578 .quad -8807064613298015146
	0xdb, 0x61, 0x65, 0x01, 0xac, 0xf8, 0x28, 0xb4, //0x00004580 .quad -5464844730172612133
	0x6c, 0xd1, 0x12, 0xbb, 0xbe, 0xc6, 0x38, 0xa7, //0x00004588 .quad -6397144748195131028
	0x52, 0xba, 0xbe, 0x01, 0xd7, 0x36, 0x33, 0xe1, //0x00004590 .quad -2219369894288377262
	0xc7, 0x85, 0xd7, 0x69, 0x6e, 0xf8, 0x06, 0xd1, //0x00004598 .quad -3384744916816525881
	0x73, 0x34, 0x17, 0x61, 0x46, 0x02, 0xc0, 0xec, //0x000045a0 .quad -1387106183930235789
	0x9c, 0xb3, 0x26, 0x02, 0x45, 0x5b, 0xa4, 0x82, //0x000045a8 .quad -9032994600651410532
	0x90, 0x01, 0x5d, 0xf9, 0xd7, 0x02, 0xf0, 0x27, //0x000045b0 .quad 2877803288514593168
	0x84, 0x60, 0xb0, 0x42, 0x16, 0x72, 0x4d, 0xa3, //0x000045b8 .quad -6679557232386875260
	0xf4, 0x41, 0xb4, 0xf7, 0x8d, 0x03, 0xec, 0x31, //0x000045c0 .quad 3597254110643241460
	0xa5, 0x78, 0x5c, 0xd3, 0x9b, 0xce, 0x20, 0xcc, //0x000045c8 .quad -3737760522056206171
	0x71, 0x52, 0xa1, 0x75, 0x71, 0x04, 0x67, 0x7e, //0x000045d0 .quad 9108253656731439729
	0xce, 0x96, 0x33, 0xc8, 0x42, 0x02, 0x29, 0xff, //0x000045d8 .quad -60514634142869810
	0x86, 0xd3, 0x84, 0xe9, 0xc6, 0x62, 0x00, 0x0f, //0x000045e0 .quad 1080972517029761926
	0x41, 0x3e, 0x20, 0xbd, 0x69, 0xa1, 0x79, 0x9f, //0x000045e8 .quad -6955350673980375487
	0x68, 0x08, 0xe6, 0xa3, 0x78, 0x7b, 0xc0, 0x52, //0x000045f0 .quad 5962901664714590312
	0xd1, 0x4d, 0x68, 0x2c, 0xc4, 0x09, 0x58, 0xc7, //0x000045f8 .quad -4082502324048081455
	0x82, 0x8a, 0xdf, 0xcc, 0x56, 0x9a, 0x70, 0xa7, //0x00004600 .quad -6381430974388925822
	0x45, 0x61, 0x82, 0x37, 0x35, 0x0c, 0x2e, 0xf9, //0x00004608 .quad -491441886632713915
	0x91, 0xb6, 0x0b, 0x40, 0x76, 0x60, 0xa6, 0x88, //0x00004610 .quad -8600080377420466543
	0xcb, 0x7c, 0xb1, 0x42, 0xa1, 0xc7, 0xbc, 0x9b, //0x00004618 .quad -7224680206786528053
	0x35, 0xa4, 0x0e, 0xd0, 0x93, 0xf8, 0xcf, 0x6a, //0x00004620 .quad 7696643601933968437
	0xfe, 0xdb, 0x5d, 0x93, 0x89, 0xf9, 0xab, 0xc2, //0x00004628 .quad -4419164240055772162
	0x43, 0x4d, 0x12, 0xc4, 0xb8, 0xf6, 0x83, 0x05, //0x00004630 .quad 397432465562684739
	0xfe, 0x52, 0x35, 0xf8, 0xeb, 0xf7, 0x56, 0xf3, //0x00004638 .quad -912269281642327298
	0x4a, 0x70, 0x8b, 0x7a, 0x33, 0x7a, 0x72, 0xc3, //0x00004640 .quad -4363290727450709942
	0xde, 0x53, 0x21, 0x7b, 0xf3, 0x5a, 0x16, 0x98, //0x00004648 .quad -7487697328667536418
	0x5c, 0x4c, 0x2e, 0x59, 0xc0, 0x18, 0x4f, 0x74, //0x00004650 .quad 8380944645968776284
	0xd6, 0xa8, 0xe9, 0x59, 0xb0, 0xf1, 0x1b, 0xbe, //0x00004658 .quad -4747935642407032618
	0x73, 0xdf, 0x79, 0x6f, 0xf0, 0xde, 0x62, 0x11, //0x00004660 .quad 1252808770606194547
	0x0c, 0x13, 0x64, 0x70, 0x1c, 0xee, 0xa2, 0xed, //0x00004668 .quad -1323233534581402868
	0xa8, 0x2b, 0xac, 0x45, 0x56, 0xcb, 0xdd, 0x8a, //0x00004670 .quad -8440366555225904216
	0xe7, 0x8b, 0x3e, 0xc6, 0xd1, 0xd4, 0x85, 0x94, //0x00004678 .quad -7744549986754458649
	0x92, 0x36, 0x17, 0xd7, 0x2b, 0x3e, 0x95, 0x6d, //0x00004680 .quad 7896285879677171346
	0xe1, 0x2e, 0xce, 0x37, 0x06, 0x4a, 0xa7, 0xb9, //0x00004688 .quad -5069001465015685407
	0x37, 0x04, 0xdd, 0xcc, 0xb6, 0x8d, 0xfa, 0xc8, //0x00004690 .quad -3964700705685699529
	0x99, 0xba, 0xc1, 0xc5, 0x87, 0x1c, 0x11, 0xe8, //0x00004698 .quad -1724565812842218855
	0xa2, 0x22, 0x0a, 0x40, 0x92, 0x98, 0x9c, 0x1d, //0x000046a0 .quad 2133748077373825698
	0xa0, 0x14, 0x99, 0xdb, 0xd4, 0xb1, 0x0a, 0x91, //0x000046a8 .quad -7995382660667468640
	0x4b, 0xab, 0x0c, 0xd0, 0xb6, 0xbe, 0x03, 0x25, //0x000046b0 .quad 2667185096717282123
	0xc8, 0x59, 0x7f, 0x12, 0x4a, 0x5e, 0x4d, 0xb5, //0x000046b8 .quad -5382542307406947896
	0x1d, 0xd6, 0x0f, 0x84, 0x64, 0xae, 0x44, 0x2e, //0x000046c0 .quad 3333981370896602653
	0x3a, 0x30, 0x1f, 0x97, 0xdc, 0xb5, 0xa0, 0xe2, //0x000046c8 .quad -2116491865831296966
	0xd2, 0xe5, 0x89, 0xd2, 0xfe, 0xec, 0xea, 0x5c, //0x000046d0 .quad 6695424375237764562
	0x24, 0x7e, 0x73, 0xde, 0xa9, 0x71, 0xa4, 0x8d, //0x000046d8 .quad -8240336443785642460
	0x47, 0x5f, 0x2c, 0x87, 0x3e, 0xa8, 0x25, 0x74, //0x000046e0 .quad 8369280469047205703
	0xad, 0x5d, 0x10, 0x56, 0x14, 0x8e, 0x0d, 0xb1, //0x000046e8 .quad -5688734536304665171
	0x19, 0x77, 0xf7, 0x28, 0x4e, 0x12, 0x2f, 0xd1, //0x000046f0 .quad -3373457468973156583
	0x18, 0x75, 0x94, 0x6b, 0x99, 0xf1, 0x50, 0xdd, //0x000046f8 .quad -2499232151953443560
	0x6f, 0xaa, 0x9a, 0xd9, 0x70, 0x6b, 0xbd, 0x82, //0x00004700 .quad -9025939945749304721
	0x2f, 0xc9, 0x3c, 0xe3, 0xff, 0x96, 0x52, 0x8a, //0x00004708 .quad -8479549122611984081
	0x0b, 0x55, 0x01, 0x10, 0x4d, 0xc6, 0x6c, 0x63, //0x00004710 .quad 7164319141522920715
	0x7b, 0xfb, 0x0b, 0xdc, 0xbf, 0x3c, 0xe7, 0xac, //0x00004718 .quad -5987750384837592197
	0x4e, 0xaa, 0x01, 0x54, 0xe0, 0xf7, 0x47, 0x3c, //0x00004720 .quad 4343712908476262990
	0x5a, 0xfa, 0x0e, 0xd3, 0xef, 0x0b, 0x21, 0xd8, //0x00004728 .quad -2873001962619602342
	0x71, 0x0a, 0x81, 0x34, 0xec, 0xfa, 0xac, 0x65, //0x00004730 .quad 7326506586225052273
	0x78, 0x5c, 0xe9, 0xe3, 0x75, 0xa7, 0x14, 0x87, //0x00004738 .quad -8713155254278333320
	0x0d, 0x4d, 0xa1, 0x41, 0xa7, 0x39, 0x18, 0x7f, //0x00004740 .quad 9158133232781315341
	0x96, 0xb3, 0xe3, 0x5c, 0x53, 0xd1, 0xd9, 0xa8, //0x00004748 .quad -6279758049420528746
	0x50, 0xa0, 0x09, 0x12, 0x11, 0x48, 0xde, 0x1e, //0x00004750 .quad 2224294504121868368
	0x7c, 0xa0, 0x1c, 0x34, 0xa8, 0x45, 0x10, 0xd3, //0x00004758 .quad -3238011543348273028
	0x32, 0x04, 0x46, 0xab, 0x0a, 0xed, 0x4a, 0x93, //0x00004760 .quad -7833187971778608078
	0x4d, 0xe4, 0x91, 0x20, 0x89, 0x2b, 0xea, 0x83, //0x00004768 .quad -8941286242233752499
	0x3f, 0x85, 0x17, 0x56, 0x4d, 0xa8, 0x1d, 0xf8, //0x00004770 .quad -568112927868484289
	0x60, 0x5d, 0xb6, 0x68, 0x6b, 0xb6, 0xe4, 0xa4, //0x00004778 .quad -6564921784364802720
	0x8e, 0x66, 0x9d, 0xab, 0x60, 0x12, 0x25, 0x36, //0x00004780 .quad 3901544858591782542
	0xb9, 0xf4, 0xe3, 0x42, 0x06, 0xe4, 0x1d, 0xce, //0x00004788 .quad -3594466212028615495
	0x19, 0x60, 0x42, 0x6b, 0x7c, 0x2b, 0xd7, 0xc1, //0x00004790 .quad -4479063491021217767
	0xf3, 0x78, 0xce, 0xe9, 0x83, 0xae, 0xd2, 0x80, //0x00004798 .quad -9164070410158966541
	0x1f, 0xf8, 0x12, 0x86, 0x5b, 0xf6, 0x4c, 0xb2, //0x000047a0 .quad -5598829363776522209
	0x30, 0x17, 0x42, 0xe4, 0x24, 0x5a, 0x07, 0xa1, //0x000047a8 .quad -6843401994271320272
	0x27, 0xb6, 0x97, 0x67, 0xf2, 0x33, 0xe0, 0xde, //0x000047b0 .quad -2386850686293264857
	0xfc, 0x9c, 0x52, 0x1d, 0xae, 0x30, 0x49, 0xc9, //0x000047b8 .quad -3942566474411762436
	0xb1, 0xa3, 0x7d, 0x01, 0xef, 0x40, 0x98, 0x16, //0x000047c0 .quad 1628122660560806833
	0x3c, 0x44, 0xa7, 0xa4, 0xd9, 0x7c, 0x9b, 0xfb, //0x000047c8 .quad -316522074587315140
	0x4e, 0x86, 0xee, 0x60, 0x95, 0x28, 0x1f, 0x8e, //0x000047d0 .quad -8205795374004271538
	0xa5, 0x8a, 0xe8, 0x06, 0x08, 0x2e, 0x41, 0x9d, //0x000047d8 .quad -7115355324258153819
	0xe2, 0x27, 0x2a, 0xb9, 0xba, 0xf2, 0xa6, 0xf1, //0x000047e0 .quad -1033872180650563614
	0x4e, 0xad, 0xa2, 0x08, 0x8a, 0x79, 0x91, 0xc4, //0x000047e8 .quad -4282508136895304370
	0xdb, 0xb1, 0x74, 0x67, 0x69, 0xaf, 0x10, 0xae, //0x000047f0 .quad -5904026244240592421
	0xa2, 0x58, 0xcb, 0x8a, 0xec, 0xd7, 0xb5, 0xf5, //0x000047f8 .quad -741449152691742558
	0x29, 0xef, 0xa8, 0xe0, 0xa1, 0x6d, 0xca, 0xac, //0x00004800 .quad -5995859411864064215
	0x65, 0x17, 0xbf, 0xd6, 0xf3, 0xa6, 0x91, 0x99, //0x00004808 .quad -7380934748073420955
	0xf3, 0x2a, 0xd3, 0x58, 0x0a, 0x09, 0xfd, 0x17, //0x00004810 .quad 1728547772024695539
	0x3f, 0xdd, 0x6e, 0xcc, 0xb0, 0x10, 0xf6, 0xbf, //0x00004818 .quad -4614482416664388289
	0xb0, 0xf5, 0x07, 0xef, 0x4c, 0x4b, 0xfc, 0xdd, //0x00004820 .quad -2451001303396518480
	0x8e, 0x94, 0x8a, 0xff, 0xdc, 0x94, 0xf3, 0xef, //0x00004828 .quad -1156417002403097458
	0x8e, 0xf9, 0x64, 0x15, 0x10, 0xaf, 0xbd, 0x4a, //0x00004830 .quad 5385653213018257806
	0xd9, 0x9c, 0xb6, 0x1f, 0x0a, 0x3d, 0xf8, 0x95, //0x00004838 .quad -7640289654143017767
	0xf1, 0x37, 0xbe, 0x1a, 0xd4, 0x1a, 0x6d, 0x9d, //0x00004840 .quad -7102991539009341455
	0x0f, 0x44, 0xa4, 0xa7, 0x4c, 0x4c, 0x76, 0xbb, //0x00004848 .quad -4938676049251384305
	0xed, 0xc5, 0x6d, 0x21, 0x89, 0x61, 0xc8, 0x84, //0x00004850 .quad -8878739423761676819
	0x13, 0x55, 0x8d, 0xd1, 0x5f, 0xdf, 0x53, 0xea, //0x00004858 .quad -1561659043136842477
	0xb4, 0x9b, 0xe4, 0xb4, 0xf5, 0x3c, 0xfd, 0x32, //0x00004860 .quad 3674159897003727796
	0x2c, 0x55, 0xf8, 0xe2, 0x9b, 0x6b, 0x74, 0x92, //0x00004868 .quad -7893565929601608404
	0xa1, 0xc2, 0x1d, 0x22, 0x33, 0x8c, 0xbc, 0x3f, //0x00004870 .quad 4592699871254659745
	0x77, 0x6a, 0xb6, 0xdb, 0x82, 0x86, 0x11, 0xb7, //0x00004878 .quad -5255271393574622601
	0x4a, 0x33, 0xa5, 0xea, 0x3f, 0xaf, 0xab, 0x0f, //0x00004880 .quad 1129188820640936778
	0x15, 0x05, 0xa4, 0x92, 0x23, 0xe8, 0xd5, 0xe4, //0x00004888 .quad -1957403223540890347
	0x0e, 0x40, 0xa7, 0xf2, 0x87, 0x4d, 0xcb, 0x29, //0x00004890 .quad 3011586022114279438
	0x2d, 0x83, 0xa6, 0x3b, 0x16, 0xb1, 0x05, 0x8f, //0x00004898 .quad -8140906042354138323
	0x12, 0x10, 0x51, 0xef, 0xe9, 0x20, 0x3e, 0x74, //0x000048a0 .quad 8376168546070237202
	0xf8, 0x23, 0x90, 0xca, 0x5b, 0x1d, 0xc7, 0xb2, //0x000048a8 .quad -5564446534515285000
	0x16, 0x54, 0x25, 0x6b, 0x24, 0xa9, 0x4d, 0x91, //0x000048b0 .quad -7976533391121755114
	0xf6, 0x2c, 0x34, 0xbd, 0xb2, 0xe4, 0x78, 0xdf, //0x000048b8 .quad -2343872149716718346
	0x8e, 0x54, 0xf7, 0xc2, 0xb6, 0x89, 0xd0, 0x1a, //0x000048c0 .quad 1932195658189984910
	0x1a, 0x9c, 0x40, 0xb6, 0xef, 0x8e, 0xab, 0x8b, //0x000048c8 .quad -8382449121214030822
	0xb1, 0x29, 0xb5, 0x73, 0x24, 0xac, 0x84, 0xa1, //0x000048d0 .quad -6808127464117294671
	0x20, 0xc3, 0xd0, 0xa3, 0xab, 0x72, 0x96, 0xae, //0x000048d8 .quad -5866375383090150624
	0x1e, 0x74, 0xa2, 0x90, 0x2d, 0xd7, 0xe5, 0xc9, //0x000048e0 .quad -3898473311719230434
	0xe8, 0xf3, 0xc4, 0x8c, 0x56, 0x0f, 0x3c, 0xda, //0x000048e8 .quad -2721283210435300376
	0x92, 0x88, 0x65, 0x7a, 0x7c, 0xa6, 0x2f, 0x7e, //0x000048f0 .quad 9092669226243950738
	0x71, 0x18, 0xfb, 0x17, 0x96, 0x89, 0x65, 0x88, //0x000048f8 .quad -8618331034163144591
	0xb7, 0xea, 0xfe, 0x98, 0x1b, 0x90, 0xbb, 0xdd, //0x00004900 .quad -2469221522477225289
	0x8d, 0xde, 0xf9, 0x9d, 0xfb, 0xeb, 0x7e, 0xaa, //0x00004908 .quad -6161227774276542835
	0x65, 0xa5, 0x3e, 0x7f, 0x22, 0x74, 0x2a, 0x55, //0x00004910 .quad 6136845133758244197
	0x31, 0x56, 0x78, 0x85, 0xfa, 0xa6, 0x1e, 0xd5, //0x00004918 .quad -3089848699418290639
	0x5f, 0x27, 0x87, 0x8f, 0x95, 0x88, 0x3a, 0xd5, //0x00004920 .quad -3082000819042179233
	0xde, 0x35, 0x6b, 0x93, 0x5c, 0x28, 0x33, 0x85, //0x00004928 .quad -8848684464777513506
	0x37, 0xf1, 0x68, 0xf3, 0xba, 0x2a, 0x89, 0x8a, //0x00004930 .quad -8464187042230111945
	0x56, 0x03, 0x46, 0xb8, 0x73, 0xf2, 0x7f, 0xa6, //0x00004938 .quad -6449169562544503978
	0x85, 0x2d, 0x43, 0xb0, 0x69, 0x75, 0x2b, 0x2d, //0x00004940 .quad 3254824252494523781
	0x2c, 0x84, 0x57, 0xa6, 0x10, 0xef, 0x1f, 0xd0, //0x00004948 .quad -3449775934753242068
	0x73, 0xfc, 0x29, 0x0e, 0x62, 0x29, 0x3b, 0x9c, //0x00004950 .quad -7189106879045698445
	0x9b, 0xb2, 0xf6, 0x67, 0x6a, 0xf5, 0x13, 0x82, //0x00004958 .quad -9073638986861858149
	0x8f, 0x7b, 0xb4, 0x91, 0xba, 0xf3, 0x49, 0x83, //0x00004960 .quad -8986383598807123057
	0x42, 0x5f, 0xf4, 0x01, 0xc5, 0xf2, 0x98, 0xa2, //0x00004968 .quad -6730362715149934782
	0x73, 0x9a, 0x21, 0x36, 0xa9, 0x70, 0x1c, 0x24, //0x00004970 .quad 2602078556773259891
	0x13, 0x77, 0x71, 0x42, 0x76, 0x2f, 0x3f, 0xcb, //0x00004978 .quad -3801267375510030573
	0x10, 0x01, 0xaa, 0x83, 0xd3, 0x8c, 0x23, 0xed, //0x00004980 .quad -1359087822460813040
	0xd7, 0xd4, 0x0d, 0xd3, 0x53, 0xfb, 0x0e, 0xfe, //0x00004988 .quad -139898200960150313
	0xaa, 0x40, 0x4a, 0x32, 0x04, 0x38, 0x36, 0xf4, //0x00004990 .quad -849429889038008150
	0x06, 0xa5, 0xe8, 0x63, 0x14, 0x5d, 0xc9, 0x9e, //0x00004998 .quad -7004965403241175802
	0xd5, 0xd0, 0xdc, 0x3e, 0x05, 0xc6, 0x43, 0xb1, //0x000049a0 .quad -5673473379724898091
	0x48, 0xce, 0xe2, 0x7c, 0x59, 0xb4, 0x7b, 0xc6, //0x000049a8 .quad -4144520735624081848
	0x0a, 0x05, 0x94, 0x8e, 0x86, 0xb7, 0x94, 0xdd, //0x000049b0 .quad -2480155706228734710
	0xda, 0x81, 0x1b, 0xdc, 0x6f, 0xa1, 0x1a, 0xf8, //0x000049b8 .quad -568964901102714406
	0x26, 0x83, 0x1c, 0x19, 0xb4, 0xf2, 0x7c, 0xca, //0x000049c0 .quad -3855940325606653146
	0x28, 0x31, 0x91, 0xe9, 0xe5, 0xa4, 0x10, 0x9b, //0x000049c8 .quad -7273132090830278360
	0xf0, 0xa3, 0x63, 0x1f, 0x61, 0x2f, 0x1c, 0xfd, //0x000049d0 .quad -208239388580928528
	0x72, 0x7d, 0xf5, 0x63, 0x1f, 0xce, 0xd4, 0xc1, //0x000049d8 .quad -4479729095110460046
	0xec, 0x8c, 0x3c, 0x67, 0x39, 0x3b, 0x63, 0xbc, //0x000049e0 .quad -4871985254153548564
	0xcf, 0xdc, 0xf2, 0x3c, 0xa7, 0x01, 0x4a, 0xf2, //0x000049e8 .quad -987975350460687153
	0x13, 0xd8, 0x85, 0xe0, 0x03, 0x05, 0xbe, 0xd5, //0x000049f0 .quad -3044990783845967853
	0x01, 0xca, 0x17, 0x86, 0x08, 0x41, 0x6e, 0x97, //0x000049f8 .quad -7535013621679011327
	0x18, 0x4e, 0xa7, 0xd8, 0x44, 0x86, 0x2d, 0x4b, //0x00004a00 .quad 5417133557047315992
	0x82, 0xbc, 0x9d, 0xa7, 0x4a, 0xd1, 0x49, 0xbd, //0x00004a08 .quad -4807081008671376254
	0x9e, 0x21, 0xd1, 0x0e, 0xd6, 0xe7, 0xf8, 0xdd, //0x00004a10 .quad -2451955090545630818
	0xa2, 0x2b, 0x85, 0x51, 0x9d, 0x45, 0x9c, 0xec, //0x00004a18 .quad -1397165242411832414
	0x03, 0xb5, 0x42, 0xc9, 0xe5, 0x90, 0xbb, 0xca, //0x00004a20 .quad -3838314940804713213
	0x45, 0x3b, 0xf3, 0x52, 0x82, 0xab, 0xe1, 0x93, //0x00004a28 .quad -7790757304148477115
	0x43, 0x62, 0x93, 0x3b, 0x1f, 0x75, 0x6a, 0x3d, //0x00004a30 .quad 4425478360848884291
	0x17, 0x0a, 0xb0, 0xe7, 0x62, 0x16, 0xda, 0xb8, //0x00004a38 .quad -5126760611758208489
	0xd4, 0x3a, 0x78, 0x0a, 0x67, 0x12, 0xc5, 0x0c, //0x00004a40 .quad 920161932633717460
	0x9d, 0x0c, 0x9c, 0xa1, 0xfb, 0x9b, 0x10, 0xe7, //0x00004a48 .quad -1796764746270372707
	0xc5, 0x24, 0x8b, 0x66, 0x80, 0x2b, 0xfb, 0x27, //0x00004a50 .quad 2880944217109767365
	0xe2, 0x87, 0x01, 0x45, 0x7d, 0x61, 0x6a, 0x90, //0x00004a58 .quad -8040506994060064798
	0xf6, 0xed, 0x2d, 0x80, 0x60, 0xf6, 0xf9, 0xb1, //0x00004a60 .quad -5622191765467566602
	0xda, 0xe9, 0x41, 0x96, 0xdc, 0xf9, 0x84, 0xb4, //0x00004a68 .quad -5438947724147693094
	0x73, 0x69, 0x39, 0xa0, 0xf8, 0x73, 0x78, 0x5e, //0x00004a70 .quad 6807318348447705459
	0x51, 0x64, 0xd2, 0xbb, 0x53, 0x38, 0xa6, 0xe1, //0x00004a78 .quad -2186998636757228463
	0xe8, 0xe1, 0x23, 0x64, 0x7b, 0x48, 0x0b, 0xdb, //0x00004a80 .quad -2662955059861265944
	0xb2, 0x7e, 0x63, 0x55, 0x34, 0xe3, 0x07, 0x8d, //0x00004a88 .quad -8284403175614349646
	0x62, 0xda, 0x2c, 0x3d, 0x9a, 0x1a, 0xce, 0x91, //0x00004a90 .quad -7940379843253970334
	0x5f, 0x5e, 0xbc, 0x6a, 0x01, 0xdc, 0x49, 0xb0, //0x00004a98 .quad -5743817951090549153
	0xfb, 0x10, 0x78, 0xcc, 0x40, 0xa1, 0x41, 0x76, //0x00004aa0 .quad 8521269269642088699
	0xf7, 0x75, 0x6b, 0xc5, 0x01, 0x53, 0x5c, 0xdc, //0x00004aa8 .quad -2568086420435798537
	0x9d, 0x0a, 0xcb, 0x7f, 0xc8, 0x04, 0xe9, 0xa9, //0x00004ab0 .quad -6203421752542164323
	0xba, 0x29, 0x63, 0x1b, 0xe1, 0xb3, 0xb9, 0x89, //0x00004ab8 .quad -8522583040413455942
	0x44, 0xcd, 0xbd, 0x9f, 0xfa, 0x45, 0x63, 0x54, //0x00004ac0 .quad 6080780864604458308
	0x29, 0xf4, 0x3b, 0x62, 0xd9, 0x20, 0x28, 0xac, //0x00004ac8 .quad -6041542782089432023
	0x95, 0x40, 0xad, 0x47, 0x79, 0x17, 0x7c, 0xa9, //0x00004ad0 .quad -6234081974526590827
	0x33, 0xf1, 0xca, 0xba, 0x0f, 0x29, 0x32, 0xd7, //0x00004ad8 .quad -2940242459184402125
	0x5d, 0x48, 0xcc, 0xcc, 0xab, 0x8e, 0xed, 0x49, //0x00004ae0 .quad 5327070802775656541
	0xc0, 0xd6, 0xbe, 0xd4, 0xa9, 0x59, 0x7f, 0x86, //0x00004ae8 .quad -8755180564631333184
	0x74, 0x5a, 0xff, 0xbf, 0x56, 0xf2, 0x68, 0x5c, //0x00004af0 .quad 6658838503469570676
	0x70, 0x8c, 0xee, 0x49, 0x14, 0x30, 0x1f, 0xa8, //0x00004af8 .quad -6332289687361778576
	0x11, 0x31, 0xff, 0x6f, 0xec, 0x2e, 0x83, 0x73, //0x00004b00 .quad 8323548129336963345
	0x8c, 0x2f, 0x6a, 0x5c, 0x19, 0xfc, 0x26, 0xd2, //0x00004b08 .quad -3303676090774835316
	0xab, 0x7e, 0xff, 0xc5, 0x53, 0xfd, 0x31, 0xc8, //0x00004b10 .quad -4021154456019173717
	0xb7, 0x5d, 0xc2, 0xd9, 0x8f, 0x5d, 0x58, 0x83, //0x00004b18 .quad -8982326584375353929
	0x55, 0x5e, 0x7f, 0xb7, 0xa8, 0x7c, 0x3e, 0xba, //0x00004b20 .quad -5026443070023967147
	0x25, 0xf5, 0x32, 0xd0, 0xf3, 0x74, 0x2e, 0xa4, //0x00004b28 .quad -6616222212041804507
	0xeb, 0x35, 0x5f, 0xe5, 0xd2, 0x1b, 0xce, 0x28, //0x00004b30 .quad 2940318199324816875
	0x6f, 0xb2, 0x3f, 0xc4, 0x30, 0x12, 0x3a, 0xcd, //0x00004b38 .quad -3658591746624867729
	0xb3, 0x81, 0x5b, 0xcf, 0x63, 0xd1, 0x80, 0x79, //0x00004b40 .quad 8755227902219092403
	0x85, 0xcf, 0xa7, 0x7a, 0x5e, 0x4b, 0x44, 0x80, //0x00004b48 .quad -9204148869281624187
	0x1f, 0x62, 0x32, 0xc3, 0xbc, 0x05, 0xe1, 0xd7, //0x00004b50 .quad -2891023177508298209
	0x66, 0xc3, 0x51, 0x19, 0x36, 0x5e, 0x55, 0xa0, //0x00004b58 .quad -6893500068174642330
	0xa7, 0xfa, 0xfe, 0xf3, 0x2b, 0x47, 0xd9, 0x8d, //0x00004b60 .quad -8225464990312760665
	0x40, 0x34, 0xa6, 0x9f, 0xc3, 0xb5, 0x6a, 0xc8, //0x00004b68 .quad -4005189066790915008
	0x51, 0xb9, 0xfe, 0xf0, 0xf6, 0x98, 0x4f, 0xb1, //0x00004b70 .quad -5670145219463562927
	0x50, 0xc1, 0x8f, 0x87, 0x34, 0x63, 0x85, 0xfa, //0x00004b78 .quad -394800315061255856
	0xd3, 0x33, 0x9f, 0x56, 0x9a, 0xbf, 0xd1, 0x6e, //0x00004b80 .quad 7985374283903742931
	0xd2, 0xd8, 0xb9, 0xd4, 0x00, 0x5e, 0x93, 0x9c, //0x00004b88 .quad -7164279224554366766
	0xc8, 0x00, 0x47, 0xec, 0x80, 0x2f, 0x86, 0x0a, //0x00004b90 .quad 758345818024902856
	0x07, 0x4f, 0xe8, 0x09, 0x81, 0x35, 0xb8, 0xc3, //0x00004b98 .quad -4343663012265570553
	0xfa, 0xc0, 0x58, 0x27, 0x61, 0xbb, 0x27, 0xcd, //0x00004ba0 .quad -3663753745896259334
	0xc8, 0x62, 0x62, 0x4c, 0xe1, 0x42, 0xa6, 0xf4, //0x00004ba8 .quad -817892746904575288
	0x9c, 0x78, 0x97, 0xb8, 0x1c, 0xd5, 0x38, 0x80, //0x00004bb0 .quad -9207375118826243940
	0xbd, 0x7d, 0xbd, 0xcf, 0xcc, 0xe9, 0xe7, 0x98, //0x00004bb8 .quad -7428711994456441411
	0xc3, 0x56, 0xbd, 0xe6, 0x63, 0x0a, 0x47, 0xe0, //0x00004bc0 .quad -2285846861678029117
	0x2c, 0xdd, 0xac, 0x03, 0x40, 0xe4, 0x21, 0xbf, //0x00004bc8 .quad -4674203974643163860
	0x74, 0xac, 0x6c, 0xe0, 0xfc, 0xcc, 0x58, 0x18, //0x00004bd0 .quad 1754377441329851508
	0x78, 0x14, 0x98, 0x04, 0x50, 0x5d, 0xea, 0xee, //0x00004bd8 .quad -1231068949876566920
	0xc8, 0xeb, 0x43, 0x0c, 0x1e, 0x80, 0x37, 0x0f, //0x00004be0 .quad 1096485900831157192
	0xcb, 0x0c, 0xdf, 0x02, 0x52, 0x7a, 0x52, 0x95, //0x00004be8 .quad -7686947121313936181
	0xba, 0xe6, 0x54, 0x8f, 0x25, 0x60, 0x05, 0xd3, //0x00004bf0 .quad -3241078642388441414
	0xfd, 0xcf, 0x96, 0x83, 0xe6, 0x18, 0xa7, 0xba, //0x00004bf8 .quad -4996997883215032323
	0x69, 0x20, 0x2a, 0xf3, 0x2e, 0xb8, 0xc6, 0x47, //0x00004c00 .quad 5172023733869224041
	0xfd, 0x83, 0x7c, 0x24, 0x20, 0xdf, 0x50, 0xe9, //0x00004c08 .quad -1634561335591402499
	0x41, 0x54, 0xfa, 0x57, 0x1d, 0x33, 0xdc, 0x4c, //0x00004c10 .quad 5538357842881958977
	0x7e, 0xd2, 0xcd, 0x16, 0x74, 0x8b, 0xd2, 0x91, //0x00004c18 .quad -7939129862385708418
	0x52, 0xe9, 0xf8, 0xad, 0xe4, 0x3f, 0x13, 0xe0, //0x00004c20 .quad -2300424733252327086
	0x1d, 0x47, 0x81, 0x1c, 0x51, 0x2e, 0x47, 0xb6, //0x00004c28 .quad -5312226309554747619
	0xa6, 0x23, 0x77, 0xd9, 0xdd, 0x0f, 0x18, 0x58, //0x00004c30 .quad 6347841120289366950
	0xe5, 0x98, 0xa1, 0x63, 0xe5, 0xf9, 0xd8, 0xe3, //0x00004c38 .quad -2028596868516046619
	0x48, 0x76, 0xea, 0xa7, 0xea, 0x09, 0x0f, 0x57, //0x00004c40 .quad 6273243709394548296
	0x8f, 0xff, 0x44, 0x5e, 0x2f, 0x9c, 0x67, 0x8e, //0x00004c48 .quad -8185402070463610993
	0xda, 0x13, 0xe5, 0x51, 0x65, 0xcc, 0xd2, 0x2c, //0x00004c50 .quad 3229868618315797466
	0x73, 0x3f, 0xd6, 0x35, 0x3b, 0x83, 0x01, 0xb2, //0x00004c58 .quad -5620066569652125837
	0xd1, 0x58, 0x5e, 0xa6, 0x7e, 0x7f, 0x07, 0xf8, //0x00004c60 .quad -574350245532641071
	0x4f, 0xcf, 0x4b, 0x03, 0x0a, 0xe4, 0x81, 0xde, //0x00004c68 .quad -2413397193637769393
	0x82, 0xf7, 0xfa, 0x27, 0xaf, 0xaf, 0x04, 0xfb, //0x00004c70 .quad -358968903457900670
	0x91, 0x61, 0x0f, 0x42, 0x86, 0x2e, 0x11, 0x8b, //0x00004c78 .quad -8425902273664687727
	0x63, 0xb5, 0xf9, 0xf1, 0x9a, 0xdb, 0xc5, 0x79, //0x00004c80 .quad 8774660907532399971
	0xf6, 0x39, 0x93, 0xd2, 0x27, 0x7a, 0xd5, 0xad, //0x00004c88 .quad -5920691823653471754
	0xbc, 0x22, 0x78, 0xae, 0x81, 0x52, 0x37, 0x18, //0x00004c90 .quad 1744954097560724156
	0x74, 0x08, 0x38, 0xc7, 0xb1, 0xd8, 0x4a, 0xd9, //0x00004c98 .quad -2789178761139451788
	0xb5, 0x15, 0x0b, 0x0d, 0x91, 0x93, 0x22, 0x8f, //0x00004ca0 .quad -8132775725879323211
	0x48, 0x05, 0x83, 0x1c, 0x6f, 0xc7, 0xce, 0x87, //0x00004ca8 .quad -8660765753353239224
	0x22, 0xdb, 0x4d, 0x50, 0x75, 0x38, 0xeb, 0xb2, //0x00004cb0 .quad -5554283638921766110
	0x9a, 0xc6, 0xa3, 0xe3, 0x4a, 0x79, 0xc2, 0xa9, //0x00004cb8 .quad -6214271173264161126
	0xeb, 0x51, 0x61, 0xa4, 0x92, 0x06, 0xa6, 0x5f, //0x00004cc0 .quad 6892203506629956075
	0x41, 0xb8, 0x8c, 0x9c, 0x9d, 0x17, 0x33, 0xd4, //0x00004cc8 .quad -3156152948152813503
	0x33, 0xd3, 0xbc, 0xa6, 0x1b, 0xc4, 0xc7, 0xdb, //0x00004cd0 .quad -2609901835997359309
	0x28, 0xf3, 0xd7, 0x81, 0xc2, 0xee, 0x9f, 0x84, //0x00004cd8 .quad -8890124620236590296
	0x00, 0x08, 0x6c, 0x90, 0x22, 0xb5, 0xb9, 0x12, //0x00004ce0 .quad 1349308723430688768
	0xf3, 0xef, 0x4d, 0x22, 0x73, 0xea, 0xc7, 0xa5, //0x00004ce8 .quad -6500969756868349965
	0x00, 0x0a, 0x87, 0x34, 0x6b, 0x22, 0x68, 0xd7, //0x00004cf0 .quad -2925050114139026944
	0xef, 0x6b, 0xe1, 0xea, 0x0f, 0xe5, 0x39, 0xcf, //0x00004cf8 .quad -3514526177658049553
	0x40, 0x66, 0xd4, 0x00, 0x83, 0x15, 0xa1, 0xe6, //0x00004d00 .quad -1828156321336891840
	0x75, 0xe3, 0xcc, 0xf2, 0x29, 0x2f, 0x84, 0x81, //0x00004d08 .quad -9114107888677362827
	0xd0, 0x7f, 0x09, 0xc1, 0xe3, 0x5a, 0x49, 0x60, //0x00004d10 .quad 6938176635183661008
	0x53, 0x1c, 0x80, 0x6f, 0xf4, 0x3a, 0xe5, 0xa1, //0x00004d18 .quad -6780948842419315629
	0xc4, 0xdf, 0x4b, 0xb1, 0x9c, 0xb1, 0x5b, 0x38, //0x00004d20 .quad 4061034775552188356
	0x68, 0x23, 0x60, 0x8b, 0xb1, 0x89, 0x5e, 0xca, //0x00004d28 .quad -3864500034596756632
	0xb5, 0xd7, 0x9e, 0xdd, 0x03, 0x9e, 0x72, 0x46, //0x00004d30 .quad 5076293469440235445
	0x42, 0x2c, 0x38, 0xee, 0x1d, 0x2c, 0xf6, 0xfc, //0x00004d38 .quad -218939024818557886
	0xd1, 0x46, 0x83, 0x6a, 0xc2, 0xa2, 0x07, 0x6c, //0x00004d40 .quad 7784369436827535057
	0xa9, 0x1b, 0xe3, 0xb4, 0x92, 0xdb, 0x19, 0x9e, //0x00004d48 .quad -7054365918152680535
	0x85, 0x18, 0x24, 0x05, 0x73, 0x8b, 0x09, 0xc7, //0x00004d50 .quad -4104596259247744891
	0x93, 0xe2, 0x1b, 0x62, 0x77, 0x52, 0xa0, 0xc5, //0x00004d58 .quad -4206271379263462765
	0xa7, 0x1e, 0x6d, 0xc6, 0x4f, 0xee, 0xcb, 0xb8, //0x00004d60 .quad -5130745324059681113
	0x38, 0xdb, 0xa2, 0x3a, 0x15, 0x67, 0x08, 0xf7, //0x00004d68 .quad -646153205651940552
	0x28, 0x33, 0x04, 0xdc, 0xf1, 0x74, 0x7f, 0x73, //0x00004d70 .quad 8322499218531169064
	0x03, 0xc9, 0xa5, 0x44, 0x6d, 0x40, 0x65, 0x9a, //0x00004d78 .quad -7321374781173544701
	0xf2, 0x3f, 0x05, 0x53, 0x2e, 0x52, 0x5f, 0x50, //0x00004d80 .quad 5791438004736573426
	0x44, 0x3b, 0xcf, 0x95, 0x88, 0x90, 0xfe, 0xc0, //0x00004d88 .quad -4540032458039542972
	0xef, 0x8f, 0xc6, 0xe7, 0xb9, 0x26, 0x77, 0x64, //0x00004d90 .quad 7239297505920716783
	0x15, 0x0a, 0x43, 0xbb, 0xaa, 0x34, 0x3e, 0xf1, //0x00004d98 .quad -1063354554122040811
	0xf5, 0x19, 0xdc, 0x30, 0x34, 0x78, 0xca, 0x5e, //0x00004da0 .quad 6830403950414141941
	0x4d, 0xe6, 0x09, 0xb5, 0xea, 0xe0, 0xc6, 0x96, //0x00004da8 .quad -7582125623967357363
	0x72, 0x20, 0x13, 0x3d, 0x41, 0x16, 0x7d, 0xb6, //0x00004db0 .quad -5297053117264486286
	0xe0, 0x5f, 0x4c, 0x62, 0x25, 0x99, 0x78, 0xbc, //0x00004db8 .quad -4865971011531808800
	0x8f, 0xe8, 0x57, 0x8c, 0xd1, 0x5b, 0x1c, 0xe4, //0x00004dc0 .quad -2009630378153219953
	0xd8, 0x77, 0xdf, 0xba, 0x6e, 0xbf, 0x96, 0xeb, //0x00004dc8 .quad -1470777745987373096
	0x59, 0xf1, 0xb6, 0xf7, 0x62, 0xb9, 0x91, 0x8e, //0x00004dd0 .quad -8173548013986844327
	0xe7, 0xaa, 0xcb, 0x34, 0xa5, 0x37, 0x3e, 0x93, //0x00004dd8 .quad -7836765118883190041
	0xb0, 0xad, 0xa4, 0xb5, 0xbb, 0x27, 0x36, 0x72, //0x00004de0 .quad 8229809056225996208
	0xa1, 0x95, 0xfe, 0x81, 0x8e, 0xc5, 0x0d, 0xb8, //0x00004de8 .quad -5184270380176599647
	0x1c, 0xd9, 0x0d, 0xa3, 0xaa, 0xb1, 0xc3, 0xce, //0x00004df0 .quad -3547796734999668452
	0x09, 0x3b, 0x7e, 0x22, 0xf2, 0x36, 0x11, 0xe6, //0x00004df8 .quad -1868651956793361655
	0xb1, 0xa7, 0xe8, 0xa5, 0x0a, 0x4f, 0x3a, 0x21, //0x00004e00 .quad 2394313059052595121
	0xe6, 0xe4, 0x8e, 0x55, 0x57, 0xc2, 0xca, 0x8f, //0x00004e08 .quad -8085436500636932890
	0x9d, 0xd1, 0x62, 0x4f, 0xcd, 0xe2, 0x88, 0xa9, //0x00004e10 .quad -6230480713039031907
	0x1f, 0x9e, 0xf2, 0x2a, 0xed, 0x72, 0xbd, 0xb3, //0x00004e18 .quad -5495109607368778209
	0x05, 0x86, 0x3b, 0xa3, 0x80, 0x1b, 0xeb, 0x93, //0x00004e20 .quad -7788100891298789883
	0xa7, 0x45, 0xaf, 0x75, 0xa8, 0xcf, 0xac, 0xe0, //0x00004e28 .quad -2257200990783584857
	0xc3, 0x33, 0x05, 0x66, 0x30, 0xf1, 0x72, 0xbc, //0x00004e30 .quad -4867563057061743677
	0x88, 0x8b, 0x8d, 0x49, 0xc9, 0x01, 0x6c, 0x8c, //0x00004e38 .quad -8328279646880822392
	0xb4, 0x80, 0x86, 0x7f, 0x7c, 0xad, 0x8f, 0xeb, //0x00004e40 .quad -1472767802899791692
	0x6a, 0xee, 0xf0, 0x9b, 0x3b, 0x02, 0x87, 0xaf, //0x00004e48 .quad -5798663540173640086
	0xe1, 0x20, 0x68, 0x9f, 0xdb, 0x98, 0x73, 0xa6, //0x00004e50 .quad -6452645772052127519
	0x05, 0x2a, 0xed, 0x82, 0xca, 0xc2, 0x68, 0xdb, //0x00004e58 .quad -2636643406789662203
	0x8c, 0x14, 0xa1, 0x43, 0x89, 0x3f, 0x08, 0x88, //0x00004e60 .quad -8644589625959967604
	0x43, 0x3a, 0xd4, 0x91, 0xbe, 0x79, 0x21, 0x89, //0x00004e68 .quad -8565431156884620733
	0xb0, 0x59, 0x89, 0x94, 0x6b, 0x4f, 0x0a, 0x6a, //0x00004e70 .quad 7641007041259592112
	0xd4, 0x48, 0x49, 0x36, 0x2e, 0xd8, 0x69, 0xab, //0x00004e78 .quad -6095102927678388012
	0x1c, 0xb0, 0xab, 0x79, 0x46, 0xe3, 0x8c, 0x84, //0x00004e80 .quad -8895485272135061476
	0x09, 0x9b, 0xdb, 0xc3, 0x39, 0x4e, 0x44, 0xd6, //0x00004e88 .quad -3007192641170597111
	0x11, 0x4e, 0x0b, 0x0c, 0x0c, 0x0e, 0xd8, 0xf2, //0x00004e90 .quad -947992276657025519
	0xe5, 0x40, 0x69, 0x1a, 0xe4, 0xb0, 0xea, 0x85, //0x00004e98 .quad -8797024428372705051
	0x95, 0x21, 0x0e, 0x0f, 0x8f, 0x11, 0x8e, 0x6f, //0x00004ea0 .quad 8038381691033493909
	0x1f, 0x91, 0x03, 0x21, 0x1d, 0x5d, 0x65, 0xa7, //0x00004ea8 .quad -6384594517038493409
	0xfb, 0xa9, 0xd1, 0xd2, 0xf2, 0x95, 0x71, 0x4b, //0x00004eb0 .quad 5436291095364479483
	0x67, 0x75, 0x44, 0x69, 0x64, 0xb4, 0x3e, 0xd1, //0x00004eb8 .quad -3369057127870728857
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ec0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	//0x00004ed0 .p2align 4, 0x00
	//0x00004ed0 _POW_TAB
	0x01, 0x00, 0x00, 0x00, //0x00004ed0 .long 1
	0x03, 0x00, 0x00, 0x00, //0x00004ed4 .long 3
	0x06, 0x00, 0x00, 0x00, //0x00004ed8 .long 6
	0x09, 0x00, 0x00, 0x00, //0x00004edc .long 9
	0x0d, 0x00, 0x00, 0x00, //0x00004ee0 .long 13
	0x10, 0x00, 0x00, 0x00, //0x00004ee4 .long 16
	0x13, 0x00, 0x00, 0x00, //0x00004ee8 .long 19
	0x17, 0x00, 0x00, 0x00, //0x00004eec .long 23
	0x1a, 0x00, 0x00, 0x00, //0x00004ef0 .long 26
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ef4 .p2align 4, 0x00
	//0x00004f00 _LSHIFT_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f00 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f10 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f20 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f30 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f40 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f50 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f60 QUAD $0x0000000000000000  // .space 8, '\x00\x00\x00\x00\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00004f68 .long 1
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f6c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004f9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004fac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004fbc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00004fcc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00004fd0 .long 1
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004fd4 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004fe4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ff4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005004 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005014 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005024 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005034 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00005038 .long 1
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000503c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000504c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000505c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000506c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000507c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000508c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000509c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x000050a0 .long 2
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050a4 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000050f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005104 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x00005108 .long 2
	0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000510c QUAD $0x0000000035323133; QUAD $0x0000000000000000  // .asciz 16, '3125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000511c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000512c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000513c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000514c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000515c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000516c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x00005170 .long 2
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005174 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005184 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005194 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000051d4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x000051d8 .long 3
	0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051dc QUAD $0x0000003532313837; QUAD $0x0000000000000000  // .asciz 16, '78125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000051fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000520c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000521c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000522c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000523c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x00005240 .long 3
	0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005244 QUAD $0x0000353236303933; QUAD $0x0000000000000000  // .asciz 16, '390625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005254 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005264 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005274 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005284 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005294 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000052a4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x000052a8 .long 3
	0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000052ac QUAD $0x0035323133353931; QUAD $0x0000000000000000  // .asciz 16, '1953125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000052bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000052cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000052dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000052ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000052fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000530c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00005310 .long 4
	0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005314 QUAD $0x0035323635363739; QUAD $0x0000000000000000  // .asciz 16, '9765625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005324 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005334 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005344 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005354 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005364 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005374 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00005378 .long 4
	0x34, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000537c QUAD $0x3532313832383834; QUAD $0x0000000000000000  // .asciz 16, '48828125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000538c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000539c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000053ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000053bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000053cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000053dc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x000053e0 .long 4
	0x32, 0x34, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000053e4 QUAD $0x3236303431343432; QUAD $0x0000000000000035  // .asciz 16, '244140625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000053f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005404 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005414 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005424 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005434 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005444 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00005448 .long 4
	0x31, 0x32, 0x32, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000544c QUAD $0x3133303730323231; QUAD $0x0000000000003532  // .asciz 16, '1220703125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000545c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000546c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000547c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000548c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000549c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000054ac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x000054b0 .long 5
	0x36, 0x31, 0x30, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000054b4 QUAD $0x3635313533303136; QUAD $0x0000000000003532  // .asciz 16, '6103515625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000054c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000054d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000054e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000054f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005504 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005514 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00005518 .long 5
	0x33, 0x30, 0x35, 0x31, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000551c QUAD $0x3837353731353033; QUAD $0x0000000000353231  // .asciz 16, '30517578125\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000552c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000553c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000554c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000555c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000556c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000557c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00005580 .long 5
	0x31, 0x35, 0x32, 0x35, 0x38, 0x37, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00005584 QUAD $0x3938373835323531; QUAD $0x0000000035323630  // .asciz 16, '152587890625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005594 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000055a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000055b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000055c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000055d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000055e4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x000055e8 .long 6
	0x37, 0x36, 0x32, 0x39, 0x33, 0x39, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x000055ec QUAD $0x3534393339323637; QUAD $0x0000000035323133  // .asciz 16, '762939453125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000055fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000560c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000561c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000562c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000563c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000564c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00005650 .long 6
	0x33, 0x38, 0x31, 0x34, 0x36, 0x39, 0x37, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, //0x00005654 QUAD $0x3237393634313833; QUAD $0x0000003532363536  // .asciz 16, '3814697265625\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005664 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005674 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005684 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005694 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000056a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000056b4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x000056b8 .long 6
	0x31, 0x39, 0x30, 0x37, 0x33, 0x34, 0x38, 0x36, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, //0x000056bc QUAD $0x3638343337303931; QUAD $0x0000353231383233  // .asciz 16, '19073486328125\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000056cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000056dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000056ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000056fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000570c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000571c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00005720 .long 7
	0x39, 0x35, 0x33, 0x36, 0x37, 0x34, 0x33, 0x31, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, //0x00005724 QUAD $0x3133343736333539; QUAD $0x0000353236303436  // .asciz 16, '95367431640625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005734 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005744 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005754 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005764 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005774 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005784 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00005788 .long 7
	0x34, 0x37, 0x36, 0x38, 0x33, 0x37, 0x31, 0x35, 0x38, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, //0x0000578c QUAD $0x3531373338363734; QUAD $0x0035323133303238  // .asciz 16, '476837158203125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000579c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000057ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000057bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000057cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000057dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000057ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x000057f0 .long 7
	0x32, 0x33, 0x38, 0x34, 0x31, 0x38, 0x35, 0x37, 0x39, 0x31, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, //0x000057f4 QUAD $0x3735383134383332; QUAD $0x3532363531303139  // .asciz 16, '2384185791015625'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005804 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005814 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005824 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005834 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005844 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005854 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00005858 .long 7
	0x31, 0x31, 0x39, 0x32, 0x30, 0x39, 0x32, 0x38, 0x39, 0x35, 0x35, 0x30, 0x37, 0x38, 0x31, 0x32, //0x0000585c QUAD $0x3832393032393131; QUAD $0x3231383730353539  // .asciz 16, '1192092895507812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000586c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000587c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000588c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000589c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000058ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000058bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x000058c0 .long 8
	0x35, 0x39, 0x36, 0x30, 0x34, 0x36, 0x34, 0x34, 0x37, 0x37, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, //0x000058c4 QUAD $0x3434363430363935; QUAD $0x3236303933353737  // .asciz 16, '5960464477539062'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000058d4 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000058e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000058f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005904 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005914 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005924 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00005928 .long 8
	0x32, 0x39, 0x38, 0x30, 0x32, 0x33, 0x32, 0x32, 0x33, 0x38, 0x37, 0x36, 0x39, 0x35, 0x33, 0x31, //0x0000592c QUAD $0x3232333230383932; QUAD $0x3133353936373833  // .asciz 16, '2980232238769531'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000593c QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000594c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000595c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000596c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000597c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000598c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00005990 .long 8
	0x31, 0x34, 0x39, 0x30, 0x31, 0x31, 0x36, 0x31, 0x31, 0x39, 0x33, 0x38, 0x34, 0x37, 0x36, 0x35, //0x00005994 QUAD $0x3136313130393431; QUAD $0x3536373438333931  // .asciz 16, '1490116119384765'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059a4 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000059e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000059f4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x000059f8 .long 9
	0x37, 0x34, 0x35, 0x30, 0x35, 0x38, 0x30, 0x35, 0x39, 0x36, 0x39, 0x32, 0x33, 0x38, 0x32, 0x38, //0x000059fc QUAD $0x3530383530353437; QUAD $0x3832383332393639  // .asciz 16, '7450580596923828'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a0c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a3c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005a5c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00005a60 .long 9
	0x33, 0x37, 0x32, 0x35, 0x32, 0x39, 0x30, 0x32, 0x39, 0x38, 0x34, 0x36, 0x31, 0x39, 0x31, 0x34, //0x00005a64 QUAD $0x3230393235323733; QUAD $0x3431393136343839  // .asciz 16, '3725290298461914'
	0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a74 QUAD $0x0000000035323630; QUAD $0x0000000000000000  // .asciz 16, '0625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005a94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005aa4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ab4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005ac4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00005ac8 .long 9
	0x31, 0x38, 0x36, 0x32, 0x36, 0x34, 0x35, 0x31, 0x34, 0x39, 0x32, 0x33, 0x30, 0x39, 0x35, 0x37, //0x00005acc QUAD $0x3135343632363831; QUAD $0x3735393033323934  // .asciz 16, '1862645149230957'
	0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005adc QUAD $0x0000003532313330; QUAD $0x0000000000000000  // .asciz 16, '03125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005aec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005afc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005b2c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00005b30 .long 10
	0x39, 0x33, 0x31, 0x33, 0x32, 0x32, 0x35, 0x37, 0x34, 0x36, 0x31, 0x35, 0x34, 0x37, 0x38, 0x35, //0x00005b34 QUAD $0x3735323233313339; QUAD $0x3538373435313634  // .asciz 16, '9313225746154785'
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b44 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005b84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005b94 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00005b98 .long 10
	0x34, 0x36, 0x35, 0x36, 0x36, 0x31, 0x32, 0x38, 0x37, 0x33, 0x30, 0x37, 0x37, 0x33, 0x39, 0x32, //0x00005b9c QUAD $0x3832313636353634; QUAD $0x3239333737303337  // .asciz 16, '4656612873077392'
	0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005bac QUAD $0x0000353231383735; QUAD $0x0000000000000000  // .asciz 16, '578125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005bbc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005bcc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005bdc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005bec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005bfc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00005c00 .long 10
	0x32, 0x33, 0x32, 0x38, 0x33, 0x30, 0x36, 0x34, 0x33, 0x36, 0x35, 0x33, 0x38, 0x36, 0x39, 0x36, //0x00005c04 QUAD $0x3436303338323332; QUAD $0x3639363833353633  // .asciz 16, '2328306436538696'
	0x32, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c14 QUAD $0x0035323630393832; QUAD $0x0000000000000000  // .asciz 16, '2890625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005c64 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00005c68 .long 10
	0x31, 0x31, 0x36, 0x34, 0x31, 0x35, 0x33, 0x32, 0x31, 0x38, 0x32, 0x36, 0x39, 0x33, 0x34, 0x38, //0x00005c6c QUAD $0x3233353134363131; QUAD $0x3834333936323831  // .asciz 16, '1164153218269348'
	0x31, 0x34, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c7c QUAD $0x3532313335343431; QUAD $0x0000000000000000  // .asciz 16, '14453125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005c9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005cac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005cbc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005ccc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00005cd0 .long 11
	0x35, 0x38, 0x32, 0x30, 0x37, 0x36, 0x36, 0x30, 0x39, 0x31, 0x33, 0x34, 0x36, 0x37, 0x34, 0x30, //0x00005cd4 QUAD $0x3036363730323835; QUAD $0x3034373634333139  // .asciz 16, '5820766091346740'
	0x37, 0x32, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ce4 QUAD $0x3532363536323237; QUAD $0x0000000000000000  // .asciz 16, '72265625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005cf4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005d34 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00005d38 .long 11
	0x32, 0x39, 0x31, 0x30, 0x33, 0x38, 0x33, 0x30, 0x34, 0x35, 0x36, 0x37, 0x33, 0x33, 0x37, 0x30, //0x00005d3c QUAD $0x3033383330313932; QUAD $0x3037333337363534  // .asciz 16, '2910383045673370'
	0x33, 0x36, 0x31, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d4c QUAD $0x3231383233313633; QUAD $0x0000000000000035  // .asciz 16, '361328125\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005d8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005d9c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00005da0 .long 11
	0x31, 0x34, 0x35, 0x35, 0x31, 0x39, 0x31, 0x35, 0x32, 0x32, 0x38, 0x33, 0x36, 0x36, 0x38, 0x35, //0x00005da4 QUAD $0x3531393135353431; QUAD $0x3538363633383232  // .asciz 16, '1455191522836685'
	0x31, 0x38, 0x30, 0x36, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005db4 QUAD $0x3630343636303831; QUAD $0x0000000000003532  // .asciz 16, '1806640625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005dc4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005dd4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005de4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005df4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005e04 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00005e08 .long 12
	0x37, 0x32, 0x37, 0x35, 0x39, 0x35, 0x37, 0x36, 0x31, 0x34, 0x31, 0x38, 0x33, 0x34, 0x32, 0x35, //0x00005e0c QUAD $0x3637353935373237; QUAD $0x3532343338313431  // .asciz 16, '7275957614183425'
	0x39, 0x30, 0x33, 0x33, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e1c QUAD $0x3133303233333039; QUAD $0x0000000000003532  // .asciz 16, '9033203125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e3c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005e6c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00005e70 .long 12
	0x33, 0x36, 0x33, 0x37, 0x39, 0x37, 0x38, 0x38, 0x30, 0x37, 0x30, 0x39, 0x31, 0x37, 0x31, 0x32, //0x00005e74 QUAD $0x3838373937333633; QUAD $0x3231373139303730  // .asciz 16, '3637978807091712'
	0x39, 0x35, 0x31, 0x36, 0x36, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e84 QUAD $0x3531303636313539; QUAD $0x0000000000353236  // .asciz 16, '95166015625\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ea4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005eb4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ec4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005ed4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00005ed8 .long 12
	0x31, 0x38, 0x31, 0x38, 0x39, 0x38, 0x39, 0x34, 0x30, 0x33, 0x35, 0x34, 0x35, 0x38, 0x35, 0x36, //0x00005edc QUAD $0x3439383938313831; QUAD $0x3635383534353330  // .asciz 16, '1818989403545856'
	0x34, 0x37, 0x35, 0x38, 0x33, 0x30, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00005eec QUAD $0x3730303338353734; QUAD $0x0000000035323138  // .asciz 16, '475830078125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005efc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005f3c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00005f40 .long 13
	0x39, 0x30, 0x39, 0x34, 0x39, 0x34, 0x37, 0x30, 0x31, 0x37, 0x37, 0x32, 0x39, 0x32, 0x38, 0x32, //0x00005f44 QUAD $0x3037343934393039; QUAD $0x3238323932373731  // .asciz 16, '9094947017729282'
	0x33, 0x37, 0x39, 0x31, 0x35, 0x30, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00005f54 QUAD $0x3933303531393733; QUAD $0x0000000035323630  // .asciz 16, '379150390625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005f94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00005fa4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00005fa8 .long 13
	0x34, 0x35, 0x34, 0x37, 0x34, 0x37, 0x33, 0x35, 0x30, 0x38, 0x38, 0x36, 0x34, 0x36, 0x34, 0x31, //0x00005fac QUAD $0x3533373437343534; QUAD $0x3134363436383830  // .asciz 16, '4547473508864641'
	0x31, 0x38, 0x39, 0x35, 0x37, 0x35, 0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, //0x00005fbc QUAD $0x3931353735393831; QUAD $0x0000003532313335  // .asciz 16, '1895751953125\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fcc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fdc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005fec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ffc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000600c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00006010 .long 13
	0x32, 0x32, 0x37, 0x33, 0x37, 0x33, 0x36, 0x37, 0x35, 0x34, 0x34, 0x33, 0x32, 0x33, 0x32, 0x30, //0x00006014 QUAD $0x3736333733373232; QUAD $0x3032333233343435  // .asciz 16, '2273736754432320'
	0x35, 0x39, 0x34, 0x37, 0x38, 0x37, 0x35, 0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, //0x00006024 QUAD $0x3935373837343935; QUAD $0x0000353236353637  // .asciz 16, '59478759765625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006034 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006044 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006054 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006064 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006074 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00006078 .long 13
	0x31, 0x31, 0x33, 0x36, 0x38, 0x36, 0x38, 0x33, 0x37, 0x37, 0x32, 0x31, 0x36, 0x31, 0x36, 0x30, //0x0000607c QUAD $0x3338363836333131; QUAD $0x3036313631323737  // .asciz 16, '1136868377216160'
	0x32, 0x39, 0x37, 0x33, 0x39, 0x33, 0x37, 0x39, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, //0x0000608c QUAD $0x3937333933373932; QUAD $0x0035323138323838  // .asciz 16, '297393798828125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000609c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000060ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000060bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000060cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000060dc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x000060e0 .long 14
	0x35, 0x36, 0x38, 0x34, 0x33, 0x34, 0x31, 0x38, 0x38, 0x36, 0x30, 0x38, 0x30, 0x38, 0x30, 0x31, //0x000060e4 QUAD $0x3831343334383635; QUAD $0x3130383038303638  // .asciz 16, '5684341886080801'
	0x34, 0x38, 0x36, 0x39, 0x36, 0x38, 0x39, 0x39, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, //0x000060f4 QUAD $0x3939383639363834; QUAD $0x0035323630343134  // .asciz 16, '486968994140625\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006104 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006114 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006124 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006134 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006144 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00006148 .long 14
	0x32, 0x38, 0x34, 0x32, 0x31, 0x37, 0x30, 0x39, 0x34, 0x33, 0x30, 0x34, 0x30, 0x34, 0x30, 0x30, //0x0000614c QUAD $0x3930373132343832; QUAD $0x3030343034303334  // .asciz 16, '2842170943040400'
	0x37, 0x34, 0x33, 0x34, 0x38, 0x34, 0x34, 0x39, 0x37, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, //0x0000615c QUAD $0x3934343834333437; QUAD $0x3532313330373037  // .asciz 16, '7434844970703125'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000616c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000617c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000618c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000619c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000061ac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x000061b0 .long 14
	0x31, 0x34, 0x32, 0x31, 0x30, 0x38, 0x35, 0x34, 0x37, 0x31, 0x35, 0x32, 0x30, 0x32, 0x30, 0x30, //0x000061b4 QUAD $0x3435383031323431; QUAD $0x3030323032353137  // .asciz 16, '1421085471520200'
	0x33, 0x37, 0x31, 0x37, 0x34, 0x32, 0x32, 0x34, 0x38, 0x35, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, //0x000061c4 QUAD $0x3432323437313733; QUAD $0x3236353135333538  // .asciz 16, '3717422485351562'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061d4 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006204 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006214 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00006218 .long 15
	0x37, 0x31, 0x30, 0x35, 0x34, 0x32, 0x37, 0x33, 0x35, 0x37, 0x36, 0x30, 0x31, 0x30, 0x30, 0x31, //0x0000621c QUAD $0x3337323435303137; QUAD $0x3130303130363735  // .asciz 16, '7105427357601001'
	0x38, 0x35, 0x38, 0x37, 0x31, 0x31, 0x32, 0x34, 0x32, 0x36, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, //0x0000622c QUAD $0x3432313137383538; QUAD $0x3231383735373632  // .asciz 16, '8587112426757812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000623c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000624c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000625c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000626c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000627c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00006280 .long 15
	0x33, 0x35, 0x35, 0x32, 0x37, 0x31, 0x33, 0x36, 0x37, 0x38, 0x38, 0x30, 0x30, 0x35, 0x30, 0x30, //0x00006284 QUAD $0x3633313732353533; QUAD $0x3030353030383837  // .asciz 16, '3552713678800500'
	0x39, 0x32, 0x39, 0x33, 0x35, 0x35, 0x36, 0x32, 0x31, 0x33, 0x33, 0x37, 0x38, 0x39, 0x30, 0x36, //0x00006294 QUAD $0x3236353533393239; QUAD $0x3630393837333331  // .asciz 16, '9293556213378906'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062a4 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000062e4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x000062e8 .long 15
	0x31, 0x37, 0x37, 0x36, 0x33, 0x35, 0x36, 0x38, 0x33, 0x39, 0x34, 0x30, 0x30, 0x32, 0x35, 0x30, //0x000062ec QUAD $0x3836353336373731; QUAD $0x3035323030343933  // .asciz 16, '1776356839400250'
	0x34, 0x36, 0x34, 0x36, 0x37, 0x37, 0x38, 0x31, 0x30, 0x36, 0x36, 0x38, 0x39, 0x34, 0x35, 0x33, //0x000062fc QUAD $0x3138373736343634; QUAD $0x3335343938363630  // .asciz 16, '4646778106689453'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000630c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000631c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000632c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000633c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000634c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00006350 .long 16
	0x38, 0x38, 0x38, 0x31, 0x37, 0x38, 0x34, 0x31, 0x39, 0x37, 0x30, 0x30, 0x31, 0x32, 0x35, 0x32, //0x00006354 QUAD $0x3134383731383838; QUAD $0x3235323130303739  // .asciz 16, '8881784197001252'
	0x33, 0x32, 0x33, 0x33, 0x38, 0x39, 0x30, 0x35, 0x33, 0x33, 0x34, 0x34, 0x37, 0x32, 0x36, 0x35, //0x00006364 QUAD $0x3530393833333233; QUAD $0x3536323734343333  // .asciz 16, '3233890533447265'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006374 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006384 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006394 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000063b4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x000063b8 .long 16
	0x34, 0x34, 0x34, 0x30, 0x38, 0x39, 0x32, 0x30, 0x39, 0x38, 0x35, 0x30, 0x30, 0x36, 0x32, 0x36, //0x000063bc QUAD $0x3032393830343434; QUAD $0x3632363030353839  // .asciz 16, '4440892098500626'
	0x31, 0x36, 0x31, 0x36, 0x39, 0x34, 0x35, 0x32, 0x36, 0x36, 0x37, 0x32, 0x33, 0x36, 0x33, 0x32, //0x000063cc QUAD $0x3235343936313631; QUAD $0x3233363332373636  // .asciz 16, '1616945266723632'
	0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063dc QUAD $0x0000000035323138; QUAD $0x0000000000000000  // .asciz 16, '8125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000640c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000641c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00006420 .long 16
	0x32, 0x32, 0x32, 0x30, 0x34, 0x34, 0x36, 0x30, 0x34, 0x39, 0x32, 0x35, 0x30, 0x33, 0x31, 0x33, //0x00006424 QUAD $0x3036343430323232; QUAD $0x3331333035323934  // .asciz 16, '2220446049250313'
	0x30, 0x38, 0x30, 0x38, 0x34, 0x37, 0x32, 0x36, 0x33, 0x33, 0x33, 0x36, 0x31, 0x38, 0x31, 0x36, //0x00006434 QUAD $0x3632373438303830; QUAD $0x3631383136333333  // .asciz 16, '0808472633361816'
	0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006444 QUAD $0x0000003532363034; QUAD $0x0000000000000000  // .asciz 16, '40625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006454 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006464 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006474 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006484 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00006488 .long 16
	0x31, 0x31, 0x31, 0x30, 0x32, 0x32, 0x33, 0x30, 0x32, 0x34, 0x36, 0x32, 0x35, 0x31, 0x35, 0x36, //0x0000648c QUAD $0x3033323230313131; QUAD $0x3635313532363432  // .asciz 16, '1110223024625156'
	0x35, 0x34, 0x30, 0x34, 0x32, 0x33, 0x36, 0x33, 0x31, 0x36, 0x36, 0x38, 0x30, 0x39, 0x30, 0x38, //0x0000649c QUAD $0x3336333234303435; QUAD $0x3830393038363631  // .asciz 16, '5404236316680908'
	0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064ac QUAD $0x0000353231333032; QUAD $0x0000000000000000  // .asciz 16, '203125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000064ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x000064f0 .long 17
	0x35, 0x35, 0x35, 0x31, 0x31, 0x31, 0x35, 0x31, 0x32, 0x33, 0x31, 0x32, 0x35, 0x37, 0x38, 0x32, //0x000064f4 QUAD $0x3135313131353535; QUAD $0x3238373532313332  // .asciz 16, '5551115123125782'
	0x37, 0x30, 0x32, 0x31, 0x31, 0x38, 0x31, 0x35, 0x38, 0x33, 0x34, 0x30, 0x34, 0x35, 0x34, 0x31, //0x00006504 QUAD $0x3531383131323037; QUAD $0x3134353430343338  // .asciz 16, '7021181583404541'
	0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006514 QUAD $0x0000353236353130; QUAD $0x0000000000000000  // .asciz 16, '015625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006524 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006534 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006544 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006554 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00006558 .long 17
	0x32, 0x37, 0x37, 0x35, 0x35, 0x35, 0x37, 0x35, 0x36, 0x31, 0x35, 0x36, 0x32, 0x38, 0x39, 0x31, //0x0000655c QUAD $0x3537353535373732; QUAD $0x3139383236353136  // .asciz 16, '2775557561562891'
	0x33, 0x35, 0x31, 0x30, 0x35, 0x39, 0x30, 0x37, 0x39, 0x31, 0x37, 0x30, 0x32, 0x32, 0x37, 0x30, //0x0000656c QUAD $0x3730393530313533; QUAD $0x3037323230373139  // .asciz 16, '3510590791702270'
	0x35, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000657c QUAD $0x0035323138373035; QUAD $0x0000000000000000  // .asciz 16, '5078125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000658c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000659c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000065bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x000065c0 .long 17
	0x31, 0x33, 0x38, 0x37, 0x37, 0x37, 0x38, 0x37, 0x38, 0x30, 0x37, 0x38, 0x31, 0x34, 0x34, 0x35, //0x000065c4 QUAD $0x3738373737383331; QUAD $0x3534343138373038  // .asciz 16, '1387778780781445'
	0x36, 0x37, 0x35, 0x35, 0x32, 0x39, 0x35, 0x33, 0x39, 0x35, 0x38, 0x35, 0x31, 0x31, 0x33, 0x35, //0x000065d4 QUAD $0x3335393235353736; QUAD $0x3533313135383539  // .asciz 16, '6755295395851135'
	0x32, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065e4 QUAD $0x3532363039333532; QUAD $0x0000000000000000  // .asciz 16, '25390625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006604 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006614 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006624 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00006628 .long 18
	0x36, 0x39, 0x33, 0x38, 0x38, 0x39, 0x33, 0x39, 0x30, 0x33, 0x39, 0x30, 0x37, 0x32, 0x32, 0x38, //0x0000662c QUAD $0x3933393838333936; QUAD $0x3832323730393330  // .asciz 16, '6938893903907228'
	0x33, 0x37, 0x37, 0x36, 0x34, 0x37, 0x36, 0x39, 0x37, 0x39, 0x32, 0x35, 0x35, 0x36, 0x37, 0x36, //0x0000663c QUAD $0x3936373436373733; QUAD $0x3637363535323937  // .asciz 16, '3776476979255676'
	0x32, 0x36, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000664c QUAD $0x3532313335393632; QUAD $0x0000000000000000  // .asciz 16, '26953125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000665c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000666c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000667c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000668c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00006690 .long 18
	0x33, 0x34, 0x36, 0x39, 0x34, 0x34, 0x36, 0x39, 0x35, 0x31, 0x39, 0x35, 0x33, 0x36, 0x31, 0x34, //0x00006694 QUAD $0x3936343439363433; QUAD $0x3431363335393135  // .asciz 16, '3469446951953614'
	0x31, 0x38, 0x38, 0x38, 0x32, 0x33, 0x38, 0x34, 0x38, 0x39, 0x36, 0x32, 0x37, 0x38, 0x33, 0x38, //0x000066a4 QUAD $0x3438333238383831; QUAD $0x3833383732363938  // .asciz 16, '1888238489627838'
	0x31, 0x33, 0x34, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066b4 QUAD $0x3236353637343331; QUAD $0x0000000000000035  // .asciz 16, '134765625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000066f4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x000066f8 .long 18
	0x31, 0x37, 0x33, 0x34, 0x37, 0x32, 0x33, 0x34, 0x37, 0x35, 0x39, 0x37, 0x36, 0x38, 0x30, 0x37, //0x000066fc QUAD $0x3433323734333731; QUAD $0x3730383637393537  // .asciz 16, '1734723475976807'
	0x30, 0x39, 0x34, 0x34, 0x31, 0x31, 0x39, 0x32, 0x34, 0x34, 0x38, 0x31, 0x33, 0x39, 0x31, 0x39, //0x0000670c QUAD $0x3239313134343930; QUAD $0x3931393331383434  // .asciz 16, '0944119244813919'
	0x30, 0x36, 0x37, 0x33, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000671c QUAD $0x3138323833373630; QUAD $0x0000000000003532  // .asciz 16, '0673828125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000672c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000673c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000674c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000675c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x13, 0x00, 0x00, 0x00, //0x00006760 .long 19
	0x38, 0x36, 0x37, 0x33, 0x36, 0x31, 0x37, 0x33, 0x37, 0x39, 0x38, 0x38, 0x34, 0x30, 0x33, 0x35, //0x00006764 QUAD $0x3337313633373638; QUAD $0x3533303438383937  // .asciz 16, '8673617379884035'
	0x34, 0x37, 0x32, 0x30, 0x35, 0x39, 0x36, 0x32, 0x32, 0x34, 0x30, 0x36, 0x39, 0x35, 0x39, 0x35, //0x00006774 QUAD $0x3236393530323734; QUAD $0x3539353936303432  // .asciz 16, '4720596224069595'
	0x33, 0x33, 0x36, 0x39, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006784 QUAD $0x3630343139363333; QUAD $0x0000000000003532  // .asciz 16, '3369140625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006794 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000067c4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
}
 
