// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_lookup_small_key = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, // QUAD $0xbfbfbfbfbfbfbfbf; QUAD $0xbfbfbfbfbfbfbfbf  // .space 16, '\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf'
	0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, 0xbf, //0x00000010 QUAD $0xbfbfbfbfbfbfbfbf; QUAD $0xbfbfbfbfbfbfbfbf  // .space 16, '\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf\xbf'
	//0x00000020 LCPI0_1
	0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, //0x00000020 QUAD $0x1818181818181818; QUAD $0x1818181818181818  // .space 16, '\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18'
	0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, //0x00000030 QUAD $0x1818181818181818; QUAD $0x1818181818181818  // .space 16, '\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18\x18'
	//0x00000040 LCPI0_2
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000040 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000050 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000060 .p2align 4, 0x90
	//0x00000060 _lookup_small_key
	0x55, //0x00000060 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000061 movq         %rsp, %rbp
	0x41, 0x57, //0x00000064 pushq        %r15
	0x41, 0x56, //0x00000066 pushq        %r14
	0x41, 0x55, //0x00000068 pushq        %r13
	0x41, 0x54, //0x0000006a pushq        %r12
	0x53, //0x0000006c pushq        %rbx
	0x50, //0x0000006d pushq        %rax
	0x4c, 0x8b, 0x57, 0x08, //0x0000006e movq         $8(%rdi), %r10
	0x4c, 0x8b, 0x2e, //0x00000072 movq         (%rsi), %r13
	0x45, 0x0f, 0xb6, 0xc2, //0x00000075 movzbl       %r10b, %r8d
	0x4b, 0x8d, 0x0c, 0x80, //0x00000079 leaq         (%r8,%r8,4), %rcx
	0x45, 0x0f, 0xb6, 0x4c, 0x0d, 0x00, //0x0000007d movzbl       (%r13,%rcx), %r9d
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000083 movq         $-1, %rax
	0x45, 0x85, 0xc9, //0x0000008a testl        %r9d, %r9d
	0x0f, 0x84, 0xd5, 0x02, 0x00, 0x00, //0x0000008d je           LBB0_39
	0x48, 0x89, 0x55, 0xd0, //0x00000093 movq         %rdx, $-48(%rbp)
	0x4c, 0x8b, 0x37, //0x00000097 movq         (%rdi), %r14
	0x41, 0x8b, 0x44, 0x0d, 0x01, //0x0000009a movl         $1(%r13,%rcx), %eax
	0x8d, 0xb8, 0xa5, 0x00, 0x00, 0x00, //0x0000009f leal         $165(%rax), %edi
	0x4c, 0x01, 0xef, //0x000000a5 addq         %r13, %rdi
	0x41, 0x0f, 0xb6, 0xca, //0x000000a8 movzbl       %r10b, %ecx
	0x41, 0x83, 0xf8, 0x09, //0x000000ac cmpl         $9, %r8d
	0x0f, 0x83, 0xc6, 0x00, 0x00, 0x00, //0x000000b0 jae          LBB0_2
	0x45, 0x8a, 0x3e, //0x000000b6 movb         (%r14), %r15b
	0x45, 0x8d, 0x60, 0x01, //0x000000b9 leal         $1(%r8), %r12d
	0x44, 0x89, 0xcb, //0x000000bd movl         %r9d, %ebx
	//0x000000c0 .p2align 4, 0x90
	//0x000000c0 LBB0_7
	0x44, 0x38, 0x3f, //0x000000c0 cmpb         %r15b, (%rdi)
	0x0f, 0x85, 0x97, 0x00, 0x00, 0x00, //0x000000c3 jne          LBB0_8
	0x44, 0x0f, 0xb6, 0x5f, 0x01, //0x000000c9 movzbl       $1(%rdi), %r11d
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x000000ce movl         $1, %esi
	0x45, 0x3a, 0x5e, 0x01, //0x000000d3 cmpb         $1(%r14), %r11b
	0x0f, 0x85, 0x85, 0x00, 0x00, 0x00, //0x000000d7 jne          LBB0_16
	0x0f, 0xb6, 0x57, 0x02, //0x000000dd movzbl       $2(%rdi), %edx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x000000e1 movl         $2, %esi
	0x41, 0x3a, 0x56, 0x02, //0x000000e6 cmpb         $2(%r14), %dl
	0x0f, 0x85, 0x72, 0x00, 0x00, 0x00, //0x000000ea jne          LBB0_16
	0x0f, 0xb6, 0x57, 0x03, //0x000000f0 movzbl       $3(%rdi), %edx
	0xbe, 0x03, 0x00, 0x00, 0x00, //0x000000f4 movl         $3, %esi
	0x41, 0x3a, 0x56, 0x03, //0x000000f9 cmpb         $3(%r14), %dl
	0x0f, 0x85, 0x5f, 0x00, 0x00, 0x00, //0x000000fd jne          LBB0_16
	0x0f, 0xb6, 0x57, 0x04, //0x00000103 movzbl       $4(%rdi), %edx
	0xbe, 0x04, 0x00, 0x00, 0x00, //0x00000107 movl         $4, %esi
	0x41, 0x3a, 0x56, 0x04, //0x0000010c cmpb         $4(%r14), %dl
	0x0f, 0x85, 0x4c, 0x00, 0x00, 0x00, //0x00000110 jne          LBB0_16
	0x0f, 0xb6, 0x57, 0x05, //0x00000116 movzbl       $5(%rdi), %edx
	0xbe, 0x05, 0x00, 0x00, 0x00, //0x0000011a movl         $5, %esi
	0x41, 0x3a, 0x56, 0x05, //0x0000011f cmpb         $5(%r14), %dl
	0x0f, 0x85, 0x39, 0x00, 0x00, 0x00, //0x00000123 jne          LBB0_16
	0x0f, 0xb6, 0x57, 0x06, //0x00000129 movzbl       $6(%rdi), %edx
	0xbe, 0x06, 0x00, 0x00, 0x00, //0x0000012d movl         $6, %esi
	0x41, 0x3a, 0x56, 0x06, //0x00000132 cmpb         $6(%r14), %dl
	0x0f, 0x85, 0x26, 0x00, 0x00, 0x00, //0x00000136 jne          LBB0_16
	0x0f, 0xb6, 0x57, 0x07, //0x0000013c movzbl       $7(%rdi), %edx
	0x31, 0xf6, //0x00000140 xorl         %esi, %esi
	0x41, 0x3a, 0x56, 0x07, //0x00000142 cmpb         $7(%r14), %dl
	0x40, 0x0f, 0x94, 0xc6, //0x00000146 sete         %sil
	0x48, 0x83, 0xc6, 0x07, //0x0000014a addq         $7, %rsi
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x0000014e jmp          LBB0_16
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000153 .p2align 4, 0x90
	//0x00000160 LBB0_8
	0x31, 0xf6, //0x00000160 xorl         %esi, %esi
	//0x00000162 LBB0_16
	0x48, 0x39, 0xce, //0x00000162 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x71, 0x01, 0x00, 0x00, //0x00000165 jae          LBB0_17
	0x4c, 0x01, 0xe7, //0x0000016b addq         %r12, %rdi
	0x83, 0xc3, 0xff, //0x0000016e addl         $-1, %ebx
	0x0f, 0x85, 0x49, 0xff, 0xff, 0xff, //0x00000171 jne          LBB0_7
	0xe9, 0x43, 0x00, 0x00, 0x00, //0x00000177 jmp          LBB0_20
	//0x0000017c LBB0_2
	0xc4, 0xc1, 0x7e, 0x6f, 0x06, //0x0000017c vmovdqu      (%r14), %ymm0
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00000181 movq         $-1, %rsi
	0x48, 0xd3, 0xe6, //0x00000188 shlq         %cl, %rsi
	0x45, 0x8d, 0x78, 0x01, //0x0000018b leal         $1(%r8), %r15d
	0x44, 0x89, 0xcb, //0x0000018f movl         %r9d, %ebx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000192 .p2align 4, 0x90
	//0x000001a0 LBB0_3
	0xc5, 0xfd, 0x74, 0x0f, //0x000001a0 vpcmpeqb     (%rdi), %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x000001a4 vpmovmskb    %ymm1, %edx
	0x09, 0xf2, //0x000001a8 orl          %esi, %edx
	0x83, 0xfa, 0xff, //0x000001aa cmpl         $-1, %edx
	0x0f, 0x84, 0x38, 0x01, 0x00, 0x00, //0x000001ad je           LBB0_4
	0x4c, 0x01, 0xff, //0x000001b3 addq         %r15, %rdi
	0x83, 0xc3, 0xff, //0x000001b6 addl         $-1, %ebx
	0x0f, 0x85, 0xe1, 0xff, 0xff, 0xff, //0x000001b9 jne          LBB0_3
	//0x000001bf LBB0_20
	0xc4, 0xc1, 0x7e, 0x6f, 0x06, //0x000001bf vmovdqu      (%r14), %ymm0
	0xc5, 0xfd, 0xfc, 0x0d, 0x34, 0xfe, 0xff, 0xff, //0x000001c4 vpaddb       $-460(%rip), %ymm0, %ymm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf5, 0xda, 0x15, 0x4c, 0xfe, 0xff, 0xff, //0x000001cc vpminub      $-436(%rip), %ymm1, %ymm2  /* LCPI0_1+0(%rip) */
	0x48, 0x03, 0x45, 0xd0, //0x000001d4 addq         $-48(%rbp), %rax
	0xc5, 0xf5, 0x74, 0xca, //0x000001d8 vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xf5, 0xdb, 0x0d, 0x5c, 0xfe, 0xff, 0xff, //0x000001dc vpand        $-420(%rip), %ymm1, %ymm1  /* LCPI0_2+0(%rip) */
	0x49, 0x01, 0xc5, //0x000001e4 addq         %rax, %r13
	0xc5, 0xf5, 0xfc, 0xc0, //0x000001e7 vpaddb       %ymm0, %ymm1, %ymm0
	0x41, 0x0f, 0xb6, 0xca, //0x000001eb movzbl       %r10b, %ecx
	0x41, 0x83, 0xf8, 0x09, //0x000001ef cmpl         $9, %r8d
	0x0f, 0x83, 0xfd, 0x00, 0x00, 0x00, //0x000001f3 jae          LBB0_21
	0xc4, 0xe3, 0x79, 0x14, 0xc2, 0x01, //0x000001f9 vpextrb      $1, %xmm0, %edx
	0xc4, 0xc3, 0x79, 0x14, 0xc4, 0x02, //0x000001ff vpextrb      $2, %xmm0, %r12d
	0xc4, 0xc3, 0x79, 0x14, 0xc7, 0x03, //0x00000205 vpextrb      $3, %xmm0, %r15d
	0xc4, 0xc3, 0x79, 0x14, 0xc2, 0x04, //0x0000020b vpextrb      $4, %xmm0, %r10d
	0xc4, 0xc3, 0x79, 0x14, 0xc3, 0x05, //0x00000211 vpextrb      $5, %xmm0, %r11d
	0xc4, 0xc3, 0x79, 0x14, 0xc6, 0x06, //0x00000217 vpextrb      $6, %xmm0, %r14d
	0xc5, 0xf9, 0x7e, 0xc3, //0x0000021d vmovd        %xmm0, %ebx
	0xc4, 0xe3, 0x79, 0x14, 0xc0, 0x07, //0x00000221 vpextrb      $7, %xmm0, %eax
	0x41, 0x83, 0xc0, 0x01, //0x00000227 addl         $1, %r8d
	0x41, 0x83, 0xf9, 0x02, //0x0000022b cmpl         $2, %r9d
	0xbf, 0x01, 0x00, 0x00, 0x00, //0x0000022f movl         $1, %edi
	0x41, 0x0f, 0x43, 0xf9, //0x00000234 cmovael      %r9d, %edi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000238 .p2align 4, 0x90
	//0x00000240 LBB0_25
	0x41, 0x38, 0x5d, 0x00, //0x00000240 cmpb         %bl, (%r13)
	0x0f, 0x85, 0x76, 0x00, 0x00, 0x00, //0x00000244 jne          LBB0_26
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x0000024a movl         $1, %esi
	0x41, 0x38, 0x55, 0x01, //0x0000024f cmpb         %dl, $1(%r13)
	0x0f, 0x85, 0x69, 0x00, 0x00, 0x00, //0x00000253 jne          LBB0_34
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00000259 movl         $2, %esi
	0x45, 0x38, 0x65, 0x02, //0x0000025e cmpb         %r12b, $2(%r13)
	0x0f, 0x85, 0x5a, 0x00, 0x00, 0x00, //0x00000262 jne          LBB0_34
	0xbe, 0x03, 0x00, 0x00, 0x00, //0x00000268 movl         $3, %esi
	0x45, 0x38, 0x7d, 0x03, //0x0000026d cmpb         %r15b, $3(%r13)
	0x0f, 0x85, 0x4b, 0x00, 0x00, 0x00, //0x00000271 jne          LBB0_34
	0xbe, 0x04, 0x00, 0x00, 0x00, //0x00000277 movl         $4, %esi
	0x45, 0x38, 0x55, 0x04, //0x0000027c cmpb         %r10b, $4(%r13)
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x00000280 jne          LBB0_34
	0xbe, 0x05, 0x00, 0x00, 0x00, //0x00000286 movl         $5, %esi
	0x45, 0x38, 0x5d, 0x05, //0x0000028b cmpb         %r11b, $5(%r13)
	0x0f, 0x85, 0x2d, 0x00, 0x00, 0x00, //0x0000028f jne          LBB0_34
	0xbe, 0x06, 0x00, 0x00, 0x00, //0x00000295 movl         $6, %esi
	0x45, 0x38, 0x75, 0x06, //0x0000029a cmpb         %r14b, $6(%r13)
	0x0f, 0x85, 0x1e, 0x00, 0x00, 0x00, //0x0000029e jne          LBB0_34
	0x31, 0xf6, //0x000002a4 xorl         %esi, %esi
	0x41, 0x38, 0x45, 0x07, //0x000002a6 cmpb         %al, $7(%r13)
	0x40, 0x0f, 0x94, 0xc6, //0x000002aa sete         %sil
	0x48, 0x83, 0xc6, 0x07, //0x000002ae addq         $7, %rsi
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x000002b2 jmp          LBB0_34
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000002b7 .p2align 4, 0x90
	//0x000002c0 LBB0_26
	0x31, 0xf6, //0x000002c0 xorl         %esi, %esi
	//0x000002c2 LBB0_34
	0x48, 0x39, 0xce, //0x000002c2 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x82, 0x00, 0x00, 0x00, //0x000002c5 jae          LBB0_35
	0x4d, 0x01, 0xc5, //0x000002cb addq         %r8, %r13
	0x83, 0xc7, 0xff, //0x000002ce addl         $-1, %edi
	0x0f, 0x85, 0x69, 0xff, 0xff, 0xff, //0x000002d1 jne          LBB0_25
	0xe9, 0x65, 0x00, 0x00, 0x00, //0x000002d7 jmp          LBB0_38
	//0x000002dc LBB0_17
	0x4c, 0x01, 0xe7, //0x000002dc addq         %r12, %rdi
	0x48, 0x83, 0xc7, 0xff, //0x000002df addq         $-1, %rdi
	0x0f, 0xb6, 0x07, //0x000002e3 movzbl       (%rdi), %eax
	0xe9, 0x7d, 0x00, 0x00, 0x00, //0x000002e6 jmp          LBB0_39
	//0x000002eb LBB0_4
	0x48, 0x01, 0xcf, //0x000002eb addq         %rcx, %rdi
	0x0f, 0xb6, 0x07, //0x000002ee movzbl       (%rdi), %eax
	0xe9, 0x72, 0x00, 0x00, 0x00, //0x000002f1 jmp          LBB0_39
	//0x000002f6 LBB0_21
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000002f6 movq         $-1, %rax
	0x48, 0xd3, 0xe0, //0x000002fd shlq         %cl, %rax
	0x41, 0x83, 0xc0, 0x01, //0x00000300 addl         $1, %r8d
	0x41, 0x83, 0xf9, 0x02, //0x00000304 cmpl         $2, %r9d
	0xba, 0x01, 0x00, 0x00, 0x00, //0x00000308 movl         $1, %edx
	0x41, 0x0f, 0x43, 0xd1, //0x0000030d cmovael      %r9d, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000311 .p2align 4, 0x90
	//0x00000320 LBB0_22
	0xc4, 0xc1, 0x7d, 0x74, 0x4d, 0x00, //0x00000320 vpcmpeqb     (%r13), %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00000326 vpmovmskb    %ymm1, %esi
	0x09, 0xc6, //0x0000032a orl          %eax, %esi
	0x83, 0xfe, 0xff, //0x0000032c cmpl         $-1, %esi
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x0000032f je           LBB0_23
	0x4d, 0x01, 0xc5, //0x00000335 addq         %r8, %r13
	0x83, 0xc2, 0xff, //0x00000338 addl         $-1, %edx
	0x0f, 0x85, 0xdf, 0xff, 0xff, 0xff, //0x0000033b jne          LBB0_22
	//0x00000341 LBB0_38
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000341 movq         $-1, %rax
	0xe9, 0x1b, 0x00, 0x00, 0x00, //0x00000348 jmp          LBB0_39
	//0x0000034d LBB0_35
	0x4b, 0x8d, 0x3c, 0x28, //0x0000034d leaq         (%r8,%r13), %rdi
	0x48, 0x83, 0xc7, 0xff, //0x00000351 addq         $-1, %rdi
	0x0f, 0xb6, 0x07, //0x00000355 movzbl       (%rdi), %eax
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00000358 jmp          LBB0_39
	//0x0000035d LBB0_23
	0x49, 0x01, 0xcd, //0x0000035d addq         %rcx, %r13
	0x4c, 0x89, 0xef, //0x00000360 movq         %r13, %rdi
	0x41, 0x0f, 0xb6, 0x45, 0x00, //0x00000363 movzbl       (%r13), %eax
	//0x00000368 LBB0_39
	0x48, 0x83, 0xc4, 0x08, //0x00000368 addq         $8, %rsp
	0x5b, //0x0000036c popq         %rbx
	0x41, 0x5c, //0x0000036d popq         %r12
	0x41, 0x5d, //0x0000036f popq         %r13
	0x41, 0x5e, //0x00000371 popq         %r14
	0x41, 0x5f, //0x00000373 popq         %r15
	0x5d, //0x00000375 popq         %rbp
	0xc5, 0xf8, 0x77, //0x00000376 vzeroupper   
	0xc3, //0x00000379 retq         
}
 
