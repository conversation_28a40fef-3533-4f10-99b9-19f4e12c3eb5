// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__lspace = 32
)

const (
    _stack__lspace = 8
)

const (
    _size__lspace = 240
)

var (
    _pcsp__lspace = [][2]uint32{
        {0x1, 0},
        {0xbb, 8},
        {0xbf, 0},
        {0xd0, 8},
        {0xd4, 0},
        {0xdb, 8},
        {0xdf, 0},
        {0xf0, 8},
    }
)

var _cfunc_lspace = []loader.CFunc{
    {"_lspace_entry", 0,  _entry__lspace, 0, nil},
    {"_lspace", _entry__lspace, _size__lspace, _stack__lspace, _pcsp__lspace},
}
