// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__unquote = 48
)

const (
    _stack__unquote = 80
)

const (
    _size__unquote = 2096
)

var (
    _pcsp__unquote = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0x5f8, 80},
        {0x5f9, 48},
        {0x5fb, 40},
        {0x5fd, 32},
        {0x5ff, 24},
        {0x601, 16},
        {0x602, 8},
        {0x606, 0},
        {0x830, 80},
    }
)

var _cfunc_unquote = []loader.CFunc{
    {"_unquote_entry", 0,  _entry__unquote, 0, nil},
    {"_unquote", _entry__unquote, _size__unquote, _stack__unquote, _pcsp__unquote},
}
