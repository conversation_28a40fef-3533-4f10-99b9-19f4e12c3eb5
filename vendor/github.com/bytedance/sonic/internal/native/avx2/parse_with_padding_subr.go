// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__parse_with_padding = 688
)

const (
    _stack__parse_with_padding = 192
)

const (
    _size__parse_with_padding = 49496
)

var (
    _pcsp__parse_with_padding = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x14, 48},
        {0xf22, 192},
        {0xf23, 48},
        {0xf25, 40},
        {0xf27, 32},
        {0xf29, 24},
        {0xf2b, 16},
        {0xf2c, 8},
        {0xf30, 0},
        {0xc158, 192},
    }
)

var _cfunc_parse_with_padding = []loader.CFunc{
    {"_parse_with_padding_entry", 0,  _entry__parse_with_padding, 0, nil},
    {"_parse_with_padding", _entry__parse_with_padding, _size__parse_with_padding, _stack__parse_with_padding, _pcsp__parse_with_padding},
}
