// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_skip_number = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, // QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000010 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000020 LCPI0_1
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000020 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000030 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000040 LCPI0_2
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000040 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000050 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000060 LCPI0_3
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000060 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000070 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000080 LCPI0_4
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000080 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000090 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x000000a0 LCPI0_5
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x000000a0 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x000000b0 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x000000c0 LCPI0_6
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x000000c0 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x000000d0 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x000000e0 .p2align 4, 0x00
	//0x000000e0 LCPI0_7
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000e0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000000f0 LCPI0_8
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000f0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000100 LCPI0_9
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x00000100 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x00000110 LCPI0_10
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x00000110 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x00000120 LCPI0_11
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000120 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000130 LCPI0_12
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000130 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000140 LCPI0_13
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000140 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x00000150 .p2align 4, 0x90
	//0x00000150 _skip_number
	0x55, //0x00000150 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000151 movq         %rsp, %rbp
	0x41, 0x57, //0x00000154 pushq        %r15
	0x41, 0x56, //0x00000156 pushq        %r14
	0x41, 0x55, //0x00000158 pushq        %r13
	0x41, 0x54, //0x0000015a pushq        %r12
	0x53, //0x0000015c pushq        %rbx
	0x48, 0x83, 0xec, 0x28, //0x0000015d subq         $40, %rsp
	0x4c, 0x8b, 0x0f, //0x00000161 movq         (%rdi), %r9
	0x4c, 0x8b, 0x57, 0x08, //0x00000164 movq         $8(%rdi), %r10
	0x48, 0x8b, 0x16, //0x00000168 movq         (%rsi), %rdx
	0x49, 0x29, 0xd2, //0x0000016b subq         %rdx, %r10
	0x31, 0xdb, //0x0000016e xorl         %ebx, %ebx
	0x41, 0x80, 0x3c, 0x11, 0x2d, //0x00000170 cmpb         $45, (%r9,%rdx)
	0x4d, 0x8d, 0x04, 0x11, //0x00000175 leaq         (%r9,%rdx), %r8
	0x0f, 0x94, 0xc3, //0x00000179 sete         %bl
	0x4d, 0x8d, 0x34, 0x18, //0x0000017c leaq         (%r8,%rbx), %r14
	0x49, 0x29, 0xda, //0x00000180 subq         %rbx, %r10
	0x0f, 0x84, 0x90, 0x05, 0x00, 0x00, //0x00000183 je           LBB0_1
	0x41, 0x8a, 0x3e, //0x00000189 movb         (%r14), %dil
	0x8d, 0x4f, 0xc6, //0x0000018c leal         $-58(%rdi), %ecx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000018f movq         $-2, %rax
	0x80, 0xf9, 0xf6, //0x00000196 cmpb         $-10, %cl
	0x0f, 0x82, 0x62, 0x05, 0x00, 0x00, //0x00000199 jb           LBB0_83
	0x48, 0x89, 0x55, 0xb0, //0x0000019f movq         %rdx, $-80(%rbp)
	0x40, 0x80, 0xff, 0x30, //0x000001a3 cmpb         $48, %dil
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x000001a7 jne          LBB0_7
	0xba, 0x01, 0x00, 0x00, 0x00, //0x000001ad movl         $1, %edx
	0x49, 0x83, 0xfa, 0x01, //0x000001b2 cmpq         $1, %r10
	0x0f, 0x84, 0x3e, 0x05, 0x00, 0x00, //0x000001b6 je           LBB0_82
	0x41, 0x8a, 0x46, 0x01, //0x000001bc movb         $1(%r14), %al
	0x04, 0xd2, //0x000001c0 addb         $-46, %al
	0x3c, 0x37, //0x000001c2 cmpb         $55, %al
	0x0f, 0x87, 0x30, 0x05, 0x00, 0x00, //0x000001c4 ja           LBB0_82
	0x0f, 0xb6, 0xc0, //0x000001ca movzbl       %al, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000001cd movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x000001d7 btq          %rax, %rcx
	0x0f, 0x83, 0x19, 0x05, 0x00, 0x00, //0x000001db jae          LBB0_82
	//0x000001e1 LBB0_7
	0x49, 0x83, 0xfa, 0x20, //0x000001e1 cmpq         $32, %r10
	0x4c, 0x89, 0x45, 0xc8, //0x000001e5 movq         %r8, $-56(%rbp)
	0x48, 0x89, 0x5d, 0xd0, //0x000001e9 movq         %rbx, $-48(%rbp)
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x000001ed movq         $-1, %r13
	0x0f, 0x82, 0x2b, 0x05, 0x00, 0x00, //0x000001f4 jb           LBB0_8
	0x31, 0xd2, //0x000001fa xorl         %edx, %edx
	0xc5, 0xfe, 0x6f, 0x05, 0xfc, 0xfd, 0xff, 0xff, //0x000001fc vmovdqu      $-516(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x14, 0xfe, 0xff, 0xff, //0x00000204 vmovdqu      $-492(%rip), %ymm1  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0x2c, 0xfe, 0xff, 0xff, //0x0000020c vmovdqu      $-468(%rip), %ymm2  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x44, 0xfe, 0xff, 0xff, //0x00000214 vmovdqu      $-444(%rip), %ymm3  /* LCPI0_3+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x5c, 0xfe, 0xff, 0xff, //0x0000021c vmovdqu      $-420(%rip), %ymm4  /* LCPI0_4+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x74, 0xfe, 0xff, 0xff, //0x00000224 vmovdqu      $-396(%rip), %ymm5  /* LCPI0_5+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x8c, 0xfe, 0xff, 0xff, //0x0000022c vmovdqu      $-372(%rip), %ymm6  /* LCPI0_6+0(%rip) */
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000234 movq         $-1, %r15
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000023b movq         $-1, %r12
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000242 .p2align 4, 0x90
	//0x00000250 LBB0_10
	0xc4, 0xc1, 0x7e, 0x6f, 0x3c, 0x16, //0x00000250 vmovdqu      (%r14,%rdx), %ymm7
	0xc5, 0x45, 0x74, 0xc0, //0x00000256 vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc5, 0x45, 0x74, 0xc9, //0x0000025a vpcmpeqb     %ymm1, %ymm7, %ymm9
	0xc4, 0x41, 0x35, 0xeb, 0xc0, //0x0000025e vpor         %ymm8, %ymm9, %ymm8
	0xc5, 0x45, 0xdb, 0xca, //0x00000263 vpand        %ymm2, %ymm7, %ymm9
	0xc5, 0x35, 0x74, 0xcc, //0x00000267 vpcmpeqb     %ymm4, %ymm9, %ymm9
	0xc5, 0x45, 0x74, 0xd3, //0x0000026b vpcmpeqb     %ymm3, %ymm7, %ymm10
	0xc4, 0xc1, 0x7d, 0xd7, 0xc2, //0x0000026f vpmovmskb    %ymm10, %eax
	0xc4, 0xc1, 0x7d, 0xd7, 0xf9, //0x00000274 vpmovmskb    %ymm9, %edi
	0xc4, 0x41, 0x7d, 0xd7, 0xc0, //0x00000279 vpmovmskb    %ymm8, %r8d
	0xc5, 0xc5, 0xfc, 0xfd, //0x0000027e vpaddb       %ymm5, %ymm7, %ymm7
	0xc5, 0x45, 0xda, 0xde, //0x00000282 vpminub      %ymm6, %ymm7, %ymm11
	0xc5, 0xa5, 0x74, 0xff, //0x00000286 vpcmpeqb     %ymm7, %ymm11, %ymm7
	0xc4, 0x41, 0x2d, 0xeb, 0xc9, //0x0000028a vpor         %ymm9, %ymm10, %ymm9
	0xc5, 0xb5, 0xeb, 0xff, //0x0000028f vpor         %ymm7, %ymm9, %ymm7
	0xc5, 0xbd, 0xeb, 0xff, //0x00000293 vpor         %ymm7, %ymm8, %ymm7
	0xc5, 0xfd, 0xd7, 0xcf, //0x00000297 vpmovmskb    %ymm7, %ecx
	0x48, 0xf7, 0xd1, //0x0000029b notq         %rcx
	0x4c, 0x0f, 0xbc, 0xd9, //0x0000029e bsfq         %rcx, %r11
	0x41, 0x83, 0xfb, 0x20, //0x000002a2 cmpl         $32, %r11d
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000002a6 je           LBB0_12
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x000002ac movl         $-1, %ebx
	0x44, 0x89, 0xd9, //0x000002b1 movl         %r11d, %ecx
	0xd3, 0xe3, //0x000002b4 shll         %cl, %ebx
	0xf7, 0xd3, //0x000002b6 notl         %ebx
	0x21, 0xd8, //0x000002b8 andl         %ebx, %eax
	0x21, 0xdf, //0x000002ba andl         %ebx, %edi
	0x44, 0x21, 0xc3, //0x000002bc andl         %r8d, %ebx
	0x41, 0x89, 0xd8, //0x000002bf movl         %ebx, %r8d
	//0x000002c2 LBB0_12
	0x8d, 0x48, 0xff, //0x000002c2 leal         $-1(%rax), %ecx
	0x21, 0xc1, //0x000002c5 andl         %eax, %ecx
	0x0f, 0x85, 0xb9, 0x03, 0x00, 0x00, //0x000002c7 jne          LBB0_13
	0x8d, 0x4f, 0xff, //0x000002cd leal         $-1(%rdi), %ecx
	0x21, 0xf9, //0x000002d0 andl         %edi, %ecx
	0x0f, 0x85, 0xae, 0x03, 0x00, 0x00, //0x000002d2 jne          LBB0_13
	0x41, 0x8d, 0x48, 0xff, //0x000002d8 leal         $-1(%r8), %ecx
	0x44, 0x21, 0xc1, //0x000002dc andl         %r8d, %ecx
	0x0f, 0x85, 0xa1, 0x03, 0x00, 0x00, //0x000002df jne          LBB0_13
	0x85, 0xc0, //0x000002e5 testl        %eax, %eax
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000002e7 je           LBB0_20
	0x0f, 0xbc, 0xc8, //0x000002ed bsfl         %eax, %ecx
	0x49, 0x83, 0xfc, 0xff, //0x000002f0 cmpq         $-1, %r12
	0x0f, 0x85, 0xaa, 0x03, 0x00, 0x00, //0x000002f4 jne          LBB0_84
	0x48, 0x01, 0xd1, //0x000002fa addq         %rdx, %rcx
	0x49, 0x89, 0xcc, //0x000002fd movq         %rcx, %r12
	//0x00000300 LBB0_20
	0x85, 0xff, //0x00000300 testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000302 je           LBB0_23
	0x0f, 0xbc, 0xcf, //0x00000308 bsfl         %edi, %ecx
	0x49, 0x83, 0xff, 0xff, //0x0000030b cmpq         $-1, %r15
	0x0f, 0x85, 0x8f, 0x03, 0x00, 0x00, //0x0000030f jne          LBB0_84
	0x48, 0x01, 0xd1, //0x00000315 addq         %rdx, %rcx
	0x49, 0x89, 0xcf, //0x00000318 movq         %rcx, %r15
	//0x0000031b LBB0_23
	0x45, 0x85, 0xc0, //0x0000031b testl        %r8d, %r8d
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000031e je           LBB0_26
	0x41, 0x0f, 0xbc, 0xc0, //0x00000324 bsfl         %r8d, %eax
	0x49, 0x83, 0xfd, 0xff, //0x00000328 cmpq         $-1, %r13
	0x0f, 0x85, 0x81, 0x03, 0x00, 0x00, //0x0000032c jne          LBB0_85
	0x48, 0x01, 0xd0, //0x00000332 addq         %rdx, %rax
	0x49, 0x89, 0xc5, //0x00000335 movq         %rax, %r13
	//0x00000338 LBB0_26
	0x41, 0x83, 0xfb, 0x20, //0x00000338 cmpl         $32, %r11d
	0x0f, 0x85, 0x27, 0x02, 0x00, 0x00, //0x0000033c jne          LBB0_86
	0x49, 0x83, 0xc2, 0xe0, //0x00000342 addq         $-32, %r10
	0x48, 0x83, 0xc2, 0x20, //0x00000346 addq         $32, %rdx
	0x49, 0x83, 0xfa, 0x1f, //0x0000034a cmpq         $31, %r10
	0x0f, 0x87, 0xfc, 0xfe, 0xff, 0xff, //0x0000034e ja           LBB0_10
	0xc5, 0xf8, 0x77, //0x00000354 vzeroupper   
	0x4c, 0x01, 0xf2, //0x00000357 addq         %r14, %rdx
	0x49, 0x89, 0xd3, //0x0000035a movq         %rdx, %r11
	0x49, 0x83, 0xfa, 0x10, //0x0000035d cmpq         $16, %r10
	0x0f, 0x82, 0x69, 0x01, 0x00, 0x00, //0x00000361 jb           LBB0_51
	//0x00000367 LBB0_30
	0x4c, 0x89, 0x4d, 0xb8, //0x00000367 movq         %r9, $-72(%rbp)
	0x48, 0x89, 0xf3, //0x0000036b movq         %rsi, %rbx
	0x4c, 0x89, 0xf0, //0x0000036e movq         %r14, %rax
	0x4d, 0x89, 0xde, //0x00000371 movq         %r11, %r14
	0x48, 0x89, 0x45, 0xc0, //0x00000374 movq         %rax, $-64(%rbp)
	0x49, 0x29, 0xc6, //0x00000378 subq         %rax, %r14
	0x31, 0xd2, //0x0000037b xorl         %edx, %edx
	0xc5, 0x7a, 0x6f, 0x05, 0x5b, 0xfd, 0xff, 0xff, //0x0000037d vmovdqu      $-677(%rip), %xmm8  /* LCPI0_7+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x0d, 0x63, 0xfd, 0xff, 0xff, //0x00000385 vmovdqu      $-669(%rip), %xmm9  /* LCPI0_8+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x15, 0x6b, 0xfd, 0xff, 0xff, //0x0000038d vmovdqu      $-661(%rip), %xmm10  /* LCPI0_9+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x1d, 0x73, 0xfd, 0xff, 0xff, //0x00000395 vmovdqu      $-653(%rip), %xmm11  /* LCPI0_10+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x25, 0x7b, 0xfd, 0xff, 0xff, //0x0000039d vmovdqu      $-645(%rip), %xmm4  /* LCPI0_11+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x2d, 0x83, 0xfd, 0xff, 0xff, //0x000003a5 vmovdqu      $-637(%rip), %xmm5  /* LCPI0_12+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x35, 0x8b, 0xfd, 0xff, 0xff, //0x000003ad vmovdqu      $-629(%rip), %xmm6  /* LCPI0_13+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003b5 .p2align 4, 0x90
	//0x000003c0 LBB0_31
	0xc4, 0xc1, 0x7a, 0x6f, 0x3c, 0x13, //0x000003c0 vmovdqu      (%r11,%rdx), %xmm7
	0xc5, 0xb9, 0x74, 0xc7, //0x000003c6 vpcmpeqb     %xmm7, %xmm8, %xmm0
	0xc5, 0xb1, 0x74, 0xcf, //0x000003ca vpcmpeqb     %xmm7, %xmm9, %xmm1
	0xc5, 0xf1, 0xeb, 0xc0, //0x000003ce vpor         %xmm0, %xmm1, %xmm0
	0xc5, 0xa9, 0xfc, 0xcf, //0x000003d2 vpaddb       %xmm7, %xmm10, %xmm1
	0xc5, 0xa1, 0xda, 0xd1, //0x000003d6 vpminub      %xmm1, %xmm11, %xmm2
	0xc5, 0xf1, 0x74, 0xca, //0x000003da vpcmpeqb     %xmm2, %xmm1, %xmm1
	0xc5, 0xc1, 0xdb, 0xd4, //0x000003de vpand        %xmm4, %xmm7, %xmm2
	0xc5, 0xe9, 0x74, 0xd6, //0x000003e2 vpcmpeqb     %xmm6, %xmm2, %xmm2
	0xc5, 0xc1, 0x74, 0xfd, //0x000003e6 vpcmpeqb     %xmm5, %xmm7, %xmm7
	0xc5, 0xe9, 0xeb, 0xdf, //0x000003ea vpor         %xmm7, %xmm2, %xmm3
	0xc5, 0xe1, 0xeb, 0xd8, //0x000003ee vpor         %xmm0, %xmm3, %xmm3
	0xc5, 0xe1, 0xeb, 0xc9, //0x000003f2 vpor         %xmm1, %xmm3, %xmm1
	0xc5, 0xf9, 0xd7, 0xf7, //0x000003f6 vpmovmskb    %xmm7, %esi
	0xc5, 0x79, 0xd7, 0xc2, //0x000003fa vpmovmskb    %xmm2, %r8d
	0xc5, 0xf9, 0xd7, 0xc0, //0x000003fe vpmovmskb    %xmm0, %eax
	0xc5, 0xf9, 0xd7, 0xc9, //0x00000402 vpmovmskb    %xmm1, %ecx
	0xf7, 0xd1, //0x00000406 notl         %ecx
	0x0f, 0xbc, 0xc9, //0x00000408 bsfl         %ecx, %ecx
	0x83, 0xf9, 0x10, //0x0000040b cmpl         $16, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000040e je           LBB0_33
	0xbf, 0xff, 0xff, 0xff, 0xff, //0x00000414 movl         $-1, %edi
	0xd3, 0xe7, //0x00000419 shll         %cl, %edi
	0xf7, 0xd7, //0x0000041b notl         %edi
	0x21, 0xfe, //0x0000041d andl         %edi, %esi
	0x41, 0x21, 0xf8, //0x0000041f andl         %edi, %r8d
	0x21, 0xc7, //0x00000422 andl         %eax, %edi
	0x89, 0xf8, //0x00000424 movl         %edi, %eax
	//0x00000426 LBB0_33
	0x44, 0x8d, 0x4e, 0xff, //0x00000426 leal         $-1(%rsi), %r9d
	0x41, 0x21, 0xf1, //0x0000042a andl         %esi, %r9d
	0x0f, 0x85, 0x94, 0x02, 0x00, 0x00, //0x0000042d jne          LBB0_34
	0x41, 0x8d, 0x78, 0xff, //0x00000433 leal         $-1(%r8), %edi
	0x44, 0x21, 0xc7, //0x00000437 andl         %r8d, %edi
	0x0f, 0x85, 0x6b, 0x02, 0x00, 0x00, //0x0000043a jne          LBB0_37
	0x8d, 0x78, 0xff, //0x00000440 leal         $-1(%rax), %edi
	0x21, 0xc7, //0x00000443 andl         %eax, %edi
	0x0f, 0x85, 0x60, 0x02, 0x00, 0x00, //0x00000445 jne          LBB0_37
	0x85, 0xf6, //0x0000044b testl        %esi, %esi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x0000044d je           LBB0_42
	0x0f, 0xbc, 0xf6, //0x00000453 bsfl         %esi, %esi
	0x49, 0x83, 0xfc, 0xff, //0x00000456 cmpq         $-1, %r12
	0x0f, 0x85, 0x60, 0x02, 0x00, 0x00, //0x0000045a jne          LBB0_87
	0x4c, 0x01, 0xf6, //0x00000460 addq         %r14, %rsi
	0x48, 0x01, 0xd6, //0x00000463 addq         %rdx, %rsi
	0x49, 0x89, 0xf4, //0x00000466 movq         %rsi, %r12
	//0x00000469 LBB0_42
	0x45, 0x85, 0xc0, //0x00000469 testl        %r8d, %r8d
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x0000046c je           LBB0_45
	0x41, 0x0f, 0xbc, 0xf0, //0x00000472 bsfl         %r8d, %esi
	0x49, 0x83, 0xff, 0xff, //0x00000476 cmpq         $-1, %r15
	0x0f, 0x85, 0x40, 0x02, 0x00, 0x00, //0x0000047a jne          LBB0_87
	0x4c, 0x01, 0xf6, //0x00000480 addq         %r14, %rsi
	0x48, 0x01, 0xd6, //0x00000483 addq         %rdx, %rsi
	0x49, 0x89, 0xf7, //0x00000486 movq         %rsi, %r15
	//0x00000489 LBB0_45
	0x85, 0xc0, //0x00000489 testl        %eax, %eax
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x0000048b je           LBB0_48
	0x0f, 0xbc, 0xc0, //0x00000491 bsfl         %eax, %eax
	0x49, 0x83, 0xfd, 0xff, //0x00000494 cmpq         $-1, %r13
	0x0f, 0x85, 0x32, 0x02, 0x00, 0x00, //0x00000498 jne          LBB0_88
	0x4c, 0x01, 0xf0, //0x0000049e addq         %r14, %rax
	0x48, 0x01, 0xd0, //0x000004a1 addq         %rdx, %rax
	0x49, 0x89, 0xc5, //0x000004a4 movq         %rax, %r13
	//0x000004a7 LBB0_48
	0x83, 0xf9, 0x10, //0x000004a7 cmpl         $16, %ecx
	0x0f, 0x85, 0xd7, 0x00, 0x00, 0x00, //0x000004aa jne          LBB0_89
	0x49, 0x83, 0xc2, 0xf0, //0x000004b0 addq         $-16, %r10
	0x48, 0x83, 0xc2, 0x10, //0x000004b4 addq         $16, %rdx
	0x49, 0x83, 0xfa, 0x0f, //0x000004b8 cmpq         $15, %r10
	0x0f, 0x87, 0xfe, 0xfe, 0xff, 0xff, //0x000004bc ja           LBB0_31
	0x49, 0x01, 0xd3, //0x000004c2 addq         %rdx, %r11
	0x48, 0x89, 0xde, //0x000004c5 movq         %rbx, %rsi
	0x4c, 0x8b, 0x4d, 0xb8, //0x000004c8 movq         $-72(%rbp), %r9
	0x4c, 0x8b, 0x75, 0xc0, //0x000004cc movq         $-64(%rbp), %r14
	//0x000004d0 LBB0_51
	0x4d, 0x85, 0xd2, //0x000004d0 testq        %r10, %r10
	0x0f, 0x84, 0xd9, 0x00, 0x00, 0x00, //0x000004d3 je           LBB0_66
	0x4f, 0x8d, 0x04, 0x13, //0x000004d9 leaq         (%r11,%r10), %r8
	0x4c, 0x89, 0xd9, //0x000004dd movq         %r11, %rcx
	0x4c, 0x29, 0xf1, //0x000004e0 subq         %r14, %rcx
	0x31, 0xd2, //0x000004e3 xorl         %edx, %edx
	0x48, 0x8d, 0x3d, 0x5c, 0x02, 0x00, 0x00, //0x000004e5 leaq         $604(%rip), %rdi  /* LJTI0_0+0(%rip) */
	0xe9, 0x2c, 0x00, 0x00, 0x00, //0x000004ec jmp          LBB0_53
	//0x000004f1 LBB0_55
	0x83, 0xfb, 0x65, //0x000004f1 cmpl         $101, %ebx
	0x0f, 0x85, 0xb5, 0x00, 0x00, 0x00, //0x000004f4 jne          LBB0_65
	//0x000004fa LBB0_56
	0x49, 0x83, 0xff, 0xff, //0x000004fa cmpq         $-1, %r15
	0x0f, 0x85, 0x8a, 0x01, 0x00, 0x00, //0x000004fe jne          LBB0_90
	0x4c, 0x8d, 0x3c, 0x11, //0x00000504 leaq         (%rcx,%rdx), %r15
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000508 .p2align 4, 0x90
	//0x00000510 LBB0_63
	0x48, 0x83, 0xc2, 0x01, //0x00000510 addq         $1, %rdx
	0x49, 0x39, 0xd2, //0x00000514 cmpq         %rdx, %r10
	0x0f, 0x84, 0x44, 0x01, 0x00, 0x00, //0x00000517 je           LBB0_64
	//0x0000051d LBB0_53
	0x41, 0x0f, 0xbe, 0x1c, 0x13, //0x0000051d movsbl       (%r11,%rdx), %ebx
	0x8d, 0x43, 0xd0, //0x00000522 leal         $-48(%rbx), %eax
	0x83, 0xf8, 0x0a, //0x00000525 cmpl         $10, %eax
	0x0f, 0x82, 0xe2, 0xff, 0xff, 0xff, //0x00000528 jb           LBB0_63
	0x8d, 0x43, 0xd5, //0x0000052e leal         $-43(%rbx), %eax
	0x83, 0xf8, 0x1a, //0x00000531 cmpl         $26, %eax
	0x0f, 0x87, 0xb7, 0xff, 0xff, 0xff, //0x00000534 ja           LBB0_55
	0x48, 0x63, 0x04, 0x87, //0x0000053a movslq       (%rdi,%rax,4), %rax
	0x48, 0x01, 0xf8, //0x0000053e addq         %rdi, %rax
	0xff, 0xe0, //0x00000541 jmpq         *%rax
	//0x00000543 LBB0_61
	0x49, 0x83, 0xfd, 0xff, //0x00000543 cmpq         $-1, %r13
	0x0f, 0x85, 0x41, 0x01, 0x00, 0x00, //0x00000547 jne          LBB0_90
	0x4c, 0x8d, 0x2c, 0x11, //0x0000054d leaq         (%rcx,%rdx), %r13
	0xe9, 0xba, 0xff, 0xff, 0xff, //0x00000551 jmp          LBB0_63
	//0x00000556 LBB0_59
	0x49, 0x83, 0xfc, 0xff, //0x00000556 cmpq         $-1, %r12
	0x0f, 0x85, 0x2e, 0x01, 0x00, 0x00, //0x0000055a jne          LBB0_90
	0x4c, 0x8d, 0x24, 0x11, //0x00000560 leaq         (%rcx,%rdx), %r12
	0xe9, 0xa7, 0xff, 0xff, 0xff, //0x00000564 jmp          LBB0_63
	//0x00000569 LBB0_86
	0x49, 0x01, 0xd3, //0x00000569 addq         %rdx, %r11
	0x4d, 0x01, 0xf3, //0x0000056c addq         %r14, %r11
	0xc5, 0xf8, 0x77, //0x0000056f vzeroupper   
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00000572 movq         $-1, %rdx
	0x4d, 0x85, 0xe4, //0x00000579 testq        %r12, %r12
	0x0f, 0x85, 0x40, 0x00, 0x00, 0x00, //0x0000057c jne          LBB0_67
	0xe9, 0xed, 0x00, 0x00, 0x00, //0x00000582 jmp          LBB0_81
	//0x00000587 LBB0_89
	0x89, 0xc8, //0x00000587 movl         %ecx, %eax
	0x49, 0x01, 0xc3, //0x00000589 addq         %rax, %r11
	0x49, 0x01, 0xd3, //0x0000058c addq         %rdx, %r11
	0x48, 0x89, 0xde, //0x0000058f movq         %rbx, %rsi
	0x4c, 0x8b, 0x4d, 0xb8, //0x00000592 movq         $-72(%rbp), %r9
	0x4c, 0x8b, 0x75, 0xc0, //0x00000596 movq         $-64(%rbp), %r14
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000059a movq         $-1, %rdx
	0x4d, 0x85, 0xe4, //0x000005a1 testq        %r12, %r12
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x000005a4 jne          LBB0_67
	0xe9, 0xc5, 0x00, 0x00, 0x00, //0x000005aa jmp          LBB0_81
	//0x000005af LBB0_65
	0x49, 0x01, 0xd3, //0x000005af addq         %rdx, %r11
	//0x000005b2 LBB0_66
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000005b2 movq         $-1, %rdx
	0x4d, 0x85, 0xe4, //0x000005b9 testq        %r12, %r12
	0x0f, 0x84, 0xb2, 0x00, 0x00, 0x00, //0x000005bc je           LBB0_81
	//0x000005c2 LBB0_67
	0x4d, 0x85, 0xed, //0x000005c2 testq        %r13, %r13
	0x0f, 0x84, 0xa9, 0x00, 0x00, 0x00, //0x000005c5 je           LBB0_81
	0x4d, 0x85, 0xff, //0x000005cb testq        %r15, %r15
	0x0f, 0x84, 0xa0, 0x00, 0x00, 0x00, //0x000005ce je           LBB0_81
	0x4d, 0x29, 0xf3, //0x000005d4 subq         %r14, %r11
	0x49, 0x8d, 0x43, 0xff, //0x000005d7 leaq         $-1(%r11), %rax
	0x49, 0x39, 0xc4, //0x000005db cmpq         %rax, %r12
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x000005de je           LBB0_72
	0x49, 0x39, 0xc5, //0x000005e4 cmpq         %rax, %r13
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x000005e7 je           LBB0_72
	0x49, 0x39, 0xc7, //0x000005ed cmpq         %rax, %r15
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x000005f0 je           LBB0_72
	0x4d, 0x85, 0xed, //0x000005f6 testq        %r13, %r13
	0x0f, 0x8e, 0x23, 0x00, 0x00, 0x00, //0x000005f9 jle          LBB0_76
	0x49, 0x8d, 0x45, 0xff, //0x000005ff leaq         $-1(%r13), %rax
	0x49, 0x39, 0xc7, //0x00000603 cmpq         %rax, %r15
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000606 je           LBB0_76
	0x49, 0xf7, 0xd5, //0x0000060c notq         %r13
	0x4c, 0x89, 0xea, //0x0000060f movq         %r13, %rdx
	0xe9, 0xda, 0x00, 0x00, 0x00, //0x00000612 jmp          LBB0_80
	//0x00000617 LBB0_72
	0x49, 0xf7, 0xdb, //0x00000617 negq         %r11
	0x4c, 0x89, 0xda, //0x0000061a movq         %r11, %rdx
	0xe9, 0xcf, 0x00, 0x00, 0x00, //0x0000061d jmp          LBB0_80
	//0x00000622 LBB0_76
	0x4c, 0x89, 0xe0, //0x00000622 movq         %r12, %rax
	0x4c, 0x09, 0xf8, //0x00000625 orq          %r15, %rax
	0x0f, 0x99, 0xc0, //0x00000628 setns        %al
	0x0f, 0x88, 0x14, 0x00, 0x00, 0x00, //0x0000062b js           LBB0_79
	0x4d, 0x39, 0xfc, //0x00000631 cmpq         %r15, %r12
	0x0f, 0x8c, 0x0b, 0x00, 0x00, 0x00, //0x00000634 jl           LBB0_79
	0x49, 0xf7, 0xd4, //0x0000063a notq         %r12
	0x4c, 0x89, 0xe2, //0x0000063d movq         %r12, %rdx
	0xe9, 0xac, 0x00, 0x00, 0x00, //0x00000640 jmp          LBB0_80
	//0x00000645 LBB0_79
	0x49, 0x8d, 0x4f, 0xff, //0x00000645 leaq         $-1(%r15), %rcx
	0x49, 0x39, 0xcc, //0x00000649 cmpq         %rcx, %r12
	0x49, 0xf7, 0xd7, //0x0000064c notq         %r15
	0x4d, 0x0f, 0x45, 0xfb, //0x0000064f cmovneq      %r11, %r15
	0x84, 0xc0, //0x00000653 testb        %al, %al
	0x4d, 0x0f, 0x44, 0xfb, //0x00000655 cmoveq       %r11, %r15
	0x4c, 0x89, 0xfa, //0x00000659 movq         %r15, %rdx
	0xe9, 0x90, 0x00, 0x00, 0x00, //0x0000065c jmp          LBB0_80
	//0x00000661 LBB0_64
	0x4d, 0x89, 0xc3, //0x00000661 movq         %r8, %r11
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00000664 movq         $-1, %rdx
	0x4d, 0x85, 0xe4, //0x0000066b testq        %r12, %r12
	0x0f, 0x85, 0x4e, 0xff, 0xff, 0xff, //0x0000066e jne          LBB0_67
	//0x00000674 LBB0_81
	0x48, 0xf7, 0xd2, //0x00000674 notq         %rdx
	0x49, 0x01, 0xd6, //0x00000677 addq         %rdx, %r14
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000067a movq         $-2, %rax
	0xe9, 0x7b, 0x00, 0x00, 0x00, //0x00000681 jmp          LBB0_83
	//0x00000686 LBB0_13
	0x0f, 0xbc, 0xc1, //0x00000686 bsfl         %ecx, %eax
	0xe9, 0x27, 0x00, 0x00, 0x00, //0x00000689 jmp          LBB0_14
	//0x0000068e LBB0_90
	0x48, 0x8b, 0x45, 0xc8, //0x0000068e movq         $-56(%rbp), %rax
	0x48, 0x03, 0x45, 0xd0, //0x00000692 addq         $-48(%rbp), %rax
	0x4c, 0x29, 0xd8, //0x00000696 subq         %r11, %rax
	0x48, 0xf7, 0xd2, //0x00000699 notq         %rdx
	0x48, 0x01, 0xc2, //0x0000069c addq         %rax, %rdx
	0xe9, 0x4d, 0x00, 0x00, 0x00, //0x0000069f jmp          LBB0_80
	//0x000006a4 LBB0_84
	0x89, 0xc8, //0x000006a4 movl         %ecx, %eax
	0xe9, 0x0a, 0x00, 0x00, 0x00, //0x000006a6 jmp          LBB0_14
	//0x000006ab LBB0_37
	0x0f, 0xbc, 0xc7, //0x000006ab bsfl         %edi, %eax
	0xe9, 0x1f, 0x00, 0x00, 0x00, //0x000006ae jmp          LBB0_35
	//0x000006b3 LBB0_85
	0x89, 0xc0, //0x000006b3 movl         %eax, %eax
	//0x000006b5 LBB0_14
	0x48, 0xf7, 0xd2, //0x000006b5 notq         %rdx
	0x48, 0x29, 0xc2, //0x000006b8 subq         %rax, %rdx
	0xe9, 0x31, 0x00, 0x00, 0x00, //0x000006bb jmp          LBB0_80
	//0x000006c0 LBB0_87
	0x89, 0xf0, //0x000006c0 movl         %esi, %eax
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x000006c2 jmp          LBB0_35
	//0x000006c7 LBB0_34
	0x41, 0x0f, 0xbc, 0xc1, //0x000006c7 bsfl         %r9d, %eax
	0xe9, 0x02, 0x00, 0x00, 0x00, //0x000006cb jmp          LBB0_35
	//0x000006d0 LBB0_88
	0x89, 0xc0, //0x000006d0 movl         %eax, %eax
	//0x000006d2 LBB0_35
	0x48, 0x8b, 0x4d, 0xc8, //0x000006d2 movq         $-56(%rbp), %rcx
	0x48, 0x03, 0x4d, 0xd0, //0x000006d6 addq         $-48(%rbp), %rcx
	0x4c, 0x29, 0xd9, //0x000006da subq         %r11, %rcx
	0x48, 0x29, 0xc1, //0x000006dd subq         %rax, %rcx
	0x48, 0xf7, 0xd2, //0x000006e0 notq         %rdx
	0x48, 0x01, 0xca, //0x000006e3 addq         %rcx, %rdx
	0x48, 0x89, 0xde, //0x000006e6 movq         %rbx, %rsi
	0x4c, 0x8b, 0x4d, 0xb8, //0x000006e9 movq         $-72(%rbp), %r9
	0x4c, 0x8b, 0x75, 0xc0, //0x000006ed movq         $-64(%rbp), %r14
	//0x000006f1 LBB0_80
	0x48, 0x85, 0xd2, //0x000006f1 testq        %rdx, %rdx
	0x0f, 0x88, 0x7a, 0xff, 0xff, 0xff, //0x000006f4 js           LBB0_81
	//0x000006fa LBB0_82
	0x49, 0x01, 0xd6, //0x000006fa addq         %rdx, %r14
	0x48, 0x8b, 0x45, 0xb0, //0x000006fd movq         $-80(%rbp), %rax
	//0x00000701 LBB0_83
	0x4d, 0x29, 0xce, //0x00000701 subq         %r9, %r14
	0x4c, 0x89, 0x36, //0x00000704 movq         %r14, (%rsi)
	0x48, 0x83, 0xc4, 0x28, //0x00000707 addq         $40, %rsp
	0x5b, //0x0000070b popq         %rbx
	0x41, 0x5c, //0x0000070c popq         %r12
	0x41, 0x5d, //0x0000070e popq         %r13
	0x41, 0x5e, //0x00000710 popq         %r14
	0x41, 0x5f, //0x00000712 popq         %r15
	0x5d, //0x00000714 popq         %rbp
	0xc5, 0xf8, 0x77, //0x00000715 vzeroupper   
	0xc3, //0x00000718 retq         
	//0x00000719 LBB0_1
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000719 movq         $-1, %rax
	0xe9, 0xdc, 0xff, 0xff, 0xff, //0x00000720 jmp          LBB0_83
	//0x00000725 LBB0_8
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000725 movq         $-1, %r15
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000072c movq         $-1, %r12
	0x4d, 0x89, 0xf3, //0x00000733 movq         %r14, %r11
	0x49, 0x83, 0xfa, 0x10, //0x00000736 cmpq         $16, %r10
	0x0f, 0x83, 0x27, 0xfc, 0xff, 0xff, //0x0000073a jae          LBB0_30
	0xe9, 0x8b, 0xfd, 0xff, 0xff, //0x00000740 jmp          LBB0_51
	0x90, 0x90, 0x90, //0x00000745 .p2align 2, 0x90
	// // .set L0_0_set_61, LBB0_61-LJTI0_0
	// // .set L0_0_set_65, LBB0_65-LJTI0_0
	// // .set L0_0_set_59, LBB0_59-LJTI0_0
	// // .set L0_0_set_56, LBB0_56-LJTI0_0
	//0x00000748 LJTI0_0
	0xfb, 0xfd, 0xff, 0xff, //0x00000748 .long L0_0_set_61
	0x67, 0xfe, 0xff, 0xff, //0x0000074c .long L0_0_set_65
	0xfb, 0xfd, 0xff, 0xff, //0x00000750 .long L0_0_set_61
	0x0e, 0xfe, 0xff, 0xff, //0x00000754 .long L0_0_set_59
	0x67, 0xfe, 0xff, 0xff, //0x00000758 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x0000075c .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x00000760 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x00000764 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x00000768 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x0000076c .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x00000770 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x00000774 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x00000778 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x0000077c .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x00000780 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x00000784 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x00000788 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x0000078c .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x00000790 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x00000794 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x00000798 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x0000079c .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x000007a0 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x000007a4 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x000007a8 .long L0_0_set_65
	0x67, 0xfe, 0xff, 0xff, //0x000007ac .long L0_0_set_65
	0xb2, 0xfd, 0xff, 0xff, //0x000007b0 .long L0_0_set_56
	//0x000007b4 .p2align 2, 0x00
	//0x000007b4 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x000007b4 .long 2
}
 
