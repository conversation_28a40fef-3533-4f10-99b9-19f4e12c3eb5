// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_number = 336
)

const (
    _stack__skip_number = 88
)

const (
    _size__skip_number = 1528
)

var (
    _pcsp__skip_number = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0x5bb, 88},
        {0x5bc, 48},
        {0x5be, 40},
        {0x5c0, 32},
        {0x5c2, 24},
        {0x5c4, 16},
        {0x5c5, 8},
        {0x5c9, 0},
        {0x5f8, 88},
    }
)

var _cfunc_skip_number = []loader.CFunc{
    {"_skip_number_entry", 0,  _entry__skip_number, 0, nil},
    {"_skip_number", _entry__skip_number, _size__skip_number, _stack__skip_number, _pcsp__skip_number},
}
