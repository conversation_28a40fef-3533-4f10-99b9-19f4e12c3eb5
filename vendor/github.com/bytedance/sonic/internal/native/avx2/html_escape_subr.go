// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__html_escape = 192
)

const (
    _stack__html_escape = 72
)

const (
    _size__html_escape = 2048
)

var (
    _pcsp__html_escape = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0x7e5, 72},
        {0x7e6, 48},
        {0x7e8, 40},
        {0x7ea, 32},
        {0x7ec, 24},
        {0x7ee, 16},
        {0x7ef, 8},
        {0x800, 0},
    }
)

var _cfunc_html_escape = []loader.CFunc{
    {"_html_escape_entry", 0,  _entry__html_escape, 0, nil},
    {"_html_escape", _entry__html_escape, _size__html_escape, _stack__html_escape, _pcsp__html_escape},
}
