// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__value = 576
)

const (
    _stack__value = 120
)

const (
    _size__value = 12840
)

var (
    _pcsp__value = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0x11ca, 120},
        {0x11cb, 48},
        {0x11cd, 40},
        {0x11cf, 32},
        {0x11d1, 24},
        {0x11d3, 16},
        {0x11d4, 8},
        {0x11d8, 0},
        {0x3228, 120},
    }
)

var _cfunc_value = []loader.CFunc{
    {"_value_entry", 0,  _entry__value, 0, nil},
    {"_value", _entry__value, _size__value, _stack__value, _pcsp__value},
}
