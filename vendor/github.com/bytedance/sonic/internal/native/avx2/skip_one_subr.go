// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__skip_one = 672
)

const (
    _stack__skip_one = 208
)

const (
    _size__skip_one = 12508
)

var (
    _pcsp__skip_one = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x14, 48},
        {0x2ea6, 208},
        {0x2ea7, 48},
        {0x2ea9, 40},
        {0x2eab, 32},
        {0x2ead, 24},
        {0x2eaf, 16},
        {0x2eb0, 8},
        {0x2eb4, 0},
        {0x30dc, 208},
    }
)

var _cfunc_skip_one = []loader.CFunc{
    {"_skip_one_entry", 0,  _entry__skip_one, 0, nil},
    {"_skip_one", _entry__skip_one, _size__skip_one, _stack__skip_one, _pcsp__skip_one},
}
