// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__quote = 144
)

const (
    _stack__quote = 72
)

const (
    _size__quote = 2720
)

var (
    _pcsp__quote = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0xa6c, 72},
        {0xa6d, 48},
        {0xa6f, 40},
        {0xa71, 32},
        {0xa73, 24},
        {0xa75, 16},
        {0xa76, 8},
        {0xa7a, 0},
        {0xaa0, 72},
    }
)

var _cfunc_quote = []loader.CFunc{
    {"_quote_entry", 0,  _entry__quote, 0, nil},
    {"_quote", _entry__quote, _size__quote, _stack__quote, _pcsp__quote},
}
