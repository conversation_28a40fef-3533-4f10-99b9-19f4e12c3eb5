// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__validate_one = 704
)

const (
    _stack__validate_one = 208
)

const (
    _size__validate_one = 15888
)

var (
    _pcsp__validate_one = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x14, 48},
        {0x3b41, 208},
        {0x3b42, 48},
        {0x3b44, 40},
        {0x3b46, 32},
        {0x3b48, 24},
        {0x3b4a, 16},
        {0x3b4b, 8},
        {0x3b4f, 0},
        {0x3e10, 208},
    }
)

var _cfunc_validate_one = []loader.CFunc{
    {"_validate_one_entry", 0,  _entry__validate_one, 0, nil},
    {"_validate_one", _entry__validate_one, _size__validate_one, _stack__validate_one, _pcsp__validate_one},
}
