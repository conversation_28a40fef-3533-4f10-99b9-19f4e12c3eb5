// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__vnumber = 128
)

const (
    _stack__vnumber = 136
)

const (
    _size__vnumber = 8700
)

var (
    _pcsp__vnumber = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0xc71, 136},
        {0xc72, 48},
        {0xc74, 40},
        {0xc76, 32},
        {0xc78, 24},
        {0xc7a, 16},
        {0xc7b, 8},
        {0xc7f, 0},
        {0x21fc, 136},
    }
)

var _cfunc_vnumber = []loader.CFunc{
    {"_vnumber_entry", 0,  _entry__vnumber, 0, nil},
    {"_vnumber", _entry__vnumber, _size__vnumber, _stack__vnumber, _pcsp__vnumber},
}
