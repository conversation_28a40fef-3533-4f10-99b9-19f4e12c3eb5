// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_get_by_path = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 LCPI0_1
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000020 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000030 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000040 LCPI0_2
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000040 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000050 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000060 LCPI0_3
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000060 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000070 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000080 LCPI0_7
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000080 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000090 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x000000a0 LCPI0_8
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000a0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x000000b0 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x000000c0 LCPI0_9
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x000000c0 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x000000d0 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x000000e0 LCPI0_10
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x000000e0 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x000000f0 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000100 LCPI0_11
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000100 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000110 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000120 LCPI0_13
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000120 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000130 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000140 LCPI0_14
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000140 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000150 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000160 LCPI0_15
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000160 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000170 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000180 LCPI0_16
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000180 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000190 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x000001a0 LCPI0_17
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x000001a0 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x000001b0 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x000001c0 LCPI0_18
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x000001c0 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x000001d0 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x000001e0 .p2align 4, 0x00
	//0x000001e0 LCPI0_4
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x000001e0 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x000001f0 LCPI0_5
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x000001f0 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000200 LCPI0_6
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000200 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000210 LCPI0_12
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000210 .quad 1
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000218 .quad 0
	//0x00000220 LCPI0_19
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000220 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000230 LCPI0_20
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000230 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000240 LCPI0_21
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x00000240 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x00000250 LCPI0_22
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x00000250 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x00000260 LCPI0_23
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000260 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000270 LCPI0_24
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000270 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x00000280 .p2align 4, 0x90
	//0x00000280 _get_by_path
	0x55, //0x00000280 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000281 movq         %rsp, %rbp
	0x41, 0x57, //0x00000284 pushq        %r15
	0x41, 0x56, //0x00000286 pushq        %r14
	0x41, 0x55, //0x00000288 pushq        %r13
	0x41, 0x54, //0x0000028a pushq        %r12
	0x53, //0x0000028c pushq        %rbx
	0x48, 0x81, 0xec, 0xc0, 0x00, 0x00, 0x00, //0x0000028d subq         $192, %rsp
	0x49, 0x89, 0xcb, //0x00000294 movq         %rcx, %r11
	0x49, 0x89, 0xf5, //0x00000297 movq         %rsi, %r13
	0x49, 0x89, 0xfa, //0x0000029a movq         %rdi, %r10
	0x48, 0x8b, 0x42, 0x08, //0x0000029d movq         $8(%rdx), %rax
	0x48, 0x85, 0xc0, //0x000002a1 testq        %rax, %rax
	0x48, 0x89, 0x74, 0x24, 0x10, //0x000002a4 movq         %rsi, $16(%rsp)
	0x48, 0x89, 0x7c, 0x24, 0x18, //0x000002a9 movq         %rdi, $24(%rsp)
	0x48, 0x89, 0x4c, 0x24, 0x20, //0x000002ae movq         %rcx, $32(%rsp)
	0x0f, 0x84, 0x1b, 0x2b, 0x00, 0x00, //0x000002b3 je           LBB0_520
	0x48, 0x8b, 0x0a, //0x000002b9 movq         (%rdx), %rcx
	0x48, 0xc1, 0xe0, 0x04, //0x000002bc shlq         $4, %rax
	0x48, 0x89, 0x4c, 0x24, 0x30, //0x000002c0 movq         %rcx, $48(%rsp)
	0x48, 0x01, 0xc8, //0x000002c5 addq         %rcx, %rax
	0x48, 0x89, 0x84, 0x24, 0xa0, 0x00, 0x00, 0x00, //0x000002c8 movq         %rax, $160(%rsp)
	0x4d, 0x8d, 0x42, 0x08, //0x000002d0 leaq         $8(%r10), %r8
	0x4d, 0x8b, 0x22, //0x000002d4 movq         (%r10), %r12
	0x49, 0x8b, 0x5d, 0x00, //0x000002d7 movq         (%r13), %rbx
	0xc5, 0xfe, 0x6f, 0x05, 0x1d, 0xfd, 0xff, 0xff, //0x000002db vmovdqu      $-739(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x95, 0xfd, 0xff, 0xff, //0x000002e3 vmovdqu      $-619(%rip), %ymm1  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0xad, 0xfd, 0xff, 0xff, //0x000002eb vmovdqu      $-595(%rip), %ymm2  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x25, 0xfd, 0xff, 0xff, //0x000002f3 vmovdqu      $-731(%rip), %ymm3  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x3d, 0xfd, 0xff, 0xff, //0x000002fb vmovdqu      $-707(%rip), %ymm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x55, 0xfd, 0xff, 0xff, //0x00000303 vmovdqu      $-683(%rip), %ymm5  /* LCPI0_3+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x0d, 0xdd, 0xfe, 0xff, 0xff, //0x0000030b vmovdqu      $-291(%rip), %xmm9  /* LCPI0_5+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x1d, 0xe5, 0xfe, 0xff, 0xff, //0x00000313 vmovdqu      $-283(%rip), %xmm11  /* LCPI0_6+0(%rip) */
	0xc4, 0x41, 0x39, 0x76, 0xc0, //0x0000031b vpcmpeqd     %xmm8, %xmm8, %xmm8
	0xc5, 0x7e, 0x6f, 0x15, 0xd8, 0xfd, 0xff, 0xff, //0x00000320 vmovdqu      $-552(%rip), %ymm10  /* LCPI0_11+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x90, 0xfd, 0xff, 0xff, //0x00000328 vmovdqu      $-624(%rip), %ymm12  /* LCPI0_9+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xa8, 0xfd, 0xff, 0xff, //0x00000330 vmovdqu      $-600(%rip), %ymm13  /* LCPI0_10+0(%rip) */
	//0x00000338 LBB0_2
	0x49, 0x8b, 0x10, //0x00000338 movq         (%r8), %rdx
	0x48, 0x39, 0xd3, //0x0000033b cmpq         %rdx, %rbx
	0x0f, 0x83, 0x2c, 0x00, 0x00, 0x00, //0x0000033e jae          LBB0_7
	0x41, 0x8a, 0x04, 0x1c, //0x00000344 movb         (%r12,%rbx), %al
	0x3c, 0x0d, //0x00000348 cmpb         $13, %al
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x0000034a je           LBB0_7
	0x3c, 0x20, //0x00000350 cmpb         $32, %al
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x00000352 je           LBB0_7
	0x04, 0xf5, //0x00000358 addb         $-11, %al
	0x3c, 0xfe, //0x0000035a cmpb         $-2, %al
	0x0f, 0x83, 0x0e, 0x00, 0x00, 0x00, //0x0000035c jae          LBB0_7
	0x48, 0x89, 0xd8, //0x00000362 movq         %rbx, %rax
	0xe9, 0xb0, 0x01, 0x00, 0x00, //0x00000365 jmp          LBB0_32
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000036a .p2align 4, 0x90
	//0x00000370 LBB0_7
	0x48, 0x8d, 0x43, 0x01, //0x00000370 leaq         $1(%rbx), %rax
	0x48, 0x39, 0xd0, //0x00000374 cmpq         %rdx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000377 jae          LBB0_11
	0x41, 0x8a, 0x0c, 0x04, //0x0000037d movb         (%r12,%rax), %cl
	0x80, 0xf9, 0x0d, //0x00000381 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000384 je           LBB0_11
	0x80, 0xf9, 0x20, //0x0000038a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000038d je           LBB0_11
	0x80, 0xc1, 0xf5, //0x00000393 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000396 cmpb         $-2, %cl
	0x0f, 0x82, 0x7b, 0x01, 0x00, 0x00, //0x00000399 jb           LBB0_32
	0x90, //0x0000039f .p2align 4, 0x90
	//0x000003a0 LBB0_11
	0x48, 0x8d, 0x43, 0x02, //0x000003a0 leaq         $2(%rbx), %rax
	0x48, 0x39, 0xd0, //0x000003a4 cmpq         %rdx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000003a7 jae          LBB0_15
	0x41, 0x8a, 0x0c, 0x04, //0x000003ad movb         (%r12,%rax), %cl
	0x80, 0xf9, 0x0d, //0x000003b1 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000003b4 je           LBB0_15
	0x80, 0xf9, 0x20, //0x000003ba cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000003bd je           LBB0_15
	0x80, 0xc1, 0xf5, //0x000003c3 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x000003c6 cmpb         $-2, %cl
	0x0f, 0x82, 0x4b, 0x01, 0x00, 0x00, //0x000003c9 jb           LBB0_32
	0x90, //0x000003cf .p2align 4, 0x90
	//0x000003d0 LBB0_15
	0x48, 0x8d, 0x43, 0x03, //0x000003d0 leaq         $3(%rbx), %rax
	0x48, 0x39, 0xd0, //0x000003d4 cmpq         %rdx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000003d7 jae          LBB0_19
	0x41, 0x8a, 0x0c, 0x04, //0x000003dd movb         (%r12,%rax), %cl
	0x80, 0xf9, 0x0d, //0x000003e1 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000003e4 je           LBB0_19
	0x80, 0xf9, 0x20, //0x000003ea cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000003ed je           LBB0_19
	0x80, 0xc1, 0xf5, //0x000003f3 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x000003f6 cmpb         $-2, %cl
	0x0f, 0x82, 0x1b, 0x01, 0x00, 0x00, //0x000003f9 jb           LBB0_32
	0x90, //0x000003ff .p2align 4, 0x90
	//0x00000400 LBB0_19
	0x4c, 0x8d, 0x73, 0x04, //0x00000400 leaq         $4(%rbx), %r14
	0x48, 0x89, 0xd1, //0x00000404 movq         %rdx, %rcx
	0x4c, 0x29, 0xf1, //0x00000407 subq         %r14, %rcx
	0x0f, 0x86, 0xd0, 0x00, 0x00, 0x00, //0x0000040a jbe          LBB0_995
	0x48, 0x83, 0xf9, 0x20, //0x00000410 cmpq         $32, %rcx
	0x0f, 0x82, 0x95, 0x29, 0x00, 0x00, //0x00000414 jb           LBB0_597
	0x48, 0xc7, 0xc1, 0xfc, 0xff, 0xff, 0xff, //0x0000041a movq         $-4, %rcx
	0x48, 0x29, 0xd9, //0x00000421 subq         %rbx, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000424 .p2align 4, 0x90
	//0x00000430 LBB0_22
	0xc4, 0x01, 0x7e, 0x6f, 0x34, 0x34, //0x00000430 vmovdqu      (%r12,%r14), %ymm14
	0xc4, 0x42, 0x7d, 0x00, 0xfe, //0x00000436 vpshufb      %ymm14, %ymm0, %ymm15
	0xc4, 0xc1, 0x0d, 0xf8, 0xf7, //0x0000043b vpsubb       %ymm15, %ymm14, %ymm6
	0xc4, 0xe2, 0x7d, 0x17, 0xf6, //0x00000440 vptest       %ymm6, %ymm6
	0x0f, 0x85, 0xb5, 0x00, 0x00, 0x00, //0x00000445 jne          LBB0_31
	0x49, 0x83, 0xc6, 0x20, //0x0000044b addq         $32, %r14
	0x48, 0x8d, 0x04, 0x0a, //0x0000044f leaq         (%rdx,%rcx), %rax
	0x48, 0x83, 0xc0, 0xe0, //0x00000453 addq         $-32, %rax
	0x48, 0x83, 0xc1, 0xe0, //0x00000457 addq         $-32, %rcx
	0x48, 0x83, 0xf8, 0x1f, //0x0000045b cmpq         $31, %rax
	0x0f, 0x87, 0xcb, 0xff, 0xff, 0xff, //0x0000045f ja           LBB0_22
	0x4c, 0x89, 0xe0, //0x00000465 movq         %r12, %rax
	0x48, 0x29, 0xc8, //0x00000468 subq         %rcx, %rax
	0x48, 0x01, 0xd1, //0x0000046b addq         %rdx, %rcx
	0x48, 0x85, 0xc9, //0x0000046e testq        %rcx, %rcx
	0x0f, 0x84, 0x3f, 0x00, 0x00, 0x00, //0x00000471 je           LBB0_30
	//0x00000477 LBB0_25
	0x4d, 0x89, 0xc1, //0x00000477 movq         %r8, %r9
	0x4c, 0x8d, 0x04, 0x08, //0x0000047a leaq         (%rax,%rcx), %r8
	0x31, 0xf6, //0x0000047e xorl         %esi, %esi
	//0x00000480 LBB0_26
	0x44, 0x0f, 0xbe, 0x34, 0x30, //0x00000480 movsbl       (%rax,%rsi), %r14d
	0x41, 0x83, 0xfe, 0x20, //0x00000485 cmpl         $32, %r14d
	0x0f, 0x87, 0xf5, 0x28, 0x00, 0x00, //0x00000489 ja           LBB0_33
	0x48, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000048f movabsq      $4294977024, %rdi
	0x4c, 0x0f, 0xa3, 0xf7, //0x00000499 btq          %r14, %rdi
	0x0f, 0x83, 0xe1, 0x28, 0x00, 0x00, //0x0000049d jae          LBB0_33
	0x48, 0x83, 0xc6, 0x01, //0x000004a3 addq         $1, %rsi
	0x48, 0x39, 0xf1, //0x000004a7 cmpq         %rsi, %rcx
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x000004aa jne          LBB0_26
	0x4c, 0x89, 0xc0, //0x000004b0 movq         %r8, %rax
	0x4d, 0x89, 0xc8, //0x000004b3 movq         %r9, %r8
	//0x000004b6 LBB0_30
	0x4c, 0x29, 0xe0, //0x000004b6 subq         %r12, %rax
	0x48, 0x39, 0xd0, //0x000004b9 cmpq         %rdx, %rax
	0x0f, 0x82, 0x58, 0x00, 0x00, 0x00, //0x000004bc jb           LBB0_32
	//0x000004c2 LBB0_34
	0x31, 0xc0, //0x000004c2 xorl         %eax, %eax
	0x49, 0x89, 0xde, //0x000004c4 movq         %rbx, %r14
	0x48, 0x8b, 0x4c, 0x24, 0x30, //0x000004c7 movq         $48(%rsp), %rcx
	0x48, 0x8b, 0x09, //0x000004cc movq         (%rcx), %rcx
	0x48, 0x85, 0xc9, //0x000004cf testq        %rcx, %rcx
	0x0f, 0x85, 0x5f, 0x00, 0x00, 0x00, //0x000004d2 jne          LBB0_35
	0xe9, 0xd2, 0x44, 0x00, 0x00, //0x000004d8 jmp          LBB0_996
	0x90, 0x90, 0x90, //0x000004dd .p2align 4, 0x90
	//0x000004e0 LBB0_995
	0x4d, 0x89, 0x75, 0x00, //0x000004e0 movq         %r14, (%r13)
	0x31, 0xc0, //0x000004e4 xorl         %eax, %eax
	0x48, 0x8b, 0x4c, 0x24, 0x30, //0x000004e6 movq         $48(%rsp), %rcx
	0x48, 0x8b, 0x09, //0x000004eb movq         (%rcx), %rcx
	0x48, 0x85, 0xc9, //0x000004ee testq        %rcx, %rcx
	0x0f, 0x85, 0x40, 0x00, 0x00, 0x00, //0x000004f1 jne          LBB0_35
	0xe9, 0xb3, 0x44, 0x00, 0x00, //0x000004f7 jmp          LBB0_996
	0x90, 0x90, 0x90, 0x90, //0x000004fc .p2align 4, 0x90
	//0x00000500 LBB0_31
	0xc4, 0xc1, 0x0d, 0x74, 0xf7, //0x00000500 vpcmpeqb     %ymm15, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00000505 vpmovmskb    %ymm6, %eax
	0xf7, 0xd0, //0x00000509 notl         %eax
	0x0f, 0xbc, 0xc0, //0x0000050b bsfl         %eax, %eax
	0x48, 0x29, 0xc8, //0x0000050e subq         %rcx, %rax
	0x48, 0x39, 0xd0, //0x00000511 cmpq         %rdx, %rax
	0x0f, 0x83, 0xa8, 0xff, 0xff, 0xff, //0x00000514 jae          LBB0_34
	//0x0000051a LBB0_32
	0x4c, 0x8d, 0x70, 0x01, //0x0000051a leaq         $1(%rax), %r14
	0x4d, 0x89, 0x75, 0x00, //0x0000051e movq         %r14, (%r13)
	0x41, 0x8a, 0x04, 0x04, //0x00000522 movb         (%r12,%rax), %al
	0x48, 0x8b, 0x4c, 0x24, 0x30, //0x00000526 movq         $48(%rsp), %rcx
	0x48, 0x8b, 0x09, //0x0000052b movq         (%rcx), %rcx
	0x48, 0x85, 0xc9, //0x0000052e testq        %rcx, %rcx
	0x0f, 0x84, 0x78, 0x44, 0x00, 0x00, //0x00000531 je           LBB0_996
	//0x00000537 LBB0_35
	0x8a, 0x49, 0x17, //0x00000537 movb         $23(%rcx), %cl
	0x80, 0xe1, 0x1f, //0x0000053a andb         $31, %cl
	0x80, 0xf9, 0x02, //0x0000053d cmpb         $2, %cl
	0x0f, 0x84, 0x5a, 0x19, 0x00, 0x00, //0x00000540 je           LBB0_387
	0x80, 0xf9, 0x18, //0x00000546 cmpb         $24, %cl
	0x0f, 0x85, 0x60, 0x44, 0x00, 0x00, //0x00000549 jne          LBB0_996
	0x3c, 0x7b, //0x0000054f cmpb         $123, %al
	0x4c, 0x89, 0x44, 0x24, 0x28, //0x00000551 movq         %r8, $40(%rsp)
	0x0f, 0x85, 0x15, 0x46, 0x00, 0x00, //0x00000556 jne          LBB0_1023
	//0x0000055c LBB0_38
	0x49, 0x8b, 0x00, //0x0000055c movq         (%r8), %rax
	0x49, 0x39, 0xc6, //0x0000055f cmpq         %rax, %r14
	0x0f, 0x83, 0x38, 0x00, 0x00, 0x00, //0x00000562 jae          LBB0_43
	0x43, 0x8a, 0x0c, 0x34, //0x00000568 movb         (%r12,%r14), %cl
	0x80, 0xf9, 0x0d, //0x0000056c cmpb         $13, %cl
	0x0f, 0x84, 0x2b, 0x00, 0x00, 0x00, //0x0000056f je           LBB0_43
	0x80, 0xf9, 0x20, //0x00000575 cmpb         $32, %cl
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x00000578 je           LBB0_43
	0x80, 0xc1, 0xf5, //0x0000057e addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000581 cmpb         $-2, %cl
	0x0f, 0x83, 0x16, 0x00, 0x00, 0x00, //0x00000584 jae          LBB0_43
	0x4c, 0x89, 0xf3, //0x0000058a movq         %r14, %rbx
	0xe9, 0x88, 0x01, 0x00, 0x00, //0x0000058d jmp          LBB0_68
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000592 .p2align 4, 0x90
	//0x000005a0 LBB0_43
	0x49, 0x8d, 0x5e, 0x01, //0x000005a0 leaq         $1(%r14), %rbx
	0x48, 0x39, 0xc3, //0x000005a4 cmpq         %rax, %rbx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000005a7 jae          LBB0_47
	0x41, 0x8a, 0x0c, 0x1c, //0x000005ad movb         (%r12,%rbx), %cl
	0x80, 0xf9, 0x0d, //0x000005b1 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000005b4 je           LBB0_47
	0x80, 0xf9, 0x20, //0x000005ba cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000005bd je           LBB0_47
	0x80, 0xc1, 0xf5, //0x000005c3 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x000005c6 cmpb         $-2, %cl
	0x0f, 0x82, 0x4b, 0x01, 0x00, 0x00, //0x000005c9 jb           LBB0_68
	0x90, //0x000005cf .p2align 4, 0x90
	//0x000005d0 LBB0_47
	0x49, 0x8d, 0x5e, 0x02, //0x000005d0 leaq         $2(%r14), %rbx
	0x48, 0x39, 0xc3, //0x000005d4 cmpq         %rax, %rbx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000005d7 jae          LBB0_51
	0x41, 0x8a, 0x0c, 0x1c, //0x000005dd movb         (%r12,%rbx), %cl
	0x80, 0xf9, 0x0d, //0x000005e1 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000005e4 je           LBB0_51
	0x80, 0xf9, 0x20, //0x000005ea cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000005ed je           LBB0_51
	0x80, 0xc1, 0xf5, //0x000005f3 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x000005f6 cmpb         $-2, %cl
	0x0f, 0x82, 0x1b, 0x01, 0x00, 0x00, //0x000005f9 jb           LBB0_68
	0x90, //0x000005ff .p2align 4, 0x90
	//0x00000600 LBB0_51
	0x49, 0x8d, 0x5e, 0x03, //0x00000600 leaq         $3(%r14), %rbx
	0x48, 0x39, 0xc3, //0x00000604 cmpq         %rax, %rbx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000607 jae          LBB0_55
	0x41, 0x8a, 0x0c, 0x1c, //0x0000060d movb         (%r12,%rbx), %cl
	0x80, 0xf9, 0x0d, //0x00000611 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000614 je           LBB0_55
	0x80, 0xf9, 0x20, //0x0000061a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000061d je           LBB0_55
	0x80, 0xc1, 0xf5, //0x00000623 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000626 cmpb         $-2, %cl
	0x0f, 0x82, 0xeb, 0x00, 0x00, 0x00, //0x00000629 jb           LBB0_68
	0x90, //0x0000062f .p2align 4, 0x90
	//0x00000630 LBB0_55
	0x49, 0x8d, 0x5e, 0x04, //0x00000630 leaq         $4(%r14), %rbx
	0x48, 0x89, 0xc1, //0x00000634 movq         %rax, %rcx
	0x48, 0x29, 0xd9, //0x00000637 subq         %rbx, %rcx
	0x0f, 0x86, 0x2e, 0x45, 0x00, 0x00, //0x0000063a jbe          LBB0_1021
	0x48, 0x83, 0xf9, 0x20, //0x00000640 cmpq         $32, %rcx
	0x0f, 0x82, 0x9f, 0x08, 0x00, 0x00, //0x00000644 jb           LBB0_352
	0x48, 0xc7, 0xc1, 0xfc, 0xff, 0xff, 0xff, //0x0000064a movq         $-4, %rcx
	0x4c, 0x29, 0xf1, //0x00000651 subq         %r14, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000654 .p2align 4, 0x90
	//0x00000660 LBB0_58
	0xc4, 0x41, 0x7e, 0x6f, 0x34, 0x1c, //0x00000660 vmovdqu      (%r12,%rbx), %ymm14
	0xc4, 0x42, 0x7d, 0x00, 0xfe, //0x00000666 vpshufb      %ymm14, %ymm0, %ymm15
	0xc4, 0xc1, 0x0d, 0xf8, 0xf7, //0x0000066b vpsubb       %ymm15, %ymm14, %ymm6
	0xc4, 0xe2, 0x7d, 0x17, 0xf6, //0x00000670 vptest       %ymm6, %ymm6
	0x0f, 0x85, 0x85, 0x00, 0x00, 0x00, //0x00000675 jne          LBB0_67
	0x48, 0x83, 0xc3, 0x20, //0x0000067b addq         $32, %rbx
	0x48, 0x8d, 0x14, 0x08, //0x0000067f leaq         (%rax,%rcx), %rdx
	0x48, 0x83, 0xc2, 0xe0, //0x00000683 addq         $-32, %rdx
	0x48, 0x83, 0xc1, 0xe0, //0x00000687 addq         $-32, %rcx
	0x48, 0x83, 0xfa, 0x1f, //0x0000068b cmpq         $31, %rdx
	0x0f, 0x87, 0xcb, 0xff, 0xff, 0xff, //0x0000068f ja           LBB0_58
	0x4c, 0x89, 0xe3, //0x00000695 movq         %r12, %rbx
	0x48, 0x29, 0xcb, //0x00000698 subq         %rcx, %rbx
	0x48, 0x01, 0xc1, //0x0000069b addq         %rax, %rcx
	0x48, 0x85, 0xc9, //0x0000069e testq        %rcx, %rcx
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x000006a1 je           LBB0_66
	//0x000006a7 LBB0_61
	0x4c, 0x8d, 0x0c, 0x0b, //0x000006a7 leaq         (%rbx,%rcx), %r9
	0x31, 0xd2, //0x000006ab xorl         %edx, %edx
	0x90, 0x90, 0x90, //0x000006ad .p2align 4, 0x90
	//0x000006b0 LBB0_62
	0x0f, 0xbe, 0x3c, 0x13, //0x000006b0 movsbl       (%rbx,%rdx), %edi
	0x83, 0xff, 0x20, //0x000006b4 cmpl         $32, %edi
	0x0f, 0x87, 0x74, 0x04, 0x00, 0x00, //0x000006b7 ja           LBB0_302
	0x48, 0xbe, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000006bd movabsq      $4294977024, %rsi
	0x48, 0x0f, 0xa3, 0xfe, //0x000006c7 btq          %rdi, %rsi
	0x0f, 0x83, 0x60, 0x04, 0x00, 0x00, //0x000006cb jae          LBB0_302
	0x48, 0x83, 0xc2, 0x01, //0x000006d1 addq         $1, %rdx
	0x48, 0x39, 0xd1, //0x000006d5 cmpq         %rdx, %rcx
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x000006d8 jne          LBB0_62
	0x4c, 0x89, 0xcb, //0x000006de movq         %r9, %rbx
	//0x000006e1 LBB0_66
	0x4c, 0x29, 0xe3, //0x000006e1 subq         %r12, %rbx
	0x48, 0x39, 0xc3, //0x000006e4 cmpq         %rax, %rbx
	0x0f, 0x82, 0x2d, 0x00, 0x00, 0x00, //0x000006e7 jb           LBB0_68
	0xe9, 0x7f, 0x44, 0x00, 0x00, //0x000006ed jmp          LBB0_1023
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000006f2 .p2align 4, 0x90
	//0x00000700 LBB0_67
	0xc4, 0xc1, 0x0d, 0x74, 0xf7, //0x00000700 vpcmpeqb     %ymm15, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xd6, //0x00000705 vpmovmskb    %ymm6, %edx
	0xf7, 0xd2, //0x00000709 notl         %edx
	0x0f, 0xbc, 0xda, //0x0000070b bsfl         %edx, %ebx
	0x48, 0x29, 0xcb, //0x0000070e subq         %rcx, %rbx
	0x48, 0x39, 0xc3, //0x00000711 cmpq         %rax, %rbx
	0x0f, 0x83, 0x57, 0x44, 0x00, 0x00, //0x00000714 jae          LBB0_1023
	//0x0000071a LBB0_68
	0x4c, 0x8d, 0x73, 0x01, //0x0000071a leaq         $1(%rbx), %r14
	0x4d, 0x89, 0x75, 0x00, //0x0000071e movq         %r14, (%r13)
	0x41, 0x8a, 0x04, 0x1c, //0x00000722 movb         (%r12,%rbx), %al
	0x3c, 0x22, //0x00000726 cmpb         $34, %al
	0x0f, 0x85, 0x36, 0x27, 0x00, 0x00, //0x00000728 jne          LBB0_234
	0x4d, 0x8b, 0x00, //0x0000072e movq         (%r8), %r8
	0x4d, 0x89, 0xc5, //0x00000731 movq         %r8, %r13
	0x4d, 0x29, 0xf5, //0x00000734 subq         %r14, %r13
	0x0f, 0x84, 0xb0, 0x4b, 0x00, 0x00, //0x00000737 je           LBB0_1101
	0x48, 0x8b, 0x44, 0x24, 0x30, //0x0000073d movq         $48(%rsp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00000742 movq         $8(%rax), %rax
	0x4c, 0x8b, 0x10, //0x00000746 movq         (%rax), %r10
	0x48, 0x8b, 0x40, 0x08, //0x00000749 movq         $8(%rax), %rax
	0x48, 0x89, 0x44, 0x24, 0x38, //0x0000074d movq         %rax, $56(%rsp)
	0x4f, 0x8d, 0x1c, 0x34, //0x00000752 leaq         (%r12,%r14), %r11
	0x49, 0x83, 0xfd, 0x40, //0x00000756 cmpq         $64, %r13
	0x0f, 0x82, 0xe5, 0x03, 0x00, 0x00, //0x0000075a jb           LBB0_306
	0x49, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00000760 movq         $-2, %r9
	0x49, 0x29, 0xd9, //0x00000767 subq         %rbx, %r9
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000076a movq         $-1, %rax
	0x31, 0xd2, //0x00000771 xorl         %edx, %edx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000773 .p2align 4, 0x90
	//0x00000780 LBB0_72
	0xc4, 0x81, 0x7e, 0x6f, 0x34, 0x34, //0x00000780 vmovdqu      (%r12,%r14), %ymm6
	0xc4, 0x81, 0x7e, 0x6f, 0x7c, 0x34, 0x20, //0x00000786 vmovdqu      $32(%r12,%r14), %ymm7
	0xc5, 0x4d, 0x74, 0xf1, //0x0000078d vpcmpeqb     %ymm1, %ymm6, %ymm14
	0xc4, 0xc1, 0x7d, 0xd7, 0xce, //0x00000791 vpmovmskb    %ymm14, %ecx
	0xc5, 0x45, 0x74, 0xf1, //0x00000796 vpcmpeqb     %ymm1, %ymm7, %ymm14
	0xc4, 0xc1, 0x7d, 0xd7, 0xfe, //0x0000079a vpmovmskb    %ymm14, %edi
	0xc5, 0xcd, 0x74, 0xf2, //0x0000079f vpcmpeqb     %ymm2, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x000007a3 vpmovmskb    %ymm6, %esi
	0xc5, 0xc5, 0x74, 0xf2, //0x000007a7 vpcmpeqb     %ymm2, %ymm7, %ymm6
	0xc5, 0x7d, 0xd7, 0xfe, //0x000007ab vpmovmskb    %ymm6, %r15d
	0x48, 0xc1, 0xe7, 0x20, //0x000007af shlq         $32, %rdi
	0x48, 0x09, 0xf9, //0x000007b3 orq          %rdi, %rcx
	0x49, 0xc1, 0xe7, 0x20, //0x000007b6 shlq         $32, %r15
	0x4c, 0x09, 0xfe, //0x000007ba orq          %r15, %rsi
	0x0f, 0x85, 0x2f, 0x00, 0x00, 0x00, //0x000007bd jne          LBB0_81
	0x48, 0x85, 0xd2, //0x000007c3 testq        %rdx, %rdx
	0x0f, 0x85, 0x37, 0x00, 0x00, 0x00, //0x000007c6 jne          LBB0_83
	0x31, 0xd2, //0x000007cc xorl         %edx, %edx
	0x48, 0x85, 0xc9, //0x000007ce testq        %rcx, %rcx
	0x0f, 0x85, 0x99, 0x00, 0x00, 0x00, //0x000007d1 jne          LBB0_84
	//0x000007d7 LBB0_75
	0x49, 0x83, 0xc5, 0xc0, //0x000007d7 addq         $-64, %r13
	0x49, 0x83, 0xc1, 0xc0, //0x000007db addq         $-64, %r9
	0x49, 0x83, 0xc6, 0x40, //0x000007df addq         $64, %r14
	0x49, 0x83, 0xfd, 0x3f, //0x000007e3 cmpq         $63, %r13
	0x0f, 0x87, 0x93, 0xff, 0xff, 0xff, //0x000007e7 ja           LBB0_72
	0xe9, 0xee, 0x02, 0x00, 0x00, //0x000007ed jmp          LBB0_76
	//0x000007f2 LBB0_81
	0x48, 0x83, 0xf8, 0xff, //0x000007f2 cmpq         $-1, %rax
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x000007f6 jne          LBB0_83
	0x48, 0x0f, 0xbc, 0xc6, //0x000007fc bsfq         %rsi, %rax
	0x4c, 0x01, 0xf0, //0x00000800 addq         %r14, %rax
	//0x00000803 LBB0_83
	0x48, 0x89, 0x84, 0x24, 0x98, 0x00, 0x00, 0x00, //0x00000803 movq         %rax, $152(%rsp)
	0x48, 0x89, 0xd0, //0x0000080b movq         %rdx, %rax
	0x48, 0xf7, 0xd0, //0x0000080e notq         %rax
	0x48, 0x21, 0xf0, //0x00000811 andq         %rsi, %rax
	0x48, 0x8d, 0x3c, 0x00, //0x00000814 leaq         (%rax,%rax), %rdi
	0x48, 0x09, 0xd7, //0x00000818 orq          %rdx, %rdi
	0x49, 0x89, 0xff, //0x0000081b movq         %rdi, %r15
	0x49, 0xf7, 0xd7, //0x0000081e notq         %r15
	0x49, 0x21, 0xf7, //0x00000821 andq         %rsi, %r15
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000824 movabsq      $-6148914691236517206, %rdx
	0x49, 0x21, 0xd7, //0x0000082e andq         %rdx, %r15
	0x31, 0xd2, //0x00000831 xorl         %edx, %edx
	0x49, 0x01, 0xc7, //0x00000833 addq         %rax, %r15
	0x48, 0x8b, 0x84, 0x24, 0x98, 0x00, 0x00, 0x00, //0x00000836 movq         $152(%rsp), %rax
	0x0f, 0x92, 0xc2, //0x0000083e setb         %dl
	0x4d, 0x01, 0xff, //0x00000841 addq         %r15, %r15
	0x48, 0xbe, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000844 movabsq      $6148914691236517205, %rsi
	0x49, 0x31, 0xf7, //0x0000084e xorq         %rsi, %r15
	0x49, 0x21, 0xff, //0x00000851 andq         %rdi, %r15
	0x49, 0xf7, 0xd7, //0x00000854 notq         %r15
	0x4c, 0x21, 0xf9, //0x00000857 andq         %r15, %rcx
	0x48, 0x85, 0xc9, //0x0000085a testq        %rcx, %rcx
	0x0f, 0x84, 0x74, 0xff, 0xff, 0xff, //0x0000085d je           LBB0_75
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000863 .p2align 4, 0x90
	//0x00000870 LBB0_84
	0x4c, 0x0f, 0xbc, 0xf1, //0x00000870 bsfq         %rcx, %r14
	0x4d, 0x29, 0xce, //0x00000874 subq         %r9, %r14
	//0x00000877 LBB0_85
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00000877 movq         $16(%rsp), %r13
	0x4d, 0x85, 0xf6, //0x0000087c testq        %r14, %r14
	0x0f, 0x88, 0x70, 0x4a, 0x00, 0x00, //0x0000087f js           LBB0_1103
	0x4d, 0x89, 0x75, 0x00, //0x00000885 movq         %r14, (%r13)
	0x48, 0x83, 0xf8, 0xff, //0x00000889 cmpq         $-1, %rax
	0x0f, 0x84, 0x09, 0x00, 0x00, 0x00, //0x0000088d je           LBB0_88
	0x4c, 0x39, 0xf0, //0x00000893 cmpq         %r14, %rax
	0x0f, 0x8e, 0xc4, 0x02, 0x00, 0x00, //0x00000896 jle          LBB0_307
	//0x0000089c LBB0_88
	0x4c, 0x89, 0xf1, //0x0000089c movq         %r14, %rcx
	0x48, 0x29, 0xd9, //0x0000089f subq         %rbx, %rcx
	0x48, 0x83, 0xc1, 0xfe, //0x000008a2 addq         $-2, %rcx
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x000008a6 movl         $1, %r9d
	0x48, 0x89, 0xca, //0x000008ac movq         %rcx, %rdx
	0x48, 0x8b, 0x44, 0x24, 0x38, //0x000008af movq         $56(%rsp), %rax
	0x48, 0x09, 0xc2, //0x000008b4 orq          %rax, %rdx
	0x4c, 0x8b, 0x44, 0x24, 0x28, //0x000008b7 movq         $40(%rsp), %r8
	0x0f, 0x84, 0xde, 0x01, 0x00, 0x00, //0x000008bc je           LBB0_95
	0x48, 0x39, 0xc1, //0x000008c2 cmpq         %rax, %rcx
	0x0f, 0x85, 0x35, 0x00, 0x00, 0x00, //0x000008c5 jne          LBB0_94
	0x31, 0xd2, //0x000008cb xorl         %edx, %edx
	0x90, 0x90, 0x90, //0x000008cd .p2align 4, 0x90
	//0x000008d0 LBB0_91
	0x48, 0x83, 0xf8, 0x20, //0x000008d0 cmpq         $32, %rax
	0x0f, 0x82, 0x3a, 0x00, 0x00, 0x00, //0x000008d4 jb           LBB0_177
	0xc4, 0xc1, 0x7e, 0x6f, 0x34, 0x13, //0x000008da vmovdqu      (%r11,%rdx), %ymm6
	0xc4, 0xc1, 0x4d, 0xf8, 0x34, 0x12, //0x000008e0 vpsubb       (%r10,%rdx), %ymm6, %ymm6
	0x48, 0x83, 0xc0, 0xe0, //0x000008e6 addq         $-32, %rax
	0x48, 0x83, 0xc2, 0x20, //0x000008ea addq         $32, %rdx
	0xc4, 0xe2, 0x7d, 0x17, 0xf6, //0x000008ee vptest       %ymm6, %ymm6
	0x0f, 0x84, 0xd7, 0xff, 0xff, 0xff, //0x000008f3 je           LBB0_91
	0xe9, 0xab, 0x00, 0x00, 0x00, //0x000008f9 jmp          LBB0_93
	0x90, 0x90, //0x000008fe .p2align 4, 0x90
	//0x00000900 LBB0_94
	0x45, 0x31, 0xc9, //0x00000900 xorl         %r9d, %r9d
	0x49, 0x8b, 0x18, //0x00000903 movq         (%r8), %rbx
	0x49, 0x39, 0xde, //0x00000906 cmpq         %rbx, %r14
	0x0f, 0x83, 0x11, 0x06, 0x00, 0x00, //0x00000909 jae          LBB0_100
	0xe9, 0x98, 0x01, 0x00, 0x00, //0x0000090f jmp          LBB0_96
	//0x00000914 LBB0_177
	0x41, 0x8d, 0x0c, 0x1c, //0x00000914 leal         (%r12,%rbx), %ecx
	0x01, 0xd1, //0x00000918 addl         %edx, %ecx
	0x83, 0xc1, 0x01, //0x0000091a addl         $1, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x0000091d andl         $4095, %ecx
	0x81, 0xf9, 0xe0, 0x0f, 0x00, 0x00, //0x00000923 cmpl         $4064, %ecx
	0x0f, 0x87, 0x51, 0x00, 0x00, 0x00, //0x00000929 ja           LBB0_181
	0x41, 0x8d, 0x0c, 0x12, //0x0000092f leal         (%r10,%rdx), %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00000933 andl         $4095, %ecx
	0x81, 0xf9, 0xe1, 0x0f, 0x00, 0x00, //0x00000939 cmpl         $4065, %ecx
	0x0f, 0x83, 0x3b, 0x00, 0x00, 0x00, //0x0000093f jae          LBB0_181
	0xc4, 0x41, 0x7e, 0x6f, 0x34, 0x13, //0x00000945 vmovdqu      (%r11,%rdx), %ymm14
	0xc4, 0xc1, 0x7e, 0x6f, 0x34, 0x12, //0x0000094b vmovdqu      (%r10,%rdx), %ymm6
	0xc5, 0x8d, 0xf8, 0xfe, //0x00000951 vpsubb       %ymm6, %ymm14, %ymm7
	0xc4, 0xe2, 0x7d, 0x17, 0xff, //0x00000955 vptest       %ymm7, %ymm7
	0x0f, 0x84, 0xd8, 0x00, 0x00, 0x00, //0x0000095a je           LBB0_300
	0xc5, 0x8d, 0x74, 0xf6, //0x00000960 vpcmpeqb     %ymm6, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xce, //0x00000964 vpmovmskb    %ymm6, %ecx
	0xf7, 0xd1, //0x00000968 notl         %ecx
	0x0f, 0xbc, 0xc9, //0x0000096a bsfl         %ecx, %ecx
	0xe9, 0xab, 0x00, 0x00, 0x00, //0x0000096d jmp          LBB0_188
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000972 .p2align 4, 0x90
	//0x00000980 LBB0_181
	0x48, 0x83, 0xf8, 0x10, //0x00000980 cmpq         $16, %rax
	0x0f, 0x82, 0x36, 0x00, 0x00, 0x00, //0x00000984 jb           LBB0_184
	0xc4, 0xc1, 0x7a, 0x6f, 0x34, 0x13, //0x0000098a vmovdqu      (%r11,%rdx), %xmm6
	0xc4, 0xc1, 0x49, 0xf8, 0x34, 0x12, //0x00000990 vpsubb       (%r10,%rdx), %xmm6, %xmm6
	0x48, 0x83, 0xc0, 0xf0, //0x00000996 addq         $-16, %rax
	0x48, 0x83, 0xc2, 0x10, //0x0000099a addq         $16, %rdx
	0xc4, 0xe2, 0x79, 0x17, 0xf6, //0x0000099e vptest       %xmm6, %xmm6
	0x0f, 0x84, 0xd7, 0xff, 0xff, 0xff, //0x000009a3 je           LBB0_181
	//0x000009a9 LBB0_93
	0x31, 0xc0, //0x000009a9 xorl         %eax, %eax
	0x44, 0x0f, 0xb6, 0xc8, //0x000009ab movzbl       %al, %r9d
	0x49, 0x8b, 0x18, //0x000009af movq         (%r8), %rbx
	0x49, 0x39, 0xde, //0x000009b2 cmpq         %rbx, %r14
	0x0f, 0x83, 0x65, 0x05, 0x00, 0x00, //0x000009b5 jae          LBB0_100
	0xe9, 0xec, 0x00, 0x00, 0x00, //0x000009bb jmp          LBB0_96
	//0x000009c0 LBB0_184
	0x41, 0x8d, 0x0c, 0x1c, //0x000009c0 leal         (%r12,%rbx), %ecx
	0x01, 0xd1, //0x000009c4 addl         %edx, %ecx
	0x83, 0xc1, 0x01, //0x000009c6 addl         $1, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x000009c9 andl         $4095, %ecx
	0x81, 0xf9, 0xf0, 0x0f, 0x00, 0x00, //0x000009cf cmpl         $4080, %ecx
	0x0f, 0x87, 0x74, 0x00, 0x00, 0x00, //0x000009d5 ja           LBB0_291
	0x41, 0x8d, 0x0c, 0x12, //0x000009db leal         (%r10,%rdx), %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x000009df andl         $4095, %ecx
	0x81, 0xf9, 0xf1, 0x0f, 0x00, 0x00, //0x000009e5 cmpl         $4081, %ecx
	0x0f, 0x83, 0x5e, 0x00, 0x00, 0x00, //0x000009eb jae          LBB0_291
	0xc4, 0x41, 0x7a, 0x6f, 0x34, 0x13, //0x000009f1 vmovdqu      (%r11,%rdx), %xmm14
	0xc4, 0xc1, 0x7a, 0x6f, 0x3c, 0x12, //0x000009f7 vmovdqu      (%r10,%rdx), %xmm7
	0xc5, 0x89, 0xf8, 0xf7, //0x000009fd vpsubb       %xmm7, %xmm14, %xmm6
	0xc4, 0xe2, 0x79, 0x17, 0xf6, //0x00000a01 vptest       %xmm6, %xmm6
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x00000a06 je           LBB0_300
	0xc5, 0x89, 0x74, 0xf7, //0x00000a0c vpcmpeqb     %xmm7, %xmm14, %xmm6
	0xc5, 0xf9, 0xd7, 0xce, //0x00000a10 vpmovmskb    %xmm6, %ecx
	0xf7, 0xd1, //0x00000a14 notl         %ecx
	0x66, 0x0f, 0xbc, 0xc9, //0x00000a16 bsfw         %cx, %cx
	0x0f, 0xb7, 0xc9, //0x00000a1a movzwl       %cx, %ecx
	//0x00000a1d LBB0_188
	0x48, 0x39, 0xc8, //0x00000a1d cmpq         %rcx, %rax
	0x0f, 0x96, 0xc0, //0x00000a20 setbe        %al
	0x44, 0x0f, 0xb6, 0xc8, //0x00000a23 movzbl       %al, %r9d
	0x49, 0x8b, 0x18, //0x00000a27 movq         (%r8), %rbx
	0x49, 0x39, 0xde, //0x00000a2a cmpq         %rbx, %r14
	0x0f, 0x83, 0xed, 0x04, 0x00, 0x00, //0x00000a2d jae          LBB0_100
	0xe9, 0x74, 0x00, 0x00, 0x00, //0x00000a33 jmp          LBB0_96
	//0x00000a38 LBB0_300
	0xb0, 0x01, //0x00000a38 movb         $1, %al
	0x44, 0x0f, 0xb6, 0xc8, //0x00000a3a movzbl       %al, %r9d
	0x49, 0x8b, 0x18, //0x00000a3e movq         (%r8), %rbx
	0x49, 0x39, 0xde, //0x00000a41 cmpq         %rbx, %r14
	0x0f, 0x83, 0xd6, 0x04, 0x00, 0x00, //0x00000a44 jae          LBB0_100
	0xe9, 0x5d, 0x00, 0x00, 0x00, //0x00000a4a jmp          LBB0_96
	//0x00000a4f LBB0_291
	0xb0, 0x01, //0x00000a4f movb         $1, %al
	0x48, 0x39, 0x54, 0x24, 0x38, //0x00000a51 cmpq         %rdx, $56(%rsp)
	0x0f, 0x84, 0x38, 0x00, 0x00, 0x00, //0x00000a56 je           LBB0_295
	0x48, 0x83, 0x44, 0x24, 0x38, 0xff, //0x00000a5c addq         $-1, $56(%rsp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a62 .p2align 4, 0x90
	//0x00000a70 LBB0_293
	0x41, 0x0f, 0xb6, 0x04, 0x13, //0x00000a70 movzbl       (%r11,%rdx), %eax
	0x41, 0x3a, 0x04, 0x12, //0x00000a75 cmpb         (%r10,%rdx), %al
	0x0f, 0x94, 0xc0, //0x00000a79 sete         %al
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00000a7c jne          LBB0_295
	0x48, 0x8d, 0x4a, 0x01, //0x00000a82 leaq         $1(%rdx), %rcx
	0x48, 0x39, 0x54, 0x24, 0x38, //0x00000a86 cmpq         %rdx, $56(%rsp)
	0x48, 0x89, 0xca, //0x00000a8b movq         %rcx, %rdx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00000a8e jne          LBB0_293
	//0x00000a94 LBB0_295
	0x44, 0x0f, 0xb6, 0xc8, //0x00000a94 movzbl       %al, %r9d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000a98 .p2align 4, 0x90
	//0x00000aa0 LBB0_95
	0x49, 0x8b, 0x18, //0x00000aa0 movq         (%r8), %rbx
	0x49, 0x39, 0xde, //0x00000aa3 cmpq         %rbx, %r14
	0x0f, 0x83, 0x74, 0x04, 0x00, 0x00, //0x00000aa6 jae          LBB0_100
	//0x00000aac LBB0_96
	0x43, 0x8a, 0x0c, 0x34, //0x00000aac movb         (%r12,%r14), %cl
	0x80, 0xf9, 0x0d, //0x00000ab0 cmpb         $13, %cl
	0x0f, 0x84, 0x67, 0x04, 0x00, 0x00, //0x00000ab3 je           LBB0_100
	0x80, 0xf9, 0x20, //0x00000ab9 cmpb         $32, %cl
	0x0f, 0x84, 0x5e, 0x04, 0x00, 0x00, //0x00000abc je           LBB0_100
	0x80, 0xc1, 0xf5, //0x00000ac2 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000ac5 cmpb         $-2, %cl
	0x0f, 0x83, 0x52, 0x04, 0x00, 0x00, //0x00000ac8 jae          LBB0_100
	0x4c, 0x89, 0xf2, //0x00000ace movq         %r14, %rdx
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00000ad1 movq         $24(%rsp), %r10
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00000ad6 movq         $32(%rsp), %r11
	0xe9, 0xc9, 0x05, 0x00, 0x00, //0x00000adb jmp          LBB0_125
	//0x00000ae0 LBB0_76
	0x4d, 0x01, 0xe6, //0x00000ae0 addq         %r12, %r14
	0x49, 0x83, 0xfd, 0x20, //0x00000ae3 cmpq         $32, %r13
	0x0f, 0x82, 0x77, 0x12, 0x00, 0x00, //0x00000ae7 jb           LBB0_367
	//0x00000aed LBB0_77
	0xc4, 0xc1, 0x7e, 0x6f, 0x36, //0x00000aed vmovdqu      (%r14), %ymm6
	0xc5, 0xcd, 0x74, 0xf9, //0x00000af2 vpcmpeqb     %ymm1, %ymm6, %ymm7
	0xc5, 0xfd, 0xd7, 0xcf, //0x00000af6 vpmovmskb    %ymm7, %ecx
	0xc5, 0xcd, 0x74, 0xf2, //0x00000afa vpcmpeqb     %ymm2, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x00000afe vpmovmskb    %ymm6, %esi
	0x85, 0xf6, //0x00000b02 testl        %esi, %esi
	0x0f, 0x85, 0xe8, 0x11, 0x00, 0x00, //0x00000b04 jne          LBB0_362
	0x48, 0x85, 0xd2, //0x00000b0a testq        %rdx, %rdx
	0x0f, 0x85, 0xfe, 0x11, 0x00, 0x00, //0x00000b0d jne          LBB0_364
	0x31, 0xd2, //0x00000b13 xorl         %edx, %edx
	0x48, 0x85, 0xc9, //0x00000b15 testq        %rcx, %rcx
	0x0f, 0x84, 0x3e, 0x12, 0x00, 0x00, //0x00000b18 je           LBB0_366
	//0x00000b1e LBB0_80
	0x48, 0x0f, 0xbc, 0xc9, //0x00000b1e bsfq         %rcx, %rcx
	0x4d, 0x29, 0xe6, //0x00000b22 subq         %r12, %r14
	0x49, 0x01, 0xce, //0x00000b25 addq         %rcx, %r14
	0x49, 0x83, 0xc6, 0x01, //0x00000b28 addq         $1, %r14
	0xe9, 0x46, 0xfd, 0xff, 0xff, //0x00000b2c jmp          LBB0_85
	//0x00000b31 LBB0_302
	0x4c, 0x29, 0xe3, //0x00000b31 subq         %r12, %rbx
	0x48, 0x01, 0xd3, //0x00000b34 addq         %rdx, %rbx
	0x48, 0x39, 0xc3, //0x00000b37 cmpq         %rax, %rbx
	0x0f, 0x82, 0xda, 0xfb, 0xff, 0xff, //0x00000b3a jb           LBB0_68
	0xe9, 0x2c, 0x40, 0x00, 0x00, //0x00000b40 jmp          LBB0_1023
	//0x00000b45 LBB0_306
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b45 movq         $-1, %rax
	0x31, 0xd2, //0x00000b4c xorl         %edx, %edx
	0x4d, 0x89, 0xde, //0x00000b4e movq         %r11, %r14
	0x49, 0x83, 0xfd, 0x20, //0x00000b51 cmpq         $32, %r13
	0x0f, 0x83, 0x92, 0xff, 0xff, 0xff, //0x00000b55 jae          LBB0_77
	0xe9, 0x04, 0x12, 0x00, 0x00, //0x00000b5b jmp          LBB0_367
	//0x00000b60 LBB0_307
	0x48, 0xc7, 0x44, 0x24, 0x40, 0x00, 0x00, 0x00, 0x00, //0x00000b60 movq         $0, $64(%rsp)
	0x4f, 0x8d, 0x3c, 0x34, //0x00000b69 leaq         (%r12,%r14), %r15
	0x49, 0x83, 0xc7, 0xff, //0x00000b6d addq         $-1, %r15
	0x48, 0x8b, 0x44, 0x24, 0x38, //0x00000b71 movq         $56(%rsp), %rax
	0x4d, 0x8d, 0x2c, 0x02, //0x00000b76 leaq         (%r10,%rax), %r13
	0x4d, 0x39, 0xfb, //0x00000b7a cmpq         %r15, %r11
	0x4c, 0x8b, 0x44, 0x24, 0x28, //0x00000b7d movq         $40(%rsp), %r8
	0x0f, 0x83, 0x72, 0x03, 0x00, 0x00, //0x00000b82 jae          LBB0_338
	0x48, 0x85, 0xc0, //0x00000b88 testq        %rax, %rax
	0x0f, 0x8e, 0x69, 0x03, 0x00, 0x00, //0x00000b8b jle          LBB0_338
	//0x00000b91 LBB0_309
	0x41, 0x8a, 0x03, //0x00000b91 movb         (%r11), %al
	0x3c, 0x5c, //0x00000b94 cmpb         $92, %al
	0x0f, 0x85, 0x47, 0x00, 0x00, 0x00, //0x00000b96 jne          LBB0_314
	0x4c, 0x89, 0xf9, //0x00000b9c movq         %r15, %rcx
	0x4c, 0x29, 0xd9, //0x00000b9f subq         %r11, %rcx
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00000ba2 movq         $-1, %rbx
	0x48, 0x85, 0xc9, //0x00000ba9 testq        %rcx, %rcx
	0x0f, 0x8e, 0x04, 0x49, 0x00, 0x00, //0x00000bac jle          LBB0_1128
	0x41, 0x0f, 0xb6, 0x43, 0x01, //0x00000bb2 movzbl       $1(%r11), %eax
	0x48, 0x8d, 0x15, 0x42, 0x52, 0x00, 0x00, //0x00000bb7 leaq         $21058(%rip), %rdx  /* __UnquoteTab+0(%rip) */
	0x8a, 0x04, 0x10, //0x00000bbe movb         (%rax,%rdx), %al
	0x3c, 0xff, //0x00000bc1 cmpb         $-1, %al
	0x0f, 0x84, 0x30, 0x00, 0x00, 0x00, //0x00000bc3 je           LBB0_316
	0x84, 0xc0, //0x00000bc9 testb        %al, %al
	0x0f, 0x84, 0xd1, 0x48, 0x00, 0x00, //0x00000bcb je           LBB0_1126
	0x88, 0x44, 0x24, 0x40, //0x00000bd1 movb         %al, $64(%rsp)
	0x49, 0x83, 0xc3, 0x02, //0x00000bd5 addq         $2, %r11
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000bd9 movl         $1, %ecx
	0xe9, 0x1d, 0x01, 0x00, 0x00, //0x00000bde jmp          LBB0_327
	//0x00000be3 LBB0_314
	0x41, 0x3a, 0x02, //0x00000be3 cmpb         (%r10), %al
	0x0f, 0x85, 0xe5, 0x10, 0x00, 0x00, //0x00000be6 jne          LBB0_360
	0x49, 0x83, 0xc3, 0x01, //0x00000bec addq         $1, %r11
	0x49, 0x83, 0xc2, 0x01, //0x00000bf0 addq         $1, %r10
	0xe9, 0x79, 0x01, 0x00, 0x00, //0x00000bf4 jmp          LBB0_336
	//0x00000bf9 LBB0_316
	0x48, 0x83, 0xf9, 0x04, //0x00000bf9 cmpq         $4, %rcx
	0x0f, 0x82, 0xaf, 0x48, 0x00, 0x00, //0x00000bfd jb           LBB0_1127
	0x4d, 0x8d, 0x43, 0x02, //0x00000c03 leaq         $2(%r11), %r8
	0x41, 0x8b, 0x53, 0x02, //0x00000c07 movl         $2(%r11), %edx
	0x89, 0xd0, //0x00000c0b movl         %edx, %eax
	0xf7, 0xd0, //0x00000c0d notl         %eax
	0x8d, 0xb2, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000c0f leal         $-808464432(%rdx), %esi
	0x25, 0x80, 0x80, 0x80, 0x80, //0x00000c15 andl         $-2139062144, %eax
	0x48, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00000c1a movq         $-2, %rbx
	0x85, 0xf0, //0x00000c21 testl        %esi, %eax
	0x0f, 0x85, 0x4a, 0x48, 0x00, 0x00, //0x00000c23 jne          LBB0_1138
	0x8d, 0xb2, 0x19, 0x19, 0x19, 0x19, //0x00000c29 leal         $421075225(%rdx), %esi
	0x09, 0xd6, //0x00000c2f orl          %edx, %esi
	0xf7, 0xc6, 0x80, 0x80, 0x80, 0x80, //0x00000c31 testl        $-2139062144, %esi
	0x0f, 0x85, 0x36, 0x48, 0x00, 0x00, //0x00000c37 jne          LBB0_1138
	0x89, 0xd6, //0x00000c3d movl         %edx, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000c3f andl         $2139062143, %esi
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000c45 movl         $-1061109568, %edi
	0x29, 0xf7, //0x00000c4a subl         %esi, %edi
	0x44, 0x8d, 0x8e, 0x46, 0x46, 0x46, 0x46, //0x00000c4c leal         $1179010630(%rsi), %r9d
	0x21, 0xc7, //0x00000c53 andl         %eax, %edi
	0x44, 0x85, 0xcf, //0x00000c55 testl        %r9d, %edi
	0x0f, 0x85, 0x15, 0x48, 0x00, 0x00, //0x00000c58 jne          LBB0_1138
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000c5e movl         $-522133280, %edi
	0x29, 0xf7, //0x00000c63 subl         %esi, %edi
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00000c65 addl         $960051513, %esi
	0x21, 0xf8, //0x00000c6b andl         %edi, %eax
	0x85, 0xf0, //0x00000c6d testl        %esi, %eax
	0x0f, 0x85, 0x44, 0x48, 0x00, 0x00, //0x00000c6f jne          LBB0_1129
	0x0f, 0xca, //0x00000c75 bswapl       %edx
	0x89, 0xd0, //0x00000c77 movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x00000c79 shrl         $4, %eax
	0xf7, 0xd0, //0x00000c7c notl         %eax
	0x25, 0x01, 0x01, 0x01, 0x01, //0x00000c7e andl         $16843009, %eax
	0x8d, 0x04, 0xc0, //0x00000c83 leal         (%rax,%rax,8), %eax
	0x81, 0xe2, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000c86 andl         $252645135, %edx
	0x01, 0xc2, //0x00000c8c addl         %eax, %edx
	0x89, 0xd0, //0x00000c8e movl         %edx, %eax
	0xc1, 0xe8, 0x04, //0x00000c90 shrl         $4, %eax
	0x09, 0xd0, //0x00000c93 orl          %edx, %eax
	0x89, 0xc6, //0x00000c95 movl         %eax, %esi
	0xc1, 0xee, 0x08, //0x00000c97 shrl         $8, %esi
	0x81, 0xe6, 0x00, 0xff, 0x00, 0x00, //0x00000c9a andl         $65280, %esi
	0x0f, 0xb6, 0xd0, //0x00000ca0 movzbl       %al, %edx
	0x09, 0xf2, //0x00000ca3 orl          %esi, %edx
	0x4d, 0x8d, 0x43, 0x06, //0x00000ca5 leaq         $6(%r11), %r8
	0x83, 0xfa, 0x7f, //0x00000ca9 cmpl         $127, %edx
	0x0f, 0x86, 0xd7, 0x00, 0x00, 0x00, //0x00000cac jbe          LBB0_340
	0x81, 0xfa, 0xff, 0x07, 0x00, 0x00, //0x00000cb2 cmpl         $2047, %edx
	0x0f, 0x86, 0xd9, 0x00, 0x00, 0x00, //0x00000cb8 jbe          LBB0_341
	0x8d, 0xba, 0x00, 0x20, 0xff, 0xff, //0x00000cbe leal         $-57344(%rdx), %edi
	0x81, 0xff, 0xff, 0xf7, 0xff, 0xff, //0x00000cc4 cmpl         $-2049, %edi
	0x0f, 0x87, 0xe5, 0x00, 0x00, 0x00, //0x00000cca ja           LBB0_342
	0xc1, 0xee, 0x0c, //0x00000cd0 shrl         $12, %esi
	0x40, 0x80, 0xce, 0xe0, //0x00000cd3 orb          $-32, %sil
	0x40, 0x88, 0x74, 0x24, 0x40, //0x00000cd7 movb         %sil, $64(%rsp)
	0xc1, 0xea, 0x06, //0x00000cdc shrl         $6, %edx
	0x80, 0xe2, 0x3f, //0x00000cdf andb         $63, %dl
	0x80, 0xca, 0x80, //0x00000ce2 orb          $-128, %dl
	0x88, 0x54, 0x24, 0x41, //0x00000ce5 movb         %dl, $65(%rsp)
	0x24, 0x3f, //0x00000ce9 andb         $63, %al
	0x0c, 0x80, //0x00000ceb orb          $-128, %al
	0x88, 0x44, 0x24, 0x42, //0x00000ced movb         %al, $66(%rsp)
	0xb9, 0x03, 0x00, 0x00, 0x00, //0x00000cf1 movl         $3, %ecx
	0x89, 0xf0, //0x00000cf6 movl         %esi, %eax
	//0x00000cf8 LBB0_325
	0x4d, 0x89, 0xc3, //0x00000cf8 movq         %r8, %r11
	//0x00000cfb LBB0_326
	0x4c, 0x8b, 0x44, 0x24, 0x28, //0x00000cfb movq         $40(%rsp), %r8
	//0x00000d00 LBB0_327
	0x48, 0x01, 0xe1, //0x00000d00 addq         %rsp, %rcx
	0x48, 0x83, 0xc1, 0x40, //0x00000d03 addq         $64, %rcx
	0x4d, 0x39, 0xea, //0x00000d07 cmpq         %r13, %r10
	0x0f, 0x83, 0x54, 0x00, 0x00, 0x00, //0x00000d0a jae          LBB0_334
	0x48, 0x8d, 0x54, 0x24, 0x40, //0x00000d10 leaq         $64(%rsp), %rdx
	0x48, 0x39, 0xd1, //0x00000d15 cmpq         %rdx, %rcx
	0x0f, 0x86, 0x46, 0x00, 0x00, 0x00, //0x00000d18 jbe          LBB0_334
	0x41, 0x38, 0x02, //0x00000d1e cmpb         %al, (%r10)
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000d21 jne          LBB0_334
	0x49, 0x83, 0xc2, 0x01, //0x00000d27 addq         $1, %r10
	0x48, 0x8d, 0x54, 0x24, 0x41, //0x00000d2b leaq         $65(%rsp), %rdx
	0x4c, 0x89, 0xd6, //0x00000d30 movq         %r10, %rsi
	//0x00000d33 LBB0_331
	0x49, 0x89, 0xf2, //0x00000d33 movq         %rsi, %r10
	0x48, 0x89, 0xd0, //0x00000d36 movq         %rdx, %rax
	0x4c, 0x39, 0xee, //0x00000d39 cmpq         %r13, %rsi
	0x0f, 0x83, 0x27, 0x00, 0x00, 0x00, //0x00000d3c jae          LBB0_335
	0x48, 0x39, 0xc8, //0x00000d42 cmpq         %rcx, %rax
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x00000d45 jae          LBB0_335
	0x41, 0x0f, 0xb6, 0x1a, //0x00000d4b movzbl       (%r10), %ebx
	0x49, 0x8d, 0x72, 0x01, //0x00000d4f leaq         $1(%r10), %rsi
	0x48, 0x8d, 0x50, 0x01, //0x00000d53 leaq         $1(%rax), %rdx
	0x3a, 0x18, //0x00000d57 cmpb         (%rax), %bl
	0x0f, 0x84, 0xd4, 0xff, 0xff, 0xff, //0x00000d59 je           LBB0_331
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00000d5f jmp          LBB0_335
	//0x00000d64 LBB0_334
	0x48, 0x8d, 0x44, 0x24, 0x40, //0x00000d64 leaq         $64(%rsp), %rax
	//0x00000d69 LBB0_335
	0x48, 0x39, 0xc8, //0x00000d69 cmpq         %rcx, %rax
	0x0f, 0x85, 0x5f, 0x0f, 0x00, 0x00, //0x00000d6c jne          LBB0_360
	//0x00000d72 LBB0_336
	0x4d, 0x39, 0xfb, //0x00000d72 cmpq         %r15, %r11
	0x0f, 0x83, 0x7f, 0x01, 0x00, 0x00, //0x00000d75 jae          LBB0_338
	0x4d, 0x39, 0xea, //0x00000d7b cmpq         %r13, %r10
	0x0f, 0x82, 0x0d, 0xfe, 0xff, 0xff, //0x00000d7e jb           LBB0_309
	0xe9, 0x71, 0x01, 0x00, 0x00, //0x00000d84 jmp          LBB0_338
	//0x00000d89 LBB0_340
	0x88, 0x44, 0x24, 0x40, //0x00000d89 movb         %al, $64(%rsp)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000d8d movl         $1, %ecx
	0xe9, 0x61, 0xff, 0xff, 0xff, //0x00000d92 jmp          LBB0_325
	//0x00000d97 LBB0_341
	0xc1, 0xea, 0x06, //0x00000d97 shrl         $6, %edx
	0x80, 0xca, 0xc0, //0x00000d9a orb          $-64, %dl
	0x88, 0x54, 0x24, 0x40, //0x00000d9d movb         %dl, $64(%rsp)
	0x24, 0x3f, //0x00000da1 andb         $63, %al
	0x0c, 0x80, //0x00000da3 orb          $-128, %al
	0x88, 0x44, 0x24, 0x41, //0x00000da5 movb         %al, $65(%rsp)
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00000da9 movl         $2, %ecx
	0x89, 0xd0, //0x00000dae movl         %edx, %eax
	0xe9, 0x43, 0xff, 0xff, 0xff, //0x00000db0 jmp          LBB0_325
	//0x00000db5 LBB0_342
	0x48, 0xc7, 0xc3, 0xfc, 0xff, 0xff, 0xff, //0x00000db5 movq         $-4, %rbx
	0x48, 0x83, 0xf9, 0x06, //0x00000dbc cmpq         $6, %rcx
	0x0f, 0x82, 0xf3, 0x46, 0x00, 0x00, //0x00000dc0 jb           LBB0_1129
	0x81, 0xfa, 0xff, 0xdb, 0x00, 0x00, //0x00000dc6 cmpl         $56319, %edx
	0x0f, 0x87, 0xe7, 0x46, 0x00, 0x00, //0x00000dcc ja           LBB0_1129
	0x41, 0x80, 0x38, 0x5c, //0x00000dd2 cmpb         $92, (%r8)
	0x0f, 0x85, 0xdd, 0x46, 0x00, 0x00, //0x00000dd6 jne          LBB0_1129
	0x41, 0x80, 0x7b, 0x07, 0x75, //0x00000ddc cmpb         $117, $7(%r11)
	0x0f, 0x85, 0xd2, 0x46, 0x00, 0x00, //0x00000de1 jne          LBB0_1129
	0x4d, 0x8d, 0x43, 0x08, //0x00000de7 leaq         $8(%r11), %r8
	0x41, 0x8b, 0x43, 0x08, //0x00000deb movl         $8(%r11), %eax
	0x89, 0xc1, //0x00000def movl         %eax, %ecx
	0xf7, 0xd1, //0x00000df1 notl         %ecx
	0x8d, 0xb0, 0xd0, 0xcf, 0xcf, 0xcf, //0x00000df3 leal         $-808464432(%rax), %esi
	0x81, 0xe1, 0x80, 0x80, 0x80, 0x80, //0x00000df9 andl         $-2139062144, %ecx
	0x85, 0xf1, //0x00000dff testl        %esi, %ecx
	0x0f, 0x85, 0x6c, 0x46, 0x00, 0x00, //0x00000e01 jne          LBB0_1138
	0x8d, 0xb0, 0x19, 0x19, 0x19, 0x19, //0x00000e07 leal         $421075225(%rax), %esi
	0x09, 0xc6, //0x00000e0d orl          %eax, %esi
	0xf7, 0xc6, 0x80, 0x80, 0x80, 0x80, //0x00000e0f testl        $-2139062144, %esi
	0x0f, 0x85, 0x58, 0x46, 0x00, 0x00, //0x00000e15 jne          LBB0_1138
	0x89, 0xc6, //0x00000e1b movl         %eax, %esi
	0x81, 0xe6, 0x7f, 0x7f, 0x7f, 0x7f, //0x00000e1d andl         $2139062143, %esi
	0xbf, 0xc0, 0xc0, 0xc0, 0xc0, //0x00000e23 movl         $-1061109568, %edi
	0x29, 0xf7, //0x00000e28 subl         %esi, %edi
	0x44, 0x8d, 0x8e, 0x46, 0x46, 0x46, 0x46, //0x00000e2a leal         $1179010630(%rsi), %r9d
	0x21, 0xcf, //0x00000e31 andl         %ecx, %edi
	0x44, 0x85, 0xcf, //0x00000e33 testl        %r9d, %edi
	0x0f, 0x85, 0x37, 0x46, 0x00, 0x00, //0x00000e36 jne          LBB0_1138
	0xbf, 0xe0, 0xe0, 0xe0, 0xe0, //0x00000e3c movl         $-522133280, %edi
	0x29, 0xf7, //0x00000e41 subl         %esi, %edi
	0x81, 0xc6, 0x39, 0x39, 0x39, 0x39, //0x00000e43 addl         $960051513, %esi
	0x21, 0xf9, //0x00000e49 andl         %edi, %ecx
	0x85, 0xf1, //0x00000e4b testl        %esi, %ecx
	0x0f, 0x85, 0x20, 0x46, 0x00, 0x00, //0x00000e4d jne          LBB0_1138
	0x0f, 0xc8, //0x00000e53 bswapl       %eax
	0x89, 0xc1, //0x00000e55 movl         %eax, %ecx
	0xc1, 0xe9, 0x04, //0x00000e57 shrl         $4, %ecx
	0xf7, 0xd1, //0x00000e5a notl         %ecx
	0x81, 0xe1, 0x01, 0x01, 0x01, 0x01, //0x00000e5c andl         $16843009, %ecx
	0x8d, 0x0c, 0xc9, //0x00000e62 leal         (%rcx,%rcx,8), %ecx
	0x25, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000e65 andl         $252645135, %eax
	0x01, 0xc8, //0x00000e6a addl         %ecx, %eax
	0x89, 0xc6, //0x00000e6c movl         %eax, %esi
	0xc1, 0xee, 0x04, //0x00000e6e shrl         $4, %esi
	0x09, 0xc6, //0x00000e71 orl          %eax, %esi
	0x40, 0x0f, 0xb6, 0xce, //0x00000e73 movzbl       %sil, %ecx
	0x89, 0xf0, //0x00000e77 movl         %esi, %eax
	0xc1, 0xe8, 0x08, //0x00000e79 shrl         $8, %eax
	0x25, 0x00, 0xff, 0x00, 0x00, //0x00000e7c andl         $65280, %eax
	0x8d, 0x34, 0x08, //0x00000e81 leal         (%rax,%rcx), %esi
	0x81, 0xc6, 0x00, 0x20, 0xff, 0xff, //0x00000e84 addl         $-57344, %esi
	0x81, 0xfe, 0x00, 0xfc, 0xff, 0xff, //0x00000e8a cmpl         $-1024, %esi
	0x0f, 0x82, 0x23, 0x46, 0x00, 0x00, //0x00000e90 jb           LBB0_1129
	0x09, 0xc8, //0x00000e96 orl          %ecx, %eax
	0xc1, 0xe2, 0x0a, //0x00000e98 shll         $10, %edx
	0x89, 0xc1, //0x00000e9b movl         %eax, %ecx
	0x01, 0xd1, //0x00000e9d addl         %edx, %ecx
	0x01, 0xc2, //0x00000e9f addl         %eax, %edx
	0x81, 0xc2, 0x00, 0x24, 0xa0, 0xfc, //0x00000ea1 addl         $-56613888, %edx
	0x89, 0xd0, //0x00000ea7 movl         %edx, %eax
	0xc1, 0xe8, 0x12, //0x00000ea9 shrl         $18, %eax
	0x0c, 0xf0, //0x00000eac orb          $-16, %al
	0x88, 0x44, 0x24, 0x40, //0x00000eae movb         %al, $64(%rsp)
	0x89, 0xd6, //0x00000eb2 movl         %edx, %esi
	0xc1, 0xee, 0x0c, //0x00000eb4 shrl         $12, %esi
	0x40, 0x80, 0xe6, 0x3f, //0x00000eb7 andb         $63, %sil
	0x40, 0x80, 0xce, 0x80, //0x00000ebb orb          $-128, %sil
	0x40, 0x88, 0x74, 0x24, 0x41, //0x00000ebf movb         %sil, $65(%rsp)
	0xc1, 0xea, 0x06, //0x00000ec4 shrl         $6, %edx
	0x80, 0xe2, 0x3f, //0x00000ec7 andb         $63, %dl
	0x80, 0xca, 0x80, //0x00000eca orb          $-128, %dl
	0x88, 0x54, 0x24, 0x42, //0x00000ecd movb         %dl, $66(%rsp)
	0x80, 0xe1, 0x3f, //0x00000ed1 andb         $63, %cl
	0x80, 0xc9, 0x80, //0x00000ed4 orb          $-128, %cl
	0x88, 0x4c, 0x24, 0x43, //0x00000ed7 movb         %cl, $67(%rsp)
	0x49, 0x83, 0xc3, 0x0c, //0x00000edb addq         $12, %r11
	0xb9, 0x04, 0x00, 0x00, 0x00, //0x00000edf movl         $4, %ecx
	0xe9, 0x12, 0xfe, 0xff, 0xff, //0x00000ee4 jmp          LBB0_326
	//0x00000ee9 LBB0_352
	0x4c, 0x01, 0xe3, //0x00000ee9 addq         %r12, %rbx
	0x48, 0x85, 0xc9, //0x00000eec testq        %rcx, %rcx
	0x0f, 0x85, 0xb2, 0xf7, 0xff, 0xff, //0x00000eef jne          LBB0_61
	0xe9, 0xe7, 0xf7, 0xff, 0xff, //0x00000ef5 jmp          LBB0_66
	//0x00000efa LBB0_338
	0x4d, 0x31, 0xfb, //0x00000efa xorq         %r15, %r11
	0x4d, 0x31, 0xea, //0x00000efd xorq         %r13, %r10
	0x45, 0x31, 0xc9, //0x00000f00 xorl         %r9d, %r9d
	0x4d, 0x09, 0xda, //0x00000f03 orq          %r11, %r10
	0x41, 0x0f, 0x94, 0xc1, //0x00000f06 sete         %r9b
	//0x00000f0a LBB0_339
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00000f0a movq         $16(%rsp), %r13
	0x49, 0x8b, 0x18, //0x00000f0f movq         (%r8), %rbx
	0x49, 0x39, 0xde, //0x00000f12 cmpq         %rbx, %r14
	0x0f, 0x82, 0x91, 0xfb, 0xff, 0xff, //0x00000f15 jb           LBB0_96
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00000f1b .p2align 4, 0x90
	//0x00000f20 LBB0_100
	0x49, 0x8d, 0x56, 0x01, //0x00000f20 leaq         $1(%r14), %rdx
	0x48, 0x39, 0xda, //0x00000f24 cmpq         %rbx, %rdx
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00000f27 movq         $24(%rsp), %r10
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00000f2c movq         $32(%rsp), %r11
	0x0f, 0x83, 0x29, 0x00, 0x00, 0x00, //0x00000f31 jae          LBB0_104
	0x41, 0x8a, 0x0c, 0x14, //0x00000f37 movb         (%r12,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00000f3b cmpb         $13, %cl
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00000f3e je           LBB0_104
	0x80, 0xf9, 0x20, //0x00000f44 cmpb         $32, %cl
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000f47 je           LBB0_104
	0x80, 0xc1, 0xf5, //0x00000f4d addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000f50 cmpb         $-2, %cl
	0x0f, 0x82, 0x50, 0x01, 0x00, 0x00, //0x00000f53 jb           LBB0_125
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000f59 .p2align 4, 0x90
	//0x00000f60 LBB0_104
	0x49, 0x8d, 0x56, 0x02, //0x00000f60 leaq         $2(%r14), %rdx
	0x48, 0x39, 0xda, //0x00000f64 cmpq         %rbx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000f67 jae          LBB0_108
	0x41, 0x8a, 0x0c, 0x14, //0x00000f6d movb         (%r12,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00000f71 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000f74 je           LBB0_108
	0x80, 0xf9, 0x20, //0x00000f7a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00000f7d je           LBB0_108
	0x80, 0xc1, 0xf5, //0x00000f83 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000f86 cmpb         $-2, %cl
	0x0f, 0x82, 0x1a, 0x01, 0x00, 0x00, //0x00000f89 jb           LBB0_125
	0x90, //0x00000f8f .p2align 4, 0x90
	//0x00000f90 LBB0_108
	0x49, 0x8d, 0x56, 0x03, //0x00000f90 leaq         $3(%r14), %rdx
	0x48, 0x39, 0xda, //0x00000f94 cmpq         %rbx, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000f97 jae          LBB0_112
	0x41, 0x8a, 0x0c, 0x14, //0x00000f9d movb         (%r12,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00000fa1 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000fa4 je           LBB0_112
	0x80, 0xf9, 0x20, //0x00000faa cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00000fad je           LBB0_112
	0x80, 0xc1, 0xf5, //0x00000fb3 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00000fb6 cmpb         $-2, %cl
	0x0f, 0x82, 0xea, 0x00, 0x00, 0x00, //0x00000fb9 jb           LBB0_125
	0x90, //0x00000fbf .p2align 4, 0x90
	//0x00000fc0 LBB0_112
	0x49, 0x8d, 0x56, 0x04, //0x00000fc0 leaq         $4(%r14), %rdx
	0x48, 0x89, 0xd9, //0x00000fc4 movq         %rbx, %rcx
	0x48, 0x29, 0xd1, //0x00000fc7 subq         %rdx, %rcx
	0x0f, 0x86, 0x51, 0x20, 0x00, 0x00, //0x00000fca jbe          LBB0_1022
	0x48, 0x83, 0xf9, 0x20, //0x00000fd0 cmpq         $32, %rcx
	0x0f, 0x82, 0xa6, 0x0c, 0x00, 0x00, //0x00000fd4 jb           LBB0_353
	0x48, 0xc7, 0xc1, 0xfc, 0xff, 0xff, 0xff, //0x00000fda movq         $-4, %rcx
	0x4c, 0x29, 0xf1, //0x00000fe1 subq         %r14, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000fe4 .p2align 4, 0x90
	//0x00000ff0 LBB0_115
	0xc4, 0x41, 0x7e, 0x6f, 0x34, 0x14, //0x00000ff0 vmovdqu      (%r12,%rdx), %ymm14
	0xc4, 0xc2, 0x7d, 0x00, 0xf6, //0x00000ff6 vpshufb      %ymm14, %ymm0, %ymm6
	0xc5, 0x8d, 0xf8, 0xfe, //0x00000ffb vpsubb       %ymm6, %ymm14, %ymm7
	0xc4, 0xe2, 0x7d, 0x17, 0xff, //0x00000fff vptest       %ymm7, %ymm7
	0x0f, 0x85, 0x86, 0x00, 0x00, 0x00, //0x00001004 jne          LBB0_124
	0x48, 0x83, 0xc2, 0x20, //0x0000100a addq         $32, %rdx
	0x48, 0x8d, 0x34, 0x0b, //0x0000100e leaq         (%rbx,%rcx), %rsi
	0x48, 0x83, 0xc6, 0xe0, //0x00001012 addq         $-32, %rsi
	0x48, 0x83, 0xc1, 0xe0, //0x00001016 addq         $-32, %rcx
	0x48, 0x83, 0xfe, 0x1f, //0x0000101a cmpq         $31, %rsi
	0x0f, 0x87, 0xcc, 0xff, 0xff, 0xff, //0x0000101e ja           LBB0_115
	0x4c, 0x89, 0xe2, //0x00001024 movq         %r12, %rdx
	0x48, 0x29, 0xca, //0x00001027 subq         %rcx, %rdx
	0x48, 0x01, 0xd9, //0x0000102a addq         %rbx, %rcx
	0x48, 0x85, 0xc9, //0x0000102d testq        %rcx, %rcx
	0x0f, 0x84, 0x40, 0x00, 0x00, 0x00, //0x00001030 je           LBB0_123
	//0x00001036 LBB0_118
	0x4c, 0x8d, 0x04, 0x0a, //0x00001036 leaq         (%rdx,%rcx), %r8
	0x31, 0xf6, //0x0000103a xorl         %esi, %esi
	0x90, 0x90, 0x90, 0x90, //0x0000103c .p2align 4, 0x90
	//0x00001040 LBB0_119
	0x0f, 0xbe, 0x3c, 0x32, //0x00001040 movsbl       (%rdx,%rsi), %edi
	0x83, 0xff, 0x20, //0x00001044 cmpl         $32, %edi
	0x0f, 0x87, 0xf2, 0x0b, 0x00, 0x00, //0x00001047 ja           LBB0_303
	0x48, 0xb8, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000104d movabsq      $4294977024, %rax
	0x48, 0x0f, 0xa3, 0xf8, //0x00001057 btq          %rdi, %rax
	0x0f, 0x83, 0xde, 0x0b, 0x00, 0x00, //0x0000105b jae          LBB0_303
	0x48, 0x83, 0xc6, 0x01, //0x00001061 addq         $1, %rsi
	0x48, 0x39, 0xf1, //0x00001065 cmpq         %rsi, %rcx
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x00001068 jne          LBB0_119
	0x4c, 0x89, 0xc2, //0x0000106e movq         %r8, %rdx
	0x4c, 0x8b, 0x44, 0x24, 0x28, //0x00001071 movq         $40(%rsp), %r8
	//0x00001076 LBB0_123
	0x4c, 0x29, 0xe2, //0x00001076 subq         %r12, %rdx
	0x48, 0x39, 0xda, //0x00001079 cmpq         %rbx, %rdx
	0x0f, 0x82, 0x27, 0x00, 0x00, 0x00, //0x0000107c jb           LBB0_125
	0xe9, 0xea, 0x3a, 0x00, 0x00, //0x00001082 jmp          LBB0_1023
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001087 .p2align 4, 0x90
	//0x00001090 LBB0_124
	0xc5, 0x8d, 0x74, 0xf6, //0x00001090 vpcmpeqb     %ymm6, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xd6, //0x00001094 vpmovmskb    %ymm6, %edx
	0xf7, 0xd2, //0x00001098 notl         %edx
	0x0f, 0xbc, 0xd2, //0x0000109a bsfl         %edx, %edx
	0x48, 0x29, 0xca, //0x0000109d subq         %rcx, %rdx
	0x48, 0x39, 0xda, //0x000010a0 cmpq         %rbx, %rdx
	0x0f, 0x83, 0xc8, 0x3a, 0x00, 0x00, //0x000010a3 jae          LBB0_1023
	//0x000010a9 LBB0_125
	0x4c, 0x8d, 0x72, 0x01, //0x000010a9 leaq         $1(%rdx), %r14
	0x4d, 0x89, 0x75, 0x00, //0x000010ad movq         %r14, (%r13)
	0x41, 0x80, 0x3c, 0x14, 0x3a, //0x000010b1 cmpb         $58, (%r12,%rdx)
	0x0f, 0x85, 0xb5, 0x3a, 0x00, 0x00, //0x000010b6 jne          LBB0_1023
	0x4d, 0x85, 0xc9, //0x000010bc testq        %r9, %r9
	0x0f, 0x85, 0x9b, 0x1c, 0x00, 0x00, //0x000010bf jne          LBB0_519
	0x49, 0x8b, 0x18, //0x000010c5 movq         (%r8), %rbx
	0x49, 0x39, 0xde, //0x000010c8 cmpq         %rbx, %r14
	0x0f, 0x83, 0x2f, 0x00, 0x00, 0x00, //0x000010cb jae          LBB0_132
	0x43, 0x8a, 0x04, 0x34, //0x000010d1 movb         (%r12,%r14), %al
	0x3c, 0x0d, //0x000010d5 cmpb         $13, %al
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x000010d7 je           LBB0_132
	0x3c, 0x20, //0x000010dd cmpb         $32, %al
	0x0f, 0x84, 0x1b, 0x00, 0x00, 0x00, //0x000010df je           LBB0_132
	0x04, 0xf5, //0x000010e5 addb         $-11, %al
	0x3c, 0xfe, //0x000010e7 cmpb         $-2, %al
	0x0f, 0x83, 0x11, 0x00, 0x00, 0x00, //0x000010e9 jae          LBB0_132
	0x4c, 0x89, 0xf0, //0x000010ef movq         %r14, %rax
	0xe9, 0x72, 0x01, 0x00, 0x00, //0x000010f2 jmp          LBB0_157
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000010f7 .p2align 4, 0x90
	//0x00001100 LBB0_132
	0x48, 0x8d, 0x42, 0x02, //0x00001100 leaq         $2(%rdx), %rax
	0x48, 0x39, 0xd8, //0x00001104 cmpq         %rbx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001107 jae          LBB0_136
	0x41, 0x8a, 0x0c, 0x04, //0x0000110d movb         (%r12,%rax), %cl
	0x80, 0xf9, 0x0d, //0x00001111 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001114 je           LBB0_136
	0x80, 0xf9, 0x20, //0x0000111a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000111d je           LBB0_136
	0x80, 0xc1, 0xf5, //0x00001123 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00001126 cmpb         $-2, %cl
	0x0f, 0x82, 0x3a, 0x01, 0x00, 0x00, //0x00001129 jb           LBB0_157
	0x90, //0x0000112f .p2align 4, 0x90
	//0x00001130 LBB0_136
	0x48, 0x8d, 0x42, 0x03, //0x00001130 leaq         $3(%rdx), %rax
	0x48, 0x39, 0xd8, //0x00001134 cmpq         %rbx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001137 jae          LBB0_140
	0x41, 0x8a, 0x0c, 0x04, //0x0000113d movb         (%r12,%rax), %cl
	0x80, 0xf9, 0x0d, //0x00001141 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001144 je           LBB0_140
	0x80, 0xf9, 0x20, //0x0000114a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000114d je           LBB0_140
	0x80, 0xc1, 0xf5, //0x00001153 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00001156 cmpb         $-2, %cl
	0x0f, 0x82, 0x0a, 0x01, 0x00, 0x00, //0x00001159 jb           LBB0_157
	0x90, //0x0000115f .p2align 4, 0x90
	//0x00001160 LBB0_140
	0x48, 0x8d, 0x42, 0x04, //0x00001160 leaq         $4(%rdx), %rax
	0x48, 0x39, 0xd8, //0x00001164 cmpq         %rbx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001167 jae          LBB0_144
	0x41, 0x8a, 0x0c, 0x04, //0x0000116d movb         (%r12,%rax), %cl
	0x80, 0xf9, 0x0d, //0x00001171 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001174 je           LBB0_144
	0x80, 0xf9, 0x20, //0x0000117a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000117d je           LBB0_144
	0x80, 0xc1, 0xf5, //0x00001183 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00001186 cmpb         $-2, %cl
	0x0f, 0x82, 0xda, 0x00, 0x00, 0x00, //0x00001189 jb           LBB0_157
	0x90, //0x0000118f .p2align 4, 0x90
	//0x00001190 LBB0_144
	0x48, 0x8d, 0x42, 0x05, //0x00001190 leaq         $5(%rdx), %rax
	0x48, 0x89, 0xd9, //0x00001194 movq         %rbx, %rcx
	0x48, 0x29, 0xc1, //0x00001197 subq         %rax, %rcx
	0x0f, 0x86, 0x00, 0x03, 0x00, 0x00, //0x0000119a jbe          LBB0_202
	0x48, 0x83, 0xf9, 0x20, //0x000011a0 cmpq         $32, %rcx
	0x0f, 0x82, 0x04, 0x0b, 0x00, 0x00, //0x000011a4 jb           LBB0_356
	0x48, 0xc7, 0xc1, 0xfb, 0xff, 0xff, 0xff, //0x000011aa movq         $-5, %rcx
	0x48, 0x29, 0xd1, //0x000011b1 subq         %rdx, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000011b4 .p2align 4, 0x90
	//0x000011c0 LBB0_147
	0xc4, 0x41, 0x7e, 0x6f, 0x34, 0x04, //0x000011c0 vmovdqu      (%r12,%rax), %ymm14
	0xc4, 0xc2, 0x7d, 0x00, 0xf6, //0x000011c6 vpshufb      %ymm14, %ymm0, %ymm6
	0xc5, 0x8d, 0xf8, 0xfe, //0x000011cb vpsubb       %ymm6, %ymm14, %ymm7
	0xc4, 0xe2, 0x7d, 0x17, 0xff, //0x000011cf vptest       %ymm7, %ymm7
	0x0f, 0x85, 0x76, 0x00, 0x00, 0x00, //0x000011d4 jne          LBB0_156
	0x48, 0x83, 0xc0, 0x20, //0x000011da addq         $32, %rax
	0x48, 0x8d, 0x14, 0x0b, //0x000011de leaq         (%rbx,%rcx), %rdx
	0x48, 0x83, 0xc2, 0xe0, //0x000011e2 addq         $-32, %rdx
	0x48, 0x83, 0xc1, 0xe0, //0x000011e6 addq         $-32, %rcx
	0x48, 0x83, 0xfa, 0x1f, //0x000011ea cmpq         $31, %rdx
	0x0f, 0x87, 0xcc, 0xff, 0xff, 0xff, //0x000011ee ja           LBB0_147
	0x4c, 0x89, 0xe0, //0x000011f4 movq         %r12, %rax
	0x48, 0x29, 0xc8, //0x000011f7 subq         %rcx, %rax
	0x48, 0x01, 0xd9, //0x000011fa addq         %rbx, %rcx
	0x48, 0x85, 0xc9, //0x000011fd testq        %rcx, %rcx
	0x0f, 0x84, 0x37, 0x00, 0x00, 0x00, //0x00001200 je           LBB0_155
	//0x00001206 LBB0_150
	0x4c, 0x8d, 0x0c, 0x08, //0x00001206 leaq         (%rax,%rcx), %r9
	0x31, 0xd2, //0x0000120a xorl         %edx, %edx
	//0x0000120c LBB0_151
	0x0f, 0xbe, 0x3c, 0x10, //0x0000120c movsbl       (%rax,%rdx), %edi
	0x83, 0xff, 0x20, //0x00001210 cmpl         $32, %edi
	0x0f, 0x87, 0x53, 0x0a, 0x00, 0x00, //0x00001213 ja           LBB0_305
	0x48, 0xbe, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001219 movabsq      $4294977024, %rsi
	0x48, 0x0f, 0xa3, 0xfe, //0x00001223 btq          %rdi, %rsi
	0x0f, 0x83, 0x3f, 0x0a, 0x00, 0x00, //0x00001227 jae          LBB0_305
	0x48, 0x83, 0xc2, 0x01, //0x0000122d addq         $1, %rdx
	0x48, 0x39, 0xd1, //0x00001231 cmpq         %rdx, %rcx
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x00001234 jne          LBB0_151
	0x4c, 0x89, 0xc8, //0x0000123a movq         %r9, %rax
	//0x0000123d LBB0_155
	0x4c, 0x29, 0xe0, //0x0000123d subq         %r12, %rax
	0x48, 0x39, 0xd8, //0x00001240 cmpq         %rbx, %rax
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x00001243 jb           LBB0_157
	0xe9, 0x59, 0x02, 0x00, 0x00, //0x00001249 jmp          LBB0_203
	0x90, 0x90, //0x0000124e .p2align 4, 0x90
	//0x00001250 LBB0_156
	0xc5, 0x8d, 0x74, 0xf6, //0x00001250 vpcmpeqb     %ymm6, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00001254 vpmovmskb    %ymm6, %eax
	0xf7, 0xd0, //0x00001258 notl         %eax
	0x0f, 0xbc, 0xc0, //0x0000125a bsfl         %eax, %eax
	0x48, 0x29, 0xc8, //0x0000125d subq         %rcx, %rax
	0x48, 0x39, 0xd8, //0x00001260 cmpq         %rbx, %rax
	0x0f, 0x83, 0x3e, 0x02, 0x00, 0x00, //0x00001263 jae          LBB0_203
	//0x00001269 LBB0_157
	0x4c, 0x8d, 0x70, 0x01, //0x00001269 leaq         $1(%rax), %r14
	0x4d, 0x89, 0x75, 0x00, //0x0000126d movq         %r14, (%r13)
	0x41, 0x0f, 0xbe, 0x0c, 0x04, //0x00001271 movsbl       (%r12,%rax), %ecx
	0x83, 0xf9, 0x7b, //0x00001276 cmpl         $123, %ecx
	0x0f, 0x87, 0x21, 0x02, 0x00, 0x00, //0x00001279 ja           LBB0_202
	0x48, 0x8d, 0x15, 0xaa, 0x44, 0x00, 0x00, //0x0000127f leaq         $17578(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x0c, 0x8a, //0x00001286 movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x0000128a addq         %rdx, %rcx
	0xff, 0xe1, //0x0000128d jmpq         *%rcx
	//0x0000128f LBB0_159
	0x49, 0x8b, 0x10, //0x0000128f movq         (%r8), %rdx
	0x48, 0x89, 0xd1, //0x00001292 movq         %rdx, %rcx
	0x4c, 0x29, 0xf1, //0x00001295 subq         %r14, %rcx
	0x48, 0x83, 0xf9, 0x20, //0x00001298 cmpq         $32, %rcx
	0x0f, 0x82, 0x1d, 0x0a, 0x00, 0x00, //0x0000129c jb           LBB0_357
	0x48, 0xf7, 0xd0, //0x000012a2 notq         %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000012a5 .p2align 4, 0x90
	//0x000012b0 LBB0_161
	0xc4, 0x81, 0x7e, 0x6f, 0x34, 0x34, //0x000012b0 vmovdqu      (%r12,%r14), %ymm6
	0xc5, 0xcd, 0x74, 0xfb, //0x000012b6 vpcmpeqb     %ymm3, %ymm6, %ymm7
	0xc5, 0xcd, 0xdb, 0xf4, //0x000012ba vpand        %ymm4, %ymm6, %ymm6
	0xc5, 0xcd, 0x74, 0xf5, //0x000012be vpcmpeqb     %ymm5, %ymm6, %ymm6
	0xc5, 0xcd, 0xeb, 0xf7, //0x000012c2 vpor         %ymm7, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xce, //0x000012c6 vpmovmskb    %ymm6, %ecx
	0x85, 0xc9, //0x000012ca testl        %ecx, %ecx
	0x0f, 0x85, 0xbe, 0x00, 0x00, 0x00, //0x000012cc jne          LBB0_175
	0x49, 0x83, 0xc6, 0x20, //0x000012d2 addq         $32, %r14
	0x48, 0x8d, 0x0c, 0x02, //0x000012d6 leaq         (%rdx,%rax), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x000012da addq         $-32, %rcx
	0x48, 0x83, 0xc0, 0xe0, //0x000012de addq         $-32, %rax
	0x48, 0x83, 0xf9, 0x1f, //0x000012e2 cmpq         $31, %rcx
	0x0f, 0x87, 0xc4, 0xff, 0xff, 0xff, //0x000012e6 ja           LBB0_161
	0x4d, 0x89, 0xe6, //0x000012ec movq         %r12, %r14
	0x49, 0x29, 0xc6, //0x000012ef subq         %rax, %r14
	0x48, 0x01, 0xc2, //0x000012f2 addq         %rax, %rdx
	0x48, 0x89, 0xd1, //0x000012f5 movq         %rdx, %rcx
	0x48, 0x83, 0xf9, 0x10, //0x000012f8 cmpq         $16, %rcx
	0x0f, 0x82, 0x41, 0x00, 0x00, 0x00, //0x000012fc jb           LBB0_167
	//0x00001302 LBB0_164
	0x4c, 0x89, 0xe0, //0x00001302 movq         %r12, %rax
	0x4c, 0x29, 0xf0, //0x00001305 subq         %r14, %rax
	//0x00001308 LBB0_165
	0xc4, 0xc1, 0x7a, 0x6f, 0x36, //0x00001308 vmovdqu      (%r14), %xmm6
	0xc5, 0xc9, 0x74, 0x3d, 0xcb, 0xee, 0xff, 0xff, //0x0000130d vpcmpeqb     $-4405(%rip), %xmm6, %xmm7  /* LCPI0_4+0(%rip) */
	0xc5, 0xb1, 0xdb, 0xf6, //0x00001315 vpand        %xmm6, %xmm9, %xmm6
	0xc5, 0xa1, 0x74, 0xf6, //0x00001319 vpcmpeqb     %xmm6, %xmm11, %xmm6
	0xc5, 0xc9, 0xeb, 0xf7, //0x0000131d vpor         %xmm7, %xmm6, %xmm6
	0xc5, 0xf9, 0xd7, 0xd6, //0x00001321 vpmovmskb    %xmm6, %edx
	0x85, 0xd2, //0x00001325 testl        %edx, %edx
	0x0f, 0x85, 0x05, 0x09, 0x00, 0x00, //0x00001327 jne          LBB0_301
	0x49, 0x83, 0xc6, 0x10, //0x0000132d addq         $16, %r14
	0x48, 0x83, 0xc1, 0xf0, //0x00001331 addq         $-16, %rcx
	0x48, 0x83, 0xc0, 0xf0, //0x00001335 addq         $-16, %rax
	0x48, 0x83, 0xf9, 0x0f, //0x00001339 cmpq         $15, %rcx
	0x0f, 0x87, 0xc5, 0xff, 0xff, 0xff, //0x0000133d ja           LBB0_165
	//0x00001343 LBB0_167
	0x48, 0x85, 0xc9, //0x00001343 testq        %rcx, %rcx
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00001346 je           LBB0_174
	0x49, 0x8d, 0x14, 0x0e, //0x0000134c leaq         (%r14,%rcx), %rdx
	0x31, 0xc0, //0x00001350 xorl         %eax, %eax
	//0x00001352 LBB0_169
	0x41, 0x0f, 0xb6, 0x1c, 0x06, //0x00001352 movzbl       (%r14,%rax), %ebx
	0x80, 0xfb, 0x2c, //0x00001357 cmpb         $44, %bl
	0x0f, 0x84, 0x79, 0x09, 0x00, 0x00, //0x0000135a je           LBB0_358
	0x80, 0xfb, 0x7d, //0x00001360 cmpb         $125, %bl
	0x0f, 0x84, 0x70, 0x09, 0x00, 0x00, //0x00001363 je           LBB0_358
	0x80, 0xfb, 0x5d, //0x00001369 cmpb         $93, %bl
	0x0f, 0x84, 0x67, 0x09, 0x00, 0x00, //0x0000136c je           LBB0_358
	0x48, 0x83, 0xc0, 0x01, //0x00001372 addq         $1, %rax
	0x48, 0x39, 0xc1, //0x00001376 cmpq         %rax, %rcx
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x00001379 jne          LBB0_169
	0x49, 0x89, 0xd6, //0x0000137f movq         %rdx, %r14
	//0x00001382 LBB0_174
	0x4d, 0x29, 0xe6, //0x00001382 subq         %r12, %r14
	0xe9, 0x55, 0x09, 0x00, 0x00, //0x00001385 jmp          LBB0_359
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000138a .p2align 4, 0x90
	//0x00001390 LBB0_175
	0x44, 0x0f, 0xbc, 0xf1, //0x00001390 bsfl         %ecx, %r14d
	//0x00001394 LBB0_176
	0x49, 0x29, 0xc6, //0x00001394 subq         %rax, %r14
	0x4d, 0x89, 0x75, 0x00, //0x00001397 movq         %r14, (%r13)
	0xe9, 0x07, 0x01, 0x00, 0x00, //0x0000139b jmp          LBB0_203
	//0x000013a0 LBB0_183
	0x48, 0x83, 0xc0, 0x04, //0x000013a0 addq         $4, %rax
	0x49, 0x3b, 0x00, //0x000013a4 cmpq         (%r8), %rax
	0x0f, 0x86, 0xf3, 0x00, 0x00, 0x00, //0x000013a7 jbe          LBB0_202
	0xe9, 0xf5, 0x00, 0x00, 0x00, //0x000013ad jmp          LBB0_203
	//0x000013b2 LBB0_189
	0x4d, 0x8b, 0x00, //0x000013b2 movq         (%r8), %r8
	0x4d, 0x89, 0xc7, //0x000013b5 movq         %r8, %r15
	0x4d, 0x29, 0xf7, //0x000013b8 subq         %r14, %r15
	0x49, 0x83, 0xff, 0x20, //0x000013bb cmpq         $32, %r15
	0x0f, 0x8c, 0x24, 0x09, 0x00, 0x00, //0x000013bf jl           LBB0_361
	0x4d, 0x8d, 0x0c, 0x04, //0x000013c5 leaq         (%r12,%rax), %r9
	0x49, 0x29, 0xc0, //0x000013c9 subq         %rax, %r8
	0xbf, 0x1f, 0x00, 0x00, 0x00, //0x000013cc movl         $31, %edi
	0x45, 0x31, 0xff, //0x000013d1 xorl         %r15d, %r15d
	0x45, 0x31, 0xdb, //0x000013d4 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000013d7 .p2align 4, 0x90
	//0x000013e0 LBB0_191
	0xc4, 0x81, 0x7e, 0x6f, 0x74, 0x39, 0x01, //0x000013e0 vmovdqu      $1(%r9,%r15), %ymm6
	0xc5, 0xcd, 0x74, 0xf9, //0x000013e7 vpcmpeqb     %ymm1, %ymm6, %ymm7
	0xc5, 0x7d, 0xd7, 0xd7, //0x000013eb vpmovmskb    %ymm7, %r10d
	0xc5, 0xcd, 0x74, 0xf2, //0x000013ef vpcmpeqb     %ymm2, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xce, //0x000013f3 vpmovmskb    %ymm6, %ecx
	0x85, 0xc9, //0x000013f7 testl        %ecx, %ecx
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x000013f9 jne          LBB0_194
	0x4d, 0x85, 0xdb, //0x000013ff testq        %r11, %r11
	0x0f, 0x85, 0x08, 0x00, 0x00, 0x00, //0x00001402 jne          LBB0_194
	0x45, 0x31, 0xdb, //0x00001408 xorl         %r11d, %r11d
	0xe9, 0x31, 0x00, 0x00, 0x00, //0x0000140b jmp          LBB0_195
	//0x00001410 .p2align 4, 0x90
	//0x00001410 LBB0_194
	0x44, 0x89, 0xde, //0x00001410 movl         %r11d, %esi
	0xf7, 0xd6, //0x00001413 notl         %esi
	0x21, 0xce, //0x00001415 andl         %ecx, %esi
	0x8d, 0x14, 0x36, //0x00001417 leal         (%rsi,%rsi), %edx
	0x44, 0x09, 0xda, //0x0000141a orl          %r11d, %edx
	0x89, 0xd3, //0x0000141d movl         %edx, %ebx
	0xf7, 0xd3, //0x0000141f notl         %ebx
	0x21, 0xcb, //0x00001421 andl         %ecx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001423 andl         $-1431655766, %ebx
	0x45, 0x31, 0xdb, //0x00001429 xorl         %r11d, %r11d
	0x01, 0xf3, //0x0000142c addl         %esi, %ebx
	0x41, 0x0f, 0x92, 0xc3, //0x0000142e setb         %r11b
	0x01, 0xdb, //0x00001432 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00001434 xorl         $1431655765, %ebx
	0x21, 0xd3, //0x0000143a andl         %edx, %ebx
	0xf7, 0xd3, //0x0000143c notl         %ebx
	0x41, 0x21, 0xda, //0x0000143e andl         %ebx, %r10d
	//0x00001441 LBB0_195
	0x4d, 0x85, 0xd2, //0x00001441 testq        %r10, %r10
	0x0f, 0x85, 0x7c, 0x07, 0x00, 0x00, //0x00001444 jne          LBB0_288
	0x49, 0x83, 0xc7, 0x20, //0x0000144a addq         $32, %r15
	0x49, 0x8d, 0x0c, 0x38, //0x0000144e leaq         (%r8,%rdi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00001452 addq         $-32, %rcx
	0x48, 0x83, 0xc7, 0xe0, //0x00001456 addq         $-32, %rdi
	0x48, 0x83, 0xf9, 0x3f, //0x0000145a cmpq         $63, %rcx
	0x0f, 0x8f, 0x7c, 0xff, 0xff, 0xff, //0x0000145e jg           LBB0_191
	0x4d, 0x85, 0xdb, //0x00001464 testq        %r11, %r11
	0x0f, 0x85, 0xf8, 0x09, 0x00, 0x00, //0x00001467 jne          LBB0_384
	0x4b, 0x8d, 0x04, 0x0f, //0x0000146d leaq         (%r15,%r9), %rax
	0x48, 0x83, 0xc0, 0x01, //0x00001471 addq         $1, %rax
	0x49, 0xf7, 0xd7, //0x00001475 notq         %r15
	0x4d, 0x01, 0xc7, //0x00001478 addq         %r8, %r15
	//0x0000147b LBB0_199
	0x4d, 0x85, 0xff, //0x0000147b testq        %r15, %r15
	0x4c, 0x8b, 0x44, 0x24, 0x28, //0x0000147e movq         $40(%rsp), %r8
	0x0f, 0x8e, 0xd2, 0x09, 0x00, 0x00, //0x00001483 jle          LBB0_383
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00001489 movq         $24(%rsp), %r10
	0xe9, 0x6d, 0x07, 0x00, 0x00, //0x0000148e jmp          LBB0_297
	//0x00001493 LBB0_201
	0x48, 0x83, 0xc0, 0x05, //0x00001493 addq         $5, %rax
	0x49, 0x3b, 0x00, //0x00001497 cmpq         (%r8), %rax
	0x0f, 0x87, 0x07, 0x00, 0x00, 0x00, //0x0000149a ja           LBB0_203
	//0x000014a0 .p2align 4, 0x90
	//0x000014a0 LBB0_202
	0x49, 0x89, 0x45, 0x00, //0x000014a0 movq         %rax, (%r13)
	0x49, 0x89, 0xc6, //0x000014a4 movq         %rax, %r14
	//0x000014a7 LBB0_203
	0x4d, 0x8b, 0x22, //0x000014a7 movq         (%r10), %r12
	0x49, 0x8b, 0x42, 0x08, //0x000014aa movq         $8(%r10), %rax
	0x49, 0x39, 0xc6, //0x000014ae cmpq         %rax, %r14
	0x0f, 0x83, 0x39, 0x00, 0x00, 0x00, //0x000014b1 jae          LBB0_208
	0x43, 0x8a, 0x0c, 0x34, //0x000014b7 movb         (%r12,%r14), %cl
	0x80, 0xf9, 0x0d, //0x000014bb cmpb         $13, %cl
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x000014be je           LBB0_208
	0x80, 0xf9, 0x20, //0x000014c4 cmpb         $32, %cl
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x000014c7 je           LBB0_208
	0x80, 0xc1, 0xf5, //0x000014cd addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x000014d0 cmpb         $-2, %cl
	0x0f, 0x83, 0x17, 0x00, 0x00, 0x00, //0x000014d3 jae          LBB0_208
	0x4c, 0x89, 0xf2, //0x000014d9 movq         %r14, %rdx
	0xe9, 0x78, 0x01, 0x00, 0x00, //0x000014dc jmp          LBB0_233
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000014e1 .p2align 4, 0x90
	//0x000014f0 LBB0_208
	0x49, 0x8d, 0x56, 0x01, //0x000014f0 leaq         $1(%r14), %rdx
	0x48, 0x39, 0xc2, //0x000014f4 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000014f7 jae          LBB0_212
	0x41, 0x8a, 0x0c, 0x14, //0x000014fd movb         (%r12,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00001501 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001504 je           LBB0_212
	0x80, 0xf9, 0x20, //0x0000150a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000150d je           LBB0_212
	0x80, 0xc1, 0xf5, //0x00001513 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00001516 cmpb         $-2, %cl
	0x0f, 0x82, 0x3a, 0x01, 0x00, 0x00, //0x00001519 jb           LBB0_233
	0x90, //0x0000151f .p2align 4, 0x90
	//0x00001520 LBB0_212
	0x49, 0x8d, 0x56, 0x02, //0x00001520 leaq         $2(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00001524 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001527 jae          LBB0_216
	0x41, 0x8a, 0x0c, 0x14, //0x0000152d movb         (%r12,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00001531 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001534 je           LBB0_216
	0x80, 0xf9, 0x20, //0x0000153a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000153d je           LBB0_216
	0x80, 0xc1, 0xf5, //0x00001543 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00001546 cmpb         $-2, %cl
	0x0f, 0x82, 0x0a, 0x01, 0x00, 0x00, //0x00001549 jb           LBB0_233
	0x90, //0x0000154f .p2align 4, 0x90
	//0x00001550 LBB0_216
	0x49, 0x8d, 0x56, 0x03, //0x00001550 leaq         $3(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00001554 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001557 jae          LBB0_220
	0x41, 0x8a, 0x0c, 0x14, //0x0000155d movb         (%r12,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00001561 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001564 je           LBB0_220
	0x80, 0xf9, 0x20, //0x0000156a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000156d je           LBB0_220
	0x80, 0xc1, 0xf5, //0x00001573 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00001576 cmpb         $-2, %cl
	0x0f, 0x82, 0xda, 0x00, 0x00, 0x00, //0x00001579 jb           LBB0_233
	0x90, //0x0000157f .p2align 4, 0x90
	//0x00001580 LBB0_220
	0x49, 0x8d, 0x56, 0x04, //0x00001580 leaq         $4(%r14), %rdx
	0x48, 0x89, 0xc1, //0x00001584 movq         %rax, %rcx
	0x48, 0x29, 0xd1, //0x00001587 subq         %rdx, %rcx
	0x0f, 0x86, 0x91, 0x1a, 0x00, 0x00, //0x0000158a jbe          LBB0_1022
	0x48, 0x83, 0xf9, 0x20, //0x00001590 cmpq         $32, %rcx
	0x0f, 0x82, 0x03, 0x07, 0x00, 0x00, //0x00001594 jb           LBB0_355
	0x48, 0xc7, 0xc1, 0xfc, 0xff, 0xff, 0xff, //0x0000159a movq         $-4, %rcx
	0x4c, 0x29, 0xf1, //0x000015a1 subq         %r14, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000015a4 .p2align 4, 0x90
	//0x000015b0 LBB0_223
	0xc4, 0x41, 0x7e, 0x6f, 0x34, 0x14, //0x000015b0 vmovdqu      (%r12,%rdx), %ymm14
	0xc4, 0xc2, 0x7d, 0x00, 0xf6, //0x000015b6 vpshufb      %ymm14, %ymm0, %ymm6
	0xc5, 0x8d, 0xf8, 0xfe, //0x000015bb vpsubb       %ymm6, %ymm14, %ymm7
	0xc4, 0xe2, 0x7d, 0x17, 0xff, //0x000015bf vptest       %ymm7, %ymm7
	0x0f, 0x85, 0x76, 0x00, 0x00, 0x00, //0x000015c4 jne          LBB0_232
	0x48, 0x83, 0xc2, 0x20, //0x000015ca addq         $32, %rdx
	0x48, 0x8d, 0x34, 0x08, //0x000015ce leaq         (%rax,%rcx), %rsi
	0x48, 0x83, 0xc6, 0xe0, //0x000015d2 addq         $-32, %rsi
	0x48, 0x83, 0xc1, 0xe0, //0x000015d6 addq         $-32, %rcx
	0x48, 0x83, 0xfe, 0x1f, //0x000015da cmpq         $31, %rsi
	0x0f, 0x87, 0xcc, 0xff, 0xff, 0xff, //0x000015de ja           LBB0_223
	0x4c, 0x89, 0xe2, //0x000015e4 movq         %r12, %rdx
	0x48, 0x29, 0xca, //0x000015e7 subq         %rcx, %rdx
	0x48, 0x01, 0xc1, //0x000015ea addq         %rax, %rcx
	0x48, 0x85, 0xc9, //0x000015ed testq        %rcx, %rcx
	0x0f, 0x84, 0x37, 0x00, 0x00, 0x00, //0x000015f0 je           LBB0_231
	//0x000015f6 LBB0_226
	0x4c, 0x8d, 0x0c, 0x0a, //0x000015f6 leaq         (%rdx,%rcx), %r9
	0x31, 0xf6, //0x000015fa xorl         %esi, %esi
	//0x000015fc LBB0_227
	0x0f, 0xbe, 0x1c, 0x32, //0x000015fc movsbl       (%rdx,%rsi), %ebx
	0x83, 0xfb, 0x20, //0x00001600 cmpl         $32, %ebx
	0x0f, 0x87, 0x4f, 0x06, 0x00, 0x00, //0x00001603 ja           LBB0_304
	0x48, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001609 movabsq      $4294977024, %rdi
	0x48, 0x0f, 0xa3, 0xdf, //0x00001613 btq          %rbx, %rdi
	0x0f, 0x83, 0x3b, 0x06, 0x00, 0x00, //0x00001617 jae          LBB0_304
	0x48, 0x83, 0xc6, 0x01, //0x0000161d addq         $1, %rsi
	0x48, 0x39, 0xf1, //0x00001621 cmpq         %rsi, %rcx
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x00001624 jne          LBB0_227
	0x4c, 0x89, 0xca, //0x0000162a movq         %r9, %rdx
	//0x0000162d LBB0_231
	0x4c, 0x29, 0xe2, //0x0000162d subq         %r12, %rdx
	0x48, 0x39, 0xc2, //0x00001630 cmpq         %rax, %rdx
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x00001633 jb           LBB0_233
	0xe9, 0x33, 0x35, 0x00, 0x00, //0x00001639 jmp          LBB0_1023
	0x90, 0x90, //0x0000163e .p2align 4, 0x90
	//0x00001640 LBB0_232
	0xc5, 0x8d, 0x74, 0xf6, //0x00001640 vpcmpeqb     %ymm6, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xd6, //0x00001644 vpmovmskb    %ymm6, %edx
	0xf7, 0xd2, //0x00001648 notl         %edx
	0x0f, 0xbc, 0xd2, //0x0000164a bsfl         %edx, %edx
	0x48, 0x29, 0xca, //0x0000164d subq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x00001650 cmpq         %rax, %rdx
	0x0f, 0x83, 0x18, 0x35, 0x00, 0x00, //0x00001653 jae          LBB0_1023
	//0x00001659 LBB0_233
	0x4c, 0x8d, 0x72, 0x01, //0x00001659 leaq         $1(%rdx), %r14
	0x4d, 0x89, 0x75, 0x00, //0x0000165d movq         %r14, (%r13)
	0x41, 0x8a, 0x04, 0x14, //0x00001661 movb         (%r12,%rdx), %al
	0x3c, 0x2c, //0x00001665 cmpb         $44, %al
	0x0f, 0x84, 0xef, 0xee, 0xff, 0xff, //0x00001667 je           LBB0_38
	0xe9, 0xf2, 0x17, 0x00, 0x00, //0x0000166d jmp          LBB0_234
	//0x00001672 LBB0_235
	0x4d, 0x8b, 0x00, //0x00001672 movq         (%r8), %r8
	0x4d, 0x29, 0xf0, //0x00001675 subq         %r14, %r8
	0x4d, 0x01, 0xf4, //0x00001678 addq         %r14, %r12
	0x45, 0x31, 0xd2, //0x0000167b xorl         %r10d, %r10d
	0x45, 0x31, 0xdb, //0x0000167e xorl         %r11d, %r11d
	0x45, 0x31, 0xf6, //0x00001681 xorl         %r14d, %r14d
	0x31, 0xd2, //0x00001684 xorl         %edx, %edx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00001686 jmp          LBB0_237
	//0x0000168b LBB0_236
	0x48, 0xc1, 0xf9, 0x3f, //0x0000168b sarq         $63, %rcx
	0xf3, 0x48, 0x0f, 0xb8, 0xc6, //0x0000168f popcntq      %rsi, %rax
	0x49, 0x01, 0xc6, //0x00001694 addq         %rax, %r14
	0x49, 0x83, 0xc4, 0x40, //0x00001697 addq         $64, %r12
	0x49, 0x83, 0xc0, 0xc0, //0x0000169b addq         $-64, %r8
	0x49, 0x89, 0xca, //0x0000169f movq         %rcx, %r10
	//0x000016a2 LBB0_237
	0x49, 0x83, 0xf8, 0x40, //0x000016a2 cmpq         $64, %r8
	0x0f, 0x8c, 0x31, 0x01, 0x00, 0x00, //0x000016a6 jl           LBB0_245
	//0x000016ac LBB0_238
	0xc4, 0x41, 0x7e, 0x6f, 0x3c, 0x24, //0x000016ac vmovdqu      (%r12), %ymm15
	0xc4, 0x41, 0x7e, 0x6f, 0x74, 0x24, 0x20, //0x000016b2 vmovdqu      $32(%r12), %ymm14
	0xc5, 0x85, 0x74, 0xf2, //0x000016b9 vpcmpeqb     %ymm2, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xce, //0x000016bd vpmovmskb    %ymm6, %ecx
	0xc5, 0x8d, 0x74, 0xf2, //0x000016c1 vpcmpeqb     %ymm2, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x000016c5 vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x000016c9 shlq         $32, %rax
	0x48, 0x09, 0xc1, //0x000016cd orq          %rax, %rcx
	0x48, 0x89, 0xc8, //0x000016d0 movq         %rcx, %rax
	0x4c, 0x09, 0xd8, //0x000016d3 orq          %r11, %rax
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x000016d6 jne          LBB0_240
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000016dc movq         $-1, %rcx
	0x45, 0x31, 0xdb, //0x000016e3 xorl         %r11d, %r11d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x000016e6 jmp          LBB0_241
	//0x000016eb LBB0_240
	0x4c, 0x89, 0xd8, //0x000016eb movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x000016ee notq         %rax
	0x48, 0x21, 0xc8, //0x000016f1 andq         %rcx, %rax
	0x48, 0x8d, 0x34, 0x00, //0x000016f4 leaq         (%rax,%rax), %rsi
	0x4c, 0x09, 0xde, //0x000016f8 orq          %r11, %rsi
	0x48, 0x89, 0xf7, //0x000016fb movq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x000016fe notq         %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001701 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xd9, //0x0000170b andq         %rbx, %rcx
	0x48, 0x21, 0xf9, //0x0000170e andq         %rdi, %rcx
	0x45, 0x31, 0xdb, //0x00001711 xorl         %r11d, %r11d
	0x48, 0x01, 0xc1, //0x00001714 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc3, //0x00001717 setb         %r11b
	0x48, 0x01, 0xc9, //0x0000171b addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000171e movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x00001728 xorq         %rax, %rcx
	0x48, 0x21, 0xf1, //0x0000172b andq         %rsi, %rcx
	0x48, 0xf7, 0xd1, //0x0000172e notq         %rcx
	//0x00001731 LBB0_241
	0xc5, 0x8d, 0x74, 0xf1, //0x00001731 vpcmpeqb     %ymm1, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00001735 vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001739 shlq         $32, %rax
	0xc5, 0x85, 0x74, 0xf1, //0x0000173d vpcmpeqb     %ymm1, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x00001741 vpmovmskb    %ymm6, %esi
	0x48, 0x09, 0xc6, //0x00001745 orq          %rax, %rsi
	0x48, 0x21, 0xce, //0x00001748 andq         %rcx, %rsi
	0xc4, 0xe1, 0xf9, 0x6e, 0xf6, //0x0000174b vmovq        %rsi, %xmm6
	0xc4, 0xc3, 0x49, 0x44, 0xf0, 0x00, //0x00001750 vpclmulqdq   $0, %xmm8, %xmm6, %xmm6
	0xc4, 0xe1, 0xf9, 0x7e, 0xf1, //0x00001756 vmovq        %xmm6, %rcx
	0x4c, 0x31, 0xd1, //0x0000175b xorq         %r10, %rcx
	0xc4, 0xc1, 0x05, 0x74, 0xf2, //0x0000175e vpcmpeqb     %ymm10, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x00001763 vpmovmskb    %ymm6, %esi
	0xc4, 0xc1, 0x0d, 0x74, 0xf2, //0x00001767 vpcmpeqb     %ymm10, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x0000176c vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001770 shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00001774 orq          %rax, %rsi
	0x48, 0x89, 0xc8, //0x00001777 movq         %rcx, %rax
	0x48, 0xf7, 0xd0, //0x0000177a notq         %rax
	0x48, 0x21, 0xc6, //0x0000177d andq         %rax, %rsi
	0xc5, 0x85, 0x74, 0xf5, //0x00001780 vpcmpeqb     %ymm5, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x00001784 vpmovmskb    %ymm6, %edi
	0xc5, 0x8d, 0x74, 0xf5, //0x00001788 vpcmpeqb     %ymm5, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x0000178c vpmovmskb    %ymm6, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x00001790 shlq         $32, %rbx
	0x48, 0x09, 0xdf, //0x00001794 orq          %rbx, %rdi
	0x48, 0x21, 0xc7, //0x00001797 andq         %rax, %rdi
	0x0f, 0x84, 0xeb, 0xfe, 0xff, 0xff, //0x0000179a je           LBB0_236
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000017a0 movq         $24(%rsp), %r10
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000017a5 .p2align 4, 0x90
	//0x000017b0 LBB0_243
	0x48, 0x8d, 0x5f, 0xff, //0x000017b0 leaq         $-1(%rdi), %rbx
	0x48, 0x89, 0xd8, //0x000017b4 movq         %rbx, %rax
	0x48, 0x21, 0xf0, //0x000017b7 andq         %rsi, %rax
	0xf3, 0x48, 0x0f, 0xb8, 0xc0, //0x000017ba popcntq      %rax, %rax
	0x4c, 0x01, 0xf0, //0x000017bf addq         %r14, %rax
	0x48, 0x39, 0xd0, //0x000017c2 cmpq         %rdx, %rax
	0x0f, 0x86, 0xca, 0x03, 0x00, 0x00, //0x000017c5 jbe          LBB0_287
	0x48, 0x83, 0xc2, 0x01, //0x000017cb addq         $1, %rdx
	0x48, 0x21, 0xdf, //0x000017cf andq         %rbx, %rdi
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x000017d2 jne          LBB0_243
	0xe9, 0xae, 0xfe, 0xff, 0xff, //0x000017d8 jmp          LBB0_236
	//0x000017dd LBB0_245
	0x4d, 0x85, 0xc0, //0x000017dd testq        %r8, %r8
	0x0f, 0x8e, 0x64, 0x06, 0x00, 0x00, //0x000017e0 jle          LBB0_382
	0xc5, 0xc9, 0xef, 0xf6, //0x000017e6 vpxor        %xmm6, %xmm6, %xmm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x60, //0x000017ea vmovdqu      %ymm6, $96(%rsp)
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x000017f0 vmovdqu      %ymm6, $64(%rsp)
	0x44, 0x89, 0xe0, //0x000017f6 movl         %r12d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x000017f9 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x000017fe cmpl         $4033, %eax
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x00001803 jb           LBB0_249
	0x49, 0x83, 0xf8, 0x20, //0x00001809 cmpq         $32, %r8
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x0000180d jb           LBB0_250
	0xc4, 0xc1, 0x7e, 0x6f, 0x34, 0x24, //0x00001813 vmovdqu      (%r12), %ymm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x00001819 vmovdqu      %ymm6, $64(%rsp)
	0x49, 0x83, 0xc4, 0x20, //0x0000181f addq         $32, %r12
	0x49, 0x8d, 0x48, 0xe0, //0x00001823 leaq         $-32(%r8), %rcx
	0x4c, 0x8d, 0x4c, 0x24, 0x60, //0x00001827 leaq         $96(%rsp), %r9
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x0000182c jmp          LBB0_251
	//0x00001831 LBB0_249
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00001831 movq         $16(%rsp), %r13
	0xe9, 0x71, 0xfe, 0xff, 0xff, //0x00001836 jmp          LBB0_238
	//0x0000183b LBB0_250
	0x4c, 0x8d, 0x4c, 0x24, 0x40, //0x0000183b leaq         $64(%rsp), %r9
	0x4c, 0x89, 0xc1, //0x00001840 movq         %r8, %rcx
	//0x00001843 LBB0_251
	0x48, 0x83, 0xf9, 0x10, //0x00001843 cmpq         $16, %rcx
	0x0f, 0x82, 0x4d, 0x00, 0x00, 0x00, //0x00001847 jb           LBB0_252
	0xc4, 0xc1, 0x7a, 0x6f, 0x34, 0x24, //0x0000184d vmovdqu      (%r12), %xmm6
	0xc4, 0xc1, 0x7a, 0x7f, 0x31, //0x00001853 vmovdqu      %xmm6, (%r9)
	0x49, 0x83, 0xc4, 0x10, //0x00001858 addq         $16, %r12
	0x49, 0x83, 0xc1, 0x10, //0x0000185c addq         $16, %r9
	0x48, 0x83, 0xc1, 0xf0, //0x00001860 addq         $-16, %rcx
	0x48, 0x83, 0xf9, 0x08, //0x00001864 cmpq         $8, %rcx
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x00001868 jae          LBB0_259
	//0x0000186e LBB0_253
	0x48, 0x83, 0xf9, 0x04, //0x0000186e cmpq         $4, %rcx
	0x0f, 0x82, 0x49, 0x00, 0x00, 0x00, //0x00001872 jb           LBB0_254
	//0x00001878 LBB0_260
	0x41, 0x8b, 0x04, 0x24, //0x00001878 movl         (%r12), %eax
	0x41, 0x89, 0x01, //0x0000187c movl         %eax, (%r9)
	0x49, 0x83, 0xc4, 0x04, //0x0000187f addq         $4, %r12
	0x49, 0x83, 0xc1, 0x04, //0x00001883 addq         $4, %r9
	0x48, 0x83, 0xc1, 0xfc, //0x00001887 addq         $-4, %rcx
	0x48, 0x83, 0xf9, 0x02, //0x0000188b cmpq         $2, %rcx
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x0000188f jae          LBB0_255
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00001895 jmp          LBB0_256
	//0x0000189a LBB0_252
	0x48, 0x83, 0xf9, 0x08, //0x0000189a cmpq         $8, %rcx
	0x0f, 0x82, 0xca, 0xff, 0xff, 0xff, //0x0000189e jb           LBB0_253
	//0x000018a4 LBB0_259
	0x49, 0x8b, 0x04, 0x24, //0x000018a4 movq         (%r12), %rax
	0x49, 0x89, 0x01, //0x000018a8 movq         %rax, (%r9)
	0x49, 0x83, 0xc4, 0x08, //0x000018ab addq         $8, %r12
	0x49, 0x83, 0xc1, 0x08, //0x000018af addq         $8, %r9
	0x48, 0x83, 0xc1, 0xf8, //0x000018b3 addq         $-8, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x000018b7 cmpq         $4, %rcx
	0x0f, 0x83, 0xb7, 0xff, 0xff, 0xff, //0x000018bb jae          LBB0_260
	//0x000018c1 LBB0_254
	0x48, 0x83, 0xf9, 0x02, //0x000018c1 cmpq         $2, %rcx
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x000018c5 jb           LBB0_256
	//0x000018cb LBB0_255
	0x41, 0x0f, 0xb7, 0x04, 0x24, //0x000018cb movzwl       (%r12), %eax
	0x66, 0x41, 0x89, 0x01, //0x000018d0 movw         %ax, (%r9)
	0x49, 0x83, 0xc4, 0x02, //0x000018d4 addq         $2, %r12
	0x49, 0x83, 0xc1, 0x02, //0x000018d8 addq         $2, %r9
	0x48, 0x83, 0xc1, 0xfe, //0x000018dc addq         $-2, %rcx
	//0x000018e0 LBB0_256
	0x4c, 0x89, 0xe6, //0x000018e0 movq         %r12, %rsi
	0x4c, 0x8d, 0x64, 0x24, 0x40, //0x000018e3 leaq         $64(%rsp), %r12
	0x48, 0x85, 0xc9, //0x000018e8 testq        %rcx, %rcx
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x000018eb movq         $16(%rsp), %r13
	0x0f, 0x84, 0xb6, 0xfd, 0xff, 0xff, //0x000018f0 je           LBB0_238
	0x8a, 0x06, //0x000018f6 movb         (%rsi), %al
	0x41, 0x88, 0x01, //0x000018f8 movb         %al, (%r9)
	0x4c, 0x8d, 0x64, 0x24, 0x40, //0x000018fb leaq         $64(%rsp), %r12
	0xe9, 0xa7, 0xfd, 0xff, 0xff, //0x00001900 jmp          LBB0_238
	//0x00001905 LBB0_261
	0x4d, 0x8b, 0x00, //0x00001905 movq         (%r8), %r8
	0x4d, 0x29, 0xf0, //0x00001908 subq         %r14, %r8
	0x4d, 0x01, 0xf4, //0x0000190b addq         %r14, %r12
	0x45, 0x31, 0xd2, //0x0000190e xorl         %r10d, %r10d
	0x45, 0x31, 0xdb, //0x00001911 xorl         %r11d, %r11d
	0x45, 0x31, 0xf6, //0x00001914 xorl         %r14d, %r14d
	0x31, 0xd2, //0x00001917 xorl         %edx, %edx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00001919 jmp          LBB0_263
	//0x0000191e LBB0_262
	0x48, 0xc1, 0xf9, 0x3f, //0x0000191e sarq         $63, %rcx
	0xf3, 0x48, 0x0f, 0xb8, 0xc6, //0x00001922 popcntq      %rsi, %rax
	0x49, 0x01, 0xc6, //0x00001927 addq         %rax, %r14
	0x49, 0x83, 0xc4, 0x40, //0x0000192a addq         $64, %r12
	0x49, 0x83, 0xc0, 0xc0, //0x0000192e addq         $-64, %r8
	0x49, 0x89, 0xca, //0x00001932 movq         %rcx, %r10
	//0x00001935 LBB0_263
	0x49, 0x83, 0xf8, 0x40, //0x00001935 cmpq         $64, %r8
	0x0f, 0x8c, 0x2e, 0x01, 0x00, 0x00, //0x00001939 jl           LBB0_271
	//0x0000193f LBB0_264
	0xc4, 0x41, 0x7e, 0x6f, 0x3c, 0x24, //0x0000193f vmovdqu      (%r12), %ymm15
	0xc4, 0x41, 0x7e, 0x6f, 0x74, 0x24, 0x20, //0x00001945 vmovdqu      $32(%r12), %ymm14
	0xc5, 0x85, 0x74, 0xf2, //0x0000194c vpcmpeqb     %ymm2, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xce, //0x00001950 vpmovmskb    %ymm6, %ecx
	0xc5, 0x8d, 0x74, 0xf2, //0x00001954 vpcmpeqb     %ymm2, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00001958 vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x0000195c shlq         $32, %rax
	0x48, 0x09, 0xc1, //0x00001960 orq          %rax, %rcx
	0x48, 0x89, 0xc8, //0x00001963 movq         %rcx, %rax
	0x4c, 0x09, 0xd8, //0x00001966 orq          %r11, %rax
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00001969 jne          LBB0_266
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000196f movq         $-1, %rcx
	0x45, 0x31, 0xdb, //0x00001976 xorl         %r11d, %r11d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00001979 jmp          LBB0_267
	//0x0000197e LBB0_266
	0x4c, 0x89, 0xd8, //0x0000197e movq         %r11, %rax
	0x48, 0xf7, 0xd0, //0x00001981 notq         %rax
	0x48, 0x21, 0xc8, //0x00001984 andq         %rcx, %rax
	0x48, 0x8d, 0x34, 0x00, //0x00001987 leaq         (%rax,%rax), %rsi
	0x4c, 0x09, 0xde, //0x0000198b orq          %r11, %rsi
	0x48, 0x89, 0xf7, //0x0000198e movq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x00001991 notq         %rdi
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001994 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xd9, //0x0000199e andq         %rbx, %rcx
	0x48, 0x21, 0xf9, //0x000019a1 andq         %rdi, %rcx
	0x45, 0x31, 0xdb, //0x000019a4 xorl         %r11d, %r11d
	0x48, 0x01, 0xc1, //0x000019a7 addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc3, //0x000019aa setb         %r11b
	0x48, 0x01, 0xc9, //0x000019ae addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000019b1 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x000019bb xorq         %rax, %rcx
	0x48, 0x21, 0xf1, //0x000019be andq         %rsi, %rcx
	0x48, 0xf7, 0xd1, //0x000019c1 notq         %rcx
	//0x000019c4 LBB0_267
	0xc5, 0x8d, 0x74, 0xf1, //0x000019c4 vpcmpeqb     %ymm1, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x000019c8 vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x000019cc shlq         $32, %rax
	0xc5, 0x85, 0x74, 0xf1, //0x000019d0 vpcmpeqb     %ymm1, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x000019d4 vpmovmskb    %ymm6, %esi
	0x48, 0x09, 0xc6, //0x000019d8 orq          %rax, %rsi
	0x48, 0x21, 0xce, //0x000019db andq         %rcx, %rsi
	0xc4, 0xe1, 0xf9, 0x6e, 0xf6, //0x000019de vmovq        %rsi, %xmm6
	0xc4, 0xc3, 0x49, 0x44, 0xf0, 0x00, //0x000019e3 vpclmulqdq   $0, %xmm8, %xmm6, %xmm6
	0xc4, 0xe1, 0xf9, 0x7e, 0xf1, //0x000019e9 vmovq        %xmm6, %rcx
	0x4c, 0x31, 0xd1, //0x000019ee xorq         %r10, %rcx
	0xc4, 0xc1, 0x05, 0x74, 0xf4, //0x000019f1 vpcmpeqb     %ymm12, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x000019f6 vpmovmskb    %ymm6, %esi
	0xc4, 0xc1, 0x0d, 0x74, 0xf4, //0x000019fa vpcmpeqb     %ymm12, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x000019ff vpmovmskb    %ymm6, %eax
	0x48, 0xc1, 0xe0, 0x20, //0x00001a03 shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00001a07 orq          %rax, %rsi
	0x48, 0x89, 0xc8, //0x00001a0a movq         %rcx, %rax
	0x48, 0xf7, 0xd0, //0x00001a0d notq         %rax
	0x48, 0x21, 0xc6, //0x00001a10 andq         %rax, %rsi
	0xc4, 0xc1, 0x05, 0x74, 0xf5, //0x00001a13 vpcmpeqb     %ymm13, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x00001a18 vpmovmskb    %ymm6, %edi
	0xc4, 0xc1, 0x0d, 0x74, 0xf5, //0x00001a1c vpcmpeqb     %ymm13, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x00001a21 vpmovmskb    %ymm6, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x00001a25 shlq         $32, %rbx
	0x48, 0x09, 0xdf, //0x00001a29 orq          %rbx, %rdi
	0x48, 0x21, 0xc7, //0x00001a2c andq         %rax, %rdi
	0x0f, 0x84, 0xe9, 0xfe, 0xff, 0xff, //0x00001a2f je           LBB0_262
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00001a35 movq         $24(%rsp), %r10
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001a3a .p2align 4, 0x90
	//0x00001a40 LBB0_269
	0x48, 0x8d, 0x5f, 0xff, //0x00001a40 leaq         $-1(%rdi), %rbx
	0x48, 0x89, 0xd8, //0x00001a44 movq         %rbx, %rax
	0x48, 0x21, 0xf0, //0x00001a47 andq         %rsi, %rax
	0xf3, 0x48, 0x0f, 0xb8, 0xc0, //0x00001a4a popcntq      %rax, %rax
	0x4c, 0x01, 0xf0, //0x00001a4f addq         %r14, %rax
	0x48, 0x39, 0xd0, //0x00001a52 cmpq         %rdx, %rax
	0x0f, 0x86, 0x3a, 0x01, 0x00, 0x00, //0x00001a55 jbe          LBB0_287
	0x48, 0x83, 0xc2, 0x01, //0x00001a5b addq         $1, %rdx
	0x48, 0x21, 0xdf, //0x00001a5f andq         %rbx, %rdi
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00001a62 jne          LBB0_269
	0xe9, 0xb1, 0xfe, 0xff, 0xff, //0x00001a68 jmp          LBB0_262
	//0x00001a6d LBB0_271
	0x4d, 0x85, 0xc0, //0x00001a6d testq        %r8, %r8
	0x0f, 0x8e, 0xd4, 0x03, 0x00, 0x00, //0x00001a70 jle          LBB0_382
	0xc5, 0xc9, 0xef, 0xf6, //0x00001a76 vpxor        %xmm6, %xmm6, %xmm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x60, //0x00001a7a vmovdqu      %ymm6, $96(%rsp)
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x00001a80 vmovdqu      %ymm6, $64(%rsp)
	0x44, 0x89, 0xe0, //0x00001a86 movl         %r12d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00001a89 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00001a8e cmpl         $4033, %eax
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x00001a93 jb           LBB0_275
	0x49, 0x83, 0xf8, 0x20, //0x00001a99 cmpq         $32, %r8
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x00001a9d jb           LBB0_276
	0xc4, 0xc1, 0x7e, 0x6f, 0x34, 0x24, //0x00001aa3 vmovdqu      (%r12), %ymm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x00001aa9 vmovdqu      %ymm6, $64(%rsp)
	0x49, 0x83, 0xc4, 0x20, //0x00001aaf addq         $32, %r12
	0x49, 0x8d, 0x48, 0xe0, //0x00001ab3 leaq         $-32(%r8), %rcx
	0x4c, 0x8d, 0x4c, 0x24, 0x60, //0x00001ab7 leaq         $96(%rsp), %r9
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00001abc jmp          LBB0_277
	//0x00001ac1 LBB0_275
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00001ac1 movq         $16(%rsp), %r13
	0xe9, 0x74, 0xfe, 0xff, 0xff, //0x00001ac6 jmp          LBB0_264
	//0x00001acb LBB0_276
	0x4c, 0x8d, 0x4c, 0x24, 0x40, //0x00001acb leaq         $64(%rsp), %r9
	0x4c, 0x89, 0xc1, //0x00001ad0 movq         %r8, %rcx
	//0x00001ad3 LBB0_277
	0x48, 0x83, 0xf9, 0x10, //0x00001ad3 cmpq         $16, %rcx
	0x0f, 0x82, 0x4d, 0x00, 0x00, 0x00, //0x00001ad7 jb           LBB0_278
	0xc4, 0xc1, 0x7a, 0x6f, 0x34, 0x24, //0x00001add vmovdqu      (%r12), %xmm6
	0xc4, 0xc1, 0x7a, 0x7f, 0x31, //0x00001ae3 vmovdqu      %xmm6, (%r9)
	0x49, 0x83, 0xc4, 0x10, //0x00001ae8 addq         $16, %r12
	0x49, 0x83, 0xc1, 0x10, //0x00001aec addq         $16, %r9
	0x48, 0x83, 0xc1, 0xf0, //0x00001af0 addq         $-16, %rcx
	0x48, 0x83, 0xf9, 0x08, //0x00001af4 cmpq         $8, %rcx
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x00001af8 jae          LBB0_285
	//0x00001afe LBB0_279
	0x48, 0x83, 0xf9, 0x04, //0x00001afe cmpq         $4, %rcx
	0x0f, 0x82, 0x49, 0x00, 0x00, 0x00, //0x00001b02 jb           LBB0_280
	//0x00001b08 LBB0_286
	0x41, 0x8b, 0x04, 0x24, //0x00001b08 movl         (%r12), %eax
	0x41, 0x89, 0x01, //0x00001b0c movl         %eax, (%r9)
	0x49, 0x83, 0xc4, 0x04, //0x00001b0f addq         $4, %r12
	0x49, 0x83, 0xc1, 0x04, //0x00001b13 addq         $4, %r9
	0x48, 0x83, 0xc1, 0xfc, //0x00001b17 addq         $-4, %rcx
	0x48, 0x83, 0xf9, 0x02, //0x00001b1b cmpq         $2, %rcx
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x00001b1f jae          LBB0_281
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00001b25 jmp          LBB0_282
	//0x00001b2a LBB0_278
	0x48, 0x83, 0xf9, 0x08, //0x00001b2a cmpq         $8, %rcx
	0x0f, 0x82, 0xca, 0xff, 0xff, 0xff, //0x00001b2e jb           LBB0_279
	//0x00001b34 LBB0_285
	0x49, 0x8b, 0x04, 0x24, //0x00001b34 movq         (%r12), %rax
	0x49, 0x89, 0x01, //0x00001b38 movq         %rax, (%r9)
	0x49, 0x83, 0xc4, 0x08, //0x00001b3b addq         $8, %r12
	0x49, 0x83, 0xc1, 0x08, //0x00001b3f addq         $8, %r9
	0x48, 0x83, 0xc1, 0xf8, //0x00001b43 addq         $-8, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00001b47 cmpq         $4, %rcx
	0x0f, 0x83, 0xb7, 0xff, 0xff, 0xff, //0x00001b4b jae          LBB0_286
	//0x00001b51 LBB0_280
	0x48, 0x83, 0xf9, 0x02, //0x00001b51 cmpq         $2, %rcx
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x00001b55 jb           LBB0_282
	//0x00001b5b LBB0_281
	0x41, 0x0f, 0xb7, 0x04, 0x24, //0x00001b5b movzwl       (%r12), %eax
	0x66, 0x41, 0x89, 0x01, //0x00001b60 movw         %ax, (%r9)
	0x49, 0x83, 0xc4, 0x02, //0x00001b64 addq         $2, %r12
	0x49, 0x83, 0xc1, 0x02, //0x00001b68 addq         $2, %r9
	0x48, 0x83, 0xc1, 0xfe, //0x00001b6c addq         $-2, %rcx
	//0x00001b70 LBB0_282
	0x4c, 0x89, 0xe6, //0x00001b70 movq         %r12, %rsi
	0x4c, 0x8d, 0x64, 0x24, 0x40, //0x00001b73 leaq         $64(%rsp), %r12
	0x48, 0x85, 0xc9, //0x00001b78 testq        %rcx, %rcx
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00001b7b movq         $16(%rsp), %r13
	0x0f, 0x84, 0xb9, 0xfd, 0xff, 0xff, //0x00001b80 je           LBB0_264
	0x8a, 0x06, //0x00001b86 movb         (%rsi), %al
	0x41, 0x88, 0x01, //0x00001b88 movb         %al, (%r9)
	0x4c, 0x8d, 0x64, 0x24, 0x40, //0x00001b8b leaq         $64(%rsp), %r12
	0xe9, 0xaa, 0xfd, 0xff, 0xff, //0x00001b90 jmp          LBB0_264
	//0x00001b95 LBB0_287
	0x48, 0x8b, 0x54, 0x24, 0x28, //0x00001b95 movq         $40(%rsp), %rdx
	0x48, 0x8b, 0x02, //0x00001b9a movq         (%rdx), %rax
	0x48, 0x0f, 0xbc, 0xcf, //0x00001b9d bsfq         %rdi, %rcx
	0x4c, 0x29, 0xc1, //0x00001ba1 subq         %r8, %rcx
	0x49, 0x89, 0xd0, //0x00001ba4 movq         %rdx, %r8
	0x4c, 0x8d, 0x34, 0x01, //0x00001ba7 leaq         (%rcx,%rax), %r14
	0x49, 0x83, 0xc6, 0x01, //0x00001bab addq         $1, %r14
	0x4d, 0x89, 0x75, 0x00, //0x00001baf movq         %r14, (%r13)
	0x48, 0x8b, 0x02, //0x00001bb3 movq         (%rdx), %rax
	0x49, 0x39, 0xc6, //0x00001bb6 cmpq         %rax, %r14
	0x4c, 0x0f, 0x47, 0xf0, //0x00001bb9 cmovaq       %rax, %r14
	0x4d, 0x89, 0x75, 0x00, //0x00001bbd movq         %r14, (%r13)
	0xe9, 0xe1, 0xf8, 0xff, 0xff, //0x00001bc1 jmp          LBB0_203
	//0x00001bc6 LBB0_288
	0x41, 0x0f, 0xbc, 0xca, //0x00001bc6 bsfl         %r10d, %ecx
	0x48, 0x01, 0xc8, //0x00001bca addq         %rcx, %rax
	0x4d, 0x8d, 0x34, 0x07, //0x00001bcd leaq         (%r15,%rax), %r14
	0x49, 0x83, 0xc6, 0x02, //0x00001bd1 addq         $2, %r14
	//0x00001bd5 LBB0_289
	0x4d, 0x89, 0x75, 0x00, //0x00001bd5 movq         %r14, (%r13)
	//0x00001bd9 LBB0_290
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00001bd9 movq         $24(%rsp), %r10
	0x4c, 0x8b, 0x44, 0x24, 0x28, //0x00001bde movq         $40(%rsp), %r8
	0xe9, 0xbf, 0xf8, 0xff, 0xff, //0x00001be3 jmp          LBB0_203
	//0x00001be8 LBB0_296
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00001be8 movq         $-2, %rcx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00001bef movl         $2, %esi
	0x48, 0x01, 0xf0, //0x00001bf4 addq         %rsi, %rax
	0x49, 0x01, 0xcf, //0x00001bf7 addq         %rcx, %r15
	0x0f, 0x8e, 0xa7, 0xf8, 0xff, 0xff, //0x00001bfa jle          LBB0_203
	//0x00001c00 LBB0_297
	0x0f, 0xb6, 0x08, //0x00001c00 movzbl       (%rax), %ecx
	0x80, 0xf9, 0x5c, //0x00001c03 cmpb         $92, %cl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x00001c06 je           LBB0_296
	0x80, 0xf9, 0x22, //0x00001c0c cmpb         $34, %cl
	0x0f, 0x84, 0x7c, 0x00, 0x00, 0x00, //0x00001c0f je           LBB0_354
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00001c15 movq         $-1, %rcx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00001c1c movl         $1, %esi
	0x48, 0x01, 0xf0, //0x00001c21 addq         %rsi, %rax
	0x49, 0x01, 0xcf, //0x00001c24 addq         %rcx, %r15
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x00001c27 jg           LBB0_297
	0xe9, 0x75, 0xf8, 0xff, 0xff, //0x00001c2d jmp          LBB0_203
	//0x00001c32 LBB0_301
	0x66, 0x0f, 0xbc, 0xca, //0x00001c32 bsfw         %dx, %cx
	0x44, 0x0f, 0xb7, 0xf1, //0x00001c36 movzwl       %cx, %r14d
	0xe9, 0x55, 0xf7, 0xff, 0xff, //0x00001c3a jmp          LBB0_176
	//0x00001c3f LBB0_303
	0x4c, 0x29, 0xe2, //0x00001c3f subq         %r12, %rdx
	0x48, 0x01, 0xf2, //0x00001c42 addq         %rsi, %rdx
	0x4c, 0x8b, 0x44, 0x24, 0x28, //0x00001c45 movq         $40(%rsp), %r8
	0x48, 0x39, 0xda, //0x00001c4a cmpq         %rbx, %rdx
	0x0f, 0x82, 0x56, 0xf4, 0xff, 0xff, //0x00001c4d jb           LBB0_125
	0xe9, 0x19, 0x2f, 0x00, 0x00, //0x00001c53 jmp          LBB0_1023
	//0x00001c58 LBB0_304
	0x4c, 0x29, 0xe2, //0x00001c58 subq         %r12, %rdx
	0x48, 0x01, 0xf2, //0x00001c5b addq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x00001c5e cmpq         %rax, %rdx
	0x0f, 0x82, 0xf2, 0xf9, 0xff, 0xff, //0x00001c61 jb           LBB0_233
	0xe9, 0x05, 0x2f, 0x00, 0x00, //0x00001c67 jmp          LBB0_1023
	//0x00001c6c LBB0_305
	0x4c, 0x29, 0xe0, //0x00001c6c subq         %r12, %rax
	0x48, 0x01, 0xd0, //0x00001c6f addq         %rdx, %rax
	0x48, 0x39, 0xd8, //0x00001c72 cmpq         %rbx, %rax
	0x0f, 0x82, 0xee, 0xf5, 0xff, 0xff, //0x00001c75 jb           LBB0_157
	0xe9, 0x27, 0xf8, 0xff, 0xff, //0x00001c7b jmp          LBB0_203
	//0x00001c80 LBB0_353
	0x4c, 0x01, 0xe2, //0x00001c80 addq         %r12, %rdx
	0x48, 0x85, 0xc9, //0x00001c83 testq        %rcx, %rcx
	0x0f, 0x85, 0xaa, 0xf3, 0xff, 0xff, //0x00001c86 jne          LBB0_118
	0xe9, 0xe5, 0xf3, 0xff, 0xff, //0x00001c8c jmp          LBB0_123
	//0x00001c91 LBB0_354
	0x4c, 0x29, 0xe0, //0x00001c91 subq         %r12, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00001c94 addq         $1, %rax
	0xe9, 0x03, 0xf8, 0xff, 0xff, //0x00001c98 jmp          LBB0_202
	//0x00001c9d LBB0_355
	0x4c, 0x01, 0xe2, //0x00001c9d addq         %r12, %rdx
	0x48, 0x85, 0xc9, //0x00001ca0 testq        %rcx, %rcx
	0x0f, 0x85, 0x4d, 0xf9, 0xff, 0xff, //0x00001ca3 jne          LBB0_226
	0xe9, 0x7f, 0xf9, 0xff, 0xff, //0x00001ca9 jmp          LBB0_231
	//0x00001cae LBB0_356
	0x4c, 0x01, 0xe0, //0x00001cae addq         %r12, %rax
	0x48, 0x85, 0xc9, //0x00001cb1 testq        %rcx, %rcx
	0x0f, 0x85, 0x4c, 0xf5, 0xff, 0xff, //0x00001cb4 jne          LBB0_150
	0xe9, 0x7e, 0xf5, 0xff, 0xff, //0x00001cba jmp          LBB0_155
	//0x00001cbf LBB0_357
	0x4d, 0x01, 0xe6, //0x00001cbf addq         %r12, %r14
	0x48, 0x83, 0xf9, 0x10, //0x00001cc2 cmpq         $16, %rcx
	0x0f, 0x83, 0x36, 0xf6, 0xff, 0xff, //0x00001cc6 jae          LBB0_164
	0xe9, 0x72, 0xf6, 0xff, 0xff, //0x00001ccc jmp          LBB0_167
	//0x00001cd1 LBB0_360
	0x45, 0x31, 0xc9, //0x00001cd1 xorl         %r9d, %r9d
	0xe9, 0x31, 0xf2, 0xff, 0xff, //0x00001cd4 jmp          LBB0_339
	//0x00001cd9 LBB0_358
	0x4d, 0x29, 0xe6, //0x00001cd9 subq         %r12, %r14
	0x49, 0x01, 0xc6, //0x00001cdc addq         %rax, %r14
	//0x00001cdf LBB0_359
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00001cdf movq         $16(%rsp), %r13
	0xe9, 0xec, 0xfe, 0xff, 0xff, //0x00001ce4 jmp          LBB0_289
	//0x00001ce9 LBB0_361
	0x4b, 0x8d, 0x04, 0x34, //0x00001ce9 leaq         (%r12,%r14), %rax
	0xe9, 0x89, 0xf7, 0xff, 0xff, //0x00001ced jmp          LBB0_199
	//0x00001cf2 LBB0_362
	0x48, 0x89, 0x84, 0x24, 0x98, 0x00, 0x00, 0x00, //0x00001cf2 movq         %rax, $152(%rsp)
	0x48, 0x83, 0xf8, 0xff, //0x00001cfa cmpq         $-1, %rax
	0x0f, 0x85, 0x15, 0x00, 0x00, 0x00, //0x00001cfe jne          LBB0_365
	0x4c, 0x89, 0xf7, //0x00001d04 movq         %r14, %rdi
	0x4c, 0x29, 0xe7, //0x00001d07 subq         %r12, %rdi
	0x48, 0x0f, 0xbc, 0xc6, //0x00001d0a bsfq         %rsi, %rax
	0x48, 0x01, 0xf8, //0x00001d0e addq         %rdi, %rax
	//0x00001d11 LBB0_364
	0x48, 0x89, 0x84, 0x24, 0x98, 0x00, 0x00, 0x00, //0x00001d11 movq         %rax, $152(%rsp)
	//0x00001d19 LBB0_365
	0x41, 0x89, 0xd1, //0x00001d19 movl         %edx, %r9d
	0x41, 0xf7, 0xd1, //0x00001d1c notl         %r9d
	0x41, 0x21, 0xf1, //0x00001d1f andl         %esi, %r9d
	0x43, 0x8d, 0x3c, 0x09, //0x00001d22 leal         (%r9,%r9), %edi
	0x46, 0x8d, 0x3c, 0x4a, //0x00001d26 leal         (%rdx,%r9,2), %r15d
	0xf7, 0xd7, //0x00001d2a notl         %edi
	0x21, 0xf7, //0x00001d2c andl         %esi, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001d2e andl         $-1431655766, %edi
	0x31, 0xd2, //0x00001d34 xorl         %edx, %edx
	0x44, 0x01, 0xcf, //0x00001d36 addl         %r9d, %edi
	0x0f, 0x92, 0xc2, //0x00001d39 setb         %dl
	0x01, 0xff, //0x00001d3c addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00001d3e xorl         $1431655765, %edi
	0x44, 0x21, 0xff, //0x00001d44 andl         %r15d, %edi
	0xf7, 0xd7, //0x00001d47 notl         %edi
	0x21, 0xf9, //0x00001d49 andl         %edi, %ecx
	0x48, 0x8b, 0x84, 0x24, 0x98, 0x00, 0x00, 0x00, //0x00001d4b movq         $152(%rsp), %rax
	0x48, 0x85, 0xc9, //0x00001d53 testq        %rcx, %rcx
	0x0f, 0x85, 0xc2, 0xed, 0xff, 0xff, //0x00001d56 jne          LBB0_80
	//0x00001d5c LBB0_366
	0x49, 0x83, 0xc6, 0x20, //0x00001d5c addq         $32, %r14
	0x49, 0x83, 0xc5, 0xe0, //0x00001d60 addq         $-32, %r13
	//0x00001d64 LBB0_367
	0x48, 0x85, 0xd2, //0x00001d64 testq        %rdx, %rdx
	0x0f, 0x85, 0xa3, 0x00, 0x00, 0x00, //0x00001d67 jne          LBB0_380
	0x48, 0x89, 0xc7, //0x00001d6d movq         %rax, %rdi
	0x4d, 0x85, 0xed, //0x00001d70 testq        %r13, %r13
	0x0f, 0x84, 0x7e, 0x00, 0x00, 0x00, //0x00001d73 je           LBB0_379
	//0x00001d79 LBB0_369
	0x4c, 0x89, 0xe2, //0x00001d79 movq         %r12, %rdx
	0x48, 0xf7, 0xda, //0x00001d7c negq         %rdx
	//0x00001d7f LBB0_370
	0x31, 0xf6, //0x00001d7f xorl         %esi, %esi
	//0x00001d81 LBB0_371
	0x41, 0x0f, 0xb6, 0x0c, 0x36, //0x00001d81 movzbl       (%r14,%rsi), %ecx
	0x80, 0xf9, 0x22, //0x00001d86 cmpb         $34, %cl
	0x0f, 0x84, 0x61, 0x00, 0x00, 0x00, //0x00001d89 je           LBB0_378
	0x80, 0xf9, 0x5c, //0x00001d8f cmpb         $92, %cl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00001d92 je           LBB0_376
	0x48, 0x83, 0xc6, 0x01, //0x00001d98 addq         $1, %rsi
	0x49, 0x39, 0xf5, //0x00001d9c cmpq         %rsi, %r13
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00001d9f jne          LBB0_371
	0xe9, 0x55, 0x00, 0x00, 0x00, //0x00001da5 jmp          LBB0_374
	//0x00001daa LBB0_376
	0x49, 0x8d, 0x4d, 0xff, //0x00001daa leaq         $-1(%r13), %rcx
	0x48, 0x39, 0xf1, //0x00001dae cmpq         %rsi, %rcx
	0x0f, 0x84, 0x39, 0x35, 0x00, 0x00, //0x00001db1 je           LBB0_1102
	0x4a, 0x8d, 0x0c, 0x32, //0x00001db7 leaq         (%rdx,%r14), %rcx
	0x48, 0x01, 0xf1, //0x00001dbb addq         %rsi, %rcx
	0x48, 0x83, 0xff, 0xff, //0x00001dbe cmpq         $-1, %rdi
	0x48, 0x0f, 0x44, 0xc1, //0x00001dc2 cmoveq       %rcx, %rax
	0x48, 0x0f, 0x44, 0xf9, //0x00001dc6 cmoveq       %rcx, %rdi
	0x49, 0x01, 0xf6, //0x00001dca addq         %rsi, %r14
	0x49, 0x83, 0xc6, 0x02, //0x00001dcd addq         $2, %r14
	0x4c, 0x89, 0xe9, //0x00001dd1 movq         %r13, %rcx
	0x48, 0x29, 0xf1, //0x00001dd4 subq         %rsi, %rcx
	0x48, 0x83, 0xc1, 0xfe, //0x00001dd7 addq         $-2, %rcx
	0x49, 0x83, 0xc5, 0xfe, //0x00001ddb addq         $-2, %r13
	0x49, 0x39, 0xf5, //0x00001ddf cmpq         %rsi, %r13
	0x49, 0x89, 0xcd, //0x00001de2 movq         %rcx, %r13
	0x0f, 0x85, 0x94, 0xff, 0xff, 0xff, //0x00001de5 jne          LBB0_370
	0xe9, 0x00, 0x35, 0x00, 0x00, //0x00001deb jmp          LBB0_1102
	//0x00001df0 LBB0_378
	0x49, 0x01, 0xf6, //0x00001df0 addq         %rsi, %r14
	0x49, 0x83, 0xc6, 0x01, //0x00001df3 addq         $1, %r14
	//0x00001df7 LBB0_379
	0x4d, 0x29, 0xe6, //0x00001df7 subq         %r12, %r14
	0xe9, 0x78, 0xea, 0xff, 0xff, //0x00001dfa jmp          LBB0_85
	//0x00001dff LBB0_374
	0x80, 0xf9, 0x22, //0x00001dff cmpb         $34, %cl
	0x0f, 0x85, 0xe8, 0x34, 0x00, 0x00, //0x00001e02 jne          LBB0_1102
	0x4d, 0x01, 0xee, //0x00001e08 addq         %r13, %r14
	0xe9, 0xe7, 0xff, 0xff, 0xff, //0x00001e0b jmp          LBB0_379
	//0x00001e10 LBB0_380
	0x4d, 0x85, 0xed, //0x00001e10 testq        %r13, %r13
	0x0f, 0x84, 0xd7, 0x34, 0x00, 0x00, //0x00001e13 je           LBB0_1102
	0x4c, 0x89, 0xe7, //0x00001e19 movq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x00001e1c notq         %rdi
	0x4c, 0x01, 0xf7, //0x00001e1f addq         %r14, %rdi
	0x48, 0x83, 0xf8, 0xff, //0x00001e22 cmpq         $-1, %rax
	0x48, 0x89, 0xc1, //0x00001e26 movq         %rax, %rcx
	0x48, 0x0f, 0x44, 0xcf, //0x00001e29 cmoveq       %rdi, %rcx
	0x48, 0x0f, 0x45, 0xf8, //0x00001e2d cmovneq      %rax, %rdi
	0x49, 0x83, 0xc6, 0x01, //0x00001e31 addq         $1, %r14
	0x49, 0x83, 0xc5, 0xff, //0x00001e35 addq         $-1, %r13
	0x48, 0x89, 0xc8, //0x00001e39 movq         %rcx, %rax
	0x4d, 0x85, 0xed, //0x00001e3c testq        %r13, %r13
	0x0f, 0x85, 0x34, 0xff, 0xff, 0xff, //0x00001e3f jne          LBB0_369
	0xe9, 0xad, 0xff, 0xff, 0xff, //0x00001e45 jmp          LBB0_379
	//0x00001e4a LBB0_382
	0x4c, 0x8b, 0x44, 0x24, 0x28, //0x00001e4a movq         $40(%rsp), %r8
	0x4d, 0x8b, 0x30, //0x00001e4f movq         (%r8), %r14
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00001e52 movq         $16(%rsp), %r13
	0x4d, 0x89, 0x75, 0x00, //0x00001e57 movq         %r14, (%r13)
	//0x00001e5b LBB0_383
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00001e5b movq         $24(%rsp), %r10
	0xe9, 0x42, 0xf6, 0xff, 0xff, //0x00001e60 jmp          LBB0_203
	//0x00001e65 LBB0_384
	0x49, 0x8d, 0x40, 0xff, //0x00001e65 leaq         $-1(%r8), %rax
	0x4c, 0x39, 0xf8, //0x00001e69 cmpq         %r15, %rax
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x00001e6c jne          LBB0_386
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00001e72 movq         $16(%rsp), %r13
	0xe9, 0x5d, 0xfd, 0xff, 0xff, //0x00001e77 jmp          LBB0_290
	//0x00001e7c LBB0_386
	0x4b, 0x8d, 0x04, 0x0f, //0x00001e7c leaq         (%r15,%r9), %rax
	0x48, 0x83, 0xc0, 0x02, //0x00001e80 addq         $2, %rax
	0x4d, 0x29, 0xf8, //0x00001e84 subq         %r15, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00001e87 addq         $-2, %r8
	0x4d, 0x89, 0xc7, //0x00001e8b movq         %r8, %r15
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00001e8e movq         $16(%rsp), %r13
	0xe9, 0xe3, 0xf5, 0xff, 0xff, //0x00001e93 jmp          LBB0_199
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001e98 .p2align 4, 0x90
	//0x00001ea0 LBB0_387
	0x3c, 0x5b, //0x00001ea0 cmpb         $91, %al
	0x0f, 0x85, 0xc9, 0x2c, 0x00, 0x00, //0x00001ea2 jne          LBB0_1023
	0x48, 0x8b, 0x44, 0x24, 0x30, //0x00001ea8 movq         $48(%rsp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00001ead movq         $8(%rax), %rax
	0x4c, 0x8b, 0x38, //0x00001eb1 movq         (%rax), %r15
	0x4d, 0x85, 0xff, //0x00001eb4 testq        %r15, %r15
	0x0f, 0x88, 0xf2, 0x2a, 0x00, 0x00, //0x00001eb7 js           LBB0_996
	0x49, 0x8b, 0x00, //0x00001ebd movq         (%r8), %rax
	0x49, 0x39, 0xc6, //0x00001ec0 cmpq         %rax, %r14
	0x0f, 0x83, 0x37, 0x00, 0x00, 0x00, //0x00001ec3 jae          LBB0_394
	0x43, 0x8a, 0x0c, 0x34, //0x00001ec9 movb         (%r12,%r14), %cl
	0x80, 0xf9, 0x0d, //0x00001ecd cmpb         $13, %cl
	0x0f, 0x84, 0x2a, 0x00, 0x00, 0x00, //0x00001ed0 je           LBB0_394
	0x80, 0xf9, 0x20, //0x00001ed6 cmpb         $32, %cl
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x00001ed9 je           LBB0_394
	0x80, 0xc1, 0xf5, //0x00001edf addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00001ee2 cmpb         $-2, %cl
	0x0f, 0x83, 0x15, 0x00, 0x00, 0x00, //0x00001ee5 jae          LBB0_394
	0x4c, 0x89, 0xf2, //0x00001eeb movq         %r14, %rdx
	0xe9, 0x7e, 0x01, 0x00, 0x00, //0x00001eee jmp          LBB0_420
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001ef3 .p2align 4, 0x90
	//0x00001f00 LBB0_394
	0x49, 0x8d, 0x56, 0x01, //0x00001f00 leaq         $1(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00001f04 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001f07 jae          LBB0_398
	0x41, 0x8a, 0x0c, 0x14, //0x00001f0d movb         (%r12,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00001f11 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001f14 je           LBB0_398
	0x80, 0xf9, 0x20, //0x00001f1a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00001f1d je           LBB0_398
	0x80, 0xc1, 0xf5, //0x00001f23 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00001f26 cmpb         $-2, %cl
	0x0f, 0x82, 0x42, 0x01, 0x00, 0x00, //0x00001f29 jb           LBB0_420
	0x90, //0x00001f2f .p2align 4, 0x90
	//0x00001f30 LBB0_398
	0x49, 0x8d, 0x56, 0x02, //0x00001f30 leaq         $2(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00001f34 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001f37 jae          LBB0_402
	0x41, 0x8a, 0x0c, 0x14, //0x00001f3d movb         (%r12,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00001f41 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001f44 je           LBB0_402
	0x80, 0xf9, 0x20, //0x00001f4a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00001f4d je           LBB0_402
	0x80, 0xc1, 0xf5, //0x00001f53 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00001f56 cmpb         $-2, %cl
	0x0f, 0x82, 0x12, 0x01, 0x00, 0x00, //0x00001f59 jb           LBB0_420
	0x90, //0x00001f5f .p2align 4, 0x90
	//0x00001f60 LBB0_402
	0x49, 0x8d, 0x56, 0x03, //0x00001f60 leaq         $3(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00001f64 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00001f67 jae          LBB0_406
	0x41, 0x8a, 0x0c, 0x14, //0x00001f6d movb         (%r12,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00001f71 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001f74 je           LBB0_406
	0x80, 0xf9, 0x20, //0x00001f7a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00001f7d je           LBB0_406
	0x80, 0xc1, 0xf5, //0x00001f83 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00001f86 cmpb         $-2, %cl
	0x0f, 0x82, 0xe2, 0x00, 0x00, 0x00, //0x00001f89 jb           LBB0_420
	0x90, //0x00001f8f .p2align 4, 0x90
	//0x00001f90 LBB0_406
	0x49, 0x8d, 0x56, 0x04, //0x00001f90 leaq         $4(%r14), %rdx
	0x48, 0x89, 0xc1, //0x00001f94 movq         %rax, %rcx
	0x48, 0x29, 0xd1, //0x00001f97 subq         %rdx, %rcx
	0x0f, 0x86, 0xaf, 0x00, 0x00, 0x00, //0x00001f9a jbe          LBB0_418
	0x48, 0x83, 0xf9, 0x20, //0x00001fa0 cmpq         $32, %rcx
	0x0f, 0x82, 0x19, 0x0e, 0x00, 0x00, //0x00001fa4 jb           LBB0_598
	0x48, 0xc7, 0xc1, 0xfc, 0xff, 0xff, 0xff, //0x00001faa movq         $-4, %rcx
	0x4c, 0x29, 0xf1, //0x00001fb1 subq         %r14, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001fb4 .p2align 4, 0x90
	//0x00001fc0 LBB0_409
	0xc4, 0x41, 0x7e, 0x6f, 0x34, 0x14, //0x00001fc0 vmovdqu      (%r12,%rdx), %ymm14
	0xc4, 0x42, 0x7d, 0x00, 0xfe, //0x00001fc6 vpshufb      %ymm14, %ymm0, %ymm15
	0xc4, 0xc1, 0x0d, 0xf8, 0xf7, //0x00001fcb vpsubb       %ymm15, %ymm14, %ymm6
	0xc4, 0xe2, 0x7d, 0x17, 0xf6, //0x00001fd0 vptest       %ymm6, %ymm6
	0x0f, 0x85, 0x7c, 0x00, 0x00, 0x00, //0x00001fd5 jne          LBB0_419
	0x48, 0x83, 0xc2, 0x20, //0x00001fdb addq         $32, %rdx
	0x48, 0x8d, 0x34, 0x08, //0x00001fdf leaq         (%rax,%rcx), %rsi
	0x48, 0x83, 0xc6, 0xe0, //0x00001fe3 addq         $-32, %rsi
	0x48, 0x83, 0xc1, 0xe0, //0x00001fe7 addq         $-32, %rcx
	0x48, 0x83, 0xfe, 0x1f, //0x00001feb cmpq         $31, %rsi
	0x0f, 0x87, 0xcb, 0xff, 0xff, 0xff, //0x00001fef ja           LBB0_409
	0x4c, 0x89, 0xe2, //0x00001ff5 movq         %r12, %rdx
	0x48, 0x29, 0xca, //0x00001ff8 subq         %rcx, %rdx
	0x48, 0x01, 0xc1, //0x00001ffb addq         %rax, %rcx
	0x48, 0x85, 0xc9, //0x00001ffe testq        %rcx, %rcx
	0x0f, 0x84, 0x37, 0x00, 0x00, 0x00, //0x00002001 je           LBB0_417
	//0x00002007 LBB0_412
	0x4c, 0x8d, 0x0c, 0x0a, //0x00002007 leaq         (%rdx,%rcx), %r9
	0x31, 0xf6, //0x0000200b xorl         %esi, %esi
	//0x0000200d LBB0_413
	0x0f, 0xbe, 0x1c, 0x32, //0x0000200d movsbl       (%rdx,%rsi), %ebx
	0x83, 0xfb, 0x20, //0x00002011 cmpl         $32, %ebx
	0x0f, 0x87, 0x81, 0x0d, 0x00, 0x00, //0x00002014 ja           LBB0_596
	0x48, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000201a movabsq      $4294977024, %rdi
	0x48, 0x0f, 0xa3, 0xdf, //0x00002024 btq          %rbx, %rdi
	0x0f, 0x83, 0x6d, 0x0d, 0x00, 0x00, //0x00002028 jae          LBB0_596
	0x48, 0x83, 0xc6, 0x01, //0x0000202e addq         $1, %rsi
	0x48, 0x39, 0xf1, //0x00002032 cmpq         %rsi, %rcx
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x00002035 jne          LBB0_413
	0x4c, 0x89, 0xca, //0x0000203b movq         %r9, %rdx
	//0x0000203e LBB0_417
	0x4c, 0x29, 0xe2, //0x0000203e subq         %r12, %rdx
	0x48, 0x39, 0xc2, //0x00002041 cmpq         %rax, %rdx
	0x0f, 0x82, 0x27, 0x00, 0x00, 0x00, //0x00002044 jb           LBB0_420
	0xe9, 0x35, 0x00, 0x00, 0x00, //0x0000204a jmp          LBB0_421
	//0x0000204f LBB0_418
	0x49, 0x89, 0xd6, //0x0000204f movq         %rdx, %r14
	0xe9, 0x2d, 0x00, 0x00, 0x00, //0x00002052 jmp          LBB0_421
	//0x00002057 LBB0_419
	0xc4, 0xc1, 0x0d, 0x74, 0xf7, //0x00002057 vpcmpeqb     %ymm15, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xd6, //0x0000205c vpmovmskb    %ymm6, %edx
	0xf7, 0xd2, //0x00002060 notl         %edx
	0x0f, 0xbc, 0xd2, //0x00002062 bsfl         %edx, %edx
	0x48, 0x29, 0xca, //0x00002065 subq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x00002068 cmpq         %rax, %rdx
	0x0f, 0x83, 0x13, 0x00, 0x00, 0x00, //0x0000206b jae          LBB0_421
	//0x00002071 LBB0_420
	0x4c, 0x8d, 0x72, 0x01, //0x00002071 leaq         $1(%rdx), %r14
	0x4d, 0x89, 0x75, 0x00, //0x00002075 movq         %r14, (%r13)
	0x41, 0x80, 0x3c, 0x14, 0x5d, //0x00002079 cmpb         $93, (%r12,%rdx)
	0x0f, 0x84, 0x89, 0x0f, 0x00, 0x00, //0x0000207e je           LBB0_629
	//0x00002084 LBB0_421
	0x49, 0x83, 0xc6, 0xff, //0x00002084 addq         $-1, %r14
	0x4d, 0x89, 0x75, 0x00, //0x00002088 movq         %r14, (%r13)
	0x4d, 0x85, 0xff, //0x0000208c testq        %r15, %r15
	0x0f, 0x8e, 0xcb, 0x0c, 0x00, 0x00, //0x0000208f jle          LBB0_519
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002095 .p2align 4, 0x90
	//0x000020a0 LBB0_422
	0x49, 0x8b, 0x10, //0x000020a0 movq         (%r8), %rdx
	0x49, 0x39, 0xd6, //0x000020a3 cmpq         %rdx, %r14
	0x0f, 0x83, 0x34, 0x00, 0x00, 0x00, //0x000020a6 jae          LBB0_427
	0x43, 0x8a, 0x04, 0x34, //0x000020ac movb         (%r12,%r14), %al
	0x3c, 0x0d, //0x000020b0 cmpb         $13, %al
	0x0f, 0x84, 0x28, 0x00, 0x00, 0x00, //0x000020b2 je           LBB0_427
	0x3c, 0x20, //0x000020b8 cmpb         $32, %al
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x000020ba je           LBB0_427
	0x04, 0xf5, //0x000020c0 addb         $-11, %al
	0x3c, 0xfe, //0x000020c2 cmpb         $-2, %al
	0x0f, 0x83, 0x16, 0x00, 0x00, 0x00, //0x000020c4 jae          LBB0_427
	0x4c, 0x89, 0xf0, //0x000020ca movq         %r14, %rax
	0xe9, 0x78, 0x01, 0x00, 0x00, //0x000020cd jmp          LBB0_452
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000020d2 .p2align 4, 0x90
	//0x000020e0 LBB0_427
	0x49, 0x8d, 0x46, 0x01, //0x000020e0 leaq         $1(%r14), %rax
	0x48, 0x39, 0xd0, //0x000020e4 cmpq         %rdx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000020e7 jae          LBB0_431
	0x41, 0x8a, 0x0c, 0x04, //0x000020ed movb         (%r12,%rax), %cl
	0x80, 0xf9, 0x0d, //0x000020f1 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000020f4 je           LBB0_431
	0x80, 0xf9, 0x20, //0x000020fa cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x000020fd je           LBB0_431
	0x80, 0xc1, 0xf5, //0x00002103 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00002106 cmpb         $-2, %cl
	0x0f, 0x82, 0x3b, 0x01, 0x00, 0x00, //0x00002109 jb           LBB0_452
	0x90, //0x0000210f .p2align 4, 0x90
	//0x00002110 LBB0_431
	0x49, 0x8d, 0x46, 0x02, //0x00002110 leaq         $2(%r14), %rax
	0x48, 0x39, 0xd0, //0x00002114 cmpq         %rdx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002117 jae          LBB0_435
	0x41, 0x8a, 0x0c, 0x04, //0x0000211d movb         (%r12,%rax), %cl
	0x80, 0xf9, 0x0d, //0x00002121 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002124 je           LBB0_435
	0x80, 0xf9, 0x20, //0x0000212a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000212d je           LBB0_435
	0x80, 0xc1, 0xf5, //0x00002133 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00002136 cmpb         $-2, %cl
	0x0f, 0x82, 0x0b, 0x01, 0x00, 0x00, //0x00002139 jb           LBB0_452
	0x90, //0x0000213f .p2align 4, 0x90
	//0x00002140 LBB0_435
	0x49, 0x8d, 0x46, 0x03, //0x00002140 leaq         $3(%r14), %rax
	0x48, 0x39, 0xd0, //0x00002144 cmpq         %rdx, %rax
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002147 jae          LBB0_439
	0x41, 0x8a, 0x0c, 0x04, //0x0000214d movb         (%r12,%rax), %cl
	0x80, 0xf9, 0x0d, //0x00002151 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002154 je           LBB0_439
	0x80, 0xf9, 0x20, //0x0000215a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000215d je           LBB0_439
	0x80, 0xc1, 0xf5, //0x00002163 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00002166 cmpb         $-2, %cl
	0x0f, 0x82, 0xdb, 0x00, 0x00, 0x00, //0x00002169 jb           LBB0_452
	0x90, //0x0000216f .p2align 4, 0x90
	//0x00002170 LBB0_439
	0x49, 0x8d, 0x46, 0x04, //0x00002170 leaq         $4(%r14), %rax
	0x48, 0x89, 0xd1, //0x00002174 movq         %rdx, %rcx
	0x48, 0x29, 0xc1, //0x00002177 subq         %rax, %rcx
	0x0f, 0x86, 0x30, 0x03, 0x00, 0x00, //0x0000217a jbe          LBB0_486
	0x48, 0x83, 0xf9, 0x20, //0x00002180 cmpq         $32, %rcx
	0x0f, 0x82, 0x44, 0x0b, 0x00, 0x00, //0x00002184 jb           LBB0_585
	0x48, 0xc7, 0xc1, 0xfc, 0xff, 0xff, 0xff, //0x0000218a movq         $-4, %rcx
	0x4c, 0x29, 0xf1, //0x00002191 subq         %r14, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002194 .p2align 4, 0x90
	//0x000021a0 LBB0_442
	0xc4, 0x41, 0x7e, 0x6f, 0x34, 0x04, //0x000021a0 vmovdqu      (%r12,%rax), %ymm14
	0xc4, 0x42, 0x7d, 0x00, 0xfe, //0x000021a6 vpshufb      %ymm14, %ymm0, %ymm15
	0xc4, 0xc1, 0x0d, 0xf8, 0xf7, //0x000021ab vpsubb       %ymm15, %ymm14, %ymm6
	0xc4, 0xe2, 0x7d, 0x17, 0xf6, //0x000021b0 vptest       %ymm6, %ymm6
	0x0f, 0x85, 0x75, 0x00, 0x00, 0x00, //0x000021b5 jne          LBB0_451
	0x48, 0x83, 0xc0, 0x20, //0x000021bb addq         $32, %rax
	0x48, 0x8d, 0x34, 0x0a, //0x000021bf leaq         (%rdx,%rcx), %rsi
	0x48, 0x83, 0xc6, 0xe0, //0x000021c3 addq         $-32, %rsi
	0x48, 0x83, 0xc1, 0xe0, //0x000021c7 addq         $-32, %rcx
	0x48, 0x83, 0xfe, 0x1f, //0x000021cb cmpq         $31, %rsi
	0x0f, 0x87, 0xcb, 0xff, 0xff, 0xff, //0x000021cf ja           LBB0_442
	0x4c, 0x89, 0xe0, //0x000021d5 movq         %r12, %rax
	0x48, 0x29, 0xc8, //0x000021d8 subq         %rcx, %rax
	0x48, 0x01, 0xd1, //0x000021db addq         %rdx, %rcx
	0x48, 0x85, 0xc9, //0x000021de testq        %rcx, %rcx
	0x0f, 0x84, 0x37, 0x00, 0x00, 0x00, //0x000021e1 je           LBB0_450
	//0x000021e7 LBB0_445
	0x4c, 0x8d, 0x0c, 0x08, //0x000021e7 leaq         (%rax,%rcx), %r9
	0x31, 0xf6, //0x000021eb xorl         %esi, %esi
	//0x000021ed LBB0_446
	0x0f, 0xbe, 0x1c, 0x30, //0x000021ed movsbl       (%rax,%rsi), %ebx
	0x83, 0xfb, 0x20, //0x000021f1 cmpl         $32, %ebx
	0x0f, 0x87, 0x1b, 0x0a, 0x00, 0x00, //0x000021f4 ja           LBB0_582
	0x48, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000021fa movabsq      $4294977024, %rdi
	0x48, 0x0f, 0xa3, 0xdf, //0x00002204 btq          %rbx, %rdi
	0x0f, 0x83, 0x07, 0x0a, 0x00, 0x00, //0x00002208 jae          LBB0_582
	0x48, 0x83, 0xc6, 0x01, //0x0000220e addq         $1, %rsi
	0x48, 0x39, 0xf1, //0x00002212 cmpq         %rsi, %rcx
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x00002215 jne          LBB0_446
	0x4c, 0x89, 0xc8, //0x0000221b movq         %r9, %rax
	//0x0000221e LBB0_450
	0x4c, 0x29, 0xe0, //0x0000221e subq         %r12, %rax
	0x48, 0x39, 0xd0, //0x00002221 cmpq         %rdx, %rax
	0x0f, 0x82, 0x20, 0x00, 0x00, 0x00, //0x00002224 jb           LBB0_452
	0xe9, 0x88, 0x02, 0x00, 0x00, //0x0000222a jmp          LBB0_487
	0x90, //0x0000222f .p2align 4, 0x90
	//0x00002230 LBB0_451
	0xc4, 0xc1, 0x0d, 0x74, 0xf7, //0x00002230 vpcmpeqb     %ymm15, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xc6, //0x00002235 vpmovmskb    %ymm6, %eax
	0xf7, 0xd0, //0x00002239 notl         %eax
	0x0f, 0xbc, 0xc0, //0x0000223b bsfl         %eax, %eax
	0x48, 0x29, 0xc8, //0x0000223e subq         %rcx, %rax
	0x48, 0x39, 0xd0, //0x00002241 cmpq         %rdx, %rax
	0x0f, 0x83, 0x6d, 0x02, 0x00, 0x00, //0x00002244 jae          LBB0_487
	//0x0000224a LBB0_452
	0x4c, 0x8d, 0x70, 0x01, //0x0000224a leaq         $1(%rax), %r14
	0x4d, 0x89, 0x75, 0x00, //0x0000224e movq         %r14, (%r13)
	0x41, 0x0f, 0xbe, 0x0c, 0x04, //0x00002252 movsbl       (%r12,%rax), %ecx
	0x83, 0xf9, 0x7b, //0x00002257 cmpl         $123, %ecx
	0x0f, 0x87, 0x50, 0x02, 0x00, 0x00, //0x0000225a ja           LBB0_486
	0x48, 0x8d, 0x15, 0xd9, 0x32, 0x00, 0x00, //0x00002260 leaq         $13017(%rip), %rdx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8a, //0x00002267 movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x0000226b addq         %rdx, %rcx
	0xff, 0xe1, //0x0000226e jmpq         *%rcx
	//0x00002270 LBB0_454
	0x49, 0x8b, 0x10, //0x00002270 movq         (%r8), %rdx
	0x48, 0x89, 0xd1, //0x00002273 movq         %rdx, %rcx
	0x4c, 0x29, 0xf1, //0x00002276 subq         %r14, %rcx
	0x48, 0x83, 0xf9, 0x20, //0x00002279 cmpq         $32, %rcx
	0x0f, 0x82, 0x5c, 0x0a, 0x00, 0x00, //0x0000227d jb           LBB0_586
	0x48, 0xf7, 0xd0, //0x00002283 notq         %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002286 .p2align 4, 0x90
	//0x00002290 LBB0_456
	0xc4, 0x81, 0x7e, 0x6f, 0x34, 0x34, //0x00002290 vmovdqu      (%r12,%r14), %ymm6
	0xc5, 0xcd, 0x74, 0xfb, //0x00002296 vpcmpeqb     %ymm3, %ymm6, %ymm7
	0xc5, 0xcd, 0xdb, 0xf4, //0x0000229a vpand        %ymm4, %ymm6, %ymm6
	0xc5, 0xcd, 0x74, 0xf5, //0x0000229e vpcmpeqb     %ymm5, %ymm6, %ymm6
	0xc5, 0xcd, 0xeb, 0xf7, //0x000022a2 vpor         %ymm7, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xce, //0x000022a6 vpmovmskb    %ymm6, %ecx
	0x85, 0xc9, //0x000022aa testl        %ecx, %ecx
	0x0f, 0x85, 0xce, 0x00, 0x00, 0x00, //0x000022ac jne          LBB0_470
	0x49, 0x83, 0xc6, 0x20, //0x000022b2 addq         $32, %r14
	0x48, 0x8d, 0x0c, 0x02, //0x000022b6 leaq         (%rdx,%rax), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x000022ba addq         $-32, %rcx
	0x48, 0x83, 0xc0, 0xe0, //0x000022be addq         $-32, %rax
	0x48, 0x83, 0xf9, 0x1f, //0x000022c2 cmpq         $31, %rcx
	0x0f, 0x87, 0xc4, 0xff, 0xff, 0xff, //0x000022c6 ja           LBB0_456
	0x4d, 0x89, 0xe6, //0x000022cc movq         %r12, %r14
	0x49, 0x29, 0xc6, //0x000022cf subq         %rax, %r14
	0x48, 0x01, 0xc2, //0x000022d2 addq         %rax, %rdx
	0x48, 0x89, 0xd1, //0x000022d5 movq         %rdx, %rcx
	0x48, 0x83, 0xf9, 0x10, //0x000022d8 cmpq         $16, %rcx
	0x0f, 0x82, 0x49, 0x00, 0x00, 0x00, //0x000022dc jb           LBB0_462
	//0x000022e2 LBB0_459
	0x4c, 0x89, 0xe0, //0x000022e2 movq         %r12, %rax
	0x4c, 0x29, 0xf0, //0x000022e5 subq         %r14, %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000022e8 .p2align 4, 0x90
	//0x000022f0 LBB0_460
	0xc4, 0xc1, 0x7a, 0x6f, 0x36, //0x000022f0 vmovdqu      (%r14), %xmm6
	0xc5, 0xc9, 0x74, 0x3d, 0xe3, 0xde, 0xff, 0xff, //0x000022f5 vpcmpeqb     $-8477(%rip), %xmm6, %xmm7  /* LCPI0_4+0(%rip) */
	0xc5, 0xb1, 0xdb, 0xf6, //0x000022fd vpand        %xmm6, %xmm9, %xmm6
	0xc5, 0xa1, 0x74, 0xf6, //0x00002301 vpcmpeqb     %xmm6, %xmm11, %xmm6
	0xc5, 0xc9, 0xeb, 0xf7, //0x00002305 vpor         %xmm7, %xmm6, %xmm6
	0xc5, 0xf9, 0xd7, 0xd6, //0x00002309 vpmovmskb    %xmm6, %edx
	0x85, 0xd2, //0x0000230d testl        %edx, %edx
	0x0f, 0x85, 0xdf, 0x08, 0x00, 0x00, //0x0000230f jne          LBB0_580
	0x49, 0x83, 0xc6, 0x10, //0x00002315 addq         $16, %r14
	0x48, 0x83, 0xc1, 0xf0, //0x00002319 addq         $-16, %rcx
	0x48, 0x83, 0xc0, 0xf0, //0x0000231d addq         $-16, %rax
	0x48, 0x83, 0xf9, 0x0f, //0x00002321 cmpq         $15, %rcx
	0x0f, 0x87, 0xc5, 0xff, 0xff, 0xff, //0x00002325 ja           LBB0_460
	//0x0000232b LBB0_462
	0x48, 0x85, 0xc9, //0x0000232b testq        %rcx, %rcx
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x0000232e je           LBB0_469
	0x49, 0x8d, 0x14, 0x0e, //0x00002334 leaq         (%r14,%rcx), %rdx
	0x31, 0xc0, //0x00002338 xorl         %eax, %eax
	//0x0000233a LBB0_464
	0x41, 0x0f, 0xb6, 0x1c, 0x06, //0x0000233a movzbl       (%r14,%rax), %ebx
	0x80, 0xfb, 0x2c, //0x0000233f cmpb         $44, %bl
	0x0f, 0x84, 0xa9, 0x09, 0x00, 0x00, //0x00002342 je           LBB0_587
	0x80, 0xfb, 0x7d, //0x00002348 cmpb         $125, %bl
	0x0f, 0x84, 0xa0, 0x09, 0x00, 0x00, //0x0000234b je           LBB0_587
	0x80, 0xfb, 0x5d, //0x00002351 cmpb         $93, %bl
	0x0f, 0x84, 0x97, 0x09, 0x00, 0x00, //0x00002354 je           LBB0_587
	0x48, 0x83, 0xc0, 0x01, //0x0000235a addq         $1, %rax
	0x48, 0x39, 0xc1, //0x0000235e cmpq         %rax, %rcx
	0x0f, 0x85, 0xd3, 0xff, 0xff, 0xff, //0x00002361 jne          LBB0_464
	0x49, 0x89, 0xd6, //0x00002367 movq         %rdx, %r14
	//0x0000236a LBB0_469
	0x4d, 0x29, 0xe6, //0x0000236a subq         %r12, %r14
	0xe9, 0x85, 0x09, 0x00, 0x00, //0x0000236d jmp          LBB0_588
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002372 .p2align 4, 0x90
	//0x00002380 LBB0_470
	0x44, 0x0f, 0xbc, 0xf1, //0x00002380 bsfl         %ecx, %r14d
	//0x00002384 LBB0_471
	0x49, 0x29, 0xc6, //0x00002384 subq         %rax, %r14
	0x4d, 0x89, 0x75, 0x00, //0x00002387 movq         %r14, (%r13)
	0xe9, 0x27, 0x01, 0x00, 0x00, //0x0000238b jmp          LBB0_487
	//0x00002390 LBB0_472
	0x48, 0x83, 0xc0, 0x04, //0x00002390 addq         $4, %rax
	0x49, 0x3b, 0x00, //0x00002394 cmpq         (%r8), %rax
	0x0f, 0x86, 0x13, 0x01, 0x00, 0x00, //0x00002397 jbe          LBB0_486
	0xe9, 0x15, 0x01, 0x00, 0x00, //0x0000239d jmp          LBB0_487
	//0x000023a2 LBB0_473
	0x4c, 0x89, 0x44, 0x24, 0x28, //0x000023a2 movq         %r8, $40(%rsp)
	0x4d, 0x8b, 0x00, //0x000023a7 movq         (%r8), %r8
	0x4d, 0x89, 0xc5, //0x000023aa movq         %r8, %r13
	0x4d, 0x29, 0xf5, //0x000023ad subq         %r14, %r13
	0x49, 0x83, 0xfd, 0x20, //0x000023b0 cmpq         $32, %r13
	0x0f, 0x8c, 0x55, 0x09, 0x00, 0x00, //0x000023b4 jl           LBB0_589
	0x4d, 0x8d, 0x0c, 0x04, //0x000023ba leaq         (%r12,%rax), %r9
	0x49, 0x29, 0xc0, //0x000023be subq         %rax, %r8
	0xbe, 0x1f, 0x00, 0x00, 0x00, //0x000023c1 movl         $31, %esi
	0x45, 0x31, 0xed, //0x000023c6 xorl         %r13d, %r13d
	0x45, 0x31, 0xdb, //0x000023c9 xorl         %r11d, %r11d
	0xe9, 0x63, 0x00, 0x00, 0x00, //0x000023cc jmp          LBB0_475
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000023d1 .p2align 4, 0x90
	//0x000023e0 LBB0_479
	0x44, 0x89, 0xdb, //0x000023e0 movl         %r11d, %ebx
	0xf7, 0xd3, //0x000023e3 notl         %ebx
	0x21, 0xfb, //0x000023e5 andl         %edi, %ebx
	0x8d, 0x14, 0x1b, //0x000023e7 leal         (%rbx,%rbx), %edx
	0x44, 0x09, 0xda, //0x000023ea orl          %r11d, %edx
	0x89, 0xd1, //0x000023ed movl         %edx, %ecx
	0xf7, 0xd1, //0x000023ef notl         %ecx
	0x21, 0xf9, //0x000023f1 andl         %edi, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x000023f3 andl         $-1431655766, %ecx
	0x45, 0x31, 0xdb, //0x000023f9 xorl         %r11d, %r11d
	0x01, 0xd9, //0x000023fc addl         %ebx, %ecx
	0x41, 0x0f, 0x92, 0xc3, //0x000023fe setb         %r11b
	0x01, 0xc9, //0x00002402 addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x00002404 xorl         $1431655765, %ecx
	0x21, 0xd1, //0x0000240a andl         %edx, %ecx
	0xf7, 0xd1, //0x0000240c notl         %ecx
	0x41, 0x21, 0xca, //0x0000240e andl         %ecx, %r10d
	0x4d, 0x85, 0xd2, //0x00002411 testq        %r10, %r10
	0x0f, 0x85, 0x4e, 0x00, 0x00, 0x00, //0x00002414 jne          LBB0_478
	//0x0000241a LBB0_480
	0x49, 0x83, 0xc5, 0x20, //0x0000241a addq         $32, %r13
	0x49, 0x8d, 0x0c, 0x30, //0x0000241e leaq         (%r8,%rsi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00002422 addq         $-32, %rcx
	0x48, 0x83, 0xc6, 0xe0, //0x00002426 addq         $-32, %rsi
	0x48, 0x83, 0xf9, 0x3f, //0x0000242a cmpq         $63, %rcx
	0x0f, 0x8e, 0xf5, 0x07, 0x00, 0x00, //0x0000242e jle          LBB0_481
	//0x00002434 LBB0_475
	0xc4, 0x81, 0x7e, 0x6f, 0x74, 0x29, 0x01, //0x00002434 vmovdqu      $1(%r9,%r13), %ymm6
	0xc5, 0xcd, 0x74, 0xf9, //0x0000243b vpcmpeqb     %ymm1, %ymm6, %ymm7
	0xc5, 0x7d, 0xd7, 0xd7, //0x0000243f vpmovmskb    %ymm7, %r10d
	0xc5, 0xcd, 0x74, 0xf2, //0x00002443 vpcmpeqb     %ymm2, %ymm6, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x00002447 vpmovmskb    %ymm6, %edi
	0x85, 0xff, //0x0000244b testl        %edi, %edi
	0x0f, 0x85, 0x8d, 0xff, 0xff, 0xff, //0x0000244d jne          LBB0_479
	0x4d, 0x85, 0xdb, //0x00002453 testq        %r11, %r11
	0x0f, 0x85, 0x84, 0xff, 0xff, 0xff, //0x00002456 jne          LBB0_479
	0x45, 0x31, 0xdb, //0x0000245c xorl         %r11d, %r11d
	0x4d, 0x85, 0xd2, //0x0000245f testq        %r10, %r10
	0x0f, 0x84, 0xb2, 0xff, 0xff, 0xff, //0x00002462 je           LBB0_480
	//0x00002468 LBB0_478
	0x41, 0x0f, 0xbc, 0xca, //0x00002468 bsfl         %r10d, %ecx
	0x48, 0x01, 0xc8, //0x0000246c addq         %rcx, %rax
	0x4e, 0x8d, 0x34, 0x28, //0x0000246f leaq         (%rax,%r13), %r14
	0x49, 0x83, 0xc6, 0x02, //0x00002473 addq         $2, %r14
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00002477 movq         $16(%rsp), %r13
	0x4d, 0x89, 0x75, 0x00, //0x0000247c movq         %r14, (%r13)
	//0x00002480 LBB0_594
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002480 movq         $24(%rsp), %r10
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002485 movq         $32(%rsp), %r11
	0x4c, 0x8b, 0x44, 0x24, 0x28, //0x0000248a movq         $40(%rsp), %r8
	0xe9, 0x23, 0x00, 0x00, 0x00, //0x0000248f jmp          LBB0_487
	//0x00002494 LBB0_485
	0x48, 0x83, 0xc0, 0x05, //0x00002494 addq         $5, %rax
	0x49, 0x3b, 0x00, //0x00002498 cmpq         (%r8), %rax
	0x0f, 0x87, 0x16, 0x00, 0x00, 0x00, //0x0000249b ja           LBB0_487
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000024a1 .p2align 4, 0x90
	//0x000024b0 LBB0_486
	0x49, 0x89, 0x45, 0x00, //0x000024b0 movq         %rax, (%r13)
	0x49, 0x89, 0xc6, //0x000024b4 movq         %rax, %r14
	//0x000024b7 LBB0_487
	0x4d, 0x8b, 0x22, //0x000024b7 movq         (%r10), %r12
	0x49, 0x8b, 0x42, 0x08, //0x000024ba movq         $8(%r10), %rax
	0x49, 0x39, 0xc6, //0x000024be cmpq         %rax, %r14
	0x0f, 0x83, 0x39, 0x00, 0x00, 0x00, //0x000024c1 jae          LBB0_492
	0x43, 0x8a, 0x0c, 0x34, //0x000024c7 movb         (%r12,%r14), %cl
	0x80, 0xf9, 0x0d, //0x000024cb cmpb         $13, %cl
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x000024ce je           LBB0_492
	0x80, 0xf9, 0x20, //0x000024d4 cmpb         $32, %cl
	0x0f, 0x84, 0x23, 0x00, 0x00, 0x00, //0x000024d7 je           LBB0_492
	0x80, 0xc1, 0xf5, //0x000024dd addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x000024e0 cmpb         $-2, %cl
	0x0f, 0x83, 0x17, 0x00, 0x00, 0x00, //0x000024e3 jae          LBB0_492
	0x4c, 0x89, 0xf2, //0x000024e9 movq         %r14, %rdx
	0xe9, 0x89, 0x01, 0x00, 0x00, //0x000024ec jmp          LBB0_517
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000024f1 .p2align 4, 0x90
	//0x00002500 LBB0_492
	0x49, 0x8d, 0x56, 0x01, //0x00002500 leaq         $1(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00002504 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002507 jae          LBB0_496
	0x41, 0x8a, 0x0c, 0x14, //0x0000250d movb         (%r12,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00002511 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002514 je           LBB0_496
	0x80, 0xf9, 0x20, //0x0000251a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000251d je           LBB0_496
	0x80, 0xc1, 0xf5, //0x00002523 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00002526 cmpb         $-2, %cl
	0x0f, 0x82, 0x4b, 0x01, 0x00, 0x00, //0x00002529 jb           LBB0_517
	0x90, //0x0000252f .p2align 4, 0x90
	//0x00002530 LBB0_496
	0x49, 0x8d, 0x56, 0x02, //0x00002530 leaq         $2(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00002534 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002537 jae          LBB0_500
	0x41, 0x8a, 0x0c, 0x14, //0x0000253d movb         (%r12,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00002541 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002544 je           LBB0_500
	0x80, 0xf9, 0x20, //0x0000254a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000254d je           LBB0_500
	0x80, 0xc1, 0xf5, //0x00002553 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00002556 cmpb         $-2, %cl
	0x0f, 0x82, 0x1b, 0x01, 0x00, 0x00, //0x00002559 jb           LBB0_517
	0x90, //0x0000255f .p2align 4, 0x90
	//0x00002560 LBB0_500
	0x49, 0x8d, 0x56, 0x03, //0x00002560 leaq         $3(%r14), %rdx
	0x48, 0x39, 0xc2, //0x00002564 cmpq         %rax, %rdx
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00002567 jae          LBB0_504
	0x41, 0x8a, 0x0c, 0x14, //0x0000256d movb         (%r12,%rdx), %cl
	0x80, 0xf9, 0x0d, //0x00002571 cmpb         $13, %cl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00002574 je           LBB0_504
	0x80, 0xf9, 0x20, //0x0000257a cmpb         $32, %cl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000257d je           LBB0_504
	0x80, 0xc1, 0xf5, //0x00002583 addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00002586 cmpb         $-2, %cl
	0x0f, 0x82, 0xeb, 0x00, 0x00, 0x00, //0x00002589 jb           LBB0_517
	0x90, //0x0000258f .p2align 4, 0x90
	//0x00002590 LBB0_504
	0x49, 0x8d, 0x56, 0x04, //0x00002590 leaq         $4(%r14), %rdx
	0x48, 0x89, 0xc1, //0x00002594 movq         %rax, %rcx
	0x48, 0x29, 0xd1, //0x00002597 subq         %rdx, %rcx
	0x0f, 0x86, 0x81, 0x0a, 0x00, 0x00, //0x0000259a jbe          LBB0_1022
	0x48, 0x83, 0xf9, 0x20, //0x000025a0 cmpq         $32, %rcx
	0x0f, 0x82, 0x13, 0x07, 0x00, 0x00, //0x000025a4 jb           LBB0_584
	0x48, 0xc7, 0xc1, 0xfc, 0xff, 0xff, 0xff, //0x000025aa movq         $-4, %rcx
	0x4c, 0x29, 0xf1, //0x000025b1 subq         %r14, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000025b4 .p2align 4, 0x90
	//0x000025c0 LBB0_507
	0xc4, 0x41, 0x7e, 0x6f, 0x34, 0x14, //0x000025c0 vmovdqu      (%r12,%rdx), %ymm14
	0xc4, 0x42, 0x7d, 0x00, 0xfe, //0x000025c6 vpshufb      %ymm14, %ymm0, %ymm15
	0xc4, 0xc1, 0x0d, 0xf8, 0xf7, //0x000025cb vpsubb       %ymm15, %ymm14, %ymm6
	0xc4, 0xe2, 0x7d, 0x17, 0xf6, //0x000025d0 vptest       %ymm6, %ymm6
	0x0f, 0x85, 0x85, 0x00, 0x00, 0x00, //0x000025d5 jne          LBB0_516
	0x48, 0x83, 0xc2, 0x20, //0x000025db addq         $32, %rdx
	0x48, 0x8d, 0x34, 0x08, //0x000025df leaq         (%rax,%rcx), %rsi
	0x48, 0x83, 0xc6, 0xe0, //0x000025e3 addq         $-32, %rsi
	0x48, 0x83, 0xc1, 0xe0, //0x000025e7 addq         $-32, %rcx
	0x48, 0x83, 0xfe, 0x1f, //0x000025eb cmpq         $31, %rsi
	0x0f, 0x87, 0xcb, 0xff, 0xff, 0xff, //0x000025ef ja           LBB0_507
	0x4c, 0x89, 0xe2, //0x000025f5 movq         %r12, %rdx
	0x48, 0x29, 0xca, //0x000025f8 subq         %rcx, %rdx
	0x48, 0x01, 0xc1, //0x000025fb addq         %rax, %rcx
	0x48, 0x85, 0xc9, //0x000025fe testq        %rcx, %rcx
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x00002601 je           LBB0_515
	//0x00002607 LBB0_510
	0x4c, 0x8d, 0x0c, 0x0a, //0x00002607 leaq         (%rdx,%rcx), %r9
	0x31, 0xf6, //0x0000260b xorl         %esi, %esi
	0x90, 0x90, 0x90, //0x0000260d .p2align 4, 0x90
	//0x00002610 LBB0_511
	0x0f, 0xbe, 0x1c, 0x32, //0x00002610 movsbl       (%rdx,%rsi), %ebx
	0x83, 0xfb, 0x20, //0x00002614 cmpl         $32, %ebx
	0x0f, 0x87, 0xe4, 0x05, 0x00, 0x00, //0x00002617 ja           LBB0_581
	0x48, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000261d movabsq      $4294977024, %rdi
	0x48, 0x0f, 0xa3, 0xdf, //0x00002627 btq          %rbx, %rdi
	0x0f, 0x83, 0xd0, 0x05, 0x00, 0x00, //0x0000262b jae          LBB0_581
	0x48, 0x83, 0xc6, 0x01, //0x00002631 addq         $1, %rsi
	0x48, 0x39, 0xf1, //0x00002635 cmpq         %rsi, %rcx
	0x0f, 0x85, 0xd2, 0xff, 0xff, 0xff, //0x00002638 jne          LBB0_511
	0x4c, 0x89, 0xca, //0x0000263e movq         %r9, %rdx
	//0x00002641 LBB0_515
	0x4c, 0x29, 0xe2, //0x00002641 subq         %r12, %rdx
	0x48, 0x39, 0xc2, //0x00002644 cmpq         %rax, %rdx
	0x0f, 0x82, 0x2d, 0x00, 0x00, 0x00, //0x00002647 jb           LBB0_517
	0xe9, 0x1f, 0x25, 0x00, 0x00, //0x0000264d jmp          LBB0_1023
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002652 .p2align 4, 0x90
	//0x00002660 LBB0_516
	0xc4, 0xc1, 0x0d, 0x74, 0xf7, //0x00002660 vpcmpeqb     %ymm15, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xd6, //0x00002665 vpmovmskb    %ymm6, %edx
	0xf7, 0xd2, //0x00002669 notl         %edx
	0x0f, 0xbc, 0xd2, //0x0000266b bsfl         %edx, %edx
	0x48, 0x29, 0xca, //0x0000266e subq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x00002671 cmpq         %rax, %rdx
	0x0f, 0x83, 0xf7, 0x24, 0x00, 0x00, //0x00002674 jae          LBB0_1023
	//0x0000267a LBB0_517
	0x4c, 0x8d, 0x72, 0x01, //0x0000267a leaq         $1(%rdx), %r14
	0x4d, 0x89, 0x75, 0x00, //0x0000267e movq         %r14, (%r13)
	0x41, 0x8a, 0x04, 0x14, //0x00002682 movb         (%r12,%rdx), %al
	0x3c, 0x2c, //0x00002686 cmpb         $44, %al
	0x0f, 0x85, 0x77, 0x09, 0x00, 0x00, //0x00002688 jne          LBB0_628
	0x49, 0x8d, 0x47, 0xff, //0x0000268e leaq         $-1(%r15), %rax
	0x49, 0x83, 0xff, 0x02, //0x00002692 cmpq         $2, %r15
	0x49, 0x89, 0xc7, //0x00002696 movq         %rax, %r15
	0x0f, 0x8d, 0x01, 0xfa, 0xff, 0xff, //0x00002699 jge          LBB0_422
	0xe9, 0xbc, 0x06, 0x00, 0x00, //0x0000269f jmp          LBB0_519
	//0x000026a4 LBB0_522
	0x4d, 0x89, 0xc1, //0x000026a4 movq         %r8, %r9
	0x4d, 0x8b, 0x00, //0x000026a7 movq         (%r8), %r8
	0x4d, 0x29, 0xf0, //0x000026aa subq         %r14, %r8
	0x4d, 0x01, 0xf4, //0x000026ad addq         %r14, %r12
	0x45, 0x31, 0xdb, //0x000026b0 xorl         %r11d, %r11d
	0x45, 0x31, 0xd2, //0x000026b3 xorl         %r10d, %r10d
	0x45, 0x31, 0xf6, //0x000026b6 xorl         %r14d, %r14d
	0x31, 0xc0, //0x000026b9 xorl         %eax, %eax
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x000026bb jmp          LBB0_524
	//0x000026c0 LBB0_523
	0x48, 0xc1, 0xfa, 0x3f, //0x000026c0 sarq         $63, %rdx
	0xf3, 0x48, 0x0f, 0xb8, 0xce, //0x000026c4 popcntq      %rsi, %rcx
	0x49, 0x01, 0xce, //0x000026c9 addq         %rcx, %r14
	0x49, 0x83, 0xc4, 0x40, //0x000026cc addq         $64, %r12
	0x49, 0x83, 0xc0, 0xc0, //0x000026d0 addq         $-64, %r8
	0x49, 0x89, 0xd3, //0x000026d4 movq         %rdx, %r11
	//0x000026d7 LBB0_524
	0x49, 0x83, 0xf8, 0x40, //0x000026d7 cmpq         $64, %r8
	0x0f, 0x8c, 0x2c, 0x01, 0x00, 0x00, //0x000026db jl           LBB0_532
	//0x000026e1 LBB0_525
	0xc4, 0x41, 0x7e, 0x6f, 0x3c, 0x24, //0x000026e1 vmovdqu      (%r12), %ymm15
	0xc4, 0x41, 0x7e, 0x6f, 0x74, 0x24, 0x20, //0x000026e7 vmovdqu      $32(%r12), %ymm14
	0xc5, 0x85, 0x74, 0xf2, //0x000026ee vpcmpeqb     %ymm2, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xce, //0x000026f2 vpmovmskb    %ymm6, %ecx
	0xc5, 0x8d, 0x74, 0xf2, //0x000026f6 vpcmpeqb     %ymm2, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x000026fa vpmovmskb    %ymm6, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x000026fe shlq         $32, %rsi
	0x48, 0x09, 0xf1, //0x00002702 orq          %rsi, %rcx
	0x48, 0x89, 0xce, //0x00002705 movq         %rcx, %rsi
	0x4c, 0x09, 0xd6, //0x00002708 orq          %r10, %rsi
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x0000270b jne          LBB0_527
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002711 movq         $-1, %rcx
	0x45, 0x31, 0xd2, //0x00002718 xorl         %r10d, %r10d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x0000271b jmp          LBB0_528
	//0x00002720 LBB0_527
	0x4c, 0x89, 0xd6, //0x00002720 movq         %r10, %rsi
	0x48, 0xf7, 0xd6, //0x00002723 notq         %rsi
	0x48, 0x21, 0xce, //0x00002726 andq         %rcx, %rsi
	0x48, 0x8d, 0x1c, 0x36, //0x00002729 leaq         (%rsi,%rsi), %rbx
	0x4c, 0x09, 0xd3, //0x0000272d orq          %r10, %rbx
	0x48, 0x89, 0xdf, //0x00002730 movq         %rbx, %rdi
	0x48, 0xf7, 0xd7, //0x00002733 notq         %rdi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002736 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x00002740 andq         %rdx, %rcx
	0x48, 0x21, 0xf9, //0x00002743 andq         %rdi, %rcx
	0x45, 0x31, 0xd2, //0x00002746 xorl         %r10d, %r10d
	0x48, 0x01, 0xf1, //0x00002749 addq         %rsi, %rcx
	0x41, 0x0f, 0x92, 0xc2, //0x0000274c setb         %r10b
	0x48, 0x01, 0xc9, //0x00002750 addq         %rcx, %rcx
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002753 movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd1, //0x0000275d xorq         %rdx, %rcx
	0x48, 0x21, 0xd9, //0x00002760 andq         %rbx, %rcx
	0x48, 0xf7, 0xd1, //0x00002763 notq         %rcx
	//0x00002766 LBB0_528
	0xc5, 0x8d, 0x74, 0xf1, //0x00002766 vpcmpeqb     %ymm1, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x0000276a vpmovmskb    %ymm6, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x0000276e shlq         $32, %rsi
	0xc5, 0x85, 0x74, 0xf1, //0x00002772 vpcmpeqb     %ymm1, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x00002776 vpmovmskb    %ymm6, %edi
	0x48, 0x09, 0xf7, //0x0000277a orq          %rsi, %rdi
	0x48, 0x21, 0xcf, //0x0000277d andq         %rcx, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xf7, //0x00002780 vmovq        %rdi, %xmm6
	0xc4, 0xc3, 0x49, 0x44, 0xf0, 0x00, //0x00002785 vpclmulqdq   $0, %xmm8, %xmm6, %xmm6
	0xc4, 0xe1, 0xf9, 0x7e, 0xf2, //0x0000278b vmovq        %xmm6, %rdx
	0x4c, 0x31, 0xda, //0x00002790 xorq         %r11, %rdx
	0xc4, 0xc1, 0x05, 0x74, 0xf4, //0x00002793 vpcmpeqb     %ymm12, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x00002798 vpmovmskb    %ymm6, %esi
	0xc4, 0xc1, 0x0d, 0x74, 0xf4, //0x0000279c vpcmpeqb     %ymm12, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x000027a1 vpmovmskb    %ymm6, %edi
	0x48, 0xc1, 0xe7, 0x20, //0x000027a5 shlq         $32, %rdi
	0x48, 0x09, 0xfe, //0x000027a9 orq          %rdi, %rsi
	0x48, 0x89, 0xd1, //0x000027ac movq         %rdx, %rcx
	0x48, 0xf7, 0xd1, //0x000027af notq         %rcx
	0x48, 0x21, 0xce, //0x000027b2 andq         %rcx, %rsi
	0xc4, 0xc1, 0x05, 0x74, 0xf5, //0x000027b5 vpcmpeqb     %ymm13, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x000027ba vpmovmskb    %ymm6, %edi
	0xc4, 0xc1, 0x0d, 0x74, 0xf5, //0x000027be vpcmpeqb     %ymm13, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x000027c3 vpmovmskb    %ymm6, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x000027c7 shlq         $32, %rbx
	0x48, 0x09, 0xdf, //0x000027cb orq          %rbx, %rdi
	0x48, 0x21, 0xcf, //0x000027ce andq         %rcx, %rdi
	0x0f, 0x84, 0xe9, 0xfe, 0xff, 0xff, //0x000027d1 je           LBB0_523
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000027d7 movq         $32(%rsp), %r11
	0x90, 0x90, 0x90, 0x90, //0x000027dc .p2align 4, 0x90
	//0x000027e0 LBB0_530
	0x48, 0x8d, 0x5f, 0xff, //0x000027e0 leaq         $-1(%rdi), %rbx
	0x48, 0x89, 0xd9, //0x000027e4 movq         %rbx, %rcx
	0x48, 0x21, 0xf1, //0x000027e7 andq         %rsi, %rcx
	0xf3, 0x48, 0x0f, 0xb8, 0xc9, //0x000027ea popcntq      %rcx, %rcx
	0x4c, 0x01, 0xf1, //0x000027ef addq         %r14, %rcx
	0x48, 0x39, 0xc1, //0x000027f2 cmpq         %rax, %rcx
	0x0f, 0x86, 0xc8, 0x03, 0x00, 0x00, //0x000027f5 jbe          LBB0_574
	0x48, 0x83, 0xc0, 0x01, //0x000027fb addq         $1, %rax
	0x48, 0x21, 0xdf, //0x000027ff andq         %rbx, %rdi
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00002802 jne          LBB0_530
	0xe9, 0xb3, 0xfe, 0xff, 0xff, //0x00002808 jmp          LBB0_523
	//0x0000280d LBB0_532
	0x4d, 0x85, 0xc0, //0x0000280d testq        %r8, %r8
	0x0f, 0x8e, 0x02, 0x05, 0x00, 0x00, //0x00002810 jle          LBB0_590
	0xc5, 0xc9, 0xef, 0xf6, //0x00002816 vpxor        %xmm6, %xmm6, %xmm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x60, //0x0000281a vmovdqu      %ymm6, $96(%rsp)
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x00002820 vmovdqu      %ymm6, $64(%rsp)
	0x44, 0x89, 0xe1, //0x00002826 movl         %r12d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00002829 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x0000282f cmpl         $4033, %ecx
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x00002835 jb           LBB0_536
	0x49, 0x83, 0xf8, 0x20, //0x0000283b cmpq         $32, %r8
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x0000283f jb           LBB0_537
	0xc4, 0xc1, 0x7e, 0x6f, 0x34, 0x24, //0x00002845 vmovdqu      (%r12), %ymm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x0000284b vmovdqu      %ymm6, $64(%rsp)
	0x49, 0x83, 0xc4, 0x20, //0x00002851 addq         $32, %r12
	0x49, 0x8d, 0x48, 0xe0, //0x00002855 leaq         $-32(%r8), %rcx
	0x48, 0x8d, 0x7c, 0x24, 0x60, //0x00002859 leaq         $96(%rsp), %rdi
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x0000285e jmp          LBB0_538
	//0x00002863 LBB0_536
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00002863 movq         $16(%rsp), %r13
	0xe9, 0x74, 0xfe, 0xff, 0xff, //0x00002868 jmp          LBB0_525
	//0x0000286d LBB0_537
	0x48, 0x8d, 0x7c, 0x24, 0x40, //0x0000286d leaq         $64(%rsp), %rdi
	0x4c, 0x89, 0xc1, //0x00002872 movq         %r8, %rcx
	//0x00002875 LBB0_538
	0x48, 0x83, 0xf9, 0x10, //0x00002875 cmpq         $16, %rcx
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x00002879 jb           LBB0_539
	0xc4, 0xc1, 0x7a, 0x6f, 0x34, 0x24, //0x0000287f vmovdqu      (%r12), %xmm6
	0xc5, 0xfa, 0x7f, 0x37, //0x00002885 vmovdqu      %xmm6, (%rdi)
	0x49, 0x83, 0xc4, 0x10, //0x00002889 addq         $16, %r12
	0x48, 0x83, 0xc7, 0x10, //0x0000288d addq         $16, %rdi
	0x48, 0x83, 0xc1, 0xf0, //0x00002891 addq         $-16, %rcx
	0x48, 0x83, 0xf9, 0x08, //0x00002895 cmpq         $8, %rcx
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00002899 jae          LBB0_546
	//0x0000289f LBB0_540
	0x48, 0x83, 0xf9, 0x04, //0x0000289f cmpq         $4, %rcx
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x000028a3 jb           LBB0_541
	//0x000028a9 LBB0_547
	0x41, 0x8b, 0x14, 0x24, //0x000028a9 movl         (%r12), %edx
	0x89, 0x17, //0x000028ad movl         %edx, (%rdi)
	0x49, 0x83, 0xc4, 0x04, //0x000028af addq         $4, %r12
	0x48, 0x83, 0xc7, 0x04, //0x000028b3 addq         $4, %rdi
	0x48, 0x83, 0xc1, 0xfc, //0x000028b7 addq         $-4, %rcx
	0x48, 0x83, 0xf9, 0x02, //0x000028bb cmpq         $2, %rcx
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x000028bf jae          LBB0_542
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x000028c5 jmp          LBB0_543
	//0x000028ca LBB0_539
	0x48, 0x83, 0xf9, 0x08, //0x000028ca cmpq         $8, %rcx
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x000028ce jb           LBB0_540
	//0x000028d4 LBB0_546
	0x49, 0x8b, 0x14, 0x24, //0x000028d4 movq         (%r12), %rdx
	0x48, 0x89, 0x17, //0x000028d8 movq         %rdx, (%rdi)
	0x49, 0x83, 0xc4, 0x08, //0x000028db addq         $8, %r12
	0x48, 0x83, 0xc7, 0x08, //0x000028df addq         $8, %rdi
	0x48, 0x83, 0xc1, 0xf8, //0x000028e3 addq         $-8, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x000028e7 cmpq         $4, %rcx
	0x0f, 0x83, 0xb8, 0xff, 0xff, 0xff, //0x000028eb jae          LBB0_547
	//0x000028f1 LBB0_541
	0x48, 0x83, 0xf9, 0x02, //0x000028f1 cmpq         $2, %rcx
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x000028f5 jb           LBB0_543
	//0x000028fb LBB0_542
	0x41, 0x0f, 0xb7, 0x14, 0x24, //0x000028fb movzwl       (%r12), %edx
	0x66, 0x89, 0x17, //0x00002900 movw         %dx, (%rdi)
	0x49, 0x83, 0xc4, 0x02, //0x00002903 addq         $2, %r12
	0x48, 0x83, 0xc7, 0x02, //0x00002907 addq         $2, %rdi
	0x48, 0x83, 0xc1, 0xfe, //0x0000290b addq         $-2, %rcx
	//0x0000290f LBB0_543
	0x4c, 0x89, 0xe6, //0x0000290f movq         %r12, %rsi
	0x4c, 0x8d, 0x64, 0x24, 0x40, //0x00002912 leaq         $64(%rsp), %r12
	0x48, 0x85, 0xc9, //0x00002917 testq        %rcx, %rcx
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x0000291a movq         $16(%rsp), %r13
	0x0f, 0x84, 0xbc, 0xfd, 0xff, 0xff, //0x0000291f je           LBB0_525
	0x8a, 0x0e, //0x00002925 movb         (%rsi), %cl
	0x88, 0x0f, //0x00002927 movb         %cl, (%rdi)
	0x4c, 0x8d, 0x64, 0x24, 0x40, //0x00002929 leaq         $64(%rsp), %r12
	0xe9, 0xae, 0xfd, 0xff, 0xff, //0x0000292e jmp          LBB0_525
	//0x00002933 LBB0_548
	0x4d, 0x89, 0xc1, //0x00002933 movq         %r8, %r9
	0x4d, 0x8b, 0x00, //0x00002936 movq         (%r8), %r8
	0x4d, 0x29, 0xf0, //0x00002939 subq         %r14, %r8
	0x4d, 0x01, 0xf4, //0x0000293c addq         %r14, %r12
	0x45, 0x31, 0xdb, //0x0000293f xorl         %r11d, %r11d
	0x45, 0x31, 0xd2, //0x00002942 xorl         %r10d, %r10d
	0x45, 0x31, 0xf6, //0x00002945 xorl         %r14d, %r14d
	0x31, 0xc0, //0x00002948 xorl         %eax, %eax
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x0000294a jmp          LBB0_550
	//0x0000294f LBB0_549
	0x48, 0xc1, 0xfa, 0x3f, //0x0000294f sarq         $63, %rdx
	0xf3, 0x48, 0x0f, 0xb8, 0xce, //0x00002953 popcntq      %rsi, %rcx
	0x49, 0x01, 0xce, //0x00002958 addq         %rcx, %r14
	0x49, 0x83, 0xc4, 0x40, //0x0000295b addq         $64, %r12
	0x49, 0x83, 0xc0, 0xc0, //0x0000295f addq         $-64, %r8
	0x49, 0x89, 0xd3, //0x00002963 movq         %rdx, %r11
	//0x00002966 LBB0_550
	0x49, 0x83, 0xf8, 0x40, //0x00002966 cmpq         $64, %r8
	0x0f, 0x8c, 0x2d, 0x01, 0x00, 0x00, //0x0000296a jl           LBB0_558
	//0x00002970 LBB0_551
	0xc4, 0x41, 0x7e, 0x6f, 0x3c, 0x24, //0x00002970 vmovdqu      (%r12), %ymm15
	0xc4, 0x41, 0x7e, 0x6f, 0x74, 0x24, 0x20, //0x00002976 vmovdqu      $32(%r12), %ymm14
	0xc5, 0x85, 0x74, 0xf2, //0x0000297d vpcmpeqb     %ymm2, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xce, //0x00002981 vpmovmskb    %ymm6, %ecx
	0xc5, 0x8d, 0x74, 0xf2, //0x00002985 vpcmpeqb     %ymm2, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x00002989 vpmovmskb    %ymm6, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x0000298d shlq         $32, %rsi
	0x48, 0x09, 0xf1, //0x00002991 orq          %rsi, %rcx
	0x48, 0x89, 0xce, //0x00002994 movq         %rcx, %rsi
	0x4c, 0x09, 0xd6, //0x00002997 orq          %r10, %rsi
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x0000299a jne          LBB0_553
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000029a0 movq         $-1, %rcx
	0x45, 0x31, 0xd2, //0x000029a7 xorl         %r10d, %r10d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x000029aa jmp          LBB0_554
	//0x000029af LBB0_553
	0x4c, 0x89, 0xd6, //0x000029af movq         %r10, %rsi
	0x48, 0xf7, 0xd6, //0x000029b2 notq         %rsi
	0x48, 0x21, 0xce, //0x000029b5 andq         %rcx, %rsi
	0x48, 0x8d, 0x1c, 0x36, //0x000029b8 leaq         (%rsi,%rsi), %rbx
	0x4c, 0x09, 0xd3, //0x000029bc orq          %r10, %rbx
	0x48, 0x89, 0xdf, //0x000029bf movq         %rbx, %rdi
	0x48, 0xf7, 0xd7, //0x000029c2 notq         %rdi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000029c5 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd1, //0x000029cf andq         %rdx, %rcx
	0x48, 0x21, 0xf9, //0x000029d2 andq         %rdi, %rcx
	0x45, 0x31, 0xd2, //0x000029d5 xorl         %r10d, %r10d
	0x48, 0x01, 0xf1, //0x000029d8 addq         %rsi, %rcx
	0x41, 0x0f, 0x92, 0xc2, //0x000029db setb         %r10b
	0x48, 0x01, 0xc9, //0x000029df addq         %rcx, %rcx
	0x48, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000029e2 movabsq      $6148914691236517205, %rdx
	0x48, 0x31, 0xd1, //0x000029ec xorq         %rdx, %rcx
	0x48, 0x21, 0xd9, //0x000029ef andq         %rbx, %rcx
	0x48, 0xf7, 0xd1, //0x000029f2 notq         %rcx
	//0x000029f5 LBB0_554
	0xc5, 0x8d, 0x74, 0xf1, //0x000029f5 vpcmpeqb     %ymm1, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x000029f9 vpmovmskb    %ymm6, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x000029fd shlq         $32, %rsi
	0xc5, 0x85, 0x74, 0xf1, //0x00002a01 vpcmpeqb     %ymm1, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x00002a05 vpmovmskb    %ymm6, %edi
	0x48, 0x09, 0xf7, //0x00002a09 orq          %rsi, %rdi
	0x48, 0x21, 0xcf, //0x00002a0c andq         %rcx, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xf7, //0x00002a0f vmovq        %rdi, %xmm6
	0xc4, 0xc3, 0x49, 0x44, 0xf0, 0x00, //0x00002a14 vpclmulqdq   $0, %xmm8, %xmm6, %xmm6
	0xc4, 0xe1, 0xf9, 0x7e, 0xf2, //0x00002a1a vmovq        %xmm6, %rdx
	0x4c, 0x31, 0xda, //0x00002a1f xorq         %r11, %rdx
	0xc4, 0xc1, 0x05, 0x74, 0xf2, //0x00002a22 vpcmpeqb     %ymm10, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x00002a27 vpmovmskb    %ymm6, %esi
	0xc4, 0xc1, 0x0d, 0x74, 0xf2, //0x00002a2b vpcmpeqb     %ymm10, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x00002a30 vpmovmskb    %ymm6, %edi
	0x48, 0xc1, 0xe7, 0x20, //0x00002a34 shlq         $32, %rdi
	0x48, 0x09, 0xfe, //0x00002a38 orq          %rdi, %rsi
	0x48, 0x89, 0xd1, //0x00002a3b movq         %rdx, %rcx
	0x48, 0xf7, 0xd1, //0x00002a3e notq         %rcx
	0x48, 0x21, 0xce, //0x00002a41 andq         %rcx, %rsi
	0xc5, 0x85, 0x74, 0xf5, //0x00002a44 vpcmpeqb     %ymm5, %ymm15, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x00002a48 vpmovmskb    %ymm6, %edi
	0xc5, 0x8d, 0x74, 0xf5, //0x00002a4c vpcmpeqb     %ymm5, %ymm14, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x00002a50 vpmovmskb    %ymm6, %ebx
	0x48, 0xc1, 0xe3, 0x20, //0x00002a54 shlq         $32, %rbx
	0x48, 0x09, 0xdf, //0x00002a58 orq          %rbx, %rdi
	0x48, 0x21, 0xcf, //0x00002a5b andq         %rcx, %rdi
	0x0f, 0x84, 0xeb, 0xfe, 0xff, 0xff, //0x00002a5e je           LBB0_549
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002a64 movq         $32(%rsp), %r11
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002a69 .p2align 4, 0x90
	//0x00002a70 LBB0_556
	0x48, 0x8d, 0x5f, 0xff, //0x00002a70 leaq         $-1(%rdi), %rbx
	0x48, 0x89, 0xd9, //0x00002a74 movq         %rbx, %rcx
	0x48, 0x21, 0xf1, //0x00002a77 andq         %rsi, %rcx
	0xf3, 0x48, 0x0f, 0xb8, 0xc9, //0x00002a7a popcntq      %rcx, %rcx
	0x4c, 0x01, 0xf1, //0x00002a7f addq         %r14, %rcx
	0x48, 0x39, 0xc1, //0x00002a82 cmpq         %rax, %rcx
	0x0f, 0x86, 0x38, 0x01, 0x00, 0x00, //0x00002a85 jbe          LBB0_574
	0x48, 0x83, 0xc0, 0x01, //0x00002a8b addq         $1, %rax
	0x48, 0x21, 0xdf, //0x00002a8f andq         %rbx, %rdi
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00002a92 jne          LBB0_556
	0xe9, 0xb2, 0xfe, 0xff, 0xff, //0x00002a98 jmp          LBB0_549
	//0x00002a9d LBB0_558
	0x4d, 0x85, 0xc0, //0x00002a9d testq        %r8, %r8
	0x0f, 0x8e, 0x72, 0x02, 0x00, 0x00, //0x00002aa0 jle          LBB0_590
	0xc5, 0xc9, 0xef, 0xf6, //0x00002aa6 vpxor        %xmm6, %xmm6, %xmm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x60, //0x00002aaa vmovdqu      %ymm6, $96(%rsp)
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x00002ab0 vmovdqu      %ymm6, $64(%rsp)
	0x44, 0x89, 0xe1, //0x00002ab6 movl         %r12d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00002ab9 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x00002abf cmpl         $4033, %ecx
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x00002ac5 jb           LBB0_562
	0x49, 0x83, 0xf8, 0x20, //0x00002acb cmpq         $32, %r8
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x00002acf jb           LBB0_563
	0xc4, 0xc1, 0x7e, 0x6f, 0x34, 0x24, //0x00002ad5 vmovdqu      (%r12), %ymm6
	0xc5, 0xfe, 0x7f, 0x74, 0x24, 0x40, //0x00002adb vmovdqu      %ymm6, $64(%rsp)
	0x49, 0x83, 0xc4, 0x20, //0x00002ae1 addq         $32, %r12
	0x49, 0x8d, 0x48, 0xe0, //0x00002ae5 leaq         $-32(%r8), %rcx
	0x48, 0x8d, 0x7c, 0x24, 0x60, //0x00002ae9 leaq         $96(%rsp), %rdi
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00002aee jmp          LBB0_564
	//0x00002af3 LBB0_562
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00002af3 movq         $16(%rsp), %r13
	0xe9, 0x73, 0xfe, 0xff, 0xff, //0x00002af8 jmp          LBB0_551
	//0x00002afd LBB0_563
	0x48, 0x8d, 0x7c, 0x24, 0x40, //0x00002afd leaq         $64(%rsp), %rdi
	0x4c, 0x89, 0xc1, //0x00002b02 movq         %r8, %rcx
	//0x00002b05 LBB0_564
	0x48, 0x83, 0xf9, 0x10, //0x00002b05 cmpq         $16, %rcx
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x00002b09 jb           LBB0_565
	0xc4, 0xc1, 0x7a, 0x6f, 0x34, 0x24, //0x00002b0f vmovdqu      (%r12), %xmm6
	0xc5, 0xfa, 0x7f, 0x37, //0x00002b15 vmovdqu      %xmm6, (%rdi)
	0x49, 0x83, 0xc4, 0x10, //0x00002b19 addq         $16, %r12
	0x48, 0x83, 0xc7, 0x10, //0x00002b1d addq         $16, %rdi
	0x48, 0x83, 0xc1, 0xf0, //0x00002b21 addq         $-16, %rcx
	0x48, 0x83, 0xf9, 0x08, //0x00002b25 cmpq         $8, %rcx
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00002b29 jae          LBB0_572
	//0x00002b2f LBB0_566
	0x48, 0x83, 0xf9, 0x04, //0x00002b2f cmpq         $4, %rcx
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x00002b33 jb           LBB0_567
	//0x00002b39 LBB0_573
	0x41, 0x8b, 0x14, 0x24, //0x00002b39 movl         (%r12), %edx
	0x89, 0x17, //0x00002b3d movl         %edx, (%rdi)
	0x49, 0x83, 0xc4, 0x04, //0x00002b3f addq         $4, %r12
	0x48, 0x83, 0xc7, 0x04, //0x00002b43 addq         $4, %rdi
	0x48, 0x83, 0xc1, 0xfc, //0x00002b47 addq         $-4, %rcx
	0x48, 0x83, 0xf9, 0x02, //0x00002b4b cmpq         $2, %rcx
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x00002b4f jae          LBB0_568
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x00002b55 jmp          LBB0_569
	//0x00002b5a LBB0_565
	0x48, 0x83, 0xf9, 0x08, //0x00002b5a cmpq         $8, %rcx
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x00002b5e jb           LBB0_566
	//0x00002b64 LBB0_572
	0x49, 0x8b, 0x14, 0x24, //0x00002b64 movq         (%r12), %rdx
	0x48, 0x89, 0x17, //0x00002b68 movq         %rdx, (%rdi)
	0x49, 0x83, 0xc4, 0x08, //0x00002b6b addq         $8, %r12
	0x48, 0x83, 0xc7, 0x08, //0x00002b6f addq         $8, %rdi
	0x48, 0x83, 0xc1, 0xf8, //0x00002b73 addq         $-8, %rcx
	0x48, 0x83, 0xf9, 0x04, //0x00002b77 cmpq         $4, %rcx
	0x0f, 0x83, 0xb8, 0xff, 0xff, 0xff, //0x00002b7b jae          LBB0_573
	//0x00002b81 LBB0_567
	0x48, 0x83, 0xf9, 0x02, //0x00002b81 cmpq         $2, %rcx
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00002b85 jb           LBB0_569
	//0x00002b8b LBB0_568
	0x41, 0x0f, 0xb7, 0x14, 0x24, //0x00002b8b movzwl       (%r12), %edx
	0x66, 0x89, 0x17, //0x00002b90 movw         %dx, (%rdi)
	0x49, 0x83, 0xc4, 0x02, //0x00002b93 addq         $2, %r12
	0x48, 0x83, 0xc7, 0x02, //0x00002b97 addq         $2, %rdi
	0x48, 0x83, 0xc1, 0xfe, //0x00002b9b addq         $-2, %rcx
	//0x00002b9f LBB0_569
	0x4c, 0x89, 0xe6, //0x00002b9f movq         %r12, %rsi
	0x4c, 0x8d, 0x64, 0x24, 0x40, //0x00002ba2 leaq         $64(%rsp), %r12
	0x48, 0x85, 0xc9, //0x00002ba7 testq        %rcx, %rcx
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00002baa movq         $16(%rsp), %r13
	0x0f, 0x84, 0xbb, 0xfd, 0xff, 0xff, //0x00002baf je           LBB0_551
	0x8a, 0x0e, //0x00002bb5 movb         (%rsi), %cl
	0x88, 0x0f, //0x00002bb7 movb         %cl, (%rdi)
	0x4c, 0x8d, 0x64, 0x24, 0x40, //0x00002bb9 leaq         $64(%rsp), %r12
	0xe9, 0xad, 0xfd, 0xff, 0xff, //0x00002bbe jmp          LBB0_551
	//0x00002bc3 LBB0_574
	0x49, 0x8b, 0x01, //0x00002bc3 movq         (%r9), %rax
	0x48, 0x0f, 0xbc, 0xcf, //0x00002bc6 bsfq         %rdi, %rcx
	0x4c, 0x29, 0xc1, //0x00002bca subq         %r8, %rcx
	0x4d, 0x89, 0xc8, //0x00002bcd movq         %r9, %r8
	0x4c, 0x8d, 0x34, 0x01, //0x00002bd0 leaq         (%rcx,%rax), %r14
	0x49, 0x83, 0xc6, 0x01, //0x00002bd4 addq         $1, %r14
	0x4d, 0x89, 0x75, 0x00, //0x00002bd8 movq         %r14, (%r13)
	0x49, 0x8b, 0x01, //0x00002bdc movq         (%r9), %rax
	0x49, 0x39, 0xc6, //0x00002bdf cmpq         %rax, %r14
	0x4c, 0x0f, 0x47, 0xf0, //0x00002be2 cmovaq       %rax, %r14
	0x4d, 0x89, 0x75, 0x00, //0x00002be6 movq         %r14, (%r13)
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002bea movq         $24(%rsp), %r10
	0xe9, 0xc3, 0xf8, 0xff, 0xff, //0x00002bef jmp          LBB0_487
	//0x00002bf4 LBB0_580
	0x66, 0x0f, 0xbc, 0xca, //0x00002bf4 bsfw         %dx, %cx
	0x44, 0x0f, 0xb7, 0xf1, //0x00002bf8 movzwl       %cx, %r14d
	0xe9, 0x83, 0xf7, 0xff, 0xff, //0x00002bfc jmp          LBB0_471
	//0x00002c01 LBB0_581
	0x4c, 0x29, 0xe2, //0x00002c01 subq         %r12, %rdx
	0x48, 0x01, 0xf2, //0x00002c04 addq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x00002c07 cmpq         %rax, %rdx
	0x0f, 0x82, 0x6a, 0xfa, 0xff, 0xff, //0x00002c0a jb           LBB0_517
	0xe9, 0x5c, 0x1f, 0x00, 0x00, //0x00002c10 jmp          LBB0_1023
	//0x00002c15 LBB0_582
	0x4c, 0x29, 0xe0, //0x00002c15 subq         %r12, %rax
	0x48, 0x01, 0xf0, //0x00002c18 addq         %rsi, %rax
	0x48, 0x39, 0xd0, //0x00002c1b cmpq         %rdx, %rax
	0x0f, 0x82, 0x26, 0xf6, 0xff, 0xff, //0x00002c1e jb           LBB0_452
	0xe9, 0x8e, 0xf8, 0xff, 0xff, //0x00002c24 jmp          LBB0_487
	//0x00002c29 LBB0_481
	0x4d, 0x85, 0xdb, //0x00002c29 testq        %r11, %r11
	0x0f, 0x85, 0x00, 0x01, 0x00, 0x00, //0x00002c2c jne          LBB0_592
	0x4b, 0x8d, 0x04, 0x29, //0x00002c32 leaq         (%r9,%r13), %rax
	0x48, 0x83, 0xc0, 0x01, //0x00002c36 addq         $1, %rax
	0x49, 0xf7, 0xd5, //0x00002c3a notq         %r13
	0x4d, 0x01, 0xc5, //0x00002c3d addq         %r8, %r13
	//0x00002c40 LBB0_483
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002c40 movq         $32(%rsp), %r11
	0x4d, 0x85, 0xed, //0x00002c45 testq        %r13, %r13
	0x4c, 0x8b, 0x44, 0x24, 0x28, //0x00002c48 movq         $40(%rsp), %r8
	0x0f, 0x8e, 0xd0, 0x00, 0x00, 0x00, //0x00002c4d jle          LBB0_591
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002c53 movq         $24(%rsp), %r10
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00002c58 jmp          LBB0_576
	//0x00002c5d LBB0_575
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00002c5d movq         $-2, %rcx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00002c64 movl         $2, %esi
	0x48, 0x01, 0xf0, //0x00002c69 addq         %rsi, %rax
	0x49, 0x01, 0xcd, //0x00002c6c addq         %rcx, %r13
	0x0f, 0x8e, 0x2d, 0x00, 0x00, 0x00, //0x00002c6f jle          LBB0_579
	//0x00002c75 LBB0_576
	0x0f, 0xb6, 0x08, //0x00002c75 movzbl       (%rax), %ecx
	0x80, 0xf9, 0x5c, //0x00002c78 cmpb         $92, %cl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x00002c7b je           LBB0_575
	0x80, 0xf9, 0x22, //0x00002c81 cmpb         $34, %cl
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x00002c84 je           LBB0_583
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002c8a movq         $-1, %rcx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00002c91 movl         $1, %esi
	0x48, 0x01, 0xf0, //0x00002c96 addq         %rsi, %rax
	0x49, 0x01, 0xcd, //0x00002c99 addq         %rcx, %r13
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x00002c9c jg           LBB0_576
	//0x00002ca2 LBB0_579
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00002ca2 movq         $16(%rsp), %r13
	0xe9, 0x0b, 0xf8, 0xff, 0xff, //0x00002ca7 jmp          LBB0_487
	//0x00002cac LBB0_583
	0x4c, 0x29, 0xe0, //0x00002cac subq         %r12, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00002caf addq         $1, %rax
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00002cb3 movq         $16(%rsp), %r13
	0xe9, 0xf3, 0xf7, 0xff, 0xff, //0x00002cb8 jmp          LBB0_486
	//0x00002cbd LBB0_584
	0x4c, 0x01, 0xe2, //0x00002cbd addq         %r12, %rdx
	0x48, 0x85, 0xc9, //0x00002cc0 testq        %rcx, %rcx
	0x0f, 0x85, 0x3e, 0xf9, 0xff, 0xff, //0x00002cc3 jne          LBB0_510
	0xe9, 0x73, 0xf9, 0xff, 0xff, //0x00002cc9 jmp          LBB0_515
	//0x00002cce LBB0_585
	0x4c, 0x01, 0xe0, //0x00002cce addq         %r12, %rax
	0x48, 0x85, 0xc9, //0x00002cd1 testq        %rcx, %rcx
	0x0f, 0x85, 0x0d, 0xf5, 0xff, 0xff, //0x00002cd4 jne          LBB0_445
	0xe9, 0x3f, 0xf5, 0xff, 0xff, //0x00002cda jmp          LBB0_450
	//0x00002cdf LBB0_586
	0x4d, 0x01, 0xe6, //0x00002cdf addq         %r12, %r14
	0x48, 0x83, 0xf9, 0x10, //0x00002ce2 cmpq         $16, %rcx
	0x0f, 0x83, 0xf6, 0xf5, 0xff, 0xff, //0x00002ce6 jae          LBB0_459
	0xe9, 0x3a, 0xf6, 0xff, 0xff, //0x00002cec jmp          LBB0_462
	//0x00002cf1 LBB0_587
	0x4d, 0x29, 0xe6, //0x00002cf1 subq         %r12, %r14
	0x49, 0x01, 0xc6, //0x00002cf4 addq         %rax, %r14
	//0x00002cf7 LBB0_588
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00002cf7 movq         $16(%rsp), %r13
	0x4d, 0x89, 0x75, 0x00, //0x00002cfc movq         %r14, (%r13)
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002d00 movq         $24(%rsp), %r10
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002d05 movq         $32(%rsp), %r11
	0xe9, 0xa8, 0xf7, 0xff, 0xff, //0x00002d0a jmp          LBB0_487
	//0x00002d0f LBB0_589
	0x4b, 0x8d, 0x04, 0x34, //0x00002d0f leaq         (%r12,%r14), %rax
	0xe9, 0x28, 0xff, 0xff, 0xff, //0x00002d13 jmp          LBB0_483
	//0x00002d18 LBB0_590
	0x4d, 0x89, 0xc8, //0x00002d18 movq         %r9, %r8
	0x4d, 0x8b, 0x31, //0x00002d1b movq         (%r9), %r14
	0xe9, 0xd4, 0xff, 0xff, 0xff, //0x00002d1e jmp          LBB0_588
	//0x00002d23 LBB0_591
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00002d23 movq         $16(%rsp), %r13
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00002d28 movq         $24(%rsp), %r10
	0xe9, 0x85, 0xf7, 0xff, 0xff, //0x00002d2d jmp          LBB0_487
	//0x00002d32 LBB0_592
	0x49, 0x8d, 0x40, 0xff, //0x00002d32 leaq         $-1(%r8), %rax
	0x4c, 0x39, 0xe8, //0x00002d36 cmpq         %r13, %rax
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x00002d39 jne          LBB0_595
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00002d3f movq         $16(%rsp), %r13
	0xe9, 0x37, 0xf7, 0xff, 0xff, //0x00002d44 jmp          LBB0_594
	//0x00002d49 LBB0_595
	0x4b, 0x8d, 0x04, 0x29, //0x00002d49 leaq         (%r9,%r13), %rax
	0x48, 0x83, 0xc0, 0x02, //0x00002d4d addq         $2, %rax
	0x4d, 0x29, 0xe8, //0x00002d51 subq         %r13, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00002d54 addq         $-2, %r8
	0x4d, 0x89, 0xc5, //0x00002d58 movq         %r8, %r13
	0xe9, 0xe0, 0xfe, 0xff, 0xff, //0x00002d5b jmp          LBB0_483
	//0x00002d60 .p2align 4, 0x90
	//0x00002d60 LBB0_519
	0x48, 0x8b, 0x44, 0x24, 0x30, //0x00002d60 movq         $48(%rsp), %rax
	0x48, 0x83, 0xc0, 0x10, //0x00002d65 addq         $16, %rax
	0x4c, 0x89, 0xf3, //0x00002d69 movq         %r14, %rbx
	0x48, 0x89, 0x44, 0x24, 0x30, //0x00002d6c movq         %rax, $48(%rsp)
	0x48, 0x3b, 0x84, 0x24, 0xa0, 0x00, 0x00, 0x00, //0x00002d71 cmpq         $160(%rsp), %rax
	0x0f, 0x85, 0xb9, 0xd5, 0xff, 0xff, //0x00002d79 jne          LBB0_2
	0xe9, 0x50, 0x00, 0x00, 0x00, //0x00002d7f jmp          LBB0_520
	//0x00002d84 LBB0_33
	0x4c, 0x29, 0xe0, //0x00002d84 subq         %r12, %rax
	0x48, 0x01, 0xf0, //0x00002d87 addq         %rsi, %rax
	0x4d, 0x89, 0xc8, //0x00002d8a movq         %r9, %r8
	0x48, 0x39, 0xd0, //0x00002d8d cmpq         %rdx, %rax
	0x0f, 0x82, 0x84, 0xd7, 0xff, 0xff, //0x00002d90 jb           LBB0_32
	0xe9, 0x27, 0xd7, 0xff, 0xff, //0x00002d96 jmp          LBB0_34
	//0x00002d9b LBB0_596
	0x4c, 0x29, 0xe2, //0x00002d9b subq         %r12, %rdx
	0x48, 0x01, 0xf2, //0x00002d9e addq         %rsi, %rdx
	0x48, 0x39, 0xc2, //0x00002da1 cmpq         %rax, %rdx
	0x0f, 0x82, 0xc7, 0xf2, 0xff, 0xff, //0x00002da4 jb           LBB0_420
	0xe9, 0xd5, 0xf2, 0xff, 0xff, //0x00002daa jmp          LBB0_421
	//0x00002daf LBB0_597
	0x4d, 0x01, 0xe6, //0x00002daf addq         %r12, %r14
	0x4c, 0x89, 0xf0, //0x00002db2 movq         %r14, %rax
	0x48, 0x85, 0xc9, //0x00002db5 testq        %rcx, %rcx
	0x0f, 0x85, 0xb9, 0xd6, 0xff, 0xff, //0x00002db8 jne          LBB0_25
	0xe9, 0xf3, 0xd6, 0xff, 0xff, //0x00002dbe jmp          LBB0_30
	//0x00002dc3 LBB0_598
	0x4c, 0x01, 0xe2, //0x00002dc3 addq         %r12, %rdx
	0x48, 0x85, 0xc9, //0x00002dc6 testq        %rcx, %rcx
	0x0f, 0x85, 0x38, 0xf2, 0xff, 0xff, //0x00002dc9 jne          LBB0_412
	0xe9, 0x6a, 0xf2, 0xff, 0xff, //0x00002dcf jmp          LBB0_417
	//0x00002dd4 LBB0_520
	0x4d, 0x85, 0xdb, //0x00002dd4 testq        %r11, %r11
	0x0f, 0x84, 0x94, 0x00, 0x00, 0x00, //0x00002dd7 je           LBB0_599
	0x49, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00002ddd movabsq      $9223372036854775806, %r9
	0xc5, 0xfa, 0x6f, 0x05, 0x21, 0xd4, 0xff, 0xff, //0x00002de7 vmovdqu      $-11231(%rip), %xmm0  /* LCPI0_12+0(%rip) */
	0xc4, 0xc1, 0x7a, 0x7f, 0x03, //0x00002def vmovdqu      %xmm0, (%r11)
	0x4d, 0x8b, 0x65, 0x00, //0x00002df4 movq         (%r13), %r12
	0x48, 0xc7, 0x44, 0x24, 0x38, 0xff, 0xff, 0xff, 0xff, //0x00002df8 movq         $-1, $56(%rsp)
	0x41, 0xb8, 0x01, 0x00, 0x00, 0x00, //0x00002e01 movl         $1, %r8d
	0xc5, 0xfe, 0x6f, 0x2d, 0xf1, 0xd1, 0xff, 0xff, //0x00002e07 vmovdqu      $-11791(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x69, 0xd2, 0xff, 0xff, //0x00002e0f vmovdqu      $-11671(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x81, 0xd2, 0xff, 0xff, //0x00002e17 vmovdqu      $-11647(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xf9, 0xd2, 0xff, 0xff, //0x00002e1f vmovdqu      $-11527(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x11, 0xd3, 0xff, 0xff, //0x00002e27 vmovdqu      $-11503(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x09, 0xd2, 0xff, 0xff, //0x00002e2f vmovdqu      $-11767(%rip), %ymm10  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x21, 0xd3, 0xff, 0xff, //0x00002e37 vmovdqu      $-11487(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x39, 0xd3, 0xff, 0xff, //0x00002e3f vmovdqu      $-11463(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x51, 0xd3, 0xff, 0xff, //0x00002e47 vmovdqu      $-11439(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x69, 0xd3, 0xff, 0xff, //0x00002e4f vmovdqu      $-11415(%rip), %ymm14  /* LCPI0_18+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x3d, 0xc1, 0xd3, 0xff, 0xff, //0x00002e57 vmovdqu      $-11327(%rip), %xmm15  /* LCPI0_19+0(%rip) */
	0xe9, 0x70, 0x02, 0x00, 0x00, //0x00002e5f jmp          LBB0_636
	//0x00002e64 LBB0_234
	0x3c, 0x7d, //0x00002e64 cmpb         $125, %al
	0x0f, 0x84, 0xa1, 0x01, 0x00, 0x00, //0x00002e66 je           LBB0_629
	0xe9, 0x00, 0x1d, 0x00, 0x00, //0x00002e6c jmp          LBB0_1023
	//0x00002e71 LBB0_599
	0x4d, 0x8b, 0x1a, //0x00002e71 movq         (%r10), %r11
	0x49, 0x8b, 0x52, 0x08, //0x00002e74 movq         $8(%r10), %rdx
	0x49, 0x8b, 0x7d, 0x00, //0x00002e78 movq         (%r13), %rdi
	0x48, 0x39, 0xd7, //0x00002e7c cmpq         %rdx, %rdi
	0x0f, 0x83, 0x26, 0x00, 0x00, 0x00, //0x00002e7f jae          LBB0_604
	0x41, 0x8a, 0x04, 0x3b, //0x00002e85 movb         (%r11,%rdi), %al
	0x3c, 0x0d, //0x00002e89 cmpb         $13, %al
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00002e8b je           LBB0_604
	0x3c, 0x20, //0x00002e91 cmpb         $32, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002e93 je           LBB0_604
	0x04, 0xf5, //0x00002e99 addb         $-11, %al
	0x3c, 0xfe, //0x00002e9b cmpb         $-2, %al
	0x0f, 0x83, 0x08, 0x00, 0x00, 0x00, //0x00002e9d jae          LBB0_604
	0x48, 0x89, 0xf8, //0x00002ea3 movq         %rdi, %rax
	0xe9, 0x48, 0x1b, 0x00, 0x00, //0x00002ea6 jmp          LBB0_999
	//0x00002eab LBB0_604
	0x48, 0x8d, 0x47, 0x01, //0x00002eab leaq         $1(%rdi), %rax
	0x48, 0x39, 0xd0, //0x00002eaf cmpq         %rdx, %rax
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00002eb2 jae          LBB0_608
	0x41, 0x8a, 0x0c, 0x03, //0x00002eb8 movb         (%r11,%rax), %cl
	0x80, 0xf9, 0x0d, //0x00002ebc cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00002ebf je           LBB0_608
	0x80, 0xf9, 0x20, //0x00002ec5 cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00002ec8 je           LBB0_608
	0x80, 0xc1, 0xf5, //0x00002ece addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00002ed1 cmpb         $-2, %cl
	0x0f, 0x82, 0x19, 0x1b, 0x00, 0x00, //0x00002ed4 jb           LBB0_999
	//0x00002eda LBB0_608
	0x48, 0x8d, 0x47, 0x02, //0x00002eda leaq         $2(%rdi), %rax
	0x48, 0x39, 0xd0, //0x00002ede cmpq         %rdx, %rax
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00002ee1 jae          LBB0_612
	0x41, 0x8a, 0x0c, 0x03, //0x00002ee7 movb         (%r11,%rax), %cl
	0x80, 0xf9, 0x0d, //0x00002eeb cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00002eee je           LBB0_612
	0x80, 0xf9, 0x20, //0x00002ef4 cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00002ef7 je           LBB0_612
	0x80, 0xc1, 0xf5, //0x00002efd addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00002f00 cmpb         $-2, %cl
	0x0f, 0x82, 0xea, 0x1a, 0x00, 0x00, //0x00002f03 jb           LBB0_999
	//0x00002f09 LBB0_612
	0x48, 0x8d, 0x47, 0x03, //0x00002f09 leaq         $3(%rdi), %rax
	0x48, 0x39, 0xd0, //0x00002f0d cmpq         %rdx, %rax
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00002f10 jae          LBB0_616
	0x41, 0x8a, 0x0c, 0x03, //0x00002f16 movb         (%r11,%rax), %cl
	0x80, 0xf9, 0x0d, //0x00002f1a cmpb         $13, %cl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00002f1d je           LBB0_616
	0x80, 0xf9, 0x20, //0x00002f23 cmpb         $32, %cl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x00002f26 je           LBB0_616
	0x80, 0xc1, 0xf5, //0x00002f2c addb         $-11, %cl
	0x80, 0xf9, 0xfe, //0x00002f2f cmpb         $-2, %cl
	0x0f, 0x82, 0xbb, 0x1a, 0x00, 0x00, //0x00002f32 jb           LBB0_999
	//0x00002f38 LBB0_616
	0x48, 0x8d, 0x47, 0x04, //0x00002f38 leaq         $4(%rdi), %rax
	0x48, 0x89, 0xd6, //0x00002f3c movq         %rdx, %rsi
	0x48, 0x29, 0xc6, //0x00002f3f subq         %rax, %rsi
	0x0f, 0x86, 0x7b, 0x1a, 0x00, 0x00, //0x00002f42 jbe          LBB0_997
	0x48, 0x83, 0xfe, 0x20, //0x00002f48 cmpq         $32, %rsi
	0x0f, 0x82, 0x3f, 0x25, 0x00, 0x00, //0x00002f4c jb           LBB0_1125
	0x48, 0xc7, 0xc6, 0xfc, 0xff, 0xff, 0xff, //0x00002f52 movq         $-4, %rsi
	0x48, 0x29, 0xfe, //0x00002f59 subq         %rdi, %rsi
	0xc5, 0xfe, 0x6f, 0x05, 0x9c, 0xd0, 0xff, 0xff, //0x00002f5c vmovdqu      $-12132(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002f64 .p2align 4, 0x90
	//0x00002f70 LBB0_619
	0xc4, 0xc1, 0x7e, 0x6f, 0x0c, 0x03, //0x00002f70 vmovdqu      (%r11,%rax), %ymm1
	0xc4, 0xe2, 0x7d, 0x00, 0xd1, //0x00002f76 vpshufb      %ymm1, %ymm0, %ymm2
	0xc5, 0xf5, 0xf8, 0xda, //0x00002f7b vpsubb       %ymm2, %ymm1, %ymm3
	0xc4, 0xe2, 0x7d, 0x17, 0xdb, //0x00002f7f vptest       %ymm3, %ymm3
	0x0f, 0x85, 0x49, 0x1a, 0x00, 0x00, //0x00002f84 jne          LBB0_998
	0x48, 0x83, 0xc0, 0x20, //0x00002f8a addq         $32, %rax
	0x48, 0x8d, 0x0c, 0x32, //0x00002f8e leaq         (%rdx,%rsi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00002f92 addq         $-32, %rcx
	0x48, 0x83, 0xc6, 0xe0, //0x00002f96 addq         $-32, %rsi
	0x48, 0x83, 0xf9, 0x1f, //0x00002f9a cmpq         $31, %rcx
	0x0f, 0x87, 0xcc, 0xff, 0xff, 0xff, //0x00002f9e ja           LBB0_619
	0x4c, 0x89, 0xd8, //0x00002fa4 movq         %r11, %rax
	0x48, 0x29, 0xf0, //0x00002fa7 subq         %rsi, %rax
	0x48, 0x01, 0xd6, //0x00002faa addq         %rdx, %rsi
	0x48, 0x85, 0xf6, //0x00002fad testq        %rsi, %rsi
	0x0f, 0x84, 0x37, 0x00, 0x00, 0x00, //0x00002fb0 je           LBB0_627
	//0x00002fb6 LBB0_622
	0x4c, 0x8d, 0x04, 0x30, //0x00002fb6 leaq         (%rax,%rsi), %r8
	0x31, 0xc9, //0x00002fba xorl         %ecx, %ecx
	0x48, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002fbc movabsq      $4294977024, %rbx
	//0x00002fc6 LBB0_623
	0x0f, 0xbe, 0x3c, 0x08, //0x00002fc6 movsbl       (%rax,%rcx), %edi
	0x83, 0xff, 0x20, //0x00002fca cmpl         $32, %edi
	0x0f, 0x87, 0x85, 0x24, 0x00, 0x00, //0x00002fcd ja           LBB0_1123
	0x48, 0x0f, 0xa3, 0xfb, //0x00002fd3 btq          %rdi, %rbx
	0x0f, 0x83, 0x7b, 0x24, 0x00, 0x00, //0x00002fd7 jae          LBB0_1123
	0x48, 0x83, 0xc1, 0x01, //0x00002fdd addq         $1, %rcx
	0x48, 0x39, 0xce, //0x00002fe1 cmpq         %rcx, %rsi
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00002fe4 jne          LBB0_623
	0x4c, 0x89, 0xc0, //0x00002fea movq         %r8, %rax
	//0x00002fed LBB0_627
	0x4c, 0x29, 0xd8, //0x00002fed subq         %r11, %rax
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00002ff0 movq         $-1, %rbx
	0x48, 0x39, 0xd0, //0x00002ff7 cmpq         %rdx, %rax
	0x0f, 0x82, 0xf3, 0x19, 0x00, 0x00, //0x00002ffa jb           LBB0_999
	0xe9, 0x7b, 0x1b, 0x00, 0x00, //0x00003000 jmp          LBB0_1025
	//0x00003005 LBB0_628
	0x3c, 0x5d, //0x00003005 cmpb         $93, %al
	0x0f, 0x85, 0x64, 0x1b, 0x00, 0x00, //0x00003007 jne          LBB0_1023
	//0x0000300d LBB0_629
	0x49, 0x83, 0xc6, 0xff, //0x0000300d addq         $-1, %r14
	0x4d, 0x89, 0x75, 0x00, //0x00003011 movq         %r14, (%r13)
	0x48, 0xc7, 0xc3, 0xdf, 0xff, 0xff, 0xff, //0x00003015 movq         $-33, %rbx
	0xe9, 0x5f, 0x1b, 0x00, 0x00, //0x0000301c jmp          LBB0_1025
	//0x00003021 LBB0_1022
	0x49, 0x89, 0xd6, //0x00003021 movq         %rdx, %r14
	0xe9, 0x48, 0x1b, 0x00, 0x00, //0x00003024 jmp          LBB0_1023
	//0x00003029 LBB0_630
	0x49, 0xf7, 0xdd, //0x00003029 negq         %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000302c movq         $32(%rsp), %r11
	0xc5, 0xfe, 0x6f, 0x2d, 0xc7, 0xcf, 0xff, 0xff, //0x00003031 vmovdqu      $-12345(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x3f, 0xd0, 0xff, 0xff, //0x00003039 vmovdqu      $-12225(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x57, 0xd0, 0xff, 0xff, //0x00003041 vmovdqu      $-12201(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xcf, 0xd0, 0xff, 0xff, //0x00003049 vmovdqu      $-12081(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0xe7, 0xd0, 0xff, 0xff, //0x00003051 vmovdqu      $-12057(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xdf, 0xcf, 0xff, 0xff, //0x00003059 vmovdqu      $-12321(%rip), %ymm10  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xf7, 0xd0, 0xff, 0xff, //0x00003061 vmovdqu      $-12041(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x0f, 0xd1, 0xff, 0xff, //0x00003069 vmovdqu      $-12017(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x27, 0xd1, 0xff, 0xff, //0x00003071 vmovdqu      $-11993(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x3f, 0xd1, 0xff, 0xff, //0x00003079 vmovdqu      $-11969(%rip), %ymm14  /* LCPI0_18+0(%rip) */
	//0x00003081 LBB0_631
	0x4d, 0x85, 0xed, //0x00003081 testq        %r13, %r13
	0x0f, 0x88, 0x4c, 0x1b, 0x00, 0x00, //0x00003084 js           LBB0_1028
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x0000308a movq         $16(%rsp), %rax
	0x4c, 0x8b, 0x20, //0x0000308f movq         (%rax), %r12
	//0x00003092 LBB0_633
	0x4d, 0x01, 0xec, //0x00003092 addq         %r13, %r12
	0x49, 0x83, 0xc4, 0xff, //0x00003095 addq         $-1, %r12
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00003099 movq         $16(%rsp), %r13
	0x4d, 0x89, 0x65, 0x00, //0x0000309e movq         %r12, (%r13)
	0x4c, 0x89, 0xf3, //0x000030a2 movq         %r14, %rbx
	0x4d, 0x39, 0xce, //0x000030a5 cmpq         %r9, %r14
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000030a8 movq         $24(%rsp), %r10
	0x0f, 0x87, 0xcd, 0x1a, 0x00, 0x00, //0x000030ad ja           LBB0_1025
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000030b3 .p2align 4, 0x90
	//0x000030c0 LBB0_634
	0x49, 0x8b, 0x33, //0x000030c0 movq         (%r11), %rsi
	0x49, 0x89, 0xf0, //0x000030c3 movq         %rsi, %r8
	0x48, 0x8b, 0x5c, 0x24, 0x38, //0x000030c6 movq         $56(%rsp), %rbx
	0x48, 0x85, 0xf6, //0x000030cb testq        %rsi, %rsi
	0x0f, 0x84, 0xac, 0x1a, 0x00, 0x00, //0x000030ce je           LBB0_1025
	//0x000030d4 LBB0_636
	0x4d, 0x8b, 0x3a, //0x000030d4 movq         (%r10), %r15
	0x49, 0x8b, 0x5a, 0x08, //0x000030d7 movq         $8(%r10), %rbx
	0x49, 0x39, 0xdc, //0x000030db cmpq         %rbx, %r12
	0x0f, 0x83, 0x2c, 0x00, 0x00, 0x00, //0x000030de jae          LBB0_641
	0x43, 0x8a, 0x04, 0x27, //0x000030e4 movb         (%r15,%r12), %al
	0x3c, 0x0d, //0x000030e8 cmpb         $13, %al
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x000030ea je           LBB0_641
	0x3c, 0x20, //0x000030f0 cmpb         $32, %al
	0x0f, 0x84, 0x18, 0x00, 0x00, 0x00, //0x000030f2 je           LBB0_641
	0x04, 0xf5, //0x000030f8 addb         $-11, %al
	0x3c, 0xfe, //0x000030fa cmpb         $-2, %al
	0x0f, 0x83, 0x0e, 0x00, 0x00, 0x00, //0x000030fc jae          LBB0_641
	0x4d, 0x89, 0xe6, //0x00003102 movq         %r12, %r14
	0xe9, 0x80, 0x01, 0x00, 0x00, //0x00003105 jmp          LBB0_666
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000310a .p2align 4, 0x90
	//0x00003110 LBB0_641
	0x4d, 0x8d, 0x74, 0x24, 0x01, //0x00003110 leaq         $1(%r12), %r14
	0x49, 0x39, 0xde, //0x00003115 cmpq         %rbx, %r14
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00003118 jae          LBB0_645
	0x43, 0x8a, 0x14, 0x37, //0x0000311e movb         (%r15,%r14), %dl
	0x80, 0xfa, 0x0d, //0x00003122 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00003125 je           LBB0_645
	0x80, 0xfa, 0x20, //0x0000312b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000312e je           LBB0_645
	0x80, 0xc2, 0xf5, //0x00003134 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00003137 cmpb         $-2, %dl
	0x0f, 0x82, 0x4a, 0x01, 0x00, 0x00, //0x0000313a jb           LBB0_666
	//0x00003140 .p2align 4, 0x90
	//0x00003140 LBB0_645
	0x4d, 0x8d, 0x74, 0x24, 0x02, //0x00003140 leaq         $2(%r12), %r14
	0x49, 0x39, 0xde, //0x00003145 cmpq         %rbx, %r14
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00003148 jae          LBB0_649
	0x43, 0x8a, 0x14, 0x37, //0x0000314e movb         (%r15,%r14), %dl
	0x80, 0xfa, 0x0d, //0x00003152 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00003155 je           LBB0_649
	0x80, 0xfa, 0x20, //0x0000315b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000315e je           LBB0_649
	0x80, 0xc2, 0xf5, //0x00003164 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00003167 cmpb         $-2, %dl
	0x0f, 0x82, 0x1a, 0x01, 0x00, 0x00, //0x0000316a jb           LBB0_666
	//0x00003170 .p2align 4, 0x90
	//0x00003170 LBB0_649
	0x4d, 0x8d, 0x74, 0x24, 0x03, //0x00003170 leaq         $3(%r12), %r14
	0x49, 0x39, 0xde, //0x00003175 cmpq         %rbx, %r14
	0x0f, 0x83, 0x22, 0x00, 0x00, 0x00, //0x00003178 jae          LBB0_653
	0x43, 0x8a, 0x14, 0x37, //0x0000317e movb         (%r15,%r14), %dl
	0x80, 0xfa, 0x0d, //0x00003182 cmpb         $13, %dl
	0x0f, 0x84, 0x15, 0x00, 0x00, 0x00, //0x00003185 je           LBB0_653
	0x80, 0xfa, 0x20, //0x0000318b cmpb         $32, %dl
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x0000318e je           LBB0_653
	0x80, 0xc2, 0xf5, //0x00003194 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00003197 cmpb         $-2, %dl
	0x0f, 0x82, 0xea, 0x00, 0x00, 0x00, //0x0000319a jb           LBB0_666
	//0x000031a0 .p2align 4, 0x90
	//0x000031a0 LBB0_653
	0x4d, 0x8d, 0x74, 0x24, 0x04, //0x000031a0 leaq         $4(%r12), %r14
	0x48, 0x89, 0xde, //0x000031a5 movq         %rbx, %rsi
	0x4c, 0x29, 0xf6, //0x000031a8 subq         %r14, %rsi
	0x0f, 0x86, 0xad, 0x19, 0x00, 0x00, //0x000031ab jbe          LBB0_1019
	0x48, 0x83, 0xfe, 0x20, //0x000031b1 cmpq         $32, %rsi
	0x0f, 0x82, 0xd6, 0x11, 0x00, 0x00, //0x000031b5 jb           LBB0_917
	0x48, 0xc7, 0xc6, 0xfc, 0xff, 0xff, 0xff, //0x000031bb movq         $-4, %rsi
	0x4c, 0x29, 0xe6, //0x000031c2 subq         %r12, %rsi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000031c5 .p2align 4, 0x90
	//0x000031d0 LBB0_656
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x37, //0x000031d0 vmovdqu      (%r15,%r14), %ymm0
	0xc4, 0xe2, 0x55, 0x00, 0xc8, //0x000031d6 vpshufb      %ymm0, %ymm5, %ymm1
	0xc5, 0xfd, 0xf8, 0xd1, //0x000031db vpsubb       %ymm1, %ymm0, %ymm2
	0xc4, 0xe2, 0x7d, 0x17, 0xd2, //0x000031df vptest       %ymm2, %ymm2
	0x0f, 0x85, 0x86, 0x00, 0x00, 0x00, //0x000031e4 jne          LBB0_665
	0x49, 0x83, 0xc6, 0x20, //0x000031ea addq         $32, %r14
	0x48, 0x8d, 0x0c, 0x33, //0x000031ee leaq         (%rbx,%rsi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x000031f2 addq         $-32, %rcx
	0x48, 0x83, 0xc6, 0xe0, //0x000031f6 addq         $-32, %rsi
	0x48, 0x83, 0xf9, 0x1f, //0x000031fa cmpq         $31, %rcx
	0x0f, 0x87, 0xcc, 0xff, 0xff, 0xff, //0x000031fe ja           LBB0_656
	0x4d, 0x89, 0xfe, //0x00003204 movq         %r15, %r14
	0x49, 0x29, 0xf6, //0x00003207 subq         %rsi, %r14
	0x48, 0x01, 0xde, //0x0000320a addq         %rbx, %rsi
	0x48, 0x85, 0xf6, //0x0000320d testq        %rsi, %rsi
	0x0f, 0x84, 0x3c, 0x00, 0x00, 0x00, //0x00003210 je           LBB0_664
	//0x00003216 LBB0_659
	0x49, 0x8d, 0x3c, 0x36, //0x00003216 leaq         (%r14,%rsi), %rdi
	0x31, 0xc9, //0x0000321a xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, //0x0000321c .p2align 4, 0x90
	//0x00003220 LBB0_660
	0x41, 0x0f, 0xbe, 0x14, 0x0e, //0x00003220 movsbl       (%r14,%rcx), %edx
	0x83, 0xfa, 0x20, //0x00003225 cmpl         $32, %edx
	0x0f, 0x87, 0x7e, 0x10, 0x00, 0x00, //0x00003228 ja           LBB0_909
	0x48, 0xb8, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000322e movabsq      $4294977024, %rax
	0x48, 0x0f, 0xa3, 0xd0, //0x00003238 btq          %rdx, %rax
	0x0f, 0x83, 0x6a, 0x10, 0x00, 0x00, //0x0000323c jae          LBB0_909
	0x48, 0x83, 0xc1, 0x01, //0x00003242 addq         $1, %rcx
	0x48, 0x39, 0xce, //0x00003246 cmpq         %rcx, %rsi
	0x0f, 0x85, 0xd1, 0xff, 0xff, 0xff, //0x00003249 jne          LBB0_660
	0x49, 0x89, 0xfe, //0x0000324f movq         %rdi, %r14
	//0x00003252 LBB0_664
	0x4d, 0x29, 0xfe, //0x00003252 subq         %r15, %r14
	0x49, 0x39, 0xde, //0x00003255 cmpq         %rbx, %r14
	0x0f, 0x82, 0x2c, 0x00, 0x00, 0x00, //0x00003258 jb           LBB0_666
	0xe9, 0xff, 0x18, 0x00, 0x00, //0x0000325e jmp          LBB0_1020
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003263 .p2align 4, 0x90
	//0x00003270 LBB0_665
	0xc5, 0xfd, 0x74, 0xc1, //0x00003270 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00003274 vpmovmskb    %ymm0, %eax
	0xf7, 0xd0, //0x00003278 notl         %eax
	0x44, 0x0f, 0xbc, 0xf0, //0x0000327a bsfl         %eax, %r14d
	0x49, 0x29, 0xf6, //0x0000327e subq         %rsi, %r14
	0x49, 0x39, 0xde, //0x00003281 cmpq         %rbx, %r14
	0x0f, 0x83, 0xd8, 0x18, 0x00, 0x00, //0x00003284 jae          LBB0_1020
	//0x0000328a LBB0_666
	0x4d, 0x8d, 0x66, 0x01, //0x0000328a leaq         $1(%r14), %r12
	0x4d, 0x89, 0x65, 0x00, //0x0000328e movq         %r12, (%r13)
	0x43, 0x0f, 0xbe, 0x3c, 0x37, //0x00003292 movsbl       (%r15,%r14), %edi
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00003297 movq         $-1, %rbx
	0x85, 0xff, //0x0000329e testl        %edi, %edi
	0x0f, 0x84, 0xda, 0x18, 0x00, 0x00, //0x000032a0 je           LBB0_1025
	0x49, 0x8d, 0x70, 0xff, //0x000032a6 leaq         $-1(%r8), %rsi
	0x43, 0x8b, 0x14, 0xc3, //0x000032aa movl         (%r11,%r8,8), %edx
	0x48, 0x8b, 0x44, 0x24, 0x38, //0x000032ae movq         $56(%rsp), %rax
	0x48, 0x83, 0xf8, 0xff, //0x000032b3 cmpq         $-1, %rax
	0x49, 0x0f, 0x44, 0xc6, //0x000032b7 cmoveq       %r14, %rax
	0x48, 0x89, 0x44, 0x24, 0x38, //0x000032bb movq         %rax, $56(%rsp)
	0x83, 0xc2, 0xff, //0x000032c0 addl         $-1, %edx
	0x83, 0xfa, 0x05, //0x000032c3 cmpl         $5, %edx
	0x0f, 0x87, 0xe3, 0x01, 0x00, 0x00, //0x000032c6 ja           LBB0_698
	0x48, 0x8d, 0x05, 0x4d, 0x26, 0x00, 0x00, //0x000032cc leaq         $9805(%rip), %rax  /* LJTI0_2+0(%rip) */
	0x48, 0x63, 0x14, 0x90, //0x000032d3 movslq       (%rax,%rdx,4), %rdx
	0x48, 0x01, 0xc2, //0x000032d7 addq         %rax, %rdx
	0xff, 0xe2, //0x000032da jmpq         *%rdx
	//0x000032dc LBB0_669
	0x83, 0xff, 0x2c, //0x000032dc cmpl         $44, %edi
	0x0f, 0x84, 0xd0, 0x05, 0x00, 0x00, //0x000032df je           LBB0_756
	0x83, 0xff, 0x5d, //0x000032e5 cmpl         $93, %edi
	0x0f, 0x84, 0xae, 0x05, 0x00, 0x00, //0x000032e8 je           LBB0_671
	0xe9, 0x86, 0x18, 0x00, 0x00, //0x000032ee jmp          LBB0_1024
	//0x000032f3 LBB0_672
	0x40, 0x80, 0xff, 0x5d, //0x000032f3 cmpb         $93, %dil
	0x0f, 0x84, 0x9f, 0x05, 0x00, 0x00, //0x000032f7 je           LBB0_671
	0x4b, 0xc7, 0x04, 0xc3, 0x01, 0x00, 0x00, 0x00, //0x000032fd movq         $1, (%r11,%r8,8)
	0x83, 0xff, 0x7b, //0x00003305 cmpl         $123, %edi
	0x0f, 0x86, 0xad, 0x01, 0x00, 0x00, //0x00003308 jbe          LBB0_674
	0xe9, 0x66, 0x18, 0x00, 0x00, //0x0000330e jmp          LBB0_1024
	//0x00003313 LBB0_675
	0x40, 0x80, 0xff, 0x22, //0x00003313 cmpb         $34, %dil
	0x0f, 0x85, 0x5c, 0x18, 0x00, 0x00, //0x00003317 jne          LBB0_1024
	0x4b, 0xc7, 0x04, 0xc3, 0x04, 0x00, 0x00, 0x00, //0x0000331d movq         $4, (%r11,%r8,8)
	0x4d, 0x8b, 0x4a, 0x08, //0x00003325 movq         $8(%r10), %r9
	0x4c, 0x89, 0xca, //0x00003329 movq         %r9, %rdx
	0x4c, 0x29, 0xe2, //0x0000332c subq         %r12, %rdx
	0x0f, 0x84, 0x1b, 0x21, 0x00, 0x00, //0x0000332f je           LBB0_1133
	0x48, 0x83, 0xfa, 0x40, //0x00003335 cmpq         $64, %rdx
	0x0f, 0x82, 0x1d, 0x11, 0x00, 0x00, //0x00003339 jb           LBB0_926
	0x48, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x0000333f movq         $-2, %rbx
	0x4c, 0x29, 0xf3, //0x00003346 subq         %r14, %rbx
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003349 movq         $-1, %r8
	0x45, 0x31, 0xd2, //0x00003350 xorl         %r10d, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003353 .p2align 4, 0x90
	//0x00003360 LBB0_679
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x27, //0x00003360 vmovdqu      (%r15,%r12), %ymm0
	0xc4, 0x81, 0x7e, 0x6f, 0x4c, 0x27, 0x20, //0x00003366 vmovdqu      $32(%r15,%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x0000336d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00003371 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00003375 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00003379 vpmovmskb    %ymm2, %ecx
	0xc5, 0xfd, 0x74, 0xc7, //0x0000337d vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00003381 vpmovmskb    %ymm0, %esi
	0xc5, 0xf5, 0x74, 0xc7, //0x00003385 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00003389 vpmovmskb    %ymm0, %eax
	0x48, 0xc1, 0xe1, 0x20, //0x0000338d shlq         $32, %rcx
	0x48, 0x09, 0xcf, //0x00003391 orq          %rcx, %rdi
	0x48, 0xc1, 0xe0, 0x20, //0x00003394 shlq         $32, %rax
	0x48, 0x09, 0xc6, //0x00003398 orq          %rax, %rsi
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x0000339b jne          LBB0_688
	0x4d, 0x85, 0xd2, //0x000033a1 testq        %r10, %r10
	0x0f, 0x85, 0x38, 0x00, 0x00, 0x00, //0x000033a4 jne          LBB0_690
	0x45, 0x31, 0xd2, //0x000033aa xorl         %r10d, %r10d
	0x48, 0x85, 0xff, //0x000033ad testq        %rdi, %rdi
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x000033b0 jne          LBB0_691
	//0x000033b6 LBB0_682
	0x48, 0x83, 0xc2, 0xc0, //0x000033b6 addq         $-64, %rdx
	0x48, 0x83, 0xc3, 0xc0, //0x000033ba addq         $-64, %rbx
	0x49, 0x83, 0xc4, 0x40, //0x000033be addq         $64, %r12
	0x48, 0x83, 0xfa, 0x3f, //0x000033c2 cmpq         $63, %rdx
	0x0f, 0x87, 0x94, 0xff, 0xff, 0xff, //0x000033c6 ja           LBB0_679
	0xe9, 0x25, 0x0d, 0x00, 0x00, //0x000033cc jmp          LBB0_683
	//0x000033d1 LBB0_688
	0x49, 0x83, 0xf8, 0xff, //0x000033d1 cmpq         $-1, %r8
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x000033d5 jne          LBB0_690
	0x4c, 0x0f, 0xbc, 0xc6, //0x000033db bsfq         %rsi, %r8
	0x4d, 0x01, 0xe0, //0x000033df addq         %r12, %r8
	//0x000033e2 LBB0_690
	0x4c, 0x89, 0xd0, //0x000033e2 movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x000033e5 notq         %rax
	0x48, 0x21, 0xf0, //0x000033e8 andq         %rsi, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x000033eb leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xd3, //0x000033ef orq          %r10, %r11
	0x4c, 0x89, 0xd9, //0x000033f2 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x000033f5 notq         %rcx
	0x48, 0x21, 0xf1, //0x000033f8 andq         %rsi, %rcx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000033fb movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf1, //0x00003405 andq         %rsi, %rcx
	0x45, 0x31, 0xd2, //0x00003408 xorl         %r10d, %r10d
	0x48, 0x01, 0xc1, //0x0000340b addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc2, //0x0000340e setb         %r10b
	0x48, 0x01, 0xc9, //0x00003412 addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00003415 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x0000341f xorq         %rax, %rcx
	0x4c, 0x21, 0xd9, //0x00003422 andq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00003425 notq         %rcx
	0x48, 0x21, 0xcf, //0x00003428 andq         %rcx, %rdi
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000342b movq         $32(%rsp), %r11
	0x48, 0x85, 0xff, //0x00003430 testq        %rdi, %rdi
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00003433 je           LBB0_682
	//0x00003439 LBB0_691
	0x4c, 0x0f, 0xbc, 0xe7, //0x00003439 bsfq         %rdi, %r12
	0x49, 0x29, 0xdc, //0x0000343d subq         %rbx, %r12
	//0x00003440 LBB0_692
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00003440 movq         $24(%rsp), %r10
	0x4d, 0x85, 0xe4, //0x00003445 testq        %r12, %r12
	0x0f, 0x88, 0x53, 0x17, 0x00, 0x00, //0x00003448 js           LBB0_813
	//0x0000344e LBB0_693
	0x4d, 0x89, 0x65, 0x00, //0x0000344e movq         %r12, (%r13)
	0x4c, 0x89, 0xf3, //0x00003452 movq         %r14, %rbx
	0x49, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00003455 movabsq      $9223372036854775806, %r9
	0x4d, 0x39, 0xce, //0x0000345f cmpq         %r9, %r14
	0x0f, 0x86, 0x58, 0xfc, 0xff, 0xff, //0x00003462 jbe          LBB0_634
	0xe9, 0x13, 0x17, 0x00, 0x00, //0x00003468 jmp          LBB0_1025
	//0x0000346d LBB0_694
	0x40, 0x80, 0xff, 0x3a, //0x0000346d cmpb         $58, %dil
	0x0f, 0x85, 0x02, 0x17, 0x00, 0x00, //0x00003471 jne          LBB0_1024
	0x4b, 0xc7, 0x04, 0xc3, 0x00, 0x00, 0x00, 0x00, //0x00003477 movq         $0, (%r11,%r8,8)
	0xe9, 0x3c, 0xfc, 0xff, 0xff, //0x0000347f jmp          LBB0_634
	//0x00003484 LBB0_696
	0x83, 0xff, 0x2c, //0x00003484 cmpl         $44, %edi
	0x0f, 0x85, 0x06, 0x04, 0x00, 0x00, //0x00003487 jne          LBB0_697
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x0000348d cmpq         $4095, %r8
	0x0f, 0x8f, 0xfb, 0x16, 0x00, 0x00, //0x00003494 jg           LBB0_1106
	0x49, 0x8d, 0x40, 0x01, //0x0000349a leaq         $1(%r8), %rax
	0x49, 0x89, 0x03, //0x0000349e movq         %rax, (%r11)
	0x4b, 0xc7, 0x44, 0xc3, 0x08, 0x03, 0x00, 0x00, 0x00, //0x000034a1 movq         $3, $8(%r11,%r8,8)
	0xe9, 0x11, 0xfc, 0xff, 0xff, //0x000034aa jmp          LBB0_634
	//0x000034af LBB0_698
	0x49, 0x89, 0x33, //0x000034af movq         %rsi, (%r11)
	0x83, 0xff, 0x7b, //0x000034b2 cmpl         $123, %edi
	0x0f, 0x87, 0xbe, 0x16, 0x00, 0x00, //0x000034b5 ja           LBB0_1024
	//0x000034bb LBB0_674
	0x4f, 0x8d, 0x04, 0x37, //0x000034bb leaq         (%r15,%r14), %r8
	0x89, 0xf8, //0x000034bf movl         %edi, %eax
	0x48, 0x8d, 0x0d, 0x70, 0x24, 0x00, 0x00, //0x000034c1 leaq         $9328(%rip), %rcx  /* LJTI0_3+0(%rip) */
	0x48, 0x63, 0x04, 0x81, //0x000034c8 movslq       (%rcx,%rax,4), %rax
	0x48, 0x01, 0xc8, //0x000034cc addq         %rcx, %rax
	0xff, 0xe0, //0x000034cf jmpq         *%rax
	//0x000034d1 LBB0_701
	0x4d, 0x8b, 0x52, 0x08, //0x000034d1 movq         $8(%r10), %r10
	0x4d, 0x29, 0xf2, //0x000034d5 subq         %r14, %r10
	0x0f, 0x84, 0xec, 0x16, 0x00, 0x00, //0x000034d8 je           LBB0_1027
	0x41, 0x80, 0x38, 0x30, //0x000034de cmpb         $48, (%r8)
	0x0f, 0x85, 0x37, 0x00, 0x00, 0x00, //0x000034e2 jne          LBB0_706
	0x41, 0xbd, 0x01, 0x00, 0x00, 0x00, //0x000034e8 movl         $1, %r13d
	0x49, 0x83, 0xfa, 0x01, //0x000034ee cmpq         $1, %r10
	0x0f, 0x84, 0x9a, 0xfb, 0xff, 0xff, //0x000034f2 je           LBB0_633
	0x43, 0x8a, 0x14, 0x27, //0x000034f8 movb         (%r15,%r12), %dl
	0x80, 0xc2, 0xd2, //0x000034fc addb         $-46, %dl
	0x80, 0xfa, 0x37, //0x000034ff cmpb         $55, %dl
	0x0f, 0x87, 0x8a, 0xfb, 0xff, 0xff, //0x00003502 ja           LBB0_633
	0x0f, 0xb6, 0xc2, //0x00003508 movzbl       %dl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x0000350b movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00003515 btq          %rax, %rcx
	0x0f, 0x83, 0x73, 0xfb, 0xff, 0xff, //0x00003519 jae          LBB0_633
	//0x0000351f LBB0_706
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000351f movq         $-1, %r11
	0x49, 0x83, 0xfa, 0x20, //0x00003526 cmpq         $32, %r10
	0x0f, 0x82, 0xc6, 0x0f, 0x00, 0x00, //0x0000352a jb           LBB0_930
	0x45, 0x31, 0xed, //0x00003530 xorl         %r13d, %r13d
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00003533 movq         $-1, %r12
	0x48, 0xc7, 0x44, 0x24, 0x30, 0xff, 0xff, 0xff, 0xff, //0x0000353a movq         $-1, $48(%rsp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003543 .p2align 4, 0x90
	//0x00003550 LBB0_708
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x28, //0x00003550 vmovdqu      (%r8,%r13), %ymm0
	0xc5, 0xbd, 0x74, 0xc8, //0x00003556 vpcmpeqb     %ymm0, %ymm8, %ymm1
	0xc5, 0xb5, 0x74, 0xd0, //0x0000355a vpcmpeqb     %ymm0, %ymm9, %ymm2
	0xc5, 0xed, 0xeb, 0xc9, //0x0000355e vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xad, 0xdb, 0xd0, //0x00003562 vpand        %ymm0, %ymm10, %ymm2
	0xc5, 0xa5, 0x74, 0xd8, //0x00003566 vpcmpeqb     %ymm0, %ymm11, %ymm3
	0xc5, 0xfd, 0xd7, 0xdb, //0x0000356a vpmovmskb    %ymm3, %ebx
	0xc5, 0x9d, 0x74, 0xd2, //0x0000356e vpcmpeqb     %ymm2, %ymm12, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x00003572 vpmovmskb    %ymm2, %esi
	0xc5, 0xfd, 0xd7, 0xf9, //0x00003576 vpmovmskb    %ymm1, %edi
	0xc5, 0x95, 0xfc, 0xc0, //0x0000357a vpaddb       %ymm0, %ymm13, %ymm0
	0xc5, 0x8d, 0xda, 0xe0, //0x0000357e vpminub      %ymm0, %ymm14, %ymm4
	0xc5, 0xfd, 0x74, 0xc4, //0x00003582 vpcmpeqb     %ymm4, %ymm0, %ymm0
	0xc5, 0xe5, 0xeb, 0xd2, //0x00003586 vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0xfd, 0xeb, 0xc2, //0x0000358a vpor         %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x0000358e vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00003592 vpmovmskb    %ymm0, %eax
	0x48, 0xf7, 0xd0, //0x00003596 notq         %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x00003599 bsfq         %rax, %rcx
	0x83, 0xf9, 0x20, //0x0000359d cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000035a0 je           LBB0_710
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x000035a6 movl         $-1, %eax
	0xd3, 0xe0, //0x000035ab shll         %cl, %eax
	0xf7, 0xd0, //0x000035ad notl         %eax
	0x21, 0xc3, //0x000035af andl         %eax, %ebx
	0x21, 0xc6, //0x000035b1 andl         %eax, %esi
	0x21, 0xf8, //0x000035b3 andl         %edi, %eax
	0x89, 0xc7, //0x000035b5 movl         %eax, %edi
	//0x000035b7 LBB0_710
	0x8d, 0x53, 0xff, //0x000035b7 leal         $-1(%rbx), %edx
	0x21, 0xda, //0x000035ba andl         %ebx, %edx
	0x0f, 0x85, 0x87, 0x0b, 0x00, 0x00, //0x000035bc jne          LBB0_891
	0x8d, 0x56, 0xff, //0x000035c2 leal         $-1(%rsi), %edx
	0x21, 0xf2, //0x000035c5 andl         %esi, %edx
	0x0f, 0x85, 0x7c, 0x0b, 0x00, 0x00, //0x000035c7 jne          LBB0_891
	0x8d, 0x57, 0xff, //0x000035cd leal         $-1(%rdi), %edx
	0x21, 0xfa, //0x000035d0 andl         %edi, %edx
	0x0f, 0x85, 0x71, 0x0b, 0x00, 0x00, //0x000035d2 jne          LBB0_891
	0x85, 0xdb, //0x000035d8 testl        %ebx, %ebx
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000035da je           LBB0_716
	0x0f, 0xbc, 0xdb, //0x000035e0 bsfl         %ebx, %ebx
	0x48, 0x83, 0x7c, 0x24, 0x30, 0xff, //0x000035e3 cmpq         $-1, $48(%rsp)
	0x0f, 0x85, 0x90, 0x0d, 0x00, 0x00, //0x000035e9 jne          LBB0_914
	0x4c, 0x01, 0xeb, //0x000035ef addq         %r13, %rbx
	0x48, 0x89, 0x5c, 0x24, 0x30, //0x000035f2 movq         %rbx, $48(%rsp)
	//0x000035f7 LBB0_716
	0x85, 0xf6, //0x000035f7 testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000035f9 je           LBB0_719
	0x0f, 0xbc, 0xf6, //0x000035ff bsfl         %esi, %esi
	0x49, 0x83, 0xfc, 0xff, //0x00003602 cmpq         $-1, %r12
	0x0f, 0x85, 0x6c, 0x0d, 0x00, 0x00, //0x00003606 jne          LBB0_913
	0x4c, 0x01, 0xee, //0x0000360c addq         %r13, %rsi
	0x49, 0x89, 0xf4, //0x0000360f movq         %rsi, %r12
	//0x00003612 LBB0_719
	0x85, 0xff, //0x00003612 testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00003614 je           LBB0_722
	0x0f, 0xbc, 0xf7, //0x0000361a bsfl         %edi, %esi
	0x49, 0x83, 0xfb, 0xff, //0x0000361d cmpq         $-1, %r11
	0x0f, 0x85, 0x51, 0x0d, 0x00, 0x00, //0x00003621 jne          LBB0_913
	0x4c, 0x01, 0xee, //0x00003627 addq         %r13, %rsi
	0x49, 0x89, 0xf3, //0x0000362a movq         %rsi, %r11
	//0x0000362d LBB0_722
	0x83, 0xf9, 0x20, //0x0000362d cmpl         $32, %ecx
	0x0f, 0x85, 0x06, 0x04, 0x00, 0x00, //0x00003630 jne          LBB0_780
	0x49, 0x83, 0xc2, 0xe0, //0x00003636 addq         $-32, %r10
	0x49, 0x83, 0xc5, 0x20, //0x0000363a addq         $32, %r13
	0x49, 0x83, 0xfa, 0x1f, //0x0000363e cmpq         $31, %r10
	0x0f, 0x87, 0x08, 0xff, 0xff, 0xff, //0x00003642 ja           LBB0_708
	0xc5, 0xf8, 0x77, //0x00003648 vzeroupper   
	0xc5, 0x7a, 0x6f, 0x3d, 0xcd, 0xcb, 0xff, 0xff, //0x0000364b vmovdqu      $-13363(%rip), %xmm15  /* LCPI0_19+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x65, 0xcb, 0xff, 0xff, //0x00003653 vmovdqu      $-13467(%rip), %ymm14  /* LCPI0_18+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x3d, 0xcb, 0xff, 0xff, //0x0000365b vmovdqu      $-13507(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x15, 0xcb, 0xff, 0xff, //0x00003663 vmovdqu      $-13547(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xed, 0xca, 0xff, 0xff, //0x0000366b vmovdqu      $-13587(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xc5, 0xc9, 0xff, 0xff, //0x00003673 vmovdqu      $-13883(%rip), %ymm10  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0xbd, 0xca, 0xff, 0xff, //0x0000367b vmovdqu      $-13635(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x95, 0xca, 0xff, 0xff, //0x00003683 vmovdqu      $-13675(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x0d, 0xca, 0xff, 0xff, //0x0000368b vmovdqu      $-13811(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xe5, 0xc9, 0xff, 0xff, //0x00003693 vmovdqu      $-13851(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x5d, 0xc9, 0xff, 0xff, //0x0000369b vmovdqu      $-13987(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0x4d, 0x01, 0xc5, //0x000036a3 addq         %r8, %r13
	0x49, 0x83, 0xfa, 0x10, //0x000036a6 cmpq         $16, %r10
	0x4c, 0x89, 0x44, 0x24, 0x28, //0x000036aa movq         %r8, $40(%rsp)
	0x0f, 0x82, 0x26, 0x01, 0x00, 0x00, //0x000036af jb           LBB0_743
	//0x000036b5 LBB0_725
	0x4d, 0x89, 0xe9, //0x000036b5 movq         %r13, %r9
	0x4d, 0x29, 0xc1, //0x000036b8 subq         %r8, %r9
	0x45, 0x31, 0xc0, //0x000036bb xorl         %r8d, %r8d
	0x90, 0x90, //0x000036be .p2align 4, 0x90
	//0x000036c0 LBB0_726
	0xc4, 0x81, 0x7a, 0x6f, 0x44, 0x05, 0x00, //0x000036c0 vmovdqu      (%r13,%r8), %xmm0
	0xc5, 0x81, 0x74, 0xc8, //0x000036c7 vpcmpeqb     %xmm0, %xmm15, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0x5d, 0xcb, 0xff, 0xff, //0x000036cb vpcmpeqb     $-13475(%rip), %xmm0, %xmm2  /* LCPI0_20+0(%rip) */
	0xc5, 0xe9, 0xeb, 0xc9, //0x000036d3 vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xf9, 0xfc, 0x15, 0x61, 0xcb, 0xff, 0xff, //0x000036d7 vpaddb       $-13471(%rip), %xmm0, %xmm2  /* LCPI0_21+0(%rip) */
	0xc5, 0xe9, 0xda, 0x1d, 0x69, 0xcb, 0xff, 0xff, //0x000036df vpminub      $-13463(%rip), %xmm2, %xmm3  /* LCPI0_22+0(%rip) */
	0xc5, 0xe9, 0x74, 0xd3, //0x000036e7 vpcmpeqb     %xmm3, %xmm2, %xmm2
	0xc5, 0xf9, 0xdb, 0x1d, 0xfd, 0xca, 0xff, 0xff, //0x000036eb vpand        $-13571(%rip), %xmm0, %xmm3  /* LCPI0_5+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x65, 0xcb, 0xff, 0xff, //0x000036f3 vpcmpeqb     $-13467(%rip), %xmm0, %xmm0  /* LCPI0_23+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x6d, 0xcb, 0xff, 0xff, //0x000036fb vpcmpeqb     $-13459(%rip), %xmm3, %xmm3  /* LCPI0_24+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00003703 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xd9, 0xeb, 0xe1, //0x00003707 vpor         %xmm1, %xmm4, %xmm4
	0xc5, 0xd9, 0xeb, 0xd2, //0x0000370b vpor         %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xc0, //0x0000370f vpmovmskb    %xmm0, %eax
	0xc5, 0xf9, 0xd7, 0xfb, //0x00003713 vpmovmskb    %xmm3, %edi
	0xc5, 0xf9, 0xd7, 0xf1, //0x00003717 vpmovmskb    %xmm1, %esi
	0xc5, 0xf9, 0xd7, 0xca, //0x0000371b vpmovmskb    %xmm2, %ecx
	0xf7, 0xd1, //0x0000371f notl         %ecx
	0x0f, 0xbc, 0xc9, //0x00003721 bsfl         %ecx, %ecx
	0x83, 0xf9, 0x10, //0x00003724 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00003727 je           LBB0_728
	0xba, 0xff, 0xff, 0xff, 0xff, //0x0000372d movl         $-1, %edx
	0xd3, 0xe2, //0x00003732 shll         %cl, %edx
	0xf7, 0xd2, //0x00003734 notl         %edx
	0x21, 0xd0, //0x00003736 andl         %edx, %eax
	0x21, 0xd7, //0x00003738 andl         %edx, %edi
	0x21, 0xf2, //0x0000373a andl         %esi, %edx
	0x89, 0xd6, //0x0000373c movl         %edx, %esi
	//0x0000373e LBB0_728
	0x8d, 0x50, 0xff, //0x0000373e leal         $-1(%rax), %edx
	0x21, 0xc2, //0x00003741 andl         %eax, %edx
	0x0f, 0x85, 0xf2, 0x0b, 0x00, 0x00, //0x00003743 jne          LBB0_910
	0x8d, 0x57, 0xff, //0x00003749 leal         $-1(%rdi), %edx
	0x21, 0xfa, //0x0000374c andl         %edi, %edx
	0x0f, 0x85, 0xe7, 0x0b, 0x00, 0x00, //0x0000374e jne          LBB0_910
	0x8d, 0x56, 0xff, //0x00003754 leal         $-1(%rsi), %edx
	0x21, 0xf2, //0x00003757 andl         %esi, %edx
	0x0f, 0x85, 0xdc, 0x0b, 0x00, 0x00, //0x00003759 jne          LBB0_910
	0x85, 0xc0, //0x0000375f testl        %eax, %eax
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x00003761 je           LBB0_734
	0x0f, 0xbc, 0xd8, //0x00003767 bsfl         %eax, %ebx
	0x48, 0x83, 0x7c, 0x24, 0x30, 0xff, //0x0000376a cmpq         $-1, $48(%rsp)
	0x0f, 0x85, 0x60, 0x0c, 0x00, 0x00, //0x00003770 jne          LBB0_921
	0x4c, 0x01, 0xcb, //0x00003776 addq         %r9, %rbx
	0x4c, 0x01, 0xc3, //0x00003779 addq         %r8, %rbx
	0x48, 0x89, 0x5c, 0x24, 0x30, //0x0000377c movq         %rbx, $48(%rsp)
	//0x00003781 LBB0_734
	0x85, 0xff, //0x00003781 testl        %edi, %edi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00003783 je           LBB0_737
	0x0f, 0xbc, 0xff, //0x00003789 bsfl         %edi, %edi
	0x49, 0x83, 0xfc, 0xff, //0x0000378c cmpq         $-1, %r12
	0x0f, 0x85, 0x47, 0x0c, 0x00, 0x00, //0x00003790 jne          LBB0_922
	0x4c, 0x01, 0xcf, //0x00003796 addq         %r9, %rdi
	0x4c, 0x01, 0xc7, //0x00003799 addq         %r8, %rdi
	0x49, 0x89, 0xfc, //0x0000379c movq         %rdi, %r12
	//0x0000379f LBB0_737
	0x85, 0xf6, //0x0000379f testl        %esi, %esi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000037a1 je           LBB0_740
	0x0f, 0xbc, 0xf6, //0x000037a7 bsfl         %esi, %esi
	0x49, 0x83, 0xfb, 0xff, //0x000037aa cmpq         $-1, %r11
	0x0f, 0x85, 0x30, 0x0c, 0x00, 0x00, //0x000037ae jne          LBB0_923
	0x4c, 0x01, 0xce, //0x000037b4 addq         %r9, %rsi
	0x4c, 0x01, 0xc6, //0x000037b7 addq         %r8, %rsi
	0x49, 0x89, 0xf3, //0x000037ba movq         %rsi, %r11
	//0x000037bd LBB0_740
	0x83, 0xf9, 0x10, //0x000037bd cmpl         $16, %ecx
	0x0f, 0x85, 0x8f, 0x02, 0x00, 0x00, //0x000037c0 jne          LBB0_781
	0x49, 0x83, 0xc2, 0xf0, //0x000037c6 addq         $-16, %r10
	0x49, 0x83, 0xc0, 0x10, //0x000037ca addq         $16, %r8
	0x49, 0x83, 0xfa, 0x0f, //0x000037ce cmpq         $15, %r10
	0x0f, 0x87, 0xe8, 0xfe, 0xff, 0xff, //0x000037d2 ja           LBB0_726
	0x4d, 0x01, 0xc5, //0x000037d8 addq         %r8, %r13
	//0x000037db LBB0_743
	0x4d, 0x85, 0xd2, //0x000037db testq        %r10, %r10
	0x49, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x000037de movabsq      $9223372036854775806, %r9
	0x48, 0x8d, 0x35, 0xa5, 0x23, 0x00, 0x00, //0x000037e8 leaq         $9125(%rip), %rsi  /* LJTI0_5+0(%rip) */
	0x0f, 0x84, 0x7a, 0x02, 0x00, 0x00, //0x000037ef je           LBB0_783
	0x4f, 0x8d, 0x04, 0x2a, //0x000037f5 leaq         (%r10,%r13), %r8
	0x4c, 0x89, 0xef, //0x000037f9 movq         %r13, %rdi
	0x48, 0x2b, 0x7c, 0x24, 0x28, //0x000037fc subq         $40(%rsp), %rdi
	0x31, 0xc9, //0x00003801 xorl         %ecx, %ecx
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00003803 jmp          LBB0_748
	//0x00003808 LBB0_745
	0x49, 0x83, 0xfb, 0xff, //0x00003808 cmpq         $-1, %r11
	0x0f, 0x85, 0x52, 0x0b, 0x00, 0x00, //0x0000380c jne          LBB0_912
	0x4c, 0x8d, 0x1c, 0x0f, //0x00003812 leaq         (%rdi,%rcx), %r11
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003816 .p2align 4, 0x90
	//0x00003820 LBB0_747
	0x48, 0x83, 0xc1, 0x01, //0x00003820 addq         $1, %rcx
	0x49, 0x39, 0xca, //0x00003824 cmpq         %rcx, %r10
	0x0f, 0x84, 0xc1, 0x08, 0x00, 0x00, //0x00003827 je           LBB0_890
	//0x0000382d LBB0_748
	0x41, 0x0f, 0xbe, 0x5c, 0x0d, 0x00, //0x0000382d movsbl       (%r13,%rcx), %ebx
	0x8d, 0x43, 0xd0, //0x00003833 leal         $-48(%rbx), %eax
	0x83, 0xf8, 0x0a, //0x00003836 cmpl         $10, %eax
	0x0f, 0x82, 0xe1, 0xff, 0xff, 0xff, //0x00003839 jb           LBB0_747
	0x8d, 0x53, 0xd5, //0x0000383f leal         $-43(%rbx), %edx
	0x83, 0xfa, 0x1a, //0x00003842 cmpl         $26, %edx
	0x0f, 0x87, 0x23, 0x00, 0x00, 0x00, //0x00003845 ja           LBB0_753
	0x48, 0x63, 0x04, 0x96, //0x0000384b movslq       (%rsi,%rdx,4), %rax
	0x48, 0x01, 0xf0, //0x0000384f addq         %rsi, %rax
	0xff, 0xe0, //0x00003852 jmpq         *%rax
	//0x00003854 LBB0_751
	0x48, 0x83, 0x7c, 0x24, 0x30, 0xff, //0x00003854 cmpq         $-1, $48(%rsp)
	0x0f, 0x85, 0x04, 0x0b, 0x00, 0x00, //0x0000385a jne          LBB0_912
	0x48, 0x8d, 0x04, 0x0f, //0x00003860 leaq         (%rdi,%rcx), %rax
	0x48, 0x89, 0x44, 0x24, 0x30, //0x00003864 movq         %rax, $48(%rsp)
	0xe9, 0xb2, 0xff, 0xff, 0xff, //0x00003869 jmp          LBB0_747
	//0x0000386e LBB0_753
	0x83, 0xfb, 0x65, //0x0000386e cmpl         $101, %ebx
	0x0f, 0x85, 0xf5, 0x01, 0x00, 0x00, //0x00003871 jne          LBB0_782
	//0x00003877 LBB0_754
	0x49, 0x83, 0xfc, 0xff, //0x00003877 cmpq         $-1, %r12
	0x0f, 0x85, 0xe3, 0x0a, 0x00, 0x00, //0x0000387b jne          LBB0_912
	0x4c, 0x8d, 0x24, 0x0f, //0x00003881 leaq         (%rdi,%rcx), %r12
	0xe9, 0x96, 0xff, 0xff, 0xff, //0x00003885 jmp          LBB0_747
	//0x0000388a LBB0_699
	0x83, 0xff, 0x22, //0x0000388a cmpl         $34, %edi
	0x0f, 0x84, 0x44, 0x00, 0x00, 0x00, //0x0000388d je           LBB0_760
	//0x00003893 LBB0_697
	0x83, 0xff, 0x7d, //0x00003893 cmpl         $125, %edi
	0x0f, 0x85, 0xdd, 0x12, 0x00, 0x00, //0x00003896 jne          LBB0_1024
	//0x0000389c LBB0_671
	0x49, 0x89, 0x33, //0x0000389c movq         %rsi, (%r11)
	0x49, 0x89, 0xf0, //0x0000389f movq         %rsi, %r8
	0x48, 0x8b, 0x5c, 0x24, 0x38, //0x000038a2 movq         $56(%rsp), %rbx
	0x48, 0x85, 0xf6, //0x000038a7 testq        %rsi, %rsi
	0x0f, 0x85, 0x24, 0xf8, 0xff, 0xff, //0x000038aa jne          LBB0_636
	0xe9, 0xcb, 0x12, 0x00, 0x00, //0x000038b0 jmp          LBB0_1025
	//0x000038b5 LBB0_756
	0x49, 0x81, 0xf8, 0xff, 0x0f, 0x00, 0x00, //0x000038b5 cmpq         $4095, %r8
	0x0f, 0x8f, 0xd3, 0x12, 0x00, 0x00, //0x000038bc jg           LBB0_1106
	0x49, 0x8d, 0x40, 0x01, //0x000038c2 leaq         $1(%r8), %rax
	0x49, 0x89, 0x03, //0x000038c6 movq         %rax, (%r11)
	0x4b, 0xc7, 0x44, 0xc3, 0x08, 0x00, 0x00, 0x00, 0x00, //0x000038c9 movq         $0, $8(%r11,%r8,8)
	0xe9, 0xe9, 0xf7, 0xff, 0xff, //0x000038d2 jmp          LBB0_634
	//0x000038d7 LBB0_760
	0x4b, 0xc7, 0x04, 0xc3, 0x02, 0x00, 0x00, 0x00, //0x000038d7 movq         $2, (%r11,%r8,8)
	0x4d, 0x8b, 0x4a, 0x08, //0x000038df movq         $8(%r10), %r9
	0x4c, 0x89, 0xca, //0x000038e3 movq         %r9, %rdx
	0x4c, 0x29, 0xe2, //0x000038e6 subq         %r12, %rdx
	0x0f, 0x84, 0x61, 0x1b, 0x00, 0x00, //0x000038e9 je           LBB0_1133
	0x48, 0x83, 0xfa, 0x40, //0x000038ef cmpq         $64, %rdx
	0x0f, 0x82, 0x39, 0x0c, 0x00, 0x00, //0x000038f3 jb           LBB0_934
	0x48, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x000038f9 movq         $-2, %rbx
	0x4c, 0x29, 0xf3, //0x00003900 subq         %r14, %rbx
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003903 movq         $-1, %r8
	0x45, 0x31, 0xd2, //0x0000390a xorl         %r10d, %r10d
	0x90, 0x90, 0x90, //0x0000390d .p2align 4, 0x90
	//0x00003910 LBB0_763
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x27, //0x00003910 vmovdqu      (%r15,%r12), %ymm0
	0xc4, 0x81, 0x7e, 0x6f, 0x4c, 0x27, 0x20, //0x00003916 vmovdqu      $32(%r15,%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x0000391d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00003921 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00003925 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00003929 vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x0000392d vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00003931 vpmovmskb    %ymm0, %esi
	0xc5, 0xf5, 0x74, 0xc7, //0x00003935 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x00003939 vpmovmskb    %ymm0, %ecx
	0x48, 0xc1, 0xe0, 0x20, //0x0000393d shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00003941 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00003944 shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x00003948 orq          %rcx, %rsi
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x0000394b jne          LBB0_772
	0x4d, 0x85, 0xd2, //0x00003951 testq        %r10, %r10
	0x0f, 0x85, 0x38, 0x00, 0x00, 0x00, //0x00003954 jne          LBB0_774
	0x45, 0x31, 0xd2, //0x0000395a xorl         %r10d, %r10d
	0x48, 0x85, 0xff, //0x0000395d testq        %rdi, %rdi
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x00003960 jne          LBB0_775
	//0x00003966 LBB0_766
	0x48, 0x83, 0xc2, 0xc0, //0x00003966 addq         $-64, %rdx
	0x48, 0x83, 0xc3, 0xc0, //0x0000396a addq         $-64, %rbx
	0x49, 0x83, 0xc4, 0x40, //0x0000396e addq         $64, %r12
	0x48, 0x83, 0xfa, 0x3f, //0x00003972 cmpq         $63, %rdx
	0x0f, 0x87, 0x94, 0xff, 0xff, 0xff, //0x00003976 ja           LBB0_763
	0xe9, 0x3f, 0x09, 0x00, 0x00, //0x0000397c jmp          LBB0_767
	//0x00003981 LBB0_772
	0x49, 0x83, 0xf8, 0xff, //0x00003981 cmpq         $-1, %r8
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00003985 jne          LBB0_774
	0x4c, 0x0f, 0xbc, 0xc6, //0x0000398b bsfq         %rsi, %r8
	0x4d, 0x01, 0xe0, //0x0000398f addq         %r12, %r8
	//0x00003992 LBB0_774
	0x4c, 0x89, 0xd0, //0x00003992 movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00003995 notq         %rax
	0x48, 0x21, 0xf0, //0x00003998 andq         %rsi, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x0000399b leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xd3, //0x0000399f orq          %r10, %r11
	0x4c, 0x89, 0xd9, //0x000039a2 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x000039a5 notq         %rcx
	0x48, 0x21, 0xf1, //0x000039a8 andq         %rsi, %rcx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000039ab movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf1, //0x000039b5 andq         %rsi, %rcx
	0x45, 0x31, 0xd2, //0x000039b8 xorl         %r10d, %r10d
	0x48, 0x01, 0xc1, //0x000039bb addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc2, //0x000039be setb         %r10b
	0x48, 0x01, 0xc9, //0x000039c2 addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000039c5 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x000039cf xorq         %rax, %rcx
	0x4c, 0x21, 0xd9, //0x000039d2 andq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x000039d5 notq         %rcx
	0x48, 0x21, 0xcf, //0x000039d8 andq         %rcx, %rdi
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000039db movq         $32(%rsp), %r11
	0x48, 0x85, 0xff, //0x000039e0 testq        %rdi, %rdi
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x000039e3 je           LBB0_766
	//0x000039e9 LBB0_775
	0x4c, 0x0f, 0xbc, 0xe7, //0x000039e9 bsfq         %rdi, %r12
	0x49, 0x29, 0xdc, //0x000039ed subq         %rbx, %r12
	//0x000039f0 LBB0_776
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000039f0 movq         $24(%rsp), %r10
	0x4d, 0x85, 0xe4, //0x000039f5 testq        %r12, %r12
	0x0f, 0x88, 0xa3, 0x11, 0x00, 0x00, //0x000039f8 js           LBB0_813
	0x4d, 0x89, 0x65, 0x00, //0x000039fe movq         %r12, (%r13)
	0x4c, 0x89, 0xf3, //0x00003a02 movq         %r14, %rbx
	0x49, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00003a05 movabsq      $9223372036854775806, %r9
	0x4d, 0x39, 0xce, //0x00003a0f cmpq         %r9, %r14
	0x0f, 0x87, 0x68, 0x11, 0x00, 0x00, //0x00003a12 ja           LBB0_1025
	0x49, 0x8b, 0x03, //0x00003a18 movq         (%r11), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00003a1b cmpq         $4095, %rax
	0x0f, 0x8f, 0x6e, 0x11, 0x00, 0x00, //0x00003a21 jg           LBB0_1106
	0x48, 0x8d, 0x48, 0x01, //0x00003a27 leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0b, //0x00003a2b movq         %rcx, (%r11)
	0x49, 0xc7, 0x44, 0xc3, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00003a2e movq         $4, $8(%r11,%rax,8)
	0xe9, 0x84, 0xf6, 0xff, 0xff, //0x00003a37 jmp          LBB0_634
	//0x00003a3c LBB0_780
	0x4c, 0x01, 0xe9, //0x00003a3c addq         %r13, %rcx
	0x4c, 0x01, 0xc1, //0x00003a3f addq         %r8, %rcx
	0xc5, 0xf8, 0x77, //0x00003a42 vzeroupper   
	0xc5, 0x7a, 0x6f, 0x3d, 0xd3, 0xc7, 0xff, 0xff, //0x00003a45 vmovdqu      $-14381(%rip), %xmm15  /* LCPI0_19+0(%rip) */
	0x49, 0x89, 0xcd, //0x00003a4d movq         %rcx, %r13
	0xe9, 0x1f, 0x00, 0x00, 0x00, //0x00003a50 jmp          LBB0_784
	//0x00003a55 LBB0_781
	0x89, 0xc8, //0x00003a55 movl         %ecx, %eax
	0x49, 0x01, 0xc5, //0x00003a57 addq         %rax, %r13
	0x4d, 0x01, 0xc5, //0x00003a5a addq         %r8, %r13
	0x49, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00003a5d movabsq      $9223372036854775806, %r9
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00003a67 jmp          LBB0_783
	//0x00003a6c LBB0_782
	0x49, 0x01, 0xcd, //0x00003a6c addq         %rcx, %r13
	//0x00003a6f LBB0_783
	0x4c, 0x8b, 0x44, 0x24, 0x28, //0x00003a6f movq         $40(%rsp), %r8
	//0x00003a74 LBB0_784
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00003a74 movq         $-1, %rcx
	0x48, 0x8b, 0x54, 0x24, 0x30, //0x00003a7b movq         $48(%rsp), %rdx
	0x48, 0x85, 0xd2, //0x00003a80 testq        %rdx, %rdx
	0x0f, 0x84, 0x50, 0x11, 0x00, 0x00, //0x00003a83 je           LBB0_1029
	0x4d, 0x85, 0xdb, //0x00003a89 testq        %r11, %r11
	0x0f, 0x84, 0x47, 0x11, 0x00, 0x00, //0x00003a8c je           LBB0_1029
	0x4d, 0x85, 0xe4, //0x00003a92 testq        %r12, %r12
	0x0f, 0x84, 0x3e, 0x11, 0x00, 0x00, //0x00003a95 je           LBB0_1029
	0x4d, 0x29, 0xc5, //0x00003a9b subq         %r8, %r13
	0x49, 0x8d, 0x4d, 0xff, //0x00003a9e leaq         $-1(%r13), %rcx
	0x48, 0x39, 0xca, //0x00003aa2 cmpq         %rcx, %rdx
	0x0f, 0x84, 0x7e, 0xf5, 0xff, 0xff, //0x00003aa5 je           LBB0_630
	0x49, 0x39, 0xcb, //0x00003aab cmpq         %rcx, %r11
	0x0f, 0x84, 0x75, 0xf5, 0xff, 0xff, //0x00003aae je           LBB0_630
	0x49, 0x39, 0xcc, //0x00003ab4 cmpq         %rcx, %r12
	0x0f, 0x84, 0x6c, 0xf5, 0xff, 0xff, //0x00003ab7 je           LBB0_630
	0x4d, 0x85, 0xdb, //0x00003abd testq        %r11, %r11
	0xc5, 0xfe, 0x6f, 0x2d, 0x38, 0xc5, 0xff, 0xff, //0x00003ac0 vmovdqu      $-15048(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xb0, 0xc5, 0xff, 0xff, //0x00003ac8 vmovdqu      $-14928(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xc8, 0xc5, 0xff, 0xff, //0x00003ad0 vmovdqu      $-14904(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x40, 0xc6, 0xff, 0xff, //0x00003ad8 vmovdqu      $-14784(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x58, 0xc6, 0xff, 0xff, //0x00003ae0 vmovdqu      $-14760(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x50, 0xc5, 0xff, 0xff, //0x00003ae8 vmovdqu      $-15024(%rip), %ymm10  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x68, 0xc6, 0xff, 0xff, //0x00003af0 vmovdqu      $-14744(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x80, 0xc6, 0xff, 0xff, //0x00003af8 vmovdqu      $-14720(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x98, 0xc6, 0xff, 0xff, //0x00003b00 vmovdqu      $-14696(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xb0, 0xc6, 0xff, 0xff, //0x00003b08 vmovdqu      $-14672(%rip), %ymm14  /* LCPI0_18+0(%rip) */
	0x0f, 0x8e, 0x18, 0x00, 0x00, 0x00, //0x00003b10 jle          LBB0_793
	0x49, 0x8d, 0x43, 0xff, //0x00003b16 leaq         $-1(%r11), %rax
	0x49, 0x39, 0xc4, //0x00003b1a cmpq         %rax, %r12
	0x0f, 0x84, 0x0b, 0x00, 0x00, 0x00, //0x00003b1d je           LBB0_793
	0x49, 0xf7, 0xd3, //0x00003b23 notq         %r11
	0x4d, 0x89, 0xdd, //0x00003b26 movq         %r11, %r13
	0xe9, 0x59, 0x08, 0x00, 0x00, //0x00003b29 jmp          LBB0_916
	//0x00003b2e LBB0_793
	0x48, 0x89, 0xd0, //0x00003b2e movq         %rdx, %rax
	0x4c, 0x09, 0xe0, //0x00003b31 orq          %r12, %rax
	0x0f, 0x99, 0xc1, //0x00003b34 setns        %cl
	0x0f, 0x88, 0x6c, 0x05, 0x00, 0x00, //0x00003b37 js           LBB0_888
	0x4c, 0x39, 0xe2, //0x00003b3d cmpq         %r12, %rdx
	0x0f, 0x8c, 0x63, 0x05, 0x00, 0x00, //0x00003b40 jl           LBB0_888
	0x48, 0xf7, 0xd2, //0x00003b46 notq         %rdx
	0x49, 0x89, 0xd5, //0x00003b49 movq         %rdx, %r13
	0xe9, 0x36, 0x08, 0x00, 0x00, //0x00003b4c jmp          LBB0_916
	//0x00003b51 LBB0_796
	0x4d, 0x8b, 0x4a, 0x08, //0x00003b51 movq         $8(%r10), %r9
	0x4c, 0x89, 0xca, //0x00003b55 movq         %r9, %rdx
	0x4c, 0x29, 0xe2, //0x00003b58 subq         %r12, %rdx
	0x0f, 0x84, 0xef, 0x18, 0x00, 0x00, //0x00003b5b je           LBB0_1133
	0x48, 0x83, 0xfa, 0x40, //0x00003b61 cmpq         $64, %rdx
	0x0f, 0x82, 0xe3, 0x09, 0x00, 0x00, //0x00003b65 jb           LBB0_935
	0x48, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00003b6b movq         $-2, %rbx
	0x4c, 0x29, 0xf3, //0x00003b72 subq         %r14, %rbx
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003b75 movq         $-1, %r8
	0x45, 0x31, 0xd2, //0x00003b7c xorl         %r10d, %r10d
	0x90, //0x00003b7f .p2align 4, 0x90
	//0x00003b80 LBB0_799
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x27, //0x00003b80 vmovdqu      (%r15,%r12), %ymm0
	0xc4, 0x81, 0x7e, 0x6f, 0x4c, 0x27, 0x20, //0x00003b86 vmovdqu      $32(%r15,%r12), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00003b8d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00003b91 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00003b95 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00003b99 vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x00003b9d vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00003ba1 vpmovmskb    %ymm0, %esi
	0xc5, 0xf5, 0x74, 0xc7, //0x00003ba5 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x00003ba9 vpmovmskb    %ymm0, %ecx
	0x48, 0xc1, 0xe0, 0x20, //0x00003bad shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00003bb1 orq          %rax, %rdi
	0x48, 0xc1, 0xe1, 0x20, //0x00003bb4 shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x00003bb8 orq          %rcx, %rsi
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00003bbb jne          LBB0_808
	0x4d, 0x85, 0xd2, //0x00003bc1 testq        %r10, %r10
	0x0f, 0x85, 0x38, 0x00, 0x00, 0x00, //0x00003bc4 jne          LBB0_810
	0x45, 0x31, 0xd2, //0x00003bca xorl         %r10d, %r10d
	0x48, 0x85, 0xff, //0x00003bcd testq        %rdi, %rdi
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x00003bd0 jne          LBB0_811
	//0x00003bd6 LBB0_802
	0x48, 0x83, 0xc2, 0xc0, //0x00003bd6 addq         $-64, %rdx
	0x48, 0x83, 0xc3, 0xc0, //0x00003bda addq         $-64, %rbx
	0x49, 0x83, 0xc4, 0x40, //0x00003bde addq         $64, %r12
	0x48, 0x83, 0xfa, 0x3f, //0x00003be2 cmpq         $63, %rdx
	0x0f, 0x87, 0x94, 0xff, 0xff, 0xff, //0x00003be6 ja           LBB0_799
	0xe9, 0xfa, 0x07, 0x00, 0x00, //0x00003bec jmp          LBB0_803
	//0x00003bf1 LBB0_808
	0x49, 0x83, 0xf8, 0xff, //0x00003bf1 cmpq         $-1, %r8
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00003bf5 jne          LBB0_810
	0x4c, 0x0f, 0xbc, 0xc6, //0x00003bfb bsfq         %rsi, %r8
	0x4d, 0x01, 0xe0, //0x00003bff addq         %r12, %r8
	//0x00003c02 LBB0_810
	0x4c, 0x89, 0xd0, //0x00003c02 movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00003c05 notq         %rax
	0x48, 0x21, 0xf0, //0x00003c08 andq         %rsi, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x00003c0b leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xd3, //0x00003c0f orq          %r10, %r11
	0x4c, 0x89, 0xd9, //0x00003c12 movq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00003c15 notq         %rcx
	0x48, 0x21, 0xf1, //0x00003c18 andq         %rsi, %rcx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003c1b movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf1, //0x00003c25 andq         %rsi, %rcx
	0x45, 0x31, 0xd2, //0x00003c28 xorl         %r10d, %r10d
	0x48, 0x01, 0xc1, //0x00003c2b addq         %rax, %rcx
	0x41, 0x0f, 0x92, 0xc2, //0x00003c2e setb         %r10b
	0x48, 0x01, 0xc9, //0x00003c32 addq         %rcx, %rcx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00003c35 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc1, //0x00003c3f xorq         %rax, %rcx
	0x4c, 0x21, 0xd9, //0x00003c42 andq         %r11, %rcx
	0x48, 0xf7, 0xd1, //0x00003c45 notq         %rcx
	0x48, 0x21, 0xcf, //0x00003c48 andq         %rcx, %rdi
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00003c4b movq         $32(%rsp), %r11
	0x48, 0x85, 0xff, //0x00003c50 testq        %rdi, %rdi
	0x0f, 0x84, 0x7d, 0xff, 0xff, 0xff, //0x00003c53 je           LBB0_802
	//0x00003c59 LBB0_811
	0x4c, 0x0f, 0xbc, 0xe7, //0x00003c59 bsfq         %rdi, %r12
	0x49, 0x29, 0xdc, //0x00003c5d subq         %rbx, %r12
	//0x00003c60 LBB0_812
	0x4d, 0x85, 0xe4, //0x00003c60 testq        %r12, %r12
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00003c63 movq         $24(%rsp), %r10
	0x0f, 0x89, 0xe0, 0xf7, 0xff, 0xff, //0x00003c68 jns          LBB0_693
	0xe9, 0x2e, 0x0f, 0x00, 0x00, //0x00003c6e jmp          LBB0_813
	//0x00003c73 LBB0_816
	0x4d, 0x8b, 0x5a, 0x08, //0x00003c73 movq         $8(%r10), %r11
	0x4d, 0x29, 0xe3, //0x00003c77 subq         %r12, %r11
	0x0f, 0x84, 0x9b, 0x16, 0x00, 0x00, //0x00003c7a je           LBB0_1105
	0x4b, 0x8d, 0x04, 0x27, //0x00003c80 leaq         (%r15,%r12), %rax
	0x48, 0x89, 0x44, 0x24, 0x30, //0x00003c84 movq         %rax, $48(%rsp)
	0x80, 0x38, 0x30, //0x00003c89 cmpb         $48, (%rax)
	0x0f, 0x85, 0x3b, 0x00, 0x00, 0x00, //0x00003c8c jne          LBB0_821
	0x41, 0xbd, 0x01, 0x00, 0x00, 0x00, //0x00003c92 movl         $1, %r13d
	0x49, 0x83, 0xfb, 0x01, //0x00003c98 cmpq         $1, %r11
	0x0f, 0x84, 0xb7, 0x05, 0x00, 0x00, //0x00003c9c je           LBB0_905
	0x48, 0x8b, 0x44, 0x24, 0x30, //0x00003ca2 movq         $48(%rsp), %rax
	0x8a, 0x50, 0x01, //0x00003ca7 movb         $1(%rax), %dl
	0x80, 0xc2, 0xd2, //0x00003caa addb         $-46, %dl
	0x80, 0xfa, 0x37, //0x00003cad cmpb         $55, %dl
	0x0f, 0x87, 0xa3, 0x05, 0x00, 0x00, //0x00003cb0 ja           LBB0_905
	0x0f, 0xb6, 0xc2, //0x00003cb6 movzbl       %dl, %eax
	0x48, 0xb9, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00003cb9 movabsq      $36028797027352577, %rcx
	0x48, 0x0f, 0xa3, 0xc1, //0x00003cc3 btq          %rax, %rcx
	0x0f, 0x83, 0x8c, 0x05, 0x00, 0x00, //0x00003cc7 jae          LBB0_905
	//0x00003ccd LBB0_821
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003ccd movq         $-1, %r8
	0x49, 0x83, 0xfb, 0x20, //0x00003cd4 cmpq         $32, %r11
	0x0f, 0x82, 0x8c, 0x08, 0x00, 0x00, //0x00003cd8 jb           LBB0_936
	0x45, 0x31, 0xed, //0x00003cde xorl         %r13d, %r13d
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00003ce1 movq         $-1, %r12
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00003ce8 movq         $-1, %r10
	//0x00003cef LBB0_823
	0x48, 0x8b, 0x44, 0x24, 0x30, //0x00003cef movq         $48(%rsp), %rax
	0xc4, 0xa1, 0x7e, 0x6f, 0x04, 0x28, //0x00003cf4 vmovdqu      (%rax,%r13), %ymm0
	0xc5, 0xbd, 0x74, 0xc8, //0x00003cfa vpcmpeqb     %ymm0, %ymm8, %ymm1
	0xc5, 0xb5, 0x74, 0xd0, //0x00003cfe vpcmpeqb     %ymm0, %ymm9, %ymm2
	0xc5, 0xed, 0xeb, 0xc9, //0x00003d02 vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xad, 0xdb, 0xd0, //0x00003d06 vpand        %ymm0, %ymm10, %ymm2
	0xc5, 0xa5, 0x74, 0xd8, //0x00003d0a vpcmpeqb     %ymm0, %ymm11, %ymm3
	0xc5, 0xfd, 0xd7, 0xfb, //0x00003d0e vpmovmskb    %ymm3, %edi
	0xc5, 0x9d, 0x74, 0xd2, //0x00003d12 vpcmpeqb     %ymm2, %ymm12, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x00003d16 vpmovmskb    %ymm2, %esi
	0xc5, 0xfd, 0xd7, 0xd1, //0x00003d1a vpmovmskb    %ymm1, %edx
	0xc5, 0x95, 0xfc, 0xc0, //0x00003d1e vpaddb       %ymm0, %ymm13, %ymm0
	0xc5, 0x8d, 0xda, 0xe0, //0x00003d22 vpminub      %ymm0, %ymm14, %ymm4
	0xc5, 0xfd, 0x74, 0xc4, //0x00003d26 vpcmpeqb     %ymm4, %ymm0, %ymm0
	0xc5, 0xe5, 0xeb, 0xd2, //0x00003d2a vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0xfd, 0xeb, 0xc2, //0x00003d2e vpor         %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x00003d32 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00003d36 vpmovmskb    %ymm0, %eax
	0x48, 0xf7, 0xd0, //0x00003d3a notq         %rax
	0x48, 0x0f, 0xbc, 0xc8, //0x00003d3d bsfq         %rax, %rcx
	0x83, 0xf9, 0x20, //0x00003d41 cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00003d44 je           LBB0_825
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00003d4a movl         $-1, %eax
	0xd3, 0xe0, //0x00003d4f shll         %cl, %eax
	0xf7, 0xd0, //0x00003d51 notl         %eax
	0x21, 0xc7, //0x00003d53 andl         %eax, %edi
	0x21, 0xc6, //0x00003d55 andl         %eax, %esi
	0x21, 0xd0, //0x00003d57 andl         %edx, %eax
	0x89, 0xc2, //0x00003d59 movl         %eax, %edx
	//0x00003d5b LBB0_825
	0x8d, 0x5f, 0xff, //0x00003d5b leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x00003d5e andl         %edi, %ebx
	0x0f, 0x85, 0x54, 0x06, 0x00, 0x00, //0x00003d60 jne          LBB0_919
	0x8d, 0x5e, 0xff, //0x00003d66 leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00003d69 andl         %esi, %ebx
	0x0f, 0x85, 0x49, 0x06, 0x00, 0x00, //0x00003d6b jne          LBB0_919
	0x8d, 0x5a, 0xff, //0x00003d71 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x00003d74 andl         %edx, %ebx
	0x0f, 0x85, 0x3e, 0x06, 0x00, 0x00, //0x00003d76 jne          LBB0_919
	0x85, 0xff, //0x00003d7c testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00003d7e je           LBB0_831
	0x0f, 0xbc, 0xff, //0x00003d84 bsfl         %edi, %edi
	0x49, 0x83, 0xfa, 0xff, //0x00003d87 cmpq         $-1, %r10
	0x0f, 0x85, 0x8c, 0x07, 0x00, 0x00, //0x00003d8b jne          LBB0_931
	0x4c, 0x01, 0xef, //0x00003d91 addq         %r13, %rdi
	0x49, 0x89, 0xfa, //0x00003d94 movq         %rdi, %r10
	//0x00003d97 LBB0_831
	0x85, 0xf6, //0x00003d97 testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00003d99 je           LBB0_834
	0x0f, 0xbc, 0xf6, //0x00003d9f bsfl         %esi, %esi
	0x49, 0x83, 0xfc, 0xff, //0x00003da2 cmpq         $-1, %r12
	0x0f, 0x85, 0x78, 0x07, 0x00, 0x00, //0x00003da6 jne          LBB0_932
	0x4c, 0x01, 0xee, //0x00003dac addq         %r13, %rsi
	0x49, 0x89, 0xf4, //0x00003daf movq         %rsi, %r12
	//0x00003db2 LBB0_834
	0x85, 0xd2, //0x00003db2 testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00003db4 je           LBB0_837
	0x0f, 0xbc, 0xd2, //0x00003dba bsfl         %edx, %edx
	0x49, 0x83, 0xf8, 0xff, //0x00003dbd cmpq         $-1, %r8
	0x0f, 0x85, 0x64, 0x07, 0x00, 0x00, //0x00003dc1 jne          LBB0_933
	0x4c, 0x01, 0xea, //0x00003dc7 addq         %r13, %rdx
	0x49, 0x89, 0xd0, //0x00003dca movq         %rdx, %r8
	//0x00003dcd LBB0_837
	0x83, 0xf9, 0x20, //0x00003dcd cmpl         $32, %ecx
	0x0f, 0x85, 0xed, 0x02, 0x00, 0x00, //0x00003dd0 jne          LBB0_889
	0x49, 0x83, 0xc3, 0xe0, //0x00003dd6 addq         $-32, %r11
	0x49, 0x83, 0xc5, 0x20, //0x00003dda addq         $32, %r13
	0x49, 0x83, 0xfb, 0x1f, //0x00003dde cmpq         $31, %r11
	0x0f, 0x87, 0x07, 0xff, 0xff, 0xff, //0x00003de2 ja           LBB0_823
	0xc5, 0xf8, 0x77, //0x00003de8 vzeroupper   
	0x4c, 0x03, 0x6c, 0x24, 0x30, //0x00003deb addq         $48(%rsp), %r13
	0x49, 0x83, 0xfb, 0x10, //0x00003df0 cmpq         $16, %r11
	0xc5, 0x7a, 0x6f, 0x3d, 0x24, 0xc4, 0xff, 0xff, //0x00003df4 vmovdqu      $-15324(%rip), %xmm15  /* LCPI0_19+0(%rip) */
	0x0f, 0x82, 0x28, 0x01, 0x00, 0x00, //0x00003dfc jb           LBB0_858
	//0x00003e02 LBB0_840
	0x4c, 0x89, 0xe8, //0x00003e02 movq         %r13, %rax
	0x4c, 0x29, 0xf0, //0x00003e05 subq         %r14, %rax
	0x4d, 0x89, 0xf9, //0x00003e08 movq         %r15, %r9
	0x49, 0xf7, 0xd1, //0x00003e0b notq         %r9
	0x49, 0x01, 0xc1, //0x00003e0e addq         %rax, %r9
	0x31, 0xc0, //0x00003e11 xorl         %eax, %eax
	//0x00003e13 LBB0_841
	0xc4, 0xc1, 0x7a, 0x6f, 0x44, 0x05, 0x00, //0x00003e13 vmovdqu      (%r13,%rax), %xmm0
	0xc5, 0x81, 0x74, 0xc8, //0x00003e1a vpcmpeqb     %xmm0, %xmm15, %xmm1
	0xc5, 0xf9, 0x74, 0x15, 0x0a, 0xc4, 0xff, 0xff, //0x00003e1e vpcmpeqb     $-15350(%rip), %xmm0, %xmm2  /* LCPI0_20+0(%rip) */
	0xc5, 0xe9, 0xeb, 0xc9, //0x00003e26 vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xf9, 0xfc, 0x15, 0x0e, 0xc4, 0xff, 0xff, //0x00003e2a vpaddb       $-15346(%rip), %xmm0, %xmm2  /* LCPI0_21+0(%rip) */
	0xc5, 0xe9, 0xda, 0x1d, 0x16, 0xc4, 0xff, 0xff, //0x00003e32 vpminub      $-15338(%rip), %xmm2, %xmm3  /* LCPI0_22+0(%rip) */
	0xc5, 0xe9, 0x74, 0xd3, //0x00003e3a vpcmpeqb     %xmm3, %xmm2, %xmm2
	0xc5, 0xf9, 0xdb, 0x1d, 0xaa, 0xc3, 0xff, 0xff, //0x00003e3e vpand        $-15446(%rip), %xmm0, %xmm3  /* LCPI0_5+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x12, 0xc4, 0xff, 0xff, //0x00003e46 vpcmpeqb     $-15342(%rip), %xmm0, %xmm0  /* LCPI0_23+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x1a, 0xc4, 0xff, 0xff, //0x00003e4e vpcmpeqb     $-15334(%rip), %xmm3, %xmm3  /* LCPI0_24+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00003e56 vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xd9, 0xeb, 0xe1, //0x00003e5a vpor         %xmm1, %xmm4, %xmm4
	0xc5, 0xd9, 0xeb, 0xd2, //0x00003e5e vpor         %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xd0, //0x00003e62 vpmovmskb    %xmm0, %edx
	0xc5, 0xf9, 0xd7, 0xfb, //0x00003e66 vpmovmskb    %xmm3, %edi
	0xc5, 0xf9, 0xd7, 0xf1, //0x00003e6a vpmovmskb    %xmm1, %esi
	0xc5, 0xf9, 0xd7, 0xca, //0x00003e6e vpmovmskb    %xmm2, %ecx
	0xf7, 0xd1, //0x00003e72 notl         %ecx
	0x0f, 0xbc, 0xc9, //0x00003e74 bsfl         %ecx, %ecx
	0x83, 0xf9, 0x10, //0x00003e77 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00003e7a je           LBB0_843
	0xbb, 0xff, 0xff, 0xff, 0xff, //0x00003e80 movl         $-1, %ebx
	0xd3, 0xe3, //0x00003e85 shll         %cl, %ebx
	0xf7, 0xd3, //0x00003e87 notl         %ebx
	0x21, 0xda, //0x00003e89 andl         %ebx, %edx
	0x21, 0xdf, //0x00003e8b andl         %ebx, %edi
	0x21, 0xf3, //0x00003e8d andl         %esi, %ebx
	0x89, 0xde, //0x00003e8f movl         %ebx, %esi
	//0x00003e91 LBB0_843
	0x8d, 0x5a, 0xff, //0x00003e91 leal         $-1(%rdx), %ebx
	0x21, 0xd3, //0x00003e94 andl         %edx, %ebx
	0x0f, 0x85, 0xa2, 0x05, 0x00, 0x00, //0x00003e96 jne          LBB0_924
	0x8d, 0x5f, 0xff, //0x00003e9c leal         $-1(%rdi), %ebx
	0x21, 0xfb, //0x00003e9f andl         %edi, %ebx
	0x0f, 0x85, 0x97, 0x05, 0x00, 0x00, //0x00003ea1 jne          LBB0_924
	0x8d, 0x5e, 0xff, //0x00003ea7 leal         $-1(%rsi), %ebx
	0x21, 0xf3, //0x00003eaa andl         %esi, %ebx
	0x0f, 0x85, 0x8c, 0x05, 0x00, 0x00, //0x00003eac jne          LBB0_924
	0x85, 0xd2, //0x00003eb2 testl        %edx, %edx
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00003eb4 je           LBB0_849
	0x0f, 0xbc, 0xd2, //0x00003eba bsfl         %edx, %edx
	0x49, 0x83, 0xfa, 0xff, //0x00003ebd cmpq         $-1, %r10
	0x0f, 0x85, 0xb1, 0x05, 0x00, 0x00, //0x00003ec1 jne          LBB0_927
	0x4c, 0x01, 0xca, //0x00003ec7 addq         %r9, %rdx
	0x48, 0x01, 0xc2, //0x00003eca addq         %rax, %rdx
	0x49, 0x89, 0xd2, //0x00003ecd movq         %rdx, %r10
	//0x00003ed0 LBB0_849
	0x85, 0xff, //0x00003ed0 testl        %edi, %edi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00003ed2 je           LBB0_852
	0x0f, 0xbc, 0xd7, //0x00003ed8 bsfl         %edi, %edx
	0x49, 0x83, 0xfc, 0xff, //0x00003edb cmpq         $-1, %r12
	0x0f, 0x85, 0x93, 0x05, 0x00, 0x00, //0x00003edf jne          LBB0_927
	0x4c, 0x01, 0xca, //0x00003ee5 addq         %r9, %rdx
	0x48, 0x01, 0xc2, //0x00003ee8 addq         %rax, %rdx
	0x49, 0x89, 0xd4, //0x00003eeb movq         %rdx, %r12
	//0x00003eee LBB0_852
	0x85, 0xf6, //0x00003eee testl        %esi, %esi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00003ef0 je           LBB0_855
	0x0f, 0xbc, 0xd6, //0x00003ef6 bsfl         %esi, %edx
	0x49, 0x83, 0xf8, 0xff, //0x00003ef9 cmpq         $-1, %r8
	0x0f, 0x85, 0x75, 0x05, 0x00, 0x00, //0x00003efd jne          LBB0_927
	0x4c, 0x01, 0xca, //0x00003f03 addq         %r9, %rdx
	0x48, 0x01, 0xc2, //0x00003f06 addq         %rax, %rdx
	0x49, 0x89, 0xd0, //0x00003f09 movq         %rdx, %r8
	//0x00003f0c LBB0_855
	0x83, 0xf9, 0x10, //0x00003f0c cmpl         $16, %ecx
	0x0f, 0x85, 0x3c, 0x02, 0x00, 0x00, //0x00003f0f jne          LBB0_892
	0x49, 0x83, 0xc3, 0xf0, //0x00003f15 addq         $-16, %r11
	0x48, 0x83, 0xc0, 0x10, //0x00003f19 addq         $16, %rax
	0x49, 0x83, 0xfb, 0x0f, //0x00003f1d cmpq         $15, %r11
	0x0f, 0x87, 0xec, 0xfe, 0xff, 0xff, //0x00003f21 ja           LBB0_841
	0x49, 0x01, 0xc5, //0x00003f27 addq         %rax, %r13
	//0x00003f2a LBB0_858
	0x4d, 0x85, 0xdb, //0x00003f2a testq        %r11, %r11
	0x49, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00003f2d movabsq      $9223372036854775806, %r9
	0x0f, 0x84, 0x3e, 0x02, 0x00, 0x00, //0x00003f37 je           LBB0_894
	0x4b, 0x8d, 0x14, 0x2b, //0x00003f3d leaq         (%r11,%r13), %rdx
	0x4c, 0x89, 0xe8, //0x00003f41 movq         %r13, %rax
	0x4c, 0x29, 0xf0, //0x00003f44 subq         %r14, %rax
	0x4c, 0x89, 0xfe, //0x00003f47 movq         %r15, %rsi
	0x48, 0xf7, 0xd6, //0x00003f4a notq         %rsi
	0x48, 0x01, 0xc6, //0x00003f4d addq         %rax, %rsi
	0x31, 0xc9, //0x00003f50 xorl         %ecx, %ecx
	0xe9, 0x16, 0x00, 0x00, 0x00, //0x00003f52 jmp          LBB0_861
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00003f57 .p2align 4, 0x90
	//0x00003f60 LBB0_860
	0x48, 0x83, 0xc1, 0x01, //0x00003f60 addq         $1, %rcx
	0x49, 0x39, 0xcb, //0x00003f64 cmpq         %rcx, %r11
	0x0f, 0x84, 0x35, 0x04, 0x00, 0x00, //0x00003f67 je           LBB0_918
	//0x00003f6d LBB0_861
	0x41, 0x0f, 0xbe, 0x7c, 0x0d, 0x00, //0x00003f6d movsbl       (%r13,%rcx), %edi
	0x8d, 0x47, 0xd0, //0x00003f73 leal         $-48(%rdi), %eax
	0x83, 0xf8, 0x0a, //0x00003f76 cmpl         $10, %eax
	0x0f, 0x82, 0xe1, 0xff, 0xff, 0xff, //0x00003f79 jb           LBB0_860
	0x8d, 0x5f, 0xd5, //0x00003f7f leal         $-43(%rdi), %ebx
	0x83, 0xfb, 0x1a, //0x00003f82 cmpl         $26, %ebx
	0x0f, 0x87, 0x23, 0x00, 0x00, 0x00, //0x00003f85 ja           LBB0_866
	0x48, 0x8d, 0x3d, 0x96, 0x1b, 0x00, 0x00, //0x00003f8b leaq         $7062(%rip), %rdi  /* LJTI0_4+0(%rip) */
	0x48, 0x63, 0x04, 0x9f, //0x00003f92 movslq       (%rdi,%rbx,4), %rax
	0x48, 0x01, 0xf8, //0x00003f96 addq         %rdi, %rax
	0xff, 0xe0, //0x00003f99 jmpq         *%rax
	//0x00003f9b LBB0_864
	0x49, 0x83, 0xf8, 0xff, //0x00003f9b cmpq         $-1, %r8
	0x0f, 0x85, 0xa1, 0x04, 0x00, 0x00, //0x00003f9f jne          LBB0_925
	0x4c, 0x8d, 0x04, 0x0e, //0x00003fa5 leaq         (%rsi,%rcx), %r8
	0xe9, 0xb2, 0xff, 0xff, 0xff, //0x00003fa9 jmp          LBB0_860
	//0x00003fae LBB0_866
	0x83, 0xff, 0x65, //0x00003fae cmpl         $101, %edi
	0x0f, 0x85, 0xc1, 0x01, 0x00, 0x00, //0x00003fb1 jne          LBB0_893
	//0x00003fb7 LBB0_867
	0x49, 0x83, 0xfc, 0xff, //0x00003fb7 cmpq         $-1, %r12
	0x0f, 0x85, 0x85, 0x04, 0x00, 0x00, //0x00003fbb jne          LBB0_925
	0x4c, 0x8d, 0x24, 0x0e, //0x00003fc1 leaq         (%rsi,%rcx), %r12
	0xe9, 0x96, 0xff, 0xff, 0xff, //0x00003fc5 jmp          LBB0_860
	//0x00003fca LBB0_869
	0x49, 0x83, 0xfa, 0xff, //0x00003fca cmpq         $-1, %r10
	0x0f, 0x85, 0x72, 0x04, 0x00, 0x00, //0x00003fce jne          LBB0_925
	0x4c, 0x8d, 0x14, 0x0e, //0x00003fd4 leaq         (%rsi,%rcx), %r10
	0xe9, 0x83, 0xff, 0xff, 0xff, //0x00003fd8 jmp          LBB0_860
	//0x00003fdd LBB0_871
	0x49, 0x8b, 0x03, //0x00003fdd movq         (%r11), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00003fe0 cmpq         $4095, %rax
	0x0f, 0x8f, 0xa9, 0x0b, 0x00, 0x00, //0x00003fe6 jg           LBB0_1106
	0x48, 0x8d, 0x48, 0x01, //0x00003fec leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0b, //0x00003ff0 movq         %rcx, (%r11)
	0x49, 0xc7, 0x44, 0xc3, 0x08, 0x05, 0x00, 0x00, 0x00, //0x00003ff3 movq         $5, $8(%r11,%rax,8)
	0xe9, 0xbf, 0xf0, 0xff, 0xff, //0x00003ffc jmp          LBB0_634
	//0x00004001 LBB0_873
	0x49, 0x8b, 0x52, 0x08, //0x00004001 movq         $8(%r10), %rdx
	0x48, 0x8d, 0x42, 0xfc, //0x00004005 leaq         $-4(%rdx), %rax
	0x49, 0x39, 0xc6, //0x00004009 cmpq         %rax, %r14
	0x0f, 0x83, 0x15, 0x13, 0x00, 0x00, //0x0000400c jae          LBB0_1107
	0x43, 0x8b, 0x0c, 0x27, //0x00004012 movl         (%r15,%r12), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x00004016 cmpl         $1702063201, %ecx
	0x0f, 0x85, 0x0e, 0x13, 0x00, 0x00, //0x0000401c jne          LBB0_1109
	0x4d, 0x8d, 0x66, 0x05, //0x00004022 leaq         $5(%r14), %r12
	0xe9, 0x69, 0x00, 0x00, 0x00, //0x00004026 jmp          LBB0_887
	//0x0000402b LBB0_876
	0x49, 0x8b, 0x4a, 0x08, //0x0000402b movq         $8(%r10), %rcx
	0x48, 0x8d, 0x41, 0xfd, //0x0000402f leaq         $-3(%rcx), %rax
	0x49, 0x39, 0xc6, //0x00004033 cmpq         %rax, %r14
	0x0f, 0x83, 0xa8, 0x12, 0x00, 0x00, //0x00004036 jae          LBB0_1108
	0x41, 0x81, 0x38, 0x6e, 0x75, 0x6c, 0x6c, //0x0000403c cmpl         $1819047278, (%r8)
	0x0f, 0x84, 0x47, 0x00, 0x00, 0x00, //0x00004043 je           LBB0_886
	0xe9, 0x37, 0x13, 0x00, 0x00, //0x00004049 jmp          LBB0_878
	//0x0000404e LBB0_882
	0x49, 0x8b, 0x03, //0x0000404e movq         (%r11), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00004051 cmpq         $4095, %rax
	0x0f, 0x8f, 0x38, 0x0b, 0x00, 0x00, //0x00004057 jg           LBB0_1106
	0x48, 0x8d, 0x48, 0x01, //0x0000405d leaq         $1(%rax), %rcx
	0x49, 0x89, 0x0b, //0x00004061 movq         %rcx, (%r11)
	0x49, 0xc7, 0x44, 0xc3, 0x08, 0x06, 0x00, 0x00, 0x00, //0x00004064 movq         $6, $8(%r11,%rax,8)
	0xe9, 0x4e, 0xf0, 0xff, 0xff, //0x0000406d jmp          LBB0_634
	//0x00004072 LBB0_884
	0x49, 0x8b, 0x4a, 0x08, //0x00004072 movq         $8(%r10), %rcx
	0x48, 0x8d, 0x41, 0xfd, //0x00004076 leaq         $-3(%rcx), %rax
	0x49, 0x39, 0xc6, //0x0000407a cmpq         %rax, %r14
	0x0f, 0x83, 0x61, 0x12, 0x00, 0x00, //0x0000407d jae          LBB0_1108
	0x41, 0x81, 0x38, 0x74, 0x72, 0x75, 0x65, //0x00004083 cmpl         $1702195828, (%r8)
	0x0f, 0x85, 0x4b, 0x13, 0x00, 0x00, //0x0000408a jne          LBB0_1114
	//0x00004090 LBB0_886
	0x4d, 0x8d, 0x66, 0x04, //0x00004090 leaq         $4(%r14), %r12
	//0x00004094 LBB0_887
	0x4d, 0x89, 0x65, 0x00, //0x00004094 movq         %r12, (%r13)
	0x4c, 0x89, 0xf3, //0x00004098 movq         %r14, %rbx
	0x4d, 0x39, 0xce, //0x0000409b cmpq         %r9, %r14
	0x0f, 0x86, 0x1c, 0xf0, 0xff, 0xff, //0x0000409e jbe          LBB0_634
	0xe9, 0xd7, 0x0a, 0x00, 0x00, //0x000040a4 jmp          LBB0_1025
	//0x000040a9 LBB0_888
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x000040a9 leaq         $-1(%r12), %rax
	0x48, 0x39, 0xc2, //0x000040ae cmpq         %rax, %rdx
	0x49, 0xf7, 0xd4, //0x000040b1 notq         %r12
	0x4d, 0x0f, 0x45, 0xe5, //0x000040b4 cmovneq      %r13, %r12
	0x84, 0xc9, //0x000040b8 testb        %cl, %cl
	0x4d, 0x0f, 0x45, 0xec, //0x000040ba cmovneq      %r12, %r13
	0xe9, 0xc4, 0x02, 0x00, 0x00, //0x000040be jmp          LBB0_916
	//0x000040c3 LBB0_889
	0x4c, 0x01, 0xe9, //0x000040c3 addq         %r13, %rcx
	0x48, 0x03, 0x4c, 0x24, 0x30, //0x000040c6 addq         $48(%rsp), %rcx
	0xc5, 0xf8, 0x77, //0x000040cb vzeroupper   
	0xc5, 0x7a, 0x6f, 0x3d, 0x4a, 0xc1, 0xff, 0xff, //0x000040ce vmovdqu      $-16054(%rip), %xmm15  /* LCPI0_19+0(%rip) */
	0x49, 0x89, 0xcd, //0x000040d6 movq         %rcx, %r13
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000040d9 movq         $-1, %rcx
	0x4d, 0x85, 0xd2, //0x000040e0 testq        %r10, %r10
	0x0f, 0x85, 0xa2, 0x00, 0x00, 0x00, //0x000040e3 jne          LBB0_895
	0xe9, 0x52, 0x13, 0x00, 0x00, //0x000040e9 jmp          LBB0_1122
	//0x000040ee LBB0_890
	0x4d, 0x89, 0xc5, //0x000040ee movq         %r8, %r13
	0xe9, 0x79, 0xf9, 0xff, 0xff, //0x000040f1 jmp          LBB0_783
	//0x000040f6 LBB0_683
	0x4d, 0x01, 0xfc, //0x000040f6 addq         %r15, %r12
	0x48, 0x83, 0xfa, 0x20, //0x000040f9 cmpq         $32, %rdx
	0x0f, 0x82, 0xf2, 0x04, 0x00, 0x00, //0x000040fd jb           LBB0_941
	//0x00004103 LBB0_684
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00004103 vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00004109 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x0000410d vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x00004111 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x00004115 vpmovmskb    %ymm0, %edi
	0x85, 0xff, //0x00004119 testl        %edi, %edi
	0x0f, 0x85, 0x73, 0x04, 0x00, 0x00, //0x0000411b jne          LBB0_937
	0x4d, 0x85, 0xd2, //0x00004121 testq        %r10, %r10
	0x0f, 0x85, 0x81, 0x04, 0x00, 0x00, //0x00004124 jne          LBB0_939
	0x45, 0x31, 0xd2, //0x0000412a xorl         %r10d, %r10d
	0x48, 0x85, 0xf6, //0x0000412d testq        %rsi, %rsi
	0x0f, 0x84, 0xb7, 0x04, 0x00, 0x00, //0x00004130 je           LBB0_940
	//0x00004136 LBB0_687
	0x48, 0x0f, 0xbc, 0xc6, //0x00004136 bsfq         %rsi, %rax
	0x4d, 0x29, 0xfc, //0x0000413a subq         %r15, %r12
	0x49, 0x01, 0xc4, //0x0000413d addq         %rax, %r12
	0x49, 0x83, 0xc4, 0x01, //0x00004140 addq         $1, %r12
	0xe9, 0xf7, 0xf2, 0xff, 0xff, //0x00004144 jmp          LBB0_692
	//0x00004149 LBB0_891
	0x0f, 0xbc, 0xc2, //0x00004149 bsfl         %edx, %eax
	0xe9, 0x30, 0x02, 0x00, 0x00, //0x0000414c jmp          LBB0_915
	//0x00004151 LBB0_892
	0x89, 0xc9, //0x00004151 movl         %ecx, %ecx
	0x49, 0x01, 0xcd, //0x00004153 addq         %rcx, %r13
	0x49, 0x01, 0xc5, //0x00004156 addq         %rax, %r13
	0x49, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00004159 movabsq      $9223372036854775806, %r9
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00004163 movq         $-1, %rcx
	0x4d, 0x85, 0xd2, //0x0000416a testq        %r10, %r10
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x0000416d jne          LBB0_895
	0xe9, 0xc8, 0x12, 0x00, 0x00, //0x00004173 jmp          LBB0_1122
	//0x00004178 LBB0_893
	0x49, 0x01, 0xcd, //0x00004178 addq         %rcx, %r13
	//0x0000417b LBB0_894
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000417b movq         $-1, %rcx
	0x4d, 0x85, 0xd2, //0x00004182 testq        %r10, %r10
	0x0f, 0x84, 0xb5, 0x12, 0x00, 0x00, //0x00004185 je           LBB0_1122
	//0x0000418b LBB0_895
	0x4d, 0x85, 0xc0, //0x0000418b testq        %r8, %r8
	0xc5, 0xfe, 0x6f, 0x2d, 0x6a, 0xbe, 0xff, 0xff, //0x0000418e vmovdqu      $-16790(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xe2, 0xbe, 0xff, 0xff, //0x00004196 vmovdqu      $-16670(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xfa, 0xbe, 0xff, 0xff, //0x0000419e vmovdqu      $-16646(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x72, 0xbf, 0xff, 0xff, //0x000041a6 vmovdqu      $-16526(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x8a, 0xbf, 0xff, 0xff, //0x000041ae vmovdqu      $-16502(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x82, 0xbe, 0xff, 0xff, //0x000041b6 vmovdqu      $-16766(%rip), %ymm10  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x9a, 0xbf, 0xff, 0xff, //0x000041be vmovdqu      $-16486(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xb2, 0xbf, 0xff, 0xff, //0x000041c6 vmovdqu      $-16462(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xca, 0xbf, 0xff, 0xff, //0x000041ce vmovdqu      $-16438(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xe2, 0xbf, 0xff, 0xff, //0x000041d6 vmovdqu      $-16414(%rip), %ymm14  /* LCPI0_18+0(%rip) */
	0x0f, 0x84, 0x5c, 0x12, 0x00, 0x00, //0x000041de je           LBB0_1122
	0x4d, 0x85, 0xe4, //0x000041e4 testq        %r12, %r12
	0x0f, 0x84, 0x53, 0x12, 0x00, 0x00, //0x000041e7 je           LBB0_1122
	0x4c, 0x2b, 0x6c, 0x24, 0x30, //0x000041ed subq         $48(%rsp), %r13
	0x49, 0x8d, 0x4d, 0xff, //0x000041f2 leaq         $-1(%r13), %rcx
	0x49, 0x39, 0xca, //0x000041f6 cmpq         %rcx, %r10
	0x0f, 0x84, 0x41, 0x00, 0x00, 0x00, //0x000041f9 je           LBB0_903
	0x49, 0x39, 0xc8, //0x000041ff cmpq         %rcx, %r8
	0x0f, 0x84, 0x38, 0x00, 0x00, 0x00, //0x00004202 je           LBB0_903
	0x49, 0x39, 0xcc, //0x00004208 cmpq         %rcx, %r12
	0x0f, 0x84, 0x2f, 0x00, 0x00, 0x00, //0x0000420b je           LBB0_903
	0x4d, 0x85, 0xc0, //0x00004211 testq        %r8, %r8
	0x0f, 0x8e, 0x61, 0x00, 0x00, 0x00, //0x00004214 jle          LBB0_906
	0x49, 0x8d, 0x40, 0xff, //0x0000421a leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc4, //0x0000421e cmpq         %rax, %r12
	0x0f, 0x84, 0x54, 0x00, 0x00, 0x00, //0x00004221 je           LBB0_906
	0x49, 0xf7, 0xd0, //0x00004227 notq         %r8
	0x4d, 0x89, 0xc5, //0x0000422a movq         %r8, %r13
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x0000422d movq         $24(%rsp), %r10
	0x4d, 0x85, 0xed, //0x00004232 testq        %r13, %r13
	0x0f, 0x89, 0x16, 0x00, 0x00, 0x00, //0x00004235 jns          LBB0_904
	0xe9, 0xfd, 0x11, 0x00, 0x00, //0x0000423b jmp          LBB0_1121
	//0x00004240 LBB0_903
	0x49, 0xf7, 0xdd, //0x00004240 negq         %r13
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00004243 movq         $24(%rsp), %r10
	0x4d, 0x85, 0xed, //0x00004248 testq        %r13, %r13
	0x0f, 0x88, 0xec, 0x11, 0x00, 0x00, //0x0000424b js           LBB0_1121
	//0x00004251 LBB0_904
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00004251 movq         $16(%rsp), %rax
	0x4c, 0x8b, 0x20, //0x00004256 movq         (%rax), %r12
	//0x00004259 LBB0_905
	0x4d, 0x01, 0xec, //0x00004259 addq         %r13, %r12
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x0000425c movq         $16(%rsp), %r13
	0x4d, 0x89, 0x65, 0x00, //0x00004261 movq         %r12, (%r13)
	0x4c, 0x89, 0xf3, //0x00004265 movq         %r14, %rbx
	0x4d, 0x39, 0xce, //0x00004268 cmpq         %r9, %r14
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000426b movq         $32(%rsp), %r11
	0x0f, 0x86, 0x4a, 0xee, 0xff, 0xff, //0x00004270 jbe          LBB0_634
	0xe9, 0x05, 0x09, 0x00, 0x00, //0x00004276 jmp          LBB0_1025
	//0x0000427b LBB0_906
	0x4c, 0x89, 0xd0, //0x0000427b movq         %r10, %rax
	0x4c, 0x09, 0xe0, //0x0000427e orq          %r12, %rax
	0x0f, 0x99, 0xc1, //0x00004281 setns        %cl
	0x0f, 0x88, 0x89, 0x00, 0x00, 0x00, //0x00004284 js           LBB0_1120
	0x4d, 0x39, 0xe2, //0x0000428a cmpq         %r12, %r10
	0x0f, 0x8c, 0x80, 0x00, 0x00, 0x00, //0x0000428d jl           LBB0_1120
	0x49, 0xf7, 0xd2, //0x00004293 notq         %r10
	0x4d, 0x89, 0xd5, //0x00004296 movq         %r10, %r13
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00004299 movq         $24(%rsp), %r10
	0x4d, 0x85, 0xed, //0x0000429e testq        %r13, %r13
	0x0f, 0x89, 0xaa, 0xff, 0xff, 0xff, //0x000042a1 jns          LBB0_904
	0xe9, 0x91, 0x11, 0x00, 0x00, //0x000042a7 jmp          LBB0_1121
	//0x000042ac LBB0_909
	0x4d, 0x29, 0xfe, //0x000042ac subq         %r15, %r14
	0x49, 0x01, 0xce, //0x000042af addq         %rcx, %r14
	0x49, 0x39, 0xde, //0x000042b2 cmpq         %rbx, %r14
	0x0f, 0x82, 0xcf, 0xef, 0xff, 0xff, //0x000042b5 jb           LBB0_666
	0xe9, 0xa2, 0x08, 0x00, 0x00, //0x000042bb jmp          LBB0_1020
	//0x000042c0 LBB0_767
	0x4d, 0x01, 0xfc, //0x000042c0 addq         %r15, %r12
	0x48, 0x83, 0xfa, 0x20, //0x000042c3 cmpq         $32, %rdx
	0x0f, 0x82, 0x49, 0x04, 0x00, 0x00, //0x000042c7 jb           LBB0_958
	//0x000042cd LBB0_768
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x000042cd vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x000042d3 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x000042d7 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x000042db vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x000042df vpmovmskb    %ymm0, %edi
	0x85, 0xff, //0x000042e3 testl        %edi, %edi
	0x0f, 0x85, 0xca, 0x03, 0x00, 0x00, //0x000042e5 jne          LBB0_954
	0x4d, 0x85, 0xd2, //0x000042eb testq        %r10, %r10
	0x0f, 0x85, 0xd8, 0x03, 0x00, 0x00, //0x000042ee jne          LBB0_956
	0x45, 0x31, 0xd2, //0x000042f4 xorl         %r10d, %r10d
	0x48, 0x85, 0xf6, //0x000042f7 testq        %rsi, %rsi
	0x0f, 0x84, 0x0e, 0x04, 0x00, 0x00, //0x000042fa je           LBB0_957
	//0x00004300 LBB0_771
	0x48, 0x0f, 0xbc, 0xc6, //0x00004300 bsfq         %rsi, %rax
	0x4d, 0x29, 0xfc, //0x00004304 subq         %r15, %r12
	0x49, 0x01, 0xc4, //0x00004307 addq         %rax, %r12
	0x49, 0x83, 0xc4, 0x01, //0x0000430a addq         $1, %r12
	0xe9, 0xdd, 0xf6, 0xff, 0xff, //0x0000430e jmp          LBB0_776
	//0x00004313 LBB0_1120
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x00004313 leaq         $-1(%r12), %rax
	0x49, 0x39, 0xc2, //0x00004318 cmpq         %rax, %r10
	0x49, 0xf7, 0xd4, //0x0000431b notq         %r12
	0x4d, 0x0f, 0x45, 0xe5, //0x0000431e cmovneq      %r13, %r12
	0x84, 0xc9, //0x00004322 testb        %cl, %cl
	0x4d, 0x0f, 0x45, 0xec, //0x00004324 cmovneq      %r12, %r13
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00004328 movq         $24(%rsp), %r10
	0x4d, 0x85, 0xed, //0x0000432d testq        %r13, %r13
	0x0f, 0x89, 0x1b, 0xff, 0xff, 0xff, //0x00004330 jns          LBB0_904
	0xe9, 0x02, 0x11, 0x00, 0x00, //0x00004336 jmp          LBB0_1121
	//0x0000433b LBB0_910
	0x0f, 0xbc, 0xc2, //0x0000433b bsfl         %edx, %eax
	//0x0000433e LBB0_911
	0x4d, 0x01, 0xf7, //0x0000433e addq         %r14, %r15
	0x4d, 0x29, 0xef, //0x00004341 subq         %r13, %r15
	0x49, 0x29, 0xc7, //0x00004344 subq         %rax, %r15
	0x49, 0xf7, 0xd0, //0x00004347 notq         %r8
	0x4d, 0x01, 0xf8, //0x0000434a addq         %r15, %r8
	0x4d, 0x89, 0xc5, //0x0000434d movq         %r8, %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00004350 movq         $32(%rsp), %r11
	0x49, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00004355 movabsq      $9223372036854775806, %r9
	0xe9, 0x1d, 0xed, 0xff, 0xff, //0x0000435f jmp          LBB0_631
	//0x00004364 LBB0_912
	0x4d, 0x01, 0xf7, //0x00004364 addq         %r14, %r15
	0x4d, 0x29, 0xef, //0x00004367 subq         %r13, %r15
	0x48, 0xf7, 0xd1, //0x0000436a notq         %rcx
	0x4c, 0x01, 0xf9, //0x0000436d addq         %r15, %rcx
	0x49, 0x89, 0xcd, //0x00004370 movq         %rcx, %r13
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00004373 jmp          LBB0_916
	//0x00004378 LBB0_913
	0x89, 0xf0, //0x00004378 movl         %esi, %eax
	0xe9, 0x02, 0x00, 0x00, 0x00, //0x0000437a jmp          LBB0_915
	//0x0000437f LBB0_914
	0x89, 0xd8, //0x0000437f movl         %ebx, %eax
	//0x00004381 LBB0_915
	0x49, 0xf7, 0xd5, //0x00004381 notq         %r13
	0x49, 0x29, 0xc5, //0x00004384 subq         %rax, %r13
	//0x00004387 LBB0_916
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00004387 movq         $32(%rsp), %r11
	0xe9, 0xf0, 0xec, 0xff, 0xff, //0x0000438c jmp          LBB0_631
	//0x00004391 LBB0_917
	0x4d, 0x01, 0xfe, //0x00004391 addq         %r15, %r14
	0x48, 0x85, 0xf6, //0x00004394 testq        %rsi, %rsi
	0x0f, 0x85, 0x79, 0xee, 0xff, 0xff, //0x00004397 jne          LBB0_659
	0xe9, 0xb0, 0xee, 0xff, 0xff, //0x0000439d jmp          LBB0_664
	//0x000043a2 LBB0_918
	0x49, 0x89, 0xd5, //0x000043a2 movq         %rdx, %r13
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000043a5 movq         $-1, %rcx
	0x4d, 0x85, 0xd2, //0x000043ac testq        %r10, %r10
	0x0f, 0x85, 0xd6, 0xfd, 0xff, 0xff, //0x000043af jne          LBB0_895
	0xe9, 0x86, 0x10, 0x00, 0x00, //0x000043b5 jmp          LBB0_1122
	//0x000043ba LBB0_919
	0x0f, 0xbc, 0xc3, //0x000043ba bsfl         %ebx, %eax
	//0x000043bd LBB0_920
	0x49, 0xf7, 0xd5, //0x000043bd notq         %r13
	0x49, 0x29, 0xc5, //0x000043c0 subq         %rax, %r13
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000043c3 movq         $24(%rsp), %r10
	0x4d, 0x85, 0xed, //0x000043c8 testq        %r13, %r13
	0x0f, 0x89, 0x80, 0xfe, 0xff, 0xff, //0x000043cb jns          LBB0_904
	0xe9, 0x67, 0x10, 0x00, 0x00, //0x000043d1 jmp          LBB0_1121
	//0x000043d6 LBB0_921
	0x89, 0xd8, //0x000043d6 movl         %ebx, %eax
	0xe9, 0x61, 0xff, 0xff, 0xff, //0x000043d8 jmp          LBB0_911
	//0x000043dd LBB0_922
	0x89, 0xf8, //0x000043dd movl         %edi, %eax
	0xe9, 0x5a, 0xff, 0xff, 0xff, //0x000043df jmp          LBB0_911
	//0x000043e4 LBB0_923
	0x89, 0xf0, //0x000043e4 movl         %esi, %eax
	0xe9, 0x53, 0xff, 0xff, 0xff, //0x000043e6 jmp          LBB0_911
	//0x000043eb LBB0_803
	0x4d, 0x01, 0xfc, //0x000043eb addq         %r15, %r12
	0x48, 0x83, 0xfa, 0x20, //0x000043ee cmpq         $32, %rdx
	0x0f, 0x82, 0xc7, 0x04, 0x00, 0x00, //0x000043f2 jb           LBB0_979
	//0x000043f8 LBB0_804
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x000043f8 vmovdqu      (%r12), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x000043fe vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00004402 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x00004406 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf8, //0x0000440a vpmovmskb    %ymm0, %edi
	0x85, 0xff, //0x0000440e testl        %edi, %edi
	0x0f, 0x85, 0x48, 0x04, 0x00, 0x00, //0x00004410 jne          LBB0_975
	0x4d, 0x85, 0xd2, //0x00004416 testq        %r10, %r10
	0x0f, 0x85, 0x56, 0x04, 0x00, 0x00, //0x00004419 jne          LBB0_977
	0x45, 0x31, 0xd2, //0x0000441f xorl         %r10d, %r10d
	0x48, 0x85, 0xf6, //0x00004422 testq        %rsi, %rsi
	0x0f, 0x84, 0x8c, 0x04, 0x00, 0x00, //0x00004425 je           LBB0_978
	//0x0000442b LBB0_807
	0x48, 0x0f, 0xbc, 0xc6, //0x0000442b bsfq         %rsi, %rax
	0x4d, 0x29, 0xfc, //0x0000442f subq         %r15, %r12
	0x49, 0x01, 0xc4, //0x00004432 addq         %rax, %r12
	0x49, 0x83, 0xc4, 0x01, //0x00004435 addq         $1, %r12
	0xe9, 0x22, 0xf8, 0xff, 0xff, //0x00004439 jmp          LBB0_812
	//0x0000443e LBB0_924
	0x0f, 0xbc, 0xcb, //0x0000443e bsfl         %ebx, %ecx
	0xe9, 0x34, 0x00, 0x00, 0x00, //0x00004441 jmp          LBB0_928
	//0x00004446 LBB0_925
	0x4d, 0x01, 0xf7, //0x00004446 addq         %r14, %r15
	0x4d, 0x29, 0xef, //0x00004449 subq         %r13, %r15
	0x49, 0x29, 0xcf, //0x0000444c subq         %rcx, %r15
	0x4d, 0x89, 0xfd, //0x0000444f movq         %r15, %r13
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00004452 movq         $24(%rsp), %r10
	0xe9, 0x3c, 0x00, 0x00, 0x00, //0x00004457 jmp          LBB0_929
	//0x0000445c LBB0_926
	0x4d, 0x01, 0xfc, //0x0000445c addq         %r15, %r12
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000445f movq         $-1, %r8
	0x45, 0x31, 0xd2, //0x00004466 xorl         %r10d, %r10d
	0x48, 0x83, 0xfa, 0x20, //0x00004469 cmpq         $32, %rdx
	0x0f, 0x83, 0x90, 0xfc, 0xff, 0xff, //0x0000446d jae          LBB0_684
	0xe9, 0x7d, 0x01, 0x00, 0x00, //0x00004473 jmp          LBB0_941
	//0x00004478 LBB0_927
	0x89, 0xd1, //0x00004478 movl         %edx, %ecx
	//0x0000447a LBB0_928
	0x4d, 0x01, 0xf7, //0x0000447a addq         %r14, %r15
	0x4d, 0x29, 0xef, //0x0000447d subq         %r13, %r15
	0x49, 0x29, 0xcf, //0x00004480 subq         %rcx, %r15
	0x49, 0x29, 0xc7, //0x00004483 subq         %rax, %r15
	0x4d, 0x89, 0xfd, //0x00004486 movq         %r15, %r13
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00004489 movq         $24(%rsp), %r10
	0x49, 0xb9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x0000448e movabsq      $9223372036854775806, %r9
	//0x00004498 LBB0_929
	0xc5, 0xfe, 0x6f, 0x2d, 0x60, 0xbb, 0xff, 0xff, //0x00004498 vmovdqu      $-17568(%rip), %ymm5  /* LCPI0_0+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xd8, 0xbb, 0xff, 0xff, //0x000044a0 vmovdqu      $-17448(%rip), %ymm6  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xf0, 0xbb, 0xff, 0xff, //0x000044a8 vmovdqu      $-17424(%rip), %ymm7  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x68, 0xbc, 0xff, 0xff, //0x000044b0 vmovdqu      $-17304(%rip), %ymm8  /* LCPI0_13+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x80, 0xbc, 0xff, 0xff, //0x000044b8 vmovdqu      $-17280(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0x78, 0xbb, 0xff, 0xff, //0x000044c0 vmovdqu      $-17544(%rip), %ymm10  /* LCPI0_2+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x90, 0xbc, 0xff, 0xff, //0x000044c8 vmovdqu      $-17264(%rip), %ymm11  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xa8, 0xbc, 0xff, 0xff, //0x000044d0 vmovdqu      $-17240(%rip), %ymm12  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xc0, 0xbc, 0xff, 0xff, //0x000044d8 vmovdqu      $-17216(%rip), %ymm13  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xd8, 0xbc, 0xff, 0xff, //0x000044e0 vmovdqu      $-17192(%rip), %ymm14  /* LCPI0_18+0(%rip) */
	0x4d, 0x85, 0xed, //0x000044e8 testq        %r13, %r13
	0x0f, 0x89, 0x60, 0xfd, 0xff, 0xff, //0x000044eb jns          LBB0_904
	0xe9, 0x47, 0x0f, 0x00, 0x00, //0x000044f1 jmp          LBB0_1121
	//0x000044f6 LBB0_930
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x000044f6 movq         $-1, %r12
	0x48, 0xc7, 0x44, 0x24, 0x30, 0xff, 0xff, 0xff, 0xff, //0x000044fd movq         $-1, $48(%rsp)
	0x4d, 0x89, 0xc5, //0x00004506 movq         %r8, %r13
	0x49, 0x83, 0xfa, 0x10, //0x00004509 cmpq         $16, %r10
	0x4c, 0x89, 0x44, 0x24, 0x28, //0x0000450d movq         %r8, $40(%rsp)
	0x0f, 0x83, 0x9d, 0xf1, 0xff, 0xff, //0x00004512 jae          LBB0_725
	0xe9, 0xbe, 0xf2, 0xff, 0xff, //0x00004518 jmp          LBB0_743
	//0x0000451d LBB0_931
	0x89, 0xf8, //0x0000451d movl         %edi, %eax
	0xe9, 0x99, 0xfe, 0xff, 0xff, //0x0000451f jmp          LBB0_920
	//0x00004524 LBB0_932
	0x89, 0xf0, //0x00004524 movl         %esi, %eax
	0xe9, 0x92, 0xfe, 0xff, 0xff, //0x00004526 jmp          LBB0_920
	//0x0000452b LBB0_933
	0x89, 0xd0, //0x0000452b movl         %edx, %eax
	0xe9, 0x8b, 0xfe, 0xff, 0xff, //0x0000452d jmp          LBB0_920
	//0x00004532 LBB0_934
	0x4d, 0x01, 0xfc, //0x00004532 addq         %r15, %r12
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00004535 movq         $-1, %r8
	0x45, 0x31, 0xd2, //0x0000453c xorl         %r10d, %r10d
	0x48, 0x83, 0xfa, 0x20, //0x0000453f cmpq         $32, %rdx
	0x0f, 0x83, 0x84, 0xfd, 0xff, 0xff, //0x00004543 jae          LBB0_768
	0xe9, 0xc8, 0x01, 0x00, 0x00, //0x00004549 jmp          LBB0_958
	//0x0000454e LBB0_935
	0x4d, 0x01, 0xfc, //0x0000454e addq         %r15, %r12
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00004551 movq         $-1, %r8
	0x45, 0x31, 0xd2, //0x00004558 xorl         %r10d, %r10d
	0x48, 0x83, 0xfa, 0x20, //0x0000455b cmpq         $32, %rdx
	0x0f, 0x83, 0x93, 0xfe, 0xff, 0xff, //0x0000455f jae          LBB0_804
	0xe9, 0x55, 0x03, 0x00, 0x00, //0x00004565 jmp          LBB0_979
	//0x0000456a LBB0_936
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x0000456a movq         $-1, %r12
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00004571 movq         $-1, %r10
	0x4c, 0x8b, 0x6c, 0x24, 0x30, //0x00004578 movq         $48(%rsp), %r13
	0x49, 0x83, 0xfb, 0x10, //0x0000457d cmpq         $16, %r11
	0xc5, 0x7a, 0x6f, 0x3d, 0x97, 0xbc, 0xff, 0xff, //0x00004581 vmovdqu      $-17257(%rip), %xmm15  /* LCPI0_19+0(%rip) */
	0x0f, 0x83, 0x73, 0xf8, 0xff, 0xff, //0x00004589 jae          LBB0_840
	0xe9, 0x96, 0xf9, 0xff, 0xff, //0x0000458f jmp          LBB0_858
	//0x00004594 LBB0_937
	0x49, 0x83, 0xf8, 0xff, //0x00004594 cmpq         $-1, %r8
	0x0f, 0x85, 0x0d, 0x00, 0x00, 0x00, //0x00004598 jne          LBB0_939
	0x4c, 0x89, 0xe0, //0x0000459e movq         %r12, %rax
	0x4c, 0x29, 0xf8, //0x000045a1 subq         %r15, %rax
	0x4c, 0x0f, 0xbc, 0xc7, //0x000045a4 bsfq         %rdi, %r8
	0x49, 0x01, 0xc0, //0x000045a8 addq         %rax, %r8
	//0x000045ab LBB0_939
	0x44, 0x89, 0xd0, //0x000045ab movl         %r10d, %eax
	0xf7, 0xd0, //0x000045ae notl         %eax
	0x21, 0xf8, //0x000045b0 andl         %edi, %eax
	0x8d, 0x0c, 0x00, //0x000045b2 leal         (%rax,%rax), %ecx
	0x41, 0x8d, 0x1c, 0x42, //0x000045b5 leal         (%r10,%rax,2), %ebx
	0xf7, 0xd1, //0x000045b9 notl         %ecx
	0x21, 0xf9, //0x000045bb andl         %edi, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x000045bd andl         $-1431655766, %ecx
	0x45, 0x31, 0xd2, //0x000045c3 xorl         %r10d, %r10d
	0x01, 0xc1, //0x000045c6 addl         %eax, %ecx
	0x41, 0x0f, 0x92, 0xc2, //0x000045c8 setb         %r10b
	0x01, 0xc9, //0x000045cc addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x000045ce xorl         $1431655765, %ecx
	0x21, 0xd9, //0x000045d4 andl         %ebx, %ecx
	0xf7, 0xd1, //0x000045d6 notl         %ecx
	0x21, 0xce, //0x000045d8 andl         %ecx, %esi
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x000045da movq         $16(%rsp), %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000045df movq         $32(%rsp), %r11
	0x48, 0x85, 0xf6, //0x000045e4 testq        %rsi, %rsi
	0x0f, 0x85, 0x49, 0xfb, 0xff, 0xff, //0x000045e7 jne          LBB0_687
	//0x000045ed LBB0_940
	0x49, 0x83, 0xc4, 0x20, //0x000045ed addq         $32, %r12
	0x48, 0x83, 0xc2, 0xe0, //0x000045f1 addq         $-32, %rdx
	//0x000045f5 LBB0_941
	0x4d, 0x85, 0xd2, //0x000045f5 testq        %r10, %r10
	0x0f, 0x85, 0xd8, 0x01, 0x00, 0x00, //0x000045f8 jne          LBB0_971
	0x4d, 0x89, 0xc2, //0x000045fe movq         %r8, %r10
	0x48, 0x85, 0xd2, //0x00004601 testq        %rdx, %rdx
	0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, //0x00004604 je           LBB0_953
	//0x0000460a LBB0_943
	0x4c, 0x89, 0xff, //0x0000460a movq         %r15, %rdi
	0x48, 0xf7, 0xdf, //0x0000460d negq         %rdi
	//0x00004610 LBB0_944
	0x31, 0xf6, //0x00004610 xorl         %esi, %esi
	//0x00004612 LBB0_945
	0x41, 0x0f, 0xb6, 0x1c, 0x34, //0x00004612 movzbl       (%r12,%rsi), %ebx
	0x80, 0xfb, 0x22, //0x00004617 cmpb         $34, %bl
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x0000461a je           LBB0_952
	0x80, 0xfb, 0x5c, //0x00004620 cmpb         $92, %bl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00004623 je           LBB0_950
	0x48, 0x83, 0xc6, 0x01, //0x00004629 addq         $1, %rsi
	0x48, 0x39, 0xf2, //0x0000462d cmpq         %rsi, %rdx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00004630 jne          LBB0_945
	0xe9, 0x5f, 0x00, 0x00, 0x00, //0x00004636 jmp          LBB0_948
	//0x0000463b LBB0_950
	0x48, 0x8d, 0x42, 0xff, //0x0000463b leaq         $-1(%rdx), %rax
	0x48, 0x39, 0xf0, //0x0000463f cmpq         %rsi, %rax
	0x0f, 0x84, 0xb1, 0x0e, 0x00, 0x00, //0x00004642 je           LBB0_990
	0x4a, 0x8d, 0x04, 0x27, //0x00004648 leaq         (%rdi,%r12), %rax
	0x48, 0x01, 0xf0, //0x0000464c addq         %rsi, %rax
	0x49, 0x83, 0xfa, 0xff, //0x0000464f cmpq         $-1, %r10
	0x4c, 0x0f, 0x44, 0xc0, //0x00004653 cmoveq       %rax, %r8
	0x4c, 0x0f, 0x44, 0xd0, //0x00004657 cmoveq       %rax, %r10
	0x49, 0x01, 0xf4, //0x0000465b addq         %rsi, %r12
	0x49, 0x83, 0xc4, 0x02, //0x0000465e addq         $2, %r12
	0x48, 0x89, 0xd0, //0x00004662 movq         %rdx, %rax
	0x48, 0x29, 0xf0, //0x00004665 subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00004668 addq         $-2, %rax
	0x48, 0x83, 0xc2, 0xfe, //0x0000466c addq         $-2, %rdx
	0x48, 0x39, 0xf2, //0x00004670 cmpq         %rsi, %rdx
	0x48, 0x89, 0xc2, //0x00004673 movq         %rax, %rdx
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00004676 movq         $16(%rsp), %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000467b movq         $32(%rsp), %r11
	0x0f, 0x85, 0x8a, 0xff, 0xff, 0xff, //0x00004680 jne          LBB0_944
	0xe9, 0x20, 0x05, 0x00, 0x00, //0x00004686 jmp          LBB0_814
	//0x0000468b LBB0_952
	0x49, 0x01, 0xf4, //0x0000468b addq         %rsi, %r12
	0x49, 0x83, 0xc4, 0x01, //0x0000468e addq         $1, %r12
	//0x00004692 LBB0_953
	0x4d, 0x29, 0xfc, //0x00004692 subq         %r15, %r12
	0xe9, 0xa6, 0xed, 0xff, 0xff, //0x00004695 jmp          LBB0_692
	//0x0000469a LBB0_948
	0x80, 0xfb, 0x22, //0x0000469a cmpb         $34, %bl
	0x0f, 0x85, 0x56, 0x0e, 0x00, 0x00, //0x0000469d jne          LBB0_990
	0x49, 0x01, 0xd4, //0x000046a3 addq         %rdx, %r12
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x000046a6 movq         $16(%rsp), %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000046ab movq         $32(%rsp), %r11
	0xe9, 0xdd, 0xff, 0xff, 0xff, //0x000046b0 jmp          LBB0_953
	//0x000046b5 LBB0_954
	0x49, 0x83, 0xf8, 0xff, //0x000046b5 cmpq         $-1, %r8
	0x0f, 0x85, 0x0d, 0x00, 0x00, 0x00, //0x000046b9 jne          LBB0_956
	0x4c, 0x89, 0xe0, //0x000046bf movq         %r12, %rax
	0x4c, 0x29, 0xf8, //0x000046c2 subq         %r15, %rax
	0x4c, 0x0f, 0xbc, 0xc7, //0x000046c5 bsfq         %rdi, %r8
	0x49, 0x01, 0xc0, //0x000046c9 addq         %rax, %r8
	//0x000046cc LBB0_956
	0x44, 0x89, 0xd0, //0x000046cc movl         %r10d, %eax
	0xf7, 0xd0, //0x000046cf notl         %eax
	0x21, 0xf8, //0x000046d1 andl         %edi, %eax
	0x8d, 0x0c, 0x00, //0x000046d3 leal         (%rax,%rax), %ecx
	0x41, 0x8d, 0x1c, 0x42, //0x000046d6 leal         (%r10,%rax,2), %ebx
	0xf7, 0xd1, //0x000046da notl         %ecx
	0x21, 0xf9, //0x000046dc andl         %edi, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x000046de andl         $-1431655766, %ecx
	0x45, 0x31, 0xd2, //0x000046e4 xorl         %r10d, %r10d
	0x01, 0xc1, //0x000046e7 addl         %eax, %ecx
	0x41, 0x0f, 0x92, 0xc2, //0x000046e9 setb         %r10b
	0x01, 0xc9, //0x000046ed addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x000046ef xorl         $1431655765, %ecx
	0x21, 0xd9, //0x000046f5 andl         %ebx, %ecx
	0xf7, 0xd1, //0x000046f7 notl         %ecx
	0x21, 0xce, //0x000046f9 andl         %ecx, %esi
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x000046fb movq         $16(%rsp), %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00004700 movq         $32(%rsp), %r11
	0x48, 0x85, 0xf6, //0x00004705 testq        %rsi, %rsi
	0x0f, 0x85, 0xf2, 0xfb, 0xff, 0xff, //0x00004708 jne          LBB0_771
	//0x0000470e LBB0_957
	0x49, 0x83, 0xc4, 0x20, //0x0000470e addq         $32, %r12
	0x48, 0x83, 0xc2, 0xe0, //0x00004712 addq         $-32, %rdx
	//0x00004716 LBB0_958
	0x4d, 0x85, 0xd2, //0x00004716 testq        %r10, %r10
	0x0f, 0x85, 0xfb, 0x00, 0x00, 0x00, //0x00004719 jne          LBB0_973
	0x4d, 0x89, 0xc2, //0x0000471f movq         %r8, %r10
	0x48, 0x85, 0xd2, //0x00004722 testq        %rdx, %rdx
	0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, //0x00004725 je           LBB0_970
	//0x0000472b LBB0_960
	0x4c, 0x89, 0xff, //0x0000472b movq         %r15, %rdi
	0x48, 0xf7, 0xdf, //0x0000472e negq         %rdi
	//0x00004731 LBB0_961
	0x31, 0xf6, //0x00004731 xorl         %esi, %esi
	//0x00004733 LBB0_962
	0x41, 0x0f, 0xb6, 0x1c, 0x34, //0x00004733 movzbl       (%r12,%rsi), %ebx
	0x80, 0xfb, 0x22, //0x00004738 cmpb         $34, %bl
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x0000473b je           LBB0_969
	0x80, 0xfb, 0x5c, //0x00004741 cmpb         $92, %bl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00004744 je           LBB0_967
	0x48, 0x83, 0xc6, 0x01, //0x0000474a addq         $1, %rsi
	0x48, 0x39, 0xf2, //0x0000474e cmpq         %rsi, %rdx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00004751 jne          LBB0_962
	0xe9, 0x5f, 0x00, 0x00, 0x00, //0x00004757 jmp          LBB0_965
	//0x0000475c LBB0_967
	0x48, 0x8d, 0x42, 0xff, //0x0000475c leaq         $-1(%rdx), %rax
	0x48, 0x39, 0xf0, //0x00004760 cmpq         %rsi, %rax
	0x0f, 0x84, 0x90, 0x0d, 0x00, 0x00, //0x00004763 je           LBB0_990
	0x4a, 0x8d, 0x04, 0x27, //0x00004769 leaq         (%rdi,%r12), %rax
	0x48, 0x01, 0xf0, //0x0000476d addq         %rsi, %rax
	0x49, 0x83, 0xfa, 0xff, //0x00004770 cmpq         $-1, %r10
	0x4c, 0x0f, 0x44, 0xc0, //0x00004774 cmoveq       %rax, %r8
	0x4c, 0x0f, 0x44, 0xd0, //0x00004778 cmoveq       %rax, %r10
	0x49, 0x01, 0xf4, //0x0000477c addq         %rsi, %r12
	0x49, 0x83, 0xc4, 0x02, //0x0000477f addq         $2, %r12
	0x48, 0x89, 0xd0, //0x00004783 movq         %rdx, %rax
	0x48, 0x29, 0xf0, //0x00004786 subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00004789 addq         $-2, %rax
	0x48, 0x83, 0xc2, 0xfe, //0x0000478d addq         $-2, %rdx
	0x48, 0x39, 0xf2, //0x00004791 cmpq         %rsi, %rdx
	0x48, 0x89, 0xc2, //0x00004794 movq         %rax, %rdx
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00004797 movq         $16(%rsp), %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000479c movq         $32(%rsp), %r11
	0x0f, 0x85, 0x8a, 0xff, 0xff, 0xff, //0x000047a1 jne          LBB0_961
	0xe9, 0xff, 0x03, 0x00, 0x00, //0x000047a7 jmp          LBB0_814
	//0x000047ac LBB0_969
	0x49, 0x01, 0xf4, //0x000047ac addq         %rsi, %r12
	0x49, 0x83, 0xc4, 0x01, //0x000047af addq         $1, %r12
	//0x000047b3 LBB0_970
	0x4d, 0x29, 0xfc, //0x000047b3 subq         %r15, %r12
	0xe9, 0x35, 0xf2, 0xff, 0xff, //0x000047b6 jmp          LBB0_776
	//0x000047bb LBB0_965
	0x80, 0xfb, 0x22, //0x000047bb cmpb         $34, %bl
	0x0f, 0x85, 0x35, 0x0d, 0x00, 0x00, //0x000047be jne          LBB0_990
	0x49, 0x01, 0xd4, //0x000047c4 addq         %rdx, %r12
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x000047c7 movq         $16(%rsp), %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000047cc movq         $32(%rsp), %r11
	0xe9, 0xdd, 0xff, 0xff, 0xff, //0x000047d1 jmp          LBB0_970
	//0x000047d6 LBB0_971
	0x48, 0x85, 0xd2, //0x000047d6 testq        %rdx, %rdx
	0x0f, 0x84, 0x1a, 0x0d, 0x00, 0x00, //0x000047d9 je           LBB0_990
	0x4d, 0x89, 0xfa, //0x000047df movq         %r15, %r10
	0x49, 0xf7, 0xd2, //0x000047e2 notq         %r10
	0x4d, 0x01, 0xe2, //0x000047e5 addq         %r12, %r10
	0x49, 0x83, 0xf8, 0xff, //0x000047e8 cmpq         $-1, %r8
	0x4c, 0x89, 0xc0, //0x000047ec movq         %r8, %rax
	0x49, 0x0f, 0x44, 0xc2, //0x000047ef cmoveq       %r10, %rax
	0x4d, 0x0f, 0x45, 0xd0, //0x000047f3 cmovneq      %r8, %r10
	0x49, 0x83, 0xc4, 0x01, //0x000047f7 addq         $1, %r12
	0x48, 0x83, 0xc2, 0xff, //0x000047fb addq         $-1, %rdx
	0x49, 0x89, 0xc0, //0x000047ff movq         %rax, %r8
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00004802 movq         $16(%rsp), %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00004807 movq         $32(%rsp), %r11
	0x48, 0x85, 0xd2, //0x0000480c testq        %rdx, %rdx
	0x0f, 0x85, 0xf5, 0xfd, 0xff, 0xff, //0x0000480f jne          LBB0_943
	0xe9, 0x78, 0xfe, 0xff, 0xff, //0x00004815 jmp          LBB0_953
	//0x0000481a LBB0_973
	0x48, 0x85, 0xd2, //0x0000481a testq        %rdx, %rdx
	0x0f, 0x84, 0xd6, 0x0c, 0x00, 0x00, //0x0000481d je           LBB0_990
	0x4d, 0x89, 0xfa, //0x00004823 movq         %r15, %r10
	0x49, 0xf7, 0xd2, //0x00004826 notq         %r10
	0x4d, 0x01, 0xe2, //0x00004829 addq         %r12, %r10
	0x49, 0x83, 0xf8, 0xff, //0x0000482c cmpq         $-1, %r8
	0x4c, 0x89, 0xc0, //0x00004830 movq         %r8, %rax
	0x49, 0x0f, 0x44, 0xc2, //0x00004833 cmoveq       %r10, %rax
	0x4d, 0x0f, 0x45, 0xd0, //0x00004837 cmovneq      %r8, %r10
	0x49, 0x83, 0xc4, 0x01, //0x0000483b addq         $1, %r12
	0x48, 0x83, 0xc2, 0xff, //0x0000483f addq         $-1, %rdx
	0x49, 0x89, 0xc0, //0x00004843 movq         %rax, %r8
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00004846 movq         $16(%rsp), %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000484b movq         $32(%rsp), %r11
	0x48, 0x85, 0xd2, //0x00004850 testq        %rdx, %rdx
	0x0f, 0x85, 0xd2, 0xfe, 0xff, 0xff, //0x00004853 jne          LBB0_960
	0xe9, 0x55, 0xff, 0xff, 0xff, //0x00004859 jmp          LBB0_970
	//0x0000485e LBB0_975
	0x49, 0x83, 0xf8, 0xff, //0x0000485e cmpq         $-1, %r8
	0x0f, 0x85, 0x0d, 0x00, 0x00, 0x00, //0x00004862 jne          LBB0_977
	0x4c, 0x89, 0xe0, //0x00004868 movq         %r12, %rax
	0x4c, 0x29, 0xf8, //0x0000486b subq         %r15, %rax
	0x4c, 0x0f, 0xbc, 0xc7, //0x0000486e bsfq         %rdi, %r8
	0x49, 0x01, 0xc0, //0x00004872 addq         %rax, %r8
	//0x00004875 LBB0_977
	0x44, 0x89, 0xd0, //0x00004875 movl         %r10d, %eax
	0xf7, 0xd0, //0x00004878 notl         %eax
	0x21, 0xf8, //0x0000487a andl         %edi, %eax
	0x8d, 0x0c, 0x00, //0x0000487c leal         (%rax,%rax), %ecx
	0x41, 0x8d, 0x1c, 0x42, //0x0000487f leal         (%r10,%rax,2), %ebx
	0xf7, 0xd1, //0x00004883 notl         %ecx
	0x21, 0xf9, //0x00004885 andl         %edi, %ecx
	0x81, 0xe1, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004887 andl         $-1431655766, %ecx
	0x45, 0x31, 0xd2, //0x0000488d xorl         %r10d, %r10d
	0x01, 0xc1, //0x00004890 addl         %eax, %ecx
	0x41, 0x0f, 0x92, 0xc2, //0x00004892 setb         %r10b
	0x01, 0xc9, //0x00004896 addl         %ecx, %ecx
	0x81, 0xf1, 0x55, 0x55, 0x55, 0x55, //0x00004898 xorl         $1431655765, %ecx
	0x21, 0xd9, //0x0000489e andl         %ebx, %ecx
	0xf7, 0xd1, //0x000048a0 notl         %ecx
	0x21, 0xce, //0x000048a2 andl         %ecx, %esi
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x000048a4 movq         $16(%rsp), %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000048a9 movq         $32(%rsp), %r11
	0x48, 0x85, 0xf6, //0x000048ae testq        %rsi, %rsi
	0x0f, 0x85, 0x74, 0xfb, 0xff, 0xff, //0x000048b1 jne          LBB0_807
	//0x000048b7 LBB0_978
	0x49, 0x83, 0xc4, 0x20, //0x000048b7 addq         $32, %r12
	0x48, 0x83, 0xc2, 0xe0, //0x000048bb addq         $-32, %rdx
	//0x000048bf LBB0_979
	0x4d, 0x85, 0xd2, //0x000048bf testq        %r10, %r10
	0x0f, 0x85, 0xad, 0x00, 0x00, 0x00, //0x000048c2 jne          LBB0_993
	0x4d, 0x89, 0xc2, //0x000048c8 movq         %r8, %r10
	0x48, 0x85, 0xd2, //0x000048cb testq        %rdx, %rdx
	0x0f, 0x84, 0x7e, 0x00, 0x00, 0x00, //0x000048ce je           LBB0_992
	//0x000048d4 LBB0_981
	0x4c, 0x89, 0xff, //0x000048d4 movq         %r15, %rdi
	0x48, 0xf7, 0xdf, //0x000048d7 negq         %rdi
	//0x000048da LBB0_982
	0x31, 0xf6, //0x000048da xorl         %esi, %esi
	//0x000048dc LBB0_983
	0x41, 0x0f, 0xb6, 0x1c, 0x34, //0x000048dc movzbl       (%r12,%rsi), %ebx
	0x80, 0xfb, 0x22, //0x000048e1 cmpb         $34, %bl
	0x0f, 0x84, 0x61, 0x00, 0x00, 0x00, //0x000048e4 je           LBB0_991
	0x80, 0xfb, 0x5c, //0x000048ea cmpb         $92, %bl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000048ed je           LBB0_988
	0x48, 0x83, 0xc6, 0x01, //0x000048f3 addq         $1, %rsi
	0x48, 0x39, 0xf2, //0x000048f7 cmpq         %rsi, %rdx
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x000048fa jne          LBB0_983
	0xe9, 0x5f, 0x00, 0x00, 0x00, //0x00004900 jmp          LBB0_986
	//0x00004905 LBB0_988
	0x48, 0x8d, 0x42, 0xff, //0x00004905 leaq         $-1(%rdx), %rax
	0x48, 0x39, 0xf0, //0x00004909 cmpq         %rsi, %rax
	0x0f, 0x84, 0xe7, 0x0b, 0x00, 0x00, //0x0000490c je           LBB0_990
	0x4a, 0x8d, 0x04, 0x27, //0x00004912 leaq         (%rdi,%r12), %rax
	0x48, 0x01, 0xf0, //0x00004916 addq         %rsi, %rax
	0x49, 0x83, 0xfa, 0xff, //0x00004919 cmpq         $-1, %r10
	0x4c, 0x0f, 0x44, 0xc0, //0x0000491d cmoveq       %rax, %r8
	0x4c, 0x0f, 0x44, 0xd0, //0x00004921 cmoveq       %rax, %r10
	0x49, 0x01, 0xf4, //0x00004925 addq         %rsi, %r12
	0x49, 0x83, 0xc4, 0x02, //0x00004928 addq         $2, %r12
	0x48, 0x89, 0xd0, //0x0000492c movq         %rdx, %rax
	0x48, 0x29, 0xf0, //0x0000492f subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00004932 addq         $-2, %rax
	0x48, 0x83, 0xc2, 0xfe, //0x00004936 addq         $-2, %rdx
	0x48, 0x39, 0xf2, //0x0000493a cmpq         %rsi, %rdx
	0x48, 0x89, 0xc2, //0x0000493d movq         %rax, %rdx
	0x0f, 0x85, 0x94, 0xff, 0xff, 0xff, //0x00004940 jne          LBB0_982
	0xe9, 0xae, 0x0b, 0x00, 0x00, //0x00004946 jmp          LBB0_990
	//0x0000494b LBB0_991
	0x49, 0x01, 0xf4, //0x0000494b addq         %rsi, %r12
	0x49, 0x83, 0xc4, 0x01, //0x0000494e addq         $1, %r12
	//0x00004952 LBB0_992
	0x4d, 0x29, 0xfc, //0x00004952 subq         %r15, %r12
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x00004955 movq         $16(%rsp), %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000495a movq         $32(%rsp), %r11
	0xe9, 0xfc, 0xf2, 0xff, 0xff, //0x0000495f jmp          LBB0_812
	//0x00004964 LBB0_986
	0x80, 0xfb, 0x22, //0x00004964 cmpb         $34, %bl
	0x0f, 0x85, 0x8c, 0x0b, 0x00, 0x00, //0x00004967 jne          LBB0_990
	0x49, 0x01, 0xd4, //0x0000496d addq         %rdx, %r12
	0xe9, 0xdd, 0xff, 0xff, 0xff, //0x00004970 jmp          LBB0_992
	//0x00004975 LBB0_993
	0x48, 0x85, 0xd2, //0x00004975 testq        %rdx, %rdx
	0x0f, 0x84, 0x7b, 0x0b, 0x00, 0x00, //0x00004978 je           LBB0_990
	0x4d, 0x89, 0xfa, //0x0000497e movq         %r15, %r10
	0x49, 0xf7, 0xd2, //0x00004981 notq         %r10
	0x4d, 0x01, 0xe2, //0x00004984 addq         %r12, %r10
	0x49, 0x83, 0xf8, 0xff, //0x00004987 cmpq         $-1, %r8
	0x4c, 0x89, 0xc0, //0x0000498b movq         %r8, %rax
	0x49, 0x0f, 0x44, 0xc2, //0x0000498e cmoveq       %r10, %rax
	0x4d, 0x0f, 0x45, 0xd0, //0x00004992 cmovneq      %r8, %r10
	0x49, 0x83, 0xc4, 0x01, //0x00004996 addq         $1, %r12
	0x48, 0x83, 0xc2, 0xff, //0x0000499a addq         $-1, %rdx
	0x49, 0x89, 0xc0, //0x0000499e movq         %rax, %r8
	0x48, 0x85, 0xd2, //0x000049a1 testq        %rdx, %rdx
	0x0f, 0x85, 0x2a, 0xff, 0xff, 0xff, //0x000049a4 jne          LBB0_981
	0xe9, 0xa3, 0xff, 0xff, 0xff, //0x000049aa jmp          LBB0_992
	//0x000049af LBB0_996
	0x49, 0x83, 0xc6, 0xff, //0x000049af addq         $-1, %r14
	0x4d, 0x89, 0x75, 0x00, //0x000049b3 movq         %r14, (%r13)
	0x48, 0xc7, 0xc3, 0xde, 0xff, 0xff, 0xff, //0x000049b7 movq         $-34, %rbx
	0xe9, 0xbd, 0x01, 0x00, 0x00, //0x000049be jmp          LBB0_1025
	//0x000049c3 LBB0_997
	0x49, 0x89, 0x45, 0x00, //0x000049c3 movq         %rax, (%r13)
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000049c7 movq         $-1, %rbx
	0xe9, 0xad, 0x01, 0x00, 0x00, //0x000049ce jmp          LBB0_1025
	//0x000049d3 LBB0_998
	0xc5, 0xf5, 0x74, 0xc2, //0x000049d3 vpcmpeqb     %ymm2, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000049d7 vpmovmskb    %ymm0, %eax
	0xf7, 0xd0, //0x000049db notl         %eax
	0x0f, 0xbc, 0xc0, //0x000049dd bsfl         %eax, %eax
	0x48, 0x29, 0xf0, //0x000049e0 subq         %rsi, %rax
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000049e3 movq         $-1, %rbx
	0x48, 0x39, 0xd0, //0x000049ea cmpq         %rdx, %rax
	0x0f, 0x83, 0x8d, 0x01, 0x00, 0x00, //0x000049ed jae          LBB0_1025
	//0x000049f3 LBB0_999
	0x48, 0x8d, 0x50, 0x01, //0x000049f3 leaq         $1(%rax), %rdx
	0x49, 0x89, 0x55, 0x00, //0x000049f7 movq         %rdx, (%r13)
	0x41, 0x0f, 0xbe, 0x0c, 0x03, //0x000049fb movsbl       (%r11,%rax), %ecx
	0x83, 0xf9, 0x7b, //0x00004a00 cmpl         $123, %ecx
	0x0f, 0x87, 0xe7, 0x01, 0x00, 0x00, //0x00004a03 ja           LBB0_1030
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00004a09 movq         $-1, %rbx
	0x48, 0x8d, 0x35, 0xe9, 0x11, 0x00, 0x00, //0x00004a10 leaq         $4585(%rip), %rsi  /* LJTI0_6+0(%rip) */
	0x48, 0x63, 0x0c, 0x8e, //0x00004a17 movslq       (%rsi,%rcx,4), %rcx
	0x48, 0x01, 0xf1, //0x00004a1b addq         %rsi, %rcx
	0xff, 0xe1, //0x00004a1e jmpq         *%rcx
	//0x00004a20 LBB0_1001
	0x49, 0x8b, 0x4a, 0x08, //0x00004a20 movq         $8(%r10), %rcx
	0x48, 0x89, 0xce, //0x00004a24 movq         %rcx, %rsi
	0x48, 0x29, 0xd6, //0x00004a27 subq         %rdx, %rsi
	0x48, 0x83, 0xfe, 0x20, //0x00004a2a cmpq         $32, %rsi
	0x0f, 0x82, 0x95, 0x0a, 0x00, 0x00, //0x00004a2e jb           LBB0_1130
	0x48, 0x89, 0xc6, //0x00004a34 movq         %rax, %rsi
	0x48, 0xf7, 0xd6, //0x00004a37 notq         %rsi
	0xc5, 0xfe, 0x6f, 0x05, 0xde, 0xb5, 0xff, 0xff, //0x00004a3a vmovdqu      $-18978(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xf6, 0xb5, 0xff, 0xff, //0x00004a42 vmovdqu      $-18954(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0x0e, 0xb6, 0xff, 0xff, //0x00004a4a vmovdqu      $-18930(%rip), %ymm2  /* LCPI0_3+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00004a52 .p2align 4, 0x90
	//0x00004a60 LBB0_1003
	0xc4, 0xc1, 0x7e, 0x6f, 0x1c, 0x13, //0x00004a60 vmovdqu      (%r11,%rdx), %ymm3
	0xc5, 0xe5, 0x74, 0xe0, //0x00004a66 vpcmpeqb     %ymm0, %ymm3, %ymm4
	0xc5, 0xe5, 0xdb, 0xd9, //0x00004a6a vpand        %ymm1, %ymm3, %ymm3
	0xc5, 0xe5, 0x74, 0xda, //0x00004a6e vpcmpeqb     %ymm2, %ymm3, %ymm3
	0xc5, 0xe5, 0xeb, 0xdc, //0x00004a72 vpor         %ymm4, %ymm3, %ymm3
	0xc5, 0xfd, 0xd7, 0xfb, //0x00004a76 vpmovmskb    %ymm3, %edi
	0x85, 0xff, //0x00004a7a testl        %edi, %edi
	0x0f, 0x85, 0xca, 0x00, 0x00, 0x00, //0x00004a7c jne          LBB0_1017
	0x48, 0x83, 0xc2, 0x20, //0x00004a82 addq         $32, %rdx
	0x48, 0x8d, 0x3c, 0x31, //0x00004a86 leaq         (%rcx,%rsi), %rdi
	0x48, 0x83, 0xc7, 0xe0, //0x00004a8a addq         $-32, %rdi
	0x48, 0x83, 0xc6, 0xe0, //0x00004a8e addq         $-32, %rsi
	0x48, 0x83, 0xff, 0x1f, //0x00004a92 cmpq         $31, %rdi
	0x0f, 0x87, 0xc4, 0xff, 0xff, 0xff, //0x00004a96 ja           LBB0_1003
	0x4c, 0x89, 0xda, //0x00004a9c movq         %r11, %rdx
	0x48, 0x29, 0xf2, //0x00004a9f subq         %rsi, %rdx
	0x48, 0x01, 0xf1, //0x00004aa2 addq         %rsi, %rcx
	0x48, 0x89, 0xce, //0x00004aa5 movq         %rcx, %rsi
	0x48, 0x83, 0xfe, 0x10, //0x00004aa8 cmpq         $16, %rsi
	0x0f, 0x82, 0x54, 0x00, 0x00, 0x00, //0x00004aac jb           LBB0_1009
	//0x00004ab2 LBB0_1006
	0x4c, 0x89, 0xd9, //0x00004ab2 movq         %r11, %rcx
	0x48, 0x29, 0xd1, //0x00004ab5 subq         %rdx, %rcx
	0xc5, 0xfa, 0x6f, 0x05, 0x20, 0xb7, 0xff, 0xff, //0x00004ab8 vmovdqu      $-18656(%rip), %xmm0  /* LCPI0_4+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x0d, 0x28, 0xb7, 0xff, 0xff, //0x00004ac0 vmovdqu      $-18648(%rip), %xmm1  /* LCPI0_5+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x15, 0x30, 0xb7, 0xff, 0xff, //0x00004ac8 vmovdqu      $-18640(%rip), %xmm2  /* LCPI0_6+0(%rip) */
	//0x00004ad0 LBB0_1007
	0xc5, 0xfa, 0x6f, 0x1a, //0x00004ad0 vmovdqu      (%rdx), %xmm3
	0xc5, 0xe1, 0x74, 0xe0, //0x00004ad4 vpcmpeqb     %xmm0, %xmm3, %xmm4
	0xc5, 0xe1, 0xdb, 0xd9, //0x00004ad8 vpand        %xmm1, %xmm3, %xmm3
	0xc5, 0xe1, 0x74, 0xda, //0x00004adc vpcmpeqb     %xmm2, %xmm3, %xmm3
	0xc5, 0xe1, 0xeb, 0xdc, //0x00004ae0 vpor         %xmm4, %xmm3, %xmm3
	0xc5, 0xf9, 0xd7, 0xfb, //0x00004ae4 vpmovmskb    %xmm3, %edi
	0x85, 0xff, //0x00004ae8 testl        %edi, %edi
	0x0f, 0x85, 0x15, 0x08, 0x00, 0x00, //0x00004aea jne          LBB0_1104
	0x48, 0x83, 0xc2, 0x10, //0x00004af0 addq         $16, %rdx
	0x48, 0x83, 0xc6, 0xf0, //0x00004af4 addq         $-16, %rsi
	0x48, 0x83, 0xc1, 0xf0, //0x00004af8 addq         $-16, %rcx
	0x48, 0x83, 0xfe, 0x0f, //0x00004afc cmpq         $15, %rsi
	0x0f, 0x87, 0xca, 0xff, 0xff, 0xff, //0x00004b00 ja           LBB0_1007
	//0x00004b06 LBB0_1009
	0x48, 0x85, 0xf6, //0x00004b06 testq        %rsi, %rsi
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00004b09 je           LBB0_1016
	0x48, 0x8d, 0x3c, 0x32, //0x00004b0f leaq         (%rdx,%rsi), %rdi
	0x31, 0xc9, //0x00004b13 xorl         %ecx, %ecx
	//0x00004b15 LBB0_1011
	0x0f, 0xb6, 0x1c, 0x0a, //0x00004b15 movzbl       (%rdx,%rcx), %ebx
	0x80, 0xfb, 0x2c, //0x00004b19 cmpb         $44, %bl
	0x0f, 0x84, 0xb9, 0x09, 0x00, 0x00, //0x00004b1c je           LBB0_1131
	0x80, 0xfb, 0x7d, //0x00004b22 cmpb         $125, %bl
	0x0f, 0x84, 0xb0, 0x09, 0x00, 0x00, //0x00004b25 je           LBB0_1131
	0x80, 0xfb, 0x5d, //0x00004b2b cmpb         $93, %bl
	0x0f, 0x84, 0xa7, 0x09, 0x00, 0x00, //0x00004b2e je           LBB0_1131
	0x48, 0x83, 0xc1, 0x01, //0x00004b34 addq         $1, %rcx
	0x48, 0x39, 0xce, //0x00004b38 cmpq         %rcx, %rsi
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00004b3b jne          LBB0_1011
	0x48, 0x89, 0xfa, //0x00004b41 movq         %rdi, %rdx
	//0x00004b44 LBB0_1016
	0x4c, 0x29, 0xda, //0x00004b44 subq         %r11, %rdx
	0xe9, 0x95, 0x09, 0x00, 0x00, //0x00004b47 jmp          LBB0_1132
	//0x00004b4c LBB0_1017
	0x0f, 0xbc, 0xcf, //0x00004b4c bsfl         %edi, %ecx
	0x48, 0x29, 0xf1, //0x00004b4f subq         %rsi, %rcx
	//0x00004b52 LBB0_1018
	0x49, 0x89, 0x4d, 0x00, //0x00004b52 movq         %rcx, (%r13)
	0x48, 0x89, 0xc3, //0x00004b56 movq         %rax, %rbx
	0xe9, 0x22, 0x00, 0x00, 0x00, //0x00004b59 jmp          LBB0_1025
	//0x00004b5e LBB0_1019
	0x4d, 0x89, 0x75, 0x00, //0x00004b5e movq         %r14, (%r13)
	//0x00004b62 LBB0_1020
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00004b62 movq         $-1, %rbx
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00004b69 jmp          LBB0_1025
	//0x00004b6e LBB0_1021
	0x49, 0x89, 0xde, //0x00004b6e movq         %rbx, %r14
	//0x00004b71 LBB0_1023
	0x49, 0x83, 0xc6, 0xff, //0x00004b71 addq         $-1, %r14
	0x4d, 0x89, 0x75, 0x00, //0x00004b75 movq         %r14, (%r13)
	//0x00004b79 LBB0_1024
	0x48, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00004b79 movq         $-2, %rbx
	//0x00004b80 LBB0_1025
	0x48, 0x89, 0xd8, //0x00004b80 movq         %rbx, %rax
	0x48, 0x8d, 0x65, 0xd8, //0x00004b83 leaq         $-40(%rbp), %rsp
	0x5b, //0x00004b87 popq         %rbx
	0x41, 0x5c, //0x00004b88 popq         %r12
	0x41, 0x5d, //0x00004b8a popq         %r13
	0x41, 0x5e, //0x00004b8c popq         %r14
	0x41, 0x5f, //0x00004b8e popq         %r15
	0x5d, //0x00004b90 popq         %rbp
	0xc5, 0xf8, 0x77, //0x00004b91 vzeroupper   
	0xc3, //0x00004b94 retq         
	//0x00004b95 LBB0_1106
	0x48, 0xc7, 0xc3, 0xf9, 0xff, 0xff, 0xff, //0x00004b95 movq         $-7, %rbx
	0xe9, 0xdf, 0xff, 0xff, 0xff, //0x00004b9c jmp          LBB0_1025
	//0x00004ba1 LBB0_813
	0x49, 0x83, 0xfc, 0xff, //0x00004ba1 cmpq         $-1, %r12
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x00004ba5 jne          LBB0_815
	//0x00004bab LBB0_814
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00004bab movq         $-1, %r12
	0x4d, 0x89, 0xc8, //0x00004bb2 movq         %r9, %r8
	//0x00004bb5 LBB0_815
	0x4d, 0x89, 0x45, 0x00, //0x00004bb5 movq         %r8, (%r13)
	0x4c, 0x89, 0xe3, //0x00004bb9 movq         %r12, %rbx
	0xe9, 0xbf, 0xff, 0xff, 0xff, //0x00004bbc jmp          LBB0_1025
	//0x00004bc1 LBB0_1026
	0x48, 0x8d, 0x48, 0x04, //0x00004bc1 leaq         $4(%rax), %rcx
	0xe9, 0xca, 0x03, 0x00, 0x00, //0x00004bc5 jmp          LBB0_1069
	//0x00004bca LBB0_1027
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00004bca movq         $-1, %rcx
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00004bd1 jmp          LBB0_1029
	//0x00004bd6 LBB0_1028
	0x4c, 0x89, 0xe9, //0x00004bd6 movq         %r13, %rcx
	//0x00004bd9 LBB0_1029
	0x48, 0x8b, 0x54, 0x24, 0x10, //0x00004bd9 movq         $16(%rsp), %rdx
	0x48, 0x8b, 0x02, //0x00004bde movq         (%rdx), %rax
	0x48, 0x29, 0xc8, //0x00004be1 subq         %rcx, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00004be4 addq         $-2, %rax
	0x48, 0x89, 0x02, //0x00004be8 movq         %rax, (%rdx)
	0xe9, 0x89, 0xff, 0xff, 0xff, //0x00004beb jmp          LBB0_1024
	//0x00004bf0 LBB0_1030
	0x49, 0x89, 0x45, 0x00, //0x00004bf0 movq         %rax, (%r13)
	0xe9, 0x80, 0xff, 0xff, 0xff, //0x00004bf4 jmp          LBB0_1024
	//0x00004bf9 LBB0_1031
	0x4d, 0x8b, 0x42, 0x08, //0x00004bf9 movq         $8(%r10), %r8
	0x4d, 0x89, 0xc6, //0x00004bfd movq         %r8, %r14
	0x49, 0x29, 0xd6, //0x00004c00 subq         %rdx, %r14
	0x49, 0x83, 0xfe, 0x20, //0x00004c03 cmpq         $32, %r14
	0x0f, 0x8c, 0xe4, 0x08, 0x00, 0x00, //0x00004c07 jl           LBB0_1134
	0x4d, 0x8d, 0x0c, 0x03, //0x00004c0d leaq         (%r11,%rax), %r9
	0x49, 0x29, 0xc0, //0x00004c11 subq         %rax, %r8
	0xbe, 0x1f, 0x00, 0x00, 0x00, //0x00004c14 movl         $31, %esi
	0x45, 0x31, 0xf6, //0x00004c19 xorl         %r14d, %r14d
	0xc5, 0xfe, 0x6f, 0x05, 0x5c, 0xb4, 0xff, 0xff, //0x00004c1c vmovdqu      $-19364(%rip), %ymm0  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x74, 0xb4, 0xff, 0xff, //0x00004c24 vmovdqu      $-19340(%rip), %ymm1  /* LCPI0_8+0(%rip) */
	0x45, 0x31, 0xff, //0x00004c2c xorl         %r15d, %r15d
	//0x00004c2f LBB0_1033
	0xc4, 0x81, 0x7e, 0x6f, 0x54, 0x31, 0x01, //0x00004c2f vmovdqu      $1(%r9,%r14), %ymm2
	0xc5, 0xed, 0x74, 0xd8, //0x00004c36 vpcmpeqb     %ymm0, %ymm2, %ymm3
	0xc5, 0x7d, 0xd7, 0xd3, //0x00004c3a vpmovmskb    %ymm3, %r10d
	0xc5, 0xed, 0x74, 0xd1, //0x00004c3e vpcmpeqb     %ymm1, %ymm2, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00004c42 vpmovmskb    %ymm2, %ecx
	0x85, 0xc9, //0x00004c46 testl        %ecx, %ecx
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00004c48 jne          LBB0_1036
	0x4d, 0x85, 0xff, //0x00004c4e testq        %r15, %r15
	0x0f, 0x85, 0x08, 0x00, 0x00, 0x00, //0x00004c51 jne          LBB0_1036
	0x45, 0x31, 0xff, //0x00004c57 xorl         %r15d, %r15d
	0xe9, 0x34, 0x00, 0x00, 0x00, //0x00004c5a jmp          LBB0_1037
	//0x00004c5f LBB0_1036
	0x44, 0x89, 0xfa, //0x00004c5f movl         %r15d, %edx
	0xf7, 0xd2, //0x00004c62 notl         %edx
	0x21, 0xca, //0x00004c64 andl         %ecx, %edx
	0x44, 0x8d, 0x24, 0x12, //0x00004c66 leal         (%rdx,%rdx), %r12d
	0x45, 0x09, 0xfc, //0x00004c6a orl          %r15d, %r12d
	0x44, 0x89, 0xe7, //0x00004c6d movl         %r12d, %edi
	0xf7, 0xd7, //0x00004c70 notl         %edi
	0x21, 0xcf, //0x00004c72 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004c74 andl         $-1431655766, %edi
	0x45, 0x31, 0xff, //0x00004c7a xorl         %r15d, %r15d
	0x01, 0xd7, //0x00004c7d addl         %edx, %edi
	0x41, 0x0f, 0x92, 0xc7, //0x00004c7f setb         %r15b
	0x01, 0xff, //0x00004c83 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00004c85 xorl         $1431655765, %edi
	0x44, 0x21, 0xe7, //0x00004c8b andl         %r12d, %edi
	0xf7, 0xd7, //0x00004c8e notl         %edi
	0x41, 0x21, 0xfa, //0x00004c90 andl         %edi, %r10d
	//0x00004c93 LBB0_1037
	0x4d, 0x85, 0xd2, //0x00004c93 testq        %r10, %r10
	0x0f, 0x85, 0xeb, 0x05, 0x00, 0x00, //0x00004c96 jne          LBB0_1096
	0x49, 0x83, 0xc6, 0x20, //0x00004c9c addq         $32, %r14
	0x49, 0x8d, 0x0c, 0x30, //0x00004ca0 leaq         (%r8,%rsi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00004ca4 addq         $-32, %rcx
	0x48, 0x83, 0xc6, 0xe0, //0x00004ca8 addq         $-32, %rsi
	0x48, 0x83, 0xf9, 0x3f, //0x00004cac cmpq         $63, %rcx
	0x0f, 0x8f, 0x79, 0xff, 0xff, 0xff, //0x00004cb0 jg           LBB0_1033
	0x4d, 0x85, 0xff, //0x00004cb6 testq        %r15, %r15
	0x0f, 0x85, 0x5a, 0x08, 0x00, 0x00, //0x00004cb9 jne          LBB0_1136
	0x4b, 0x8d, 0x14, 0x0e, //0x00004cbf leaq         (%r14,%r9), %rdx
	0x48, 0x83, 0xc2, 0x01, //0x00004cc3 addq         $1, %rdx
	0x49, 0xf7, 0xd6, //0x00004cc7 notq         %r14
	0x4d, 0x01, 0xc6, //0x00004cca addq         %r8, %r14
	//0x00004ccd LBB0_1041
	0x4d, 0x85, 0xf6, //0x00004ccd testq        %r14, %r14
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00004cd0 movq         $16(%rsp), %rdi
	0x0f, 0x8e, 0xa5, 0xfe, 0xff, 0xff, //0x00004cd5 jle          LBB0_1025
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00004cdb movq         $-1, %rbx
	0xe9, 0xcb, 0x05, 0x00, 0x00, //0x00004ce2 jmp          LBB0_1098
	//0x00004ce7 LBB0_1043
	0x49, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00004ce7 movabsq      $6148914691236517205, %r9
	0x4d, 0x8b, 0x72, 0x08, //0x00004cf1 movq         $8(%r10), %r14
	0x49, 0x29, 0xd6, //0x00004cf5 subq         %rdx, %r14
	0x49, 0x01, 0xd3, //0x00004cf8 addq         %rdx, %r11
	0x45, 0x31, 0xd2, //0x00004cfb xorl         %r10d, %r10d
	0xc5, 0xfe, 0x6f, 0x05, 0x9a, 0xb3, 0xff, 0xff, //0x00004cfe vmovdqu      $-19558(%rip), %ymm0  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x72, 0xb3, 0xff, 0xff, //0x00004d06 vmovdqu      $-19598(%rip), %ymm1  /* LCPI0_7+0(%rip) */
	0xc5, 0xe9, 0x76, 0xd2, //0x00004d0e vpcmpeqd     %xmm2, %xmm2, %xmm2
	0xc5, 0xfe, 0x6f, 0x1d, 0xe6, 0xb3, 0xff, 0xff, //0x00004d12 vmovdqu      $-19482(%rip), %ymm3  /* LCPI0_11+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x3e, 0xb3, 0xff, 0xff, //0x00004d1a vmovdqu      $-19650(%rip), %ymm4  /* LCPI0_3+0(%rip) */
	0xc4, 0x41, 0x31, 0xef, 0xc9, //0x00004d22 vpxor        %xmm9, %xmm9, %xmm9
	0x45, 0x31, 0xe4, //0x00004d27 xorl         %r12d, %r12d
	0x45, 0x31, 0xff, //0x00004d2a xorl         %r15d, %r15d
	0x31, 0xd2, //0x00004d2d xorl         %edx, %edx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00004d2f jmp          LBB0_1045
	//0x00004d34 LBB0_1044
	0x49, 0xc1, 0xfd, 0x3f, //0x00004d34 sarq         $63, %r13
	0xf3, 0x49, 0x0f, 0xb8, 0xc8, //0x00004d38 popcntq      %r8, %rcx
	0x49, 0x01, 0xcf, //0x00004d3d addq         %rcx, %r15
	0x49, 0x83, 0xc3, 0x40, //0x00004d40 addq         $64, %r11
	0x49, 0x83, 0xc6, 0xc0, //0x00004d44 addq         $-64, %r14
	0x4d, 0x89, 0xea, //0x00004d48 movq         %r13, %r10
	//0x00004d4b LBB0_1045
	0x49, 0x83, 0xfe, 0x40, //0x00004d4b cmpq         $64, %r14
	0x0f, 0x8c, 0x1c, 0x01, 0x00, 0x00, //0x00004d4f jl           LBB0_1053
	//0x00004d55 LBB0_1046
	0xc4, 0xc1, 0x7e, 0x6f, 0x3b, //0x00004d55 vmovdqu      (%r11), %ymm7
	0xc4, 0xc1, 0x7e, 0x6f, 0x73, 0x20, //0x00004d5a vmovdqu      $32(%r11), %ymm6
	0xc5, 0x45, 0x74, 0xc0, //0x00004d60 vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf0, //0x00004d64 vpmovmskb    %ymm8, %esi
	0xc5, 0x4d, 0x74, 0xc0, //0x00004d69 vpcmpeqb     %ymm0, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf8, //0x00004d6d vpmovmskb    %ymm8, %edi
	0x48, 0xc1, 0xe7, 0x20, //0x00004d72 shlq         $32, %rdi
	0x48, 0x09, 0xfe, //0x00004d76 orq          %rdi, %rsi
	0x48, 0x89, 0xf7, //0x00004d79 movq         %rsi, %rdi
	0x4c, 0x09, 0xe7, //0x00004d7c orq          %r12, %rdi
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00004d7f jne          LBB0_1048
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00004d85 movq         $-1, %rsi
	0x45, 0x31, 0xe4, //0x00004d8c xorl         %r12d, %r12d
	0xe9, 0x3c, 0x00, 0x00, 0x00, //0x00004d8f jmp          LBB0_1049
	//0x00004d94 LBB0_1048
	0x4c, 0x89, 0xe7, //0x00004d94 movq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x00004d97 notq         %rdi
	0x48, 0x21, 0xf7, //0x00004d9a andq         %rsi, %rdi
	0x4c, 0x8d, 0x2c, 0x3f, //0x00004d9d leaq         (%rdi,%rdi), %r13
	0x4d, 0x09, 0xe5, //0x00004da1 orq          %r12, %r13
	0x4d, 0x89, 0xe8, //0x00004da4 movq         %r13, %r8
	0x49, 0xf7, 0xd0, //0x00004da7 notq         %r8
	0x48, 0xb9, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00004daa movabsq      $-6148914691236517206, %rcx
	0x48, 0x21, 0xce, //0x00004db4 andq         %rcx, %rsi
	0x4c, 0x21, 0xc6, //0x00004db7 andq         %r8, %rsi
	0x45, 0x31, 0xe4, //0x00004dba xorl         %r12d, %r12d
	0x48, 0x01, 0xfe, //0x00004dbd addq         %rdi, %rsi
	0x41, 0x0f, 0x92, 0xc4, //0x00004dc0 setb         %r12b
	0x48, 0x01, 0xf6, //0x00004dc4 addq         %rsi, %rsi
	0x4c, 0x31, 0xce, //0x00004dc7 xorq         %r9, %rsi
	0x4c, 0x21, 0xee, //0x00004dca andq         %r13, %rsi
	0x48, 0xf7, 0xd6, //0x00004dcd notq         %rsi
	//0x00004dd0 LBB0_1049
	0xc5, 0x4d, 0x74, 0xc1, //0x00004dd0 vpcmpeqb     %ymm1, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xc8, //0x00004dd4 vpmovmskb    %ymm8, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00004dd9 shlq         $32, %rcx
	0xc5, 0x45, 0x74, 0xc1, //0x00004ddd vpcmpeqb     %ymm1, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf8, //0x00004de1 vpmovmskb    %ymm8, %edi
	0x48, 0x09, 0xcf, //0x00004de6 orq          %rcx, %rdi
	0x48, 0x21, 0xf7, //0x00004de9 andq         %rsi, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xef, //0x00004dec vmovq        %rdi, %xmm5
	0xc4, 0xe3, 0x51, 0x44, 0xea, 0x00, //0x00004df1 vpclmulqdq   $0, %xmm2, %xmm5, %xmm5
	0xc4, 0xc1, 0xf9, 0x7e, 0xed, //0x00004df7 vmovq        %xmm5, %r13
	0x4d, 0x31, 0xd5, //0x00004dfc xorq         %r10, %r13
	0xc5, 0xc5, 0x74, 0xeb, //0x00004dff vpcmpeqb     %ymm3, %ymm7, %ymm5
	0xc5, 0x7d, 0xd7, 0xc5, //0x00004e03 vpmovmskb    %ymm5, %r8d
	0xc5, 0xcd, 0x74, 0xeb, //0x00004e07 vpcmpeqb     %ymm3, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xcd, //0x00004e0b vpmovmskb    %ymm5, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00004e0f shlq         $32, %rcx
	0x49, 0x09, 0xc8, //0x00004e13 orq          %rcx, %r8
	0x4d, 0x89, 0xea, //0x00004e16 movq         %r13, %r10
	0x49, 0xf7, 0xd2, //0x00004e19 notq         %r10
	0x4d, 0x21, 0xd0, //0x00004e1c andq         %r10, %r8
	0xc5, 0xc5, 0x74, 0xec, //0x00004e1f vpcmpeqb     %ymm4, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xcd, //0x00004e23 vpmovmskb    %ymm5, %ecx
	0xc5, 0xcd, 0x74, 0xec, //0x00004e27 vpcmpeqb     %ymm4, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xf5, //0x00004e2b vpmovmskb    %ymm5, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x00004e2f shlq         $32, %rsi
	0x48, 0x09, 0xf1, //0x00004e33 orq          %rsi, %rcx
	0x4c, 0x21, 0xd1, //0x00004e36 andq         %r10, %rcx
	0x0f, 0x84, 0xf5, 0xfe, 0xff, 0xff, //0x00004e39 je           LBB0_1044
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x00004e3f movq         $24(%rsp), %r10
	//0x00004e44 LBB0_1051
	0x48, 0x8d, 0x79, 0xff, //0x00004e44 leaq         $-1(%rcx), %rdi
	0x48, 0x89, 0xfe, //0x00004e48 movq         %rdi, %rsi
	0x4c, 0x21, 0xc6, //0x00004e4b andq         %r8, %rsi
	0xf3, 0x48, 0x0f, 0xb8, 0xf6, //0x00004e4e popcntq      %rsi, %rsi
	0x4c, 0x01, 0xfe, //0x00004e53 addq         %r15, %rsi
	0x48, 0x39, 0xd6, //0x00004e56 cmpq         %rdx, %rsi
	0x0f, 0x86, 0xed, 0x03, 0x00, 0x00, //0x00004e59 jbe          LBB0_1095
	0x48, 0x83, 0xc2, 0x01, //0x00004e5f addq         $1, %rdx
	0x48, 0x21, 0xf9, //0x00004e63 andq         %rdi, %rcx
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00004e66 jne          LBB0_1051
	0xe9, 0xc3, 0xfe, 0xff, 0xff, //0x00004e6c jmp          LBB0_1044
	//0x00004e71 LBB0_1053
	0x4d, 0x85, 0xf6, //0x00004e71 testq        %r14, %r14
	0x0f, 0x8e, 0x89, 0x06, 0x00, 0x00, //0x00004e74 jle          LBB0_1135
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x60, //0x00004e7a vmovdqu      %ymm9, $96(%rsp)
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x40, //0x00004e80 vmovdqu      %ymm9, $64(%rsp)
	0x44, 0x89, 0xd9, //0x00004e86 movl         %r11d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00004e89 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x00004e8f cmpl         $4033, %ecx
	0x0f, 0x82, 0xba, 0xfe, 0xff, 0xff, //0x00004e95 jb           LBB0_1046
	0x49, 0x83, 0xfe, 0x20, //0x00004e9b cmpq         $32, %r14
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x00004e9f jb           LBB0_1057
	0xc4, 0xc1, 0x7e, 0x6f, 0x2b, //0x00004ea5 vmovdqu      (%r11), %ymm5
	0xc5, 0xfe, 0x7f, 0x6c, 0x24, 0x40, //0x00004eaa vmovdqu      %ymm5, $64(%rsp)
	0x49, 0x83, 0xc3, 0x20, //0x00004eb0 addq         $32, %r11
	0x49, 0x8d, 0x7e, 0xe0, //0x00004eb4 leaq         $-32(%r14), %rdi
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x00004eb8 leaq         $96(%rsp), %rsi
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00004ebd jmp          LBB0_1058
	//0x00004ec2 LBB0_1057
	0x48, 0x8d, 0x74, 0x24, 0x40, //0x00004ec2 leaq         $64(%rsp), %rsi
	0x4c, 0x89, 0xf7, //0x00004ec7 movq         %r14, %rdi
	//0x00004eca LBB0_1058
	0x48, 0x83, 0xff, 0x10, //0x00004eca cmpq         $16, %rdi
	0x0f, 0x82, 0x5a, 0x00, 0x00, 0x00, //0x00004ece jb           LBB0_1059
	0xc4, 0xc1, 0x7a, 0x6f, 0x2b, //0x00004ed4 vmovdqu      (%r11), %xmm5
	0xc5, 0xfa, 0x7f, 0x2e, //0x00004ed9 vmovdqu      %xmm5, (%rsi)
	0x49, 0x83, 0xc3, 0x10, //0x00004edd addq         $16, %r11
	0x48, 0x83, 0xc6, 0x10, //0x00004ee1 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00004ee5 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00004ee9 cmpq         $8, %rdi
	0x0f, 0x83, 0x45, 0x00, 0x00, 0x00, //0x00004eed jae          LBB0_1064
	//0x00004ef3 LBB0_1060
	0x48, 0x83, 0xff, 0x04, //0x00004ef3 cmpq         $4, %rdi
	0x0f, 0x82, 0x57, 0x00, 0x00, 0x00, //0x00004ef7 jb           LBB0_1061
	//0x00004efd LBB0_1065
	0x41, 0x8b, 0x0b, //0x00004efd movl         (%r11), %ecx
	0x89, 0x0e, //0x00004f00 movl         %ecx, (%rsi)
	0x49, 0x83, 0xc3, 0x04, //0x00004f02 addq         $4, %r11
	0x48, 0x83, 0xc6, 0x04, //0x00004f06 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00004f0a addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00004f0e cmpq         $2, %rdi
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x00004f12 jae          LBB0_1066
	//0x00004f18 LBB0_1062
	0x4c, 0x89, 0xd9, //0x00004f18 movq         %r11, %rcx
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00004f1b leaq         $64(%rsp), %r11
	0x48, 0x85, 0xff, //0x00004f20 testq        %rdi, %rdi
	0x0f, 0x85, 0x59, 0x00, 0x00, 0x00, //0x00004f23 jne          LBB0_1067
	0xe9, 0x27, 0xfe, 0xff, 0xff, //0x00004f29 jmp          LBB0_1046
	//0x00004f2e LBB0_1059
	0x48, 0x83, 0xff, 0x08, //0x00004f2e cmpq         $8, %rdi
	0x0f, 0x82, 0xbb, 0xff, 0xff, 0xff, //0x00004f32 jb           LBB0_1060
	//0x00004f38 LBB0_1064
	0x49, 0x8b, 0x0b, //0x00004f38 movq         (%r11), %rcx
	0x48, 0x89, 0x0e, //0x00004f3b movq         %rcx, (%rsi)
	0x49, 0x83, 0xc3, 0x08, //0x00004f3e addq         $8, %r11
	0x48, 0x83, 0xc6, 0x08, //0x00004f42 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00004f46 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00004f4a cmpq         $4, %rdi
	0x0f, 0x83, 0xa9, 0xff, 0xff, 0xff, //0x00004f4e jae          LBB0_1065
	//0x00004f54 LBB0_1061
	0x48, 0x83, 0xff, 0x02, //0x00004f54 cmpq         $2, %rdi
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00004f58 jb           LBB0_1062
	//0x00004f5e LBB0_1066
	0x41, 0x0f, 0xb7, 0x0b, //0x00004f5e movzwl       (%r11), %ecx
	0x66, 0x89, 0x0e, //0x00004f62 movw         %cx, (%rsi)
	0x49, 0x83, 0xc3, 0x02, //0x00004f65 addq         $2, %r11
	0x48, 0x83, 0xc6, 0x02, //0x00004f69 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00004f6d addq         $-2, %rdi
	0x4c, 0x89, 0xd9, //0x00004f71 movq         %r11, %rcx
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00004f74 leaq         $64(%rsp), %r11
	0x48, 0x85, 0xff, //0x00004f79 testq        %rdi, %rdi
	0x0f, 0x84, 0xd3, 0xfd, 0xff, 0xff, //0x00004f7c je           LBB0_1046
	//0x00004f82 LBB0_1067
	0x8a, 0x09, //0x00004f82 movb         (%rcx), %cl
	0x88, 0x0e, //0x00004f84 movb         %cl, (%rsi)
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00004f86 leaq         $64(%rsp), %r11
	0xe9, 0xc5, 0xfd, 0xff, 0xff, //0x00004f8b jmp          LBB0_1046
	//0x00004f90 LBB0_1068
	0x48, 0x8d, 0x48, 0x05, //0x00004f90 leaq         $5(%rax), %rcx
	//0x00004f94 LBB0_1069
	0x49, 0x3b, 0x4a, 0x08, //0x00004f94 cmpq         $8(%r10), %rcx
	0x0f, 0x86, 0xb4, 0xfb, 0xff, 0xff, //0x00004f98 jbe          LBB0_1018
	0xe9, 0xdd, 0xfb, 0xff, 0xff, //0x00004f9e jmp          LBB0_1025
	//0x00004fa3 LBB0_1070
	0x49, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00004fa3 movabsq      $6148914691236517205, %r9
	0x4d, 0x8b, 0x72, 0x08, //0x00004fad movq         $8(%r10), %r14
	0x49, 0x29, 0xd6, //0x00004fb1 subq         %rdx, %r14
	0x49, 0x01, 0xd3, //0x00004fb4 addq         %rdx, %r11
	0x45, 0x31, 0xd2, //0x00004fb7 xorl         %r10d, %r10d
	0xc5, 0xfe, 0x6f, 0x05, 0xde, 0xb0, 0xff, 0xff, //0x00004fba vmovdqu      $-20258(%rip), %ymm0  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0xb6, 0xb0, 0xff, 0xff, //0x00004fc2 vmovdqu      $-20298(%rip), %ymm1  /* LCPI0_7+0(%rip) */
	0xc5, 0xe9, 0x76, 0xd2, //0x00004fca vpcmpeqd     %xmm2, %xmm2, %xmm2
	0xc5, 0xfe, 0x6f, 0x1d, 0xea, 0xb0, 0xff, 0xff, //0x00004fce vmovdqu      $-20246(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x02, 0xb1, 0xff, 0xff, //0x00004fd6 vmovdqu      $-20222(%rip), %ymm4  /* LCPI0_10+0(%rip) */
	0xc4, 0x41, 0x31, 0xef, 0xc9, //0x00004fde vpxor        %xmm9, %xmm9, %xmm9
	0x45, 0x31, 0xe4, //0x00004fe3 xorl         %r12d, %r12d
	0x45, 0x31, 0xff, //0x00004fe6 xorl         %r15d, %r15d
	0x31, 0xd2, //0x00004fe9 xorl         %edx, %edx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00004feb jmp          LBB0_1072
	//0x00004ff0 LBB0_1071
	0x49, 0xc1, 0xfd, 0x3f, //0x00004ff0 sarq         $63, %r13
	0xf3, 0x49, 0x0f, 0xb8, 0xc8, //0x00004ff4 popcntq      %r8, %rcx
	0x49, 0x01, 0xcf, //0x00004ff9 addq         %rcx, %r15
	0x49, 0x83, 0xc3, 0x40, //0x00004ffc addq         $64, %r11
	0x49, 0x83, 0xc6, 0xc0, //0x00005000 addq         $-64, %r14
	0x4d, 0x89, 0xea, //0x00005004 movq         %r13, %r10
	//0x00005007 LBB0_1072
	0x49, 0x83, 0xfe, 0x40, //0x00005007 cmpq         $64, %r14
	0x0f, 0x8c, 0x1c, 0x01, 0x00, 0x00, //0x0000500b jl           LBB0_1080
	//0x00005011 LBB0_1073
	0xc4, 0xc1, 0x7e, 0x6f, 0x3b, //0x00005011 vmovdqu      (%r11), %ymm7
	0xc4, 0xc1, 0x7e, 0x6f, 0x73, 0x20, //0x00005016 vmovdqu      $32(%r11), %ymm6
	0xc5, 0x45, 0x74, 0xc0, //0x0000501c vpcmpeqb     %ymm0, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf0, //0x00005020 vpmovmskb    %ymm8, %esi
	0xc5, 0x4d, 0x74, 0xc0, //0x00005025 vpcmpeqb     %ymm0, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf8, //0x00005029 vpmovmskb    %ymm8, %edi
	0x48, 0xc1, 0xe7, 0x20, //0x0000502e shlq         $32, %rdi
	0x48, 0x09, 0xfe, //0x00005032 orq          %rdi, %rsi
	0x48, 0x89, 0xf7, //0x00005035 movq         %rsi, %rdi
	0x4c, 0x09, 0xe7, //0x00005038 orq          %r12, %rdi
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x0000503b jne          LBB0_1075
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00005041 movq         $-1, %rsi
	0x45, 0x31, 0xe4, //0x00005048 xorl         %r12d, %r12d
	0xe9, 0x3c, 0x00, 0x00, 0x00, //0x0000504b jmp          LBB0_1076
	//0x00005050 LBB0_1075
	0x4c, 0x89, 0xe7, //0x00005050 movq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x00005053 notq         %rdi
	0x48, 0x21, 0xf7, //0x00005056 andq         %rsi, %rdi
	0x4c, 0x8d, 0x2c, 0x3f, //0x00005059 leaq         (%rdi,%rdi), %r13
	0x4d, 0x09, 0xe5, //0x0000505d orq          %r12, %r13
	0x4d, 0x89, 0xe8, //0x00005060 movq         %r13, %r8
	0x49, 0xf7, 0xd0, //0x00005063 notq         %r8
	0x48, 0xb9, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00005066 movabsq      $-6148914691236517206, %rcx
	0x48, 0x21, 0xce, //0x00005070 andq         %rcx, %rsi
	0x4c, 0x21, 0xc6, //0x00005073 andq         %r8, %rsi
	0x45, 0x31, 0xe4, //0x00005076 xorl         %r12d, %r12d
	0x48, 0x01, 0xfe, //0x00005079 addq         %rdi, %rsi
	0x41, 0x0f, 0x92, 0xc4, //0x0000507c setb         %r12b
	0x48, 0x01, 0xf6, //0x00005080 addq         %rsi, %rsi
	0x4c, 0x31, 0xce, //0x00005083 xorq         %r9, %rsi
	0x4c, 0x21, 0xee, //0x00005086 andq         %r13, %rsi
	0x48, 0xf7, 0xd6, //0x00005089 notq         %rsi
	//0x0000508c LBB0_1076
	0xc5, 0x4d, 0x74, 0xc1, //0x0000508c vpcmpeqb     %ymm1, %ymm6, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xc8, //0x00005090 vpmovmskb    %ymm8, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00005095 shlq         $32, %rcx
	0xc5, 0x45, 0x74, 0xc1, //0x00005099 vpcmpeqb     %ymm1, %ymm7, %ymm8
	0xc4, 0xc1, 0x7d, 0xd7, 0xf8, //0x0000509d vpmovmskb    %ymm8, %edi
	0x48, 0x09, 0xcf, //0x000050a2 orq          %rcx, %rdi
	0x48, 0x21, 0xf7, //0x000050a5 andq         %rsi, %rdi
	0xc4, 0xe1, 0xf9, 0x6e, 0xef, //0x000050a8 vmovq        %rdi, %xmm5
	0xc4, 0xe3, 0x51, 0x44, 0xea, 0x00, //0x000050ad vpclmulqdq   $0, %xmm2, %xmm5, %xmm5
	0xc4, 0xc1, 0xf9, 0x7e, 0xed, //0x000050b3 vmovq        %xmm5, %r13
	0x4d, 0x31, 0xd5, //0x000050b8 xorq         %r10, %r13
	0xc5, 0xc5, 0x74, 0xeb, //0x000050bb vpcmpeqb     %ymm3, %ymm7, %ymm5
	0xc5, 0x7d, 0xd7, 0xc5, //0x000050bf vpmovmskb    %ymm5, %r8d
	0xc5, 0xcd, 0x74, 0xeb, //0x000050c3 vpcmpeqb     %ymm3, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xcd, //0x000050c7 vpmovmskb    %ymm5, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x000050cb shlq         $32, %rcx
	0x49, 0x09, 0xc8, //0x000050cf orq          %rcx, %r8
	0x4d, 0x89, 0xea, //0x000050d2 movq         %r13, %r10
	0x49, 0xf7, 0xd2, //0x000050d5 notq         %r10
	0x4d, 0x21, 0xd0, //0x000050d8 andq         %r10, %r8
	0xc5, 0xc5, 0x74, 0xec, //0x000050db vpcmpeqb     %ymm4, %ymm7, %ymm5
	0xc5, 0xfd, 0xd7, 0xcd, //0x000050df vpmovmskb    %ymm5, %ecx
	0xc5, 0xcd, 0x74, 0xec, //0x000050e3 vpcmpeqb     %ymm4, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xf5, //0x000050e7 vpmovmskb    %ymm5, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x000050eb shlq         $32, %rsi
	0x48, 0x09, 0xf1, //0x000050ef orq          %rsi, %rcx
	0x4c, 0x21, 0xd1, //0x000050f2 andq         %r10, %rcx
	0x0f, 0x84, 0xf5, 0xfe, 0xff, 0xff, //0x000050f5 je           LBB0_1071
	0x4c, 0x8b, 0x54, 0x24, 0x18, //0x000050fb movq         $24(%rsp), %r10
	//0x00005100 LBB0_1078
	0x48, 0x8d, 0x79, 0xff, //0x00005100 leaq         $-1(%rcx), %rdi
	0x48, 0x89, 0xfe, //0x00005104 movq         %rdi, %rsi
	0x4c, 0x21, 0xc6, //0x00005107 andq         %r8, %rsi
	0xf3, 0x48, 0x0f, 0xb8, 0xf6, //0x0000510a popcntq      %rsi, %rsi
	0x4c, 0x01, 0xfe, //0x0000510f addq         %r15, %rsi
	0x48, 0x39, 0xd6, //0x00005112 cmpq         %rdx, %rsi
	0x0f, 0x86, 0x31, 0x01, 0x00, 0x00, //0x00005115 jbe          LBB0_1095
	0x48, 0x83, 0xc2, 0x01, //0x0000511b addq         $1, %rdx
	0x48, 0x21, 0xf9, //0x0000511f andq         %rdi, %rcx
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00005122 jne          LBB0_1078
	0xe9, 0xc3, 0xfe, 0xff, 0xff, //0x00005128 jmp          LBB0_1071
	//0x0000512d LBB0_1080
	0x4d, 0x85, 0xf6, //0x0000512d testq        %r14, %r14
	0x0f, 0x8e, 0xcd, 0x03, 0x00, 0x00, //0x00005130 jle          LBB0_1135
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x60, //0x00005136 vmovdqu      %ymm9, $96(%rsp)
	0xc5, 0x7e, 0x7f, 0x4c, 0x24, 0x40, //0x0000513c vmovdqu      %ymm9, $64(%rsp)
	0x44, 0x89, 0xd9, //0x00005142 movl         %r11d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00005145 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x0000514b cmpl         $4033, %ecx
	0x0f, 0x82, 0xba, 0xfe, 0xff, 0xff, //0x00005151 jb           LBB0_1073
	0x49, 0x83, 0xfe, 0x20, //0x00005157 cmpq         $32, %r14
	0x0f, 0x82, 0x1d, 0x00, 0x00, 0x00, //0x0000515b jb           LBB0_1084
	0xc4, 0xc1, 0x7e, 0x6f, 0x2b, //0x00005161 vmovdqu      (%r11), %ymm5
	0xc5, 0xfe, 0x7f, 0x6c, 0x24, 0x40, //0x00005166 vmovdqu      %ymm5, $64(%rsp)
	0x49, 0x83, 0xc3, 0x20, //0x0000516c addq         $32, %r11
	0x49, 0x8d, 0x7e, 0xe0, //0x00005170 leaq         $-32(%r14), %rdi
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x00005174 leaq         $96(%rsp), %rsi
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00005179 jmp          LBB0_1085
	//0x0000517e LBB0_1084
	0x48, 0x8d, 0x74, 0x24, 0x40, //0x0000517e leaq         $64(%rsp), %rsi
	0x4c, 0x89, 0xf7, //0x00005183 movq         %r14, %rdi
	//0x00005186 LBB0_1085
	0x48, 0x83, 0xff, 0x10, //0x00005186 cmpq         $16, %rdi
	0x0f, 0x82, 0x5a, 0x00, 0x00, 0x00, //0x0000518a jb           LBB0_1086
	0xc4, 0xc1, 0x7a, 0x6f, 0x2b, //0x00005190 vmovdqu      (%r11), %xmm5
	0xc5, 0xfa, 0x7f, 0x2e, //0x00005195 vmovdqu      %xmm5, (%rsi)
	0x49, 0x83, 0xc3, 0x10, //0x00005199 addq         $16, %r11
	0x48, 0x83, 0xc6, 0x10, //0x0000519d addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x000051a1 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x000051a5 cmpq         $8, %rdi
	0x0f, 0x83, 0x45, 0x00, 0x00, 0x00, //0x000051a9 jae          LBB0_1091
	//0x000051af LBB0_1087
	0x48, 0x83, 0xff, 0x04, //0x000051af cmpq         $4, %rdi
	0x0f, 0x82, 0x57, 0x00, 0x00, 0x00, //0x000051b3 jb           LBB0_1088
	//0x000051b9 LBB0_1092
	0x41, 0x8b, 0x0b, //0x000051b9 movl         (%r11), %ecx
	0x89, 0x0e, //0x000051bc movl         %ecx, (%rsi)
	0x49, 0x83, 0xc3, 0x04, //0x000051be addq         $4, %r11
	0x48, 0x83, 0xc6, 0x04, //0x000051c2 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x000051c6 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x000051ca cmpq         $2, %rdi
	0x0f, 0x83, 0x46, 0x00, 0x00, 0x00, //0x000051ce jae          LBB0_1093
	//0x000051d4 LBB0_1089
	0x4c, 0x89, 0xd9, //0x000051d4 movq         %r11, %rcx
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x000051d7 leaq         $64(%rsp), %r11
	0x48, 0x85, 0xff, //0x000051dc testq        %rdi, %rdi
	0x0f, 0x85, 0x59, 0x00, 0x00, 0x00, //0x000051df jne          LBB0_1094
	0xe9, 0x27, 0xfe, 0xff, 0xff, //0x000051e5 jmp          LBB0_1073
	//0x000051ea LBB0_1086
	0x48, 0x83, 0xff, 0x08, //0x000051ea cmpq         $8, %rdi
	0x0f, 0x82, 0xbb, 0xff, 0xff, 0xff, //0x000051ee jb           LBB0_1087
	//0x000051f4 LBB0_1091
	0x49, 0x8b, 0x0b, //0x000051f4 movq         (%r11), %rcx
	0x48, 0x89, 0x0e, //0x000051f7 movq         %rcx, (%rsi)
	0x49, 0x83, 0xc3, 0x08, //0x000051fa addq         $8, %r11
	0x48, 0x83, 0xc6, 0x08, //0x000051fe addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00005202 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00005206 cmpq         $4, %rdi
	0x0f, 0x83, 0xa9, 0xff, 0xff, 0xff, //0x0000520a jae          LBB0_1092
	//0x00005210 LBB0_1088
	0x48, 0x83, 0xff, 0x02, //0x00005210 cmpq         $2, %rdi
	0x0f, 0x82, 0xba, 0xff, 0xff, 0xff, //0x00005214 jb           LBB0_1089
	//0x0000521a LBB0_1093
	0x41, 0x0f, 0xb7, 0x0b, //0x0000521a movzwl       (%r11), %ecx
	0x66, 0x89, 0x0e, //0x0000521e movw         %cx, (%rsi)
	0x49, 0x83, 0xc3, 0x02, //0x00005221 addq         $2, %r11
	0x48, 0x83, 0xc6, 0x02, //0x00005225 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00005229 addq         $-2, %rdi
	0x4c, 0x89, 0xd9, //0x0000522d movq         %r11, %rcx
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00005230 leaq         $64(%rsp), %r11
	0x48, 0x85, 0xff, //0x00005235 testq        %rdi, %rdi
	0x0f, 0x84, 0xd3, 0xfd, 0xff, 0xff, //0x00005238 je           LBB0_1073
	//0x0000523e LBB0_1094
	0x8a, 0x09, //0x0000523e movb         (%rcx), %cl
	0x88, 0x0e, //0x00005240 movb         %cl, (%rsi)
	0x4c, 0x8d, 0x5c, 0x24, 0x40, //0x00005242 leaq         $64(%rsp), %r11
	0xe9, 0xc5, 0xfd, 0xff, 0xff, //0x00005247 jmp          LBB0_1073
	//0x0000524c LBB0_1095
	0x49, 0x8b, 0x52, 0x08, //0x0000524c movq         $8(%r10), %rdx
	0x48, 0x0f, 0xbc, 0xc9, //0x00005250 bsfq         %rcx, %rcx
	0x4c, 0x29, 0xf1, //0x00005254 subq         %r14, %rcx
	0x48, 0x01, 0xd1, //0x00005257 addq         %rdx, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x0000525a addq         $1, %rcx
	0x48, 0x8b, 0x74, 0x24, 0x10, //0x0000525e movq         $16(%rsp), %rsi
	0x48, 0x89, 0x0e, //0x00005263 movq         %rcx, (%rsi)
	0x49, 0x8b, 0x52, 0x08, //0x00005266 movq         $8(%r10), %rdx
	0x48, 0x39, 0xd1, //0x0000526a cmpq         %rdx, %rcx
	0x48, 0x0f, 0x47, 0xca, //0x0000526d cmovaq       %rdx, %rcx
	0x48, 0x89, 0x0e, //0x00005271 movq         %rcx, (%rsi)
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00005274 movq         $-1, %rcx
	0x48, 0x0f, 0x47, 0xc1, //0x0000527b cmovaq       %rcx, %rax
	0x48, 0x89, 0xc3, //0x0000527f movq         %rax, %rbx
	0xe9, 0xf9, 0xf8, 0xff, 0xff, //0x00005282 jmp          LBB0_1025
	//0x00005287 LBB0_1096
	0x41, 0x0f, 0xbc, 0xca, //0x00005287 bsfl         %r10d, %ecx
	0x48, 0x01, 0xc1, //0x0000528b addq         %rax, %rcx
	0x4c, 0x01, 0xf1, //0x0000528e addq         %r14, %rcx
	0x48, 0x83, 0xc1, 0x02, //0x00005291 addq         $2, %rcx
	0xe9, 0xb8, 0xf8, 0xff, 0xff, //0x00005295 jmp          LBB0_1018
	//0x0000529a LBB0_1097
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x0000529a movq         $-2, %rcx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x000052a1 movl         $2, %esi
	0x48, 0x01, 0xf2, //0x000052a6 addq         %rsi, %rdx
	0x49, 0x01, 0xce, //0x000052a9 addq         %rcx, %r14
	0x0f, 0x8e, 0xce, 0xf8, 0xff, 0xff, //0x000052ac jle          LBB0_1025
	//0x000052b2 LBB0_1098
	0x0f, 0xb6, 0x0a, //0x000052b2 movzbl       (%rdx), %ecx
	0x80, 0xf9, 0x5c, //0x000052b5 cmpb         $92, %cl
	0x0f, 0x84, 0xdc, 0xff, 0xff, 0xff, //0x000052b8 je           LBB0_1097
	0x80, 0xf9, 0x22, //0x000052be cmpb         $34, %cl
	0x0f, 0x84, 0xb8, 0x01, 0x00, 0x00, //0x000052c1 je           LBB0_1124
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000052c7 movq         $-1, %rcx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x000052ce movl         $1, %esi
	0x48, 0x01, 0xf2, //0x000052d3 addq         %rsi, %rdx
	0x49, 0x01, 0xce, //0x000052d6 addq         %rcx, %r14
	0x0f, 0x8f, 0xd3, 0xff, 0xff, 0xff, //0x000052d9 jg           LBB0_1098
	0xe9, 0x9c, 0xf8, 0xff, 0xff, //0x000052df jmp          LBB0_1025
	//0x000052e4 LBB0_1108
	0x49, 0x89, 0x4d, 0x00, //0x000052e4 movq         %rcx, (%r13)
	0xe9, 0x93, 0xf8, 0xff, 0xff, //0x000052e8 jmp          LBB0_1025
	//0x000052ed LBB0_1101
	0x4d, 0x89, 0xf0, //0x000052ed movq         %r14, %r8
	//0x000052f0 LBB0_1102
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x000052f0 movq         $16(%rsp), %r13
	//0x000052f5 LBB0_1103
	0x4d, 0x89, 0x45, 0x00, //0x000052f5 movq         %r8, (%r13)
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000052f9 movq         $-1, %rbx
	0xe9, 0x7b, 0xf8, 0xff, 0xff, //0x00005300 jmp          LBB0_1025
	//0x00005305 LBB0_1104
	0x66, 0x0f, 0xbc, 0xd7, //0x00005305 bsfw         %di, %dx
	0x0f, 0xb7, 0xd2, //0x00005309 movzwl       %dx, %edx
	0x48, 0x29, 0xca, //0x0000530c subq         %rcx, %rdx
	0x49, 0x89, 0x55, 0x00, //0x0000530f movq         %rdx, (%r13)
	0x48, 0x89, 0xc3, //0x00005313 movq         %rax, %rbx
	0xe9, 0x65, 0xf8, 0xff, 0xff, //0x00005316 jmp          LBB0_1025
	//0x0000531b LBB0_1105
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x0000531b movq         $-1, %rcx
	0xe9, 0x19, 0x01, 0x00, 0x00, //0x00005322 jmp          LBB0_1122
	//0x00005327 LBB0_1107
	0x49, 0x89, 0x55, 0x00, //0x00005327 movq         %rdx, (%r13)
	0xe9, 0x50, 0xf8, 0xff, 0xff, //0x0000532b jmp          LBB0_1025
	//0x00005330 LBB0_1109
	0x48, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00005330 movq         $-2, %rbx
	0x80, 0xf9, 0x61, //0x00005337 cmpb         $97, %cl
	0x0f, 0x85, 0x40, 0xf8, 0xff, 0xff, //0x0000533a jne          LBB0_1025
	0x49, 0x8d, 0x46, 0x02, //0x00005340 leaq         $2(%r14), %rax
	0x49, 0x89, 0x45, 0x00, //0x00005344 movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x37, 0x02, 0x6c, //0x00005348 cmpb         $108, $2(%r15,%r14)
	0x0f, 0x85, 0x2c, 0xf8, 0xff, 0xff, //0x0000534e jne          LBB0_1025
	0x49, 0x8d, 0x46, 0x03, //0x00005354 leaq         $3(%r14), %rax
	0x49, 0x89, 0x45, 0x00, //0x00005358 movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x37, 0x03, 0x73, //0x0000535c cmpb         $115, $3(%r15,%r14)
	0x0f, 0x85, 0x18, 0xf8, 0xff, 0xff, //0x00005362 jne          LBB0_1025
	0x49, 0x8d, 0x46, 0x04, //0x00005368 leaq         $4(%r14), %rax
	0x49, 0x89, 0x45, 0x00, //0x0000536c movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x37, 0x04, 0x65, //0x00005370 cmpb         $101, $4(%r15,%r14)
	0x0f, 0x85, 0x04, 0xf8, 0xff, 0xff, //0x00005376 jne          LBB0_1025
	0x49, 0x83, 0xc6, 0x05, //0x0000537c addq         $5, %r14
	0xe9, 0xab, 0x00, 0x00, 0x00, //0x00005380 jmp          LBB0_1119
	//0x00005385 LBB0_878
	0x4d, 0x89, 0x75, 0x00, //0x00005385 movq         %r14, (%r13)
	0x48, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00005389 movq         $-2, %rbx
	0x41, 0x80, 0x38, 0x6e, //0x00005390 cmpb         $110, (%r8)
	0x0f, 0x85, 0xe6, 0xf7, 0xff, 0xff, //0x00005394 jne          LBB0_1025
	0x49, 0x8d, 0x46, 0x01, //0x0000539a leaq         $1(%r14), %rax
	0x49, 0x89, 0x45, 0x00, //0x0000539e movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x37, 0x01, 0x75, //0x000053a2 cmpb         $117, $1(%r15,%r14)
	0x0f, 0x85, 0xd2, 0xf7, 0xff, 0xff, //0x000053a8 jne          LBB0_1025
	0x49, 0x8d, 0x46, 0x02, //0x000053ae leaq         $2(%r14), %rax
	0x49, 0x89, 0x45, 0x00, //0x000053b2 movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x37, 0x02, 0x6c, //0x000053b6 cmpb         $108, $2(%r15,%r14)
	0x0f, 0x85, 0xbe, 0xf7, 0xff, 0xff, //0x000053bc jne          LBB0_1025
	0x49, 0x8d, 0x46, 0x03, //0x000053c2 leaq         $3(%r14), %rax
	0x49, 0x89, 0x45, 0x00, //0x000053c6 movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x37, 0x03, 0x6c, //0x000053ca cmpb         $108, $3(%r15,%r14)
	0x0f, 0x85, 0xaa, 0xf7, 0xff, 0xff, //0x000053d0 jne          LBB0_1025
	0xe9, 0x51, 0x00, 0x00, 0x00, //0x000053d6 jmp          LBB0_1118
	//0x000053db LBB0_1114
	0x4d, 0x89, 0x75, 0x00, //0x000053db movq         %r14, (%r13)
	0x48, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x000053df movq         $-2, %rbx
	0x41, 0x80, 0x38, 0x74, //0x000053e6 cmpb         $116, (%r8)
	0x0f, 0x85, 0x90, 0xf7, 0xff, 0xff, //0x000053ea jne          LBB0_1025
	0x49, 0x8d, 0x46, 0x01, //0x000053f0 leaq         $1(%r14), %rax
	0x49, 0x89, 0x45, 0x00, //0x000053f4 movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x37, 0x01, 0x72, //0x000053f8 cmpb         $114, $1(%r15,%r14)
	0x0f, 0x85, 0x7c, 0xf7, 0xff, 0xff, //0x000053fe jne          LBB0_1025
	0x49, 0x8d, 0x46, 0x02, //0x00005404 leaq         $2(%r14), %rax
	0x49, 0x89, 0x45, 0x00, //0x00005408 movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x37, 0x02, 0x75, //0x0000540c cmpb         $117, $2(%r15,%r14)
	0x0f, 0x85, 0x68, 0xf7, 0xff, 0xff, //0x00005412 jne          LBB0_1025
	0x49, 0x8d, 0x46, 0x03, //0x00005418 leaq         $3(%r14), %rax
	0x49, 0x89, 0x45, 0x00, //0x0000541c movq         %rax, (%r13)
	0x43, 0x80, 0x7c, 0x37, 0x03, 0x65, //0x00005420 cmpb         $101, $3(%r15,%r14)
	0x0f, 0x85, 0x54, 0xf7, 0xff, 0xff, //0x00005426 jne          LBB0_1025
	//0x0000542c LBB0_1118
	0x49, 0x83, 0xc6, 0x04, //0x0000542c addq         $4, %r14
	//0x00005430 LBB0_1119
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00005430 movq         $16(%rsp), %rax
	0x4c, 0x89, 0x30, //0x00005435 movq         %r14, (%rax)
	0xe9, 0x43, 0xf7, 0xff, 0xff, //0x00005438 jmp          LBB0_1025
	//0x0000543d LBB0_1121
	0x4c, 0x89, 0xe9, //0x0000543d movq         %r13, %rcx
	//0x00005440 LBB0_1122
	0x48, 0xf7, 0xd1, //0x00005440 notq         %rcx
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00005443 movq         $16(%rsp), %rax
	0x48, 0x01, 0x08, //0x00005448 addq         %rcx, (%rax)
	0xe9, 0x29, 0xf7, 0xff, 0xff, //0x0000544b jmp          LBB0_1024
	//0x00005450 LBB0_1133
	0x4d, 0x89, 0xe1, //0x00005450 movq         %r12, %r9
	0xe9, 0x53, 0xf7, 0xff, 0xff, //0x00005453 jmp          LBB0_814
	//0x00005458 LBB0_1123
	0x4c, 0x29, 0xd8, //0x00005458 subq         %r11, %rax
	0x48, 0x01, 0xc8, //0x0000545b addq         %rcx, %rax
	0x48, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000545e movq         $-1, %rbx
	0x48, 0x39, 0xd0, //0x00005465 cmpq         %rdx, %rax
	0x0f, 0x82, 0x85, 0xf5, 0xff, 0xff, //0x00005468 jb           LBB0_999
	0xe9, 0x0d, 0xf7, 0xff, 0xff, //0x0000546e jmp          LBB0_1025
	//0x00005473 LBB0_1138
	0x48, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00005473 movq         $-2, %rbx
	0xe9, 0x3a, 0x00, 0x00, 0x00, //0x0000547a jmp          LBB0_1129
	//0x0000547f LBB0_1124
	0x4c, 0x29, 0xda, //0x0000547f subq         %r11, %rdx
	0x48, 0x83, 0xc2, 0x01, //0x00005482 addq         $1, %rdx
	0x48, 0x89, 0x17, //0x00005486 movq         %rdx, (%rdi)
	0x48, 0x89, 0xc3, //0x00005489 movq         %rax, %rbx
	0xe9, 0xef, 0xf6, 0xff, 0xff, //0x0000548c jmp          LBB0_1025
	//0x00005491 LBB0_1125
	0x4c, 0x01, 0xd8, //0x00005491 addq         %r11, %rax
	0x48, 0x85, 0xf6, //0x00005494 testq        %rsi, %rsi
	0x0f, 0x85, 0x19, 0xdb, 0xff, 0xff, //0x00005497 jne          LBB0_622
	0xe9, 0x4b, 0xdb, 0xff, 0xff, //0x0000549d jmp          LBB0_627
	//0x000054a2 LBB0_1126
	0x49, 0x83, 0xc3, 0x01, //0x000054a2 addq         $1, %r11
	0x48, 0xc7, 0xc3, 0xfd, 0xff, 0xff, 0xff, //0x000054a6 movq         $-3, %rbx
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x000054ad jmp          LBB0_1128
	//0x000054b2 LBB0_1127
	0x49, 0x83, 0xc3, 0x01, //0x000054b2 addq         $1, %r11
	//0x000054b6 LBB0_1128
	0x4d, 0x89, 0xd8, //0x000054b6 movq         %r11, %r8
	//0x000054b9 LBB0_1129
	0x4d, 0x29, 0xe0, //0x000054b9 subq         %r12, %r8
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x000054bc movq         $16(%rsp), %rax
	0x4c, 0x89, 0x00, //0x000054c1 movq         %r8, (%rax)
	0xe9, 0xb7, 0xf6, 0xff, 0xff, //0x000054c4 jmp          LBB0_1025
	//0x000054c9 LBB0_1130
	0x4c, 0x01, 0xda, //0x000054c9 addq         %r11, %rdx
	0x48, 0x83, 0xfe, 0x10, //0x000054cc cmpq         $16, %rsi
	0x0f, 0x83, 0xdc, 0xf5, 0xff, 0xff, //0x000054d0 jae          LBB0_1006
	0xe9, 0x2b, 0xf6, 0xff, 0xff, //0x000054d6 jmp          LBB0_1009
	//0x000054db LBB0_1131
	0x4c, 0x29, 0xda, //0x000054db subq         %r11, %rdx
	0x48, 0x01, 0xca, //0x000054de addq         %rcx, %rdx
	//0x000054e1 LBB0_1132
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x000054e1 movq         $16(%rsp), %rcx
	0x48, 0x89, 0x11, //0x000054e6 movq         %rdx, (%rcx)
	0x48, 0x89, 0xc3, //0x000054e9 movq         %rax, %rbx
	0xe9, 0x8f, 0xf6, 0xff, 0xff, //0x000054ec jmp          LBB0_1025
	//0x000054f1 LBB0_1134
	0x4c, 0x01, 0xda, //0x000054f1 addq         %r11, %rdx
	0xe9, 0xd4, 0xf7, 0xff, 0xff, //0x000054f4 jmp          LBB0_1041
	//0x000054f9 LBB0_990
	0x4c, 0x8b, 0x6c, 0x24, 0x10, //0x000054f9 movq         $16(%rsp), %r13
	0xe9, 0xa8, 0xf6, 0xff, 0xff, //0x000054fe jmp          LBB0_814
	//0x00005503 LBB0_1135
	0x48, 0x8b, 0x44, 0x24, 0x18, //0x00005503 movq         $24(%rsp), %rax
	0x48, 0x8b, 0x40, 0x08, //0x00005508 movq         $8(%rax), %rax
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x0000550c movq         $16(%rsp), %rcx
	0x48, 0x89, 0x01, //0x00005511 movq         %rax, (%rcx)
	0xe9, 0x67, 0xf6, 0xff, 0xff, //0x00005514 jmp          LBB0_1025
	//0x00005519 LBB0_1136
	0x49, 0x8d, 0x48, 0xff, //0x00005519 leaq         $-1(%r8), %rcx
	0x4c, 0x39, 0xf1, //0x0000551d cmpq         %r14, %rcx
	0x0f, 0x84, 0x5a, 0xf6, 0xff, 0xff, //0x00005520 je           LBB0_1025
	0x4b, 0x8d, 0x14, 0x0e, //0x00005526 leaq         (%r14,%r9), %rdx
	0x48, 0x83, 0xc2, 0x02, //0x0000552a addq         $2, %rdx
	0x4d, 0x29, 0xf0, //0x0000552e subq         %r14, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00005531 addq         $-2, %r8
	0x4d, 0x89, 0xc6, //0x00005535 movq         %r8, %r14
	0xe9, 0x90, 0xf7, 0xff, 0xff, //0x00005538 jmp          LBB0_1041
	0x90, 0x90, 0x90, //0x0000553d .p2align 2, 0x90
	// // .set L0_0_set_487, LBB0_487-LJTI0_0
	// // .set L0_0_set_486, LBB0_486-LJTI0_0
	// // .set L0_0_set_473, LBB0_473-LJTI0_0
	// // .set L0_0_set_454, LBB0_454-LJTI0_0
	// // .set L0_0_set_548, LBB0_548-LJTI0_0
	// // .set L0_0_set_485, LBB0_485-LJTI0_0
	// // .set L0_0_set_472, LBB0_472-LJTI0_0
	// // .set L0_0_set_522, LBB0_522-LJTI0_0
	//0x00005540 LJTI0_0
	0x77, 0xcf, 0xff, 0xff, //0x00005540 .long L0_0_set_487
	0x70, 0xcf, 0xff, 0xff, //0x00005544 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005548 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000554c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005550 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005554 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005558 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000555c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005560 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005564 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005568 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000556c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005570 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005574 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005578 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000557c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005580 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005584 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005588 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000558c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005590 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005594 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005598 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000559c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055a0 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055a4 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055a8 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055ac .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055b0 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055b4 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055b8 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055bc .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055c0 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055c4 .long L0_0_set_486
	0x62, 0xce, 0xff, 0xff, //0x000055c8 .long L0_0_set_473
	0x70, 0xcf, 0xff, 0xff, //0x000055cc .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055d0 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055d4 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055d8 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055dc .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055e0 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055e4 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055e8 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055ec .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055f0 .long L0_0_set_486
	0x30, 0xcd, 0xff, 0xff, //0x000055f4 .long L0_0_set_454
	0x70, 0xcf, 0xff, 0xff, //0x000055f8 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000055fc .long L0_0_set_486
	0x30, 0xcd, 0xff, 0xff, //0x00005600 .long L0_0_set_454
	0x30, 0xcd, 0xff, 0xff, //0x00005604 .long L0_0_set_454
	0x30, 0xcd, 0xff, 0xff, //0x00005608 .long L0_0_set_454
	0x30, 0xcd, 0xff, 0xff, //0x0000560c .long L0_0_set_454
	0x30, 0xcd, 0xff, 0xff, //0x00005610 .long L0_0_set_454
	0x30, 0xcd, 0xff, 0xff, //0x00005614 .long L0_0_set_454
	0x30, 0xcd, 0xff, 0xff, //0x00005618 .long L0_0_set_454
	0x30, 0xcd, 0xff, 0xff, //0x0000561c .long L0_0_set_454
	0x30, 0xcd, 0xff, 0xff, //0x00005620 .long L0_0_set_454
	0x30, 0xcd, 0xff, 0xff, //0x00005624 .long L0_0_set_454
	0x70, 0xcf, 0xff, 0xff, //0x00005628 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000562c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005630 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005634 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005638 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000563c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005640 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005644 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005648 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000564c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005650 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005654 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005658 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000565c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005660 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005664 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005668 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000566c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005670 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005674 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005678 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000567c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005680 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005684 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005688 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000568c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005690 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005694 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005698 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000569c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056a0 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056a4 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056a8 .long L0_0_set_486
	0xf3, 0xd3, 0xff, 0xff, //0x000056ac .long L0_0_set_548
	0x70, 0xcf, 0xff, 0xff, //0x000056b0 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056b4 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056b8 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056bc .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056c0 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056c4 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056c8 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056cc .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056d0 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056d4 .long L0_0_set_486
	0x54, 0xcf, 0xff, 0xff, //0x000056d8 .long L0_0_set_485
	0x70, 0xcf, 0xff, 0xff, //0x000056dc .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056e0 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056e4 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056e8 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056ec .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056f0 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x000056f4 .long L0_0_set_486
	0x50, 0xce, 0xff, 0xff, //0x000056f8 .long L0_0_set_472
	0x70, 0xcf, 0xff, 0xff, //0x000056fc .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005700 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005704 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005708 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000570c .long L0_0_set_486
	0x50, 0xce, 0xff, 0xff, //0x00005710 .long L0_0_set_472
	0x70, 0xcf, 0xff, 0xff, //0x00005714 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005718 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x0000571c .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005720 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005724 .long L0_0_set_486
	0x70, 0xcf, 0xff, 0xff, //0x00005728 .long L0_0_set_486
	0x64, 0xd1, 0xff, 0xff, //0x0000572c .long L0_0_set_522
	// // .set L0_1_set_203, LBB0_203-LJTI0_1
	// // .set L0_1_set_202, LBB0_202-LJTI0_1
	// // .set L0_1_set_189, LBB0_189-LJTI0_1
	// // .set L0_1_set_159, LBB0_159-LJTI0_1
	// // .set L0_1_set_235, LBB0_235-LJTI0_1
	// // .set L0_1_set_201, LBB0_201-LJTI0_1
	// // .set L0_1_set_183, LBB0_183-LJTI0_1
	// // .set L0_1_set_261, LBB0_261-LJTI0_1
	//0x00005730 LJTI0_1
	0x77, 0xbd, 0xff, 0xff, //0x00005730 .long L0_1_set_203
	0x70, 0xbd, 0xff, 0xff, //0x00005734 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005738 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000573c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005740 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005744 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005748 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000574c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005750 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005754 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005758 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000575c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005760 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005764 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005768 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000576c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005770 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005774 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005778 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000577c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005780 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005784 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005788 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000578c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005790 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005794 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005798 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000579c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057a0 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057a4 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057a8 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057ac .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057b0 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057b4 .long L0_1_set_202
	0x82, 0xbc, 0xff, 0xff, //0x000057b8 .long L0_1_set_189
	0x70, 0xbd, 0xff, 0xff, //0x000057bc .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057c0 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057c4 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057c8 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057cc .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057d0 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057d4 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057d8 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057dc .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057e0 .long L0_1_set_202
	0x5f, 0xbb, 0xff, 0xff, //0x000057e4 .long L0_1_set_159
	0x70, 0xbd, 0xff, 0xff, //0x000057e8 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000057ec .long L0_1_set_202
	0x5f, 0xbb, 0xff, 0xff, //0x000057f0 .long L0_1_set_159
	0x5f, 0xbb, 0xff, 0xff, //0x000057f4 .long L0_1_set_159
	0x5f, 0xbb, 0xff, 0xff, //0x000057f8 .long L0_1_set_159
	0x5f, 0xbb, 0xff, 0xff, //0x000057fc .long L0_1_set_159
	0x5f, 0xbb, 0xff, 0xff, //0x00005800 .long L0_1_set_159
	0x5f, 0xbb, 0xff, 0xff, //0x00005804 .long L0_1_set_159
	0x5f, 0xbb, 0xff, 0xff, //0x00005808 .long L0_1_set_159
	0x5f, 0xbb, 0xff, 0xff, //0x0000580c .long L0_1_set_159
	0x5f, 0xbb, 0xff, 0xff, //0x00005810 .long L0_1_set_159
	0x5f, 0xbb, 0xff, 0xff, //0x00005814 .long L0_1_set_159
	0x70, 0xbd, 0xff, 0xff, //0x00005818 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000581c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005820 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005824 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005828 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000582c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005830 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005834 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005838 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000583c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005840 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005844 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005848 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000584c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005850 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005854 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005858 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000585c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005860 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005864 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005868 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000586c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005870 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005874 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005878 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000587c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005880 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005884 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005888 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000588c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005890 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005894 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005898 .long L0_1_set_202
	0x42, 0xbf, 0xff, 0xff, //0x0000589c .long L0_1_set_235
	0x70, 0xbd, 0xff, 0xff, //0x000058a0 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058a4 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058a8 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058ac .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058b0 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058b4 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058b8 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058bc .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058c0 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058c4 .long L0_1_set_202
	0x63, 0xbd, 0xff, 0xff, //0x000058c8 .long L0_1_set_201
	0x70, 0xbd, 0xff, 0xff, //0x000058cc .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058d0 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058d4 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058d8 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058dc .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058e0 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058e4 .long L0_1_set_202
	0x70, 0xbc, 0xff, 0xff, //0x000058e8 .long L0_1_set_183
	0x70, 0xbd, 0xff, 0xff, //0x000058ec .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058f0 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058f4 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058f8 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x000058fc .long L0_1_set_202
	0x70, 0xbc, 0xff, 0xff, //0x00005900 .long L0_1_set_183
	0x70, 0xbd, 0xff, 0xff, //0x00005904 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005908 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x0000590c .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005910 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005914 .long L0_1_set_202
	0x70, 0xbd, 0xff, 0xff, //0x00005918 .long L0_1_set_202
	0xd5, 0xc1, 0xff, 0xff, //0x0000591c .long L0_1_set_261
	// // .set L0_2_set_669, LBB0_669-LJTI0_2
	// // .set L0_2_set_696, LBB0_696-LJTI0_2
	// // .set L0_2_set_675, LBB0_675-LJTI0_2
	// // .set L0_2_set_694, LBB0_694-LJTI0_2
	// // .set L0_2_set_672, LBB0_672-LJTI0_2
	// // .set L0_2_set_699, LBB0_699-LJTI0_2
	//0x00005920 LJTI0_2
	0xbc, 0xd9, 0xff, 0xff, //0x00005920 .long L0_2_set_669
	0x64, 0xdb, 0xff, 0xff, //0x00005924 .long L0_2_set_696
	0xf3, 0xd9, 0xff, 0xff, //0x00005928 .long L0_2_set_675
	0x4d, 0xdb, 0xff, 0xff, //0x0000592c .long L0_2_set_694
	0xd3, 0xd9, 0xff, 0xff, //0x00005930 .long L0_2_set_672
	0x6a, 0xdf, 0xff, 0xff, //0x00005934 .long L0_2_set_699
	// // .set L0_3_set_1025, LBB0_1025-LJTI0_3
	// // .set L0_3_set_1024, LBB0_1024-LJTI0_3
	// // .set L0_3_set_796, LBB0_796-LJTI0_3
	// // .set L0_3_set_816, LBB0_816-LJTI0_3
	// // .set L0_3_set_701, LBB0_701-LJTI0_3
	// // .set L0_3_set_871, LBB0_871-LJTI0_3
	// // .set L0_3_set_873, LBB0_873-LJTI0_3
	// // .set L0_3_set_876, LBB0_876-LJTI0_3
	// // .set L0_3_set_884, LBB0_884-LJTI0_3
	// // .set L0_3_set_882, LBB0_882-LJTI0_3
	//0x00005938 LJTI0_3
	0x48, 0xf2, 0xff, 0xff, //0x00005938 .long L0_3_set_1025
	0x41, 0xf2, 0xff, 0xff, //0x0000593c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005940 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005944 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005948 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x0000594c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005950 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005954 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005958 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x0000595c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005960 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005964 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005968 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x0000596c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005970 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005974 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005978 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x0000597c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005980 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005984 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005988 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x0000598c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005990 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005994 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005998 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x0000599c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059a0 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059a4 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059a8 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059ac .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059b0 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059b4 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059b8 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059bc .long L0_3_set_1024
	0x19, 0xe2, 0xff, 0xff, //0x000059c0 .long L0_3_set_796
	0x41, 0xf2, 0xff, 0xff, //0x000059c4 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059c8 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059cc .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059d0 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059d4 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059d8 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059dc .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059e0 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059e4 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059e8 .long L0_3_set_1024
	0x3b, 0xe3, 0xff, 0xff, //0x000059ec .long L0_3_set_816
	0x41, 0xf2, 0xff, 0xff, //0x000059f0 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x000059f4 .long L0_3_set_1024
	0x99, 0xdb, 0xff, 0xff, //0x000059f8 .long L0_3_set_701
	0x99, 0xdb, 0xff, 0xff, //0x000059fc .long L0_3_set_701
	0x99, 0xdb, 0xff, 0xff, //0x00005a00 .long L0_3_set_701
	0x99, 0xdb, 0xff, 0xff, //0x00005a04 .long L0_3_set_701
	0x99, 0xdb, 0xff, 0xff, //0x00005a08 .long L0_3_set_701
	0x99, 0xdb, 0xff, 0xff, //0x00005a0c .long L0_3_set_701
	0x99, 0xdb, 0xff, 0xff, //0x00005a10 .long L0_3_set_701
	0x99, 0xdb, 0xff, 0xff, //0x00005a14 .long L0_3_set_701
	0x99, 0xdb, 0xff, 0xff, //0x00005a18 .long L0_3_set_701
	0x99, 0xdb, 0xff, 0xff, //0x00005a1c .long L0_3_set_701
	0x41, 0xf2, 0xff, 0xff, //0x00005a20 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a24 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a28 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a2c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a30 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a34 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a38 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a3c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a40 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a44 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a48 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a4c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a50 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a54 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a58 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a5c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a60 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a64 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a68 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a6c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a70 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a74 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a78 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a7c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a80 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a84 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a88 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a8c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a90 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a94 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a98 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005a9c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005aa0 .long L0_3_set_1024
	0xa5, 0xe6, 0xff, 0xff, //0x00005aa4 .long L0_3_set_871
	0x41, 0xf2, 0xff, 0xff, //0x00005aa8 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005aac .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005ab0 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005ab4 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005ab8 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005abc .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005ac0 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005ac4 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005ac8 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005acc .long L0_3_set_1024
	0xc9, 0xe6, 0xff, 0xff, //0x00005ad0 .long L0_3_set_873
	0x41, 0xf2, 0xff, 0xff, //0x00005ad4 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005ad8 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005adc .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005ae0 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005ae4 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005ae8 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005aec .long L0_3_set_1024
	0xf3, 0xe6, 0xff, 0xff, //0x00005af0 .long L0_3_set_876
	0x41, 0xf2, 0xff, 0xff, //0x00005af4 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005af8 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005afc .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005b00 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005b04 .long L0_3_set_1024
	0x3a, 0xe7, 0xff, 0xff, //0x00005b08 .long L0_3_set_884
	0x41, 0xf2, 0xff, 0xff, //0x00005b0c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005b10 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005b14 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005b18 .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005b1c .long L0_3_set_1024
	0x41, 0xf2, 0xff, 0xff, //0x00005b20 .long L0_3_set_1024
	0x16, 0xe7, 0xff, 0xff, //0x00005b24 .long L0_3_set_882
	// // .set L0_4_set_864, LBB0_864-LJTI0_4
	// // .set L0_4_set_893, LBB0_893-LJTI0_4
	// // .set L0_4_set_869, LBB0_869-LJTI0_4
	// // .set L0_4_set_867, LBB0_867-LJTI0_4
	//0x00005b28 LJTI0_4
	0x73, 0xe4, 0xff, 0xff, //0x00005b28 .long L0_4_set_864
	0x50, 0xe6, 0xff, 0xff, //0x00005b2c .long L0_4_set_893
	0x73, 0xe4, 0xff, 0xff, //0x00005b30 .long L0_4_set_864
	0xa2, 0xe4, 0xff, 0xff, //0x00005b34 .long L0_4_set_869
	0x50, 0xe6, 0xff, 0xff, //0x00005b38 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b3c .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b40 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b44 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b48 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b4c .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b50 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b54 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b58 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b5c .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b60 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b64 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b68 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b6c .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b70 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b74 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b78 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b7c .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b80 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b84 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b88 .long L0_4_set_893
	0x50, 0xe6, 0xff, 0xff, //0x00005b8c .long L0_4_set_893
	0x8f, 0xe4, 0xff, 0xff, //0x00005b90 .long L0_4_set_867
	// // .set L0_5_set_745, LBB0_745-LJTI0_5
	// // .set L0_5_set_782, LBB0_782-LJTI0_5
	// // .set L0_5_set_751, LBB0_751-LJTI0_5
	// // .set L0_5_set_754, LBB0_754-LJTI0_5
	//0x00005b94 LJTI0_5
	0x74, 0xdc, 0xff, 0xff, //0x00005b94 .long L0_5_set_745
	0xd8, 0xde, 0xff, 0xff, //0x00005b98 .long L0_5_set_782
	0x74, 0xdc, 0xff, 0xff, //0x00005b9c .long L0_5_set_745
	0xc0, 0xdc, 0xff, 0xff, //0x00005ba0 .long L0_5_set_751
	0xd8, 0xde, 0xff, 0xff, //0x00005ba4 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005ba8 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bac .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bb0 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bb4 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bb8 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bbc .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bc0 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bc4 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bc8 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bcc .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bd0 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bd4 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bd8 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bdc .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005be0 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005be4 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005be8 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bec .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bf0 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bf4 .long L0_5_set_782
	0xd8, 0xde, 0xff, 0xff, //0x00005bf8 .long L0_5_set_782
	0xe3, 0xdc, 0xff, 0xff, //0x00005bfc .long L0_5_set_754
	// // .set L0_6_set_1025, LBB0_1025-LJTI0_6
	// // .set L0_6_set_1030, LBB0_1030-LJTI0_6
	// // .set L0_6_set_1031, LBB0_1031-LJTI0_6
	// // .set L0_6_set_1001, LBB0_1001-LJTI0_6
	// // .set L0_6_set_1043, LBB0_1043-LJTI0_6
	// // .set L0_6_set_1068, LBB0_1068-LJTI0_6
	// // .set L0_6_set_1026, LBB0_1026-LJTI0_6
	// // .set L0_6_set_1070, LBB0_1070-LJTI0_6
	//0x00005c00 LJTI0_6
	0x80, 0xef, 0xff, 0xff, //0x00005c00 .long L0_6_set_1025
	0xf0, 0xef, 0xff, 0xff, //0x00005c04 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c08 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c0c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c10 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c14 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c18 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c1c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c20 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c24 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c28 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c2c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c30 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c34 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c38 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c3c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c40 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c44 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c48 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c4c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c50 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c54 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c58 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c5c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c60 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c64 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c68 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c6c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c70 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c74 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c78 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c7c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c80 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c84 .long L0_6_set_1030
	0xf9, 0xef, 0xff, 0xff, //0x00005c88 .long L0_6_set_1031
	0xf0, 0xef, 0xff, 0xff, //0x00005c8c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c90 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c94 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c98 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005c9c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005ca0 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005ca4 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005ca8 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005cac .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005cb0 .long L0_6_set_1030
	0x20, 0xee, 0xff, 0xff, //0x00005cb4 .long L0_6_set_1001
	0xf0, 0xef, 0xff, 0xff, //0x00005cb8 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005cbc .long L0_6_set_1030
	0x20, 0xee, 0xff, 0xff, //0x00005cc0 .long L0_6_set_1001
	0x20, 0xee, 0xff, 0xff, //0x00005cc4 .long L0_6_set_1001
	0x20, 0xee, 0xff, 0xff, //0x00005cc8 .long L0_6_set_1001
	0x20, 0xee, 0xff, 0xff, //0x00005ccc .long L0_6_set_1001
	0x20, 0xee, 0xff, 0xff, //0x00005cd0 .long L0_6_set_1001
	0x20, 0xee, 0xff, 0xff, //0x00005cd4 .long L0_6_set_1001
	0x20, 0xee, 0xff, 0xff, //0x00005cd8 .long L0_6_set_1001
	0x20, 0xee, 0xff, 0xff, //0x00005cdc .long L0_6_set_1001
	0x20, 0xee, 0xff, 0xff, //0x00005ce0 .long L0_6_set_1001
	0x20, 0xee, 0xff, 0xff, //0x00005ce4 .long L0_6_set_1001
	0xf0, 0xef, 0xff, 0xff, //0x00005ce8 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005cec .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005cf0 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005cf4 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005cf8 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005cfc .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d00 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d04 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d08 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d0c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d10 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d14 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d18 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d1c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d20 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d24 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d28 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d2c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d30 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d34 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d38 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d3c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d40 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d44 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d48 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d4c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d50 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d54 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d58 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d5c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d60 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d64 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d68 .long L0_6_set_1030
	0xe7, 0xf0, 0xff, 0xff, //0x00005d6c .long L0_6_set_1043
	0xf0, 0xef, 0xff, 0xff, //0x00005d70 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d74 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d78 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d7c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d80 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d84 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d88 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d8c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d90 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005d94 .long L0_6_set_1030
	0x90, 0xf3, 0xff, 0xff, //0x00005d98 .long L0_6_set_1068
	0xf0, 0xef, 0xff, 0xff, //0x00005d9c .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005da0 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005da4 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005da8 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005dac .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005db0 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005db4 .long L0_6_set_1030
	0xc1, 0xef, 0xff, 0xff, //0x00005db8 .long L0_6_set_1026
	0xf0, 0xef, 0xff, 0xff, //0x00005dbc .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005dc0 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005dc4 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005dc8 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005dcc .long L0_6_set_1030
	0xc1, 0xef, 0xff, 0xff, //0x00005dd0 .long L0_6_set_1026
	0xf0, 0xef, 0xff, 0xff, //0x00005dd4 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005dd8 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005ddc .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005de0 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005de4 .long L0_6_set_1030
	0xf0, 0xef, 0xff, 0xff, //0x00005de8 .long L0_6_set_1030
	0xa3, 0xf3, 0xff, 0xff, //0x00005dec .long L0_6_set_1070
	//0x00005df0 .p2align 2, 0x00
	//0x00005df0 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00005df0 .long 2
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005df4 .p2align 4, 0x00
	//0x00005e00 __UnquoteTab
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e00 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e10 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, //0x00005e20 QUAD $0x0000000000220000; QUAD $0x2f00000000000000  // .ascii 16, '\x00\x00"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00/'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e30 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e40 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5c, 0x00, 0x00, 0x00, //0x00005e50 QUAD $0x0000000000000000; QUAD $0x0000005c00000000  // .ascii 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\\\x00\x00\x00'
	0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, //0x00005e60 QUAD $0x000c000000080000; QUAD $0x000a000000000000  // .ascii 16, '\x00\x00\x08\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\n\x00'
	0x00, 0x00, 0x0d, 0x00, 0x09, 0xff, //0x00005e70 LONG $0x000d0000; WORD $0xff09  // .ascii 6, '\x00\x00\r\x00\t\xff'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e76 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e86 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005e96 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ea6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005eb6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ec6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ed6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ee6 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00005ef6 QUAD $0x0000000000000000; WORD $0x0000  // .space 10, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
}
 
