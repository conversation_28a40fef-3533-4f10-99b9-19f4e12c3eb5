// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_skip_object = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // .quad 1
	0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000008 .quad 6
	//0x00000010 LCPI0_11
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x00000010 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000020 LCPI0_12
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000020 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000030 LCPI0_13
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000030 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000040 LCPI0_20
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000040 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000050 LCPI0_21
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000050 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000060 LCPI0_22
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x00000060 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x00000070 LCPI0_23
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x00000070 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x00000080 LCPI0_24
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000080 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000090 LCPI0_25
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000090 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x000000a0 LCPI0_26
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x000000a0 QUAD $0xffffffffffffffff; QUAD $0xffffffffffffffff  // .space 16, '\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000000b0 .p2align 5, 0x00
	//0x000000c0 LCPI0_1
	0x20, //0x000000c0 .byte 32
	0x00, //0x000000c1 .byte 0
	0x00, //0x000000c2 .byte 0
	0x00, //0x000000c3 .byte 0
	0x00, //0x000000c4 .byte 0
	0x00, //0x000000c5 .byte 0
	0x00, //0x000000c6 .byte 0
	0x00, //0x000000c7 .byte 0
	0x00, //0x000000c8 .byte 0
	0x09, //0x000000c9 .byte 9
	0x0a, //0x000000ca .byte 10
	0x00, //0x000000cb .byte 0
	0x00, //0x000000cc .byte 0
	0x0d, //0x000000cd .byte 13
	0x00, //0x000000ce .byte 0
	0x00, //0x000000cf .byte 0
	0x20, //0x000000d0 .byte 32
	0x00, //0x000000d1 .byte 0
	0x00, //0x000000d2 .byte 0
	0x00, //0x000000d3 .byte 0
	0x00, //0x000000d4 .byte 0
	0x00, //0x000000d5 .byte 0
	0x00, //0x000000d6 .byte 0
	0x00, //0x000000d7 .byte 0
	0x00, //0x000000d8 .byte 0
	0x09, //0x000000d9 .byte 9
	0x0a, //0x000000da .byte 10
	0x00, //0x000000db .byte 0
	0x00, //0x000000dc .byte 0
	0x0d, //0x000000dd .byte 13
	0x00, //0x000000de .byte 0
	0x00, //0x000000df .byte 0
	//0x000000e0 LCPI0_2
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x000000e0 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x000000f0 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000100 LCPI0_3
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000100 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000110 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000120 LCPI0_4
	0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, //0x00000120 QUAD $0x1f1f1f1f1f1f1f1f; QUAD $0x1f1f1f1f1f1f1f1f  // .space 16, '\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f'
	0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1f, //0x00000130 QUAD $0x1f1f1f1f1f1f1f1f; QUAD $0x1f1f1f1f1f1f1f1f  // .space 16, '\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f\x1f'
	//0x00000140 LCPI0_5
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000140 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000150 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x00000160 LCPI0_6
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000160 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000170 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000180 LCPI0_7
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000180 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000190 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x000001a0 LCPI0_8
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x000001a0 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x000001b0 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x000001c0 LCPI0_9
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x000001c0 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, //0x000001d0 QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x000001e0 LCPI0_10
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x000001e0 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x000001f0 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000200 LCPI0_14
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000200 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x00000210 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x00000220 LCPI0_15
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000220 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x00000230 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000240 LCPI0_16
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000240 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000250 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000260 LCPI0_17
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000260 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000270 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x00000280 LCPI0_18
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x00000280 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, 0xd0, //0x00000290 QUAD $0xd0d0d0d0d0d0d0d0; QUAD $0xd0d0d0d0d0d0d0d0  // .space 16, '\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0\xd0'
	//0x000002a0 LCPI0_19
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x000002a0 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, //0x000002b0 QUAD $0x0909090909090909; QUAD $0x0909090909090909  // .space 16, '\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'
	//0x000002c0 .p2align 4, 0x90
	//0x000002c0 _skip_object
	0x55, //0x000002c0 pushq        %rbp
	0x48, 0x89, 0xe5, //0x000002c1 movq         %rsp, %rbp
	0x41, 0x57, //0x000002c4 pushq        %r15
	0x41, 0x56, //0x000002c6 pushq        %r14
	0x41, 0x55, //0x000002c8 pushq        %r13
	0x41, 0x54, //0x000002ca pushq        %r12
	0x53, //0x000002cc pushq        %rbx
	0x48, 0x81, 0xec, 0xa0, 0x00, 0x00, 0x00, //0x000002cd subq         $160, %rsp
	0x49, 0x89, 0xc8, //0x000002d4 movq         %rcx, %r8
	0x49, 0x89, 0xd6, //0x000002d7 movq         %rdx, %r14
	0x49, 0x89, 0xfb, //0x000002da movq         %rdi, %r11
	0xc5, 0xfa, 0x6f, 0x05, 0x1b, 0xfd, 0xff, 0xff, //0x000002dd vmovdqu      $-741(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xc5, 0xfa, 0x7f, 0x02, //0x000002e5 vmovdqu      %xmm0, (%rdx)
	0x48, 0x89, 0x74, 0x24, 0x08, //0x000002e9 movq         %rsi, $8(%rsp)
	0x48, 0x8b, 0x0e, //0x000002ee movq         (%rsi), %rcx
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000002f1 movq         $-1, %r9
	0x41, 0xba, 0x01, 0x00, 0x00, 0x00, //0x000002f8 movl         $1, %r10d
	0xc5, 0xfe, 0x6f, 0x2d, 0xba, 0xfd, 0xff, 0xff, //0x000002fe vmovdqu      $-582(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000306 movabsq      $4294977024, %r15
	0xc5, 0xfe, 0x6f, 0x35, 0xc8, 0xfd, 0xff, 0xff, //0x00000310 vmovdqu      $-568(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xe0, 0xfd, 0xff, 0xff, //0x00000318 vmovdqu      $-544(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xf8, 0xfd, 0xff, 0xff, //0x00000320 vmovdqu      $-520(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0xd0, 0xfe, 0xff, 0xff, //0x00000328 vmovdqu      $-304(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0xe8, 0xfe, 0xff, 0xff, //0x00000330 vmovdqu      $-280(%rip), %ymm13  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xa0, 0xfe, 0xff, 0xff, //0x00000338 vmovdqu      $-352(%rip), %ymm11  /* LCPI0_10+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xf8, 0xfe, 0xff, 0xff, //0x00000340 vmovdqu      $-264(%rip), %ymm14  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x10, 0xff, 0xff, 0xff, //0x00000348 vmovdqu      $-240(%rip), %ymm15  /* LCPI0_17+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x68, 0xfe, 0xff, 0xff, //0x00000350 vmovdqu      $-408(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x40, 0xfe, 0xff, 0xff, //0x00000358 vmovdqu      $-448(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xd8, 0xfd, 0xff, 0xff, //0x00000360 vmovdqu      $-552(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xf0, 0xfd, 0xff, 0xff, //0x00000368 vmovdqu      $-528(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0x4c, 0x89, 0x44, 0x24, 0x18, //0x00000370 movq         %r8, $24(%rsp)
	0x48, 0x89, 0x7c, 0x24, 0x20, //0x00000375 movq         %rdi, $32(%rsp)
	0x48, 0x89, 0x54, 0x24, 0x28, //0x0000037a movq         %rdx, $40(%rsp)
	0xe9, 0x2f, 0x00, 0x00, 0x00, //0x0000037f jmp          LBB0_6
	//0x00000384 LBB0_1
	0x49, 0x8d, 0x4d, 0x04, //0x00000384 leaq         $4(%r13), %rcx
	//0x00000388 LBB0_2
	0x48, 0x89, 0x0a, //0x00000388 movq         %rcx, (%rdx)
	//0x0000038b LBB0_3
	0x4c, 0x89, 0xe8, //0x0000038b movq         %r13, %rax
	0x48, 0xba, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x0000038e movabsq      $9223372036854775806, %rdx
	0x49, 0x39, 0xd5, //0x00000398 cmpq         %rdx, %r13
	0x0f, 0x87, 0x5c, 0x3a, 0x00, 0x00, //0x0000039b ja           LBB0_721
	//0x000003a1 LBB0_4
	0x49, 0x8b, 0x36, //0x000003a1 movq         (%r14), %rsi
	0x49, 0x89, 0xf2, //0x000003a4 movq         %rsi, %r10
	0x4c, 0x89, 0xc8, //0x000003a7 movq         %r9, %rax
	0x48, 0x85, 0xf6, //0x000003aa testq        %rsi, %rsi
	0x0f, 0x84, 0x4a, 0x3a, 0x00, 0x00, //0x000003ad je           LBB0_721
	//0x000003b3 LBB0_6
	0x4d, 0x8b, 0x23, //0x000003b3 movq         (%r11), %r12
	0x49, 0x8b, 0x43, 0x08, //0x000003b6 movq         $8(%r11), %rax
	0x48, 0x39, 0xc1, //0x000003ba cmpq         %rax, %rcx
	0x0f, 0x83, 0x2d, 0x00, 0x00, 0x00, //0x000003bd jae          LBB0_11
	0x41, 0x8a, 0x14, 0x0c, //0x000003c3 movb         (%r12,%rcx), %dl
	0x80, 0xfa, 0x0d, //0x000003c7 cmpb         $13, %dl
	0x0f, 0x84, 0x20, 0x00, 0x00, 0x00, //0x000003ca je           LBB0_11
	0x80, 0xfa, 0x20, //0x000003d0 cmpb         $32, %dl
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x000003d3 je           LBB0_11
	0x80, 0xc2, 0xf5, //0x000003d9 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x000003dc cmpb         $-2, %dl
	0x0f, 0x83, 0x0b, 0x00, 0x00, 0x00, //0x000003df jae          LBB0_11
	0x49, 0x89, 0xcd, //0x000003e5 movq         %rcx, %r13
	0xe9, 0x6d, 0x01, 0x00, 0x00, //0x000003e8 jmp          LBB0_36
	0x90, 0x90, 0x90, //0x000003ed .p2align 4, 0x90
	//0x000003f0 LBB0_11
	0x4c, 0x8d, 0x69, 0x01, //0x000003f0 leaq         $1(%rcx), %r13
	0x49, 0x39, 0xc5, //0x000003f4 cmpq         %rax, %r13
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x000003f7 jae          LBB0_15
	0x43, 0x8a, 0x14, 0x2c, //0x000003fd movb         (%r12,%r13), %dl
	0x80, 0xfa, 0x0d, //0x00000401 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000404 je           LBB0_15
	0x80, 0xfa, 0x20, //0x0000040a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000040d je           LBB0_15
	0x80, 0xc2, 0xf5, //0x00000413 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00000416 cmpb         $-2, %dl
	0x0f, 0x82, 0x3b, 0x01, 0x00, 0x00, //0x00000419 jb           LBB0_36
	0x90, //0x0000041f .p2align 4, 0x90
	//0x00000420 LBB0_15
	0x4c, 0x8d, 0x69, 0x02, //0x00000420 leaq         $2(%rcx), %r13
	0x49, 0x39, 0xc5, //0x00000424 cmpq         %rax, %r13
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000427 jae          LBB0_19
	0x43, 0x8a, 0x14, 0x2c, //0x0000042d movb         (%r12,%r13), %dl
	0x80, 0xfa, 0x0d, //0x00000431 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000434 je           LBB0_19
	0x80, 0xfa, 0x20, //0x0000043a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000043d je           LBB0_19
	0x80, 0xc2, 0xf5, //0x00000443 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00000446 cmpb         $-2, %dl
	0x0f, 0x82, 0x0b, 0x01, 0x00, 0x00, //0x00000449 jb           LBB0_36
	0x90, //0x0000044f .p2align 4, 0x90
	//0x00000450 LBB0_19
	0x4c, 0x8d, 0x69, 0x03, //0x00000450 leaq         $3(%rcx), %r13
	0x49, 0x39, 0xc5, //0x00000454 cmpq         %rax, %r13
	0x0f, 0x83, 0x23, 0x00, 0x00, 0x00, //0x00000457 jae          LBB0_23
	0x43, 0x8a, 0x14, 0x2c, //0x0000045d movb         (%r12,%r13), %dl
	0x80, 0xfa, 0x0d, //0x00000461 cmpb         $13, %dl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00000464 je           LBB0_23
	0x80, 0xfa, 0x20, //0x0000046a cmpb         $32, %dl
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x0000046d je           LBB0_23
	0x80, 0xc2, 0xf5, //0x00000473 addb         $-11, %dl
	0x80, 0xfa, 0xfe, //0x00000476 cmpb         $-2, %dl
	0x0f, 0x82, 0xdb, 0x00, 0x00, 0x00, //0x00000479 jb           LBB0_36
	0x90, //0x0000047f .p2align 4, 0x90
	//0x00000480 LBB0_23
	0x4c, 0x8d, 0x69, 0x04, //0x00000480 leaq         $4(%rcx), %r13
	0x48, 0x89, 0xc6, //0x00000484 movq         %rax, %rsi
	0x4c, 0x29, 0xee, //0x00000487 subq         %r13, %rsi
	0x0f, 0x86, 0x01, 0x39, 0x00, 0x00, //0x0000048a jbe          LBB0_693
	0x48, 0x83, 0xfe, 0x20, //0x00000490 cmpq         $32, %rsi
	0x0f, 0x82, 0x5c, 0x25, 0x00, 0x00, //0x00000494 jb           LBB0_502
	0x48, 0xc7, 0xc6, 0xfc, 0xff, 0xff, 0xff, //0x0000049a movq         $-4, %rsi
	0x48, 0x29, 0xce, //0x000004a1 subq         %rcx, %rsi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000004a4 .p2align 4, 0x90
	//0x000004b0 LBB0_26
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x2c, //0x000004b0 vmovdqu      (%r12,%r13), %ymm0
	0xc4, 0xe2, 0x55, 0x00, 0xc8, //0x000004b6 vpshufb      %ymm0, %ymm5, %ymm1
	0xc5, 0xfd, 0xf8, 0xd1, //0x000004bb vpsubb       %ymm1, %ymm0, %ymm2
	0xc4, 0xe2, 0x7d, 0x17, 0xd2, //0x000004bf vptest       %ymm2, %ymm2
	0x0f, 0x85, 0x76, 0x00, 0x00, 0x00, //0x000004c4 jne          LBB0_35
	0x49, 0x83, 0xc5, 0x20, //0x000004ca addq         $32, %r13
	0x48, 0x8d, 0x0c, 0x30, //0x000004ce leaq         (%rax,%rsi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x000004d2 addq         $-32, %rcx
	0x48, 0x83, 0xc6, 0xe0, //0x000004d6 addq         $-32, %rsi
	0x48, 0x83, 0xf9, 0x1f, //0x000004da cmpq         $31, %rcx
	0x0f, 0x87, 0xcc, 0xff, 0xff, 0xff, //0x000004de ja           LBB0_26
	0x4d, 0x89, 0xe5, //0x000004e4 movq         %r12, %r13
	0x49, 0x29, 0xf5, //0x000004e7 subq         %rsi, %r13
	0x48, 0x01, 0xc6, //0x000004ea addq         %rax, %rsi
	0x48, 0x85, 0xf6, //0x000004ed testq        %rsi, %rsi
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x000004f0 je           LBB0_34
	//0x000004f6 LBB0_29
	0x4a, 0x8d, 0x14, 0x2e, //0x000004f6 leaq         (%rsi,%r13), %rdx
	0x31, 0xc9, //0x000004fa xorl         %ecx, %ecx
	0x90, 0x90, 0x90, 0x90, //0x000004fc .p2align 4, 0x90
	//0x00000500 LBB0_30
	0x41, 0x0f, 0xbe, 0x7c, 0x0d, 0x00, //0x00000500 movsbl       (%r13,%rcx), %edi
	0x83, 0xff, 0x20, //0x00000506 cmpl         $32, %edi
	0x0f, 0x87, 0x98, 0x20, 0x00, 0x00, //0x00000509 ja           LBB0_470
	0x49, 0x0f, 0xa3, 0xff, //0x0000050f btq          %rdi, %r15
	0x0f, 0x83, 0x8e, 0x20, 0x00, 0x00, //0x00000513 jae          LBB0_470
	0x48, 0x83, 0xc1, 0x01, //0x00000519 addq         $1, %rcx
	0x48, 0x39, 0xce, //0x0000051d cmpq         %rcx, %rsi
	0x0f, 0x85, 0xda, 0xff, 0xff, 0xff, //0x00000520 jne          LBB0_30
	0x49, 0x89, 0xd5, //0x00000526 movq         %rdx, %r13
	//0x00000529 LBB0_34
	0x4d, 0x29, 0xe5, //0x00000529 subq         %r12, %r13
	0x49, 0x39, 0xc5, //0x0000052c cmpq         %rax, %r13
	0x0f, 0x82, 0x25, 0x00, 0x00, 0x00, //0x0000052f jb           LBB0_36
	0xe9, 0x5f, 0x38, 0x00, 0x00, //0x00000535 jmp          LBB0_694
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000053a .p2align 4, 0x90
	//0x00000540 LBB0_35
	0xc5, 0xfd, 0x74, 0xc1, //0x00000540 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x00000544 vpmovmskb    %ymm0, %ecx
	0xf7, 0xd1, //0x00000548 notl         %ecx
	0x44, 0x0f, 0xbc, 0xe9, //0x0000054a bsfl         %ecx, %r13d
	0x49, 0x29, 0xf5, //0x0000054e subq         %rsi, %r13
	0x49, 0x39, 0xc5, //0x00000551 cmpq         %rax, %r13
	0x0f, 0x83, 0x3f, 0x38, 0x00, 0x00, //0x00000554 jae          LBB0_694
	//0x0000055a LBB0_36
	0x49, 0x8d, 0x4d, 0x01, //0x0000055a leaq         $1(%r13), %rcx
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x0000055e movq         $8(%rsp), %rax
	0x48, 0x89, 0x08, //0x00000563 movq         %rcx, (%rax)
	0x43, 0x0f, 0xbe, 0x14, 0x2c, //0x00000566 movsbl       (%r12,%r13), %edx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000056b movq         $-1, %rax
	0x85, 0xd2, //0x00000572 testl        %edx, %edx
	0x0f, 0x84, 0x83, 0x38, 0x00, 0x00, //0x00000574 je           LBB0_721
	0x49, 0x8d, 0x72, 0xff, //0x0000057a leaq         $-1(%r10), %rsi
	0x43, 0x8b, 0x3c, 0xd6, //0x0000057e movl         (%r14,%r10,8), %edi
	0x49, 0x83, 0xf9, 0xff, //0x00000582 cmpq         $-1, %r9
	0x4d, 0x0f, 0x44, 0xcd, //0x00000586 cmoveq       %r13, %r9
	0x83, 0xc7, 0xff, //0x0000058a addl         $-1, %edi
	0x83, 0xff, 0x05, //0x0000058d cmpl         $5, %edi
	0x0f, 0x87, 0x13, 0x02, 0x00, 0x00, //0x00000590 ja           LBB0_84
	0x48, 0x8d, 0x1d, 0x33, 0x3b, 0x00, 0x00, //0x00000596 leaq         $15155(%rip), %rbx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x3c, 0xbb, //0x0000059d movslq       (%rbx,%rdi,4), %rdi
	0x48, 0x01, 0xdf, //0x000005a1 addq         %rbx, %rdi
	0xff, 0xe7, //0x000005a4 jmpq         *%rdi
	//0x000005a6 LBB0_39
	0x83, 0xfa, 0x2c, //0x000005a6 cmpl         $44, %edx
	0x0f, 0x84, 0x71, 0x06, 0x00, 0x00, //0x000005a9 je           LBB0_143
	0x83, 0xfa, 0x5d, //0x000005af cmpl         $93, %edx
	0x0f, 0x84, 0xda, 0x01, 0x00, 0x00, //0x000005b2 je           LBB0_41
	0xe9, 0x39, 0x38, 0x00, 0x00, //0x000005b8 jmp          LBB0_720
	//0x000005bd LBB0_42
	0x80, 0xfa, 0x5d, //0x000005bd cmpb         $93, %dl
	0x0f, 0x84, 0xcc, 0x01, 0x00, 0x00, //0x000005c0 je           LBB0_41
	0x4b, 0xc7, 0x04, 0xd6, 0x01, 0x00, 0x00, 0x00, //0x000005c6 movq         $1, (%r14,%r10,8)
	0x83, 0xfa, 0x7b, //0x000005ce cmpl         $123, %edx
	0x0f, 0x86, 0xde, 0x01, 0x00, 0x00, //0x000005d1 jbe          LBB0_85
	0xe9, 0x1a, 0x38, 0x00, 0x00, //0x000005d7 jmp          LBB0_720
	//0x000005dc LBB0_44
	0x80, 0xfa, 0x22, //0x000005dc cmpb         $34, %dl
	0x0f, 0x85, 0x11, 0x38, 0x00, 0x00, //0x000005df jne          LBB0_720
	0x4b, 0xc7, 0x04, 0xd6, 0x04, 0x00, 0x00, 0x00, //0x000005e5 movq         $4, (%r14,%r10,8)
	0x4d, 0x8b, 0x73, 0x08, //0x000005ed movq         $8(%r11), %r14
	0x41, 0xf6, 0xc0, 0x40, //0x000005f1 testb        $64, %r8b
	0x0f, 0x85, 0x94, 0x07, 0x00, 0x00, //0x000005f5 jne          LBB0_153
	0x41, 0xf6, 0xc0, 0x20, //0x000005fb testb        $32, %r8b
	0x0f, 0x85, 0xa1, 0x0a, 0x00, 0x00, //0x000005ff jne          LBB0_195
	0x4d, 0x89, 0xf2, //0x00000605 movq         %r14, %r10
	0x49, 0x29, 0xca, //0x00000608 subq         %rcx, %r10
	0x0f, 0x84, 0x92, 0x39, 0x00, 0x00, //0x0000060b je           LBB0_724
	0x49, 0x83, 0xfa, 0x40, //0x00000611 cmpq         $64, %r10
	0x0f, 0x82, 0xfc, 0x26, 0x00, 0x00, //0x00000615 jb           LBB0_532
	0x48, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x0000061b movq         $-2, %rsi
	0x4c, 0x29, 0xee, //0x00000622 subq         %r13, %rsi
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00000625 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc0, //0x0000062e xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000631 .p2align 4, 0x90
	//0x00000640 LBB0_50
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x0c, //0x00000640 vmovdqu      (%r12,%rcx), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x0c, 0x20, //0x00000646 vmovdqu      $32(%r12,%rcx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x0000064d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00000651 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000655 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00000659 vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x0000065d vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00000661 vpmovmskb    %ymm0, %edx
	0xc5, 0xf5, 0x74, 0xc7, //0x00000665 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x00000669 vpmovmskb    %ymm0, %ebx
	0x48, 0xc1, 0xe0, 0x20, //0x0000066d shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00000671 orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x20, //0x00000674 shlq         $32, %rbx
	0x48, 0x09, 0xda, //0x00000678 orq          %rbx, %rdx
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x0000067b jne          LBB0_59
	0x4d, 0x85, 0xc0, //0x00000681 testq        %r8, %r8
	0x0f, 0x85, 0x3f, 0x00, 0x00, 0x00, //0x00000684 jne          LBB0_61
	0x45, 0x31, 0xc0, //0x0000068a xorl         %r8d, %r8d
	0x48, 0x85, 0xff, //0x0000068d testq        %rdi, %rdi
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00000690 jne          LBB0_62
	//0x00000696 LBB0_53
	0x49, 0x83, 0xc2, 0xc0, //0x00000696 addq         $-64, %r10
	0x48, 0x83, 0xc6, 0xc0, //0x0000069a addq         $-64, %rsi
	0x48, 0x83, 0xc1, 0x40, //0x0000069e addq         $64, %rcx
	0x49, 0x83, 0xfa, 0x3f, //0x000006a2 cmpq         $63, %r10
	0x0f, 0x87, 0x94, 0xff, 0xff, 0xff, //0x000006a6 ja           LBB0_50
	0xe9, 0x71, 0x22, 0x00, 0x00, //0x000006ac jmp          LBB0_54
	//0x000006b1 LBB0_59
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x000006b1 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x000006b7 jne          LBB0_61
	0x48, 0x0f, 0xbc, 0xc2, //0x000006bd bsfq         %rdx, %rax
	0x48, 0x01, 0xc8, //0x000006c1 addq         %rcx, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x000006c4 movq         %rax, $16(%rsp)
	//0x000006c9 LBB0_61
	0x4c, 0x89, 0xc0, //0x000006c9 movq         %r8, %rax
	0x48, 0xf7, 0xd0, //0x000006cc notq         %rax
	0x48, 0x21, 0xd0, //0x000006cf andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x000006d2 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xc3, //0x000006d6 orq          %r8, %r11
	0x4c, 0x89, 0xdb, //0x000006d9 movq         %r11, %rbx
	0x48, 0xf7, 0xd3, //0x000006dc notq         %rbx
	0x48, 0x21, 0xd3, //0x000006df andq         %rdx, %rbx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000006e2 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd3, //0x000006ec andq         %rdx, %rbx
	0x45, 0x31, 0xc0, //0x000006ef xorl         %r8d, %r8d
	0x48, 0x01, 0xc3, //0x000006f2 addq         %rax, %rbx
	0x41, 0x0f, 0x92, 0xc0, //0x000006f5 setb         %r8b
	0x48, 0x01, 0xdb, //0x000006f9 addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000006fc movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x00000706 xorq         %rax, %rbx
	0x4c, 0x21, 0xdb, //0x00000709 andq         %r11, %rbx
	0x48, 0xf7, 0xd3, //0x0000070c notq         %rbx
	0x48, 0x21, 0xdf, //0x0000070f andq         %rbx, %rdi
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00000712 movq         $32(%rsp), %r11
	0x48, 0x85, 0xff, //0x00000717 testq        %rdi, %rdi
	0x0f, 0x84, 0x76, 0xff, 0xff, 0xff, //0x0000071a je           LBB0_53
	//0x00000720 LBB0_62
	0x48, 0x0f, 0xbc, 0xcf, //0x00000720 bsfq         %rdi, %rcx
	0x48, 0x29, 0xf1, //0x00000724 subq         %rsi, %rcx
	//0x00000727 LBB0_63
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00000727 movq         $24(%rsp), %r8
	//0x0000072c LBB0_64
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x0000072c movq         $8(%rsp), %rbx
	0x48, 0x85, 0xc9, //0x00000731 testq        %rcx, %rcx
	0x0f, 0x89, 0x67, 0x0c, 0x00, 0x00, //0x00000734 jns          LBB0_247
	0xe9, 0x72, 0x36, 0x00, 0x00, //0x0000073a jmp          LBB0_696
	//0x0000073f LBB0_65
	0x80, 0xfa, 0x3a, //0x0000073f cmpb         $58, %dl
	0x0f, 0x85, 0xae, 0x36, 0x00, 0x00, //0x00000742 jne          LBB0_720
	0x4b, 0xc7, 0x04, 0xd6, 0x00, 0x00, 0x00, 0x00, //0x00000748 movq         $0, (%r14,%r10,8)
	0xe9, 0x4c, 0xfc, 0xff, 0xff, //0x00000750 jmp          LBB0_4
	//0x00000755 LBB0_67
	0x83, 0xfa, 0x2c, //0x00000755 cmpl         $44, %edx
	0x0f, 0x85, 0x2b, 0x00, 0x00, 0x00, //0x00000758 jne          LBB0_68
	0x49, 0x81, 0xfa, 0xff, 0x0f, 0x00, 0x00, //0x0000075e cmpq         $4095, %r10
	0x0f, 0x8f, 0x3a, 0x36, 0x00, 0x00, //0x00000765 jg           LBB0_713
	0x49, 0x8d, 0x42, 0x01, //0x0000076b leaq         $1(%r10), %rax
	0x49, 0x89, 0x06, //0x0000076f movq         %rax, (%r14)
	0x4b, 0xc7, 0x44, 0xd6, 0x08, 0x03, 0x00, 0x00, 0x00, //0x00000772 movq         $3, $8(%r14,%r10,8)
	0xe9, 0x21, 0xfc, 0xff, 0xff, //0x0000077b jmp          LBB0_4
	//0x00000780 LBB0_69
	0x83, 0xfa, 0x22, //0x00000780 cmpl         $34, %edx
	0x0f, 0x84, 0xb9, 0x04, 0x00, 0x00, //0x00000783 je           LBB0_70
	//0x00000789 LBB0_68
	0x83, 0xfa, 0x7d, //0x00000789 cmpl         $125, %edx
	0x0f, 0x85, 0x64, 0x36, 0x00, 0x00, //0x0000078c jne          LBB0_720
	//0x00000792 LBB0_41
	0x49, 0x89, 0x36, //0x00000792 movq         %rsi, (%r14)
	0x49, 0x89, 0xf2, //0x00000795 movq         %rsi, %r10
	0x4c, 0x89, 0xc8, //0x00000798 movq         %r9, %rax
	0x48, 0x85, 0xf6, //0x0000079b testq        %rsi, %rsi
	0x0f, 0x85, 0x0f, 0xfc, 0xff, 0xff, //0x0000079e jne          LBB0_6
	0xe9, 0x54, 0x36, 0x00, 0x00, //0x000007a4 jmp          LBB0_721
	//0x000007a9 LBB0_84
	0x49, 0x89, 0x36, //0x000007a9 movq         %rsi, (%r14)
	0x83, 0xfa, 0x7b, //0x000007ac cmpl         $123, %edx
	0x0f, 0x87, 0x41, 0x36, 0x00, 0x00, //0x000007af ja           LBB0_720
	//0x000007b5 LBB0_85
	0x4f, 0x8d, 0x14, 0x2c, //0x000007b5 leaq         (%r12,%r13), %r10
	0x89, 0xd2, //0x000007b9 movl         %edx, %edx
	0x48, 0x8d, 0x35, 0x26, 0x39, 0x00, 0x00, //0x000007bb leaq         $14630(%rip), %rsi  /* LJTI0_1+0(%rip) */
	0x48, 0x63, 0x14, 0x96, //0x000007c2 movslq       (%rsi,%rdx,4), %rdx
	0x48, 0x01, 0xf2, //0x000007c6 addq         %rsi, %rdx
	0xff, 0xe2, //0x000007c9 jmpq         *%rdx
	//0x000007cb LBB0_86
	0x49, 0x8b, 0x5b, 0x08, //0x000007cb movq         $8(%r11), %rbx
	0x41, 0xf6, 0xc0, 0x40, //0x000007cf testb        $64, %r8b
	0x0f, 0x85, 0xa7, 0x06, 0x00, 0x00, //0x000007d3 jne          LBB0_165
	0x4c, 0x29, 0xeb, //0x000007d9 subq         %r13, %rbx
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x000007dc movq         $8(%rsp), %rsi
	0x0f, 0x84, 0xee, 0x35, 0x00, 0x00, //0x000007e1 je           LBB0_699
	0x41, 0x80, 0x3a, 0x30, //0x000007e7 cmpb         $48, (%r10)
	0x0f, 0x85, 0x35, 0x00, 0x00, 0x00, //0x000007eb jne          LBB0_92
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x000007f1 movl         $1, %r11d
	0x48, 0x83, 0xfb, 0x01, //0x000007f7 cmpq         $1, %rbx
	0x0f, 0x84, 0xd9, 0x22, 0x00, 0x00, //0x000007fb je           LBB0_509
	0x41, 0x8a, 0x04, 0x0c, //0x00000801 movb         (%r12,%rcx), %al
	0x04, 0xd2, //0x00000805 addb         $-46, %al
	0x3c, 0x37, //0x00000807 cmpb         $55, %al
	0x0f, 0x87, 0xcb, 0x22, 0x00, 0x00, //0x00000809 ja           LBB0_509
	0x0f, 0xb6, 0xc0, //0x0000080f movzbl       %al, %eax
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000812 movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xc2, //0x0000081c btq          %rax, %rdx
	0x0f, 0x83, 0xb4, 0x22, 0x00, 0x00, //0x00000820 jae          LBB0_509
	//0x00000826 LBB0_92
	0x48, 0x83, 0xfb, 0x20, //0x00000826 cmpq         $32, %rbx
	0x0f, 0x82, 0x56, 0x24, 0x00, 0x00, //0x0000082a jb           LBB0_524
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00000830 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xdb, //0x00000839 xorl         %r11d, %r11d
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000083c movq         $-1, %r8
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00000843 movq         $-1, %r15
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000084a .p2align 4, 0x90
	//0x00000850 LBB0_94
	0xc4, 0x81, 0x7e, 0x6f, 0x04, 0x1a, //0x00000850 vmovdqu      (%r10,%r11), %ymm0
	0xc5, 0xb5, 0x74, 0xc8, //0x00000856 vpcmpeqb     %ymm0, %ymm9, %ymm1
	0xc5, 0x95, 0x74, 0xd0, //0x0000085a vpcmpeqb     %ymm0, %ymm13, %ymm2
	0xc5, 0xed, 0xeb, 0xc9, //0x0000085e vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xa5, 0xdb, 0xd0, //0x00000862 vpand        %ymm0, %ymm11, %ymm2
	0xc5, 0x8d, 0x74, 0xd8, //0x00000866 vpcmpeqb     %ymm0, %ymm14, %ymm3
	0xc5, 0xfd, 0xd7, 0xfb, //0x0000086a vpmovmskb    %ymm3, %edi
	0xc5, 0x85, 0x74, 0xd2, //0x0000086e vpcmpeqb     %ymm2, %ymm15, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x00000872 vpmovmskb    %ymm2, %esi
	0xc5, 0xfd, 0xd7, 0xc1, //0x00000876 vpmovmskb    %ymm1, %eax
	0xc5, 0xfd, 0xfc, 0x05, 0xfe, 0xf9, 0xff, 0xff, //0x0000087a vpaddb       $-1538(%rip), %ymm0, %ymm0  /* LCPI0_18+0(%rip) */
	0xc5, 0xfd, 0xda, 0x25, 0x16, 0xfa, 0xff, 0xff, //0x00000882 vpminub      $-1514(%rip), %ymm0, %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfd, 0x74, 0xc4, //0x0000088a vpcmpeqb     %ymm4, %ymm0, %ymm0
	0xc5, 0xe5, 0xeb, 0xd2, //0x0000088e vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0xfd, 0xeb, 0xc2, //0x00000892 vpor         %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x00000896 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x0000089a vpmovmskb    %ymm0, %ecx
	0x48, 0xf7, 0xd1, //0x0000089e notq         %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x000008a1 bsfq         %rcx, %rcx
	0x83, 0xf9, 0x20, //0x000008a5 cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x000008a8 je           LBB0_96
	0xba, 0xff, 0xff, 0xff, 0xff, //0x000008ae movl         $-1, %edx
	0xd3, 0xe2, //0x000008b3 shll         %cl, %edx
	0xf7, 0xd2, //0x000008b5 notl         %edx
	0x21, 0xd7, //0x000008b7 andl         %edx, %edi
	0x21, 0xd6, //0x000008b9 andl         %edx, %esi
	0x21, 0xc2, //0x000008bb andl         %eax, %edx
	0x89, 0xd0, //0x000008bd movl         %edx, %eax
	//0x000008bf LBB0_96
	0x8d, 0x57, 0xff, //0x000008bf leal         $-1(%rdi), %edx
	0x21, 0xfa, //0x000008c2 andl         %edi, %edx
	0xc5, 0xfe, 0x6f, 0x1d, 0xf4, 0xf8, 0xff, 0xff, //0x000008c4 vmovdqu      $-1804(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xcc, 0xf8, 0xff, 0xff, //0x000008cc vmovdqu      $-1844(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0x0f, 0x85, 0xfc, 0x1d, 0x00, 0x00, //0x000008d4 jne          LBB0_478
	0x8d, 0x56, 0xff, //0x000008da leal         $-1(%rsi), %edx
	0x21, 0xf2, //0x000008dd andl         %esi, %edx
	0x0f, 0x85, 0xf1, 0x1d, 0x00, 0x00, //0x000008df jne          LBB0_478
	0x8d, 0x50, 0xff, //0x000008e5 leal         $-1(%rax), %edx
	0x21, 0xc2, //0x000008e8 andl         %eax, %edx
	0x0f, 0x85, 0xe6, 0x1d, 0x00, 0x00, //0x000008ea jne          LBB0_478
	0x85, 0xff, //0x000008f0 testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000008f2 je           LBB0_102
	0x0f, 0xbc, 0xd7, //0x000008f8 bsfl         %edi, %edx
	0x49, 0x83, 0xff, 0xff, //0x000008fb cmpq         $-1, %r15
	0x0f, 0x85, 0xe6, 0x20, 0x00, 0x00, //0x000008ff jne          LBB0_501
	0x4c, 0x01, 0xda, //0x00000905 addq         %r11, %rdx
	0x49, 0x89, 0xd7, //0x00000908 movq         %rdx, %r15
	//0x0000090b LBB0_102
	0x85, 0xf6, //0x0000090b testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x0000090d je           LBB0_105
	0x0f, 0xbc, 0xd6, //0x00000913 bsfl         %esi, %edx
	0x49, 0x83, 0xf8, 0xff, //0x00000916 cmpq         $-1, %r8
	0x0f, 0x85, 0xcb, 0x20, 0x00, 0x00, //0x0000091a jne          LBB0_501
	0x4c, 0x01, 0xda, //0x00000920 addq         %r11, %rdx
	0x49, 0x89, 0xd0, //0x00000923 movq         %rdx, %r8
	//0x00000926 LBB0_105
	0x85, 0xc0, //0x00000926 testl        %eax, %eax
	0x0f, 0x84, 0x17, 0x00, 0x00, 0x00, //0x00000928 je           LBB0_108
	0x0f, 0xbc, 0xc0, //0x0000092e bsfl         %eax, %eax
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00000931 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x6d, 0x21, 0x00, 0x00, //0x00000937 jne          LBB0_504
	0x4c, 0x01, 0xd8, //0x0000093d addq         %r11, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00000940 movq         %rax, $16(%rsp)
	//0x00000945 LBB0_108
	0x83, 0xf9, 0x20, //0x00000945 cmpl         $32, %ecx
	0x0f, 0x85, 0xab, 0x08, 0x00, 0x00, //0x00000948 jne          LBB0_219
	0x48, 0x83, 0xc3, 0xe0, //0x0000094e addq         $-32, %rbx
	0x49, 0x83, 0xc3, 0x20, //0x00000952 addq         $32, %r11
	0x48, 0x83, 0xfb, 0x1f, //0x00000956 cmpq         $31, %rbx
	0x0f, 0x87, 0xf0, 0xfe, 0xff, 0xff, //0x0000095a ja           LBB0_94
	0xc5, 0xf8, 0x77, //0x00000960 vzeroupper   
	0xc5, 0x7e, 0x6f, 0x3d, 0xf5, 0xf8, 0xff, 0xff, //0x00000963 vmovdqu      $-1803(%rip), %ymm15  /* LCPI0_17+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xcd, 0xf8, 0xff, 0xff, //0x0000096b vmovdqu      $-1843(%rip), %ymm14  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x65, 0xf8, 0xff, 0xff, //0x00000973 vmovdqu      $-1947(%rip), %ymm11  /* LCPI0_10+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x2d, 0x9d, 0xf8, 0xff, 0xff, //0x0000097b vmovdqu      $-1891(%rip), %ymm13  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x75, 0xf8, 0xff, 0xff, //0x00000983 vmovdqu      $-1931(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xcd, 0xf7, 0xff, 0xff, //0x0000098b vmovdqu      $-2099(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x15, 0xa5, 0xf7, 0xff, 0xff, //0x00000993 vmovdqu      $-2139(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x7d, 0xf7, 0xff, 0xff, //0x0000099b vmovdqu      $-2179(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x55, 0xf7, 0xff, 0xff, //0x000009a3 vmovdqu      $-2219(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x2d, 0xf7, 0xff, 0xff, //0x000009ab vmovdqu      $-2259(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0x05, 0xf7, 0xff, 0xff, //0x000009b3 vmovdqu      $-2299(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0x4d, 0x01, 0xd3, //0x000009bb addq         %r10, %r11
	0x48, 0x83, 0xfb, 0x10, //0x000009be cmpq         $16, %rbx
	0x0f, 0x82, 0xa2, 0x01, 0x00, 0x00, //0x000009c2 jb           LBB0_130
	//0x000009c8 LBB0_111
	0x4c, 0x89, 0xd8, //0x000009c8 movq         %r11, %rax
	0x4c, 0x29, 0xd0, //0x000009cb subq         %r10, %rax
	0x48, 0x89, 0x44, 0x24, 0x38, //0x000009ce movq         %rax, $56(%rsp)
	0x45, 0x31, 0xf6, //0x000009d3 xorl         %r14d, %r14d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000009d6 .p2align 4, 0x90
	//0x000009e0 LBB0_112
	0x4c, 0x89, 0x44, 0x24, 0x30, //0x000009e0 movq         %r8, $48(%rsp)
	0x4d, 0x89, 0xd0, //0x000009e5 movq         %r10, %r8
	0xc4, 0x81, 0x7a, 0x6f, 0x04, 0x33, //0x000009e8 vmovdqu      (%r11,%r14), %xmm0
	0xc5, 0xf9, 0x74, 0x0d, 0x4a, 0xf6, 0xff, 0xff, //0x000009ee vpcmpeqb     $-2486(%rip), %xmm0, %xmm1  /* LCPI0_20+0(%rip) */
	0xc5, 0xf9, 0x74, 0x15, 0x52, 0xf6, 0xff, 0xff, //0x000009f6 vpcmpeqb     $-2478(%rip), %xmm0, %xmm2  /* LCPI0_21+0(%rip) */
	0xc5, 0xe9, 0xeb, 0xc9, //0x000009fe vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xf9, 0xfc, 0x15, 0x56, 0xf6, 0xff, 0xff, //0x00000a02 vpaddb       $-2474(%rip), %xmm0, %xmm2  /* LCPI0_22+0(%rip) */
	0xc5, 0xe9, 0xda, 0x1d, 0x5e, 0xf6, 0xff, 0xff, //0x00000a0a vpminub      $-2466(%rip), %xmm2, %xmm3  /* LCPI0_23+0(%rip) */
	0xc5, 0xe9, 0x74, 0xd3, //0x00000a12 vpcmpeqb     %xmm3, %xmm2, %xmm2
	0xc5, 0xf9, 0xdb, 0x1d, 0x02, 0xf6, 0xff, 0xff, //0x00000a16 vpand        $-2558(%rip), %xmm0, %xmm3  /* LCPI0_12+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x5a, 0xf6, 0xff, 0xff, //0x00000a1e vpcmpeqb     $-2470(%rip), %xmm0, %xmm0  /* LCPI0_24+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x62, 0xf6, 0xff, 0xff, //0x00000a26 vpcmpeqb     $-2462(%rip), %xmm3, %xmm3  /* LCPI0_25+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x00000a2e vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xd9, 0xeb, 0xe1, //0x00000a32 vpor         %xmm1, %xmm4, %xmm4
	0xc5, 0xd9, 0xeb, 0xd2, //0x00000a36 vpor         %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xd0, //0x00000a3a vpmovmskb    %xmm0, %edx
	0xc5, 0xf9, 0xd7, 0xfb, //0x00000a3e vpmovmskb    %xmm3, %edi
	0xc5, 0xf9, 0xd7, 0xf1, //0x00000a42 vpmovmskb    %xmm1, %esi
	0xc5, 0xf9, 0xd7, 0xc2, //0x00000a46 vpmovmskb    %xmm2, %eax
	0xf7, 0xd0, //0x00000a4a notl         %eax
	0x0f, 0xbc, 0xc8, //0x00000a4c bsfl         %eax, %ecx
	0x4d, 0x89, 0xca, //0x00000a4f movq         %r9, %r10
	0x83, 0xf9, 0x10, //0x00000a52 cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00000a55 je           LBB0_114
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00000a5b movl         $-1, %eax
	0xd3, 0xe0, //0x00000a60 shll         %cl, %eax
	0xf7, 0xd0, //0x00000a62 notl         %eax
	0x21, 0xc2, //0x00000a64 andl         %eax, %edx
	0x21, 0xc7, //0x00000a66 andl         %eax, %edi
	0x21, 0xf0, //0x00000a68 andl         %esi, %eax
	0x89, 0xc6, //0x00000a6a movl         %eax, %esi
	//0x00000a6c LBB0_114
	0xc5, 0xfe, 0x6f, 0x1d, 0x4c, 0xf7, 0xff, 0xff, //0x00000a6c vmovdqu      $-2228(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x24, 0xf7, 0xff, 0xff, //0x00000a74 vmovdqu      $-2268(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0x44, 0x8d, 0x4a, 0xff, //0x00000a7c leal         $-1(%rdx), %r9d
	0x41, 0x21, 0xd1, //0x00000a80 andl         %edx, %r9d
	0x0f, 0x85, 0x7b, 0x20, 0x00, 0x00, //0x00000a83 jne          LBB0_510
	0x8d, 0x47, 0xff, //0x00000a89 leal         $-1(%rdi), %eax
	0x21, 0xf8, //0x00000a8c andl         %edi, %eax
	0x0f, 0x85, 0x7d, 0x20, 0x00, 0x00, //0x00000a8e jne          LBB0_511
	0x8d, 0x46, 0xff, //0x00000a94 leal         $-1(%rsi), %eax
	0x21, 0xf0, //0x00000a97 andl         %esi, %eax
	0x4d, 0x89, 0xd1, //0x00000a99 movq         %r10, %r9
	0x0f, 0x85, 0x9a, 0x20, 0x00, 0x00, //0x00000a9c jne          LBB0_513
	0x85, 0xd2, //0x00000aa2 testl        %edx, %edx
	0x0f, 0x84, 0x66, 0x00, 0x00, 0x00, //0x00000aa4 je           LBB0_123
	0x0f, 0xbc, 0xd2, //0x00000aaa bsfl         %edx, %edx
	0x49, 0x83, 0xff, 0xff, //0x00000aad cmpq         $-1, %r15
	0x0f, 0x85, 0x50, 0x1f, 0x00, 0x00, //0x00000ab1 jne          LBB0_503
	0x4d, 0x89, 0xc2, //0x00000ab7 movq         %r8, %r10
	0x48, 0x8b, 0x44, 0x24, 0x38, //0x00000aba movq         $56(%rsp), %rax
	0x4c, 0x01, 0xf0, //0x00000abf addq         %r14, %rax
	0x48, 0x01, 0xc2, //0x00000ac2 addq         %rax, %rdx
	0x49, 0x89, 0xd7, //0x00000ac5 movq         %rdx, %r15
	0x85, 0xff, //0x00000ac8 testl        %edi, %edi
	0x4c, 0x8b, 0x44, 0x24, 0x30, //0x00000aca movq         $48(%rsp), %r8
	0x0f, 0x84, 0x4b, 0x00, 0x00, 0x00, //0x00000acf je           LBB0_120
	//0x00000ad5 LBB0_124
	0x0f, 0xbc, 0xd7, //0x00000ad5 bsfl         %edi, %edx
	0x49, 0x83, 0xf8, 0xff, //0x00000ad8 cmpq         $-1, %r8
	0x0f, 0x85, 0x25, 0x1f, 0x00, 0x00, //0x00000adc jne          LBB0_503
	0x48, 0x8b, 0x44, 0x24, 0x38, //0x00000ae2 movq         $56(%rsp), %rax
	0x4c, 0x01, 0xf0, //0x00000ae7 addq         %r14, %rax
	0x48, 0x01, 0xc2, //0x00000aea addq         %rax, %rdx
	0x49, 0x89, 0xd0, //0x00000aed movq         %rdx, %r8
	0x85, 0xf6, //0x00000af0 testl        %esi, %esi
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00000af2 jne          LBB0_126
	//0x00000af8 LBB0_121
	0x83, 0xf9, 0x10, //0x00000af8 cmpl         $16, %ecx
	0x0f, 0x84, 0x4f, 0x00, 0x00, 0x00, //0x00000afb je           LBB0_128
	0xe9, 0x6a, 0x0e, 0x00, 0x00, //0x00000b01 jmp          LBB0_122
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000b06 .p2align 4, 0x90
	//0x00000b10 LBB0_123
	0x4d, 0x89, 0xc2, //0x00000b10 movq         %r8, %r10
	0x85, 0xff, //0x00000b13 testl        %edi, %edi
	0x4c, 0x8b, 0x44, 0x24, 0x30, //0x00000b15 movq         $48(%rsp), %r8
	0x0f, 0x85, 0xb5, 0xff, 0xff, 0xff, //0x00000b1a jne          LBB0_124
	//0x00000b20 .p2align 4, 0x90
	//0x00000b20 LBB0_120
	0x85, 0xf6, //0x00000b20 testl        %esi, %esi
	0x0f, 0x84, 0xd0, 0xff, 0xff, 0xff, //0x00000b22 je           LBB0_121
	//0x00000b28 LBB0_126
	0x0f, 0xbc, 0xd6, //0x00000b28 bsfl         %esi, %edx
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00000b2b cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0xd0, 0x1e, 0x00, 0x00, //0x00000b31 jne          LBB0_503
	0x48, 0x8b, 0x44, 0x24, 0x38, //0x00000b37 movq         $56(%rsp), %rax
	0x4c, 0x01, 0xf0, //0x00000b3c addq         %r14, %rax
	0x48, 0x01, 0xc2, //0x00000b3f addq         %rax, %rdx
	0x48, 0x89, 0x54, 0x24, 0x10, //0x00000b42 movq         %rdx, $16(%rsp)
	0x83, 0xf9, 0x10, //0x00000b47 cmpl         $16, %ecx
	0x0f, 0x85, 0x20, 0x0e, 0x00, 0x00, //0x00000b4a jne          LBB0_122
	//0x00000b50 LBB0_128
	0x48, 0x83, 0xc3, 0xf0, //0x00000b50 addq         $-16, %rbx
	0x49, 0x83, 0xc6, 0x10, //0x00000b54 addq         $16, %r14
	0x48, 0x83, 0xfb, 0x0f, //0x00000b58 cmpq         $15, %rbx
	0x0f, 0x87, 0x7e, 0xfe, 0xff, 0xff, //0x00000b5c ja           LBB0_112
	0x4d, 0x01, 0xf3, //0x00000b62 addq         %r14, %r11
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x00000b65 movq         $40(%rsp), %r14
	//0x00000b6a LBB0_130
	0x48, 0x85, 0xdb, //0x00000b6a testq        %rbx, %rbx
	0x0f, 0x84, 0x27, 0x0e, 0x00, 0x00, //0x00000b6d je           LBB0_346
	0x49, 0x8d, 0x04, 0x1b, //0x00000b73 leaq         (%r11,%rbx), %rax
	0x48, 0x89, 0x44, 0x24, 0x30, //0x00000b77 movq         %rax, $48(%rsp)
	0x4c, 0x89, 0xde, //0x00000b7c movq         %r11, %rsi
	0x4c, 0x29, 0xd6, //0x00000b7f subq         %r10, %rsi
	0x31, 0xc0, //0x00000b82 xorl         %eax, %eax
	0xc5, 0xfe, 0x6f, 0x1d, 0x34, 0xf6, 0xff, 0xff, //0x00000b84 vmovdqu      $-2508(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x0c, 0xf6, 0xff, 0xff, //0x00000b8c vmovdqu      $-2548(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xe9, 0x24, 0x00, 0x00, 0x00, //0x00000b94 jmp          LBB0_135
	//0x00000b99 LBB0_132
	0x49, 0x83, 0xff, 0xff, //0x00000b99 cmpq         $-1, %r15
	0x0f, 0x85, 0x30, 0x1e, 0x00, 0x00, //0x00000b9d jne          LBB0_500
	0x4c, 0x8d, 0x3c, 0x06, //0x00000ba3 leaq         (%rsi,%rax), %r15
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000ba7 .p2align 4, 0x90
	//0x00000bb0 LBB0_134
	0x48, 0x83, 0xc0, 0x01, //0x00000bb0 addq         $1, %rax
	0x48, 0x39, 0xc3, //0x00000bb4 cmpq         %rax, %rbx
	0x0f, 0x84, 0xfe, 0x19, 0x00, 0x00, //0x00000bb7 je           LBB0_471
	//0x00000bbd LBB0_135
	0x41, 0x0f, 0xbe, 0x14, 0x03, //0x00000bbd movsbl       (%r11,%rax), %edx
	0x8d, 0x7a, 0xd0, //0x00000bc2 leal         $-48(%rdx), %edi
	0x83, 0xff, 0x0a, //0x00000bc5 cmpl         $10, %edi
	0x0f, 0x82, 0xe2, 0xff, 0xff, 0xff, //0x00000bc8 jb           LBB0_134
	0x8d, 0x7a, 0xd5, //0x00000bce leal         $-43(%rdx), %edi
	0x83, 0xff, 0x1a, //0x00000bd1 cmpl         $26, %edi
	0x0f, 0x87, 0x2a, 0x00, 0x00, 0x00, //0x00000bd4 ja           LBB0_140
	0x48, 0x8d, 0x0d, 0x63, 0x37, 0x00, 0x00, //0x00000bda leaq         $14179(%rip), %rcx  /* LJTI0_3+0(%rip) */
	0x48, 0x63, 0x14, 0xb9, //0x00000be1 movslq       (%rcx,%rdi,4), %rdx
	0x48, 0x01, 0xca, //0x00000be5 addq         %rcx, %rdx
	0xff, 0xe2, //0x00000be8 jmpq         *%rdx
	//0x00000bea LBB0_138
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00000bea cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0xdd, 0x1d, 0x00, 0x00, //0x00000bf0 jne          LBB0_500
	0x48, 0x8d, 0x0c, 0x06, //0x00000bf6 leaq         (%rsi,%rax), %rcx
	0x48, 0x89, 0x4c, 0x24, 0x10, //0x00000bfa movq         %rcx, $16(%rsp)
	0xe9, 0xac, 0xff, 0xff, 0xff, //0x00000bff jmp          LBB0_134
	//0x00000c04 LBB0_140
	0x83, 0xfa, 0x65, //0x00000c04 cmpl         $101, %edx
	0x0f, 0x85, 0x8a, 0x0d, 0x00, 0x00, //0x00000c07 jne          LBB0_345
	//0x00000c0d LBB0_141
	0x49, 0x83, 0xf8, 0xff, //0x00000c0d cmpq         $-1, %r8
	0x0f, 0x85, 0xbc, 0x1d, 0x00, 0x00, //0x00000c11 jne          LBB0_500
	0x4c, 0x8d, 0x04, 0x06, //0x00000c17 leaq         (%rsi,%rax), %r8
	0xe9, 0x90, 0xff, 0xff, 0xff, //0x00000c1b jmp          LBB0_134
	//0x00000c20 LBB0_143
	0x49, 0x81, 0xfa, 0xff, 0x0f, 0x00, 0x00, //0x00000c20 cmpq         $4095, %r10
	0x0f, 0x8f, 0x78, 0x31, 0x00, 0x00, //0x00000c27 jg           LBB0_713
	0x49, 0x8d, 0x42, 0x01, //0x00000c2d leaq         $1(%r10), %rax
	0x49, 0x89, 0x06, //0x00000c31 movq         %rax, (%r14)
	0x4b, 0xc7, 0x44, 0xd6, 0x08, 0x00, 0x00, 0x00, 0x00, //0x00000c34 movq         $0, $8(%r14,%r10,8)
	0xe9, 0x5f, 0xf7, 0xff, 0xff, //0x00000c3d jmp          LBB0_4
	//0x00000c42 LBB0_70
	0x4b, 0xc7, 0x04, 0xd6, 0x02, 0x00, 0x00, 0x00, //0x00000c42 movq         $2, (%r14,%r10,8)
	0x4d, 0x8b, 0x73, 0x08, //0x00000c4a movq         $8(%r11), %r14
	0x41, 0xf6, 0xc0, 0x40, //0x00000c4e testb        $64, %r8b
	0x0f, 0x85, 0x49, 0x03, 0x00, 0x00, //0x00000c52 jne          LBB0_183
	0x41, 0xf6, 0xc0, 0x20, //0x00000c58 testb        $32, %r8b
	0x0f, 0x85, 0xbd, 0x05, 0x00, 0x00, //0x00000c5c jne          LBB0_220
	0x4d, 0x89, 0xf2, //0x00000c62 movq         %r14, %r10
	0x49, 0x29, 0xca, //0x00000c65 subq         %rcx, %r10
	0x0f, 0x84, 0x35, 0x33, 0x00, 0x00, //0x00000c68 je           LBB0_724
	0x49, 0x83, 0xfa, 0x40, //0x00000c6e cmpq         $64, %r10
	0x0f, 0x82, 0x18, 0x21, 0x00, 0x00, //0x00000c72 jb           LBB0_538
	0x48, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x00000c78 movq         $-2, %rsi
	0x4c, 0x29, 0xee, //0x00000c7f subq         %r13, %rsi
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00000c82 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc0, //0x00000c8b xorl         %r8d, %r8d
	0x90, 0x90, //0x00000c8e .p2align 4, 0x90
	//0x00000c90 LBB0_75
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x0c, //0x00000c90 vmovdqu      (%r12,%rcx), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x0c, 0x20, //0x00000c96 vmovdqu      $32(%r12,%rcx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x00000c9d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00000ca1 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00000ca5 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00000ca9 vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x00000cad vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00000cb1 vpmovmskb    %ymm0, %edx
	0xc5, 0xf5, 0x74, 0xc7, //0x00000cb5 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x00000cb9 vpmovmskb    %ymm0, %ebx
	0x48, 0xc1, 0xe0, 0x20, //0x00000cbd shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00000cc1 orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x20, //0x00000cc4 shlq         $32, %rbx
	0x48, 0x09, 0xda, //0x00000cc8 orq          %rbx, %rdx
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00000ccb jne          LBB0_147
	0x4d, 0x85, 0xc0, //0x00000cd1 testq        %r8, %r8
	0x0f, 0x85, 0x3f, 0x00, 0x00, 0x00, //0x00000cd4 jne          LBB0_149
	0x45, 0x31, 0xc0, //0x00000cda xorl         %r8d, %r8d
	0x48, 0x85, 0xff, //0x00000cdd testq        %rdi, %rdi
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00000ce0 jne          LBB0_150
	//0x00000ce6 LBB0_78
	0x49, 0x83, 0xc2, 0xc0, //0x00000ce6 addq         $-64, %r10
	0x48, 0x83, 0xc6, 0xc0, //0x00000cea addq         $-64, %rsi
	0x48, 0x83, 0xc1, 0x40, //0x00000cee addq         $64, %rcx
	0x49, 0x83, 0xfa, 0x3f, //0x00000cf2 cmpq         $63, %r10
	0x0f, 0x87, 0x94, 0xff, 0xff, 0xff, //0x00000cf6 ja           LBB0_75
	0xe9, 0x11, 0x1d, 0x00, 0x00, //0x00000cfc jmp          LBB0_79
	//0x00000d01 LBB0_147
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00000d01 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x00000d07 jne          LBB0_149
	0x48, 0x0f, 0xbc, 0xc2, //0x00000d0d bsfq         %rdx, %rax
	0x48, 0x01, 0xc8, //0x00000d11 addq         %rcx, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00000d14 movq         %rax, $16(%rsp)
	//0x00000d19 LBB0_149
	0x4c, 0x89, 0xc0, //0x00000d19 movq         %r8, %rax
	0x48, 0xf7, 0xd0, //0x00000d1c notq         %rax
	0x48, 0x21, 0xd0, //0x00000d1f andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x00000d22 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xc3, //0x00000d26 orq          %r8, %r11
	0x4c, 0x89, 0xdb, //0x00000d29 movq         %r11, %rbx
	0x48, 0xf7, 0xd3, //0x00000d2c notq         %rbx
	0x48, 0x21, 0xd3, //0x00000d2f andq         %rdx, %rbx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000d32 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd3, //0x00000d3c andq         %rdx, %rbx
	0x45, 0x31, 0xc0, //0x00000d3f xorl         %r8d, %r8d
	0x48, 0x01, 0xc3, //0x00000d42 addq         %rax, %rbx
	0x41, 0x0f, 0x92, 0xc0, //0x00000d45 setb         %r8b
	0x48, 0x01, 0xdb, //0x00000d49 addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000d4c movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x00000d56 xorq         %rax, %rbx
	0x4c, 0x21, 0xdb, //0x00000d59 andq         %r11, %rbx
	0x48, 0xf7, 0xd3, //0x00000d5c notq         %rbx
	0x48, 0x21, 0xdf, //0x00000d5f andq         %rbx, %rdi
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00000d62 movq         $32(%rsp), %r11
	0x48, 0x85, 0xff, //0x00000d67 testq        %rdi, %rdi
	0x0f, 0x84, 0x76, 0xff, 0xff, 0xff, //0x00000d6a je           LBB0_78
	//0x00000d70 LBB0_150
	0x48, 0x0f, 0xbc, 0xcf, //0x00000d70 bsfq         %rdi, %rcx
	0x48, 0x29, 0xf1, //0x00000d74 subq         %rsi, %rcx
	//0x00000d77 LBB0_151
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00000d77 movq         $24(%rsp), %r8
	//0x00000d7c LBB0_152
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x00000d7c movq         $8(%rsp), %rbx
	0x48, 0x85, 0xc9, //0x00000d81 testq        %rcx, %rcx
	0x0f, 0x89, 0xb9, 0x0d, 0x00, 0x00, //0x00000d84 jns          LBB0_363
	0xe9, 0x22, 0x30, 0x00, 0x00, //0x00000d8a jmp          LBB0_696
	//0x00000d8f LBB0_153
	0x4d, 0x89, 0xf2, //0x00000d8f movq         %r14, %r10
	0x49, 0x29, 0xca, //0x00000d92 subq         %rcx, %r10
	0x49, 0x83, 0xfa, 0x20, //0x00000d95 cmpq         $32, %r10
	0x0f, 0x8c, 0x9e, 0x1e, 0x00, 0x00, //0x00000d99 jl           LBB0_520
	0x4d, 0x89, 0xcf, //0x00000d9f movq         %r9, %r15
	0x4f, 0x8d, 0x04, 0x2c, //0x00000da2 leaq         (%r12,%r13), %r8
	0x4d, 0x29, 0xee, //0x00000da6 subq         %r13, %r14
	0xbe, 0x1f, 0x00, 0x00, 0x00, //0x00000da9 movl         $31, %esi
	0x45, 0x31, 0xd2, //0x00000dae xorl         %r10d, %r10d
	0x45, 0x31, 0xdb, //0x00000db1 xorl         %r11d, %r11d
	0xe9, 0x5b, 0x00, 0x00, 0x00, //0x00000db4 jmp          LBB0_155
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000db9 .p2align 4, 0x90
	//0x00000dc0 LBB0_160
	0x44, 0x89, 0xdf, //0x00000dc0 movl         %r11d, %edi
	0xf7, 0xd7, //0x00000dc3 notl         %edi
	0x21, 0xcf, //0x00000dc5 andl         %ecx, %edi
	0x8d, 0x14, 0x3f, //0x00000dc7 leal         (%rdi,%rdi), %edx
	0x44, 0x09, 0xda, //0x00000dca orl          %r11d, %edx
	0x89, 0xd3, //0x00000dcd movl         %edx, %ebx
	0xf7, 0xd3, //0x00000dcf notl         %ebx
	0x21, 0xcb, //0x00000dd1 andl         %ecx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000dd3 andl         $-1431655766, %ebx
	0x45, 0x31, 0xdb, //0x00000dd9 xorl         %r11d, %r11d
	0x01, 0xfb, //0x00000ddc addl         %edi, %ebx
	0x41, 0x0f, 0x92, 0xc3, //0x00000dde setb         %r11b
	0x01, 0xdb, //0x00000de2 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00000de4 xorl         $1431655765, %ebx
	0x21, 0xd3, //0x00000dea andl         %edx, %ebx
	0xf7, 0xd3, //0x00000dec notl         %ebx
	0x41, 0x21, 0xd9, //0x00000dee andl         %ebx, %r9d
	0x4d, 0x85, 0xc9, //0x00000df1 testq        %r9, %r9
	0x0f, 0x85, 0x4e, 0x00, 0x00, 0x00, //0x00000df4 jne          LBB0_158
	//0x00000dfa LBB0_161
	0x49, 0x83, 0xc2, 0x20, //0x00000dfa addq         $32, %r10
	0x49, 0x8d, 0x0c, 0x36, //0x00000dfe leaq         (%r14,%rsi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00000e02 addq         $-32, %rcx
	0x48, 0x83, 0xc6, 0xe0, //0x00000e06 addq         $-32, %rsi
	0x48, 0x83, 0xf9, 0x3f, //0x00000e0a cmpq         $63, %rcx
	0x0f, 0x8e, 0x28, 0x18, 0x00, 0x00, //0x00000e0e jle          LBB0_162
	//0x00000e14 LBB0_155
	0xc4, 0x81, 0x7e, 0x6f, 0x44, 0x10, 0x01, //0x00000e14 vmovdqu      $1(%r8,%r10), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00000e1b vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xc9, //0x00000e1f vpmovmskb    %ymm1, %r9d
	0xc5, 0xfd, 0x74, 0xc7, //0x00000e23 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x00000e27 vpmovmskb    %ymm0, %ecx
	0x85, 0xc9, //0x00000e2b testl        %ecx, %ecx
	0x0f, 0x85, 0x8d, 0xff, 0xff, 0xff, //0x00000e2d jne          LBB0_160
	0x4d, 0x85, 0xdb, //0x00000e33 testq        %r11, %r11
	0x0f, 0x85, 0x84, 0xff, 0xff, 0xff, //0x00000e36 jne          LBB0_160
	0x45, 0x31, 0xdb, //0x00000e3c xorl         %r11d, %r11d
	0x4d, 0x85, 0xc9, //0x00000e3f testq        %r9, %r9
	0x0f, 0x84, 0xb2, 0xff, 0xff, 0xff, //0x00000e42 je           LBB0_161
	//0x00000e48 LBB0_158
	0x41, 0x0f, 0xbc, 0xc1, //0x00000e48 bsfl         %r9d, %eax
	0x4c, 0x01, 0xe8, //0x00000e4c addq         %r13, %rax
	0x49, 0x8d, 0x0c, 0x02, //0x00000e4f leaq         (%r10,%rax), %rcx
	0x48, 0x83, 0xc1, 0x02, //0x00000e53 addq         $2, %rcx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00000e57 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00000e5c movq         $32(%rsp), %r11
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x00000e61 movq         $40(%rsp), %r14
	0x4d, 0x89, 0xf9, //0x00000e66 movq         %r15, %r9
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000e69 movabsq      $4294977024, %r15
	//0x00000e73 LBB0_159
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x00000e73 movq         $8(%rsp), %rax
	0x48, 0x89, 0x08, //0x00000e78 movq         %rcx, (%rax)
	0xe9, 0x0b, 0xf5, 0xff, 0xff, //0x00000e7b jmp          LBB0_3
	//0x00000e80 LBB0_165
	0x48, 0x89, 0xd8, //0x00000e80 movq         %rbx, %rax
	0x48, 0x29, 0xc8, //0x00000e83 subq         %rcx, %rax
	0x48, 0x83, 0xf8, 0x20, //0x00000e86 cmpq         $32, %rax
	0x48, 0x8b, 0x7c, 0x24, 0x08, //0x00000e8a movq         $8(%rsp), %rdi
	0x0f, 0x82, 0xb0, 0x1d, 0x00, 0x00, //0x00000e8f jb           LBB0_521
	0x4c, 0x89, 0xe8, //0x00000e95 movq         %r13, %rax
	0x48, 0xf7, 0xd0, //0x00000e98 notq         %rax
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00000e9b .p2align 4, 0x90
	//0x00000ea0 LBB0_167
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x0c, //0x00000ea0 vmovdqu      (%r12,%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0xcb, //0x00000ea6 vpcmpeqb     %ymm3, %ymm0, %ymm1
	0xc5, 0xa5, 0xdb, 0xc0, //0x00000eaa vpand        %ymm0, %ymm11, %ymm0
	0xc5, 0xfd, 0x74, 0xc4, //0x00000eae vpcmpeqb     %ymm4, %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x00000eb2 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00000eb6 vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x00000eba testl        %edx, %edx
	0x0f, 0x85, 0xd1, 0x00, 0x00, 0x00, //0x00000ebc jne          LBB0_181
	0x48, 0x83, 0xc1, 0x20, //0x00000ec2 addq         $32, %rcx
	0x48, 0x8d, 0x14, 0x03, //0x00000ec6 leaq         (%rbx,%rax), %rdx
	0x48, 0x83, 0xc2, 0xe0, //0x00000eca addq         $-32, %rdx
	0x48, 0x83, 0xc0, 0xe0, //0x00000ece addq         $-32, %rax
	0x48, 0x83, 0xfa, 0x1f, //0x00000ed2 cmpq         $31, %rdx
	0x0f, 0x87, 0xc4, 0xff, 0xff, 0xff, //0x00000ed6 ja           LBB0_167
	0x4c, 0x89, 0xe1, //0x00000edc movq         %r12, %rcx
	0x48, 0x29, 0xc1, //0x00000edf subq         %rax, %rcx
	0x48, 0x01, 0xc3, //0x00000ee2 addq         %rax, %rbx
	0x48, 0x89, 0xd8, //0x00000ee5 movq         %rbx, %rax
	0x48, 0x83, 0xf8, 0x10, //0x00000ee8 cmpq         $16, %rax
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x00000eec jb           LBB0_173
	//0x00000ef2 LBB0_170
	0x4c, 0x89, 0xe2, //0x00000ef2 movq         %r12, %rdx
	0x48, 0x29, 0xca, //0x00000ef5 subq         %rcx, %rdx
	//0x00000ef8 LBB0_171
	0xc5, 0xfa, 0x6f, 0x01, //0x00000ef8 vmovdqu      (%rcx), %xmm0
	0xc5, 0xf9, 0x74, 0x0d, 0x0c, 0xf1, 0xff, 0xff, //0x00000efc vpcmpeqb     $-3828(%rip), %xmm0, %xmm1  /* LCPI0_11+0(%rip) */
	0xc5, 0xf9, 0xdb, 0x05, 0x14, 0xf1, 0xff, 0xff, //0x00000f04 vpand        $-3820(%rip), %xmm0, %xmm0  /* LCPI0_12+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x1c, 0xf1, 0xff, 0xff, //0x00000f0c vpcmpeqb     $-3812(%rip), %xmm0, %xmm0  /* LCPI0_13+0(%rip) */
	0xc5, 0xf9, 0xeb, 0xc1, //0x00000f14 vpor         %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xd7, 0xf0, //0x00000f18 vpmovmskb    %xmm0, %esi
	0x85, 0xf6, //0x00000f1c testl        %esi, %esi
	0x0f, 0x85, 0x09, 0x17, 0x00, 0x00, //0x00000f1e jne          LBB0_477
	0x48, 0x83, 0xc1, 0x10, //0x00000f24 addq         $16, %rcx
	0x48, 0x83, 0xc0, 0xf0, //0x00000f28 addq         $-16, %rax
	0x48, 0x83, 0xc2, 0xf0, //0x00000f2c addq         $-16, %rdx
	0x48, 0x83, 0xf8, 0x0f, //0x00000f30 cmpq         $15, %rax
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x00000f34 ja           LBB0_171
	//0x00000f3a LBB0_173
	0xc5, 0x7d, 0x7f, 0xe1, //0x00000f3a vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00000f3e vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00000f42 vmovdqa      %ymm13, %ymm10
	0x4c, 0x89, 0xfe, //0x00000f47 movq         %r15, %rsi
	0x49, 0x89, 0xff, //0x00000f4a movq         %rdi, %r15
	0x48, 0x85, 0xc0, //0x00000f4d testq        %rax, %rax
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00000f50 je           LBB0_180
	0x48, 0x8d, 0x3c, 0x01, //0x00000f56 leaq         (%rcx,%rax), %rdi
	0x31, 0xd2, //0x00000f5a xorl         %edx, %edx
	//0x00000f5c LBB0_175
	0x0f, 0xb6, 0x1c, 0x11, //0x00000f5c movzbl       (%rcx,%rdx), %ebx
	0x80, 0xfb, 0x2c, //0x00000f60 cmpb         $44, %bl
	0x0f, 0x84, 0xef, 0x1d, 0x00, 0x00, //0x00000f63 je           LBB0_535
	0x80, 0xfb, 0x7d, //0x00000f69 cmpb         $125, %bl
	0x0f, 0x84, 0xe6, 0x1d, 0x00, 0x00, //0x00000f6c je           LBB0_535
	0x80, 0xfb, 0x5d, //0x00000f72 cmpb         $93, %bl
	0x0f, 0x84, 0xdd, 0x1d, 0x00, 0x00, //0x00000f75 je           LBB0_535
	0x48, 0x83, 0xc2, 0x01, //0x00000f7b addq         $1, %rdx
	0x48, 0x39, 0xd0, //0x00000f7f cmpq         %rdx, %rax
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00000f82 jne          LBB0_175
	0x48, 0x89, 0xf9, //0x00000f88 movq         %rdi, %rcx
	//0x00000f8b LBB0_180
	0x4c, 0x29, 0xe1, //0x00000f8b subq         %r12, %rcx
	0xe9, 0xcb, 0x1d, 0x00, 0x00, //0x00000f8e jmp          LBB0_536
	//0x00000f93 LBB0_181
	0x0f, 0xbc, 0xca, //0x00000f93 bsfl         %edx, %ecx
	0x48, 0x29, 0xc1, //0x00000f96 subq         %rax, %rcx
	//0x00000f99 LBB0_182
	0x48, 0x89, 0x0f, //0x00000f99 movq         %rcx, (%rdi)
	0xe9, 0xea, 0xf3, 0xff, 0xff, //0x00000f9c jmp          LBB0_3
	//0x00000fa1 LBB0_183
	0x4d, 0x89, 0xf2, //0x00000fa1 movq         %r14, %r10
	0x49, 0x29, 0xca, //0x00000fa4 subq         %rcx, %r10
	0x49, 0x83, 0xfa, 0x20, //0x00000fa7 cmpq         $32, %r10
	0x0f, 0x8c, 0x5e, 0x1d, 0x00, 0x00, //0x00000fab jl           LBB0_531
	0x4d, 0x89, 0xcf, //0x00000fb1 movq         %r9, %r15
	0x4f, 0x8d, 0x04, 0x2c, //0x00000fb4 leaq         (%r12,%r13), %r8
	0x4d, 0x29, 0xee, //0x00000fb8 subq         %r13, %r14
	0xbe, 0x1f, 0x00, 0x00, 0x00, //0x00000fbb movl         $31, %esi
	0x45, 0x31, 0xd2, //0x00000fc0 xorl         %r10d, %r10d
	0x45, 0x31, 0xdb, //0x00000fc3 xorl         %r11d, %r11d
	0xe9, 0x59, 0x00, 0x00, 0x00, //0x00000fc6 jmp          LBB0_185
	0x90, 0x90, 0x90, 0x90, 0x90, //0x00000fcb .p2align 4, 0x90
	//0x00000fd0 LBB0_190
	0x44, 0x89, 0xdf, //0x00000fd0 movl         %r11d, %edi
	0xf7, 0xd7, //0x00000fd3 notl         %edi
	0x21, 0xcf, //0x00000fd5 andl         %ecx, %edi
	0x8d, 0x14, 0x3f, //0x00000fd7 leal         (%rdi,%rdi), %edx
	0x44, 0x09, 0xda, //0x00000fda orl          %r11d, %edx
	0x89, 0xd3, //0x00000fdd movl         %edx, %ebx
	0xf7, 0xd3, //0x00000fdf notl         %ebx
	0x21, 0xcb, //0x00000fe1 andl         %ecx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000fe3 andl         $-1431655766, %ebx
	0x45, 0x31, 0xdb, //0x00000fe9 xorl         %r11d, %r11d
	0x01, 0xfb, //0x00000fec addl         %edi, %ebx
	0x41, 0x0f, 0x92, 0xc3, //0x00000fee setb         %r11b
	0x01, 0xdb, //0x00000ff2 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00000ff4 xorl         $1431655765, %ebx
	0x21, 0xd3, //0x00000ffa andl         %edx, %ebx
	0xf7, 0xd3, //0x00000ffc notl         %ebx
	0x41, 0x21, 0xd9, //0x00000ffe andl         %ebx, %r9d
	0x4d, 0x85, 0xc9, //0x00001001 testq        %r9, %r9
	0x0f, 0x85, 0x4e, 0x00, 0x00, 0x00, //0x00001004 jne          LBB0_188
	//0x0000100a LBB0_191
	0x49, 0x83, 0xc2, 0x20, //0x0000100a addq         $32, %r10
	0x49, 0x8d, 0x0c, 0x36, //0x0000100e leaq         (%r14,%rsi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00001012 addq         $-32, %rcx
	0x48, 0x83, 0xc6, 0xe0, //0x00001016 addq         $-32, %rsi
	0x48, 0x83, 0xf9, 0x3f, //0x0000101a cmpq         $63, %rcx
	0x0f, 0x8e, 0x5b, 0x18, 0x00, 0x00, //0x0000101e jle          LBB0_192
	//0x00001024 LBB0_185
	0xc4, 0x81, 0x7e, 0x6f, 0x44, 0x10, 0x01, //0x00001024 vmovdqu      $1(%r8,%r10), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x0000102b vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xc9, //0x0000102f vpmovmskb    %ymm1, %r9d
	0xc5, 0xfd, 0x74, 0xc7, //0x00001033 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x00001037 vpmovmskb    %ymm0, %ecx
	0x85, 0xc9, //0x0000103b testl        %ecx, %ecx
	0x0f, 0x85, 0x8d, 0xff, 0xff, 0xff, //0x0000103d jne          LBB0_190
	0x4d, 0x85, 0xdb, //0x00001043 testq        %r11, %r11
	0x0f, 0x85, 0x84, 0xff, 0xff, 0xff, //0x00001046 jne          LBB0_190
	0x45, 0x31, 0xdb, //0x0000104c xorl         %r11d, %r11d
	0x4d, 0x85, 0xc9, //0x0000104f testq        %r9, %r9
	0x0f, 0x84, 0xb2, 0xff, 0xff, 0xff, //0x00001052 je           LBB0_191
	//0x00001058 LBB0_188
	0x41, 0x0f, 0xbc, 0xc1, //0x00001058 bsfl         %r9d, %eax
	0x4c, 0x01, 0xe8, //0x0000105c addq         %r13, %rax
	0x49, 0x8d, 0x0c, 0x02, //0x0000105f leaq         (%r10,%rax), %rcx
	0x48, 0x83, 0xc1, 0x02, //0x00001063 addq         $2, %rcx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00001067 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000106c movq         $32(%rsp), %r11
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x00001071 movq         $40(%rsp), %r14
	0x4d, 0x89, 0xf9, //0x00001076 movq         %r15, %r9
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001079 movabsq      $4294977024, %r15
	//0x00001083 LBB0_189
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x00001083 movq         $8(%rsp), %rax
	0x48, 0x89, 0x08, //0x00001088 movq         %rcx, (%rax)
	0x4c, 0x89, 0xe8, //0x0000108b movq         %r13, %rax
	0x48, 0xba, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x0000108e movabsq      $9223372036854775806, %rdx
	0x49, 0x39, 0xd5, //0x00001098 cmpq         %rdx, %r13
	0x0f, 0x86, 0xc0, 0x0a, 0x00, 0x00, //0x0000109b jbe          LBB0_364
	0xe9, 0x57, 0x2d, 0x00, 0x00, //0x000010a1 jmp          LBB0_721
	//0x000010a6 LBB0_195
	0x4d, 0x89, 0xf3, //0x000010a6 movq         %r14, %r11
	0x49, 0x29, 0xcb, //0x000010a9 subq         %rcx, %r11
	0x0f, 0x84, 0xf1, 0x2e, 0x00, 0x00, //0x000010ac je           LBB0_724
	0x49, 0x83, 0xfb, 0x40, //0x000010b2 cmpq         $64, %r11
	0x0f, 0x82, 0x79, 0x1c, 0x00, 0x00, //0x000010b6 jb           LBB0_533
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x000010bc movq         $-1, $16(%rsp)
	0x45, 0x31, 0xd2, //0x000010c5 xorl         %r10d, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000010c8 .p2align 4, 0x90
	//0x000010d0 LBB0_198
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x0c, //0x000010d0 vmovdqu      (%r12,%rcx), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x0c, 0x20, //0x000010d6 vmovdqu      $32(%r12,%rcx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x000010dd vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x000010e1 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x000010e5 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x000010e9 vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xd7, //0x000010ed vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x000010f1 vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd7, //0x000010f5 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xda, //0x000010f9 vpmovmskb    %ymm2, %ebx
	0xc5, 0xbd, 0xda, 0xd1, //0x000010fd vpminub      %ymm1, %ymm8, %ymm2
	0xc5, 0xf5, 0x74, 0xca, //0x00001101 vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001105 vpmovmskb    %ymm1, %esi
	0x48, 0xc1, 0xe0, 0x20, //0x00001109 shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x0000110d orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x20, //0x00001110 shlq         $32, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x00001114 shlq         $32, %rsi
	0x48, 0x09, 0xda, //0x00001118 orq          %rbx, %rdx
	0x0f, 0x85, 0x44, 0x00, 0x00, 0x00, //0x0000111b jne          LBB0_209
	0x4d, 0x85, 0xd2, //0x00001121 testq        %r10, %r10
	0x0f, 0x85, 0x53, 0x00, 0x00, 0x00, //0x00001124 jne          LBB0_211
	0x45, 0x31, 0xd2, //0x0000112a xorl         %r10d, %r10d
	//0x0000112d LBB0_201
	0xc5, 0xbd, 0xda, 0xc8, //0x0000112d vpminub      %ymm0, %ymm8, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x00001131 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x00001135 vpmovmskb    %ymm0, %eax
	0x48, 0x09, 0xc6, //0x00001139 orq          %rax, %rsi
	0x48, 0x85, 0xff, //0x0000113c testq        %rdi, %rdi
	0x0f, 0x85, 0x8b, 0x00, 0x00, 0x00, //0x0000113f jne          LBB0_212
	0x48, 0x85, 0xf6, //0x00001145 testq        %rsi, %rsi
	0x0f, 0x85, 0xce, 0x2c, 0x00, 0x00, //0x00001148 jne          LBB0_704
	0x49, 0x83, 0xc3, 0xc0, //0x0000114e addq         $-64, %r11
	0x48, 0x83, 0xc1, 0x40, //0x00001152 addq         $64, %rcx
	0x49, 0x83, 0xfb, 0x3f, //0x00001156 cmpq         $63, %r11
	0x0f, 0x87, 0x70, 0xff, 0xff, 0xff, //0x0000115a ja           LBB0_198
	0xe9, 0x0e, 0x18, 0x00, 0x00, //0x00001160 jmp          LBB0_204
	//0x00001165 LBB0_209
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00001165 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x0000116b jne          LBB0_211
	0x48, 0x0f, 0xbc, 0xc2, //0x00001171 bsfq         %rdx, %rax
	0x48, 0x01, 0xc8, //0x00001175 addq         %rcx, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00001178 movq         %rax, $16(%rsp)
	//0x0000117d LBB0_211
	0x4c, 0x89, 0xd0, //0x0000117d movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00001180 notq         %rax
	0x48, 0x21, 0xd0, //0x00001183 andq         %rdx, %rax
	0x4c, 0x8d, 0x04, 0x00, //0x00001186 leaq         (%rax,%rax), %r8
	0x4d, 0x09, 0xd0, //0x0000118a orq          %r10, %r8
	0x4c, 0x89, 0xc3, //0x0000118d movq         %r8, %rbx
	0x48, 0xf7, 0xd3, //0x00001190 notq         %rbx
	0x48, 0x21, 0xd3, //0x00001193 andq         %rdx, %rbx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001196 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd3, //0x000011a0 andq         %rdx, %rbx
	0x45, 0x31, 0xd2, //0x000011a3 xorl         %r10d, %r10d
	0x48, 0x01, 0xc3, //0x000011a6 addq         %rax, %rbx
	0x41, 0x0f, 0x92, 0xc2, //0x000011a9 setb         %r10b
	0x48, 0x01, 0xdb, //0x000011ad addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000011b0 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x000011ba xorq         %rax, %rbx
	0x4c, 0x21, 0xc3, //0x000011bd andq         %r8, %rbx
	0x48, 0xf7, 0xd3, //0x000011c0 notq         %rbx
	0x48, 0x21, 0xdf, //0x000011c3 andq         %rbx, %rdi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000011c6 movq         $24(%rsp), %r8
	0xe9, 0x5d, 0xff, 0xff, 0xff, //0x000011cb jmp          LBB0_201
	//0x000011d0 LBB0_212
	0x48, 0x0f, 0xbc, 0xc7, //0x000011d0 bsfq         %rdi, %rax
	0x48, 0x85, 0xf6, //0x000011d4 testq        %rsi, %rsi
	0x0f, 0x84, 0x9c, 0x01, 0x00, 0x00, //0x000011d7 je           LBB0_245
	0x48, 0x0f, 0xbc, 0xd6, //0x000011dd bsfq         %rsi, %rdx
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000011e1 movq         $32(%rsp), %r11
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x000011e6 movq         $8(%rsp), %rbx
	0x48, 0x39, 0xc2, //0x000011eb cmpq         %rax, %rdx
	0x0f, 0x83, 0x9d, 0x01, 0x00, 0x00, //0x000011ee jae          LBB0_246
	0xe9, 0xda, 0x2d, 0x00, 0x00, //0x000011f4 jmp          LBB0_239
	//0x000011f9 LBB0_219
	0x4c, 0x01, 0xd9, //0x000011f9 addq         %r11, %rcx
	0x4c, 0x01, 0xd1, //0x000011fc addq         %r10, %rcx
	0xc5, 0xf8, 0x77, //0x000011ff vzeroupper   
	0x49, 0x89, 0xcb, //0x00001202 movq         %rcx, %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00001205 movq         $-1, %rax
	0x4d, 0x85, 0xff, //0x0000120c testq        %r15, %r15
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x0000120f movq         $16(%rsp), %rcx
	0x0f, 0x85, 0x95, 0x07, 0x00, 0x00, //0x00001214 jne          LBB0_347
	0xe9, 0xc5, 0x2b, 0x00, 0x00, //0x0000121a jmp          LBB0_700
	//0x0000121f LBB0_220
	0x4d, 0x89, 0xf3, //0x0000121f movq         %r14, %r11
	0x49, 0x29, 0xcb, //0x00001222 subq         %rcx, %r11
	0x0f, 0x84, 0x78, 0x2d, 0x00, 0x00, //0x00001225 je           LBB0_724
	0x49, 0x83, 0xfb, 0x40, //0x0000122b cmpq         $64, %r11
	0x0f, 0x82, 0x79, 0x1b, 0x00, 0x00, //0x0000122f jb           LBB0_539
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00001235 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xd2, //0x0000123e xorl         %r10d, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001241 .p2align 4, 0x90
	//0x00001250 LBB0_223
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x0c, //0x00001250 vmovdqu      (%r12,%rcx), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x0c, 0x20, //0x00001256 vmovdqu      $32(%r12,%rcx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x0000125d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00001261 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00001265 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00001269 vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xd7, //0x0000126d vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001271 vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd7, //0x00001275 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xda, //0x00001279 vpmovmskb    %ymm2, %ebx
	0xc5, 0xbd, 0xda, 0xd1, //0x0000127d vpminub      %ymm1, %ymm8, %ymm2
	0xc5, 0xf5, 0x74, 0xca, //0x00001281 vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00001285 vpmovmskb    %ymm1, %esi
	0x48, 0xc1, 0xe0, 0x20, //0x00001289 shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x0000128d orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x20, //0x00001290 shlq         $32, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x00001294 shlq         $32, %rsi
	0x48, 0x09, 0xda, //0x00001298 orq          %rbx, %rdx
	0x0f, 0x85, 0x44, 0x00, 0x00, 0x00, //0x0000129b jne          LBB0_234
	0x4d, 0x85, 0xd2, //0x000012a1 testq        %r10, %r10
	0x0f, 0x85, 0x53, 0x00, 0x00, 0x00, //0x000012a4 jne          LBB0_236
	0x45, 0x31, 0xd2, //0x000012aa xorl         %r10d, %r10d
	//0x000012ad LBB0_226
	0xc5, 0xbd, 0xda, 0xc8, //0x000012ad vpminub      %ymm0, %ymm8, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x000012b1 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000012b5 vpmovmskb    %ymm0, %eax
	0x48, 0x09, 0xc6, //0x000012b9 orq          %rax, %rsi
	0x48, 0x85, 0xff, //0x000012bc testq        %rdi, %rdi
	0x0f, 0x85, 0x8b, 0x00, 0x00, 0x00, //0x000012bf jne          LBB0_237
	0x48, 0x85, 0xf6, //0x000012c5 testq        %rsi, %rsi
	0x0f, 0x85, 0x4e, 0x2b, 0x00, 0x00, //0x000012c8 jne          LBB0_704
	0x49, 0x83, 0xc3, 0xc0, //0x000012ce addq         $-64, %r11
	0x48, 0x83, 0xc1, 0x40, //0x000012d2 addq         $64, %rcx
	0x49, 0x83, 0xfb, 0x3f, //0x000012d6 cmpq         $63, %r11
	0x0f, 0x87, 0x70, 0xff, 0xff, 0xff, //0x000012da ja           LBB0_223
	0xe9, 0x7e, 0x17, 0x00, 0x00, //0x000012e0 jmp          LBB0_229
	//0x000012e5 LBB0_234
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x000012e5 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x000012eb jne          LBB0_236
	0x48, 0x0f, 0xbc, 0xc2, //0x000012f1 bsfq         %rdx, %rax
	0x48, 0x01, 0xc8, //0x000012f5 addq         %rcx, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x000012f8 movq         %rax, $16(%rsp)
	//0x000012fd LBB0_236
	0x4c, 0x89, 0xd0, //0x000012fd movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00001300 notq         %rax
	0x48, 0x21, 0xd0, //0x00001303 andq         %rdx, %rax
	0x4c, 0x8d, 0x04, 0x00, //0x00001306 leaq         (%rax,%rax), %r8
	0x4d, 0x09, 0xd0, //0x0000130a orq          %r10, %r8
	0x4c, 0x89, 0xc3, //0x0000130d movq         %r8, %rbx
	0x48, 0xf7, 0xd3, //0x00001310 notq         %rbx
	0x48, 0x21, 0xd3, //0x00001313 andq         %rdx, %rbx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001316 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd3, //0x00001320 andq         %rdx, %rbx
	0x45, 0x31, 0xd2, //0x00001323 xorl         %r10d, %r10d
	0x48, 0x01, 0xc3, //0x00001326 addq         %rax, %rbx
	0x41, 0x0f, 0x92, 0xc2, //0x00001329 setb         %r10b
	0x48, 0x01, 0xdb, //0x0000132d addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001330 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x0000133a xorq         %rax, %rbx
	0x4c, 0x21, 0xc3, //0x0000133d andq         %r8, %rbx
	0x48, 0xf7, 0xd3, //0x00001340 notq         %rbx
	0x48, 0x21, 0xdf, //0x00001343 andq         %rbx, %rdi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00001346 movq         $24(%rsp), %r8
	0xe9, 0x5d, 0xff, 0xff, 0xff, //0x0000134b jmp          LBB0_226
	//0x00001350 LBB0_237
	0x48, 0x0f, 0xbc, 0xc7, //0x00001350 bsfq         %rdi, %rax
	0x48, 0x85, 0xf6, //0x00001354 testq        %rsi, %rsi
	0x0f, 0x84, 0xbe, 0x07, 0x00, 0x00, //0x00001357 je           LBB0_361
	0x48, 0x0f, 0xbc, 0xd6, //0x0000135d bsfq         %rsi, %rdx
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00001361 movq         $32(%rsp), %r11
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x00001366 movq         $8(%rsp), %rbx
	0x48, 0x39, 0xc2, //0x0000136b cmpq         %rax, %rdx
	0x0f, 0x83, 0xbf, 0x07, 0x00, 0x00, //0x0000136e jae          LBB0_362
	0xe9, 0x5a, 0x2c, 0x00, 0x00, //0x00001374 jmp          LBB0_239
	//0x00001379 LBB0_245
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00001379 movl         $64, %edx
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000137e movq         $32(%rsp), %r11
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x00001383 movq         $8(%rsp), %rbx
	0x48, 0x39, 0xc2, //0x00001388 cmpq         %rax, %rdx
	0x0f, 0x82, 0x42, 0x2c, 0x00, 0x00, //0x0000138b jb           LBB0_239
	//0x00001391 LBB0_246
	0x48, 0x01, 0xc1, //0x00001391 addq         %rax, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00001394 addq         $1, %rcx
	0x48, 0x85, 0xc9, //0x00001398 testq        %rcx, %rcx
	0x0f, 0x88, 0x10, 0x2a, 0x00, 0x00, //0x0000139b js           LBB0_696
	//0x000013a1 LBB0_247
	0x48, 0x89, 0x0b, //0x000013a1 movq         %rcx, (%rbx)
	0x4c, 0x89, 0xe8, //0x000013a4 movq         %r13, %rax
	0x48, 0xba, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x000013a7 movabsq      $9223372036854775806, %rdx
	0x49, 0x39, 0xd5, //0x000013b1 cmpq         %rdx, %r13
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x000013b4 movq         $40(%rsp), %r14
	0x0f, 0x86, 0xe2, 0xef, 0xff, 0xff, //0x000013b9 jbe          LBB0_4
	0xe9, 0x39, 0x2a, 0x00, 0x00, //0x000013bf jmp          LBB0_721
	//0x000013c4 LBB0_248
	0x49, 0x8b, 0x53, 0x08, //0x000013c4 movq         $8(%r11), %rdx
	0x48, 0x8d, 0x72, 0xfc, //0x000013c8 leaq         $-4(%rdx), %rsi
	0x49, 0x39, 0xf5, //0x000013cc cmpq         %rsi, %r13
	0x0f, 0x83, 0x6a, 0x2a, 0x00, 0x00, //0x000013cf jae          LBB0_706
	0x41, 0x8b, 0x0c, 0x0c, //0x000013d5 movl         (%r12,%rcx), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x000013d9 cmpl         $1702063201, %ecx
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x000013df movq         $8(%rsp), %rdx
	0x0f, 0x85, 0xb4, 0x2a, 0x00, 0x00, //0x000013e4 jne          LBB0_707
	0x49, 0x8d, 0x4d, 0x05, //0x000013ea leaq         $5(%r13), %rcx
	0xe9, 0x95, 0xef, 0xff, 0xff, //0x000013ee jmp          LBB0_2
	//0x000013f3 LBB0_251
	0x4d, 0x8b, 0x73, 0x08, //0x000013f3 movq         $8(%r11), %r14
	0x41, 0xf6, 0xc0, 0x40, //0x000013f7 testb        $64, %r8b
	0x0f, 0x85, 0x84, 0x07, 0x00, 0x00, //0x000013fb jne          LBB0_366
	0x41, 0xf6, 0xc0, 0x20, //0x00001401 testb        $32, %r8b
	0x0f, 0x85, 0x46, 0x10, 0x00, 0x00, //0x00001405 jne          LBB0_450
	0x4d, 0x89, 0xf2, //0x0000140b movq         %r14, %r10
	0x49, 0x29, 0xca, //0x0000140e subq         %rcx, %r10
	0x0f, 0x84, 0xe2, 0x2b, 0x00, 0x00, //0x00001411 je           LBB0_727
	0x49, 0x83, 0xfa, 0x40, //0x00001417 cmpq         $64, %r10
	0x0f, 0x82, 0x03, 0x1a, 0x00, 0x00, //0x0000141b jb           LBB0_544
	0x48, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x00001421 movq         $-2, %rsi
	0x4c, 0x29, 0xee, //0x00001428 subq         %r13, %rsi
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x0000142b movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc0, //0x00001434 xorl         %r8d, %r8d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001437 .p2align 4, 0x90
	//0x00001440 LBB0_256
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x0c, //0x00001440 vmovdqu      (%r12,%rcx), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x0c, 0x20, //0x00001446 vmovdqu      $32(%r12,%rcx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x0000144d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00001451 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00001455 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00001459 vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xc7, //0x0000145d vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00001461 vpmovmskb    %ymm0, %edx
	0xc5, 0xf5, 0x74, 0xc7, //0x00001465 vpcmpeqb     %ymm7, %ymm1, %ymm0
	0xc5, 0xfd, 0xd7, 0xd8, //0x00001469 vpmovmskb    %ymm0, %ebx
	0x48, 0xc1, 0xe0, 0x20, //0x0000146d shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x00001471 orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x20, //0x00001474 shlq         $32, %rbx
	0x48, 0x09, 0xda, //0x00001478 orq          %rbx, %rdx
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x0000147b jne          LBB0_265
	0x4d, 0x85, 0xc0, //0x00001481 testq        %r8, %r8
	0x0f, 0x85, 0x3f, 0x00, 0x00, 0x00, //0x00001484 jne          LBB0_267
	0x45, 0x31, 0xc0, //0x0000148a xorl         %r8d, %r8d
	0x48, 0x85, 0xff, //0x0000148d testq        %rdi, %rdi
	0x0f, 0x85, 0x8a, 0x00, 0x00, 0x00, //0x00001490 jne          LBB0_268
	//0x00001496 LBB0_259
	0x49, 0x83, 0xc2, 0xc0, //0x00001496 addq         $-64, %r10
	0x48, 0x83, 0xc6, 0xc0, //0x0000149a addq         $-64, %rsi
	0x48, 0x83, 0xc1, 0x40, //0x0000149e addq         $64, %rcx
	0x49, 0x83, 0xfa, 0x3f, //0x000014a2 cmpq         $63, %r10
	0x0f, 0x87, 0x94, 0xff, 0xff, 0xff, //0x000014a6 ja           LBB0_256
	0xe9, 0xc7, 0x16, 0x00, 0x00, //0x000014ac jmp          LBB0_260
	//0x000014b1 LBB0_265
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x000014b1 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x000014b7 jne          LBB0_267
	0x48, 0x0f, 0xbc, 0xc2, //0x000014bd bsfq         %rdx, %rax
	0x48, 0x01, 0xc8, //0x000014c1 addq         %rcx, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x000014c4 movq         %rax, $16(%rsp)
	//0x000014c9 LBB0_267
	0x4c, 0x89, 0xc0, //0x000014c9 movq         %r8, %rax
	0x48, 0xf7, 0xd0, //0x000014cc notq         %rax
	0x48, 0x21, 0xd0, //0x000014cf andq         %rdx, %rax
	0x4c, 0x8d, 0x1c, 0x00, //0x000014d2 leaq         (%rax,%rax), %r11
	0x4d, 0x09, 0xc3, //0x000014d6 orq          %r8, %r11
	0x4c, 0x89, 0xdb, //0x000014d9 movq         %r11, %rbx
	0x48, 0xf7, 0xd3, //0x000014dc notq         %rbx
	0x48, 0x21, 0xd3, //0x000014df andq         %rdx, %rbx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000014e2 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd3, //0x000014ec andq         %rdx, %rbx
	0x45, 0x31, 0xc0, //0x000014ef xorl         %r8d, %r8d
	0x48, 0x01, 0xc3, //0x000014f2 addq         %rax, %rbx
	0x41, 0x0f, 0x92, 0xc0, //0x000014f5 setb         %r8b
	0x48, 0x01, 0xdb, //0x000014f9 addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000014fc movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x00001506 xorq         %rax, %rbx
	0x4c, 0x21, 0xdb, //0x00001509 andq         %r11, %rbx
	0x48, 0xf7, 0xd3, //0x0000150c notq         %rbx
	0x48, 0x21, 0xdf, //0x0000150f andq         %rbx, %rdi
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00001512 movq         $32(%rsp), %r11
	0x48, 0x85, 0xff, //0x00001517 testq        %rdi, %rdi
	0x0f, 0x84, 0x76, 0xff, 0xff, 0xff, //0x0000151a je           LBB0_259
	//0x00001520 LBB0_268
	0xc5, 0x7d, 0x7f, 0xea, //0x00001520 vmovdqa      %ymm13, %ymm2
	0x48, 0x0f, 0xbc, 0xcf, //0x00001524 bsfq         %rdi, %rcx
	0x48, 0x29, 0xf1, //0x00001528 subq         %rsi, %rcx
	//0x0000152b LBB0_269
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x0000152b movq         $24(%rsp), %r8
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00001530 movq         $8(%rsp), %rsi
	0xe9, 0xc3, 0x10, 0x00, 0x00, //0x00001535 jmp          LBB0_475
	//0x0000153a LBB0_270
	0x4d, 0x8b, 0x7b, 0x08, //0x0000153a movq         $8(%r11), %r15
	0x41, 0xf6, 0xc0, 0x40, //0x0000153e testb        $64, %r8b
	0x0f, 0x85, 0x25, 0x07, 0x00, 0x00, //0x00001542 jne          LBB0_377
	0x49, 0x29, 0xcf, //0x00001548 subq         %rcx, %r15
	0x0f, 0x84, 0x0f, 0x2a, 0x00, 0x00, //0x0000154b je           LBB0_714
	0x4d, 0x8d, 0x14, 0x0c, //0x00001551 leaq         (%r12,%rcx), %r10
	0x41, 0x80, 0x3a, 0x30, //0x00001555 cmpb         $48, (%r10)
	0x0f, 0x85, 0x34, 0x00, 0x00, 0x00, //0x00001559 jne          LBB0_276
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000155f movl         $1, %ebx
	0x49, 0x83, 0xff, 0x01, //0x00001564 cmpq         $1, %r15
	0x0f, 0x84, 0x71, 0x17, 0x00, 0x00, //0x00001568 je           LBB0_530
	0x41, 0x8a, 0x42, 0x01, //0x0000156e movb         $1(%r10), %al
	0x04, 0xd2, //0x00001572 addb         $-46, %al
	0x3c, 0x37, //0x00001574 cmpb         $55, %al
	0x0f, 0x87, 0x63, 0x17, 0x00, 0x00, //0x00001576 ja           LBB0_530
	0x0f, 0xb6, 0xc0, //0x0000157c movzbl       %al, %eax
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x0000157f movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xc2, //0x00001589 btq          %rax, %rdx
	0x0f, 0x83, 0x4c, 0x17, 0x00, 0x00, //0x0000158d jae          LBB0_530
	//0x00001593 LBB0_276
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00001593 movq         $-1, %r11
	0x49, 0x83, 0xff, 0x20, //0x0000159a cmpq         $32, %r15
	0x0f, 0x82, 0x56, 0x18, 0x00, 0x00, //0x0000159e jb           LBB0_543
	0x31, 0xdb, //0x000015a4 xorl         %ebx, %ebx
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000015a6 movq         $-1, %r14
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000015ad movq         $-1, %r8
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000015b4 .p2align 4, 0x90
	//0x000015c0 LBB0_278
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x1a, //0x000015c0 vmovdqu      (%r10,%rbx), %ymm0
	0xc5, 0xb5, 0x74, 0xc8, //0x000015c6 vpcmpeqb     %ymm0, %ymm9, %ymm1
	0xc5, 0x95, 0x74, 0xd0, //0x000015ca vpcmpeqb     %ymm0, %ymm13, %ymm2
	0xc5, 0xed, 0xeb, 0xc9, //0x000015ce vpor         %ymm1, %ymm2, %ymm1
	0xc5, 0xa5, 0xdb, 0xd0, //0x000015d2 vpand        %ymm0, %ymm11, %ymm2
	0xc5, 0x8d, 0x74, 0xd8, //0x000015d6 vpcmpeqb     %ymm0, %ymm14, %ymm3
	0xc5, 0xfd, 0xd7, 0xfb, //0x000015da vpmovmskb    %ymm3, %edi
	0xc5, 0x85, 0x74, 0xd2, //0x000015de vpcmpeqb     %ymm2, %ymm15, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x000015e2 vpmovmskb    %ymm2, %esi
	0xc5, 0xfd, 0xd7, 0xc1, //0x000015e6 vpmovmskb    %ymm1, %eax
	0xc5, 0xfd, 0xfc, 0x05, 0x8e, 0xec, 0xff, 0xff, //0x000015ea vpaddb       $-4978(%rip), %ymm0, %ymm0  /* LCPI0_18+0(%rip) */
	0xc5, 0xfd, 0xda, 0x25, 0xa6, 0xec, 0xff, 0xff, //0x000015f2 vpminub      $-4954(%rip), %ymm0, %ymm4  /* LCPI0_19+0(%rip) */
	0xc5, 0xfd, 0x74, 0xc4, //0x000015fa vpcmpeqb     %ymm4, %ymm0, %ymm0
	0xc5, 0xe5, 0xeb, 0xd2, //0x000015fe vpor         %ymm2, %ymm3, %ymm2
	0xc5, 0xfd, 0xeb, 0xc2, //0x00001602 vpor         %ymm2, %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x00001606 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x0000160a vpmovmskb    %ymm0, %ecx
	0x48, 0xf7, 0xd1, //0x0000160e notq         %rcx
	0x48, 0x0f, 0xbc, 0xc9, //0x00001611 bsfq         %rcx, %rcx
	0x83, 0xf9, 0x20, //0x00001615 cmpl         $32, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00001618 je           LBB0_280
	0xba, 0xff, 0xff, 0xff, 0xff, //0x0000161e movl         $-1, %edx
	0xd3, 0xe2, //0x00001623 shll         %cl, %edx
	0xf7, 0xd2, //0x00001625 notl         %edx
	0x21, 0xd7, //0x00001627 andl         %edx, %edi
	0x21, 0xd6, //0x00001629 andl         %edx, %esi
	0x21, 0xc2, //0x0000162b andl         %eax, %edx
	0x89, 0xd0, //0x0000162d movl         %edx, %eax
	//0x0000162f LBB0_280
	0x8d, 0x57, 0xff, //0x0000162f leal         $-1(%rdi), %edx
	0x21, 0xfa, //0x00001632 andl         %edi, %edx
	0xc5, 0xfe, 0x6f, 0x1d, 0x84, 0xeb, 0xff, 0xff, //0x00001634 vmovdqu      $-5244(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x5c, 0xeb, 0xff, 0xff, //0x0000163c vmovdqu      $-5284(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0x0f, 0x85, 0x22, 0x15, 0x00, 0x00, //0x00001644 jne          LBB0_516
	0x8d, 0x56, 0xff, //0x0000164a leal         $-1(%rsi), %edx
	0x21, 0xf2, //0x0000164d andl         %esi, %edx
	0x0f, 0x85, 0x17, 0x15, 0x00, 0x00, //0x0000164f jne          LBB0_516
	0x8d, 0x50, 0xff, //0x00001655 leal         $-1(%rax), %edx
	0x21, 0xc2, //0x00001658 andl         %eax, %edx
	0x0f, 0x85, 0x0c, 0x15, 0x00, 0x00, //0x0000165a jne          LBB0_516
	0x85, 0xff, //0x00001660 testl        %edi, %edi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00001662 je           LBB0_286
	0x0f, 0xbc, 0xd7, //0x00001668 bsfl         %edi, %edx
	0x49, 0x83, 0xf8, 0xff, //0x0000166b cmpq         $-1, %r8
	0x0f, 0x85, 0xbd, 0x15, 0x00, 0x00, //0x0000166f jne          LBB0_519
	0x48, 0x01, 0xda, //0x00001675 addq         %rbx, %rdx
	0x49, 0x89, 0xd0, //0x00001678 movq         %rdx, %r8
	//0x0000167b LBB0_286
	0x85, 0xf6, //0x0000167b testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x0000167d je           LBB0_289
	0x0f, 0xbc, 0xd6, //0x00001683 bsfl         %esi, %edx
	0x49, 0x83, 0xfe, 0xff, //0x00001686 cmpq         $-1, %r14
	0x0f, 0x85, 0xa2, 0x15, 0x00, 0x00, //0x0000168a jne          LBB0_519
	0x48, 0x01, 0xda, //0x00001690 addq         %rbx, %rdx
	0x49, 0x89, 0xd6, //0x00001693 movq         %rdx, %r14
	//0x00001696 LBB0_289
	0x85, 0xc0, //0x00001696 testl        %eax, %eax
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00001698 je           LBB0_292
	0x0f, 0xbc, 0xc0, //0x0000169e bsfl         %eax, %eax
	0x49, 0x83, 0xfb, 0xff, //0x000016a1 cmpq         $-1, %r11
	0x0f, 0x85, 0x04, 0x16, 0x00, 0x00, //0x000016a5 jne          LBB0_525
	0x48, 0x01, 0xd8, //0x000016ab addq         %rbx, %rax
	0x49, 0x89, 0xc3, //0x000016ae movq         %rax, %r11
	//0x000016b1 LBB0_292
	0x83, 0xf9, 0x20, //0x000016b1 cmpl         $32, %ecx
	0x0f, 0x85, 0xdc, 0x0e, 0x00, 0x00, //0x000016b4 jne          LBB0_469
	0x49, 0x83, 0xc7, 0xe0, //0x000016ba addq         $-32, %r15
	0x48, 0x83, 0xc3, 0x20, //0x000016be addq         $32, %rbx
	0x49, 0x83, 0xff, 0x1f, //0x000016c2 cmpq         $31, %r15
	0x0f, 0x87, 0xf4, 0xfe, 0xff, 0xff, //0x000016c6 ja           LBB0_278
	0xc5, 0xf8, 0x77, //0x000016cc vzeroupper   
	0x4c, 0x01, 0xd3, //0x000016cf addq         %r10, %rbx
	0x4c, 0x89, 0x54, 0x24, 0x30, //0x000016d2 movq         %r10, $48(%rsp)
	0x4c, 0x89, 0x4c, 0x24, 0x10, //0x000016d7 movq         %r9, $16(%rsp)
	0x49, 0x83, 0xff, 0x10, //0x000016dc cmpq         $16, %r15
	0x0f, 0x82, 0x2c, 0x01, 0x00, 0x00, //0x000016e0 jb           LBB0_313
	//0x000016e6 LBB0_295
	0x48, 0x89, 0xd8, //0x000016e6 movq         %rbx, %rax
	0x4c, 0x29, 0xe8, //0x000016e9 subq         %r13, %rax
	0x4d, 0x89, 0xe1, //0x000016ec movq         %r12, %r9
	0x49, 0xf7, 0xd1, //0x000016ef notq         %r9
	0x49, 0x01, 0xc1, //0x000016f2 addq         %rax, %r9
	0x45, 0x31, 0xd2, //0x000016f5 xorl         %r10d, %r10d
	//0x000016f8 LBB0_296
	0xc4, 0xa1, 0x7a, 0x6f, 0x04, 0x13, //0x000016f8 vmovdqu      (%rbx,%r10), %xmm0
	0xc5, 0xf9, 0x74, 0x0d, 0x3a, 0xe9, 0xff, 0xff, //0x000016fe vpcmpeqb     $-5830(%rip), %xmm0, %xmm1  /* LCPI0_20+0(%rip) */
	0xc5, 0xf9, 0x74, 0x15, 0x42, 0xe9, 0xff, 0xff, //0x00001706 vpcmpeqb     $-5822(%rip), %xmm0, %xmm2  /* LCPI0_21+0(%rip) */
	0xc5, 0xe9, 0xeb, 0xc9, //0x0000170e vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xf9, 0xfc, 0x15, 0x46, 0xe9, 0xff, 0xff, //0x00001712 vpaddb       $-5818(%rip), %xmm0, %xmm2  /* LCPI0_22+0(%rip) */
	0xc5, 0xe9, 0xda, 0x1d, 0x4e, 0xe9, 0xff, 0xff, //0x0000171a vpminub      $-5810(%rip), %xmm2, %xmm3  /* LCPI0_23+0(%rip) */
	0xc5, 0xe9, 0x74, 0xd3, //0x00001722 vpcmpeqb     %xmm3, %xmm2, %xmm2
	0xc5, 0xf9, 0xdb, 0x1d, 0xf2, 0xe8, 0xff, 0xff, //0x00001726 vpand        $-5902(%rip), %xmm0, %xmm3  /* LCPI0_12+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x4a, 0xe9, 0xff, 0xff, //0x0000172e vpcmpeqb     $-5814(%rip), %xmm0, %xmm0  /* LCPI0_24+0(%rip) */
	0xc5, 0xe1, 0x74, 0x1d, 0x52, 0xe9, 0xff, 0xff, //0x00001736 vpcmpeqb     $-5806(%rip), %xmm3, %xmm3  /* LCPI0_25+0(%rip) */
	0xc5, 0xe1, 0xeb, 0xe0, //0x0000173e vpor         %xmm0, %xmm3, %xmm4
	0xc5, 0xd9, 0xeb, 0xe1, //0x00001742 vpor         %xmm1, %xmm4, %xmm4
	0xc5, 0xd9, 0xeb, 0xd2, //0x00001746 vpor         %xmm2, %xmm4, %xmm2
	0xc5, 0xf9, 0xd7, 0xd0, //0x0000174a vpmovmskb    %xmm0, %edx
	0xc5, 0xf9, 0xd7, 0xfb, //0x0000174e vpmovmskb    %xmm3, %edi
	0xc5, 0xf9, 0xd7, 0xf1, //0x00001752 vpmovmskb    %xmm1, %esi
	0xc5, 0xf9, 0xd7, 0xc2, //0x00001756 vpmovmskb    %xmm2, %eax
	0xf7, 0xd0, //0x0000175a notl         %eax
	0x0f, 0xbc, 0xc8, //0x0000175c bsfl         %eax, %ecx
	0x83, 0xf9, 0x10, //0x0000175f cmpl         $16, %ecx
	0x0f, 0x84, 0x11, 0x00, 0x00, 0x00, //0x00001762 je           LBB0_298
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001768 movl         $-1, %eax
	0xd3, 0xe0, //0x0000176d shll         %cl, %eax
	0xf7, 0xd0, //0x0000176f notl         %eax
	0x21, 0xc2, //0x00001771 andl         %eax, %edx
	0x21, 0xc7, //0x00001773 andl         %eax, %edi
	0x21, 0xf0, //0x00001775 andl         %esi, %eax
	0x89, 0xc6, //0x00001777 movl         %eax, %esi
	//0x00001779 LBB0_298
	0x8d, 0x42, 0xff, //0x00001779 leal         $-1(%rdx), %eax
	0x21, 0xd0, //0x0000177c andl         %edx, %eax
	0x0f, 0x85, 0x90, 0x14, 0x00, 0x00, //0x0000177e jne          LBB0_517
	0x8d, 0x47, 0xff, //0x00001784 leal         $-1(%rdi), %eax
	0x21, 0xf8, //0x00001787 andl         %edi, %eax
	0x0f, 0x85, 0x85, 0x14, 0x00, 0x00, //0x00001789 jne          LBB0_517
	0x8d, 0x46, 0xff, //0x0000178f leal         $-1(%rsi), %eax
	0x21, 0xf0, //0x00001792 andl         %esi, %eax
	0x0f, 0x85, 0x7a, 0x14, 0x00, 0x00, //0x00001794 jne          LBB0_517
	0x85, 0xd2, //0x0000179a testl        %edx, %edx
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x0000179c je           LBB0_304
	0x0f, 0xbc, 0xd2, //0x000017a2 bsfl         %edx, %edx
	0x49, 0x83, 0xf8, 0xff, //0x000017a5 cmpq         $-1, %r8
	0x0f, 0x85, 0xa8, 0x14, 0x00, 0x00, //0x000017a9 jne          LBB0_522
	0x4c, 0x01, 0xca, //0x000017af addq         %r9, %rdx
	0x4c, 0x01, 0xd2, //0x000017b2 addq         %r10, %rdx
	0x49, 0x89, 0xd0, //0x000017b5 movq         %rdx, %r8
	//0x000017b8 LBB0_304
	0x85, 0xff, //0x000017b8 testl        %edi, %edi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000017ba je           LBB0_307
	0x0f, 0xbc, 0xd7, //0x000017c0 bsfl         %edi, %edx
	0x49, 0x83, 0xfe, 0xff, //0x000017c3 cmpq         $-1, %r14
	0x0f, 0x85, 0x8a, 0x14, 0x00, 0x00, //0x000017c7 jne          LBB0_522
	0x4c, 0x01, 0xca, //0x000017cd addq         %r9, %rdx
	0x4c, 0x01, 0xd2, //0x000017d0 addq         %r10, %rdx
	0x49, 0x89, 0xd6, //0x000017d3 movq         %rdx, %r14
	//0x000017d6 LBB0_307
	0x85, 0xf6, //0x000017d6 testl        %esi, %esi
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000017d8 je           LBB0_310
	0x0f, 0xbc, 0xd6, //0x000017de bsfl         %esi, %edx
	0x49, 0x83, 0xfb, 0xff, //0x000017e1 cmpq         $-1, %r11
	0x0f, 0x85, 0x6c, 0x14, 0x00, 0x00, //0x000017e5 jne          LBB0_522
	0x4c, 0x01, 0xca, //0x000017eb addq         %r9, %rdx
	0x4c, 0x01, 0xd2, //0x000017ee addq         %r10, %rdx
	0x49, 0x89, 0xd3, //0x000017f1 movq         %rdx, %r11
	//0x000017f4 LBB0_310
	0x83, 0xf9, 0x10, //0x000017f4 cmpl         $16, %ecx
	0x0f, 0x85, 0xe5, 0x0e, 0x00, 0x00, //0x000017f7 jne          LBB0_479
	0x49, 0x83, 0xc7, 0xf0, //0x000017fd addq         $-16, %r15
	0x49, 0x83, 0xc2, 0x10, //0x00001801 addq         $16, %r10
	0x49, 0x83, 0xff, 0x0f, //0x00001805 cmpq         $15, %r15
	0x0f, 0x87, 0xe9, 0xfe, 0xff, 0xff, //0x00001809 ja           LBB0_296
	0x4c, 0x01, 0xd3, //0x0000180f addq         %r10, %rbx
	//0x00001812 LBB0_313
	0x4d, 0x85, 0xff, //0x00001812 testq        %r15, %r15
	0x4c, 0x8b, 0x4c, 0x24, 0x10, //0x00001815 movq         $16(%rsp), %r9
	0x48, 0x8d, 0x0d, 0xb7, 0x2a, 0x00, 0x00, //0x0000181a leaq         $10935(%rip), %rcx  /* LJTI0_2+0(%rip) */
	0x0f, 0x84, 0xd0, 0x0e, 0x00, 0x00, //0x00001821 je           LBB0_481
	0x4e, 0x8d, 0x14, 0x3b, //0x00001827 leaq         (%rbx,%r15), %r10
	0x48, 0x89, 0xd8, //0x0000182b movq         %rbx, %rax
	0x4c, 0x29, 0xe8, //0x0000182e subq         %r13, %rax
	0x4c, 0x89, 0xe6, //0x00001831 movq         %r12, %rsi
	0x48, 0xf7, 0xd6, //0x00001834 notq         %rsi
	0x48, 0x01, 0xc6, //0x00001837 addq         %rax, %rsi
	0x31, 0xc0, //0x0000183a xorl         %eax, %eax
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x0000183c jmp          LBB0_316
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001841 .p2align 4, 0x90
	//0x00001850 LBB0_315
	0x48, 0x83, 0xc0, 0x01, //0x00001850 addq         $1, %rax
	0x49, 0x39, 0xc7, //0x00001854 cmpq         %rax, %r15
	0x0f, 0x84, 0x07, 0x13, 0x00, 0x00, //0x00001857 je           LBB0_515
	//0x0000185d LBB0_316
	0x0f, 0xbe, 0x14, 0x03, //0x0000185d movsbl       (%rbx,%rax), %edx
	0x8d, 0x7a, 0xd0, //0x00001861 leal         $-48(%rdx), %edi
	0x83, 0xff, 0x0a, //0x00001864 cmpl         $10, %edi
	0x0f, 0x82, 0xe3, 0xff, 0xff, 0xff, //0x00001867 jb           LBB0_315
	0x8d, 0x7a, 0xd5, //0x0000186d leal         $-43(%rdx), %edi
	0x83, 0xff, 0x1a, //0x00001870 cmpl         $26, %edi
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x00001873 ja           LBB0_321
	0x48, 0x63, 0x14, 0xb9, //0x00001879 movslq       (%rcx,%rdi,4), %rdx
	0x48, 0x01, 0xca, //0x0000187d addq         %rcx, %rdx
	0xff, 0xe2, //0x00001880 jmpq         *%rdx
	//0x00001882 LBB0_319
	0x49, 0x83, 0xfb, 0xff, //0x00001882 cmpq         $-1, %r11
	0x0f, 0x85, 0x90, 0x13, 0x00, 0x00, //0x00001886 jne          LBB0_518
	0x4c, 0x8d, 0x1c, 0x06, //0x0000188c leaq         (%rsi,%rax), %r11
	0xe9, 0xbb, 0xff, 0xff, 0xff, //0x00001890 jmp          LBB0_315
	//0x00001895 LBB0_321
	0x83, 0xfa, 0x65, //0x00001895 cmpl         $101, %edx
	0x0f, 0x85, 0x56, 0x0e, 0x00, 0x00, //0x00001898 jne          LBB0_480
	//0x0000189e LBB0_322
	0x49, 0x83, 0xfe, 0xff, //0x0000189e cmpq         $-1, %r14
	0x0f, 0x85, 0x74, 0x13, 0x00, 0x00, //0x000018a2 jne          LBB0_518
	0x4c, 0x8d, 0x34, 0x06, //0x000018a8 leaq         (%rsi,%rax), %r14
	0xe9, 0x9f, 0xff, 0xff, 0xff, //0x000018ac jmp          LBB0_315
	//0x000018b1 LBB0_324
	0x49, 0x83, 0xf8, 0xff, //0x000018b1 cmpq         $-1, %r8
	0x0f, 0x85, 0x61, 0x13, 0x00, 0x00, //0x000018b5 jne          LBB0_518
	0x4c, 0x8d, 0x04, 0x06, //0x000018bb leaq         (%rsi,%rax), %r8
	0xe9, 0x8c, 0xff, 0xff, 0xff, //0x000018bf jmp          LBB0_315
	//0x000018c4 LBB0_326
	0x41, 0xf6, 0xc0, 0x40, //0x000018c4 testb        $64, %r8b
	0x0f, 0x85, 0xbe, 0x04, 0x00, 0x00, //0x000018c8 jne          LBB0_393
	0x49, 0x8b, 0x06, //0x000018ce movq         (%r14), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x000018d1 cmpq         $4095, %rax
	0x0f, 0x8f, 0xc8, 0x24, 0x00, 0x00, //0x000018d7 jg           LBB0_713
	0x48, 0x8d, 0x50, 0x01, //0x000018dd leaq         $1(%rax), %rdx
	0x49, 0x89, 0x16, //0x000018e1 movq         %rdx, (%r14)
	0x49, 0xc7, 0x44, 0xc6, 0x08, 0x05, 0x00, 0x00, 0x00, //0x000018e4 movq         $5, $8(%r14,%rax,8)
	0xe9, 0xaf, 0xea, 0xff, 0xff, //0x000018ed jmp          LBB0_4
	//0x000018f2 LBB0_329
	0x49, 0x8b, 0x4b, 0x08, //0x000018f2 movq         $8(%r11), %rcx
	0x48, 0x8d, 0x51, 0xfd, //0x000018f6 leaq         $-3(%rcx), %rdx
	0x49, 0x39, 0xd5, //0x000018fa cmpq         %rdx, %r13
	0x0f, 0x83, 0x0c, 0x25, 0x00, 0x00, //0x000018fd jae          LBB0_703
	0x41, 0x81, 0x3a, 0x6e, 0x75, 0x6c, 0x6c, //0x00001903 cmpl         $1819047278, (%r10)
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x0000190a movq         $8(%rsp), %rdx
	0x0f, 0x84, 0x6f, 0xea, 0xff, 0xff, //0x0000190f je           LBB0_1
	0xe9, 0x32, 0x25, 0x00, 0x00, //0x00001915 jmp          LBB0_331
	//0x0000191a LBB0_336
	0x41, 0xf6, 0xc0, 0x40, //0x0000191a testb        $64, %r8b
	0x0f, 0x85, 0x93, 0x07, 0x00, 0x00, //0x0000191e jne          LBB0_419
	0x49, 0x8b, 0x06, //0x00001924 movq         (%r14), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001927 cmpq         $4095, %rax
	0x0f, 0x8f, 0x72, 0x24, 0x00, 0x00, //0x0000192d jg           LBB0_713
	0x48, 0x8d, 0x50, 0x01, //0x00001933 leaq         $1(%rax), %rdx
	0x49, 0x89, 0x16, //0x00001937 movq         %rdx, (%r14)
	0x49, 0xc7, 0x44, 0xc6, 0x08, 0x06, 0x00, 0x00, 0x00, //0x0000193a movq         $6, $8(%r14,%rax,8)
	0xe9, 0x59, 0xea, 0xff, 0xff, //0x00001943 jmp          LBB0_4
	//0x00001948 LBB0_339
	0x49, 0x8b, 0x4b, 0x08, //0x00001948 movq         $8(%r11), %rcx
	0x48, 0x8d, 0x51, 0xfd, //0x0000194c leaq         $-3(%rcx), %rdx
	0x49, 0x39, 0xd5, //0x00001950 cmpq         %rdx, %r13
	0x0f, 0x83, 0xb6, 0x24, 0x00, 0x00, //0x00001953 jae          LBB0_703
	0x41, 0x81, 0x3a, 0x74, 0x72, 0x75, 0x65, //0x00001959 cmpl         $1702195828, (%r10)
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x00001960 movq         $8(%rsp), %rdx
	0x0f, 0x84, 0x19, 0xea, 0xff, 0xff, //0x00001965 je           LBB0_1
	0xe9, 0x83, 0x25, 0x00, 0x00, //0x0000196b jmp          LBB0_341
	//0x00001970 LBB0_122
	0x89, 0xc8, //0x00001970 movl         %ecx, %eax
	0x49, 0x01, 0xc3, //0x00001972 addq         %rax, %r11
	0x4d, 0x01, 0xf3, //0x00001975 addq         %r14, %r11
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x00001978 movq         $40(%rsp), %r14
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000197d movq         $-1, %rax
	0x4d, 0x85, 0xff, //0x00001984 testq        %r15, %r15
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x00001987 movq         $16(%rsp), %rcx
	0x0f, 0x85, 0x1d, 0x00, 0x00, 0x00, //0x0000198c jne          LBB0_347
	0xe9, 0x4d, 0x24, 0x00, 0x00, //0x00001992 jmp          LBB0_700
	//0x00001997 LBB0_345
	0x49, 0x01, 0xc3, //0x00001997 addq         %rax, %r11
	//0x0000199a LBB0_346
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000199a movq         $-1, %rax
	0x4d, 0x85, 0xff, //0x000019a1 testq        %r15, %r15
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x000019a4 movq         $16(%rsp), %rcx
	0x0f, 0x84, 0x35, 0x24, 0x00, 0x00, //0x000019a9 je           LBB0_700
	//0x000019af LBB0_347
	0x48, 0x85, 0xc9, //0x000019af testq        %rcx, %rcx
	0xc5, 0xfe, 0x6f, 0x1d, 0x06, 0xe8, 0xff, 0xff, //0x000019b2 vmovdqu      $-6138(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xde, 0xe7, 0xff, 0xff, //0x000019ba vmovdqu      $-6178(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0x0f, 0x84, 0x1c, 0x24, 0x00, 0x00, //0x000019c2 je           LBB0_700
	0x4d, 0x85, 0xc0, //0x000019c8 testq        %r8, %r8
	0x0f, 0x84, 0x13, 0x24, 0x00, 0x00, //0x000019cb je           LBB0_700
	0x4d, 0x29, 0xd3, //0x000019d1 subq         %r10, %r11
	0x49, 0x8d, 0x43, 0xff, //0x000019d4 leaq         $-1(%r11), %rax
	0x49, 0x39, 0xc7, //0x000019d8 cmpq         %rax, %r15
	0x0f, 0x84, 0x8a, 0x00, 0x00, 0x00, //0x000019db je           LBB0_355
	0x48, 0x39, 0xc1, //0x000019e1 cmpq         %rax, %rcx
	0x0f, 0x84, 0x81, 0x00, 0x00, 0x00, //0x000019e4 je           LBB0_355
	0x49, 0x39, 0xc0, //0x000019ea cmpq         %rax, %r8
	0x0f, 0x84, 0x78, 0x00, 0x00, 0x00, //0x000019ed je           LBB0_355
	0x48, 0x85, 0xc9, //0x000019f3 testq        %rcx, %rcx
	0xc5, 0xfe, 0x6f, 0x2d, 0xc2, 0xe6, 0xff, 0xff, //0x000019f6 vmovdqu      $-6462(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xda, 0xe6, 0xff, 0xff, //0x000019fe vmovdqu      $-6438(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xf2, 0xe6, 0xff, 0xff, //0x00001a06 vmovdqu      $-6414(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x0a, 0xe7, 0xff, 0xff, //0x00001a0e vmovdqu      $-6390(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0xe2, 0xe7, 0xff, 0xff, //0x00001a16 vmovdqu      $-6174(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x05, 0xfa, 0xe7, 0xff, 0xff, //0x00001a1e vmovdqu      $-6150(%rip), %ymm0  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xb2, 0xe7, 0xff, 0xff, //0x00001a26 vmovdqu      $-6222(%rip), %ymm11  /* LCPI0_10+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x0a, 0xe8, 0xff, 0xff, //0x00001a2e vmovdqu      $-6134(%rip), %ymm14  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x22, 0xe8, 0xff, 0xff, //0x00001a36 vmovdqu      $-6110(%rip), %ymm15  /* LCPI0_17+0(%rip) */
	0x0f, 0x8e, 0xa4, 0x00, 0x00, 0x00, //0x00001a3e jle          LBB0_358
	0x48, 0x8d, 0x41, 0xff, //0x00001a44 leaq         $-1(%rcx), %rax
	0x49, 0x39, 0xc0, //0x00001a48 cmpq         %rax, %r8
	0x0f, 0x84, 0x97, 0x00, 0x00, 0x00, //0x00001a4b je           LBB0_358
	0x48, 0xf7, 0xd1, //0x00001a51 notq         %rcx
	0x49, 0x89, 0xcb, //0x00001a54 movq         %rcx, %r11
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00001a57 movq         $8(%rsp), %rsi
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001a5c movabsq      $4294977024, %r15
	0xe9, 0x5a, 0x00, 0x00, 0x00, //0x00001a66 jmp          LBB0_356
	//0x00001a6b LBB0_355
	0x49, 0xf7, 0xdb, //0x00001a6b negq         %r11
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00001a6e movq         $8(%rsp), %rsi
	0xc5, 0xfe, 0x6f, 0x2d, 0x45, 0xe6, 0xff, 0xff, //0x00001a73 vmovdqu      $-6587(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001a7b movabsq      $4294977024, %r15
	0xc5, 0xfe, 0x6f, 0x35, 0x53, 0xe6, 0xff, 0xff, //0x00001a85 vmovdqu      $-6573(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x6b, 0xe6, 0xff, 0xff, //0x00001a8d vmovdqu      $-6549(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x83, 0xe6, 0xff, 0xff, //0x00001a95 vmovdqu      $-6525(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x5b, 0xe7, 0xff, 0xff, //0x00001a9d vmovdqu      $-6309(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x05, 0x73, 0xe7, 0xff, 0xff, //0x00001aa5 vmovdqu      $-6285(%rip), %ymm0  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x2b, 0xe7, 0xff, 0xff, //0x00001aad vmovdqu      $-6357(%rip), %ymm11  /* LCPI0_10+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x83, 0xe7, 0xff, 0xff, //0x00001ab5 vmovdqu      $-6269(%rip), %ymm14  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x9b, 0xe7, 0xff, 0xff, //0x00001abd vmovdqu      $-6245(%rip), %ymm15  /* LCPI0_17+0(%rip) */
	//0x00001ac5 LBB0_356
	0xc5, 0x7e, 0x6f, 0x15, 0x73, 0xe6, 0xff, 0xff, //0x00001ac5 vmovdqu      $-6541(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x8b, 0xe6, 0xff, 0xff, //0x00001acd vmovdqu      $-6517(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0x4d, 0x85, 0xdb, //0x00001ad5 testq        %r11, %r11
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00001ad8 movq         $24(%rsp), %r8
	0x0f, 0x89, 0xf0, 0x0f, 0x00, 0x00, //0x00001add jns          LBB0_508
	0xe9, 0xf9, 0x22, 0x00, 0x00, //0x00001ae3 jmp          LBB0_357
	//0x00001ae8 LBB0_358
	0x4c, 0x89, 0xf8, //0x00001ae8 movq         %r15, %rax
	0x4c, 0x09, 0xc0, //0x00001aeb orq          %r8, %rax
	0x0f, 0x99, 0xc0, //0x00001aee setns        %al
	0xc5, 0x7e, 0x6f, 0x15, 0x47, 0xe6, 0xff, 0xff, //0x00001af1 vmovdqu      $-6585(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x5f, 0xe6, 0xff, 0xff, //0x00001af9 vmovdqu      $-6561(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0x0f, 0x88, 0x31, 0x09, 0x00, 0x00, //0x00001b01 js           LBB0_449
	0x4d, 0x39, 0xc7, //0x00001b07 cmpq         %r8, %r15
	0x0f, 0x8c, 0x28, 0x09, 0x00, 0x00, //0x00001b0a jl           LBB0_449
	0x49, 0xf7, 0xd7, //0x00001b10 notq         %r15
	0x4d, 0x89, 0xfb, //0x00001b13 movq         %r15, %r11
	0xe9, 0x9b, 0x0f, 0x00, 0x00, //0x00001b16 jmp          LBB0_506
	//0x00001b1b LBB0_361
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00001b1b movl         $64, %edx
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00001b20 movq         $32(%rsp), %r11
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x00001b25 movq         $8(%rsp), %rbx
	0x48, 0x39, 0xc2, //0x00001b2a cmpq         %rax, %rdx
	0x0f, 0x82, 0xa0, 0x24, 0x00, 0x00, //0x00001b2d jb           LBB0_239
	//0x00001b33 LBB0_362
	0x48, 0x01, 0xc1, //0x00001b33 addq         %rax, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00001b36 addq         $1, %rcx
	0x48, 0x85, 0xc9, //0x00001b3a testq        %rcx, %rcx
	0x0f, 0x88, 0x6e, 0x22, 0x00, 0x00, //0x00001b3d js           LBB0_696
	//0x00001b43 LBB0_363
	0x48, 0x89, 0x0b, //0x00001b43 movq         %rcx, (%rbx)
	0x4c, 0x89, 0xe8, //0x00001b46 movq         %r13, %rax
	0x48, 0xba, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00001b49 movabsq      $9223372036854775806, %rdx
	0x49, 0x39, 0xd5, //0x00001b53 cmpq         %rdx, %r13
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x00001b56 movq         $40(%rsp), %r14
	0x0f, 0x87, 0x9c, 0x22, 0x00, 0x00, //0x00001b5b ja           LBB0_721
	//0x00001b61 LBB0_364
	0x49, 0x8b, 0x06, //0x00001b61 movq         (%r14), %rax
	0x48, 0x3d, 0xff, 0x0f, 0x00, 0x00, //0x00001b64 cmpq         $4095, %rax
	0x0f, 0x8f, 0x35, 0x22, 0x00, 0x00, //0x00001b6a jg           LBB0_713
	0x48, 0x8d, 0x50, 0x01, //0x00001b70 leaq         $1(%rax), %rdx
	0x49, 0x89, 0x16, //0x00001b74 movq         %rdx, (%r14)
	0x49, 0xc7, 0x44, 0xc6, 0x08, 0x04, 0x00, 0x00, 0x00, //0x00001b77 movq         $4, $8(%r14,%rax,8)
	0xe9, 0x1c, 0xe8, 0xff, 0xff, //0x00001b80 jmp          LBB0_4
	//0x00001b85 LBB0_366
	0x4c, 0x89, 0x4c, 0x24, 0x10, //0x00001b85 movq         %r9, $16(%rsp)
	0x4d, 0x89, 0xf2, //0x00001b8a movq         %r14, %r10
	0x49, 0x29, 0xca, //0x00001b8d subq         %rcx, %r10
	0x49, 0x83, 0xfa, 0x20, //0x00001b90 cmpq         $32, %r10
	0x0f, 0x8c, 0x37, 0x12, 0x00, 0x00, //0x00001b94 jl           LBB0_541
	0x4f, 0x8d, 0x04, 0x2c, //0x00001b9a leaq         (%r12,%r13), %r8
	0x4d, 0x29, 0xee, //0x00001b9e subq         %r13, %r14
	0xbe, 0x1f, 0x00, 0x00, 0x00, //0x00001ba1 movl         $31, %esi
	0x45, 0x31, 0xd2, //0x00001ba6 xorl         %r10d, %r10d
	0x45, 0x31, 0xdb, //0x00001ba9 xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, //0x00001bac .p2align 4, 0x90
	//0x00001bb0 LBB0_368
	0xc4, 0x81, 0x7e, 0x6f, 0x44, 0x10, 0x01, //0x00001bb0 vmovdqu      $1(%r8,%r10), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00001bb7 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xc9, //0x00001bbb vpmovmskb    %ymm1, %r9d
	0xc5, 0xfd, 0x74, 0xc7, //0x00001bbf vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc8, //0x00001bc3 vpmovmskb    %ymm0, %ecx
	0x85, 0xc9, //0x00001bc7 testl        %ecx, %ecx
	0x0f, 0x85, 0x11, 0x00, 0x00, 0x00, //0x00001bc9 jne          LBB0_371
	0x4d, 0x85, 0xdb, //0x00001bcf testq        %r11, %r11
	0x0f, 0x85, 0x08, 0x00, 0x00, 0x00, //0x00001bd2 jne          LBB0_371
	0x45, 0x31, 0xdb, //0x00001bd8 xorl         %r11d, %r11d
	0xe9, 0x31, 0x00, 0x00, 0x00, //0x00001bdb jmp          LBB0_372
	//0x00001be0 .p2align 4, 0x90
	//0x00001be0 LBB0_371
	0x44, 0x89, 0xdf, //0x00001be0 movl         %r11d, %edi
	0xf7, 0xd7, //0x00001be3 notl         %edi
	0x21, 0xcf, //0x00001be5 andl         %ecx, %edi
	0x8d, 0x14, 0x3f, //0x00001be7 leal         (%rdi,%rdi), %edx
	0x44, 0x09, 0xda, //0x00001bea orl          %r11d, %edx
	0x89, 0xd3, //0x00001bed movl         %edx, %ebx
	0xf7, 0xd3, //0x00001bef notl         %ebx
	0x21, 0xcb, //0x00001bf1 andl         %ecx, %ebx
	0x81, 0xe3, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001bf3 andl         $-1431655766, %ebx
	0x45, 0x31, 0xdb, //0x00001bf9 xorl         %r11d, %r11d
	0x01, 0xfb, //0x00001bfc addl         %edi, %ebx
	0x41, 0x0f, 0x92, 0xc3, //0x00001bfe setb         %r11b
	0x01, 0xdb, //0x00001c02 addl         %ebx, %ebx
	0x81, 0xf3, 0x55, 0x55, 0x55, 0x55, //0x00001c04 xorl         $1431655765, %ebx
	0x21, 0xd3, //0x00001c0a andl         %edx, %ebx
	0xf7, 0xd3, //0x00001c0c notl         %ebx
	0x41, 0x21, 0xd9, //0x00001c0e andl         %ebx, %r9d
	//0x00001c11 LBB0_372
	0x4d, 0x85, 0xc9, //0x00001c11 testq        %r9, %r9
	0x0f, 0x85, 0xdc, 0x07, 0x00, 0x00, //0x00001c14 jne          LBB0_447
	0x49, 0x83, 0xc2, 0x20, //0x00001c1a addq         $32, %r10
	0x49, 0x8d, 0x0c, 0x36, //0x00001c1e leaq         (%r14,%rsi), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00001c22 addq         $-32, %rcx
	0x48, 0x83, 0xc6, 0xe0, //0x00001c26 addq         $-32, %rsi
	0x48, 0x83, 0xf9, 0x3f, //0x00001c2a cmpq         $63, %rcx
	0x0f, 0x8f, 0x7c, 0xff, 0xff, 0xff, //0x00001c2e jg           LBB0_368
	0x4d, 0x85, 0xdb, //0x00001c34 testq        %r11, %r11
	0x0f, 0x85, 0x8a, 0x1c, 0x00, 0x00, //0x00001c37 jne          LBB0_640
	0x4b, 0x8d, 0x0c, 0x02, //0x00001c3d leaq         (%r10,%r8), %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00001c41 addq         $1, %rcx
	0x49, 0xf7, 0xd2, //0x00001c45 notq         %r10
	0x4d, 0x01, 0xf2, //0x00001c48 addq         %r14, %r10
	//0x00001c4b LBB0_376
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00001c4b movq         $32(%rsp), %r11
	0x4c, 0x8b, 0x4c, 0x24, 0x10, //0x00001c50 movq         $16(%rsp), %r9
	0x4d, 0x85, 0xd2, //0x00001c55 testq        %r10, %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00001c58 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x00001c5d movq         $40(%rsp), %r14
	0x0f, 0x8f, 0xe3, 0x1c, 0x00, 0x00, //0x00001c62 jg           LBB0_644
	0xe9, 0x90, 0x21, 0x00, 0x00, //0x00001c68 jmp          LBB0_721
	//0x00001c6d LBB0_377
	0x4c, 0x89, 0xf8, //0x00001c6d movq         %r15, %rax
	0x48, 0x29, 0xc8, //0x00001c70 subq         %rcx, %rax
	0x48, 0x83, 0xf8, 0x20, //0x00001c73 cmpq         $32, %rax
	0x0f, 0x82, 0x5c, 0x11, 0x00, 0x00, //0x00001c77 jb           LBB0_542
	0x4c, 0x89, 0xe8, //0x00001c7d movq         %r13, %rax
	0x48, 0xf7, 0xd0, //0x00001c80 notq         %rax
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00001c83 .p2align 4, 0x90
	//0x00001c90 LBB0_379
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x0c, //0x00001c90 vmovdqu      (%r12,%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0xcb, //0x00001c96 vpcmpeqb     %ymm3, %ymm0, %ymm1
	0xc5, 0xa5, 0xdb, 0xc0, //0x00001c9a vpand        %ymm0, %ymm11, %ymm0
	0xc5, 0xfd, 0x74, 0xc4, //0x00001c9e vpcmpeqb     %ymm4, %ymm0, %ymm0
	0xc5, 0xfd, 0xeb, 0xc1, //0x00001ca2 vpor         %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00001ca6 vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x00001caa testl        %edx, %edx
	0x0f, 0x85, 0x6c, 0x07, 0x00, 0x00, //0x00001cac jne          LBB0_448
	0x48, 0x83, 0xc1, 0x20, //0x00001cb2 addq         $32, %rcx
	0x49, 0x8d, 0x14, 0x07, //0x00001cb6 leaq         (%r15,%rax), %rdx
	0x48, 0x83, 0xc2, 0xe0, //0x00001cba addq         $-32, %rdx
	0x48, 0x83, 0xc0, 0xe0, //0x00001cbe addq         $-32, %rax
	0x48, 0x83, 0xfa, 0x1f, //0x00001cc2 cmpq         $31, %rdx
	0x0f, 0x87, 0xc4, 0xff, 0xff, 0xff, //0x00001cc6 ja           LBB0_379
	0x4c, 0x89, 0xe1, //0x00001ccc movq         %r12, %rcx
	0x48, 0x29, 0xc1, //0x00001ccf subq         %rax, %rcx
	0x49, 0x01, 0xc7, //0x00001cd2 addq         %rax, %r15
	0x4c, 0x89, 0xf8, //0x00001cd5 movq         %r15, %rax
	0x48, 0x83, 0xf8, 0x10, //0x00001cd8 cmpq         $16, %rax
	0x48, 0x8b, 0x7c, 0x24, 0x08, //0x00001cdc movq         $8(%rsp), %rdi
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00001ce1 movabsq      $4294977024, %r15
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x00001ceb jb           LBB0_385
	//0x00001cf1 LBB0_382
	0x4c, 0x89, 0xe2, //0x00001cf1 movq         %r12, %rdx
	0x48, 0x29, 0xca, //0x00001cf4 subq         %rcx, %rdx
	//0x00001cf7 LBB0_383
	0xc5, 0xfa, 0x6f, 0x01, //0x00001cf7 vmovdqu      (%rcx), %xmm0
	0xc5, 0xf9, 0x74, 0x0d, 0x0d, 0xe3, 0xff, 0xff, //0x00001cfb vpcmpeqb     $-7411(%rip), %xmm0, %xmm1  /* LCPI0_11+0(%rip) */
	0xc5, 0xf9, 0xdb, 0x05, 0x15, 0xe3, 0xff, 0xff, //0x00001d03 vpand        $-7403(%rip), %xmm0, %xmm0  /* LCPI0_12+0(%rip) */
	0xc5, 0xf9, 0x74, 0x05, 0x1d, 0xe3, 0xff, 0xff, //0x00001d0b vpcmpeqb     $-7395(%rip), %xmm0, %xmm0  /* LCPI0_13+0(%rip) */
	0xc5, 0xf9, 0xeb, 0xc1, //0x00001d13 vpor         %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xd7, 0xf0, //0x00001d17 vpmovmskb    %xmm0, %esi
	0x85, 0xf6, //0x00001d1b testl        %esi, %esi
	0x0f, 0x85, 0x0a, 0x09, 0x00, 0x00, //0x00001d1d jne          LBB0_477
	0x48, 0x83, 0xc1, 0x10, //0x00001d23 addq         $16, %rcx
	0x48, 0x83, 0xc0, 0xf0, //0x00001d27 addq         $-16, %rax
	0x48, 0x83, 0xc2, 0xf0, //0x00001d2b addq         $-16, %rdx
	0x48, 0x83, 0xf8, 0x0f, //0x00001d2f cmpq         $15, %rax
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x00001d33 ja           LBB0_383
	//0x00001d39 LBB0_385
	0xc5, 0x7d, 0x7f, 0xe1, //0x00001d39 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00001d3d vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00001d41 vmovdqa      %ymm13, %ymm10
	0x48, 0x85, 0xc0, //0x00001d46 testq        %rax, %rax
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00001d49 je           LBB0_392
	0x48, 0x8d, 0x34, 0x01, //0x00001d4f leaq         (%rcx,%rax), %rsi
	0x31, 0xd2, //0x00001d53 xorl         %edx, %edx
	//0x00001d55 LBB0_387
	0x0f, 0xb6, 0x1c, 0x11, //0x00001d55 movzbl       (%rcx,%rdx), %ebx
	0x80, 0xfb, 0x2c, //0x00001d59 cmpb         $44, %bl
	0x0f, 0x84, 0x2d, 0x11, 0x00, 0x00, //0x00001d5c je           LBB0_548
	0x80, 0xfb, 0x7d, //0x00001d62 cmpb         $125, %bl
	0x0f, 0x84, 0x24, 0x11, 0x00, 0x00, //0x00001d65 je           LBB0_548
	0x80, 0xfb, 0x5d, //0x00001d6b cmpb         $93, %bl
	0x0f, 0x84, 0x1b, 0x11, 0x00, 0x00, //0x00001d6e je           LBB0_548
	0x48, 0x83, 0xc2, 0x01, //0x00001d74 addq         $1, %rdx
	0x48, 0x39, 0xd0, //0x00001d78 cmpq         %rdx, %rax
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x00001d7b jne          LBB0_387
	0x48, 0x89, 0xf1, //0x00001d81 movq         %rsi, %rcx
	//0x00001d84 LBB0_392
	0x4c, 0x29, 0xe1, //0x00001d84 subq         %r12, %rcx
	0xe9, 0x09, 0x11, 0x00, 0x00, //0x00001d87 jmp          LBB0_549
	//0x00001d8c LBB0_393
	0x4c, 0x89, 0x4c, 0x24, 0x10, //0x00001d8c movq         %r9, $16(%rsp)
	0x4d, 0x8b, 0x43, 0x08, //0x00001d91 movq         $8(%r11), %r8
	0x49, 0x29, 0xc8, //0x00001d95 subq         %rcx, %r8
	0x49, 0x01, 0xcc, //0x00001d98 addq         %rcx, %r12
	0x45, 0x31, 0xdb, //0x00001d9b xorl         %r11d, %r11d
	0x45, 0x31, 0xc9, //0x00001d9e xorl         %r9d, %r9d
	0x45, 0x31, 0xd2, //0x00001da1 xorl         %r10d, %r10d
	0x31, 0xdb, //0x00001da4 xorl         %ebx, %ebx
	0xc4, 0x41, 0x7d, 0x6f, 0xfe, //0x00001da6 vmovdqa      %ymm14, %ymm15
	0x49, 0x83, 0xf8, 0x40, //0x00001dab cmpq         $64, %r8
	0x0f, 0x8d, 0x6c, 0x01, 0x00, 0x00, //0x00001daf jge          LBB0_394
	//0x00001db5 LBB0_403
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00001db5 movq         $8(%rsp), %rsi
	0x4d, 0x85, 0xc0, //0x00001dba testq        %r8, %r8
	0x0f, 0x8e, 0x55, 0x22, 0x00, 0x00, //0x00001dbd jle          LBB0_729
	0xc5, 0x7d, 0x7f, 0xe2, //0x00001dc3 vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd1, //0x00001dc7 vmovdqa      %ymm10, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00001dcb vmovdqa      %ymm13, %ymm10
	0x4c, 0x89, 0xfa, //0x00001dd0 movq         %r15, %rdx
	0xc5, 0xf9, 0xef, 0xc0, //0x00001dd3 vpxor        %xmm0, %xmm0, %xmm0
	0xc5, 0xfe, 0x7f, 0x44, 0x24, 0x60, //0x00001dd7 vmovdqu      %ymm0, $96(%rsp)
	0xc5, 0xfe, 0x7f, 0x44, 0x24, 0x40, //0x00001ddd vmovdqu      %ymm0, $64(%rsp)
	0x44, 0x89, 0xe1, //0x00001de3 movl         %r12d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x00001de6 andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x00001dec cmpl         $4033, %ecx
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x00001df2 jb           LBB0_407
	0x49, 0x83, 0xf8, 0x20, //0x00001df8 cmpq         $32, %r8
	0x0f, 0x82, 0x43, 0x00, 0x00, 0x00, //0x00001dfc jb           LBB0_408
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x00001e02 vmovdqu      (%r12), %ymm0
	0xc5, 0xfe, 0x7f, 0x44, 0x24, 0x40, //0x00001e08 vmovdqu      %ymm0, $64(%rsp)
	0x49, 0x83, 0xc4, 0x20, //0x00001e0e addq         $32, %r12
	0x49, 0x8d, 0x78, 0xe0, //0x00001e12 leaq         $-32(%r8), %rdi
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x00001e16 leaq         $96(%rsp), %rsi
	0xe9, 0x2d, 0x00, 0x00, 0x00, //0x00001e1b jmp          LBB0_409
	//0x00001e20 LBB0_407
	0x49, 0x89, 0xd7, //0x00001e20 movq         %rdx, %r15
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x00001e23 vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0x90, 0xe3, 0xff, 0xff, //0x00001e28 vmovdqu      $-7280(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x68, 0xe3, 0xff, 0xff, //0x00001e30 vmovdqu      $-7320(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd1, //0x00001e38 vmovdqa      %ymm1, %ymm10
	0xc5, 0x7d, 0x6f, 0xe2, //0x00001e3c vmovdqa      %ymm2, %ymm12
	0xe9, 0xdc, 0x00, 0x00, 0x00, //0x00001e40 jmp          LBB0_394
	//0x00001e45 LBB0_408
	0x48, 0x8d, 0x74, 0x24, 0x40, //0x00001e45 leaq         $64(%rsp), %rsi
	0x4c, 0x89, 0xc7, //0x00001e4a movq         %r8, %rdi
	//0x00001e4d LBB0_409
	0x48, 0x83, 0xff, 0x10, //0x00001e4d cmpq         $16, %rdi
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x00001e51 jb           LBB0_410
	0xc4, 0xc1, 0x7a, 0x6f, 0x04, 0x24, //0x00001e57 vmovdqu      (%r12), %xmm0
	0xc5, 0xfa, 0x7f, 0x06, //0x00001e5d vmovdqu      %xmm0, (%rsi)
	0x49, 0x83, 0xc4, 0x10, //0x00001e61 addq         $16, %r12
	0x48, 0x83, 0xc6, 0x10, //0x00001e65 addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x00001e69 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x00001e6d cmpq         $8, %rdi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00001e71 jae          LBB0_417
	//0x00001e77 LBB0_411
	0x48, 0x83, 0xff, 0x04, //0x00001e77 cmpq         $4, %rdi
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x00001e7b jb           LBB0_412
	//0x00001e81 LBB0_418
	0x41, 0x8b, 0x0c, 0x24, //0x00001e81 movl         (%r12), %ecx
	0x89, 0x0e, //0x00001e85 movl         %ecx, (%rsi)
	0x49, 0x83, 0xc4, 0x04, //0x00001e87 addq         $4, %r12
	0x48, 0x83, 0xc6, 0x04, //0x00001e8b addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00001e8f addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x00001e93 cmpq         $2, %rdi
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x00001e97 jae          LBB0_413
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x00001e9d jmp          LBB0_414
	//0x00001ea2 LBB0_410
	0x48, 0x83, 0xff, 0x08, //0x00001ea2 cmpq         $8, %rdi
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x00001ea6 jb           LBB0_411
	//0x00001eac LBB0_417
	0x49, 0x8b, 0x0c, 0x24, //0x00001eac movq         (%r12), %rcx
	0x48, 0x89, 0x0e, //0x00001eb0 movq         %rcx, (%rsi)
	0x49, 0x83, 0xc4, 0x08, //0x00001eb3 addq         $8, %r12
	0x48, 0x83, 0xc6, 0x08, //0x00001eb7 addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00001ebb addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00001ebf cmpq         $4, %rdi
	0x0f, 0x83, 0xb8, 0xff, 0xff, 0xff, //0x00001ec3 jae          LBB0_418
	//0x00001ec9 LBB0_412
	0x48, 0x83, 0xff, 0x02, //0x00001ec9 cmpq         $2, %rdi
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00001ecd jb           LBB0_414
	//0x00001ed3 LBB0_413
	0x41, 0x0f, 0xb7, 0x0c, 0x24, //0x00001ed3 movzwl       (%r12), %ecx
	0x66, 0x89, 0x0e, //0x00001ed8 movw         %cx, (%rsi)
	0x49, 0x83, 0xc4, 0x02, //0x00001edb addq         $2, %r12
	0x48, 0x83, 0xc6, 0x02, //0x00001edf addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x00001ee3 addq         $-2, %rdi
	//0x00001ee7 LBB0_414
	0x4c, 0x89, 0xe1, //0x00001ee7 movq         %r12, %rcx
	0x4c, 0x8d, 0x64, 0x24, 0x40, //0x00001eea leaq         $64(%rsp), %r12
	0x48, 0x85, 0xff, //0x00001eef testq        %rdi, %rdi
	0x49, 0x89, 0xd7, //0x00001ef2 movq         %rdx, %r15
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x00001ef5 vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0xbe, 0xe2, 0xff, 0xff, //0x00001efa vmovdqu      $-7490(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x96, 0xe2, 0xff, 0xff, //0x00001f02 vmovdqu      $-7530(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd1, //0x00001f0a vmovdqa      %ymm1, %ymm10
	0xc5, 0x7d, 0x6f, 0xe2, //0x00001f0e vmovdqa      %ymm2, %ymm12
	0x0f, 0x84, 0x09, 0x00, 0x00, 0x00, //0x00001f12 je           LBB0_394
	0x8a, 0x09, //0x00001f18 movb         (%rcx), %cl
	0x88, 0x0e, //0x00001f1a movb         %cl, (%rsi)
	0x4c, 0x8d, 0x64, 0x24, 0x40, //0x00001f1c leaq         $64(%rsp), %r12
	//0x00001f21 LBB0_394
	0xc4, 0x41, 0x7d, 0x6f, 0xf5, //0x00001f21 vmovdqa      %ymm13, %ymm14
	0xc4, 0xc1, 0x7e, 0x6f, 0x0c, 0x24, //0x00001f26 vmovdqu      (%r12), %ymm1
	0xc4, 0xc1, 0x7e, 0x6f, 0x44, 0x24, 0x20, //0x00001f2c vmovdqu      $32(%r12), %ymm0
	0xc5, 0xf5, 0x74, 0xd7, //0x00001f33 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x00001f37 vpmovmskb    %ymm2, %esi
	0xc5, 0xfd, 0x74, 0xd7, //0x00001f3b vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00001f3f vpmovmskb    %ymm2, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00001f43 shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x00001f47 orq          %rcx, %rsi
	0x48, 0x89, 0xf1, //0x00001f4a movq         %rsi, %rcx
	0xc4, 0x41, 0x7d, 0x6f, 0xe9, //0x00001f4d vmovdqa      %ymm9, %ymm13
	0x4c, 0x09, 0xc9, //0x00001f52 orq          %r9, %rcx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00001f55 jne          LBB0_396
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00001f5b movq         $-1, %rsi
	0x45, 0x31, 0xc9, //0x00001f62 xorl         %r9d, %r9d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00001f65 jmp          LBB0_397
	//0x00001f6a LBB0_396
	0x4c, 0x89, 0xc9, //0x00001f6a movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x00001f6d notq         %rcx
	0x48, 0x21, 0xf1, //0x00001f70 andq         %rsi, %rcx
	0x4c, 0x8d, 0x34, 0x09, //0x00001f73 leaq         (%rcx,%rcx), %r14
	0x4d, 0x09, 0xce, //0x00001f77 orq          %r9, %r14
	0x4c, 0x89, 0xf7, //0x00001f7a movq         %r14, %rdi
	0x48, 0xf7, 0xd7, //0x00001f7d notq         %rdi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00001f80 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd6, //0x00001f8a andq         %rdx, %rsi
	0x48, 0x21, 0xfe, //0x00001f8d andq         %rdi, %rsi
	0x45, 0x31, 0xc9, //0x00001f90 xorl         %r9d, %r9d
	0x48, 0x01, 0xce, //0x00001f93 addq         %rcx, %rsi
	0x41, 0x0f, 0x92, 0xc1, //0x00001f96 setb         %r9b
	0x48, 0x01, 0xf6, //0x00001f9a addq         %rsi, %rsi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00001f9d movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xce, //0x00001fa7 xorq         %rcx, %rsi
	0x4c, 0x21, 0xf6, //0x00001faa andq         %r14, %rsi
	0x48, 0xf7, 0xd6, //0x00001fad notq         %rsi
	//0x00001fb0 LBB0_397
	0xc5, 0xfd, 0x74, 0xd6, //0x00001fb0 vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00001fb4 vpmovmskb    %ymm2, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00001fb8 shlq         $32, %rcx
	0xc5, 0xf5, 0x74, 0xd6, //0x00001fbc vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001fc0 vpmovmskb    %ymm2, %edx
	0x48, 0x09, 0xca, //0x00001fc4 orq          %rcx, %rdx
	0x48, 0x21, 0xf2, //0x00001fc7 andq         %rsi, %rdx
	0xc4, 0xe1, 0xf9, 0x6e, 0xd2, //0x00001fca vmovq        %rdx, %xmm2
	0xc4, 0xe3, 0x69, 0x44, 0x15, 0xc7, 0xe0, 0xff, 0xff, 0x00, //0x00001fcf vpclmulqdq   $0, $-7993(%rip), %xmm2, %xmm2  /* LCPI0_26+0(%rip) */
	0xc4, 0xc1, 0xf9, 0x7e, 0xd6, //0x00001fd9 vmovq        %xmm2, %r14
	0x4d, 0x31, 0xde, //0x00001fde xorq         %r11, %r14
	0xc5, 0x7e, 0x6f, 0x0d, 0x97, 0xe1, 0xff, 0xff, //0x00001fe1 vmovdqu      $-7785(%rip), %ymm9  /* LCPI0_7+0(%rip) */
	0xc5, 0xb5, 0x74, 0xd1, //0x00001fe9 vpcmpeqb     %ymm1, %ymm9, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x00001fed vpmovmskb    %ymm2, %edx
	0xc5, 0xb5, 0x74, 0xd0, //0x00001ff1 vpcmpeqb     %ymm0, %ymm9, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00001ff5 vpmovmskb    %ymm2, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00001ff9 shlq         $32, %rcx
	0x48, 0x09, 0xca, //0x00001ffd orq          %rcx, %rdx
	0x4c, 0x89, 0xf1, //0x00002000 movq         %r14, %rcx
	0x48, 0xf7, 0xd1, //0x00002003 notq         %rcx
	0x48, 0x21, 0xca, //0x00002006 andq         %rcx, %rdx
	0xc5, 0xf5, 0x74, 0xcc, //0x00002009 vpcmpeqb     %ymm4, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xf9, //0x0000200d vpmovmskb    %ymm1, %edi
	0xc5, 0xfd, 0x74, 0xc4, //0x00002011 vpcmpeqb     %ymm4, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00002015 vpmovmskb    %ymm0, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x00002019 shlq         $32, %rsi
	0x48, 0x09, 0xf7, //0x0000201d orq          %rsi, %rdi
	0x48, 0x21, 0xcf, //0x00002020 andq         %rcx, %rdi
	0x0f, 0x84, 0x54, 0x00, 0x00, 0x00, //0x00002023 je           LBB0_401
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002029 movq         $32(%rsp), %r11
	0xc4, 0x41, 0x7d, 0x6f, 0xcd, //0x0000202e vmovdqa      %ymm13, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xee, //0x00002033 vmovdqa      %ymm14, %ymm13
	0xc4, 0x41, 0x7d, 0x6f, 0xf7, //0x00002038 vmovdqa      %ymm15, %ymm14
	0xc5, 0x7e, 0x6f, 0x3d, 0x1b, 0xe2, 0xff, 0xff, //0x0000203d vmovdqu      $-7653(%rip), %ymm15  /* LCPI0_17+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002045 .p2align 4, 0x90
	//0x00002050 LBB0_399
	0x48, 0x8d, 0x4f, 0xff, //0x00002050 leaq         $-1(%rdi), %rcx
	0x48, 0x89, 0xce, //0x00002054 movq         %rcx, %rsi
	0x48, 0x21, 0xd6, //0x00002057 andq         %rdx, %rsi
	0xf3, 0x48, 0x0f, 0xb8, 0xf6, //0x0000205a popcntq      %rsi, %rsi
	0x4c, 0x01, 0xd6, //0x0000205f addq         %r10, %rsi
	0x48, 0x39, 0xde, //0x00002062 cmpq         %rbx, %rsi
	0x0f, 0x86, 0x33, 0x03, 0x00, 0x00, //0x00002065 jbe          LBB0_445
	0x48, 0x83, 0xc3, 0x01, //0x0000206b addq         $1, %rbx
	0x48, 0x21, 0xcf, //0x0000206f andq         %rcx, %rdi
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00002072 jne          LBB0_399
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00002078 jmp          LBB0_402
	//0x0000207d LBB0_401
	0xc4, 0x41, 0x7d, 0x6f, 0xcd, //0x0000207d vmovdqa      %ymm13, %ymm9
	0xc4, 0x41, 0x7d, 0x6f, 0xee, //0x00002082 vmovdqa      %ymm14, %ymm13
	0xc4, 0x41, 0x7d, 0x6f, 0xf7, //0x00002087 vmovdqa      %ymm15, %ymm14
	//0x0000208c LBB0_402
	0x49, 0xc1, 0xfe, 0x3f, //0x0000208c sarq         $63, %r14
	0xf3, 0x48, 0x0f, 0xb8, 0xca, //0x00002090 popcntq      %rdx, %rcx
	0x49, 0x01, 0xca, //0x00002095 addq         %rcx, %r10
	0x49, 0x83, 0xc4, 0x40, //0x00002098 addq         $64, %r12
	0x49, 0x83, 0xc0, 0xc0, //0x0000209c addq         $-64, %r8
	0x4d, 0x89, 0xf3, //0x000020a0 movq         %r14, %r11
	0xc4, 0x41, 0x7d, 0x6f, 0xfe, //0x000020a3 vmovdqa      %ymm14, %ymm15
	0x49, 0x83, 0xf8, 0x40, //0x000020a8 cmpq         $64, %r8
	0x0f, 0x8d, 0x6f, 0xfe, 0xff, 0xff, //0x000020ac jge          LBB0_394
	0xe9, 0xfe, 0xfc, 0xff, 0xff, //0x000020b2 jmp          LBB0_403
	//0x000020b7 LBB0_419
	0x4c, 0x89, 0x4c, 0x24, 0x10, //0x000020b7 movq         %r9, $16(%rsp)
	0x4d, 0x8b, 0x43, 0x08, //0x000020bc movq         $8(%r11), %r8
	0x49, 0x29, 0xc8, //0x000020c0 subq         %rcx, %r8
	0x49, 0x01, 0xcc, //0x000020c3 addq         %rcx, %r12
	0x45, 0x31, 0xdb, //0x000020c6 xorl         %r11d, %r11d
	0x45, 0x31, 0xc9, //0x000020c9 xorl         %r9d, %r9d
	0x45, 0x31, 0xd2, //0x000020cc xorl         %r10d, %r10d
	0x31, 0xdb, //0x000020cf xorl         %ebx, %ebx
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x000020d1 jmp          LBB0_421
	//0x000020d6 LBB0_420
	0x49, 0xc1, 0xfe, 0x3f, //0x000020d6 sarq         $63, %r14
	0xf3, 0x48, 0x0f, 0xb8, 0xca, //0x000020da popcntq      %rdx, %rcx
	0x49, 0x01, 0xca, //0x000020df addq         %rcx, %r10
	0x49, 0x83, 0xc4, 0x40, //0x000020e2 addq         $64, %r12
	0x49, 0x83, 0xc0, 0xc0, //0x000020e6 addq         $-64, %r8
	0x4d, 0x89, 0xf3, //0x000020ea movq         %r14, %r11
	//0x000020ed LBB0_421
	0x49, 0x83, 0xf8, 0x40, //0x000020ed cmpq         $64, %r8
	0x0f, 0x8c, 0x36, 0x01, 0x00, 0x00, //0x000020f1 jl           LBB0_429
	//0x000020f7 LBB0_422
	0xc4, 0xc1, 0x7e, 0x6f, 0x0c, 0x24, //0x000020f7 vmovdqu      (%r12), %ymm1
	0xc4, 0xc1, 0x7e, 0x6f, 0x44, 0x24, 0x20, //0x000020fd vmovdqu      $32(%r12), %ymm0
	0xc5, 0xf5, 0x74, 0xd7, //0x00002104 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x00002108 vpmovmskb    %ymm2, %esi
	0xc5, 0xfd, 0x74, 0xd7, //0x0000210c vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00002110 vpmovmskb    %ymm2, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00002114 shlq         $32, %rcx
	0x48, 0x09, 0xce, //0x00002118 orq          %rcx, %rsi
	0x48, 0x89, 0xf1, //0x0000211b movq         %rsi, %rcx
	0x4c, 0x09, 0xc9, //0x0000211e orq          %r9, %rcx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x00002121 jne          LBB0_424
	0x48, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002127 movq         $-1, %rsi
	0x45, 0x31, 0xc9, //0x0000212e xorl         %r9d, %r9d
	0xe9, 0x46, 0x00, 0x00, 0x00, //0x00002131 jmp          LBB0_425
	//0x00002136 LBB0_424
	0x4c, 0x89, 0xc9, //0x00002136 movq         %r9, %rcx
	0x48, 0xf7, 0xd1, //0x00002139 notq         %rcx
	0x48, 0x21, 0xf1, //0x0000213c andq         %rsi, %rcx
	0x4c, 0x8d, 0x34, 0x09, //0x0000213f leaq         (%rcx,%rcx), %r14
	0x4d, 0x09, 0xce, //0x00002143 orq          %r9, %r14
	0x4c, 0x89, 0xf7, //0x00002146 movq         %r14, %rdi
	0x48, 0xf7, 0xd7, //0x00002149 notq         %rdi
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000214c movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd6, //0x00002156 andq         %rdx, %rsi
	0x48, 0x21, 0xfe, //0x00002159 andq         %rdi, %rsi
	0x45, 0x31, 0xc9, //0x0000215c xorl         %r9d, %r9d
	0x48, 0x01, 0xce, //0x0000215f addq         %rcx, %rsi
	0x41, 0x0f, 0x92, 0xc1, //0x00002162 setb         %r9b
	0x48, 0x01, 0xf6, //0x00002166 addq         %rsi, %rsi
	0x48, 0xb9, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002169 movabsq      $6148914691236517205, %rcx
	0x48, 0x31, 0xce, //0x00002173 xorq         %rcx, %rsi
	0x4c, 0x21, 0xf6, //0x00002176 andq         %r14, %rsi
	0x48, 0xf7, 0xd6, //0x00002179 notq         %rsi
	//0x0000217c LBB0_425
	0xc5, 0xfd, 0x74, 0xd6, //0x0000217c vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x00002180 vpmovmskb    %ymm2, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00002184 shlq         $32, %rcx
	0xc5, 0xf5, 0x74, 0xd6, //0x00002188 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x0000218c vpmovmskb    %ymm2, %edx
	0x48, 0x09, 0xca, //0x00002190 orq          %rcx, %rdx
	0x48, 0x21, 0xf2, //0x00002193 andq         %rsi, %rdx
	0xc4, 0xe1, 0xf9, 0x6e, 0xd2, //0x00002196 vmovq        %rdx, %xmm2
	0xc4, 0xe3, 0x69, 0x44, 0x15, 0xfb, 0xde, 0xff, 0xff, 0x00, //0x0000219b vpclmulqdq   $0, $-8453(%rip), %xmm2, %xmm2  /* LCPI0_26+0(%rip) */
	0xc4, 0xc1, 0xf9, 0x7e, 0xd6, //0x000021a5 vmovq        %xmm2, %r14
	0x4d, 0x31, 0xde, //0x000021aa xorq         %r11, %r14
	0xc5, 0xad, 0x74, 0xd1, //0x000021ad vpcmpeqb     %ymm1, %ymm10, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x000021b1 vpmovmskb    %ymm2, %edx
	0xc5, 0xad, 0x74, 0xd0, //0x000021b5 vpcmpeqb     %ymm0, %ymm10, %ymm2
	0xc5, 0xfd, 0xd7, 0xca, //0x000021b9 vpmovmskb    %ymm2, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x000021bd shlq         $32, %rcx
	0x48, 0x09, 0xca, //0x000021c1 orq          %rcx, %rdx
	0x4c, 0x89, 0xf1, //0x000021c4 movq         %r14, %rcx
	0x48, 0xf7, 0xd1, //0x000021c7 notq         %rcx
	0x48, 0x21, 0xca, //0x000021ca andq         %rcx, %rdx
	0xc5, 0x9d, 0x74, 0xc9, //0x000021cd vpcmpeqb     %ymm1, %ymm12, %ymm1
	0xc5, 0xfd, 0xd7, 0xf9, //0x000021d1 vpmovmskb    %ymm1, %edi
	0xc5, 0x9d, 0x74, 0xc0, //0x000021d5 vpcmpeqb     %ymm0, %ymm12, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x000021d9 vpmovmskb    %ymm0, %esi
	0x48, 0xc1, 0xe6, 0x20, //0x000021dd shlq         $32, %rsi
	0x48, 0x09, 0xf7, //0x000021e1 orq          %rsi, %rdi
	0x48, 0x21, 0xcf, //0x000021e4 andq         %rcx, %rdi
	0x0f, 0x84, 0xe9, 0xfe, 0xff, 0xff, //0x000021e7 je           LBB0_420
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000021ed movq         $32(%rsp), %r11
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000021f2 .p2align 4, 0x90
	//0x00002200 LBB0_427
	0x48, 0x8d, 0x4f, 0xff, //0x00002200 leaq         $-1(%rdi), %rcx
	0x48, 0x89, 0xce, //0x00002204 movq         %rcx, %rsi
	0x48, 0x21, 0xd6, //0x00002207 andq         %rdx, %rsi
	0xf3, 0x48, 0x0f, 0xb8, 0xf6, //0x0000220a popcntq      %rsi, %rsi
	0x4c, 0x01, 0xd6, //0x0000220f addq         %r10, %rsi
	0x48, 0x39, 0xde, //0x00002212 cmpq         %rbx, %rsi
	0x0f, 0x86, 0x83, 0x01, 0x00, 0x00, //0x00002215 jbe          LBB0_445
	0x48, 0x83, 0xc3, 0x01, //0x0000221b addq         $1, %rbx
	0x48, 0x21, 0xcf, //0x0000221f andq         %rcx, %rdi
	0x0f, 0x85, 0xd8, 0xff, 0xff, 0xff, //0x00002222 jne          LBB0_427
	0xe9, 0xa9, 0xfe, 0xff, 0xff, //0x00002228 jmp          LBB0_420
	//0x0000222d LBB0_429
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x0000222d movq         $8(%rsp), %rsi
	0x4d, 0x85, 0xc0, //0x00002232 testq        %r8, %r8
	0x0f, 0x8e, 0xdd, 0x1d, 0x00, 0x00, //0x00002235 jle          LBB0_729
	0xc5, 0x7d, 0x7f, 0xe2, //0x0000223b vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd1, //0x0000223f vmovdqa      %ymm10, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00002243 vmovdqa      %ymm13, %ymm10
	0x4c, 0x89, 0xfa, //0x00002248 movq         %r15, %rdx
	0xc5, 0xf9, 0xef, 0xc0, //0x0000224b vpxor        %xmm0, %xmm0, %xmm0
	0xc5, 0xfe, 0x7f, 0x44, 0x24, 0x60, //0x0000224f vmovdqu      %ymm0, $96(%rsp)
	0xc5, 0xfe, 0x7f, 0x44, 0x24, 0x40, //0x00002255 vmovdqu      %ymm0, $64(%rsp)
	0x44, 0x89, 0xe1, //0x0000225b movl         %r12d, %ecx
	0x81, 0xe1, 0xff, 0x0f, 0x00, 0x00, //0x0000225e andl         $4095, %ecx
	0x81, 0xf9, 0xc1, 0x0f, 0x00, 0x00, //0x00002264 cmpl         $4033, %ecx
	0x0f, 0x82, 0x28, 0x00, 0x00, 0x00, //0x0000226a jb           LBB0_433
	0x49, 0x83, 0xf8, 0x20, //0x00002270 cmpq         $32, %r8
	0x0f, 0x82, 0x43, 0x00, 0x00, 0x00, //0x00002274 jb           LBB0_434
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x24, //0x0000227a vmovdqu      (%r12), %ymm0
	0xc5, 0xfe, 0x7f, 0x44, 0x24, 0x40, //0x00002280 vmovdqu      %ymm0, $64(%rsp)
	0x49, 0x83, 0xc4, 0x20, //0x00002286 addq         $32, %r12
	0x49, 0x8d, 0x78, 0xe0, //0x0000228a leaq         $-32(%r8), %rdi
	0x48, 0x8d, 0x74, 0x24, 0x60, //0x0000228e leaq         $96(%rsp), %rsi
	0xe9, 0x2d, 0x00, 0x00, 0x00, //0x00002293 jmp          LBB0_435
	//0x00002298 LBB0_433
	0x49, 0x89, 0xd7, //0x00002298 movq         %rdx, %r15
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x0000229b vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0x18, 0xdf, 0xff, 0xff, //0x000022a0 vmovdqu      $-8424(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xf0, 0xde, 0xff, 0xff, //0x000022a8 vmovdqu      $-8464(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd1, //0x000022b0 vmovdqa      %ymm1, %ymm10
	0xc5, 0x7d, 0x6f, 0xe2, //0x000022b4 vmovdqa      %ymm2, %ymm12
	0xe9, 0x3a, 0xfe, 0xff, 0xff, //0x000022b8 jmp          LBB0_422
	//0x000022bd LBB0_434
	0x48, 0x8d, 0x74, 0x24, 0x40, //0x000022bd leaq         $64(%rsp), %rsi
	0x4c, 0x89, 0xc7, //0x000022c2 movq         %r8, %rdi
	//0x000022c5 LBB0_435
	0x48, 0x83, 0xff, 0x10, //0x000022c5 cmpq         $16, %rdi
	0x0f, 0x82, 0x4b, 0x00, 0x00, 0x00, //0x000022c9 jb           LBB0_436
	0xc4, 0xc1, 0x7a, 0x6f, 0x04, 0x24, //0x000022cf vmovdqu      (%r12), %xmm0
	0xc5, 0xfa, 0x7f, 0x06, //0x000022d5 vmovdqu      %xmm0, (%rsi)
	0x49, 0x83, 0xc4, 0x10, //0x000022d9 addq         $16, %r12
	0x48, 0x83, 0xc6, 0x10, //0x000022dd addq         $16, %rsi
	0x48, 0x83, 0xc7, 0xf0, //0x000022e1 addq         $-16, %rdi
	0x48, 0x83, 0xff, 0x08, //0x000022e5 cmpq         $8, %rdi
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x000022e9 jae          LBB0_443
	//0x000022ef LBB0_437
	0x48, 0x83, 0xff, 0x04, //0x000022ef cmpq         $4, %rdi
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x000022f3 jb           LBB0_438
	//0x000022f9 LBB0_444
	0x41, 0x8b, 0x0c, 0x24, //0x000022f9 movl         (%r12), %ecx
	0x89, 0x0e, //0x000022fd movl         %ecx, (%rsi)
	0x49, 0x83, 0xc4, 0x04, //0x000022ff addq         $4, %r12
	0x48, 0x83, 0xc6, 0x04, //0x00002303 addq         $4, %rsi
	0x48, 0x83, 0xc7, 0xfc, //0x00002307 addq         $-4, %rdi
	0x48, 0x83, 0xff, 0x02, //0x0000230b cmpq         $2, %rdi
	0x0f, 0x83, 0x36, 0x00, 0x00, 0x00, //0x0000230f jae          LBB0_439
	0xe9, 0x45, 0x00, 0x00, 0x00, //0x00002315 jmp          LBB0_440
	//0x0000231a LBB0_436
	0x48, 0x83, 0xff, 0x08, //0x0000231a cmpq         $8, %rdi
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x0000231e jb           LBB0_437
	//0x00002324 LBB0_443
	0x49, 0x8b, 0x0c, 0x24, //0x00002324 movq         (%r12), %rcx
	0x48, 0x89, 0x0e, //0x00002328 movq         %rcx, (%rsi)
	0x49, 0x83, 0xc4, 0x08, //0x0000232b addq         $8, %r12
	0x48, 0x83, 0xc6, 0x08, //0x0000232f addq         $8, %rsi
	0x48, 0x83, 0xc7, 0xf8, //0x00002333 addq         $-8, %rdi
	0x48, 0x83, 0xff, 0x04, //0x00002337 cmpq         $4, %rdi
	0x0f, 0x83, 0xb8, 0xff, 0xff, 0xff, //0x0000233b jae          LBB0_444
	//0x00002341 LBB0_438
	0x48, 0x83, 0xff, 0x02, //0x00002341 cmpq         $2, %rdi
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00002345 jb           LBB0_440
	//0x0000234b LBB0_439
	0x41, 0x0f, 0xb7, 0x0c, 0x24, //0x0000234b movzwl       (%r12), %ecx
	0x66, 0x89, 0x0e, //0x00002350 movw         %cx, (%rsi)
	0x49, 0x83, 0xc4, 0x02, //0x00002353 addq         $2, %r12
	0x48, 0x83, 0xc6, 0x02, //0x00002357 addq         $2, %rsi
	0x48, 0x83, 0xc7, 0xfe, //0x0000235b addq         $-2, %rdi
	//0x0000235f LBB0_440
	0x4c, 0x89, 0xe1, //0x0000235f movq         %r12, %rcx
	0x4c, 0x8d, 0x64, 0x24, 0x40, //0x00002362 leaq         $64(%rsp), %r12
	0x48, 0x85, 0xff, //0x00002367 testq        %rdi, %rdi
	0x49, 0x89, 0xd7, //0x0000236a movq         %rdx, %r15
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x0000236d vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0x46, 0xde, 0xff, 0xff, //0x00002372 vmovdqu      $-8634(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x1e, 0xde, 0xff, 0xff, //0x0000237a vmovdqu      $-8674(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd1, //0x00002382 vmovdqa      %ymm1, %ymm10
	0xc5, 0x7d, 0x6f, 0xe2, //0x00002386 vmovdqa      %ymm2, %ymm12
	0x0f, 0x84, 0x67, 0xfd, 0xff, 0xff, //0x0000238a je           LBB0_422
	0x8a, 0x09, //0x00002390 movb         (%rcx), %cl
	0x88, 0x0e, //0x00002392 movb         %cl, (%rsi)
	0x4c, 0x8d, 0x64, 0x24, 0x40, //0x00002394 leaq         $64(%rsp), %r12
	0xe9, 0x59, 0xfd, 0xff, 0xff, //0x00002399 jmp          LBB0_422
	//0x0000239e LBB0_445
	0x49, 0x8b, 0x4b, 0x08, //0x0000239e movq         $8(%r11), %rcx
	0x48, 0x0f, 0xbc, 0xd7, //0x000023a2 bsfq         %rdi, %rdx
	0x4c, 0x29, 0xc2, //0x000023a6 subq         %r8, %rdx
	0x48, 0x01, 0xd1, //0x000023a9 addq         %rdx, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x000023ac addq         $1, %rcx
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x000023b0 movq         $8(%rsp), %rsi
	0x48, 0x89, 0x0e, //0x000023b5 movq         %rcx, (%rsi)
	0x49, 0x8b, 0x53, 0x08, //0x000023b8 movq         $8(%r11), %rdx
	0x48, 0x39, 0xd1, //0x000023bc cmpq         %rdx, %rcx
	0x48, 0x0f, 0x46, 0xd1, //0x000023bf cmovbeq      %rcx, %rdx
	0x48, 0x89, 0x16, //0x000023c3 movq         %rdx, (%rsi)
	0x0f, 0x87, 0x31, 0x1a, 0x00, 0x00, //0x000023c6 ja           LBB0_721
	0x4c, 0x89, 0xe8, //0x000023cc movq         %r13, %rax
	0x48, 0xba, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x000023cf movabsq      $9223372036854775806, %rdx
	0x49, 0x39, 0xd5, //0x000023d9 cmpq         %rdx, %r13
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000023dc movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x000023e1 movq         $40(%rsp), %r14
	0x4c, 0x8b, 0x4c, 0x24, 0x10, //0x000023e6 movq         $16(%rsp), %r9
	0x0f, 0x86, 0xb0, 0xdf, 0xff, 0xff, //0x000023eb jbe          LBB0_4
	0xe9, 0x07, 0x1a, 0x00, 0x00, //0x000023f1 jmp          LBB0_721
	//0x000023f6 LBB0_447
	0x41, 0x0f, 0xbc, 0xc1, //0x000023f6 bsfl         %r9d, %eax
	0x4c, 0x01, 0xe8, //0x000023fa addq         %r13, %rax
	0x49, 0x8d, 0x0c, 0x02, //0x000023fd leaq         (%r10,%rax), %rcx
	0x48, 0x83, 0xc1, 0x02, //0x00002401 addq         $2, %rcx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002405 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000240a movq         $32(%rsp), %r11
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x0000240f movq         $40(%rsp), %r14
	0x4c, 0x8b, 0x4c, 0x24, 0x10, //0x00002414 movq         $16(%rsp), %r9
	0xe9, 0x55, 0xea, 0xff, 0xff, //0x00002419 jmp          LBB0_159
	//0x0000241e LBB0_448
	0x0f, 0xbc, 0xca, //0x0000241e bsfl         %edx, %ecx
	0x48, 0x29, 0xc1, //0x00002421 subq         %rax, %rcx
	0x48, 0x8b, 0x7c, 0x24, 0x08, //0x00002424 movq         $8(%rsp), %rdi
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002429 movabsq      $4294977024, %r15
	0xe9, 0x61, 0xeb, 0xff, 0xff, //0x00002433 jmp          LBB0_182
	//0x00002438 LBB0_449
	0x49, 0x8d, 0x48, 0xff, //0x00002438 leaq         $-1(%r8), %rcx
	0x49, 0x39, 0xcf, //0x0000243c cmpq         %rcx, %r15
	0x49, 0xf7, 0xd0, //0x0000243f notq         %r8
	0x4d, 0x0f, 0x45, 0xc3, //0x00002442 cmovneq      %r11, %r8
	0x84, 0xc0, //0x00002446 testb        %al, %al
	0x4d, 0x0f, 0x45, 0xd8, //0x00002448 cmovneq      %r8, %r11
	0xe9, 0x65, 0x06, 0x00, 0x00, //0x0000244c jmp          LBB0_506
	//0x00002451 LBB0_450
	0x4d, 0x89, 0xf3, //0x00002451 movq         %r14, %r11
	0x49, 0x29, 0xcb, //0x00002454 subq         %rcx, %r11
	0x0f, 0x84, 0x9c, 0x1b, 0x00, 0x00, //0x00002457 je           LBB0_727
	0x49, 0x83, 0xfb, 0x40, //0x0000245d cmpq         $64, %r11
	0x0f, 0x82, 0xf0, 0x09, 0x00, 0x00, //0x00002461 jb           LBB0_546
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00002467 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xd2, //0x00002470 xorl         %r10d, %r10d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002473 .p2align 4, 0x90
	//0x00002480 LBB0_453
	0xc4, 0xc1, 0x7e, 0x6f, 0x04, 0x0c, //0x00002480 vmovdqu      (%r12,%rcx), %ymm0
	0xc4, 0xc1, 0x7e, 0x6f, 0x4c, 0x0c, 0x20, //0x00002486 vmovdqu      $32(%r12,%rcx), %ymm1
	0xc5, 0xfd, 0x74, 0xd6, //0x0000248d vpcmpeqb     %ymm6, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xfa, //0x00002491 vpmovmskb    %ymm2, %edi
	0xc5, 0xf5, 0x74, 0xd6, //0x00002495 vpcmpeqb     %ymm6, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xc2, //0x00002499 vpmovmskb    %ymm2, %eax
	0xc5, 0xfd, 0x74, 0xd7, //0x0000249d vpcmpeqb     %ymm7, %ymm0, %ymm2
	0xc5, 0xfd, 0xd7, 0xd2, //0x000024a1 vpmovmskb    %ymm2, %edx
	0xc5, 0xf5, 0x74, 0xd7, //0x000024a5 vpcmpeqb     %ymm7, %ymm1, %ymm2
	0xc5, 0xfd, 0xd7, 0xda, //0x000024a9 vpmovmskb    %ymm2, %ebx
	0xc5, 0xbd, 0xda, 0xd1, //0x000024ad vpminub      %ymm1, %ymm8, %ymm2
	0xc5, 0xf5, 0x74, 0xca, //0x000024b1 vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x000024b5 vpmovmskb    %ymm1, %esi
	0x48, 0xc1, 0xe0, 0x20, //0x000024b9 shlq         $32, %rax
	0x48, 0x09, 0xc7, //0x000024bd orq          %rax, %rdi
	0x48, 0xc1, 0xe3, 0x20, //0x000024c0 shlq         $32, %rbx
	0x48, 0xc1, 0xe6, 0x20, //0x000024c4 shlq         $32, %rsi
	0x48, 0x09, 0xda, //0x000024c8 orq          %rbx, %rdx
	0x0f, 0x85, 0x44, 0x00, 0x00, 0x00, //0x000024cb jne          LBB0_464
	0x4d, 0x85, 0xd2, //0x000024d1 testq        %r10, %r10
	0x0f, 0x85, 0x53, 0x00, 0x00, 0x00, //0x000024d4 jne          LBB0_466
	0x45, 0x31, 0xd2, //0x000024da xorl         %r10d, %r10d
	//0x000024dd LBB0_456
	0xc5, 0xbd, 0xda, 0xc8, //0x000024dd vpminub      %ymm0, %ymm8, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x000024e1 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xc0, //0x000024e5 vpmovmskb    %ymm0, %eax
	0x48, 0x09, 0xc6, //0x000024e9 orq          %rax, %rsi
	0x48, 0x85, 0xff, //0x000024ec testq        %rdi, %rdi
	0x0f, 0x85, 0x8b, 0x00, 0x00, 0x00, //0x000024ef jne          LBB0_467
	0x48, 0x85, 0xf6, //0x000024f5 testq        %rsi, %rsi
	0x0f, 0x85, 0xb2, 0x1a, 0x00, 0x00, //0x000024f8 jne          LBB0_722
	0x49, 0x83, 0xc3, 0xc0, //0x000024fe addq         $-64, %r11
	0x48, 0x83, 0xc1, 0x40, //0x00002502 addq         $64, %rcx
	0x49, 0x83, 0xfb, 0x3f, //0x00002506 cmpq         $63, %r11
	0x0f, 0x87, 0x70, 0xff, 0xff, 0xff, //0x0000250a ja           LBB0_453
	0xe9, 0xb8, 0x06, 0x00, 0x00, //0x00002510 jmp          LBB0_459
	//0x00002515 LBB0_464
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00002515 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x0c, 0x00, 0x00, 0x00, //0x0000251b jne          LBB0_466
	0x48, 0x0f, 0xbc, 0xc2, //0x00002521 bsfq         %rdx, %rax
	0x48, 0x01, 0xc8, //0x00002525 addq         %rcx, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00002528 movq         %rax, $16(%rsp)
	//0x0000252d LBB0_466
	0x4c, 0x89, 0xd0, //0x0000252d movq         %r10, %rax
	0x48, 0xf7, 0xd0, //0x00002530 notq         %rax
	0x48, 0x21, 0xd0, //0x00002533 andq         %rdx, %rax
	0x4c, 0x8d, 0x04, 0x00, //0x00002536 leaq         (%rax,%rax), %r8
	0x4d, 0x09, 0xd0, //0x0000253a orq          %r10, %r8
	0x4c, 0x89, 0xc3, //0x0000253d movq         %r8, %rbx
	0x48, 0xf7, 0xd3, //0x00002540 notq         %rbx
	0x48, 0x21, 0xd3, //0x00002543 andq         %rdx, %rbx
	0x48, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002546 movabsq      $-6148914691236517206, %rdx
	0x48, 0x21, 0xd3, //0x00002550 andq         %rdx, %rbx
	0x45, 0x31, 0xd2, //0x00002553 xorl         %r10d, %r10d
	0x48, 0x01, 0xc3, //0x00002556 addq         %rax, %rbx
	0x41, 0x0f, 0x92, 0xc2, //0x00002559 setb         %r10b
	0x48, 0x01, 0xdb, //0x0000255d addq         %rbx, %rbx
	0x48, 0xb8, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00002560 movabsq      $6148914691236517205, %rax
	0x48, 0x31, 0xc3, //0x0000256a xorq         %rax, %rbx
	0x4c, 0x21, 0xc3, //0x0000256d andq         %r8, %rbx
	0x48, 0xf7, 0xd3, //0x00002570 notq         %rbx
	0x48, 0x21, 0xdf, //0x00002573 andq         %rbx, %rdi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002576 movq         $24(%rsp), %r8
	0xe9, 0x5d, 0xff, 0xff, 0xff, //0x0000257b jmp          LBB0_456
	//0x00002580 LBB0_467
	0x48, 0x0f, 0xbc, 0xc7, //0x00002580 bsfq         %rdi, %rax
	0x48, 0x85, 0xf6, //0x00002584 testq        %rsi, %rsi
	0x0f, 0x84, 0x4d, 0x00, 0x00, 0x00, //0x00002587 je           LBB0_472
	0x48, 0x0f, 0xbc, 0xd6, //0x0000258d bsfq         %rsi, %rdx
	0xe9, 0x49, 0x00, 0x00, 0x00, //0x00002591 jmp          LBB0_473
	//0x00002596 LBB0_469
	0x48, 0x01, 0xd9, //0x00002596 addq         %rbx, %rcx
	0x4c, 0x01, 0xd1, //0x00002599 addq         %r10, %rcx
	0xc5, 0xf8, 0x77, //0x0000259c vzeroupper   
	0x48, 0x89, 0xcb, //0x0000259f movq         %rcx, %rbx
	0xe9, 0x55, 0x01, 0x00, 0x00, //0x000025a2 jmp          LBB0_482
	//0x000025a7 LBB0_470
	0x4d, 0x29, 0xe5, //0x000025a7 subq         %r12, %r13
	0x49, 0x01, 0xcd, //0x000025aa addq         %rcx, %r13
	0x49, 0x39, 0xc5, //0x000025ad cmpq         %rax, %r13
	0x0f, 0x82, 0xa4, 0xdf, 0xff, 0xff, //0x000025b0 jb           LBB0_36
	0xe9, 0xde, 0x17, 0x00, 0x00, //0x000025b6 jmp          LBB0_694
	//0x000025bb LBB0_471
	0x4c, 0x8b, 0x5c, 0x24, 0x30, //0x000025bb movq         $48(%rsp), %r11
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000025c0 movq         $-1, %rax
	0x4d, 0x85, 0xff, //0x000025c7 testq        %r15, %r15
	0x48, 0x8b, 0x4c, 0x24, 0x10, //0x000025ca movq         $16(%rsp), %rcx
	0x0f, 0x85, 0xda, 0xf3, 0xff, 0xff, //0x000025cf jne          LBB0_347
	0xe9, 0x0a, 0x18, 0x00, 0x00, //0x000025d5 jmp          LBB0_700
	//0x000025da LBB0_472
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000025da movl         $64, %edx
	//0x000025df LBB0_473
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000025df movq         $32(%rsp), %r11
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x000025e4 movq         $8(%rsp), %rsi
	0x48, 0x39, 0xc2, //0x000025e9 cmpq         %rax, %rdx
	0x0f, 0x82, 0x14, 0x1a, 0x00, 0x00, //0x000025ec jb           LBB0_728
	0xc5, 0x7d, 0x7f, 0xea, //0x000025f2 vmovdqa      %ymm13, %ymm2
	0x48, 0x01, 0xc1, //0x000025f6 addq         %rax, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x000025f9 addq         $1, %rcx
	//0x000025fd LBB0_475
	0x48, 0x85, 0xc9, //0x000025fd testq        %rcx, %rcx
	0x0f, 0x88, 0x6b, 0x19, 0x00, 0x00, //0x00002600 js           LBB0_715
	0x48, 0x89, 0x0e, //0x00002606 movq         %rcx, (%rsi)
	0x4c, 0x89, 0xe8, //0x00002609 movq         %r13, %rax
	0x48, 0xba, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x0000260c movabsq      $9223372036854775806, %rdx
	0x49, 0x39, 0xd5, //0x00002616 cmpq         %rdx, %r13
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x00002619 movq         $40(%rsp), %r14
	0xc5, 0x7d, 0x6f, 0xea, //0x0000261e vmovdqa      %ymm2, %ymm13
	0x0f, 0x86, 0x79, 0xdd, 0xff, 0xff, //0x00002622 jbe          LBB0_4
	0xe9, 0xd0, 0x17, 0x00, 0x00, //0x00002628 jmp          LBB0_721
	//0x0000262d LBB0_477
	0x66, 0x0f, 0xbc, 0xc6, //0x0000262d bsfw         %si, %ax
	0x0f, 0xb7, 0xc8, //0x00002631 movzwl       %ax, %ecx
	0x48, 0x29, 0xd1, //0x00002634 subq         %rdx, %rcx
	0xe9, 0x5d, 0xe9, 0xff, 0xff, //0x00002637 jmp          LBB0_182
	//0x0000263c LBB0_162
	0x4d, 0x85, 0xdb, //0x0000263c testq        %r11, %r11
	0x0f, 0x85, 0x6e, 0x08, 0x00, 0x00, //0x0000263f jne          LBB0_550
	0x4b, 0x8d, 0x0c, 0x02, //0x00002645 leaq         (%r10,%r8), %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00002649 addq         $1, %rcx
	0x49, 0xf7, 0xd2, //0x0000264d notq         %r10
	0x4d, 0x01, 0xf2, //0x00002650 addq         %r14, %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002653 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002658 movq         $32(%rsp), %r11
	0x4d, 0x89, 0xf9, //0x0000265d movq         %r15, %r9
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002660 movabsq      $4294977024, %r15
	//0x0000266a LBB0_164
	0x4d, 0x85, 0xd2, //0x0000266a testq        %r10, %r10
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x0000266d movq         $40(%rsp), %r14
	0x0f, 0x8f, 0x27, 0x00, 0x00, 0x00, //0x00002672 jg           LBB0_216
	0xe9, 0x80, 0x17, 0x00, 0x00, //0x00002678 jmp          LBB0_721
	0x90, 0x90, 0x90, //0x0000267d .p2align 4, 0x90
	//0x00002680 LBB0_214
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00002680 movq         $-2, %rdx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00002687 movl         $2, %eax
	0x48, 0x01, 0xc1, //0x0000268c addq         %rax, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000268f movq         $-1, %rax
	0x49, 0x01, 0xd2, //0x00002696 addq         %rdx, %r10
	0x0f, 0x8e, 0x5e, 0x17, 0x00, 0x00, //0x00002699 jle          LBB0_721
	//0x0000269f LBB0_216
	0x0f, 0xb6, 0x01, //0x0000269f movzbl       (%rcx), %eax
	0x3c, 0x5c, //0x000026a2 cmpb         $92, %al
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x000026a4 je           LBB0_214
	0x3c, 0x22, //0x000026aa cmpb         $34, %al
	0x0f, 0x84, 0xd0, 0x12, 0x00, 0x00, //0x000026ac je           LBB0_647
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000026b2 movq         $-1, %rdx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000026b9 movl         $1, %eax
	0x48, 0x01, 0xc1, //0x000026be addq         %rax, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000026c1 movq         $-1, %rax
	0x49, 0x01, 0xd2, //0x000026c8 addq         %rdx, %r10
	0x0f, 0x8f, 0xce, 0xff, 0xff, 0xff, //0x000026cb jg           LBB0_216
	0xe9, 0x27, 0x17, 0x00, 0x00, //0x000026d1 jmp          LBB0_721
	//0x000026d6 LBB0_478
	0xc5, 0x7d, 0x7f, 0xe8, //0x000026d6 vmovdqa      %ymm13, %ymm0
	0x0f, 0xbc, 0xc2, //0x000026da bsfl         %edx, %eax
	0xe9, 0xce, 0x03, 0x00, 0x00, //0x000026dd jmp          LBB0_505
	//0x000026e2 LBB0_479
	0x89, 0xc8, //0x000026e2 movl         %ecx, %eax
	0x48, 0x01, 0xc3, //0x000026e4 addq         %rax, %rbx
	0x4c, 0x01, 0xd3, //0x000026e7 addq         %r10, %rbx
	0x4c, 0x8b, 0x4c, 0x24, 0x10, //0x000026ea movq         $16(%rsp), %r9
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x000026ef jmp          LBB0_481
	//0x000026f4 LBB0_480
	0x48, 0x01, 0xc3, //0x000026f4 addq         %rax, %rbx
	//0x000026f7 LBB0_481
	0x4c, 0x8b, 0x54, 0x24, 0x30, //0x000026f7 movq         $48(%rsp), %r10
	//0x000026fc LBB0_482
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000026fc movq         $-1, %rax
	0x4d, 0x85, 0xc0, //0x00002703 testq        %r8, %r8
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x00002706 movq         $8(%rsp), %rdx
	0x0f, 0x84, 0x87, 0x18, 0x00, 0x00, //0x0000270b je           LBB0_719
	0x4d, 0x85, 0xdb, //0x00002711 testq        %r11, %r11
	0x0f, 0x84, 0x7e, 0x18, 0x00, 0x00, //0x00002714 je           LBB0_719
	0x4d, 0x85, 0xf6, //0x0000271a testq        %r14, %r14
	0x0f, 0x84, 0x75, 0x18, 0x00, 0x00, //0x0000271d je           LBB0_719
	0x4c, 0x29, 0xd3, //0x00002723 subq         %r10, %rbx
	0x48, 0x8d, 0x43, 0xff, //0x00002726 leaq         $-1(%rbx), %rax
	0x49, 0x39, 0xc0, //0x0000272a cmpq         %rax, %r8
	0x0f, 0x84, 0x9a, 0x00, 0x00, 0x00, //0x0000272d je           LBB0_491
	0x49, 0x39, 0xc3, //0x00002733 cmpq         %rax, %r11
	0x0f, 0x84, 0x91, 0x00, 0x00, 0x00, //0x00002736 je           LBB0_491
	0x49, 0x39, 0xc6, //0x0000273c cmpq         %rax, %r14
	0x0f, 0x84, 0x88, 0x00, 0x00, 0x00, //0x0000273f je           LBB0_491
	0x4d, 0x85, 0xdb, //0x00002745 testq        %r11, %r11
	0xc5, 0xfe, 0x6f, 0x2d, 0x70, 0xd9, 0xff, 0xff, //0x00002748 vmovdqu      $-9872(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0x88, 0xd9, 0xff, 0xff, //0x00002750 vmovdqu      $-9848(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0xa0, 0xd9, 0xff, 0xff, //0x00002758 vmovdqu      $-9824(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0xb8, 0xd9, 0xff, 0xff, //0x00002760 vmovdqu      $-9800(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0x90, 0xda, 0xff, 0xff, //0x00002768 vmovdqu      $-9584(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x05, 0xa8, 0xda, 0xff, 0xff, //0x00002770 vmovdqu      $-9560(%rip), %ymm0  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0x60, 0xda, 0xff, 0xff, //0x00002778 vmovdqu      $-9632(%rip), %ymm11  /* LCPI0_10+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0xb8, 0xda, 0xff, 0xff, //0x00002780 vmovdqu      $-9544(%rip), %ymm14  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0xd0, 0xda, 0xff, 0xff, //0x00002788 vmovdqu      $-9520(%rip), %ymm15  /* LCPI0_17+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x28, 0xda, 0xff, 0xff, //0x00002790 vmovdqu      $-9688(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x00, 0xda, 0xff, 0xff, //0x00002798 vmovdqu      $-9728(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0x0f, 0x8e, 0xa6, 0x00, 0x00, 0x00, //0x000027a0 jle          LBB0_495
	0x49, 0x8d, 0x43, 0xff, //0x000027a6 leaq         $-1(%r11), %rax
	0x49, 0x39, 0xc6, //0x000027aa cmpq         %rax, %r14
	0x0f, 0x84, 0x99, 0x00, 0x00, 0x00, //0x000027ad je           LBB0_495
	0x49, 0xf7, 0xd3, //0x000027b3 notq         %r11
	0x4c, 0x89, 0xdb, //0x000027b6 movq         %r11, %rbx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000027b9 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000027be movq         $32(%rsp), %r11
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x000027c3 movq         $40(%rsp), %r14
	0xe9, 0x6a, 0x00, 0x00, 0x00, //0x000027c8 jmp          LBB0_494
	//0x000027cd LBB0_491
	0x48, 0xf7, 0xdb, //0x000027cd negq         %rbx
	//0x000027d0 LBB0_492
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000027d0 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000027d5 movq         $32(%rsp), %r11
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x000027da movq         $40(%rsp), %r14
	//0x000027df LBB0_493
	0xc5, 0xfe, 0x6f, 0x2d, 0xd9, 0xd8, 0xff, 0xff, //0x000027df vmovdqu      $-10023(%rip), %ymm5  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xf1, 0xd8, 0xff, 0xff, //0x000027e7 vmovdqu      $-9999(%rip), %ymm6  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x3d, 0x09, 0xd9, 0xff, 0xff, //0x000027ef vmovdqu      $-9975(%rip), %ymm7  /* LCPI0_3+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x05, 0x21, 0xd9, 0xff, 0xff, //0x000027f7 vmovdqu      $-9951(%rip), %ymm8  /* LCPI0_4+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x0d, 0xf9, 0xd9, 0xff, 0xff, //0x000027ff vmovdqu      $-9735(%rip), %ymm9  /* LCPI0_14+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x05, 0x11, 0xda, 0xff, 0xff, //0x00002807 vmovdqu      $-9711(%rip), %ymm0  /* LCPI0_15+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x1d, 0xc9, 0xd9, 0xff, 0xff, //0x0000280f vmovdqu      $-9783(%rip), %ymm11  /* LCPI0_10+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x35, 0x21, 0xda, 0xff, 0xff, //0x00002817 vmovdqu      $-9695(%rip), %ymm14  /* LCPI0_16+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x3d, 0x39, 0xda, 0xff, 0xff, //0x0000281f vmovdqu      $-9671(%rip), %ymm15  /* LCPI0_17+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0x91, 0xd9, 0xff, 0xff, //0x00002827 vmovdqu      $-9839(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x69, 0xd9, 0xff, 0xff, //0x0000282f vmovdqu      $-9879(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	//0x00002837 LBB0_494
	0xc5, 0x7e, 0x6f, 0x15, 0x01, 0xd9, 0xff, 0xff, //0x00002837 vmovdqu      $-9983(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0x19, 0xd9, 0xff, 0xff, //0x0000283f vmovdqu      $-9959(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0xe9, 0x83, 0x04, 0x00, 0x00, //0x00002847 jmp          LBB0_528
	//0x0000284c LBB0_495
	0x4c, 0x89, 0xc0, //0x0000284c movq         %r8, %rax
	0x4c, 0x09, 0xf0, //0x0000284f orq          %r14, %rax
	0x0f, 0x99, 0xc0, //0x00002852 setns        %al
	0xc5, 0x7e, 0x6f, 0x15, 0xe3, 0xd8, 0xff, 0xff, //0x00002855 vmovdqu      $-10013(%rip), %ymm10  /* LCPI0_5+0(%rip) */
	0xc5, 0x7e, 0x6f, 0x25, 0xfb, 0xd8, 0xff, 0xff, //0x0000285d vmovdqu      $-9989(%rip), %ymm12  /* LCPI0_6+0(%rip) */
	0x0f, 0x88, 0x4f, 0x01, 0x00, 0x00, //0x00002865 js           LBB0_499
	0x4d, 0x39, 0xf0, //0x0000286b cmpq         %r14, %r8
	0x0f, 0x8c, 0x46, 0x01, 0x00, 0x00, //0x0000286e jl           LBB0_499
	0x49, 0xf7, 0xd0, //0x00002874 notq         %r8
	0x4c, 0x89, 0xc3, //0x00002877 movq         %r8, %rbx
	0xe9, 0x41, 0x04, 0x00, 0x00, //0x0000287a jmp          LBB0_527
	//0x0000287f LBB0_192
	0x4d, 0x85, 0xdb, //0x0000287f testq        %r11, %r11
	0x0f, 0x85, 0x93, 0x08, 0x00, 0x00, //0x00002882 jne          LBB0_573
	0x4b, 0x8d, 0x0c, 0x02, //0x00002888 leaq         (%r10,%r8), %rcx
	0x48, 0x83, 0xc1, 0x01, //0x0000288c addq         $1, %rcx
	0x49, 0xf7, 0xd2, //0x00002890 notq         %r10
	0x4d, 0x01, 0xf2, //0x00002893 addq         %r14, %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002896 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000289b movq         $32(%rsp), %r11
	0x4d, 0x89, 0xf9, //0x000028a0 movq         %r15, %r9
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000028a3 movabsq      $4294977024, %r15
	//0x000028ad LBB0_194
	0x4d, 0x85, 0xd2, //0x000028ad testq        %r10, %r10
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x000028b0 movq         $40(%rsp), %r14
	0x0f, 0x8f, 0x24, 0x00, 0x00, 0x00, //0x000028b5 jg           LBB0_242
	0xe9, 0x3d, 0x15, 0x00, 0x00, //0x000028bb jmp          LBB0_721
	//0x000028c0 LBB0_240
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x000028c0 movq         $-2, %rdx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x000028c7 movl         $2, %eax
	0x48, 0x01, 0xc1, //0x000028cc addq         %rax, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000028cf movq         $-1, %rax
	0x49, 0x01, 0xd2, //0x000028d6 addq         %rdx, %r10
	0x0f, 0x8e, 0x1e, 0x15, 0x00, 0x00, //0x000028d9 jle          LBB0_721
	//0x000028df LBB0_242
	0x0f, 0xb6, 0x01, //0x000028df movzbl       (%rcx), %eax
	0x3c, 0x5c, //0x000028e2 cmpb         $92, %al
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x000028e4 je           LBB0_240
	0x3c, 0x22, //0x000028ea cmpb         $34, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x000028ec je           LBB0_498
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000028f2 movq         $-1, %rdx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000028f9 movl         $1, %eax
	0x48, 0x01, 0xc1, //0x000028fe addq         %rax, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002901 movq         $-1, %rax
	0x49, 0x01, 0xd2, //0x00002908 addq         %rdx, %r10
	0x0f, 0x8f, 0xce, 0xff, 0xff, 0xff, //0x0000290b jg           LBB0_242
	0xe9, 0xe7, 0x14, 0x00, 0x00, //0x00002911 jmp          LBB0_721
	//0x00002916 LBB0_498
	0x4c, 0x29, 0xe1, //0x00002916 subq         %r12, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00002919 addq         $1, %rcx
	0xe9, 0x61, 0xe7, 0xff, 0xff, //0x0000291d jmp          LBB0_189
	//0x00002922 LBB0_54
	0x4c, 0x01, 0xe1, //0x00002922 addq         %r12, %rcx
	0x49, 0x83, 0xfa, 0x20, //0x00002925 cmpq         $32, %r10
	0x0f, 0x82, 0x98, 0x06, 0x00, 0x00, //0x00002929 jb           LBB0_557
	//0x0000292f LBB0_55
	0xc5, 0xfe, 0x6f, 0x01, //0x0000292f vmovdqu      (%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00002933 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00002937 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x0000293b vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x0000293f vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x00002943 testl        %edx, %edx
	0x0f, 0x85, 0xc4, 0x05, 0x00, 0x00, //0x00002945 jne          LBB0_552
	0x4d, 0x85, 0xc0, //0x0000294b testq        %r8, %r8
	0x0f, 0x85, 0xf0, 0x05, 0x00, 0x00, //0x0000294e jne          LBB0_554
	0x45, 0x31, 0xc0, //0x00002954 xorl         %r8d, %r8d
	0x48, 0x85, 0xf6, //0x00002957 testq        %rsi, %rsi
	0x0f, 0x84, 0x5f, 0x06, 0x00, 0x00, //0x0000295a je           LBB0_556
	//0x00002960 LBB0_58
	0x48, 0x0f, 0xbc, 0xc6, //0x00002960 bsfq         %rsi, %rax
	0x4c, 0x29, 0xe1, //0x00002964 subq         %r12, %rcx
	0x48, 0x01, 0xc1, //0x00002967 addq         %rax, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x0000296a addq         $1, %rcx
	0xe9, 0xb4, 0xdd, 0xff, 0xff, //0x0000296e jmp          LBB0_63
	//0x00002973 LBB0_204
	0x4c, 0x01, 0xe1, //0x00002973 addq         %r12, %rcx
	0x49, 0x83, 0xfb, 0x20, //0x00002976 cmpq         $32, %r11
	0x0f, 0x82, 0xce, 0x03, 0x00, 0x00, //0x0000297a jb           LBB0_534
	//0x00002980 LBB0_205
	0xc5, 0xfe, 0x6f, 0x01, //0x00002980 vmovdqu      (%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00002984 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xc1, //0x00002988 vpmovmskb    %ymm1, %r8d
	0xc5, 0xfd, 0x74, 0xcf, //0x0000298c vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00002990 vpmovmskb    %ymm1, %edx
	0x85, 0xd2, //0x00002994 testl        %edx, %edx
	0x0f, 0x85, 0x47, 0x07, 0x00, 0x00, //0x00002996 jne          LBB0_571
	0x4d, 0x85, 0xd2, //0x0000299c testq        %r10, %r10
	0x0f, 0x85, 0xd2, 0x07, 0x00, 0x00, //0x0000299f jne          LBB0_575
	0x45, 0x31, 0xd2, //0x000029a5 xorl         %r10d, %r10d
	0x4d, 0x85, 0xc0, //0x000029a8 testq        %r8, %r8
	0x0f, 0x84, 0x39, 0x08, 0x00, 0x00, //0x000029ab je           LBB0_577
	//0x000029b1 LBB0_208
	0x49, 0x0f, 0xbc, 0xd0, //0x000029b1 bsfq         %r8, %rdx
	0xe9, 0x35, 0x08, 0x00, 0x00, //0x000029b5 jmp          LBB0_578
	//0x000029ba LBB0_499
	0x49, 0x8d, 0x4e, 0xff, //0x000029ba leaq         $-1(%r14), %rcx
	0x49, 0x39, 0xc8, //0x000029be cmpq         %rcx, %r8
	0x49, 0xf7, 0xd6, //0x000029c1 notq         %r14
	0x4c, 0x0f, 0x45, 0xf3, //0x000029c4 cmovneq      %rbx, %r14
	0x84, 0xc0, //0x000029c8 testb        %al, %al
	0x49, 0x0f, 0x45, 0xde, //0x000029ca cmovneq      %r14, %rbx
	0xe9, 0xed, 0x02, 0x00, 0x00, //0x000029ce jmp          LBB0_527
	//0x000029d3 LBB0_500
	0xc5, 0x7d, 0x7f, 0xe8, //0x000029d3 vmovdqa      %ymm13, %ymm0
	0x4d, 0x01, 0xec, //0x000029d7 addq         %r13, %r12
	0x4d, 0x29, 0xdc, //0x000029da subq         %r11, %r12
	0x48, 0xf7, 0xd0, //0x000029dd notq         %rax
	0x4c, 0x01, 0xe0, //0x000029e0 addq         %r12, %rax
	0x49, 0x89, 0xc3, //0x000029e3 movq         %rax, %r11
	0xe9, 0xcb, 0x00, 0x00, 0x00, //0x000029e6 jmp          LBB0_506
	//0x000029eb LBB0_501
	0xc5, 0x7d, 0x7f, 0xe8, //0x000029eb vmovdqa      %ymm13, %ymm0
	0x89, 0xd0, //0x000029ef movl         %edx, %eax
	0xe9, 0xba, 0x00, 0x00, 0x00, //0x000029f1 jmp          LBB0_505
	//0x000029f6 LBB0_502
	0x4d, 0x01, 0xe5, //0x000029f6 addq         %r12, %r13
	0x48, 0x85, 0xf6, //0x000029f9 testq        %rsi, %rsi
	0x0f, 0x85, 0xf4, 0xda, 0xff, 0xff, //0x000029fc jne          LBB0_29
	0xe9, 0x22, 0xdb, 0xff, 0xff, //0x00002a02 jmp          LBB0_34
	//0x00002a07 LBB0_503
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002a07 vmovdqa      %ymm13, %ymm0
	0x89, 0xd0, //0x00002a0b movl         %edx, %eax
	0xe9, 0x31, 0x01, 0x00, 0x00, //0x00002a0d jmp          LBB0_514
	//0x00002a12 LBB0_79
	0x4c, 0x01, 0xe1, //0x00002a12 addq         %r12, %rcx
	0x49, 0x83, 0xfa, 0x20, //0x00002a15 cmpq         $32, %r10
	0x0f, 0x82, 0xc9, 0x09, 0x00, 0x00, //0x00002a19 jb           LBB0_598
	//0x00002a1f LBB0_80
	0xc5, 0xfe, 0x6f, 0x01, //0x00002a1f vmovdqu      (%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00002a23 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00002a27 vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x00002a2b vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00002a2f vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x00002a33 testl        %edx, %edx
	0x0f, 0x85, 0xf5, 0x08, 0x00, 0x00, //0x00002a35 jne          LBB0_593
	0x4d, 0x85, 0xc0, //0x00002a3b testq        %r8, %r8
	0x0f, 0x85, 0x21, 0x09, 0x00, 0x00, //0x00002a3e jne          LBB0_595
	0x45, 0x31, 0xc0, //0x00002a44 xorl         %r8d, %r8d
	0x48, 0x85, 0xf6, //0x00002a47 testq        %rsi, %rsi
	0x0f, 0x84, 0x90, 0x09, 0x00, 0x00, //0x00002a4a je           LBB0_597
	//0x00002a50 LBB0_83
	0x48, 0x0f, 0xbc, 0xc6, //0x00002a50 bsfq         %rsi, %rax
	0x4c, 0x29, 0xe1, //0x00002a54 subq         %r12, %rcx
	0x48, 0x01, 0xc1, //0x00002a57 addq         %rax, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00002a5a addq         $1, %rcx
	0xe9, 0x14, 0xe3, 0xff, 0xff, //0x00002a5e jmp          LBB0_151
	//0x00002a63 LBB0_229
	0x4c, 0x01, 0xe1, //0x00002a63 addq         %r12, %rcx
	0x49, 0x83, 0xfb, 0x20, //0x00002a66 cmpq         $32, %r11
	0x0f, 0x82, 0x57, 0x03, 0x00, 0x00, //0x00002a6a jb           LBB0_540
	//0x00002a70 LBB0_230
	0xc5, 0xfe, 0x6f, 0x01, //0x00002a70 vmovdqu      (%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00002a74 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xc1, //0x00002a78 vpmovmskb    %ymm1, %r8d
	0xc5, 0xfd, 0x74, 0xcf, //0x00002a7c vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00002a80 vpmovmskb    %ymm1, %edx
	0x85, 0xd2, //0x00002a84 testl        %edx, %edx
	0x0f, 0x85, 0x78, 0x0a, 0x00, 0x00, //0x00002a86 jne          LBB0_612
	0x4d, 0x85, 0xd2, //0x00002a8c testq        %r10, %r10
	0x0f, 0x85, 0xa7, 0x0a, 0x00, 0x00, //0x00002a8f jne          LBB0_614
	0x45, 0x31, 0xd2, //0x00002a95 xorl         %r10d, %r10d
	0x4d, 0x85, 0xc0, //0x00002a98 testq        %r8, %r8
	0x0f, 0x84, 0x0e, 0x0b, 0x00, 0x00, //0x00002a9b je           LBB0_616
	//0x00002aa1 LBB0_233
	0x49, 0x0f, 0xbc, 0xd0, //0x00002aa1 bsfq         %r8, %rdx
	0xe9, 0x0a, 0x0b, 0x00, 0x00, //0x00002aa5 jmp          LBB0_617
	//0x00002aaa LBB0_504
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002aaa vmovdqa      %ymm13, %ymm0
	0x89, 0xc0, //0x00002aae movl         %eax, %eax
	//0x00002ab0 LBB0_505
	0x49, 0xf7, 0xd3, //0x00002ab0 notq         %r11
	0x49, 0x29, 0xc3, //0x00002ab3 subq         %rax, %r11
	//0x00002ab6 LBB0_506
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00002ab6 movq         $8(%rsp), %rsi
	//0x00002abb LBB0_507
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002abb movabsq      $4294977024, %r15
	0x4d, 0x85, 0xdb, //0x00002ac5 testq        %r11, %r11
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002ac8 movq         $24(%rsp), %r8
	0x0f, 0x88, 0x0e, 0x13, 0x00, 0x00, //0x00002acd js           LBB0_357
	//0x00002ad3 LBB0_508
	0x48, 0x8b, 0x0e, //0x00002ad3 movq         (%rsi), %rcx
	0xc5, 0x7d, 0x6f, 0xe8, //0x00002ad6 vmovdqa      %ymm0, %ymm13
	//0x00002ada LBB0_509
	0x4c, 0x01, 0xd9, //0x00002ada addq         %r11, %rcx
	0x48, 0x83, 0xc1, 0xff, //0x00002add addq         $-1, %rcx
	0x48, 0x89, 0x0e, //0x00002ae1 movq         %rcx, (%rsi)
	0x4c, 0x89, 0xe8, //0x00002ae4 movq         %r13, %rax
	0x48, 0xba, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00002ae7 movabsq      $9223372036854775806, %rdx
	0x49, 0x39, 0xd5, //0x00002af1 cmpq         %rdx, %r13
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002af4 movq         $32(%rsp), %r11
	0x0f, 0x86, 0xa2, 0xd8, 0xff, 0xff, //0x00002af9 jbe          LBB0_4
	0xe9, 0xf9, 0x12, 0x00, 0x00, //0x00002aff jmp          LBB0_721
	//0x00002b04 LBB0_510
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002b04 vmovdqa      %ymm13, %ymm0
	0x41, 0x0f, 0xbc, 0xc1, //0x00002b08 bsfl         %r9d, %eax
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00002b0c jmp          LBB0_512
	//0x00002b11 LBB0_511
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002b11 vmovdqa      %ymm13, %ymm0
	0x0f, 0xbc, 0xc0, //0x00002b15 bsfl         %eax, %eax
	//0x00002b18 LBB0_512
	0x4d, 0x01, 0xec, //0x00002b18 addq         %r13, %r12
	0x4d, 0x29, 0xdc, //0x00002b1b subq         %r11, %r12
	0x49, 0x29, 0xc4, //0x00002b1e subq         %rax, %r12
	0x49, 0xf7, 0xd6, //0x00002b21 notq         %r14
	0x4d, 0x01, 0xe6, //0x00002b24 addq         %r12, %r14
	0x4d, 0x89, 0xf3, //0x00002b27 movq         %r14, %r11
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00002b2a movq         $8(%rsp), %rsi
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x00002b2f movq         $40(%rsp), %r14
	0x4d, 0x89, 0xd1, //0x00002b34 movq         %r10, %r9
	0xe9, 0x7f, 0xff, 0xff, 0xff, //0x00002b37 jmp          LBB0_507
	//0x00002b3c LBB0_513
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002b3c vmovdqa      %ymm13, %ymm0
	0x0f, 0xbc, 0xc0, //0x00002b40 bsfl         %eax, %eax
	//0x00002b43 LBB0_514
	0x4d, 0x01, 0xec, //0x00002b43 addq         %r13, %r12
	0x4d, 0x29, 0xdc, //0x00002b46 subq         %r11, %r12
	0x49, 0x29, 0xc4, //0x00002b49 subq         %rax, %r12
	0x49, 0xf7, 0xd6, //0x00002b4c notq         %r14
	0x4d, 0x01, 0xe6, //0x00002b4f addq         %r12, %r14
	0x4d, 0x89, 0xf3, //0x00002b52 movq         %r14, %r11
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00002b55 movq         $8(%rsp), %rsi
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x00002b5a movq         $40(%rsp), %r14
	0xe9, 0x57, 0xff, 0xff, 0xff, //0x00002b5f jmp          LBB0_507
	//0x00002b64 LBB0_515
	0x4c, 0x89, 0xd3, //0x00002b64 movq         %r10, %rbx
	0xe9, 0x8b, 0xfb, 0xff, 0xff, //0x00002b67 jmp          LBB0_481
	//0x00002b6c LBB0_516
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002b6c vmovdqa      %ymm13, %ymm0
	0x0f, 0xbc, 0xc2, //0x00002b70 bsfl         %edx, %eax
	0xe9, 0x3d, 0x01, 0x00, 0x00, //0x00002b73 jmp          LBB0_526
	//0x00002b78 LBB0_260
	0x4c, 0x01, 0xe1, //0x00002b78 addq         %r12, %rcx
	0x49, 0x83, 0xfa, 0x20, //0x00002b7b cmpq         $32, %r10
	0x0f, 0x82, 0xb8, 0x02, 0x00, 0x00, //0x00002b7f jb           LBB0_545
	//0x00002b85 LBB0_261
	0xc5, 0xfe, 0x6f, 0x01, //0x00002b85 vmovdqu      (%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00002b89 vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xf1, //0x00002b8d vpmovmskb    %ymm1, %esi
	0xc5, 0xfd, 0x74, 0xc7, //0x00002b91 vpcmpeqb     %ymm7, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xd0, //0x00002b95 vpmovmskb    %ymm0, %edx
	0x85, 0xd2, //0x00002b99 testl        %edx, %edx
	0x0f, 0x85, 0xed, 0x0d, 0x00, 0x00, //0x00002b9b jne          LBB0_648
	0x4d, 0x85, 0xc0, //0x00002ba1 testq        %r8, %r8
	0x0f, 0x85, 0x19, 0x0e, 0x00, 0x00, //0x00002ba4 jne          LBB0_650
	0x45, 0x31, 0xc0, //0x00002baa xorl         %r8d, %r8d
	0xc5, 0x7d, 0x7f, 0xea, //0x00002bad vmovdqa      %ymm13, %ymm2
	0x48, 0x85, 0xf6, //0x00002bb1 testq        %rsi, %rsi
	0x0f, 0x84, 0x88, 0x0e, 0x00, 0x00, //0x00002bb4 je           LBB0_652
	//0x00002bba LBB0_264
	0x48, 0x0f, 0xbc, 0xc6, //0x00002bba bsfq         %rsi, %rax
	0x4c, 0x29, 0xe1, //0x00002bbe subq         %r12, %rcx
	0x48, 0x01, 0xc1, //0x00002bc1 addq         %rax, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00002bc4 addq         $1, %rcx
	0xe9, 0x5e, 0xe9, 0xff, 0xff, //0x00002bc8 jmp          LBB0_269
	//0x00002bcd LBB0_459
	0x4c, 0x01, 0xe1, //0x00002bcd addq         %r12, %rcx
	0x49, 0x83, 0xfb, 0x20, //0x00002bd0 cmpq         $32, %r11
	0x0f, 0x82, 0x96, 0x02, 0x00, 0x00, //0x00002bd4 jb           LBB0_547
	//0x00002bda LBB0_460
	0xc5, 0xfe, 0x6f, 0x01, //0x00002bda vmovdqu      (%rcx), %ymm0
	0xc5, 0xfd, 0x74, 0xce, //0x00002bde vpcmpeqb     %ymm6, %ymm0, %ymm1
	0xc5, 0x7d, 0xd7, 0xc1, //0x00002be2 vpmovmskb    %ymm1, %r8d
	0xc5, 0xfd, 0x74, 0xcf, //0x00002be6 vpcmpeqb     %ymm7, %ymm0, %ymm1
	0xc5, 0xfd, 0xd7, 0xd1, //0x00002bea vpmovmskb    %ymm1, %edx
	0x85, 0xd2, //0x00002bee testl        %edx, %edx
	0x0f, 0x85, 0x30, 0x0f, 0x00, 0x00, //0x00002bf0 jne          LBB0_666
	0x4d, 0x85, 0xd2, //0x00002bf6 testq        %r10, %r10
	0x0f, 0x85, 0x5f, 0x0f, 0x00, 0x00, //0x00002bf9 jne          LBB0_668
	0x45, 0x31, 0xd2, //0x00002bff xorl         %r10d, %r10d
	0x4d, 0x85, 0xc0, //0x00002c02 testq        %r8, %r8
	0x0f, 0x84, 0xc6, 0x0f, 0x00, 0x00, //0x00002c05 je           LBB0_670
	//0x00002c0b LBB0_463
	0x49, 0x0f, 0xbc, 0xd0, //0x00002c0b bsfq         %r8, %rdx
	0xe9, 0xc2, 0x0f, 0x00, 0x00, //0x00002c0f jmp          LBB0_671
	//0x00002c14 LBB0_517
	0x0f, 0xbc, 0xc0, //0x00002c14 bsfl         %eax, %eax
	0xe9, 0x3d, 0x00, 0x00, 0x00, //0x00002c17 jmp          LBB0_523
	//0x00002c1c LBB0_518
	0x4d, 0x01, 0xec, //0x00002c1c addq         %r13, %r12
	0x49, 0x29, 0xdc, //0x00002c1f subq         %rbx, %r12
	0x49, 0x29, 0xc4, //0x00002c22 subq         %rax, %r12
	0x4c, 0x89, 0xe3, //0x00002c25 movq         %r12, %rbx
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x00002c28 movq         $8(%rsp), %rdx
	0xe9, 0x9e, 0xfb, 0xff, 0xff, //0x00002c2d jmp          LBB0_492
	//0x00002c32 LBB0_519
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002c32 vmovdqa      %ymm13, %ymm0
	0x89, 0xd0, //0x00002c36 movl         %edx, %eax
	0xe9, 0x78, 0x00, 0x00, 0x00, //0x00002c38 jmp          LBB0_526
	//0x00002c3d LBB0_520
	0x4c, 0x01, 0xe1, //0x00002c3d addq         %r12, %rcx
	0xe9, 0x25, 0xfa, 0xff, 0xff, //0x00002c40 jmp          LBB0_164
	//0x00002c45 LBB0_521
	0x4c, 0x01, 0xe1, //0x00002c45 addq         %r12, %rcx
	0x48, 0x83, 0xf8, 0x10, //0x00002c48 cmpq         $16, %rax
	0x0f, 0x83, 0xa0, 0xe2, 0xff, 0xff, //0x00002c4c jae          LBB0_170
	0xe9, 0xe3, 0xe2, 0xff, 0xff, //0x00002c52 jmp          LBB0_173
	//0x00002c57 LBB0_522
	0x89, 0xd0, //0x00002c57 movl         %edx, %eax
	//0x00002c59 LBB0_523
	0x4d, 0x01, 0xec, //0x00002c59 addq         %r13, %r12
	0x49, 0x29, 0xdc, //0x00002c5c subq         %rbx, %r12
	0x49, 0x29, 0xc4, //0x00002c5f subq         %rax, %r12
	0x4d, 0x29, 0xd4, //0x00002c62 subq         %r10, %r12
	0x4c, 0x89, 0xe3, //0x00002c65 movq         %r12, %rbx
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x00002c68 movq         $8(%rsp), %rdx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002c6d movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002c72 movq         $32(%rsp), %r11
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x00002c77 movq         $40(%rsp), %r14
	0x4c, 0x8b, 0x4c, 0x24, 0x10, //0x00002c7c movq         $16(%rsp), %r9
	0xe9, 0x59, 0xfb, 0xff, 0xff, //0x00002c81 jmp          LBB0_493
	//0x00002c86 LBB0_524
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00002c86 movq         $-1, $16(%rsp)
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002c8f movq         $-1, %r8
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x00002c96 movq         $-1, %r15
	0x4d, 0x89, 0xd3, //0x00002c9d movq         %r10, %r11
	0x48, 0x83, 0xfb, 0x10, //0x00002ca0 cmpq         $16, %rbx
	0x0f, 0x83, 0x1e, 0xdd, 0xff, 0xff, //0x00002ca4 jae          LBB0_111
	0xe9, 0xbb, 0xde, 0xff, 0xff, //0x00002caa jmp          LBB0_130
	//0x00002caf LBB0_525
	0xc5, 0x7d, 0x7f, 0xe8, //0x00002caf vmovdqa      %ymm13, %ymm0
	0x89, 0xc0, //0x00002cb3 movl         %eax, %eax
	//0x00002cb5 LBB0_526
	0x48, 0xf7, 0xd3, //0x00002cb5 notq         %rbx
	0x48, 0x29, 0xc3, //0x00002cb8 subq         %rax, %rbx
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x00002cbb movq         $8(%rsp), %rdx
	//0x00002cc0 LBB0_527
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002cc0 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002cc5 movq         $32(%rsp), %r11
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x00002cca movq         $40(%rsp), %r14
	//0x00002ccf LBB0_528
	0x48, 0x85, 0xdb, //0x00002ccf testq        %rbx, %rbx
	0x0f, 0x88, 0xbd, 0x12, 0x00, 0x00, //0x00002cd2 js           LBB0_718
	0x48, 0x8b, 0x0a, //0x00002cd8 movq         (%rdx), %rcx
	0xc5, 0x7d, 0x6f, 0xe8, //0x00002cdb vmovdqa      %ymm0, %ymm13
	//0x00002cdf LBB0_530
	0x48, 0x01, 0xd9, //0x00002cdf addq         %rbx, %rcx
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x00002ce2 movq         $8(%rsp), %rax
	0x48, 0x89, 0x08, //0x00002ce7 movq         %rcx, (%rax)
	0x4c, 0x89, 0xe8, //0x00002cea movq         %r13, %rax
	0x48, 0xba, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, //0x00002ced movabsq      $9223372036854775806, %rdx
	0x49, 0x39, 0xd5, //0x00002cf7 cmpq         %rdx, %r13
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002cfa movabsq      $4294977024, %r15
	0x0f, 0x86, 0x97, 0xd6, 0xff, 0xff, //0x00002d04 jbe          LBB0_4
	0xe9, 0xee, 0x10, 0x00, 0x00, //0x00002d0a jmp          LBB0_721
	//0x00002d0f LBB0_531
	0x4c, 0x01, 0xe1, //0x00002d0f addq         %r12, %rcx
	0xe9, 0x96, 0xfb, 0xff, 0xff, //0x00002d12 jmp          LBB0_194
	//0x00002d17 LBB0_532
	0x4c, 0x01, 0xe1, //0x00002d17 addq         %r12, %rcx
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00002d1a movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc0, //0x00002d23 xorl         %r8d, %r8d
	0x49, 0x83, 0xfa, 0x20, //0x00002d26 cmpq         $32, %r10
	0x0f, 0x83, 0xff, 0xfb, 0xff, 0xff, //0x00002d2a jae          LBB0_55
	0xe9, 0x92, 0x02, 0x00, 0x00, //0x00002d30 jmp          LBB0_557
	//0x00002d35 LBB0_533
	0x4c, 0x01, 0xe1, //0x00002d35 addq         %r12, %rcx
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00002d38 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xd2, //0x00002d41 xorl         %r10d, %r10d
	0x49, 0x83, 0xfb, 0x20, //0x00002d44 cmpq         $32, %r11
	0x0f, 0x83, 0x32, 0xfc, 0xff, 0xff, //0x00002d48 jae          LBB0_205
	//0x00002d4e LBB0_534
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002d4e movq         $24(%rsp), %r8
	0xe9, 0xf0, 0x04, 0x00, 0x00, //0x00002d53 jmp          LBB0_583
	//0x00002d58 LBB0_535
	0x4c, 0x29, 0xe1, //0x00002d58 subq         %r12, %rcx
	0x48, 0x01, 0xd1, //0x00002d5b addq         %rdx, %rcx
	//0x00002d5e LBB0_536
	0x4c, 0x89, 0xff, //0x00002d5e movq         %r15, %rdi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002d61 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002d66 movq         $32(%rsp), %r11
	0x49, 0x89, 0xf7, //0x00002d6b movq         %rsi, %r15
	//0x00002d6e LBB0_537
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x00002d6e vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0x45, 0xd4, 0xff, 0xff, //0x00002d73 vmovdqu      $-11195(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x1d, 0xd4, 0xff, 0xff, //0x00002d7b vmovdqu      $-11235(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x00002d83 vmovdqa      %ymm0, %ymm10
	0xc5, 0x7d, 0x6f, 0xe1, //0x00002d87 vmovdqa      %ymm1, %ymm12
	0xe9, 0x09, 0xe2, 0xff, 0xff, //0x00002d8b jmp          LBB0_182
	//0x00002d90 LBB0_538
	0x4c, 0x01, 0xe1, //0x00002d90 addq         %r12, %rcx
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00002d93 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc0, //0x00002d9c xorl         %r8d, %r8d
	0x49, 0x83, 0xfa, 0x20, //0x00002d9f cmpq         $32, %r10
	0x0f, 0x83, 0x76, 0xfc, 0xff, 0xff, //0x00002da3 jae          LBB0_80
	0xe9, 0x3a, 0x06, 0x00, 0x00, //0x00002da9 jmp          LBB0_598
	//0x00002dae LBB0_539
	0x4c, 0x01, 0xe1, //0x00002dae addq         %r12, %rcx
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00002db1 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xd2, //0x00002dba xorl         %r10d, %r10d
	0x49, 0x83, 0xfb, 0x20, //0x00002dbd cmpq         $32, %r11
	0x0f, 0x83, 0xa9, 0xfc, 0xff, 0xff, //0x00002dc1 jae          LBB0_230
	//0x00002dc7 LBB0_540
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002dc7 movq         $24(%rsp), %r8
	0xe9, 0x3c, 0x08, 0x00, 0x00, //0x00002dcc jmp          LBB0_622
	//0x00002dd1 LBB0_541
	0x4c, 0x01, 0xe1, //0x00002dd1 addq         %r12, %rcx
	0xe9, 0x72, 0xee, 0xff, 0xff, //0x00002dd4 jmp          LBB0_376
	//0x00002dd9 LBB0_542
	0x4c, 0x01, 0xe1, //0x00002dd9 addq         %r12, %rcx
	0x48, 0x83, 0xf8, 0x10, //0x00002ddc cmpq         $16, %rax
	0x48, 0x8b, 0x7c, 0x24, 0x08, //0x00002de0 movq         $8(%rsp), %rdi
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002de5 movabsq      $4294977024, %r15
	0x0f, 0x83, 0xfc, 0xee, 0xff, 0xff, //0x00002def jae          LBB0_382
	0xe9, 0x3f, 0xef, 0xff, 0xff, //0x00002df5 jmp          LBB0_385
	//0x00002dfa LBB0_543
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002dfa movq         $-1, %r14
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002e01 movq         $-1, %r8
	0x4c, 0x89, 0xd3, //0x00002e08 movq         %r10, %rbx
	0x4c, 0x89, 0x54, 0x24, 0x30, //0x00002e0b movq         %r10, $48(%rsp)
	0x4c, 0x89, 0x4c, 0x24, 0x10, //0x00002e10 movq         %r9, $16(%rsp)
	0x49, 0x83, 0xff, 0x10, //0x00002e15 cmpq         $16, %r15
	0x0f, 0x83, 0xc7, 0xe8, 0xff, 0xff, //0x00002e19 jae          LBB0_295
	0xe9, 0xee, 0xe9, 0xff, 0xff, //0x00002e1f jmp          LBB0_313
	//0x00002e24 LBB0_544
	0x4c, 0x01, 0xe1, //0x00002e24 addq         %r12, %rcx
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00002e27 movq         $-1, $16(%rsp)
	0x45, 0x31, 0xc0, //0x00002e30 xorl         %r8d, %r8d
	0x49, 0x83, 0xfa, 0x20, //0x00002e33 cmpq         $32, %r10
	0x0f, 0x83, 0x48, 0xfd, 0xff, 0xff, //0x00002e37 jae          LBB0_261
	//0x00002e3d LBB0_545
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002e3d vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00002e41 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00002e45 vmovdqa      %ymm13, %ymm10
	0x4c, 0x89, 0xfb, //0x00002e4a movq         %r15, %rbx
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002e4d movq         $8(%rsp), %r15
	0xe9, 0x07, 0x0c, 0x00, 0x00, //0x00002e52 jmp          LBB0_653
	//0x00002e57 LBB0_546
	0x4c, 0x01, 0xe1, //0x00002e57 addq         %r12, %rcx
	0x48, 0xc7, 0x44, 0x24, 0x10, 0xff, 0xff, 0xff, 0xff, //0x00002e5a movq         $-1, $16(%rsp)
	0x45, 0x31, 0xd2, //0x00002e63 xorl         %r10d, %r10d
	0x49, 0x83, 0xfb, 0x20, //0x00002e66 cmpq         $32, %r11
	0x0f, 0x83, 0x6a, 0xfd, 0xff, 0xff, //0x00002e6a jae          LBB0_460
	//0x00002e70 LBB0_547
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002e70 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00002e74 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00002e78 vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x00002e7d vmovdqa      %ymm14, %ymm12
	0x4c, 0x89, 0xfb, //0x00002e82 movq         %r15, %rbx
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00002e85 movq         $8(%rsp), %r15
	0xe9, 0xbe, 0x0d, 0x00, 0x00, //0x00002e8a jmp          LBB0_676
	//0x00002e8f LBB0_548
	0x4c, 0x29, 0xe1, //0x00002e8f subq         %r12, %rcx
	0x48, 0x01, 0xd1, //0x00002e92 addq         %rdx, %rcx
	//0x00002e95 LBB0_549
	0x48, 0x8b, 0x7c, 0x24, 0x08, //0x00002e95 movq         $8(%rsp), %rdi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002e9a movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002e9f movq         $32(%rsp), %r11
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002ea4 movabsq      $4294977024, %r15
	0xe9, 0xbb, 0xfe, 0xff, 0xff, //0x00002eae jmp          LBB0_537
	//0x00002eb3 LBB0_550
	0x49, 0x8d, 0x4e, 0xff, //0x00002eb3 leaq         $-1(%r14), %rcx
	0x4c, 0x39, 0xd1, //0x00002eb7 cmpq         %r10, %rcx
	0x0f, 0x84, 0x3d, 0x0f, 0x00, 0x00, //0x00002eba je           LBB0_721
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002ec0 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00002ec4 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00002ec8 vmovdqa      %ymm13, %ymm10
	0x4b, 0x8d, 0x0c, 0x02, //0x00002ecd leaq         (%r10,%r8), %rcx
	0x48, 0x83, 0xc1, 0x02, //0x00002ed1 addq         $2, %rcx
	0x4d, 0x29, 0xd6, //0x00002ed5 subq         %r10, %r14
	0x49, 0x83, 0xc6, 0xfe, //0x00002ed8 addq         $-2, %r14
	0x4d, 0x89, 0xf2, //0x00002edc movq         %r14, %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002edf movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002ee4 movq         $32(%rsp), %r11
	0x4d, 0x89, 0xf9, //0x00002ee9 movq         %r15, %r9
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002eec movabsq      $4294977024, %r15
	0xc5, 0xfe, 0x6f, 0x1d, 0xc2, 0xd2, 0xff, 0xff, //0x00002ef6 vmovdqu      $-11582(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x9a, 0xd2, 0xff, 0xff, //0x00002efe vmovdqu      $-11622(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x00002f06 vmovdqa      %ymm0, %ymm10
	0xe9, 0x5b, 0xf7, 0xff, 0xff, //0x00002f0a jmp          LBB0_164
	//0x00002f0f LBB0_552
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002f0f vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00002f13 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00002f17 vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x00002f1c vmovdqa      %ymm14, %ymm12
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00002f21 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x29, 0x00, 0x00, 0x00, //0x00002f27 jne          LBB0_555
	0x48, 0x89, 0xc8, //0x00002f2d movq         %rcx, %rax
	0x4c, 0x29, 0xe0, //0x00002f30 subq         %r12, %rax
	0x48, 0x0f, 0xbc, 0xda, //0x00002f33 bsfq         %rdx, %rbx
	0x48, 0x01, 0xc3, //0x00002f37 addq         %rax, %rbx
	0x48, 0x89, 0x5c, 0x24, 0x10, //0x00002f3a movq         %rbx, $16(%rsp)
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00002f3f jmp          LBB0_555
	//0x00002f44 LBB0_554
	0xc5, 0x7d, 0x7f, 0xe1, //0x00002f44 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00002f48 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00002f4c vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x00002f51 vmovdqa      %ymm14, %ymm12
	//0x00002f56 LBB0_555
	0x44, 0x89, 0xc0, //0x00002f56 movl         %r8d, %eax
	0xf7, 0xd0, //0x00002f59 notl         %eax
	0x21, 0xd0, //0x00002f5b andl         %edx, %eax
	0x8d, 0x3c, 0x00, //0x00002f5d leal         (%rax,%rax), %edi
	0x41, 0x8d, 0x1c, 0x40, //0x00002f60 leal         (%r8,%rax,2), %ebx
	0xf7, 0xd7, //0x00002f64 notl         %edi
	0x21, 0xd7, //0x00002f66 andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00002f68 andl         $-1431655766, %edi
	0x45, 0x31, 0xc0, //0x00002f6e xorl         %r8d, %r8d
	0x01, 0xc7, //0x00002f71 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc0, //0x00002f73 setb         %r8b
	0x01, 0xff, //0x00002f77 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00002f79 xorl         $1431655765, %edi
	0x21, 0xdf, //0x00002f7f andl         %ebx, %edi
	0xf7, 0xd7, //0x00002f81 notl         %edi
	0x21, 0xfe, //0x00002f83 andl         %edi, %esi
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00002f85 movq         $32(%rsp), %r11
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00002f8a movabsq      $4294977024, %r15
	0xc4, 0x41, 0x7d, 0x6f, 0xf4, //0x00002f94 vmovdqa      %ymm12, %ymm14
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x00002f99 vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0x1a, 0xd2, 0xff, 0xff, //0x00002f9e vmovdqu      $-11750(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xf2, 0xd1, 0xff, 0xff, //0x00002fa6 vmovdqu      $-11790(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x00002fae vmovdqa      %ymm0, %ymm10
	0xc5, 0x7d, 0x6f, 0xe1, //0x00002fb2 vmovdqa      %ymm1, %ymm12
	0x48, 0x85, 0xf6, //0x00002fb6 testq        %rsi, %rsi
	0x0f, 0x85, 0xa1, 0xf9, 0xff, 0xff, //0x00002fb9 jne          LBB0_58
	//0x00002fbf LBB0_556
	0x48, 0x83, 0xc1, 0x20, //0x00002fbf addq         $32, %rcx
	0x49, 0x83, 0xc2, 0xe0, //0x00002fc3 addq         $-32, %r10
	//0x00002fc7 LBB0_557
	0x4d, 0x85, 0xc0, //0x00002fc7 testq        %r8, %r8
	0x0f, 0x85, 0x25, 0x07, 0x00, 0x00, //0x00002fca jne          LBB0_632
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00002fd0 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00002fd5 movq         $24(%rsp), %r8
	0x4d, 0x85, 0xd2, //0x00002fda testq        %r10, %r10
	0x0f, 0x84, 0xb5, 0x00, 0x00, 0x00, //0x00002fdd je           LBB0_570
	//0x00002fe3 LBB0_559
	0x4c, 0x89, 0xe2, //0x00002fe3 movq         %r12, %rdx
	0x48, 0xf7, 0xda, //0x00002fe6 negq         %rdx
	//0x00002fe9 LBB0_560
	0x31, 0xf6, //0x00002fe9 xorl         %esi, %esi
	//0x00002feb LBB0_561
	0x0f, 0xb6, 0x04, 0x31, //0x00002feb movzbl       (%rcx,%rsi), %eax
	0x3c, 0x22, //0x00002fef cmpb         $34, %al
	0x0f, 0x84, 0x9a, 0x00, 0x00, 0x00, //0x00002ff1 je           LBB0_569
	0x3c, 0x5c, //0x00002ff7 cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00002ff9 je           LBB0_566
	0x48, 0x83, 0xc6, 0x01, //0x00002fff addq         $1, %rsi
	0x49, 0x39, 0xf2, //0x00003003 cmpq         %rsi, %r10
	0x0f, 0x85, 0xdf, 0xff, 0xff, 0xff, //0x00003006 jne          LBB0_561
	0xe9, 0x8f, 0x00, 0x00, 0x00, //0x0000300c jmp          LBB0_564
	//0x00003011 LBB0_566
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x00003011 movq         $8(%rsp), %rbx
	0x49, 0x8d, 0x42, 0xff, //0x00003016 leaq         $-1(%r10), %rax
	0x48, 0x39, 0xf0, //0x0000301a cmpq         %rsi, %rax
	0x0f, 0x84, 0xa0, 0x0d, 0x00, 0x00, //0x0000301d je           LBB0_697
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003023 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00003027 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x0000302b vmovdqa      %ymm13, %ymm10
	0x48, 0x8d, 0x04, 0x0a, //0x00003030 leaq         (%rdx,%rcx), %rax
	0x48, 0x01, 0xf0, //0x00003034 addq         %rsi, %rax
	0x48, 0x83, 0xff, 0xff, //0x00003037 cmpq         $-1, %rdi
	0x48, 0x8b, 0x5c, 0x24, 0x10, //0x0000303b movq         $16(%rsp), %rbx
	0x48, 0x0f, 0x44, 0xd8, //0x00003040 cmoveq       %rax, %rbx
	0x48, 0x89, 0x5c, 0x24, 0x10, //0x00003044 movq         %rbx, $16(%rsp)
	0x48, 0x0f, 0x44, 0xf8, //0x00003049 cmoveq       %rax, %rdi
	0x48, 0x01, 0xf1, //0x0000304d addq         %rsi, %rcx
	0x48, 0x83, 0xc1, 0x02, //0x00003050 addq         $2, %rcx
	0x4c, 0x89, 0xd0, //0x00003054 movq         %r10, %rax
	0x48, 0x29, 0xf0, //0x00003057 subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x0000305a addq         $-2, %rax
	0x49, 0x83, 0xc2, 0xfe, //0x0000305e addq         $-2, %r10
	0x49, 0x39, 0xf2, //0x00003062 cmpq         %rsi, %r10
	0x49, 0x89, 0xc2, //0x00003065 movq         %rax, %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003068 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000306d movq         $32(%rsp), %r11
	0xc5, 0xfe, 0x6f, 0x1d, 0x46, 0xd1, 0xff, 0xff, //0x00003072 vmovdqu      $-11962(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x1e, 0xd1, 0xff, 0xff, //0x0000307a vmovdqu      $-12002(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x00003082 vmovdqa      %ymm0, %ymm10
	0x0f, 0x85, 0x5d, 0xff, 0xff, 0xff, //0x00003086 jne          LBB0_560
	0xe9, 0x35, 0x10, 0x00, 0x00, //0x0000308c jmp          LBB0_568
	//0x00003091 LBB0_569
	0x48, 0x01, 0xf1, //0x00003091 addq         %rsi, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00003094 addq         $1, %rcx
	//0x00003098 LBB0_570
	0x4c, 0x29, 0xe1, //0x00003098 subq         %r12, %rcx
	0xe9, 0x8c, 0xd6, 0xff, 0xff, //0x0000309b jmp          LBB0_64
	//0x000030a0 LBB0_564
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x000030a0 movq         $8(%rsp), %rsi
	0x3c, 0x22, //0x000030a5 cmpb         $34, %al
	0x0f, 0x85, 0x8c, 0x0f, 0x00, 0x00, //0x000030a7 jne          LBB0_730
	0xc5, 0x7d, 0x7f, 0xe1, //0x000030ad vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x000030b1 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x000030b5 vmovdqa      %ymm13, %ymm10
	0x4c, 0x89, 0xfa, //0x000030ba movq         %r15, %rdx
	0x4c, 0x01, 0xd1, //0x000030bd addq         %r10, %rcx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000030c0 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000030c5 movq         $32(%rsp), %r11
	0xc5, 0xfe, 0x6f, 0x1d, 0xee, 0xd0, 0xff, 0xff, //0x000030ca vmovdqu      $-12050(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xc6, 0xd0, 0xff, 0xff, //0x000030d2 vmovdqu      $-12090(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x000030da vmovdqa      %ymm0, %ymm10
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x000030de jmp          LBB0_570
	//0x000030e3 LBB0_571
	0xc5, 0x7d, 0x7f, 0xe2, //0x000030e3 vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd1, //0x000030e7 vmovdqa      %ymm10, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x000030eb vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x000030f0 vmovdqa      %ymm14, %ymm12
	0x4c, 0x89, 0xfb, //0x000030f5 movq         %r15, %rbx
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x000030f8 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x88, 0x00, 0x00, 0x00, //0x000030fe jne          LBB0_576
	0x48, 0x89, 0xc8, //0x00003104 movq         %rcx, %rax
	0x4c, 0x29, 0xe0, //0x00003107 subq         %r12, %rax
	0x48, 0x0f, 0xbc, 0xfa, //0x0000310a bsfq         %rdx, %rdi
	0x48, 0x01, 0xc7, //0x0000310e addq         %rax, %rdi
	0x48, 0x89, 0x7c, 0x24, 0x10, //0x00003111 movq         %rdi, $16(%rsp)
	0xe9, 0x71, 0x00, 0x00, 0x00, //0x00003116 jmp          LBB0_576
	//0x0000311b LBB0_573
	0x49, 0x8d, 0x4e, 0xff, //0x0000311b leaq         $-1(%r14), %rcx
	0x4c, 0x39, 0xd1, //0x0000311f cmpq         %r10, %rcx
	0x0f, 0x84, 0xd5, 0x0c, 0x00, 0x00, //0x00003122 je           LBB0_721
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003128 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x0000312c vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00003130 vmovdqa      %ymm13, %ymm10
	0x4b, 0x8d, 0x0c, 0x02, //0x00003135 leaq         (%r10,%r8), %rcx
	0x48, 0x83, 0xc1, 0x02, //0x00003139 addq         $2, %rcx
	0x4d, 0x29, 0xd6, //0x0000313d subq         %r10, %r14
	0x49, 0x83, 0xc6, 0xfe, //0x00003140 addq         $-2, %r14
	0x4d, 0x89, 0xf2, //0x00003144 movq         %r14, %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003147 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000314c movq         $32(%rsp), %r11
	0x4d, 0x89, 0xf9, //0x00003151 movq         %r15, %r9
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003154 movabsq      $4294977024, %r15
	0xc5, 0xfe, 0x6f, 0x1d, 0x5a, 0xd0, 0xff, 0xff, //0x0000315e vmovdqu      $-12198(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x32, 0xd0, 0xff, 0xff, //0x00003166 vmovdqu      $-12238(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x0000316e vmovdqa      %ymm0, %ymm10
	0xe9, 0x36, 0xf7, 0xff, 0xff, //0x00003172 jmp          LBB0_194
	//0x00003177 LBB0_575
	0xc5, 0x7d, 0x7f, 0xe2, //0x00003177 vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd1, //0x0000317b vmovdqa      %ymm10, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x0000317f vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x00003184 vmovdqa      %ymm14, %ymm12
	0x4c, 0x89, 0xfb, //0x00003189 movq         %r15, %rbx
	//0x0000318c LBB0_576
	0x44, 0x89, 0xd0, //0x0000318c movl         %r10d, %eax
	0xf7, 0xd0, //0x0000318f notl         %eax
	0x21, 0xd0, //0x00003191 andl         %edx, %eax
	0x8d, 0x34, 0x00, //0x00003193 leal         (%rax,%rax), %esi
	0x41, 0x8d, 0x3c, 0x42, //0x00003196 leal         (%r10,%rax,2), %edi
	0xf7, 0xd6, //0x0000319a notl         %esi
	0x21, 0xd6, //0x0000319c andl         %edx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x0000319e andl         $-1431655766, %esi
	0x45, 0x31, 0xd2, //0x000031a4 xorl         %r10d, %r10d
	0x01, 0xc6, //0x000031a7 addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc2, //0x000031a9 setb         %r10b
	0x01, 0xf6, //0x000031ad addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x000031af xorl         $1431655765, %esi
	0x21, 0xfe, //0x000031b5 andl         %edi, %esi
	0xf7, 0xd6, //0x000031b7 notl         %esi
	0x41, 0x21, 0xf0, //0x000031b9 andl         %esi, %r8d
	0x49, 0x89, 0xdf, //0x000031bc movq         %rbx, %r15
	0xc4, 0x41, 0x7d, 0x6f, 0xf4, //0x000031bf vmovdqa      %ymm12, %ymm14
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x000031c4 vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0xef, 0xcf, 0xff, 0xff, //0x000031c9 vmovdqu      $-12305(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xc7, 0xcf, 0xff, 0xff, //0x000031d1 vmovdqu      $-12345(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd1, //0x000031d9 vmovdqa      %ymm1, %ymm10
	0xc5, 0x7d, 0x6f, 0xe2, //0x000031dd vmovdqa      %ymm2, %ymm12
	0x4d, 0x85, 0xc0, //0x000031e1 testq        %r8, %r8
	0x0f, 0x85, 0xc7, 0xf7, 0xff, 0xff, //0x000031e4 jne          LBB0_208
	//0x000031ea LBB0_577
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000031ea movl         $64, %edx
	//0x000031ef LBB0_578
	0xc5, 0xbd, 0xda, 0xc8, //0x000031ef vpminub      %ymm0, %ymm8, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x000031f3 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x000031f7 vpmovmskb    %ymm0, %esi
	0x0f, 0xbc, 0xfe, //0x000031fb bsfl         %esi, %edi
	0x4d, 0x85, 0xc0, //0x000031fe testq        %r8, %r8
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x00003201 je           LBB0_581
	0x85, 0xf6, //0x00003207 testl        %esi, %esi
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00003209 movl         $64, %eax
	0x0f, 0x44, 0xf8, //0x0000320e cmovel       %eax, %edi
	0x48, 0x39, 0xfa, //0x00003211 cmpq         %rdi, %rdx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003214 movq         $24(%rsp), %r8
	0x0f, 0x87, 0x0a, 0x0e, 0x00, 0x00, //0x00003219 ja           LBB0_733
	0x4c, 0x29, 0xe1, //0x0000321f subq         %r12, %rcx
	0x48, 0x01, 0xd1, //0x00003222 addq         %rdx, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00003225 addq         $1, %rcx
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00003229 movq         $32(%rsp), %r11
	0xe9, 0xf9, 0xd4, 0xff, 0xff, //0x0000322e jmp          LBB0_64
	//0x00003233 LBB0_581
	0x85, 0xf6, //0x00003233 testl        %esi, %esi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003235 movq         $24(%rsp), %r8
	0x0f, 0x85, 0x01, 0x0e, 0x00, 0x00, //0x0000323a jne          LBB0_731
	0x48, 0x83, 0xc1, 0x20, //0x00003240 addq         $32, %rcx
	0x49, 0x83, 0xc3, 0xe0, //0x00003244 addq         $-32, %r11
	//0x00003248 LBB0_583
	0x4d, 0x85, 0xd2, //0x00003248 testq        %r10, %r10
	0x0f, 0x85, 0x18, 0x05, 0x00, 0x00, //0x0000324b jne          LBB0_634
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00003251 movq         $16(%rsp), %rax
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x00003256 movq         $8(%rsp), %rbx
	0x4d, 0x85, 0xdb, //0x0000325b testq        %r11, %r11
	0x0f, 0x84, 0x5f, 0x0b, 0x00, 0x00, //0x0000325e je           LBB0_697
	//0x00003264 LBB0_585
	0x0f, 0xb6, 0x11, //0x00003264 movzbl       (%rcx), %edx
	0x80, 0xfa, 0x22, //0x00003267 cmpb         $34, %dl
	0x0f, 0x84, 0xa6, 0x00, 0x00, 0x00, //0x0000326a je           LBB0_695
	0x80, 0xfa, 0x5c, //0x00003270 cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00003273 je           LBB0_589
	0x80, 0xfa, 0x1f, //0x00003279 cmpb         $31, %dl
	0x0f, 0x86, 0xdb, 0x0d, 0x00, 0x00, //0x0000327c jbe          LBB0_735
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00003282 movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00003289 movl         $1, %esi
	0x48, 0x01, 0xf1, //0x0000328e addq         %rsi, %rcx
	0x49, 0x01, 0xd3, //0x00003291 addq         %rdx, %r11
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x00003294 jne          LBB0_585
	0xe9, 0x24, 0x0b, 0x00, 0x00, //0x0000329a jmp          LBB0_697
	//0x0000329f LBB0_589
	0x4c, 0x89, 0xff, //0x0000329f movq         %r15, %rdi
	0x49, 0x89, 0xdf, //0x000032a2 movq         %rbx, %r15
	0x49, 0x83, 0xfb, 0x01, //0x000032a5 cmpq         $1, %r11
	0x0f, 0x84, 0xe2, 0x0d, 0x00, 0x00, //0x000032a9 je           LBB0_741
	0xc5, 0x7d, 0x7f, 0xe1, //0x000032af vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x000032b3 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x000032b7 vmovdqa      %ymm13, %ymm10
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x000032bc movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x000032c3 movl         $2, %esi
	0x48, 0x83, 0xf8, 0xff, //0x000032c8 cmpq         $-1, %rax
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x000032cc jne          LBB0_592
	0x48, 0x89, 0xc8, //0x000032d2 movq         %rcx, %rax
	0x4c, 0x29, 0xe0, //0x000032d5 subq         %r12, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x000032d8 movq         %rax, $16(%rsp)
	//0x000032dd LBB0_592
	0x4c, 0x89, 0xfb, //0x000032dd movq         %r15, %rbx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000032e0 movq         $24(%rsp), %r8
	0x49, 0x89, 0xff, //0x000032e5 movq         %rdi, %r15
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x000032e8 vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0xcb, 0xce, 0xff, 0xff, //0x000032ed vmovdqu      $-12597(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xa3, 0xce, 0xff, 0xff, //0x000032f5 vmovdqu      $-12637(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x000032fd vmovdqa      %ymm0, %ymm10
	0xc5, 0x7d, 0x6f, 0xe1, //0x00003301 vmovdqa      %ymm1, %ymm12
	0x48, 0x01, 0xf1, //0x00003305 addq         %rsi, %rcx
	0x49, 0x01, 0xd3, //0x00003308 addq         %rdx, %r11
	0x0f, 0x85, 0x53, 0xff, 0xff, 0xff, //0x0000330b jne          LBB0_585
	0xe9, 0xad, 0x0a, 0x00, 0x00, //0x00003311 jmp          LBB0_697
	//0x00003316 LBB0_695
	0x4c, 0x29, 0xe1, //0x00003316 subq         %r12, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00003319 addq         $1, %rcx
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000331d movq         $32(%rsp), %r11
	0x48, 0x85, 0xc9, //0x00003322 testq        %rcx, %rcx
	0x0f, 0x89, 0x76, 0xe0, 0xff, 0xff, //0x00003325 jns          LBB0_247
	0xe9, 0x81, 0x0a, 0x00, 0x00, //0x0000332b jmp          LBB0_696
	//0x00003330 LBB0_593
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003330 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00003334 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00003338 vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x0000333d vmovdqa      %ymm14, %ymm12
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00003342 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x29, 0x00, 0x00, 0x00, //0x00003348 jne          LBB0_596
	0x48, 0x89, 0xc8, //0x0000334e movq         %rcx, %rax
	0x4c, 0x29, 0xe0, //0x00003351 subq         %r12, %rax
	0x48, 0x0f, 0xbc, 0xda, //0x00003354 bsfq         %rdx, %rbx
	0x48, 0x01, 0xc3, //0x00003358 addq         %rax, %rbx
	0x48, 0x89, 0x5c, 0x24, 0x10, //0x0000335b movq         %rbx, $16(%rsp)
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x00003360 jmp          LBB0_596
	//0x00003365 LBB0_595
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003365 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00003369 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x0000336d vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x00003372 vmovdqa      %ymm14, %ymm12
	//0x00003377 LBB0_596
	0x44, 0x89, 0xc0, //0x00003377 movl         %r8d, %eax
	0xf7, 0xd0, //0x0000337a notl         %eax
	0x21, 0xd0, //0x0000337c andl         %edx, %eax
	0x8d, 0x3c, 0x00, //0x0000337e leal         (%rax,%rax), %edi
	0x41, 0x8d, 0x1c, 0x40, //0x00003381 leal         (%r8,%rax,2), %ebx
	0xf7, 0xd7, //0x00003385 notl         %edi
	0x21, 0xd7, //0x00003387 andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003389 andl         $-1431655766, %edi
	0x45, 0x31, 0xc0, //0x0000338f xorl         %r8d, %r8d
	0x01, 0xc7, //0x00003392 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc0, //0x00003394 setb         %r8b
	0x01, 0xff, //0x00003398 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x0000339a xorl         $1431655765, %edi
	0x21, 0xdf, //0x000033a0 andl         %ebx, %edi
	0xf7, 0xd7, //0x000033a2 notl         %edi
	0x21, 0xfe, //0x000033a4 andl         %edi, %esi
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000033a6 movq         $32(%rsp), %r11
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000033ab movabsq      $4294977024, %r15
	0xc4, 0x41, 0x7d, 0x6f, 0xf4, //0x000033b5 vmovdqa      %ymm12, %ymm14
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x000033ba vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0xf9, 0xcd, 0xff, 0xff, //0x000033bf vmovdqu      $-12807(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xd1, 0xcd, 0xff, 0xff, //0x000033c7 vmovdqu      $-12847(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x000033cf vmovdqa      %ymm0, %ymm10
	0xc5, 0x7d, 0x6f, 0xe1, //0x000033d3 vmovdqa      %ymm1, %ymm12
	0x48, 0x85, 0xf6, //0x000033d7 testq        %rsi, %rsi
	0x0f, 0x85, 0x70, 0xf6, 0xff, 0xff, //0x000033da jne          LBB0_83
	//0x000033e0 LBB0_597
	0x48, 0x83, 0xc1, 0x20, //0x000033e0 addq         $32, %rcx
	0x49, 0x83, 0xc2, 0xe0, //0x000033e4 addq         $-32, %r10
	//0x000033e8 LBB0_598
	0x4d, 0x85, 0xc0, //0x000033e8 testq        %r8, %r8
	0x0f, 0x85, 0xed, 0x03, 0x00, 0x00, //0x000033eb jne          LBB0_636
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x000033f1 movq         $16(%rsp), %rdi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000033f6 movq         $24(%rsp), %r8
	0x4d, 0x85, 0xd2, //0x000033fb testq        %r10, %r10
	0x0f, 0x84, 0xb5, 0x00, 0x00, 0x00, //0x000033fe je           LBB0_611
	//0x00003404 LBB0_600
	0x4c, 0x89, 0xe2, //0x00003404 movq         %r12, %rdx
	0x48, 0xf7, 0xda, //0x00003407 negq         %rdx
	//0x0000340a LBB0_601
	0x31, 0xf6, //0x0000340a xorl         %esi, %esi
	//0x0000340c LBB0_602
	0x0f, 0xb6, 0x04, 0x31, //0x0000340c movzbl       (%rcx,%rsi), %eax
	0x3c, 0x22, //0x00003410 cmpb         $34, %al
	0x0f, 0x84, 0x9a, 0x00, 0x00, 0x00, //0x00003412 je           LBB0_610
	0x3c, 0x5c, //0x00003418 cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x0000341a je           LBB0_607
	0x48, 0x83, 0xc6, 0x01, //0x00003420 addq         $1, %rsi
	0x49, 0x39, 0xf2, //0x00003424 cmpq         %rsi, %r10
	0x0f, 0x85, 0xdf, 0xff, 0xff, 0xff, //0x00003427 jne          LBB0_602
	0xe9, 0x8f, 0x00, 0x00, 0x00, //0x0000342d jmp          LBB0_605
	//0x00003432 LBB0_607
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x00003432 movq         $8(%rsp), %rbx
	0x49, 0x8d, 0x42, 0xff, //0x00003437 leaq         $-1(%r10), %rax
	0x48, 0x39, 0xf0, //0x0000343b cmpq         %rsi, %rax
	0x0f, 0x84, 0x7f, 0x09, 0x00, 0x00, //0x0000343e je           LBB0_697
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003444 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00003448 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x0000344c vmovdqa      %ymm13, %ymm10
	0x48, 0x8d, 0x04, 0x0a, //0x00003451 leaq         (%rdx,%rcx), %rax
	0x48, 0x01, 0xf0, //0x00003455 addq         %rsi, %rax
	0x48, 0x83, 0xff, 0xff, //0x00003458 cmpq         $-1, %rdi
	0x48, 0x8b, 0x5c, 0x24, 0x10, //0x0000345c movq         $16(%rsp), %rbx
	0x48, 0x0f, 0x44, 0xd8, //0x00003461 cmoveq       %rax, %rbx
	0x48, 0x89, 0x5c, 0x24, 0x10, //0x00003465 movq         %rbx, $16(%rsp)
	0x48, 0x0f, 0x44, 0xf8, //0x0000346a cmoveq       %rax, %rdi
	0x48, 0x01, 0xf1, //0x0000346e addq         %rsi, %rcx
	0x48, 0x83, 0xc1, 0x02, //0x00003471 addq         $2, %rcx
	0x4c, 0x89, 0xd0, //0x00003475 movq         %r10, %rax
	0x48, 0x29, 0xf0, //0x00003478 subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x0000347b addq         $-2, %rax
	0x49, 0x83, 0xc2, 0xfe, //0x0000347f addq         $-2, %r10
	0x49, 0x39, 0xf2, //0x00003483 cmpq         %rsi, %r10
	0x49, 0x89, 0xc2, //0x00003486 movq         %rax, %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003489 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000348e movq         $32(%rsp), %r11
	0xc5, 0xfe, 0x6f, 0x1d, 0x25, 0xcd, 0xff, 0xff, //0x00003493 vmovdqu      $-13019(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xfd, 0xcc, 0xff, 0xff, //0x0000349b vmovdqu      $-13059(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x000034a3 vmovdqa      %ymm0, %ymm10
	0x0f, 0x85, 0x5d, 0xff, 0xff, 0xff, //0x000034a7 jne          LBB0_601
	0xe9, 0x14, 0x0c, 0x00, 0x00, //0x000034ad jmp          LBB0_568
	//0x000034b2 LBB0_610
	0x48, 0x01, 0xf1, //0x000034b2 addq         %rsi, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x000034b5 addq         $1, %rcx
	//0x000034b9 LBB0_611
	0x4c, 0x29, 0xe1, //0x000034b9 subq         %r12, %rcx
	0xe9, 0xbb, 0xd8, 0xff, 0xff, //0x000034bc jmp          LBB0_152
	//0x000034c1 LBB0_605
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x000034c1 movq         $8(%rsp), %rsi
	0x3c, 0x22, //0x000034c6 cmpb         $34, %al
	0x0f, 0x85, 0x6b, 0x0b, 0x00, 0x00, //0x000034c8 jne          LBB0_730
	0xc5, 0x7d, 0x7f, 0xe1, //0x000034ce vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x000034d2 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x000034d6 vmovdqa      %ymm13, %ymm10
	0x4c, 0x89, 0xfa, //0x000034db movq         %r15, %rdx
	0x4c, 0x01, 0xd1, //0x000034de addq         %r10, %rcx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000034e1 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000034e6 movq         $32(%rsp), %r11
	0xc5, 0xfe, 0x6f, 0x1d, 0xcd, 0xcc, 0xff, 0xff, //0x000034eb vmovdqu      $-13107(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xa5, 0xcc, 0xff, 0xff, //0x000034f3 vmovdqu      $-13147(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x000034fb vmovdqa      %ymm0, %ymm10
	0xe9, 0xb5, 0xff, 0xff, 0xff, //0x000034ff jmp          LBB0_611
	//0x00003504 LBB0_612
	0xc5, 0x7d, 0x7f, 0xe2, //0x00003504 vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd1, //0x00003508 vmovdqa      %ymm10, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x0000350c vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x00003511 vmovdqa      %ymm14, %ymm12
	0x4c, 0x89, 0xfb, //0x00003516 movq         %r15, %rbx
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00003519 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x0000351f jne          LBB0_615
	0x48, 0x89, 0xc8, //0x00003525 movq         %rcx, %rax
	0x4c, 0x29, 0xe0, //0x00003528 subq         %r12, %rax
	0x48, 0x0f, 0xbc, 0xfa, //0x0000352b bsfq         %rdx, %rdi
	0x48, 0x01, 0xc7, //0x0000352f addq         %rax, %rdi
	0x48, 0x89, 0x7c, 0x24, 0x10, //0x00003532 movq         %rdi, $16(%rsp)
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x00003537 jmp          LBB0_615
	//0x0000353c LBB0_614
	0xc5, 0x7d, 0x7f, 0xe2, //0x0000353c vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd1, //0x00003540 vmovdqa      %ymm10, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00003544 vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x00003549 vmovdqa      %ymm14, %ymm12
	0x4c, 0x89, 0xfb, //0x0000354e movq         %r15, %rbx
	//0x00003551 LBB0_615
	0x44, 0x89, 0xd0, //0x00003551 movl         %r10d, %eax
	0xf7, 0xd0, //0x00003554 notl         %eax
	0x21, 0xd0, //0x00003556 andl         %edx, %eax
	0x8d, 0x34, 0x00, //0x00003558 leal         (%rax,%rax), %esi
	0x41, 0x8d, 0x3c, 0x42, //0x0000355b leal         (%r10,%rax,2), %edi
	0xf7, 0xd6, //0x0000355f notl         %esi
	0x21, 0xd6, //0x00003561 andl         %edx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003563 andl         $-1431655766, %esi
	0x45, 0x31, 0xd2, //0x00003569 xorl         %r10d, %r10d
	0x01, 0xc6, //0x0000356c addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc2, //0x0000356e setb         %r10b
	0x01, 0xf6, //0x00003572 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x00003574 xorl         $1431655765, %esi
	0x21, 0xfe, //0x0000357a andl         %edi, %esi
	0xf7, 0xd6, //0x0000357c notl         %esi
	0x41, 0x21, 0xf0, //0x0000357e andl         %esi, %r8d
	0x49, 0x89, 0xdf, //0x00003581 movq         %rbx, %r15
	0xc4, 0x41, 0x7d, 0x6f, 0xf4, //0x00003584 vmovdqa      %ymm12, %ymm14
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x00003589 vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0x2a, 0xcc, 0xff, 0xff, //0x0000358e vmovdqu      $-13270(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x02, 0xcc, 0xff, 0xff, //0x00003596 vmovdqu      $-13310(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd1, //0x0000359e vmovdqa      %ymm1, %ymm10
	0xc5, 0x7d, 0x6f, 0xe2, //0x000035a2 vmovdqa      %ymm2, %ymm12
	0x4d, 0x85, 0xc0, //0x000035a6 testq        %r8, %r8
	0x0f, 0x85, 0xf2, 0xf4, 0xff, 0xff, //0x000035a9 jne          LBB0_233
	//0x000035af LBB0_616
	0xba, 0x40, 0x00, 0x00, 0x00, //0x000035af movl         $64, %edx
	//0x000035b4 LBB0_617
	0xc5, 0xbd, 0xda, 0xc8, //0x000035b4 vpminub      %ymm0, %ymm8, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x000035b8 vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x000035bc vpmovmskb    %ymm0, %esi
	0x0f, 0xbc, 0xfe, //0x000035c0 bsfl         %esi, %edi
	0x4d, 0x85, 0xc0, //0x000035c3 testq        %r8, %r8
	0x0f, 0x84, 0x2c, 0x00, 0x00, 0x00, //0x000035c6 je           LBB0_620
	0x85, 0xf6, //0x000035cc testl        %esi, %esi
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x000035ce movl         $64, %eax
	0x0f, 0x44, 0xf8, //0x000035d3 cmovel       %eax, %edi
	0x48, 0x39, 0xfa, //0x000035d6 cmpq         %rdi, %rdx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000035d9 movq         $24(%rsp), %r8
	0x0f, 0x87, 0x45, 0x0a, 0x00, 0x00, //0x000035de ja           LBB0_733
	0x4c, 0x29, 0xe1, //0x000035e4 subq         %r12, %rcx
	0x48, 0x01, 0xd1, //0x000035e7 addq         %rdx, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x000035ea addq         $1, %rcx
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000035ee movq         $32(%rsp), %r11
	0xe9, 0x84, 0xd7, 0xff, 0xff, //0x000035f3 jmp          LBB0_152
	//0x000035f8 LBB0_620
	0x85, 0xf6, //0x000035f8 testl        %esi, %esi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000035fa movq         $24(%rsp), %r8
	0x0f, 0x85, 0x3c, 0x0a, 0x00, 0x00, //0x000035ff jne          LBB0_731
	0x48, 0x83, 0xc1, 0x20, //0x00003605 addq         $32, %rcx
	0x49, 0x83, 0xc3, 0xe0, //0x00003609 addq         $-32, %r11
	//0x0000360d LBB0_622
	0x4d, 0x85, 0xd2, //0x0000360d testq        %r10, %r10
	0x0f, 0x85, 0x3c, 0x02, 0x00, 0x00, //0x00003610 jne          LBB0_638
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00003616 movq         $16(%rsp), %rax
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x0000361b movq         $8(%rsp), %rbx
	0x4d, 0x85, 0xdb, //0x00003620 testq        %r11, %r11
	0x0f, 0x84, 0x9a, 0x07, 0x00, 0x00, //0x00003623 je           LBB0_697
	//0x00003629 LBB0_624
	0x0f, 0xb6, 0x11, //0x00003629 movzbl       (%rcx), %edx
	0x80, 0xfa, 0x22, //0x0000362c cmpb         $34, %dl
	0x0f, 0x84, 0xa6, 0x00, 0x00, 0x00, //0x0000362f je           LBB0_701
	0x80, 0xfa, 0x5c, //0x00003635 cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00003638 je           LBB0_628
	0x80, 0xfa, 0x1f, //0x0000363e cmpb         $31, %dl
	0x0f, 0x86, 0x16, 0x0a, 0x00, 0x00, //0x00003641 jbe          LBB0_735
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00003647 movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x0000364e movl         $1, %esi
	0x48, 0x01, 0xf1, //0x00003653 addq         %rsi, %rcx
	0x49, 0x01, 0xd3, //0x00003656 addq         %rdx, %r11
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x00003659 jne          LBB0_624
	0xe9, 0x5f, 0x07, 0x00, 0x00, //0x0000365f jmp          LBB0_697
	//0x00003664 LBB0_628
	0x4c, 0x89, 0xff, //0x00003664 movq         %r15, %rdi
	0x49, 0x89, 0xdf, //0x00003667 movq         %rbx, %r15
	0x49, 0x83, 0xfb, 0x01, //0x0000366a cmpq         $1, %r11
	0x0f, 0x84, 0x1d, 0x0a, 0x00, 0x00, //0x0000366e je           LBB0_741
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003674 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00003678 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x0000367c vmovdqa      %ymm13, %ymm10
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00003681 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00003688 movl         $2, %esi
	0x48, 0x83, 0xf8, 0xff, //0x0000368d cmpq         $-1, %rax
	0x0f, 0x85, 0x0b, 0x00, 0x00, 0x00, //0x00003691 jne          LBB0_631
	0x48, 0x89, 0xc8, //0x00003697 movq         %rcx, %rax
	0x4c, 0x29, 0xe0, //0x0000369a subq         %r12, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x0000369d movq         %rax, $16(%rsp)
	//0x000036a2 LBB0_631
	0x4c, 0x89, 0xfb, //0x000036a2 movq         %r15, %rbx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000036a5 movq         $24(%rsp), %r8
	0x49, 0x89, 0xff, //0x000036aa movq         %rdi, %r15
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x000036ad vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0x06, 0xcb, 0xff, 0xff, //0x000036b2 vmovdqu      $-13562(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xde, 0xca, 0xff, 0xff, //0x000036ba vmovdqu      $-13602(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x000036c2 vmovdqa      %ymm0, %ymm10
	0xc5, 0x7d, 0x6f, 0xe1, //0x000036c6 vmovdqa      %ymm1, %ymm12
	0x48, 0x01, 0xf1, //0x000036ca addq         %rsi, %rcx
	0x49, 0x01, 0xd3, //0x000036cd addq         %rdx, %r11
	0x0f, 0x85, 0x53, 0xff, 0xff, 0xff, //0x000036d0 jne          LBB0_624
	0xe9, 0xe8, 0x06, 0x00, 0x00, //0x000036d6 jmp          LBB0_697
	//0x000036db LBB0_701
	0x4c, 0x29, 0xe1, //0x000036db subq         %r12, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x000036de addq         $1, %rcx
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000036e2 movq         $32(%rsp), %r11
	0x48, 0x85, 0xc9, //0x000036e7 testq        %rcx, %rcx
	0x0f, 0x89, 0x53, 0xe4, 0xff, 0xff, //0x000036ea jns          LBB0_363
	0xe9, 0xbc, 0x06, 0x00, 0x00, //0x000036f0 jmp          LBB0_696
	//0x000036f5 LBB0_632
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x000036f5 movq         $8(%rsp), %rax
	0x4d, 0x85, 0xd2, //0x000036fa testq        %r10, %r10
	0x0f, 0x84, 0xbb, 0x09, 0x00, 0x00, //0x000036fd je           LBB0_740
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003703 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00003707 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x0000370b vmovdqa      %ymm13, %ymm10
	0x4c, 0x89, 0xfa, //0x00003710 movq         %r15, %rdx
	0x4c, 0x89, 0xe7, //0x00003713 movq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x00003716 notq         %rdi
	0x48, 0x01, 0xcf, //0x00003719 addq         %rcx, %rdi
	0x48, 0x8b, 0x74, 0x24, 0x10, //0x0000371c movq         $16(%rsp), %rsi
	0x48, 0x83, 0xfe, 0xff, //0x00003721 cmpq         $-1, %rsi
	0x48, 0x89, 0xf0, //0x00003725 movq         %rsi, %rax
	0x48, 0x0f, 0x44, 0xc7, //0x00003728 cmoveq       %rdi, %rax
	0x48, 0x0f, 0x45, 0xfe, //0x0000372c cmovneq      %rsi, %rdi
	0x48, 0x83, 0xc1, 0x01, //0x00003730 addq         $1, %rcx
	0x49, 0x83, 0xc2, 0xff, //0x00003734 addq         $-1, %r10
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00003738 movq         %rax, $16(%rsp)
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x0000373d movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00003742 movq         $32(%rsp), %r11
	0xc5, 0xfe, 0x6f, 0x1d, 0x71, 0xca, 0xff, 0xff, //0x00003747 vmovdqu      $-13711(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x49, 0xca, 0xff, 0xff, //0x0000374f vmovdqu      $-13751(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x00003757 vmovdqa      %ymm0, %ymm10
	0x4d, 0x85, 0xd2, //0x0000375b testq        %r10, %r10
	0x0f, 0x85, 0x7f, 0xf8, 0xff, 0xff, //0x0000375e jne          LBB0_559
	0xe9, 0x2f, 0xf9, 0xff, 0xff, //0x00003764 jmp          LBB0_570
	//0x00003769 LBB0_634
	0x4c, 0x89, 0xfe, //0x00003769 movq         %r15, %rsi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x0000376c movq         $8(%rsp), %r15
	0x4d, 0x85, 0xdb, //0x00003771 testq        %r11, %r11
	0x0f, 0x84, 0x17, 0x09, 0x00, 0x00, //0x00003774 je           LBB0_741
	0xc5, 0x7d, 0x7f, 0xe1, //0x0000377a vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x0000377e vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00003782 vmovdqa      %ymm13, %ymm10
	0x4c, 0x89, 0xe0, //0x00003787 movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x0000378a notq         %rax
	0x48, 0x01, 0xc8, //0x0000378d addq         %rcx, %rax
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00003790 movq         $16(%rsp), %rdi
	0x48, 0x83, 0xff, 0xff, //0x00003795 cmpq         $-1, %rdi
	0x48, 0x89, 0xfa, //0x00003799 movq         %rdi, %rdx
	0x48, 0x0f, 0x44, 0xd0, //0x0000379c cmoveq       %rax, %rdx
	0x48, 0x0f, 0x45, 0xc7, //0x000037a0 cmovneq      %rdi, %rax
	0x48, 0x83, 0xc1, 0x01, //0x000037a4 addq         $1, %rcx
	0x49, 0x83, 0xc3, 0xff, //0x000037a8 addq         $-1, %r11
	0x48, 0x89, 0x54, 0x24, 0x10, //0x000037ac movq         %rdx, $16(%rsp)
	0x4c, 0x89, 0xfb, //0x000037b1 movq         %r15, %rbx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x000037b4 movq         $24(%rsp), %r8
	0x49, 0x89, 0xf7, //0x000037b9 movq         %rsi, %r15
	0xc5, 0xfe, 0x6f, 0x1d, 0xfc, 0xc9, 0xff, 0xff, //0x000037bc vmovdqu      $-13828(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xd4, 0xc9, 0xff, 0xff, //0x000037c4 vmovdqu      $-13868(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x000037cc vmovdqa      %ymm0, %ymm10
	0x4d, 0x85, 0xdb, //0x000037d0 testq        %r11, %r11
	0x0f, 0x85, 0x8b, 0xfa, 0xff, 0xff, //0x000037d3 jne          LBB0_585
	0xe9, 0xe5, 0x05, 0x00, 0x00, //0x000037d9 jmp          LBB0_697
	//0x000037de LBB0_636
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x000037de movq         $8(%rsp), %rax
	0x4d, 0x85, 0xd2, //0x000037e3 testq        %r10, %r10
	0x0f, 0x84, 0xd2, 0x08, 0x00, 0x00, //0x000037e6 je           LBB0_740
	0xc5, 0x7d, 0x7f, 0xe1, //0x000037ec vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x000037f0 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x000037f4 vmovdqa      %ymm13, %ymm10
	0x4c, 0x89, 0xfa, //0x000037f9 movq         %r15, %rdx
	0x4c, 0x89, 0xe7, //0x000037fc movq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x000037ff notq         %rdi
	0x48, 0x01, 0xcf, //0x00003802 addq         %rcx, %rdi
	0x48, 0x8b, 0x74, 0x24, 0x10, //0x00003805 movq         $16(%rsp), %rsi
	0x48, 0x83, 0xfe, 0xff, //0x0000380a cmpq         $-1, %rsi
	0x48, 0x89, 0xf0, //0x0000380e movq         %rsi, %rax
	0x48, 0x0f, 0x44, 0xc7, //0x00003811 cmoveq       %rdi, %rax
	0x48, 0x0f, 0x45, 0xfe, //0x00003815 cmovneq      %rsi, %rdi
	0x48, 0x83, 0xc1, 0x01, //0x00003819 addq         $1, %rcx
	0x49, 0x83, 0xc2, 0xff, //0x0000381d addq         $-1, %r10
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00003821 movq         %rax, $16(%rsp)
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003826 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x0000382b movq         $32(%rsp), %r11
	0xc5, 0xfe, 0x6f, 0x1d, 0x88, 0xc9, 0xff, 0xff, //0x00003830 vmovdqu      $-13944(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x60, 0xc9, 0xff, 0xff, //0x00003838 vmovdqu      $-13984(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x00003840 vmovdqa      %ymm0, %ymm10
	0x4d, 0x85, 0xd2, //0x00003844 testq        %r10, %r10
	0x0f, 0x85, 0xb7, 0xfb, 0xff, 0xff, //0x00003847 jne          LBB0_600
	0xe9, 0x67, 0xfc, 0xff, 0xff, //0x0000384d jmp          LBB0_611
	//0x00003852 LBB0_638
	0x4c, 0x89, 0xfe, //0x00003852 movq         %r15, %rsi
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00003855 movq         $8(%rsp), %r15
	0x4d, 0x85, 0xdb, //0x0000385a testq        %r11, %r11
	0x0f, 0x84, 0x2e, 0x08, 0x00, 0x00, //0x0000385d je           LBB0_741
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003863 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00003867 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x0000386b vmovdqa      %ymm13, %ymm10
	0x4c, 0x89, 0xe0, //0x00003870 movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x00003873 notq         %rax
	0x48, 0x01, 0xc8, //0x00003876 addq         %rcx, %rax
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00003879 movq         $16(%rsp), %rdi
	0x48, 0x83, 0xff, 0xff, //0x0000387e cmpq         $-1, %rdi
	0x48, 0x89, 0xfa, //0x00003882 movq         %rdi, %rdx
	0x48, 0x0f, 0x44, 0xd0, //0x00003885 cmoveq       %rax, %rdx
	0x48, 0x0f, 0x45, 0xc7, //0x00003889 cmovneq      %rdi, %rax
	0x48, 0x83, 0xc1, 0x01, //0x0000388d addq         $1, %rcx
	0x49, 0x83, 0xc3, 0xff, //0x00003891 addq         $-1, %r11
	0x48, 0x89, 0x54, 0x24, 0x10, //0x00003895 movq         %rdx, $16(%rsp)
	0x4c, 0x89, 0xfb, //0x0000389a movq         %r15, %rbx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x0000389d movq         $24(%rsp), %r8
	0x49, 0x89, 0xf7, //0x000038a2 movq         %rsi, %r15
	0xc5, 0xfe, 0x6f, 0x1d, 0x13, 0xc9, 0xff, 0xff, //0x000038a5 vmovdqu      $-14061(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xeb, 0xc8, 0xff, 0xff, //0x000038ad vmovdqu      $-14101(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x000038b5 vmovdqa      %ymm0, %ymm10
	0x4d, 0x85, 0xdb, //0x000038b9 testq        %r11, %r11
	0x0f, 0x85, 0x67, 0xfd, 0xff, 0xff, //0x000038bc jne          LBB0_624
	0xe9, 0xfc, 0x04, 0x00, 0x00, //0x000038c2 jmp          LBB0_697
	//0x000038c7 LBB0_640
	0x49, 0x8d, 0x4e, 0xff, //0x000038c7 leaq         $-1(%r14), %rcx
	0x4c, 0x39, 0xd1, //0x000038cb cmpq         %r10, %rcx
	0x0f, 0x84, 0x29, 0x05, 0x00, 0x00, //0x000038ce je           LBB0_721
	0xc5, 0x7d, 0x7f, 0xe1, //0x000038d4 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x000038d8 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x000038dc vmovdqa      %ymm13, %ymm10
	0x4c, 0x89, 0xfa, //0x000038e1 movq         %r15, %rdx
	0x4b, 0x8d, 0x0c, 0x02, //0x000038e4 leaq         (%r10,%r8), %rcx
	0x48, 0x83, 0xc1, 0x02, //0x000038e8 addq         $2, %rcx
	0x4d, 0x29, 0xd6, //0x000038ec subq         %r10, %r14
	0x49, 0x83, 0xc6, 0xfe, //0x000038ef addq         $-2, %r14
	0x4d, 0x89, 0xf2, //0x000038f3 movq         %r14, %r10
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x000038f6 movq         $32(%rsp), %r11
	0x4c, 0x8b, 0x4c, 0x24, 0x10, //0x000038fb movq         $16(%rsp), %r9
	0xc5, 0xfe, 0x6f, 0x1d, 0xb8, 0xc8, 0xff, 0xff, //0x00003900 vmovdqu      $-14152(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x90, 0xc8, 0xff, 0xff, //0x00003908 vmovdqu      $-14192(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x00003910 vmovdqa      %ymm0, %ymm10
	0x4d, 0x85, 0xd2, //0x00003914 testq        %r10, %r10
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003917 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x74, 0x24, 0x28, //0x0000391c movq         $40(%rsp), %r14
	0x0f, 0x8f, 0x24, 0x00, 0x00, 0x00, //0x00003921 jg           LBB0_644
	0xe9, 0xd1, 0x04, 0x00, 0x00, //0x00003927 jmp          LBB0_721
	//0x0000392c LBB0_642
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x0000392c movq         $-2, %rdx
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00003933 movl         $2, %eax
	0x48, 0x01, 0xc1, //0x00003938 addq         %rax, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000393b movq         $-1, %rax
	0x49, 0x01, 0xd2, //0x00003942 addq         %rdx, %r10
	0x0f, 0x8e, 0xb2, 0x04, 0x00, 0x00, //0x00003945 jle          LBB0_721
	//0x0000394b LBB0_644
	0x0f, 0xb6, 0x01, //0x0000394b movzbl       (%rcx), %eax
	0x3c, 0x5c, //0x0000394e cmpb         $92, %al
	0x0f, 0x84, 0xd6, 0xff, 0xff, 0xff, //0x00003950 je           LBB0_642
	0x3c, 0x22, //0x00003956 cmpb         $34, %al
	0x0f, 0x84, 0x24, 0x00, 0x00, 0x00, //0x00003958 je           LBB0_647
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000395e movq         $-1, %rdx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00003965 movl         $1, %eax
	0x48, 0x01, 0xc1, //0x0000396a addq         %rax, %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x0000396d movq         $-1, %rax
	0x49, 0x01, 0xd2, //0x00003974 addq         %rdx, %r10
	0x0f, 0x8f, 0xce, 0xff, 0xff, 0xff, //0x00003977 jg           LBB0_644
	0xe9, 0x7b, 0x04, 0x00, 0x00, //0x0000397d jmp          LBB0_721
	//0x00003982 LBB0_647
	0x4c, 0x29, 0xe1, //0x00003982 subq         %r12, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00003985 addq         $1, %rcx
	0xe9, 0xe5, 0xd4, 0xff, 0xff, //0x00003989 jmp          LBB0_159
	//0x0000398e LBB0_648
	0xc5, 0x7d, 0x7f, 0xe1, //0x0000398e vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00003992 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00003996 vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x0000399b vmovdqa      %ymm14, %ymm12
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x000039a0 cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x29, 0x00, 0x00, 0x00, //0x000039a6 jne          LBB0_651
	0x48, 0x89, 0xc8, //0x000039ac movq         %rcx, %rax
	0x4c, 0x29, 0xe0, //0x000039af subq         %r12, %rax
	0x48, 0x0f, 0xbc, 0xda, //0x000039b2 bsfq         %rdx, %rbx
	0x48, 0x01, 0xc3, //0x000039b6 addq         %rax, %rbx
	0x48, 0x89, 0x5c, 0x24, 0x10, //0x000039b9 movq         %rbx, $16(%rsp)
	0xe9, 0x12, 0x00, 0x00, 0x00, //0x000039be jmp          LBB0_651
	//0x000039c3 LBB0_650
	0xc5, 0x7d, 0x7f, 0xe1, //0x000039c3 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x000039c7 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x000039cb vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x000039d0 vmovdqa      %ymm14, %ymm12
	//0x000039d5 LBB0_651
	0x44, 0x89, 0xc0, //0x000039d5 movl         %r8d, %eax
	0xf7, 0xd0, //0x000039d8 notl         %eax
	0x21, 0xd0, //0x000039da andl         %edx, %eax
	0x8d, 0x3c, 0x00, //0x000039dc leal         (%rax,%rax), %edi
	0x41, 0x8d, 0x1c, 0x40, //0x000039df leal         (%r8,%rax,2), %ebx
	0xf7, 0xd7, //0x000039e3 notl         %edi
	0x21, 0xd7, //0x000039e5 andl         %edx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000039e7 andl         $-1431655766, %edi
	0x45, 0x31, 0xc0, //0x000039ed xorl         %r8d, %r8d
	0x01, 0xc7, //0x000039f0 addl         %eax, %edi
	0x41, 0x0f, 0x92, 0xc0, //0x000039f2 setb         %r8b
	0x01, 0xff, //0x000039f6 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000039f8 xorl         $1431655765, %edi
	0x21, 0xdf, //0x000039fe andl         %ebx, %edi
	0xf7, 0xd7, //0x00003a00 notl         %edi
	0x21, 0xfe, //0x00003a02 andl         %edi, %esi
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00003a04 movq         $32(%rsp), %r11
	0x49, 0xbf, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00003a09 movabsq      $4294977024, %r15
	0xc4, 0x41, 0x7d, 0x6f, 0xf4, //0x00003a13 vmovdqa      %ymm12, %ymm14
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x00003a18 vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0x9b, 0xc7, 0xff, 0xff, //0x00003a1d vmovdqu      $-14437(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0x73, 0xc7, 0xff, 0xff, //0x00003a25 vmovdqu      $-14477(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x00003a2d vmovdqa      %ymm0, %ymm10
	0xc5, 0x7d, 0x6f, 0xe1, //0x00003a31 vmovdqa      %ymm1, %ymm12
	0xc5, 0x7d, 0x7f, 0xea, //0x00003a35 vmovdqa      %ymm13, %ymm2
	0x48, 0x85, 0xf6, //0x00003a39 testq        %rsi, %rsi
	0x0f, 0x85, 0x78, 0xf1, 0xff, 0xff, //0x00003a3c jne          LBB0_264
	//0x00003a42 LBB0_652
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003a42 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00003a46 vmovdqa      %ymm10, %ymm0
	0xc5, 0x7d, 0x6f, 0xd2, //0x00003a4a vmovdqa      %ymm2, %ymm10
	0x4c, 0x89, 0xfb, //0x00003a4e movq         %r15, %rbx
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00003a51 movq         $8(%rsp), %r15
	0x48, 0x83, 0xc1, 0x20, //0x00003a56 addq         $32, %rcx
	0x49, 0x83, 0xc2, 0xe0, //0x00003a5a addq         $-32, %r10
	//0x00003a5e LBB0_653
	0x4d, 0x85, 0xc0, //0x00003a5e testq        %r8, %r8
	0x0f, 0x85, 0xa5, 0x02, 0x00, 0x00, //0x00003a61 jne          LBB0_688
	0x48, 0x8b, 0x7c, 0x24, 0x10, //0x00003a67 movq         $16(%rsp), %rdi
	0x4d, 0x85, 0xd2, //0x00003a6c testq        %r10, %r10
	0x0f, 0x84, 0x85, 0x00, 0x00, 0x00, //0x00003a6f je           LBB0_665
	//0x00003a75 LBB0_655
	0x4c, 0x89, 0xe2, //0x00003a75 movq         %r12, %rdx
	0x48, 0xf7, 0xda, //0x00003a78 negq         %rdx
	//0x00003a7b LBB0_656
	0x31, 0xf6, //0x00003a7b xorl         %esi, %esi
	//0x00003a7d LBB0_657
	0x0f, 0xb6, 0x04, 0x31, //0x00003a7d movzbl       (%rcx,%rsi), %eax
	0x3c, 0x22, //0x00003a81 cmpb         $34, %al
	0x0f, 0x84, 0x6a, 0x00, 0x00, 0x00, //0x00003a83 je           LBB0_664
	0x3c, 0x5c, //0x00003a89 cmpb         $92, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00003a8b je           LBB0_662
	0x48, 0x83, 0xc6, 0x01, //0x00003a91 addq         $1, %rsi
	0x49, 0x39, 0xf2, //0x00003a95 cmpq         %rsi, %r10
	0x0f, 0x85, 0xdf, 0xff, 0xff, 0xff, //0x00003a98 jne          LBB0_657
	0xe9, 0x73, 0x00, 0x00, 0x00, //0x00003a9e jmp          LBB0_660
	//0x00003aa3 LBB0_662
	0x49, 0x8d, 0x42, 0xff, //0x00003aa3 leaq         $-1(%r10), %rax
	0x48, 0x39, 0xf0, //0x00003aa7 cmpq         %rsi, %rax
	0x0f, 0x84, 0xd9, 0x02, 0x00, 0x00, //0x00003aaa je           LBB0_692
	0x4c, 0x8d, 0x04, 0x0a, //0x00003ab0 leaq         (%rdx,%rcx), %r8
	0x49, 0x01, 0xf0, //0x00003ab4 addq         %rsi, %r8
	0x48, 0x83, 0xff, 0xff, //0x00003ab7 cmpq         $-1, %rdi
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00003abb movq         $16(%rsp), %rax
	0x49, 0x0f, 0x44, 0xc0, //0x00003ac0 cmoveq       %r8, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00003ac4 movq         %rax, $16(%rsp)
	0x49, 0x0f, 0x44, 0xf8, //0x00003ac9 cmoveq       %r8, %rdi
	0x48, 0x01, 0xf1, //0x00003acd addq         %rsi, %rcx
	0x48, 0x83, 0xc1, 0x02, //0x00003ad0 addq         $2, %rcx
	0x4c, 0x89, 0xd0, //0x00003ad4 movq         %r10, %rax
	0x48, 0x29, 0xf0, //0x00003ad7 subq         %rsi, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00003ada addq         $-2, %rax
	0x49, 0x83, 0xc2, 0xfe, //0x00003ade addq         $-2, %r10
	0x49, 0x39, 0xf2, //0x00003ae2 cmpq         %rsi, %r10
	0x49, 0x89, 0xc2, //0x00003ae5 movq         %rax, %r10
	0x0f, 0x85, 0x8d, 0xff, 0xff, 0xff, //0x00003ae8 jne          LBB0_656
	0xe9, 0x96, 0x02, 0x00, 0x00, //0x00003aee jmp          LBB0_692
	//0x00003af3 LBB0_664
	0x48, 0x01, 0xf1, //0x00003af3 addq         %rsi, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00003af6 addq         $1, %rcx
	//0x00003afa LBB0_665
	0x4c, 0x29, 0xe1, //0x00003afa subq         %r12, %rcx
	0x4c, 0x89, 0xfe, //0x00003afd movq         %r15, %rsi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003b00 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00003b05 movq         $32(%rsp), %r11
	0x49, 0x89, 0xdf, //0x00003b0a movq         %rbx, %r15
	0xc5, 0x7d, 0x7f, 0xd2, //0x00003b0d vmovdqa      %ymm10, %ymm2
	0xe9, 0xd9, 0x01, 0x00, 0x00, //0x00003b11 jmp          LBB0_687
	//0x00003b16 LBB0_660
	0x3c, 0x22, //0x00003b16 cmpb         $34, %al
	0x0f, 0x85, 0x6b, 0x02, 0x00, 0x00, //0x00003b18 jne          LBB0_692
	0x4c, 0x01, 0xd1, //0x00003b1e addq         %r10, %rcx
	0xe9, 0xd4, 0xff, 0xff, 0xff, //0x00003b21 jmp          LBB0_665
	//0x00003b26 LBB0_666
	0xc5, 0x7d, 0x7f, 0xe2, //0x00003b26 vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd1, //0x00003b2a vmovdqa      %ymm10, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00003b2e vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x00003b33 vmovdqa      %ymm14, %ymm12
	0x4c, 0x89, 0xfb, //0x00003b38 movq         %r15, %rbx
	0x48, 0x83, 0x7c, 0x24, 0x10, 0xff, //0x00003b3b cmpq         $-1, $16(%rsp)
	0x0f, 0x85, 0x2c, 0x00, 0x00, 0x00, //0x00003b41 jne          LBB0_669
	0x48, 0x89, 0xc8, //0x00003b47 movq         %rcx, %rax
	0x4c, 0x29, 0xe0, //0x00003b4a subq         %r12, %rax
	0x48, 0x0f, 0xbc, 0xfa, //0x00003b4d bsfq         %rdx, %rdi
	0x48, 0x01, 0xc7, //0x00003b51 addq         %rax, %rdi
	0x48, 0x89, 0x7c, 0x24, 0x10, //0x00003b54 movq         %rdi, $16(%rsp)
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x00003b59 jmp          LBB0_669
	//0x00003b5e LBB0_668
	0xc5, 0x7d, 0x7f, 0xe2, //0x00003b5e vmovdqa      %ymm12, %ymm2
	0xc5, 0x7d, 0x7f, 0xd1, //0x00003b62 vmovdqa      %ymm10, %ymm1
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00003b66 vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x00003b6b vmovdqa      %ymm14, %ymm12
	0x4c, 0x89, 0xfb, //0x00003b70 movq         %r15, %rbx
	//0x00003b73 LBB0_669
	0x44, 0x89, 0xd0, //0x00003b73 movl         %r10d, %eax
	0xf7, 0xd0, //0x00003b76 notl         %eax
	0x21, 0xd0, //0x00003b78 andl         %edx, %eax
	0x8d, 0x34, 0x00, //0x00003b7a leal         (%rax,%rax), %esi
	0x41, 0x8d, 0x3c, 0x42, //0x00003b7d leal         (%r10,%rax,2), %edi
	0xf7, 0xd6, //0x00003b81 notl         %esi
	0x21, 0xd6, //0x00003b83 andl         %edx, %esi
	0x81, 0xe6, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003b85 andl         $-1431655766, %esi
	0x45, 0x31, 0xd2, //0x00003b8b xorl         %r10d, %r10d
	0x01, 0xc6, //0x00003b8e addl         %eax, %esi
	0x41, 0x0f, 0x92, 0xc2, //0x00003b90 setb         %r10b
	0x01, 0xf6, //0x00003b94 addl         %esi, %esi
	0x81, 0xf6, 0x55, 0x55, 0x55, 0x55, //0x00003b96 xorl         $1431655765, %esi
	0x21, 0xfe, //0x00003b9c andl         %edi, %esi
	0xf7, 0xd6, //0x00003b9e notl         %esi
	0x41, 0x21, 0xf0, //0x00003ba0 andl         %esi, %r8d
	0x49, 0x89, 0xdf, //0x00003ba3 movq         %rbx, %r15
	0xc4, 0x41, 0x7d, 0x6f, 0xf4, //0x00003ba6 vmovdqa      %ymm12, %ymm14
	0xc4, 0x41, 0x7d, 0x6f, 0xea, //0x00003bab vmovdqa      %ymm10, %ymm13
	0xc5, 0xfe, 0x6f, 0x1d, 0x08, 0xc6, 0xff, 0xff, //0x00003bb0 vmovdqu      $-14840(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xe0, 0xc5, 0xff, 0xff, //0x00003bb8 vmovdqu      $-14880(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd1, //0x00003bc0 vmovdqa      %ymm1, %ymm10
	0xc5, 0x7d, 0x6f, 0xe2, //0x00003bc4 vmovdqa      %ymm2, %ymm12
	0x4d, 0x85, 0xc0, //0x00003bc8 testq        %r8, %r8
	0x0f, 0x85, 0x3a, 0xf0, 0xff, 0xff, //0x00003bcb jne          LBB0_463
	//0x00003bd1 LBB0_670
	0xba, 0x40, 0x00, 0x00, 0x00, //0x00003bd1 movl         $64, %edx
	//0x00003bd6 LBB0_671
	0xc5, 0xbd, 0xda, 0xc8, //0x00003bd6 vpminub      %ymm0, %ymm8, %ymm1
	0xc5, 0xfd, 0x74, 0xc1, //0x00003bda vpcmpeqb     %ymm1, %ymm0, %ymm0
	0xc5, 0xfd, 0xd7, 0xf0, //0x00003bde vpmovmskb    %ymm0, %esi
	0x0f, 0xbc, 0xfe, //0x00003be2 bsfl         %esi, %edi
	0x4d, 0x85, 0xc0, //0x00003be5 testq        %r8, %r8
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x00003be8 je           LBB0_674
	0x85, 0xf6, //0x00003bee testl        %esi, %esi
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00003bf0 movl         $64, %eax
	0x0f, 0x44, 0xf8, //0x00003bf5 cmovel       %eax, %edi
	0x48, 0x39, 0xfa, //0x00003bf8 cmpq         %rdi, %rdx
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003bfb movq         $24(%rsp), %r8
	0x0f, 0x87, 0x6c, 0x04, 0x00, 0x00, //0x00003c00 ja           LBB0_736
	0xc5, 0x7d, 0x7f, 0xea, //0x00003c06 vmovdqa      %ymm13, %ymm2
	0x4c, 0x29, 0xe1, //0x00003c0a subq         %r12, %rcx
	0x48, 0x01, 0xd1, //0x00003c0d addq         %rdx, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00003c10 addq         $1, %rcx
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00003c14 movq         $32(%rsp), %r11
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00003c19 movq         $8(%rsp), %rsi
	0xe9, 0xda, 0xe9, 0xff, 0xff, //0x00003c1e jmp          LBB0_475
	//0x00003c23 LBB0_674
	0x4c, 0x89, 0xfb, //0x00003c23 movq         %r15, %rbx
	0x4c, 0x8b, 0x7c, 0x24, 0x08, //0x00003c26 movq         $8(%rsp), %r15
	0x85, 0xf6, //0x00003c2b testl        %esi, %esi
	0x0f, 0x85, 0x66, 0x04, 0x00, 0x00, //0x00003c2d jne          LBB0_737
	0xc5, 0x7d, 0x7f, 0xe1, //0x00003c33 vmovdqa      %ymm12, %ymm1
	0xc5, 0x7d, 0x7f, 0xd0, //0x00003c37 vmovdqa      %ymm10, %ymm0
	0xc4, 0x41, 0x7d, 0x6f, 0xd5, //0x00003c3b vmovdqa      %ymm13, %ymm10
	0xc4, 0x41, 0x7d, 0x6f, 0xe6, //0x00003c40 vmovdqa      %ymm14, %ymm12
	0x48, 0x83, 0xc1, 0x20, //0x00003c45 addq         $32, %rcx
	0x49, 0x83, 0xc3, 0xe0, //0x00003c49 addq         $-32, %r11
	//0x00003c4d LBB0_676
	0x4d, 0x85, 0xd2, //0x00003c4d testq        %r10, %r10
	0x0f, 0x85, 0xf7, 0x00, 0x00, 0x00, //0x00003c50 jne          LBB0_690
	0x48, 0x8b, 0x44, 0x24, 0x10, //0x00003c56 movq         $16(%rsp), %rax
	0x4d, 0x85, 0xdb, //0x00003c5b testq        %r11, %r11
	0x0f, 0x84, 0x25, 0x01, 0x00, 0x00, //0x00003c5e je           LBB0_692
	//0x00003c64 LBB0_678
	0x0f, 0xb6, 0x11, //0x00003c64 movzbl       (%rcx), %edx
	0x80, 0xfa, 0x22, //0x00003c67 cmpb         $34, %dl
	0x0f, 0x84, 0x5f, 0x00, 0x00, 0x00, //0x00003c6a je           LBB0_686
	0x80, 0xfa, 0x5c, //0x00003c70 cmpb         $92, %dl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x00003c73 je           LBB0_683
	0x80, 0xfa, 0x1f, //0x00003c79 cmpb         $31, %dl
	0x0f, 0x86, 0x24, 0x04, 0x00, 0x00, //0x00003c7c jbe          LBB0_738
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00003c82 movq         $-1, %rdx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00003c89 movl         $1, %esi
	//0x00003c8e LBB0_682
	0x48, 0x01, 0xf1, //0x00003c8e addq         %rsi, %rcx
	0x49, 0x01, 0xd3, //0x00003c91 addq         %rdx, %r11
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x00003c94 jne          LBB0_678
	0xe9, 0xea, 0x00, 0x00, 0x00, //0x00003c9a jmp          LBB0_692
	//0x00003c9f LBB0_683
	0x49, 0x83, 0xfb, 0x01, //0x00003c9f cmpq         $1, %r11
	0x0f, 0x84, 0xe0, 0x00, 0x00, 0x00, //0x00003ca3 je           LBB0_692
	0x48, 0xc7, 0xc2, 0xfe, 0xff, 0xff, 0xff, //0x00003ca9 movq         $-2, %rdx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x00003cb0 movl         $2, %esi
	0x48, 0x83, 0xf8, 0xff, //0x00003cb5 cmpq         $-1, %rax
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00003cb9 jne          LBB0_682
	0x48, 0x89, 0xc8, //0x00003cbf movq         %rcx, %rax
	0x4c, 0x29, 0xe0, //0x00003cc2 subq         %r12, %rax
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00003cc5 movq         %rax, $16(%rsp)
	0xe9, 0xbf, 0xff, 0xff, 0xff, //0x00003cca jmp          LBB0_682
	//0x00003ccf LBB0_686
	0x4c, 0x29, 0xe1, //0x00003ccf subq         %r12, %rcx
	0x48, 0x83, 0xc1, 0x01, //0x00003cd2 addq         $1, %rcx
	0x4c, 0x89, 0xfe, //0x00003cd6 movq         %r15, %rsi
	0x4c, 0x8b, 0x44, 0x24, 0x18, //0x00003cd9 movq         $24(%rsp), %r8
	0x4c, 0x8b, 0x5c, 0x24, 0x20, //0x00003cde movq         $32(%rsp), %r11
	0x49, 0x89, 0xdf, //0x00003ce3 movq         %rbx, %r15
	0xc5, 0x7d, 0x7f, 0xd2, //0x00003ce6 vmovdqa      %ymm10, %ymm2
	0xc4, 0x41, 0x7d, 0x6f, 0xf4, //0x00003cea vmovdqa      %ymm12, %ymm14
	//0x00003cef LBB0_687
	0xc5, 0xfe, 0x6f, 0x1d, 0xc9, 0xc4, 0xff, 0xff, //0x00003cef vmovdqu      $-15159(%rip), %ymm3  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xa1, 0xc4, 0xff, 0xff, //0x00003cf7 vmovdqu      $-15199(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0x7d, 0x6f, 0xd0, //0x00003cff vmovdqa      %ymm0, %ymm10
	0xc5, 0x7d, 0x6f, 0xe1, //0x00003d03 vmovdqa      %ymm1, %ymm12
	0xe9, 0xf1, 0xe8, 0xff, 0xff, //0x00003d07 jmp          LBB0_475
	//0x00003d0c LBB0_688
	0x4d, 0x85, 0xd2, //0x00003d0c testq        %r10, %r10
	0x0f, 0x84, 0x74, 0x00, 0x00, 0x00, //0x00003d0f je           LBB0_692
	0x4c, 0x89, 0xe7, //0x00003d15 movq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x00003d18 notq         %rdi
	0x48, 0x01, 0xcf, //0x00003d1b addq         %rcx, %rdi
	0x48, 0x8b, 0x54, 0x24, 0x10, //0x00003d1e movq         $16(%rsp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x00003d23 cmpq         $-1, %rdx
	0x48, 0x89, 0xd0, //0x00003d27 movq         %rdx, %rax
	0x48, 0x0f, 0x44, 0xc7, //0x00003d2a cmoveq       %rdi, %rax
	0x48, 0x0f, 0x45, 0xfa, //0x00003d2e cmovneq      %rdx, %rdi
	0x48, 0x83, 0xc1, 0x01, //0x00003d32 addq         $1, %rcx
	0x49, 0x83, 0xc2, 0xff, //0x00003d36 addq         $-1, %r10
	0x48, 0x89, 0x44, 0x24, 0x10, //0x00003d3a movq         %rax, $16(%rsp)
	0x4d, 0x85, 0xd2, //0x00003d3f testq        %r10, %r10
	0x0f, 0x85, 0x2d, 0xfd, 0xff, 0xff, //0x00003d42 jne          LBB0_655
	0xe9, 0xad, 0xfd, 0xff, 0xff, //0x00003d48 jmp          LBB0_665
	//0x00003d4d LBB0_690
	0x4d, 0x85, 0xdb, //0x00003d4d testq        %r11, %r11
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x00003d50 je           LBB0_692
	0x4c, 0x89, 0xe0, //0x00003d56 movq         %r12, %rax
	0x48, 0xf7, 0xd0, //0x00003d59 notq         %rax
	0x48, 0x01, 0xc8, //0x00003d5c addq         %rcx, %rax
	0x48, 0x8b, 0x74, 0x24, 0x10, //0x00003d5f movq         $16(%rsp), %rsi
	0x48, 0x83, 0xfe, 0xff, //0x00003d64 cmpq         $-1, %rsi
	0x48, 0x89, 0xf2, //0x00003d68 movq         %rsi, %rdx
	0x48, 0x0f, 0x44, 0xd0, //0x00003d6b cmoveq       %rax, %rdx
	0x48, 0x0f, 0x45, 0xc6, //0x00003d6f cmovneq      %rsi, %rax
	0x48, 0x83, 0xc1, 0x01, //0x00003d73 addq         $1, %rcx
	0x49, 0x83, 0xc3, 0xff, //0x00003d77 addq         $-1, %r11
	0x48, 0x89, 0x54, 0x24, 0x10, //0x00003d7b movq         %rdx, $16(%rsp)
	0x4d, 0x85, 0xdb, //0x00003d80 testq        %r11, %r11
	0x0f, 0x85, 0xdb, 0xfe, 0xff, 0xff, //0x00003d83 jne          LBB0_678
	//0x00003d89 LBB0_692
	0x4c, 0x89, 0xfe, //0x00003d89 movq         %r15, %rsi
	0xe9, 0xf2, 0x01, 0x00, 0x00, //0x00003d8c jmp          LBB0_716
	//0x00003d91 LBB0_693
	0x48, 0x8b, 0x44, 0x24, 0x08, //0x00003d91 movq         $8(%rsp), %rax
	0x4c, 0x89, 0x28, //0x00003d96 movq         %r13, (%rax)
	//0x00003d99 LBB0_694
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003d99 movq         $-1, %rax
	0xe9, 0x58, 0x00, 0x00, 0x00, //0x00003da0 jmp          LBB0_721
	//0x00003da5 LBB0_713
	0x48, 0xc7, 0xc0, 0xf9, 0xff, 0xff, 0xff, //0x00003da5 movq         $-7, %rax
	0xe9, 0x4c, 0x00, 0x00, 0x00, //0x00003dac jmp          LBB0_721
	//0x00003db1 LBB0_696
	0x48, 0x89, 0xc8, //0x00003db1 movq         %rcx, %rax
	0x48, 0x83, 0xf9, 0xff, //0x00003db4 cmpq         $-1, %rcx
	0x48, 0x8b, 0x54, 0x24, 0x10, //0x00003db8 movq         $16(%rsp), %rdx
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x00003dbd jne          LBB0_698
	//0x00003dc3 LBB0_697
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003dc3 movq         $-1, %rax
	0x4c, 0x89, 0xf2, //0x00003dca movq         %r14, %rdx
	//0x00003dcd LBB0_698
	0x48, 0x89, 0x13, //0x00003dcd movq         %rdx, (%rbx)
	0xe9, 0x28, 0x00, 0x00, 0x00, //0x00003dd0 jmp          LBB0_721
	//0x00003dd5 LBB0_699
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003dd5 movq         $-1, %rax
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x00003ddc jmp          LBB0_700
	//0x00003de1 LBB0_357
	0x4c, 0x89, 0xd8, //0x00003de1 movq         %r11, %rax
	//0x00003de4 LBB0_700
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x00003de4 movq         $8(%rsp), %rdx
	0x48, 0x8b, 0x0a, //0x00003de9 movq         (%rdx), %rcx
	0x48, 0x29, 0xc1, //0x00003dec subq         %rax, %rcx
	0x48, 0x83, 0xc1, 0xfe, //0x00003def addq         $-2, %rcx
	0x48, 0x89, 0x0a, //0x00003df3 movq         %rcx, (%rdx)
	//0x00003df6 LBB0_720
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003df6 movq         $-2, %rax
	//0x00003dfd LBB0_721
	0x48, 0x8d, 0x65, 0xd8, //0x00003dfd leaq         $-40(%rbp), %rsp
	0x5b, //0x00003e01 popq         %rbx
	0x41, 0x5c, //0x00003e02 popq         %r12
	0x41, 0x5d, //0x00003e04 popq         %r13
	0x41, 0x5e, //0x00003e06 popq         %r14
	0x41, 0x5f, //0x00003e08 popq         %r15
	0x5d, //0x00003e0a popq         %rbp
	0xc5, 0xf8, 0x77, //0x00003e0b vzeroupper   
	0xc3, //0x00003e0e retq         
	//0x00003e0f LBB0_703
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x00003e0f movq         $8(%rsp), %rdx
	0x48, 0x89, 0x0a, //0x00003e14 movq         %rcx, (%rdx)
	0xe9, 0xe1, 0xff, 0xff, 0xff, //0x00003e17 jmp          LBB0_721
	//0x00003e1c LBB0_704
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003e1c movq         $-2, %rax
	0x48, 0x8b, 0x54, 0x24, 0x10, //0x00003e23 movq         $16(%rsp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x00003e28 cmpq         $-1, %rdx
	0x0f, 0x84, 0x1a, 0x01, 0x00, 0x00, //0x00003e2c je           LBB0_712
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x00003e32 movq         $8(%rsp), %rbx
	0x48, 0x89, 0x13, //0x00003e37 movq         %rdx, (%rbx)
	0xe9, 0xbe, 0xff, 0xff, 0xff, //0x00003e3a jmp          LBB0_721
	//0x00003e3f LBB0_706
	0x48, 0x8b, 0x4c, 0x24, 0x08, //0x00003e3f movq         $8(%rsp), %rcx
	0x48, 0x89, 0x11, //0x00003e44 movq         %rdx, (%rcx)
	0xe9, 0xb1, 0xff, 0xff, 0xff, //0x00003e47 jmp          LBB0_721
	//0x00003e4c LBB0_331
	0x4c, 0x89, 0x2a, //0x00003e4c movq         %r13, (%rdx)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003e4f movq         $-2, %rax
	0x41, 0x80, 0x3a, 0x6e, //0x00003e56 cmpb         $110, (%r10)
	0x0f, 0x85, 0x9d, 0xff, 0xff, 0xff, //0x00003e5a jne          LBB0_721
	0x49, 0x8d, 0x4d, 0x01, //0x00003e60 leaq         $1(%r13), %rcx
	0x48, 0x89, 0x0a, //0x00003e64 movq         %rcx, (%rdx)
	0x43, 0x80, 0x7c, 0x2c, 0x01, 0x75, //0x00003e67 cmpb         $117, $1(%r12,%r13)
	0x0f, 0x85, 0x8a, 0xff, 0xff, 0xff, //0x00003e6d jne          LBB0_721
	0x49, 0x8d, 0x4d, 0x02, //0x00003e73 leaq         $2(%r13), %rcx
	0x48, 0x89, 0x0a, //0x00003e77 movq         %rcx, (%rdx)
	0x43, 0x80, 0x7c, 0x2c, 0x02, 0x6c, //0x00003e7a cmpb         $108, $2(%r12,%r13)
	0x0f, 0x85, 0x77, 0xff, 0xff, 0xff, //0x00003e80 jne          LBB0_721
	0x49, 0x8d, 0x4d, 0x03, //0x00003e86 leaq         $3(%r13), %rcx
	0x48, 0x89, 0x0a, //0x00003e8a movq         %rcx, (%rdx)
	0x43, 0x80, 0x7c, 0x2c, 0x03, 0x6c, //0x00003e8d cmpb         $108, $3(%r12,%r13)
	0x0f, 0x85, 0x64, 0xff, 0xff, 0xff, //0x00003e93 jne          LBB0_721
	0xe9, 0xa2, 0x00, 0x00, 0x00, //0x00003e99 jmp          LBB0_335
	//0x00003e9e LBB0_707
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003e9e movq         $-2, %rax
	0x80, 0xf9, 0x61, //0x00003ea5 cmpb         $97, %cl
	0x0f, 0x85, 0x4f, 0xff, 0xff, 0xff, //0x00003ea8 jne          LBB0_721
	0x49, 0x8d, 0x4d, 0x02, //0x00003eae leaq         $2(%r13), %rcx
	0x48, 0x89, 0x0a, //0x00003eb2 movq         %rcx, (%rdx)
	0x43, 0x80, 0x7c, 0x2c, 0x02, 0x6c, //0x00003eb5 cmpb         $108, $2(%r12,%r13)
	0x0f, 0x85, 0x3c, 0xff, 0xff, 0xff, //0x00003ebb jne          LBB0_721
	0x49, 0x8d, 0x4d, 0x03, //0x00003ec1 leaq         $3(%r13), %rcx
	0x48, 0x89, 0x0a, //0x00003ec5 movq         %rcx, (%rdx)
	0x43, 0x80, 0x7c, 0x2c, 0x03, 0x73, //0x00003ec8 cmpb         $115, $3(%r12,%r13)
	0x0f, 0x85, 0x29, 0xff, 0xff, 0xff, //0x00003ece jne          LBB0_721
	0x49, 0x8d, 0x4d, 0x04, //0x00003ed4 leaq         $4(%r13), %rcx
	0x48, 0x89, 0x0a, //0x00003ed8 movq         %rcx, (%rdx)
	0x43, 0x80, 0x7c, 0x2c, 0x04, 0x65, //0x00003edb cmpb         $101, $4(%r12,%r13)
	0x0f, 0x85, 0x16, 0xff, 0xff, 0xff, //0x00003ee1 jne          LBB0_721
	0x49, 0x83, 0xc5, 0x05, //0x00003ee7 addq         $5, %r13
	0x4c, 0x89, 0x2a, //0x00003eeb movq         %r13, (%rdx)
	0xe9, 0x0a, 0xff, 0xff, 0xff, //0x00003eee jmp          LBB0_721
	//0x00003ef3 LBB0_341
	0x4c, 0x89, 0x2a, //0x00003ef3 movq         %r13, (%rdx)
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003ef6 movq         $-2, %rax
	0x41, 0x80, 0x3a, 0x74, //0x00003efd cmpb         $116, (%r10)
	0x0f, 0x85, 0xf6, 0xfe, 0xff, 0xff, //0x00003f01 jne          LBB0_721
	0x49, 0x8d, 0x4d, 0x01, //0x00003f07 leaq         $1(%r13), %rcx
	0x48, 0x89, 0x0a, //0x00003f0b movq         %rcx, (%rdx)
	0x43, 0x80, 0x7c, 0x2c, 0x01, 0x72, //0x00003f0e cmpb         $114, $1(%r12,%r13)
	0x0f, 0x85, 0xe3, 0xfe, 0xff, 0xff, //0x00003f14 jne          LBB0_721
	0x49, 0x8d, 0x4d, 0x02, //0x00003f1a leaq         $2(%r13), %rcx
	0x48, 0x89, 0x0a, //0x00003f1e movq         %rcx, (%rdx)
	0x43, 0x80, 0x7c, 0x2c, 0x02, 0x75, //0x00003f21 cmpb         $117, $2(%r12,%r13)
	0x0f, 0x85, 0xd0, 0xfe, 0xff, 0xff, //0x00003f27 jne          LBB0_721
	0x49, 0x8d, 0x4d, 0x03, //0x00003f2d leaq         $3(%r13), %rcx
	0x48, 0x89, 0x0a, //0x00003f31 movq         %rcx, (%rdx)
	0x43, 0x80, 0x7c, 0x2c, 0x03, 0x65, //0x00003f34 cmpb         $101, $3(%r12,%r13)
	0x0f, 0x85, 0xbd, 0xfe, 0xff, 0xff, //0x00003f3a jne          LBB0_721
	//0x00003f40 LBB0_335
	0x49, 0x83, 0xc5, 0x04, //0x00003f40 addq         $4, %r13
	0x4c, 0x89, 0x2a, //0x00003f44 movq         %r13, (%rdx)
	0xe9, 0xb1, 0xfe, 0xff, 0xff, //0x00003f47 jmp          LBB0_721
	//0x00003f4c LBB0_712
	0x48, 0x0f, 0xbc, 0xd6, //0x00003f4c bsfq         %rsi, %rdx
	0x48, 0x01, 0xca, //0x00003f50 addq         %rcx, %rdx
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x00003f53 movq         $8(%rsp), %rbx
	0x48, 0x89, 0x13, //0x00003f58 movq         %rdx, (%rbx)
	0xe9, 0x9d, 0xfe, 0xff, 0xff, //0x00003f5b jmp          LBB0_721
	//0x00003f60 LBB0_714
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003f60 movq         $-1, %rax
	0x48, 0x8b, 0x54, 0x24, 0x08, //0x00003f67 movq         $8(%rsp), %rdx
	0xe9, 0x27, 0x00, 0x00, 0x00, //0x00003f6c jmp          LBB0_719
	//0x00003f71 LBB0_715
	0x48, 0x89, 0xc8, //0x00003f71 movq         %rcx, %rax
	0x48, 0x83, 0xf9, 0xff, //0x00003f74 cmpq         $-1, %rcx
	0x48, 0x8b, 0x54, 0x24, 0x10, //0x00003f78 movq         $16(%rsp), %rdx
	0x0f, 0x85, 0x0a, 0x00, 0x00, 0x00, //0x00003f7d jne          LBB0_717
	//0x00003f83 LBB0_716
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00003f83 movq         $-1, %rax
	0x4c, 0x89, 0xf2, //0x00003f8a movq         %r14, %rdx
	//0x00003f8d LBB0_717
	0x48, 0x89, 0x16, //0x00003f8d movq         %rdx, (%rsi)
	0xe9, 0x68, 0xfe, 0xff, 0xff, //0x00003f90 jmp          LBB0_721
	//0x00003f95 LBB0_718
	0x48, 0x89, 0xd8, //0x00003f95 movq         %rbx, %rax
	//0x00003f98 LBB0_719
	0x48, 0xf7, 0xd0, //0x00003f98 notq         %rax
	0x48, 0x01, 0x02, //0x00003f9b addq         %rax, (%rdx)
	0xe9, 0x53, 0xfe, 0xff, 0xff, //0x00003f9e jmp          LBB0_720
	//0x00003fa3 LBB0_724
	0x49, 0x89, 0xce, //0x00003fa3 movq         %rcx, %r14
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x00003fa6 movq         $8(%rsp), %rbx
	0xe9, 0x13, 0xfe, 0xff, 0xff, //0x00003fab jmp          LBB0_697
	//0x00003fb0 LBB0_722
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003fb0 movq         $-2, %rax
	0x48, 0x8b, 0x54, 0x24, 0x10, //0x00003fb7 movq         $16(%rsp), %rdx
	0x48, 0x83, 0xfa, 0xff, //0x00003fbc cmpq         $-1, %rdx
	0x0f, 0x84, 0x1f, 0x00, 0x00, 0x00, //0x00003fc0 je           LBB0_725
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00003fc6 movq         $8(%rsp), %rsi
	0x48, 0x89, 0x16, //0x00003fcb movq         %rdx, (%rsi)
	0xe9, 0x2a, 0xfe, 0xff, 0xff, //0x00003fce jmp          LBB0_721
	//0x00003fd3 LBB0_239
	0x48, 0x01, 0xca, //0x00003fd3 addq         %rcx, %rdx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00003fd6 movq         $-2, %rax
	0x48, 0x89, 0x13, //0x00003fdd movq         %rdx, (%rbx)
	0xe9, 0x18, 0xfe, 0xff, 0xff, //0x00003fe0 jmp          LBB0_721
	//0x00003fe5 LBB0_725
	0x48, 0x0f, 0xbc, 0xd6, //0x00003fe5 bsfq         %rsi, %rdx
	0x48, 0x01, 0xca, //0x00003fe9 addq         %rcx, %rdx
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00003fec movq         $8(%rsp), %rsi
	0x48, 0x89, 0x16, //0x00003ff1 movq         %rdx, (%rsi)
	0xe9, 0x04, 0xfe, 0xff, 0xff, //0x00003ff4 jmp          LBB0_721
	//0x00003ff9 LBB0_727
	0x49, 0x89, 0xce, //0x00003ff9 movq         %rcx, %r14
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00003ffc movq         $8(%rsp), %rsi
	0xe9, 0x7d, 0xff, 0xff, 0xff, //0x00004001 jmp          LBB0_716
	//0x00004006 LBB0_728
	0x48, 0x01, 0xca, //0x00004006 addq         %rcx, %rdx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00004009 movq         $-2, %rax
	0x48, 0x89, 0x16, //0x00004010 movq         %rdx, (%rsi)
	0xe9, 0xe5, 0xfd, 0xff, 0xff, //0x00004013 jmp          LBB0_721
	//0x00004018 LBB0_729
	0x48, 0x8b, 0x4c, 0x24, 0x20, //0x00004018 movq         $32(%rsp), %rcx
	0x48, 0x8b, 0x49, 0x08, //0x0000401d movq         $8(%rcx), %rcx
	0x48, 0x89, 0x0e, //0x00004021 movq         %rcx, (%rsi)
	0xe9, 0xd4, 0xfd, 0xff, 0xff, //0x00004024 jmp          LBB0_721
	//0x00004029 LBB0_733
	0x89, 0xf8, //0x00004029 movl         %edi, %eax
	0x48, 0x89, 0xca, //0x0000402b movq         %rcx, %rdx
	0x4c, 0x29, 0xe2, //0x0000402e subq         %r12, %rdx
	0x48, 0x01, 0xc2, //0x00004031 addq         %rax, %rdx
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x00004034 jmp          LBB0_732
	//0x00004039 LBB0_730
	0x48, 0x89, 0xf3, //0x00004039 movq         %rsi, %rbx
	0xe9, 0x82, 0xfd, 0xff, 0xff, //0x0000403c jmp          LBB0_697
	//0x00004041 LBB0_731
	0x4c, 0x29, 0xe1, //0x00004041 subq         %r12, %rcx
	0x89, 0xfa, //0x00004044 movl         %edi, %edx
	0x48, 0x01, 0xca, //0x00004046 addq         %rcx, %rdx
	//0x00004049 LBB0_732
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00004049 movq         $-2, %rax
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x00004050 movq         $8(%rsp), %rbx
	0x48, 0x89, 0x13, //0x00004055 movq         %rdx, (%rbx)
	0xe9, 0xa0, 0xfd, 0xff, 0xff, //0x00004058 jmp          LBB0_721
	//0x0000405d LBB0_735
	0x48, 0x89, 0xca, //0x0000405d movq         %rcx, %rdx
	0x4c, 0x29, 0xe2, //0x00004060 subq         %r12, %rdx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00004063 movq         $-2, %rax
	0x48, 0x89, 0x13, //0x0000406a movq         %rdx, (%rbx)
	0xe9, 0x8b, 0xfd, 0xff, 0xff, //0x0000406d jmp          LBB0_721
	//0x00004072 LBB0_736
	0x89, 0xf8, //0x00004072 movl         %edi, %eax
	0x48, 0x89, 0xca, //0x00004074 movq         %rcx, %rdx
	0x4c, 0x29, 0xe2, //0x00004077 subq         %r12, %rdx
	0x48, 0x01, 0xc2, //0x0000407a addq         %rax, %rdx
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000407d movq         $-2, %rax
	0x48, 0x8b, 0x74, 0x24, 0x08, //0x00004084 movq         $8(%rsp), %rsi
	0x48, 0x89, 0x16, //0x00004089 movq         %rdx, (%rsi)
	0xe9, 0x6c, 0xfd, 0xff, 0xff, //0x0000408c jmp          LBB0_721
	//0x00004091 LBB0_741
	0x4c, 0x89, 0xfb, //0x00004091 movq         %r15, %rbx
	0xe9, 0x2a, 0xfd, 0xff, 0xff, //0x00004094 jmp          LBB0_697
	//0x00004099 LBB0_737
	0x4c, 0x29, 0xe1, //0x00004099 subq         %r12, %rcx
	0x89, 0xfa, //0x0000409c movl         %edi, %edx
	0x48, 0x01, 0xca, //0x0000409e addq         %rcx, %rdx
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x000040a1 jmp          LBB0_739
	//0x000040a6 LBB0_738
	0x48, 0x89, 0xca, //0x000040a6 movq         %rcx, %rdx
	0x4c, 0x29, 0xe2, //0x000040a9 subq         %r12, %rdx
	//0x000040ac LBB0_739
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x000040ac movq         $-2, %rax
	0x4c, 0x89, 0xfe, //0x000040b3 movq         %r15, %rsi
	0x49, 0x89, 0x17, //0x000040b6 movq         %rdx, (%r15)
	0xe9, 0x3f, 0xfd, 0xff, 0xff, //0x000040b9 jmp          LBB0_721
	//0x000040be LBB0_740
	0x48, 0x89, 0xc3, //0x000040be movq         %rax, %rbx
	0xe9, 0xfd, 0xfc, 0xff, 0xff, //0x000040c1 jmp          LBB0_697
	//0x000040c6 LBB0_568
	0x48, 0x8b, 0x5c, 0x24, 0x08, //0x000040c6 movq         $8(%rsp), %rbx
	0xe9, 0xf3, 0xfc, 0xff, 0xff, //0x000040cb jmp          LBB0_697
	//0x000040d0 .p2align 2, 0x90
	// // .set L0_0_set_39, LBB0_39-LJTI0_0
	// // .set L0_0_set_67, LBB0_67-LJTI0_0
	// // .set L0_0_set_44, LBB0_44-LJTI0_0
	// // .set L0_0_set_65, LBB0_65-LJTI0_0
	// // .set L0_0_set_42, LBB0_42-LJTI0_0
	// // .set L0_0_set_69, LBB0_69-LJTI0_0
	//0x000040d0 LJTI0_0
	0xd6, 0xc4, 0xff, 0xff, //0x000040d0 .long L0_0_set_39
	0x85, 0xc6, 0xff, 0xff, //0x000040d4 .long L0_0_set_67
	0x0c, 0xc5, 0xff, 0xff, //0x000040d8 .long L0_0_set_44
	0x6f, 0xc6, 0xff, 0xff, //0x000040dc .long L0_0_set_65
	0xed, 0xc4, 0xff, 0xff, //0x000040e0 .long L0_0_set_42
	0xb0, 0xc6, 0xff, 0xff, //0x000040e4 .long L0_0_set_69
	// // .set L0_1_set_721, LBB0_721-LJTI0_1
	// // .set L0_1_set_720, LBB0_720-LJTI0_1
	// // .set L0_1_set_251, LBB0_251-LJTI0_1
	// // .set L0_1_set_270, LBB0_270-LJTI0_1
	// // .set L0_1_set_86, LBB0_86-LJTI0_1
	// // .set L0_1_set_326, LBB0_326-LJTI0_1
	// // .set L0_1_set_248, LBB0_248-LJTI0_1
	// // .set L0_1_set_329, LBB0_329-LJTI0_1
	// // .set L0_1_set_339, LBB0_339-LJTI0_1
	// // .set L0_1_set_336, LBB0_336-LJTI0_1
	//0x000040e8 LJTI0_1
	0x15, 0xfd, 0xff, 0xff, //0x000040e8 .long L0_1_set_721
	0x0e, 0xfd, 0xff, 0xff, //0x000040ec .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000040f0 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000040f4 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000040f8 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000040fc .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004100 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004104 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004108 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000410c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004110 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004114 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004118 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000411c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004120 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004124 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004128 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000412c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004130 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004134 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004138 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000413c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004140 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004144 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004148 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000414c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004150 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004154 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004158 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000415c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004160 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004164 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004168 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000416c .long L0_1_set_720
	0x0b, 0xd3, 0xff, 0xff, //0x00004170 .long L0_1_set_251
	0x0e, 0xfd, 0xff, 0xff, //0x00004174 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004178 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000417c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004180 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004184 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004188 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000418c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004190 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004194 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004198 .long L0_1_set_720
	0x52, 0xd4, 0xff, 0xff, //0x0000419c .long L0_1_set_270
	0x0e, 0xfd, 0xff, 0xff, //0x000041a0 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000041a4 .long L0_1_set_720
	0xe3, 0xc6, 0xff, 0xff, //0x000041a8 .long L0_1_set_86
	0xe3, 0xc6, 0xff, 0xff, //0x000041ac .long L0_1_set_86
	0xe3, 0xc6, 0xff, 0xff, //0x000041b0 .long L0_1_set_86
	0xe3, 0xc6, 0xff, 0xff, //0x000041b4 .long L0_1_set_86
	0xe3, 0xc6, 0xff, 0xff, //0x000041b8 .long L0_1_set_86
	0xe3, 0xc6, 0xff, 0xff, //0x000041bc .long L0_1_set_86
	0xe3, 0xc6, 0xff, 0xff, //0x000041c0 .long L0_1_set_86
	0xe3, 0xc6, 0xff, 0xff, //0x000041c4 .long L0_1_set_86
	0xe3, 0xc6, 0xff, 0xff, //0x000041c8 .long L0_1_set_86
	0xe3, 0xc6, 0xff, 0xff, //0x000041cc .long L0_1_set_86
	0x0e, 0xfd, 0xff, 0xff, //0x000041d0 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000041d4 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000041d8 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000041dc .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000041e0 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000041e4 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000041e8 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000041ec .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000041f0 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000041f4 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000041f8 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000041fc .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004200 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004204 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004208 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000420c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004210 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004214 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004218 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000421c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004220 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004224 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004228 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000422c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004230 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004234 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004238 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000423c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004240 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004244 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004248 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000424c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004250 .long L0_1_set_720
	0xdc, 0xd7, 0xff, 0xff, //0x00004254 .long L0_1_set_326
	0x0e, 0xfd, 0xff, 0xff, //0x00004258 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000425c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004260 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004264 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004268 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000426c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004270 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004274 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004278 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000427c .long L0_1_set_720
	0xdc, 0xd2, 0xff, 0xff, //0x00004280 .long L0_1_set_248
	0x0e, 0xfd, 0xff, 0xff, //0x00004284 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004288 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000428c .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004290 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004294 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x00004298 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x0000429c .long L0_1_set_720
	0x0a, 0xd8, 0xff, 0xff, //0x000042a0 .long L0_1_set_329
	0x0e, 0xfd, 0xff, 0xff, //0x000042a4 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000042a8 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000042ac .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000042b0 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000042b4 .long L0_1_set_720
	0x60, 0xd8, 0xff, 0xff, //0x000042b8 .long L0_1_set_339
	0x0e, 0xfd, 0xff, 0xff, //0x000042bc .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000042c0 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000042c4 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000042c8 .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000042cc .long L0_1_set_720
	0x0e, 0xfd, 0xff, 0xff, //0x000042d0 .long L0_1_set_720
	0x32, 0xd8, 0xff, 0xff, //0x000042d4 .long L0_1_set_336
	// // .set L0_2_set_319, LBB0_319-LJTI0_2
	// // .set L0_2_set_480, LBB0_480-LJTI0_2
	// // .set L0_2_set_324, LBB0_324-LJTI0_2
	// // .set L0_2_set_322, LBB0_322-LJTI0_2
	//0x000042d8 LJTI0_2
	0xaa, 0xd5, 0xff, 0xff, //0x000042d8 .long L0_2_set_319
	0x1c, 0xe4, 0xff, 0xff, //0x000042dc .long L0_2_set_480
	0xaa, 0xd5, 0xff, 0xff, //0x000042e0 .long L0_2_set_319
	0xd9, 0xd5, 0xff, 0xff, //0x000042e4 .long L0_2_set_324
	0x1c, 0xe4, 0xff, 0xff, //0x000042e8 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x000042ec .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x000042f0 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x000042f4 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x000042f8 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x000042fc .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x00004300 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x00004304 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x00004308 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x0000430c .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x00004310 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x00004314 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x00004318 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x0000431c .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x00004320 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x00004324 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x00004328 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x0000432c .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x00004330 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x00004334 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x00004338 .long L0_2_set_480
	0x1c, 0xe4, 0xff, 0xff, //0x0000433c .long L0_2_set_480
	0xc6, 0xd5, 0xff, 0xff, //0x00004340 .long L0_2_set_322
	// // .set L0_3_set_138, LBB0_138-LJTI0_3
	// // .set L0_3_set_345, LBB0_345-LJTI0_3
	// // .set L0_3_set_132, LBB0_132-LJTI0_3
	// // .set L0_3_set_141, LBB0_141-LJTI0_3
	//0x00004344 LJTI0_3
	0xa6, 0xc8, 0xff, 0xff, //0x00004344 .long L0_3_set_138
	0x53, 0xd6, 0xff, 0xff, //0x00004348 .long L0_3_set_345
	0xa6, 0xc8, 0xff, 0xff, //0x0000434c .long L0_3_set_138
	0x55, 0xc8, 0xff, 0xff, //0x00004350 .long L0_3_set_132
	0x53, 0xd6, 0xff, 0xff, //0x00004354 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004358 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x0000435c .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004360 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004364 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004368 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x0000436c .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004370 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004374 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004378 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x0000437c .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004380 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004384 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004388 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x0000438c .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004390 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004394 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x00004398 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x0000439c .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x000043a0 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x000043a4 .long L0_3_set_345
	0x53, 0xd6, 0xff, 0xff, //0x000043a8 .long L0_3_set_345
	0xc9, 0xc8, 0xff, 0xff, //0x000043ac .long L0_3_set_141
	//0x000043b0 .p2align 2, 0x00
	//0x000043b0 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x000043b0 .long 2
}
 
