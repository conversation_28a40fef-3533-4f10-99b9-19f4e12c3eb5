// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_i64toa = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x59, 0x17, 0xb7, 0xd1, 0x00, 0x00, 0x00, 0x00, // .quad 3518437209
	0x59, 0x17, 0xb7, 0xd1, 0x00, 0x00, 0x00, 0x00, //0x00000008 .quad 3518437209
	//0x00000010 LCPI0_3
	0x0a, 0x00, //0x00000010 .word 10
	0x0a, 0x00, //0x00000012 .word 10
	0x0a, 0x00, //0x00000014 .word 10
	0x0a, 0x00, //0x00000016 .word 10
	0x0a, 0x00, //0x00000018 .word 10
	0x0a, 0x00, //0x0000001a .word 10
	0x0a, 0x00, //0x0000001c .word 10
	0x0a, 0x00, //0x0000001e .word 10
	//0x00000020 LCPI0_4
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, //0x00000020 QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	//0x00000030 .p2align 3, 0x00
	//0x00000030 LCPI0_1
	0xc5, 0x20, 0x7b, 0x14, 0x34, 0x33, 0x00, 0x80, //0x00000030 .quad -9223315738079846203
	//0x00000038 LCPI0_2
	0x80, 0x00, 0x00, 0x08, 0x00, 0x20, 0x00, 0x80, //0x00000038 .quad -9223336852348469120
	//0x00000040 .p2align 4, 0x90
	//0x00000040 _i64toa
	0x55, //0x00000040 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000041 movq         %rsp, %rbp
	0x48, 0x85, 0xf6, //0x00000044 testq        %rsi, %rsi
	0x0f, 0x88, 0xb2, 0x00, 0x00, 0x00, //0x00000047 js           LBB0_25
	0x48, 0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x0000004d cmpq         $9999, %rsi
	0x0f, 0x87, 0xfb, 0x00, 0x00, 0x00, //0x00000054 ja           LBB0_9
	0x0f, 0xb7, 0xc6, //0x0000005a movzwl       %si, %eax
	0xc1, 0xe8, 0x02, //0x0000005d shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000060 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000066 shrl         $17, %eax
	0x48, 0x8d, 0x14, 0x00, //0x00000069 leaq         (%rax,%rax), %rdx
	0x6b, 0xc0, 0x64, //0x0000006d imull        $100, %eax, %eax
	0x89, 0xf1, //0x00000070 movl         %esi, %ecx
	0x29, 0xc1, //0x00000072 subl         %eax, %ecx
	0x0f, 0xb7, 0xc1, //0x00000074 movzwl       %cx, %eax
	0x48, 0x01, 0xc0, //0x00000077 addq         %rax, %rax
	0x81, 0xfe, 0xe8, 0x03, 0x00, 0x00, //0x0000007a cmpl         $1000, %esi
	0x0f, 0x82, 0x16, 0x00, 0x00, 0x00, //0x00000080 jb           LBB0_4
	0x48, 0x8d, 0x0d, 0x93, 0x08, 0x00, 0x00, //0x00000086 leaq         $2195(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x0c, 0x0a, //0x0000008d movb         (%rdx,%rcx), %cl
	0x88, 0x0f, //0x00000090 movb         %cl, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000092 movl         $1, %ecx
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x00000097 jmp          LBB0_5
	//0x0000009c LBB0_4
	0x31, 0xc9, //0x0000009c xorl         %ecx, %ecx
	0x83, 0xfe, 0x64, //0x0000009e cmpl         $100, %esi
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x000000a1 jb           LBB0_6
	//0x000000a7 LBB0_5
	0x0f, 0xb7, 0xd2, //0x000000a7 movzwl       %dx, %edx
	0x48, 0x83, 0xca, 0x01, //0x000000aa orq          $1, %rdx
	0x48, 0x8d, 0x35, 0x6b, 0x08, 0x00, 0x00, //0x000000ae leaq         $2155(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x32, //0x000000b5 movb         (%rdx,%rsi), %dl
	0x89, 0xce, //0x000000b8 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x000000ba addl         $1, %ecx
	0x88, 0x14, 0x37, //0x000000bd movb         %dl, (%rdi,%rsi)
	//0x000000c0 LBB0_7
	0x48, 0x8d, 0x15, 0x59, 0x08, 0x00, 0x00, //0x000000c0 leaq         $2137(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x10, //0x000000c7 movb         (%rax,%rdx), %dl
	0x89, 0xce, //0x000000ca movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x000000cc addl         $1, %ecx
	0x88, 0x14, 0x37, //0x000000cf movb         %dl, (%rdi,%rsi)
	//0x000000d2 LBB0_8
	0x0f, 0xb7, 0xc0, //0x000000d2 movzwl       %ax, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000000d5 orq          $1, %rax
	0x48, 0x8d, 0x15, 0x40, 0x08, 0x00, 0x00, //0x000000d9 leaq         $2112(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x10, //0x000000e0 movb         (%rax,%rdx), %al
	0x89, 0xca, //0x000000e3 movl         %ecx, %edx
	0x83, 0xc1, 0x01, //0x000000e5 addl         $1, %ecx
	0x88, 0x04, 0x17, //0x000000e8 movb         %al, (%rdi,%rdx)
	0x89, 0xc8, //0x000000eb movl         %ecx, %eax
	0x5d, //0x000000ed popq         %rbp
	0xc3, //0x000000ee retq         
	//0x000000ef LBB0_6
	0x31, 0xc9, //0x000000ef xorl         %ecx, %ecx
	0x83, 0xfe, 0x0a, //0x000000f1 cmpl         $10, %esi
	0x0f, 0x83, 0xc6, 0xff, 0xff, 0xff, //0x000000f4 jae          LBB0_7
	0xe9, 0xd3, 0xff, 0xff, 0xff, //0x000000fa jmp          LBB0_8
	//0x000000ff LBB0_25
	0xc6, 0x07, 0x2d, //0x000000ff movb         $45, (%rdi)
	0x48, 0xf7, 0xde, //0x00000102 negq         %rsi
	0x48, 0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x00000105 cmpq         $9999, %rsi
	0x0f, 0x87, 0xd9, 0x01, 0x00, 0x00, //0x0000010c ja           LBB0_33
	0x0f, 0xb7, 0xc6, //0x00000112 movzwl       %si, %eax
	0xc1, 0xe8, 0x02, //0x00000115 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000118 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x0000011e shrl         $17, %eax
	0x48, 0x8d, 0x14, 0x00, //0x00000121 leaq         (%rax,%rax), %rdx
	0x6b, 0xc0, 0x64, //0x00000125 imull        $100, %eax, %eax
	0x89, 0xf1, //0x00000128 movl         %esi, %ecx
	0x29, 0xc1, //0x0000012a subl         %eax, %ecx
	0x0f, 0xb7, 0xc1, //0x0000012c movzwl       %cx, %eax
	0x48, 0x01, 0xc0, //0x0000012f addq         %rax, %rax
	0x81, 0xfe, 0xe8, 0x03, 0x00, 0x00, //0x00000132 cmpl         $1000, %esi
	0x0f, 0x82, 0xab, 0x00, 0x00, 0x00, //0x00000138 jb           LBB0_28
	0x48, 0x8d, 0x0d, 0xdb, 0x07, 0x00, 0x00, //0x0000013e leaq         $2011(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x0c, 0x0a, //0x00000145 movb         (%rdx,%rcx), %cl
	0x88, 0x4f, 0x01, //0x00000148 movb         %cl, $1(%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000014b movl         $1, %ecx
	0xe9, 0x9f, 0x00, 0x00, 0x00, //0x00000150 jmp          LBB0_29
	//0x00000155 LBB0_9
	0x48, 0x81, 0xfe, 0xff, 0xe0, 0xf5, 0x05, //0x00000155 cmpq         $99999999, %rsi
	0x0f, 0x87, 0x1e, 0x02, 0x00, 0x00, //0x0000015c ja           LBB0_17
	0x89, 0xf0, //0x00000162 movl         %esi, %eax
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x00000164 movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x00000169 imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x0000016d shrq         $45, %rdx
	0x44, 0x69, 0xc2, 0x10, 0x27, 0x00, 0x00, //0x00000171 imull        $10000, %edx, %r8d
	0x89, 0xf1, //0x00000178 movl         %esi, %ecx
	0x44, 0x29, 0xc1, //0x0000017a subl         %r8d, %ecx
	0x4c, 0x69, 0xd0, 0x83, 0xde, 0x1b, 0x43, //0x0000017d imulq        $1125899907, %rax, %r10
	0x49, 0xc1, 0xea, 0x31, //0x00000184 shrq         $49, %r10
	0x41, 0x83, 0xe2, 0xfe, //0x00000188 andl         $-2, %r10d
	0x0f, 0xb7, 0xc2, //0x0000018c movzwl       %dx, %eax
	0xc1, 0xe8, 0x02, //0x0000018f shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000192 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000198 shrl         $17, %eax
	0x6b, 0xc0, 0x64, //0x0000019b imull        $100, %eax, %eax
	0x29, 0xc2, //0x0000019e subl         %eax, %edx
	0x44, 0x0f, 0xb7, 0xca, //0x000001a0 movzwl       %dx, %r9d
	0x4d, 0x01, 0xc9, //0x000001a4 addq         %r9, %r9
	0x0f, 0xb7, 0xc1, //0x000001a7 movzwl       %cx, %eax
	0xc1, 0xe8, 0x02, //0x000001aa shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x000001ad imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x000001b3 shrl         $17, %eax
	0x4c, 0x8d, 0x04, 0x00, //0x000001b6 leaq         (%rax,%rax), %r8
	0x6b, 0xc0, 0x64, //0x000001ba imull        $100, %eax, %eax
	0x29, 0xc1, //0x000001bd subl         %eax, %ecx
	0x44, 0x0f, 0xb7, 0xd9, //0x000001bf movzwl       %cx, %r11d
	0x4d, 0x01, 0xdb, //0x000001c3 addq         %r11, %r11
	0x81, 0xfe, 0x80, 0x96, 0x98, 0x00, //0x000001c6 cmpl         $10000000, %esi
	0x0f, 0x82, 0x70, 0x00, 0x00, 0x00, //0x000001cc jb           LBB0_12
	0x48, 0x8d, 0x05, 0x47, 0x07, 0x00, 0x00, //0x000001d2 leaq         $1863(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x02, //0x000001d9 movb         (%r10,%rax), %al
	0x88, 0x07, //0x000001dd movb         %al, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000001df movl         $1, %ecx
	0xe9, 0x67, 0x00, 0x00, 0x00, //0x000001e4 jmp          LBB0_13
	//0x000001e9 LBB0_28
	0x31, 0xc9, //0x000001e9 xorl         %ecx, %ecx
	0x83, 0xfe, 0x64, //0x000001eb cmpl         $100, %esi
	0x0f, 0x82, 0xd4, 0x00, 0x00, 0x00, //0x000001ee jb           LBB0_30
	//0x000001f4 LBB0_29
	0x0f, 0xb7, 0xd2, //0x000001f4 movzwl       %dx, %edx
	0x48, 0x83, 0xca, 0x01, //0x000001f7 orq          $1, %rdx
	0x48, 0x8d, 0x35, 0x1e, 0x07, 0x00, 0x00, //0x000001fb leaq         $1822(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x32, //0x00000202 movb         (%rdx,%rsi), %dl
	0x89, 0xce, //0x00000205 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x00000207 addl         $1, %ecx
	0x88, 0x54, 0x37, 0x01, //0x0000020a movb         %dl, $1(%rdi,%rsi)
	//0x0000020e LBB0_31
	0x48, 0x8d, 0x15, 0x0b, 0x07, 0x00, 0x00, //0x0000020e leaq         $1803(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x10, //0x00000215 movb         (%rax,%rdx), %dl
	0x89, 0xce, //0x00000218 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x0000021a addl         $1, %ecx
	0x88, 0x54, 0x37, 0x01, //0x0000021d movb         %dl, $1(%rdi,%rsi)
	//0x00000221 LBB0_32
	0x0f, 0xb7, 0xc0, //0x00000221 movzwl       %ax, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000224 orq          $1, %rax
	0x48, 0x8d, 0x15, 0xf1, 0x06, 0x00, 0x00, //0x00000228 leaq         $1777(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x10, //0x0000022f movb         (%rax,%rdx), %al
	0x89, 0xca, //0x00000232 movl         %ecx, %edx
	0x83, 0xc1, 0x01, //0x00000234 addl         $1, %ecx
	0x88, 0x44, 0x17, 0x01, //0x00000237 movb         %al, $1(%rdi,%rdx)
	0x83, 0xc1, 0x01, //0x0000023b addl         $1, %ecx
	0x89, 0xc8, //0x0000023e movl         %ecx, %eax
	0x5d, //0x00000240 popq         %rbp
	0xc3, //0x00000241 retq         
	//0x00000242 LBB0_12
	0x31, 0xc9, //0x00000242 xorl         %ecx, %ecx
	0x81, 0xfe, 0x40, 0x42, 0x0f, 0x00, //0x00000244 cmpl         $1000000, %esi
	0x0f, 0x82, 0x88, 0x00, 0x00, 0x00, //0x0000024a jb           LBB0_14
	//0x00000250 LBB0_13
	0x44, 0x89, 0xd0, //0x00000250 movl         %r10d, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000253 orq          $1, %rax
	0x48, 0x8d, 0x35, 0xc2, 0x06, 0x00, 0x00, //0x00000257 leaq         $1730(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x0000025e movb         (%rax,%rsi), %al
	0x89, 0xce, //0x00000261 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x00000263 addl         $1, %ecx
	0x88, 0x04, 0x37, //0x00000266 movb         %al, (%rdi,%rsi)
	//0x00000269 LBB0_15
	0x48, 0x8d, 0x05, 0xb0, 0x06, 0x00, 0x00, //0x00000269 leaq         $1712(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x01, //0x00000270 movb         (%r9,%rax), %al
	0x89, 0xce, //0x00000274 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x00000276 addl         $1, %ecx
	0x88, 0x04, 0x37, //0x00000279 movb         %al, (%rdi,%rsi)
	//0x0000027c LBB0_16
	0x41, 0x0f, 0xb7, 0xc1, //0x0000027c movzwl       %r9w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x00000280 orq          $1, %rax
	0x48, 0x8d, 0x35, 0x95, 0x06, 0x00, 0x00, //0x00000284 leaq         $1685(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x0000028b movb         (%rax,%rsi), %al
	0x89, 0xca, //0x0000028e movl         %ecx, %edx
	0x88, 0x04, 0x17, //0x00000290 movb         %al, (%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x30, //0x00000293 movb         (%r8,%rsi), %al
	0x88, 0x44, 0x17, 0x01, //0x00000297 movb         %al, $1(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc0, //0x0000029b movzwl       %r8w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x0000029f orq          $1, %rax
	0x8a, 0x04, 0x30, //0x000002a3 movb         (%rax,%rsi), %al
	0x88, 0x44, 0x17, 0x02, //0x000002a6 movb         %al, $2(%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x33, //0x000002aa movb         (%r11,%rsi), %al
	0x88, 0x44, 0x17, 0x03, //0x000002ae movb         %al, $3(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc3, //0x000002b2 movzwl       %r11w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000002b6 orq          $1, %rax
	0x8a, 0x04, 0x30, //0x000002ba movb         (%rax,%rsi), %al
	0x83, 0xc1, 0x05, //0x000002bd addl         $5, %ecx
	0x88, 0x44, 0x17, 0x04, //0x000002c0 movb         %al, $4(%rdi,%rdx)
	0x89, 0xc8, //0x000002c4 movl         %ecx, %eax
	0x5d, //0x000002c6 popq         %rbp
	0xc3, //0x000002c7 retq         
	//0x000002c8 LBB0_30
	0x31, 0xc9, //0x000002c8 xorl         %ecx, %ecx
	0x83, 0xfe, 0x0a, //0x000002ca cmpl         $10, %esi
	0x0f, 0x83, 0x3b, 0xff, 0xff, 0xff, //0x000002cd jae          LBB0_31
	0xe9, 0x49, 0xff, 0xff, 0xff, //0x000002d3 jmp          LBB0_32
	//0x000002d8 LBB0_14
	0x31, 0xc9, //0x000002d8 xorl         %ecx, %ecx
	0x81, 0xfe, 0xa0, 0x86, 0x01, 0x00, //0x000002da cmpl         $100000, %esi
	0x0f, 0x83, 0x83, 0xff, 0xff, 0xff, //0x000002e0 jae          LBB0_15
	0xe9, 0x91, 0xff, 0xff, 0xff, //0x000002e6 jmp          LBB0_16
	//0x000002eb LBB0_33
	0x48, 0x81, 0xfe, 0xff, 0xe0, 0xf5, 0x05, //0x000002eb cmpq         $99999999, %rsi
	0x0f, 0x87, 0x3e, 0x02, 0x00, 0x00, //0x000002f2 ja           LBB0_41
	0x89, 0xf0, //0x000002f8 movl         %esi, %eax
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x000002fa movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x000002ff imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x00000303 shrq         $45, %rdx
	0x44, 0x69, 0xc2, 0x10, 0x27, 0x00, 0x00, //0x00000307 imull        $10000, %edx, %r8d
	0x89, 0xf1, //0x0000030e movl         %esi, %ecx
	0x44, 0x29, 0xc1, //0x00000310 subl         %r8d, %ecx
	0x4c, 0x69, 0xd0, 0x83, 0xde, 0x1b, 0x43, //0x00000313 imulq        $1125899907, %rax, %r10
	0x49, 0xc1, 0xea, 0x31, //0x0000031a shrq         $49, %r10
	0x41, 0x83, 0xe2, 0xfe, //0x0000031e andl         $-2, %r10d
	0x0f, 0xb7, 0xc2, //0x00000322 movzwl       %dx, %eax
	0xc1, 0xe8, 0x02, //0x00000325 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000328 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x0000032e shrl         $17, %eax
	0x6b, 0xc0, 0x64, //0x00000331 imull        $100, %eax, %eax
	0x29, 0xc2, //0x00000334 subl         %eax, %edx
	0x44, 0x0f, 0xb7, 0xca, //0x00000336 movzwl       %dx, %r9d
	0x4d, 0x01, 0xc9, //0x0000033a addq         %r9, %r9
	0x0f, 0xb7, 0xc1, //0x0000033d movzwl       %cx, %eax
	0xc1, 0xe8, 0x02, //0x00000340 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000343 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000349 shrl         $17, %eax
	0x4c, 0x8d, 0x04, 0x00, //0x0000034c leaq         (%rax,%rax), %r8
	0x6b, 0xc0, 0x64, //0x00000350 imull        $100, %eax, %eax
	0x29, 0xc1, //0x00000353 subl         %eax, %ecx
	0x44, 0x0f, 0xb7, 0xd9, //0x00000355 movzwl       %cx, %r11d
	0x4d, 0x01, 0xdb, //0x00000359 addq         %r11, %r11
	0x81, 0xfe, 0x80, 0x96, 0x98, 0x00, //0x0000035c cmpl         $10000000, %esi
	0x0f, 0x82, 0x2f, 0x01, 0x00, 0x00, //0x00000362 jb           LBB0_36
	0x48, 0x8d, 0x05, 0xb1, 0x05, 0x00, 0x00, //0x00000368 leaq         $1457(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x02, //0x0000036f movb         (%r10,%rax), %al
	0x88, 0x47, 0x01, //0x00000373 movb         %al, $1(%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000376 movl         $1, %ecx
	0xe9, 0x25, 0x01, 0x00, 0x00, //0x0000037b jmp          LBB0_37
	//0x00000380 LBB0_17
	0x48, 0xb9, 0x00, 0x00, 0xc1, 0x6f, 0xf2, 0x86, 0x23, 0x00, //0x00000380 movabsq      $10000000000000000, %rcx
	0x48, 0x39, 0xce, //0x0000038a cmpq         %rcx, %rsi
	0x0f, 0x83, 0xbe, 0x02, 0x00, 0x00, //0x0000038d jae          LBB0_19
	0x48, 0xb9, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00000393 movabsq      $-6067343680855748867, %rcx
	0x48, 0x89, 0xf0, //0x0000039d movq         %rsi, %rax
	0x48, 0xf7, 0xe1, //0x000003a0 mulq         %rcx
	0x48, 0xc1, 0xea, 0x1a, //0x000003a3 shrq         $26, %rdx
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x000003a7 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x000003ad subl         %eax, %esi
	0xc5, 0xf9, 0x6e, 0xc2, //0x000003af vmovd        %edx, %xmm0
	0xc5, 0xfa, 0x6f, 0x0d, 0x45, 0xfc, 0xff, 0xff, //0x000003b3 vmovdqu      $-955(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0xf4, 0xd1, //0x000003bb vpmuludq     %xmm1, %xmm0, %xmm2
	0xc5, 0xe9, 0x73, 0xd2, 0x2d, //0x000003bf vpsrlq       $45, %xmm2, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x000003c4 movl         $10000, %eax
	0xc4, 0xe1, 0xf9, 0x6e, 0xd8, //0x000003c9 vmovq        %rax, %xmm3
	0xc5, 0xe9, 0xf4, 0xe3, //0x000003ce vpmuludq     %xmm3, %xmm2, %xmm4
	0xc5, 0xf9, 0xfa, 0xc4, //0x000003d2 vpsubd       %xmm4, %xmm0, %xmm0
	0xc5, 0xe9, 0x61, 0xc0, //0x000003d6 vpunpcklwd   %xmm0, %xmm2, %xmm0
	0xc5, 0xf9, 0x73, 0xf0, 0x02, //0x000003da vpsllq       $2, %xmm0, %xmm0
	0xc5, 0xfb, 0x70, 0xc0, 0x50, //0x000003df vpshuflw     $80, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc0, 0x50, //0x000003e4 vpshufd      $80, %xmm0, %xmm0
	0xc4, 0xe2, 0x79, 0x59, 0x15, 0x3e, 0xfc, 0xff, 0xff, //0x000003e9 vpbroadcastq $-962(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc4, 0xe2, 0x79, 0x59, 0x25, 0x3d, 0xfc, 0xff, 0xff, //0x000003f2 vpbroadcastq $-963(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc2, //0x000003fb vpmulhuw     %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0xe4, 0xc4, //0x000003ff vpmulhuw     %xmm4, %xmm0, %xmm0
	0xc5, 0xfa, 0x6f, 0x2d, 0x05, 0xfc, 0xff, 0xff, //0x00000403 vmovdqu      $-1019(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0xd5, 0xf5, //0x0000040b vpmullw      %xmm5, %xmm0, %xmm6
	0xc5, 0xc9, 0x73, 0xf6, 0x10, //0x0000040f vpsllq       $16, %xmm6, %xmm6
	0xc5, 0xf9, 0xf9, 0xc6, //0x00000414 vpsubw       %xmm6, %xmm0, %xmm0
	0xc5, 0xf9, 0x6e, 0xf6, //0x00000418 vmovd        %esi, %xmm6
	0xc5, 0xc9, 0xf4, 0xc9, //0x0000041c vpmuludq     %xmm1, %xmm6, %xmm1
	0xc5, 0xf1, 0x73, 0xd1, 0x2d, //0x00000420 vpsrlq       $45, %xmm1, %xmm1
	0xc5, 0xf1, 0xf4, 0xdb, //0x00000425 vpmuludq     %xmm3, %xmm1, %xmm3
	0xc5, 0xc9, 0xfa, 0xdb, //0x00000429 vpsubd       %xmm3, %xmm6, %xmm3
	0xc5, 0xf1, 0x61, 0xcb, //0x0000042d vpunpcklwd   %xmm3, %xmm1, %xmm1
	0xc5, 0xf1, 0x73, 0xf1, 0x02, //0x00000431 vpsllq       $2, %xmm1, %xmm1
	0xc5, 0xfb, 0x70, 0xc9, 0x50, //0x00000436 vpshuflw     $80, %xmm1, %xmm1
	0xc5, 0xf9, 0x70, 0xc9, 0x50, //0x0000043b vpshufd      $80, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xca, //0x00000440 vpmulhuw     %xmm2, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xcc, //0x00000444 vpmulhuw     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xd5, 0xd5, //0x00000448 vpmullw      %xmm5, %xmm1, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x10, //0x0000044c vpsllq       $16, %xmm2, %xmm2
	0xc5, 0xf1, 0xf9, 0xca, //0x00000451 vpsubw       %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x67, 0xc1, //0x00000455 vpackuswb    %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xfc, 0x0d, 0xbf, 0xfb, 0xff, 0xff, //0x00000459 vpaddb       $-1089(%rip), %xmm0, %xmm1  /* LCPI0_4+0(%rip) */
	0xc5, 0xe9, 0xef, 0xd2, //0x00000461 vpxor        %xmm2, %xmm2, %xmm2
	0xc5, 0xf9, 0x74, 0xc2, //0x00000465 vpcmpeqb     %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x00000469 vpmovmskb    %xmm0, %eax
	0xf7, 0xd0, //0x0000046d notl         %eax
	0x0d, 0x00, 0x80, 0x00, 0x00, //0x0000046f orl          $32768, %eax
	0x0f, 0xbc, 0xc0, //0x00000474 bsfl         %eax, %eax
	0xb9, 0x10, 0x00, 0x00, 0x00, //0x00000477 movl         $16, %ecx
	0x29, 0xc1, //0x0000047c subl         %eax, %ecx
	0x48, 0xc1, 0xe0, 0x04, //0x0000047e shlq         $4, %rax
	0x48, 0x8d, 0x15, 0x67, 0x05, 0x00, 0x00, //0x00000482 leaq         $1383(%rip), %rdx  /* _VecShiftShuffles+0(%rip) */
	0xc4, 0xe2, 0x71, 0x00, 0x04, 0x10, //0x00000489 vpshufb      (%rax,%rdx), %xmm1, %xmm0
	0xc5, 0xfa, 0x7f, 0x07, //0x0000048f vmovdqu      %xmm0, (%rdi)
	0x89, 0xc8, //0x00000493 movl         %ecx, %eax
	0x5d, //0x00000495 popq         %rbp
	0xc3, //0x00000496 retq         
	//0x00000497 LBB0_36
	0x31, 0xc9, //0x00000497 xorl         %ecx, %ecx
	0x81, 0xfe, 0x40, 0x42, 0x0f, 0x00, //0x00000499 cmpl         $1000000, %esi
	0x0f, 0x82, 0x7e, 0x00, 0x00, 0x00, //0x0000049f jb           LBB0_38
	//0x000004a5 LBB0_37
	0x44, 0x89, 0xd0, //0x000004a5 movl         %r10d, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000004a8 orq          $1, %rax
	0x48, 0x8d, 0x35, 0x6d, 0x04, 0x00, 0x00, //0x000004ac leaq         $1133(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x000004b3 movb         (%rax,%rsi), %al
	0x89, 0xce, //0x000004b6 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x000004b8 addl         $1, %ecx
	0x88, 0x44, 0x37, 0x01, //0x000004bb movb         %al, $1(%rdi,%rsi)
	//0x000004bf LBB0_39
	0x48, 0x8d, 0x05, 0x5a, 0x04, 0x00, 0x00, //0x000004bf leaq         $1114(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x01, //0x000004c6 movb         (%r9,%rax), %al
	0x89, 0xce, //0x000004ca movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x000004cc addl         $1, %ecx
	0x88, 0x44, 0x37, 0x01, //0x000004cf movb         %al, $1(%rdi,%rsi)
	//0x000004d3 LBB0_40
	0x41, 0x0f, 0xb7, 0xc1, //0x000004d3 movzwl       %r9w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000004d7 orq          $1, %rax
	0x48, 0x8d, 0x35, 0x3e, 0x04, 0x00, 0x00, //0x000004db leaq         $1086(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x000004e2 movb         (%rax,%rsi), %al
	0x89, 0xca, //0x000004e5 movl         %ecx, %edx
	0x88, 0x44, 0x17, 0x01, //0x000004e7 movb         %al, $1(%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x30, //0x000004eb movb         (%r8,%rsi), %al
	0x88, 0x44, 0x17, 0x02, //0x000004ef movb         %al, $2(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc0, //0x000004f3 movzwl       %r8w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000004f7 orq          $1, %rax
	0x8a, 0x04, 0x30, //0x000004fb movb         (%rax,%rsi), %al
	0x88, 0x44, 0x17, 0x03, //0x000004fe movb         %al, $3(%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x33, //0x00000502 movb         (%r11,%rsi), %al
	0x88, 0x44, 0x17, 0x04, //0x00000506 movb         %al, $4(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc3, //0x0000050a movzwl       %r11w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x0000050e orq          $1, %rax
	0x8a, 0x04, 0x30, //0x00000512 movb         (%rax,%rsi), %al
	0x83, 0xc1, 0x05, //0x00000515 addl         $5, %ecx
	0x88, 0x44, 0x17, 0x05, //0x00000518 movb         %al, $5(%rdi,%rdx)
	0x83, 0xc1, 0x01, //0x0000051c addl         $1, %ecx
	0x89, 0xc8, //0x0000051f movl         %ecx, %eax
	0x5d, //0x00000521 popq         %rbp
	0xc3, //0x00000522 retq         
	//0x00000523 LBB0_38
	0x31, 0xc9, //0x00000523 xorl         %ecx, %ecx
	0x81, 0xfe, 0xa0, 0x86, 0x01, 0x00, //0x00000525 cmpl         $100000, %esi
	0x0f, 0x83, 0x8e, 0xff, 0xff, 0xff, //0x0000052b jae          LBB0_39
	0xe9, 0x9d, 0xff, 0xff, 0xff, //0x00000531 jmp          LBB0_40
	//0x00000536 LBB0_41
	0x48, 0xb9, 0x00, 0x00, 0xc1, 0x6f, 0xf2, 0x86, 0x23, 0x00, //0x00000536 movabsq      $10000000000000000, %rcx
	0x48, 0x39, 0xce, //0x00000540 cmpq         %rcx, %rsi
	0x0f, 0x83, 0x69, 0x02, 0x00, 0x00, //0x00000543 jae          LBB0_43
	0x48, 0xb9, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00000549 movabsq      $-6067343680855748867, %rcx
	0x48, 0x89, 0xf0, //0x00000553 movq         %rsi, %rax
	0x48, 0xf7, 0xe1, //0x00000556 mulq         %rcx
	0x48, 0xc1, 0xea, 0x1a, //0x00000559 shrq         $26, %rdx
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x0000055d imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x00000563 subl         %eax, %esi
	0xc5, 0xf9, 0x6e, 0xc2, //0x00000565 vmovd        %edx, %xmm0
	0xc5, 0xfa, 0x6f, 0x0d, 0x8f, 0xfa, 0xff, 0xff, //0x00000569 vmovdqu      $-1393(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0xf4, 0xd1, //0x00000571 vpmuludq     %xmm1, %xmm0, %xmm2
	0xc5, 0xe9, 0x73, 0xd2, 0x2d, //0x00000575 vpsrlq       $45, %xmm2, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x0000057a movl         $10000, %eax
	0xc4, 0xe1, 0xf9, 0x6e, 0xd8, //0x0000057f vmovq        %rax, %xmm3
	0xc5, 0xe9, 0xf4, 0xe3, //0x00000584 vpmuludq     %xmm3, %xmm2, %xmm4
	0xc5, 0xf9, 0xfa, 0xc4, //0x00000588 vpsubd       %xmm4, %xmm0, %xmm0
	0xc5, 0xe9, 0x61, 0xc0, //0x0000058c vpunpcklwd   %xmm0, %xmm2, %xmm0
	0xc5, 0xf9, 0x73, 0xf0, 0x02, //0x00000590 vpsllq       $2, %xmm0, %xmm0
	0xc5, 0xfb, 0x70, 0xc0, 0x50, //0x00000595 vpshuflw     $80, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc0, 0x50, //0x0000059a vpshufd      $80, %xmm0, %xmm0
	0xc4, 0xe2, 0x79, 0x59, 0x15, 0x88, 0xfa, 0xff, 0xff, //0x0000059f vpbroadcastq $-1400(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc4, 0xe2, 0x79, 0x59, 0x25, 0x87, 0xfa, 0xff, 0xff, //0x000005a8 vpbroadcastq $-1401(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc2, //0x000005b1 vpmulhuw     %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0xe4, 0xc4, //0x000005b5 vpmulhuw     %xmm4, %xmm0, %xmm0
	0xc5, 0xfa, 0x6f, 0x2d, 0x4f, 0xfa, 0xff, 0xff, //0x000005b9 vmovdqu      $-1457(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0xd5, 0xf5, //0x000005c1 vpmullw      %xmm5, %xmm0, %xmm6
	0xc5, 0xc9, 0x73, 0xf6, 0x10, //0x000005c5 vpsllq       $16, %xmm6, %xmm6
	0xc5, 0xf9, 0xf9, 0xc6, //0x000005ca vpsubw       %xmm6, %xmm0, %xmm0
	0xc5, 0xf9, 0x6e, 0xf6, //0x000005ce vmovd        %esi, %xmm6
	0xc5, 0xc9, 0xf4, 0xc9, //0x000005d2 vpmuludq     %xmm1, %xmm6, %xmm1
	0xc5, 0xf1, 0x73, 0xd1, 0x2d, //0x000005d6 vpsrlq       $45, %xmm1, %xmm1
	0xc5, 0xf1, 0xf4, 0xdb, //0x000005db vpmuludq     %xmm3, %xmm1, %xmm3
	0xc5, 0xc9, 0xfa, 0xdb, //0x000005df vpsubd       %xmm3, %xmm6, %xmm3
	0xc5, 0xf1, 0x61, 0xcb, //0x000005e3 vpunpcklwd   %xmm3, %xmm1, %xmm1
	0xc5, 0xf1, 0x73, 0xf1, 0x02, //0x000005e7 vpsllq       $2, %xmm1, %xmm1
	0xc5, 0xfb, 0x70, 0xc9, 0x50, //0x000005ec vpshuflw     $80, %xmm1, %xmm1
	0xc5, 0xf9, 0x70, 0xc9, 0x50, //0x000005f1 vpshufd      $80, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xca, //0x000005f6 vpmulhuw     %xmm2, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xcc, //0x000005fa vpmulhuw     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xd5, 0xd5, //0x000005fe vpmullw      %xmm5, %xmm1, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x10, //0x00000602 vpsllq       $16, %xmm2, %xmm2
	0xc5, 0xf1, 0xf9, 0xca, //0x00000607 vpsubw       %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x67, 0xc1, //0x0000060b vpackuswb    %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xfc, 0x0d, 0x09, 0xfa, 0xff, 0xff, //0x0000060f vpaddb       $-1527(%rip), %xmm0, %xmm1  /* LCPI0_4+0(%rip) */
	0xc5, 0xe9, 0xef, 0xd2, //0x00000617 vpxor        %xmm2, %xmm2, %xmm2
	0xc5, 0xf9, 0x74, 0xc2, //0x0000061b vpcmpeqb     %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x0000061f vpmovmskb    %xmm0, %eax
	0xf7, 0xd0, //0x00000623 notl         %eax
	0x0d, 0x00, 0x80, 0x00, 0x00, //0x00000625 orl          $32768, %eax
	0x0f, 0xbc, 0xc0, //0x0000062a bsfl         %eax, %eax
	0xb9, 0x10, 0x00, 0x00, 0x00, //0x0000062d movl         $16, %ecx
	0x29, 0xc1, //0x00000632 subl         %eax, %ecx
	0x48, 0xc1, 0xe0, 0x04, //0x00000634 shlq         $4, %rax
	0x48, 0x8d, 0x15, 0xb1, 0x03, 0x00, 0x00, //0x00000638 leaq         $945(%rip), %rdx  /* _VecShiftShuffles+0(%rip) */
	0xc4, 0xe2, 0x71, 0x00, 0x04, 0x10, //0x0000063f vpshufb      (%rax,%rdx), %xmm1, %xmm0
	0xc5, 0xfa, 0x7f, 0x47, 0x01, //0x00000645 vmovdqu      %xmm0, $1(%rdi)
	0x83, 0xc1, 0x01, //0x0000064a addl         $1, %ecx
	0x89, 0xc8, //0x0000064d movl         %ecx, %eax
	0x5d, //0x0000064f popq         %rbp
	0xc3, //0x00000650 retq         
	//0x00000651 LBB0_19
	0x48, 0xba, 0x57, 0x78, 0x13, 0xb1, 0x2f, 0x65, 0xa5, 0x39, //0x00000651 movabsq      $4153837486827862103, %rdx
	0x48, 0x89, 0xf0, //0x0000065b movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x0000065e mulq         %rdx
	0x48, 0xc1, 0xea, 0x33, //0x00000661 shrq         $51, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x00000665 imulq        %rdx, %rcx
	0x48, 0x29, 0xce, //0x00000669 subq         %rcx, %rsi
	0x83, 0xfa, 0x09, //0x0000066c cmpl         $9, %edx
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x0000066f ja           LBB0_21
	0x80, 0xc2, 0x30, //0x00000675 addb         $48, %dl
	0x88, 0x17, //0x00000678 movb         %dl, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000067a movl         $1, %ecx
	0xe9, 0x52, 0x00, 0x00, 0x00, //0x0000067f jmp          LBB0_24
	//0x00000684 LBB0_21
	0x83, 0xfa, 0x63, //0x00000684 cmpl         $99, %edx
	0x0f, 0x87, 0x1a, 0x00, 0x00, 0x00, //0x00000687 ja           LBB0_23
	0x89, 0xd0, //0x0000068d movl         %edx, %eax
	0x48, 0x8d, 0x0d, 0x8a, 0x02, 0x00, 0x00, //0x0000068f leaq         $650(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x00000696 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x07, //0x0000069a movw         %ax, (%rdi)
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x0000069d movl         $2, %ecx
	0xe9, 0x2f, 0x00, 0x00, 0x00, //0x000006a2 jmp          LBB0_24
	//0x000006a7 LBB0_23
	0x89, 0xd0, //0x000006a7 movl         %edx, %eax
	0xc1, 0xe8, 0x02, //0x000006a9 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x000006ac imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x000006b2 shrl         $17, %eax
	0x8d, 0x48, 0x30, //0x000006b5 leal         $48(%rax), %ecx
	0x88, 0x0f, //0x000006b8 movb         %cl, (%rdi)
	0x6b, 0xc0, 0x64, //0x000006ba imull        $100, %eax, %eax
	0x29, 0xc2, //0x000006bd subl         %eax, %edx
	0x0f, 0xb7, 0xc2, //0x000006bf movzwl       %dx, %eax
	0x48, 0x8d, 0x0d, 0x57, 0x02, 0x00, 0x00, //0x000006c2 leaq         $599(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x000006c9 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x47, 0x01, //0x000006cd movw         %ax, $1(%rdi)
	0xb9, 0x03, 0x00, 0x00, 0x00, //0x000006d1 movl         $3, %ecx
	//0x000006d6 LBB0_24
	0x48, 0xba, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x000006d6 movabsq      $-6067343680855748867, %rdx
	0x48, 0x89, 0xf0, //0x000006e0 movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x000006e3 mulq         %rdx
	0x48, 0xc1, 0xea, 0x1a, //0x000006e6 shrq         $26, %rdx
	0xc5, 0xf9, 0x6e, 0xc2, //0x000006ea vmovd        %edx, %xmm0
	0xc5, 0xfa, 0x6f, 0x0d, 0x0a, 0xf9, 0xff, 0xff, //0x000006ee vmovdqu      $-1782(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0xf4, 0xd1, //0x000006f6 vpmuludq     %xmm1, %xmm0, %xmm2
	0xc5, 0xe9, 0x73, 0xd2, 0x2d, //0x000006fa vpsrlq       $45, %xmm2, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x000006ff movl         $10000, %eax
	0xc4, 0xe1, 0xf9, 0x6e, 0xd8, //0x00000704 vmovq        %rax, %xmm3
	0xc5, 0xe9, 0xf4, 0xe3, //0x00000709 vpmuludq     %xmm3, %xmm2, %xmm4
	0xc5, 0xf9, 0xfa, 0xc4, //0x0000070d vpsubd       %xmm4, %xmm0, %xmm0
	0xc5, 0xe9, 0x61, 0xc0, //0x00000711 vpunpcklwd   %xmm0, %xmm2, %xmm0
	0xc5, 0xf9, 0x73, 0xf0, 0x02, //0x00000715 vpsllq       $2, %xmm0, %xmm0
	0xc5, 0xfb, 0x70, 0xc0, 0x50, //0x0000071a vpshuflw     $80, %xmm0, %xmm0
	0xc4, 0xe2, 0x79, 0x59, 0x15, 0x08, 0xf9, 0xff, 0xff, //0x0000071f vpbroadcastq $-1784(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc5, 0xf9, 0x70, 0xc0, 0x50, //0x00000728 vpshufd      $80, %xmm0, %xmm0
	0xc5, 0xf9, 0xe4, 0xc2, //0x0000072d vpmulhuw     %xmm2, %xmm0, %xmm0
	0xc4, 0xe2, 0x79, 0x59, 0x25, 0xfe, 0xf8, 0xff, 0xff, //0x00000731 vpbroadcastq $-1794(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc4, //0x0000073a vpmulhuw     %xmm4, %xmm0, %xmm0
	0xc5, 0xfa, 0x6f, 0x2d, 0xca, 0xf8, 0xff, 0xff, //0x0000073e vmovdqu      $-1846(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0xd5, 0xf5, //0x00000746 vpmullw      %xmm5, %xmm0, %xmm6
	0xc5, 0xc9, 0x73, 0xf6, 0x10, //0x0000074a vpsllq       $16, %xmm6, %xmm6
	0xc5, 0xf9, 0xf9, 0xc6, //0x0000074f vpsubw       %xmm6, %xmm0, %xmm0
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x00000753 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x00000759 subl         %eax, %esi
	0xc5, 0xf9, 0x6e, 0xf6, //0x0000075b vmovd        %esi, %xmm6
	0xc5, 0xc9, 0xf4, 0xc9, //0x0000075f vpmuludq     %xmm1, %xmm6, %xmm1
	0xc5, 0xf1, 0x73, 0xd1, 0x2d, //0x00000763 vpsrlq       $45, %xmm1, %xmm1
	0xc5, 0xf1, 0xf4, 0xdb, //0x00000768 vpmuludq     %xmm3, %xmm1, %xmm3
	0xc5, 0xc9, 0xfa, 0xdb, //0x0000076c vpsubd       %xmm3, %xmm6, %xmm3
	0xc5, 0xf1, 0x61, 0xcb, //0x00000770 vpunpcklwd   %xmm3, %xmm1, %xmm1
	0xc5, 0xf1, 0x73, 0xf1, 0x02, //0x00000774 vpsllq       $2, %xmm1, %xmm1
	0xc5, 0xfb, 0x70, 0xc9, 0x50, //0x00000779 vpshuflw     $80, %xmm1, %xmm1
	0xc5, 0xf9, 0x70, 0xc9, 0x50, //0x0000077e vpshufd      $80, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xca, //0x00000783 vpmulhuw     %xmm2, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xcc, //0x00000787 vpmulhuw     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xd5, 0xd5, //0x0000078b vpmullw      %xmm5, %xmm1, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x10, //0x0000078f vpsllq       $16, %xmm2, %xmm2
	0xc5, 0xf1, 0xf9, 0xca, //0x00000794 vpsubw       %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x67, 0xc1, //0x00000798 vpackuswb    %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xfc, 0x05, 0x7c, 0xf8, 0xff, 0xff, //0x0000079c vpaddb       $-1924(%rip), %xmm0, %xmm0  /* LCPI0_4+0(%rip) */
	0x89, 0xc8, //0x000007a4 movl         %ecx, %eax
	0xc5, 0xfa, 0x7f, 0x04, 0x07, //0x000007a6 vmovdqu      %xmm0, (%rdi,%rax)
	0x83, 0xc9, 0x10, //0x000007ab orl          $16, %ecx
	0x89, 0xc8, //0x000007ae movl         %ecx, %eax
	0x5d, //0x000007b0 popq         %rbp
	0xc3, //0x000007b1 retq         
	//0x000007b2 LBB0_43
	0x48, 0xba, 0x57, 0x78, 0x13, 0xb1, 0x2f, 0x65, 0xa5, 0x39, //0x000007b2 movabsq      $4153837486827862103, %rdx
	0x48, 0x89, 0xf0, //0x000007bc movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x000007bf mulq         %rdx
	0x48, 0xc1, 0xea, 0x33, //0x000007c2 shrq         $51, %rdx
	0x48, 0x0f, 0xaf, 0xca, //0x000007c6 imulq        %rdx, %rcx
	0x48, 0x29, 0xce, //0x000007ca subq         %rcx, %rsi
	0x83, 0xfa, 0x09, //0x000007cd cmpl         $9, %edx
	0x0f, 0x87, 0x10, 0x00, 0x00, 0x00, //0x000007d0 ja           LBB0_45
	0x80, 0xc2, 0x30, //0x000007d6 addb         $48, %dl
	0x88, 0x57, 0x01, //0x000007d9 movb         %dl, $1(%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x000007dc movl         $1, %ecx
	0xe9, 0x54, 0x00, 0x00, 0x00, //0x000007e1 jmp          LBB0_48
	//0x000007e6 LBB0_45
	0x83, 0xfa, 0x63, //0x000007e6 cmpl         $99, %edx
	0x0f, 0x87, 0x1b, 0x00, 0x00, 0x00, //0x000007e9 ja           LBB0_47
	0x89, 0xd0, //0x000007ef movl         %edx, %eax
	0x48, 0x8d, 0x0d, 0x28, 0x01, 0x00, 0x00, //0x000007f1 leaq         $296(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x000007f8 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x47, 0x01, //0x000007fc movw         %ax, $1(%rdi)
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00000800 movl         $2, %ecx
	0xe9, 0x30, 0x00, 0x00, 0x00, //0x00000805 jmp          LBB0_48
	//0x0000080a LBB0_47
	0x89, 0xd0, //0x0000080a movl         %edx, %eax
	0xc1, 0xe8, 0x02, //0x0000080c shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x0000080f imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000815 shrl         $17, %eax
	0x8d, 0x48, 0x30, //0x00000818 leal         $48(%rax), %ecx
	0x88, 0x4f, 0x01, //0x0000081b movb         %cl, $1(%rdi)
	0x6b, 0xc0, 0x64, //0x0000081e imull        $100, %eax, %eax
	0x29, 0xc2, //0x00000821 subl         %eax, %edx
	0x0f, 0xb7, 0xc2, //0x00000823 movzwl       %dx, %eax
	0x48, 0x8d, 0x0d, 0xf3, 0x00, 0x00, 0x00, //0x00000826 leaq         $243(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x0000082d movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x47, 0x02, //0x00000831 movw         %ax, $2(%rdi)
	0xb9, 0x03, 0x00, 0x00, 0x00, //0x00000835 movl         $3, %ecx
	//0x0000083a LBB0_48
	0x48, 0xba, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x0000083a movabsq      $-6067343680855748867, %rdx
	0x48, 0x89, 0xf0, //0x00000844 movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x00000847 mulq         %rdx
	0x48, 0xc1, 0xea, 0x1a, //0x0000084a shrq         $26, %rdx
	0xc5, 0xf9, 0x6e, 0xc2, //0x0000084e vmovd        %edx, %xmm0
	0xc5, 0xfa, 0x6f, 0x0d, 0xa6, 0xf7, 0xff, 0xff, //0x00000852 vmovdqu      $-2138(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0xf4, 0xd1, //0x0000085a vpmuludq     %xmm1, %xmm0, %xmm2
	0xc5, 0xe9, 0x73, 0xd2, 0x2d, //0x0000085e vpsrlq       $45, %xmm2, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x00000863 movl         $10000, %eax
	0xc4, 0xe1, 0xf9, 0x6e, 0xd8, //0x00000868 vmovq        %rax, %xmm3
	0xc5, 0xe9, 0xf4, 0xe3, //0x0000086d vpmuludq     %xmm3, %xmm2, %xmm4
	0xc5, 0xf9, 0xfa, 0xc4, //0x00000871 vpsubd       %xmm4, %xmm0, %xmm0
	0xc5, 0xe9, 0x61, 0xc0, //0x00000875 vpunpcklwd   %xmm0, %xmm2, %xmm0
	0xc5, 0xf9, 0x73, 0xf0, 0x02, //0x00000879 vpsllq       $2, %xmm0, %xmm0
	0xc5, 0xfb, 0x70, 0xc0, 0x50, //0x0000087e vpshuflw     $80, %xmm0, %xmm0
	0xc4, 0xe2, 0x79, 0x59, 0x15, 0xa4, 0xf7, 0xff, 0xff, //0x00000883 vpbroadcastq $-2140(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc5, 0xf9, 0x70, 0xc0, 0x50, //0x0000088c vpshufd      $80, %xmm0, %xmm0
	0xc5, 0xf9, 0xe4, 0xc2, //0x00000891 vpmulhuw     %xmm2, %xmm0, %xmm0
	0xc4, 0xe2, 0x79, 0x59, 0x25, 0x9a, 0xf7, 0xff, 0xff, //0x00000895 vpbroadcastq $-2150(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc4, //0x0000089e vpmulhuw     %xmm4, %xmm0, %xmm0
	0xc5, 0xfa, 0x6f, 0x2d, 0x66, 0xf7, 0xff, 0xff, //0x000008a2 vmovdqu      $-2202(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0xd5, 0xf5, //0x000008aa vpmullw      %xmm5, %xmm0, %xmm6
	0xc5, 0xc9, 0x73, 0xf6, 0x10, //0x000008ae vpsllq       $16, %xmm6, %xmm6
	0xc5, 0xf9, 0xf9, 0xc6, //0x000008b3 vpsubw       %xmm6, %xmm0, %xmm0
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x000008b7 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x000008bd subl         %eax, %esi
	0xc5, 0xf9, 0x6e, 0xf6, //0x000008bf vmovd        %esi, %xmm6
	0xc5, 0xc9, 0xf4, 0xc9, //0x000008c3 vpmuludq     %xmm1, %xmm6, %xmm1
	0xc5, 0xf1, 0x73, 0xd1, 0x2d, //0x000008c7 vpsrlq       $45, %xmm1, %xmm1
	0xc5, 0xf1, 0xf4, 0xdb, //0x000008cc vpmuludq     %xmm3, %xmm1, %xmm3
	0xc5, 0xc9, 0xfa, 0xdb, //0x000008d0 vpsubd       %xmm3, %xmm6, %xmm3
	0xc5, 0xf1, 0x61, 0xcb, //0x000008d4 vpunpcklwd   %xmm3, %xmm1, %xmm1
	0xc5, 0xf1, 0x73, 0xf1, 0x02, //0x000008d8 vpsllq       $2, %xmm1, %xmm1
	0xc5, 0xfb, 0x70, 0xc9, 0x50, //0x000008dd vpshuflw     $80, %xmm1, %xmm1
	0xc5, 0xf9, 0x70, 0xc9, 0x50, //0x000008e2 vpshufd      $80, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xca, //0x000008e7 vpmulhuw     %xmm2, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xcc, //0x000008eb vpmulhuw     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xd5, 0xd5, //0x000008ef vpmullw      %xmm5, %xmm1, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x10, //0x000008f3 vpsllq       $16, %xmm2, %xmm2
	0xc5, 0xf1, 0xf9, 0xca, //0x000008f8 vpsubw       %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x67, 0xc1, //0x000008fc vpackuswb    %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xfc, 0x05, 0x18, 0xf7, 0xff, 0xff, //0x00000900 vpaddb       $-2280(%rip), %xmm0, %xmm0  /* LCPI0_4+0(%rip) */
	0x89, 0xc8, //0x00000908 movl         %ecx, %eax
	0xc5, 0xfa, 0x7f, 0x44, 0x07, 0x01, //0x0000090a vmovdqu      %xmm0, $1(%rdi,%rax)
	0x83, 0xc9, 0x10, //0x00000910 orl          $16, %ecx
	0x83, 0xc1, 0x01, //0x00000913 addl         $1, %ecx
	0x89, 0xc8, //0x00000916 movl         %ecx, %eax
	0x5d, //0x00000918 popq         %rbp
	0xc3, //0x00000919 retq         
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000091a .p2align 4, 0x00
	//0x00000920 _Digits
	0x30, 0x30, 0x30, 0x31, 0x30, 0x32, 0x30, 0x33, 0x30, 0x34, 0x30, 0x35, 0x30, 0x36, 0x30, 0x37, //0x00000920 QUAD $0x3330323031303030; QUAD $0x3730363035303430  // .ascii 16, '0001020304050607'
	0x30, 0x38, 0x30, 0x39, 0x31, 0x30, 0x31, 0x31, 0x31, 0x32, 0x31, 0x33, 0x31, 0x34, 0x31, 0x35, //0x00000930 QUAD $0x3131303139303830; QUAD $0x3531343133313231  // .ascii 16, '0809101112131415'
	0x31, 0x36, 0x31, 0x37, 0x31, 0x38, 0x31, 0x39, 0x32, 0x30, 0x32, 0x31, 0x32, 0x32, 0x32, 0x33, //0x00000940 QUAD $0x3931383137313631; QUAD $0x3332323231323032  // .ascii 16, '1617181920212223'
	0x32, 0x34, 0x32, 0x35, 0x32, 0x36, 0x32, 0x37, 0x32, 0x38, 0x32, 0x39, 0x33, 0x30, 0x33, 0x31, //0x00000950 QUAD $0x3732363235323432; QUAD $0x3133303339323832  // .ascii 16, '2425262728293031'
	0x33, 0x32, 0x33, 0x33, 0x33, 0x34, 0x33, 0x35, 0x33, 0x36, 0x33, 0x37, 0x33, 0x38, 0x33, 0x39, //0x00000960 QUAD $0x3533343333333233; QUAD $0x3933383337333633  // .ascii 16, '3233343536373839'
	0x34, 0x30, 0x34, 0x31, 0x34, 0x32, 0x34, 0x33, 0x34, 0x34, 0x34, 0x35, 0x34, 0x36, 0x34, 0x37, //0x00000970 QUAD $0x3334323431343034; QUAD $0x3734363435343434  // .ascii 16, '4041424344454647'
	0x34, 0x38, 0x34, 0x39, 0x35, 0x30, 0x35, 0x31, 0x35, 0x32, 0x35, 0x33, 0x35, 0x34, 0x35, 0x35, //0x00000980 QUAD $0x3135303539343834; QUAD $0x3535343533353235  // .ascii 16, '4849505152535455'
	0x35, 0x36, 0x35, 0x37, 0x35, 0x38, 0x35, 0x39, 0x36, 0x30, 0x36, 0x31, 0x36, 0x32, 0x36, 0x33, //0x00000990 QUAD $0x3935383537353635; QUAD $0x3336323631363036  // .ascii 16, '5657585960616263'
	0x36, 0x34, 0x36, 0x35, 0x36, 0x36, 0x36, 0x37, 0x36, 0x38, 0x36, 0x39, 0x37, 0x30, 0x37, 0x31, //0x000009a0 QUAD $0x3736363635363436; QUAD $0x3137303739363836  // .ascii 16, '6465666768697071'
	0x37, 0x32, 0x37, 0x33, 0x37, 0x34, 0x37, 0x35, 0x37, 0x36, 0x37, 0x37, 0x37, 0x38, 0x37, 0x39, //0x000009b0 QUAD $0x3537343733373237; QUAD $0x3937383737373637  // .ascii 16, '7273747576777879'
	0x38, 0x30, 0x38, 0x31, 0x38, 0x32, 0x38, 0x33, 0x38, 0x34, 0x38, 0x35, 0x38, 0x36, 0x38, 0x37, //0x000009c0 QUAD $0x3338323831383038; QUAD $0x3738363835383438  // .ascii 16, '8081828384858687'
	0x38, 0x38, 0x38, 0x39, 0x39, 0x30, 0x39, 0x31, 0x39, 0x32, 0x39, 0x33, 0x39, 0x34, 0x39, 0x35, //0x000009d0 QUAD $0x3139303939383838; QUAD $0x3539343933393239  // .ascii 16, '8889909192939495'
	0x39, 0x36, 0x39, 0x37, 0x39, 0x38, 0x39, 0x39, //0x000009e0 QUAD $0x3939383937393639  // .ascii 8, '96979899'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000009e8 .p2align 4, 0x00
	//0x000009f0 _VecShiftShuffles
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, //0x000009f0 QUAD $0x0706050403020100; QUAD $0x0f0e0d0c0b0a0908  // .ascii 16, '\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f'
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, //0x00000a00 QUAD $0x0807060504030201; QUAD $0xff0f0e0d0c0b0a09  // .ascii 16, '\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff'
	0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, //0x00000a10 QUAD $0x0908070605040302; QUAD $0xffff0f0e0d0c0b0a  // .ascii 16, '\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff'
	0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, //0x00000a20 QUAD $0x0a09080706050403; QUAD $0xffffff0f0e0d0c0b  // .ascii 16, '\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff'
	0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, //0x00000a30 QUAD $0x0b0a090807060504; QUAD $0xffffffff0f0e0d0c  // .ascii 16, '\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff'
	0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000a40 QUAD $0x0c0b0a0908070605; QUAD $0xffffffffff0f0e0d  // .ascii 16, '\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff'
	0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000a50 QUAD $0x0d0c0b0a09080706; QUAD $0xffffffffffff0f0e  // .ascii 16, '\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff'
	0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000a60 QUAD $0x0e0d0c0b0a090807; QUAD $0xffffffffffffff0f  // .ascii 16, '\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff\xff'
	0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000a70 QUAD $0x0f0e0d0c0b0a0908; QUAD $0xffffffffffffffff  // .ascii 16, '\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff\xff\xff'
}
 
