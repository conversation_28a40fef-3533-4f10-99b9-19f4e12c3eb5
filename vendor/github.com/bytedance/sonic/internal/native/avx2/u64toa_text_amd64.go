// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_u64toa = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x59, 0x17, 0xb7, 0xd1, 0x00, 0x00, 0x00, 0x00, // .quad 3518437209
	0x59, 0x17, 0xb7, 0xd1, 0x00, 0x00, 0x00, 0x00, //0x00000008 .quad 3518437209
	//0x00000010 LCPI0_3
	0x0a, 0x00, //0x00000010 .word 10
	0x0a, 0x00, //0x00000012 .word 10
	0x0a, 0x00, //0x00000014 .word 10
	0x0a, 0x00, //0x00000016 .word 10
	0x0a, 0x00, //0x00000018 .word 10
	0x0a, 0x00, //0x0000001a .word 10
	0x0a, 0x00, //0x0000001c .word 10
	0x0a, 0x00, //0x0000001e .word 10
	//0x00000020 LCPI0_4
	0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, //0x00000020 QUAD $0x3030303030303030; QUAD $0x3030303030303030  // .space 16, '0000000000000000'
	//0x00000030 .p2align 3, 0x00
	//0x00000030 LCPI0_1
	0xc5, 0x20, 0x7b, 0x14, 0x34, 0x33, 0x00, 0x80, //0x00000030 .quad -9223315738079846203
	//0x00000038 LCPI0_2
	0x80, 0x00, 0x00, 0x08, 0x00, 0x20, 0x00, 0x80, //0x00000038 .quad -9223336852348469120
	//0x00000040 .p2align 4, 0x90
	//0x00000040 _u64toa
	0x55, //0x00000040 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000041 movq         %rsp, %rbp
	0x48, 0x81, 0xfe, 0x0f, 0x27, 0x00, 0x00, //0x00000044 cmpq         $9999, %rsi
	0x0f, 0x87, 0xa5, 0x00, 0x00, 0x00, //0x0000004b ja           LBB0_8
	0x0f, 0xb7, 0xc6, //0x00000051 movzwl       %si, %eax
	0xc1, 0xe8, 0x02, //0x00000054 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000057 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x0000005d shrl         $17, %eax
	0x48, 0x8d, 0x14, 0x00, //0x00000060 leaq         (%rax,%rax), %rdx
	0x6b, 0xc0, 0x64, //0x00000064 imull        $100, %eax, %eax
	0x89, 0xf1, //0x00000067 movl         %esi, %ecx
	0x29, 0xc1, //0x00000069 subl         %eax, %ecx
	0x0f, 0xb7, 0xc1, //0x0000006b movzwl       %cx, %eax
	0x48, 0x01, 0xc0, //0x0000006e addq         %rax, %rax
	0x81, 0xfe, 0xe8, 0x03, 0x00, 0x00, //0x00000071 cmpl         $1000, %esi
	0x0f, 0x82, 0x16, 0x00, 0x00, 0x00, //0x00000077 jb           LBB0_3
	0x48, 0x8d, 0x0d, 0x7c, 0x04, 0x00, 0x00, //0x0000007d leaq         $1148(%rip), %rcx  /* _Digits+0(%rip) */
	0x8a, 0x0c, 0x0a, //0x00000084 movb         (%rdx,%rcx), %cl
	0x88, 0x0f, //0x00000087 movb         %cl, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000089 movl         $1, %ecx
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x0000008e jmp          LBB0_4
	//0x00000093 LBB0_3
	0x31, 0xc9, //0x00000093 xorl         %ecx, %ecx
	0x83, 0xfe, 0x64, //0x00000095 cmpl         $100, %esi
	0x0f, 0x82, 0x48, 0x00, 0x00, 0x00, //0x00000098 jb           LBB0_5
	//0x0000009e LBB0_4
	0x0f, 0xb7, 0xd2, //0x0000009e movzwl       %dx, %edx
	0x48, 0x83, 0xca, 0x01, //0x000000a1 orq          $1, %rdx
	0x48, 0x8d, 0x35, 0x54, 0x04, 0x00, 0x00, //0x000000a5 leaq         $1108(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x32, //0x000000ac movb         (%rdx,%rsi), %dl
	0x89, 0xce, //0x000000af movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x000000b1 addl         $1, %ecx
	0x88, 0x14, 0x37, //0x000000b4 movb         %dl, (%rdi,%rsi)
	//0x000000b7 LBB0_6
	0x48, 0x8d, 0x15, 0x42, 0x04, 0x00, 0x00, //0x000000b7 leaq         $1090(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x14, 0x10, //0x000000be movb         (%rax,%rdx), %dl
	0x89, 0xce, //0x000000c1 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x000000c3 addl         $1, %ecx
	0x88, 0x14, 0x37, //0x000000c6 movb         %dl, (%rdi,%rsi)
	//0x000000c9 LBB0_7
	0x0f, 0xb7, 0xc0, //0x000000c9 movzwl       %ax, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000000cc orq          $1, %rax
	0x48, 0x8d, 0x15, 0x29, 0x04, 0x00, 0x00, //0x000000d0 leaq         $1065(%rip), %rdx  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x10, //0x000000d7 movb         (%rax,%rdx), %al
	0x89, 0xca, //0x000000da movl         %ecx, %edx
	0x83, 0xc1, 0x01, //0x000000dc addl         $1, %ecx
	0x88, 0x04, 0x17, //0x000000df movb         %al, (%rdi,%rdx)
	0x89, 0xc8, //0x000000e2 movl         %ecx, %eax
	0x5d, //0x000000e4 popq         %rbp
	0xc3, //0x000000e5 retq         
	//0x000000e6 LBB0_5
	0x31, 0xc9, //0x000000e6 xorl         %ecx, %ecx
	0x83, 0xfe, 0x0a, //0x000000e8 cmpl         $10, %esi
	0x0f, 0x83, 0xc6, 0xff, 0xff, 0xff, //0x000000eb jae          LBB0_6
	0xe9, 0xd3, 0xff, 0xff, 0xff, //0x000000f1 jmp          LBB0_7
	//0x000000f6 LBB0_8
	0x48, 0x81, 0xfe, 0xff, 0xe0, 0xf5, 0x05, //0x000000f6 cmpq         $99999999, %rsi
	0x0f, 0x87, 0x20, 0x01, 0x00, 0x00, //0x000000fd ja           LBB0_16
	0x89, 0xf0, //0x00000103 movl         %esi, %eax
	0xba, 0x59, 0x17, 0xb7, 0xd1, //0x00000105 movl         $3518437209, %edx
	0x48, 0x0f, 0xaf, 0xd0, //0x0000010a imulq        %rax, %rdx
	0x48, 0xc1, 0xea, 0x2d, //0x0000010e shrq         $45, %rdx
	0x44, 0x69, 0xc2, 0x10, 0x27, 0x00, 0x00, //0x00000112 imull        $10000, %edx, %r8d
	0x89, 0xf1, //0x00000119 movl         %esi, %ecx
	0x44, 0x29, 0xc1, //0x0000011b subl         %r8d, %ecx
	0x4c, 0x69, 0xd0, 0x83, 0xde, 0x1b, 0x43, //0x0000011e imulq        $1125899907, %rax, %r10
	0x49, 0xc1, 0xea, 0x31, //0x00000125 shrq         $49, %r10
	0x41, 0x83, 0xe2, 0xfe, //0x00000129 andl         $-2, %r10d
	0x0f, 0xb7, 0xc2, //0x0000012d movzwl       %dx, %eax
	0xc1, 0xe8, 0x02, //0x00000130 shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x00000133 imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000139 shrl         $17, %eax
	0x6b, 0xc0, 0x64, //0x0000013c imull        $100, %eax, %eax
	0x29, 0xc2, //0x0000013f subl         %eax, %edx
	0x44, 0x0f, 0xb7, 0xca, //0x00000141 movzwl       %dx, %r9d
	0x4d, 0x01, 0xc9, //0x00000145 addq         %r9, %r9
	0x0f, 0xb7, 0xc1, //0x00000148 movzwl       %cx, %eax
	0xc1, 0xe8, 0x02, //0x0000014b shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x0000014e imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x00000154 shrl         $17, %eax
	0x4c, 0x8d, 0x04, 0x00, //0x00000157 leaq         (%rax,%rax), %r8
	0x6b, 0xc0, 0x64, //0x0000015b imull        $100, %eax, %eax
	0x29, 0xc1, //0x0000015e subl         %eax, %ecx
	0x44, 0x0f, 0xb7, 0xd9, //0x00000160 movzwl       %cx, %r11d
	0x4d, 0x01, 0xdb, //0x00000164 addq         %r11, %r11
	0x81, 0xfe, 0x80, 0x96, 0x98, 0x00, //0x00000167 cmpl         $10000000, %esi
	0x0f, 0x82, 0x17, 0x00, 0x00, 0x00, //0x0000016d jb           LBB0_11
	0x48, 0x8d, 0x05, 0x86, 0x03, 0x00, 0x00, //0x00000173 leaq         $902(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x02, //0x0000017a movb         (%r10,%rax), %al
	0x88, 0x07, //0x0000017e movb         %al, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000180 movl         $1, %ecx
	0xe9, 0x0e, 0x00, 0x00, 0x00, //0x00000185 jmp          LBB0_12
	//0x0000018a LBB0_11
	0x31, 0xc9, //0x0000018a xorl         %ecx, %ecx
	0x81, 0xfe, 0x40, 0x42, 0x0f, 0x00, //0x0000018c cmpl         $1000000, %esi
	0x0f, 0x82, 0x78, 0x00, 0x00, 0x00, //0x00000192 jb           LBB0_13
	//0x00000198 LBB0_12
	0x44, 0x89, 0xd0, //0x00000198 movl         %r10d, %eax
	0x48, 0x83, 0xc8, 0x01, //0x0000019b orq          $1, %rax
	0x48, 0x8d, 0x35, 0x5a, 0x03, 0x00, 0x00, //0x0000019f leaq         $858(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x000001a6 movb         (%rax,%rsi), %al
	0x89, 0xce, //0x000001a9 movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x000001ab addl         $1, %ecx
	0x88, 0x04, 0x37, //0x000001ae movb         %al, (%rdi,%rsi)
	//0x000001b1 LBB0_14
	0x48, 0x8d, 0x05, 0x48, 0x03, 0x00, 0x00, //0x000001b1 leaq         $840(%rip), %rax  /* _Digits+0(%rip) */
	0x41, 0x8a, 0x04, 0x01, //0x000001b8 movb         (%r9,%rax), %al
	0x89, 0xce, //0x000001bc movl         %ecx, %esi
	0x83, 0xc1, 0x01, //0x000001be addl         $1, %ecx
	0x88, 0x04, 0x37, //0x000001c1 movb         %al, (%rdi,%rsi)
	//0x000001c4 LBB0_15
	0x41, 0x0f, 0xb7, 0xc1, //0x000001c4 movzwl       %r9w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000001c8 orq          $1, %rax
	0x48, 0x8d, 0x35, 0x2d, 0x03, 0x00, 0x00, //0x000001cc leaq         $813(%rip), %rsi  /* _Digits+0(%rip) */
	0x8a, 0x04, 0x30, //0x000001d3 movb         (%rax,%rsi), %al
	0x89, 0xca, //0x000001d6 movl         %ecx, %edx
	0x88, 0x04, 0x17, //0x000001d8 movb         %al, (%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x30, //0x000001db movb         (%r8,%rsi), %al
	0x88, 0x44, 0x17, 0x01, //0x000001df movb         %al, $1(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc0, //0x000001e3 movzwl       %r8w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000001e7 orq          $1, %rax
	0x8a, 0x04, 0x30, //0x000001eb movb         (%rax,%rsi), %al
	0x88, 0x44, 0x17, 0x02, //0x000001ee movb         %al, $2(%rdi,%rdx)
	0x41, 0x8a, 0x04, 0x33, //0x000001f2 movb         (%r11,%rsi), %al
	0x88, 0x44, 0x17, 0x03, //0x000001f6 movb         %al, $3(%rdi,%rdx)
	0x41, 0x0f, 0xb7, 0xc3, //0x000001fa movzwl       %r11w, %eax
	0x48, 0x83, 0xc8, 0x01, //0x000001fe orq          $1, %rax
	0x8a, 0x04, 0x30, //0x00000202 movb         (%rax,%rsi), %al
	0x83, 0xc1, 0x05, //0x00000205 addl         $5, %ecx
	0x88, 0x44, 0x17, 0x04, //0x00000208 movb         %al, $4(%rdi,%rdx)
	0x89, 0xc8, //0x0000020c movl         %ecx, %eax
	0x5d, //0x0000020e popq         %rbp
	0xc3, //0x0000020f retq         
	//0x00000210 LBB0_13
	0x31, 0xc9, //0x00000210 xorl         %ecx, %ecx
	0x81, 0xfe, 0xa0, 0x86, 0x01, 0x00, //0x00000212 cmpl         $100000, %esi
	0x0f, 0x83, 0x93, 0xff, 0xff, 0xff, //0x00000218 jae          LBB0_14
	0xe9, 0xa1, 0xff, 0xff, 0xff, //0x0000021e jmp          LBB0_15
	//0x00000223 LBB0_16
	0x48, 0xb8, 0xff, 0xff, 0xc0, 0x6f, 0xf2, 0x86, 0x23, 0x00, //0x00000223 movabsq      $9999999999999999, %rax
	0x48, 0x39, 0xc6, //0x0000022d cmpq         %rax, %rsi
	0x0f, 0x87, 0x04, 0x01, 0x00, 0x00, //0x00000230 ja           LBB0_18
	0x48, 0xb9, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00000236 movabsq      $-6067343680855748867, %rcx
	0x48, 0x89, 0xf0, //0x00000240 movq         %rsi, %rax
	0x48, 0xf7, 0xe1, //0x00000243 mulq         %rcx
	0x48, 0xc1, 0xea, 0x1a, //0x00000246 shrq         $26, %rdx
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x0000024a imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x00000250 subl         %eax, %esi
	0xc5, 0xf9, 0x6e, 0xc2, //0x00000252 vmovd        %edx, %xmm0
	0xc5, 0xfa, 0x6f, 0x0d, 0xa2, 0xfd, 0xff, 0xff, //0x00000256 vmovdqu      $-606(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0xf4, 0xd1, //0x0000025e vpmuludq     %xmm1, %xmm0, %xmm2
	0xc5, 0xe9, 0x73, 0xd2, 0x2d, //0x00000262 vpsrlq       $45, %xmm2, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x00000267 movl         $10000, %eax
	0xc4, 0xe1, 0xf9, 0x6e, 0xd8, //0x0000026c vmovq        %rax, %xmm3
	0xc5, 0xe9, 0xf4, 0xe3, //0x00000271 vpmuludq     %xmm3, %xmm2, %xmm4
	0xc5, 0xf9, 0xfa, 0xc4, //0x00000275 vpsubd       %xmm4, %xmm0, %xmm0
	0xc5, 0xe9, 0x61, 0xc0, //0x00000279 vpunpcklwd   %xmm0, %xmm2, %xmm0
	0xc5, 0xf9, 0x73, 0xf0, 0x02, //0x0000027d vpsllq       $2, %xmm0, %xmm0
	0xc5, 0xfb, 0x70, 0xc0, 0x50, //0x00000282 vpshuflw     $80, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc0, 0x50, //0x00000287 vpshufd      $80, %xmm0, %xmm0
	0xc4, 0xe2, 0x79, 0x59, 0x15, 0x9b, 0xfd, 0xff, 0xff, //0x0000028c vpbroadcastq $-613(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc4, 0xe2, 0x79, 0x59, 0x25, 0x9a, 0xfd, 0xff, 0xff, //0x00000295 vpbroadcastq $-614(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc2, //0x0000029e vpmulhuw     %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0xe4, 0xc4, //0x000002a2 vpmulhuw     %xmm4, %xmm0, %xmm0
	0xc5, 0xfa, 0x6f, 0x2d, 0x62, 0xfd, 0xff, 0xff, //0x000002a6 vmovdqu      $-670(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0xd5, 0xf5, //0x000002ae vpmullw      %xmm5, %xmm0, %xmm6
	0xc5, 0xc9, 0x73, 0xf6, 0x10, //0x000002b2 vpsllq       $16, %xmm6, %xmm6
	0xc5, 0xf9, 0xf9, 0xc6, //0x000002b7 vpsubw       %xmm6, %xmm0, %xmm0
	0xc5, 0xf9, 0x6e, 0xf6, //0x000002bb vmovd        %esi, %xmm6
	0xc5, 0xc9, 0xf4, 0xc9, //0x000002bf vpmuludq     %xmm1, %xmm6, %xmm1
	0xc5, 0xf1, 0x73, 0xd1, 0x2d, //0x000002c3 vpsrlq       $45, %xmm1, %xmm1
	0xc5, 0xf1, 0xf4, 0xdb, //0x000002c8 vpmuludq     %xmm3, %xmm1, %xmm3
	0xc5, 0xc9, 0xfa, 0xdb, //0x000002cc vpsubd       %xmm3, %xmm6, %xmm3
	0xc5, 0xf1, 0x61, 0xcb, //0x000002d0 vpunpcklwd   %xmm3, %xmm1, %xmm1
	0xc5, 0xf1, 0x73, 0xf1, 0x02, //0x000002d4 vpsllq       $2, %xmm1, %xmm1
	0xc5, 0xfb, 0x70, 0xc9, 0x50, //0x000002d9 vpshuflw     $80, %xmm1, %xmm1
	0xc5, 0xf9, 0x70, 0xc9, 0x50, //0x000002de vpshufd      $80, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xca, //0x000002e3 vpmulhuw     %xmm2, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xcc, //0x000002e7 vpmulhuw     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xd5, 0xd5, //0x000002eb vpmullw      %xmm5, %xmm1, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x10, //0x000002ef vpsllq       $16, %xmm2, %xmm2
	0xc5, 0xf1, 0xf9, 0xca, //0x000002f4 vpsubw       %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x67, 0xc1, //0x000002f8 vpackuswb    %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xfc, 0x0d, 0x1c, 0xfd, 0xff, 0xff, //0x000002fc vpaddb       $-740(%rip), %xmm0, %xmm1  /* LCPI0_4+0(%rip) */
	0xc5, 0xe9, 0xef, 0xd2, //0x00000304 vpxor        %xmm2, %xmm2, %xmm2
	0xc5, 0xf9, 0x74, 0xc2, //0x00000308 vpcmpeqb     %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0xd7, 0xc0, //0x0000030c vpmovmskb    %xmm0, %eax
	0xf7, 0xd0, //0x00000310 notl         %eax
	0x0d, 0x00, 0x80, 0x00, 0x00, //0x00000312 orl          $32768, %eax
	0x0f, 0xbc, 0xc0, //0x00000317 bsfl         %eax, %eax
	0xb9, 0x10, 0x00, 0x00, 0x00, //0x0000031a movl         $16, %ecx
	0x29, 0xc1, //0x0000031f subl         %eax, %ecx
	0x48, 0xc1, 0xe0, 0x04, //0x00000321 shlq         $4, %rax
	0x48, 0x8d, 0x15, 0xa4, 0x02, 0x00, 0x00, //0x00000325 leaq         $676(%rip), %rdx  /* _VecShiftShuffles+0(%rip) */
	0xc4, 0xe2, 0x71, 0x00, 0x04, 0x10, //0x0000032c vpshufb      (%rax,%rdx), %xmm1, %xmm0
	0xc5, 0xfa, 0x7f, 0x07, //0x00000332 vmovdqu      %xmm0, (%rdi)
	0x89, 0xc8, //0x00000336 movl         %ecx, %eax
	0x5d, //0x00000338 popq         %rbp
	0xc3, //0x00000339 retq         
	//0x0000033a LBB0_18
	0x48, 0xb9, 0x57, 0x78, 0x13, 0xb1, 0x2f, 0x65, 0xa5, 0x39, //0x0000033a movabsq      $4153837486827862103, %rcx
	0x48, 0x89, 0xf0, //0x00000344 movq         %rsi, %rax
	0x48, 0xf7, 0xe1, //0x00000347 mulq         %rcx
	0x48, 0xc1, 0xea, 0x33, //0x0000034a shrq         $51, %rdx
	0x48, 0xb8, 0x00, 0x00, 0xc1, 0x6f, 0xf2, 0x86, 0x23, 0x00, //0x0000034e movabsq      $10000000000000000, %rax
	0x48, 0x0f, 0xaf, 0xc2, //0x00000358 imulq        %rdx, %rax
	0x48, 0x29, 0xc6, //0x0000035c subq         %rax, %rsi
	0x83, 0xfa, 0x09, //0x0000035f cmpl         $9, %edx
	0x0f, 0x87, 0x0f, 0x00, 0x00, 0x00, //0x00000362 ja           LBB0_20
	0x80, 0xc2, 0x30, //0x00000368 addb         $48, %dl
	0x88, 0x17, //0x0000036b movb         %dl, (%rdi)
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x0000036d movl         $1, %ecx
	0xe9, 0xa5, 0x00, 0x00, 0x00, //0x00000372 jmp          LBB0_25
	//0x00000377 LBB0_20
	0x83, 0xfa, 0x63, //0x00000377 cmpl         $99, %edx
	0x0f, 0x87, 0x1a, 0x00, 0x00, 0x00, //0x0000037a ja           LBB0_22
	0x89, 0xd0, //0x00000380 movl         %edx, %eax
	0x48, 0x8d, 0x0d, 0x77, 0x01, 0x00, 0x00, //0x00000382 leaq         $375(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x00000389 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x07, //0x0000038d movw         %ax, (%rdi)
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00000390 movl         $2, %ecx
	0xe9, 0x82, 0x00, 0x00, 0x00, //0x00000395 jmp          LBB0_25
	//0x0000039a LBB0_22
	0x89, 0xd0, //0x0000039a movl         %edx, %eax
	0xc1, 0xe8, 0x02, //0x0000039c shrl         $2, %eax
	0x69, 0xc0, 0x7b, 0x14, 0x00, 0x00, //0x0000039f imull        $5243, %eax, %eax
	0xc1, 0xe8, 0x11, //0x000003a5 shrl         $17, %eax
	0x81, 0xfa, 0xe7, 0x03, 0x00, 0x00, //0x000003a8 cmpl         $999, %edx
	0x0f, 0x87, 0x37, 0x00, 0x00, 0x00, //0x000003ae ja           LBB0_24
	0x83, 0xc0, 0x30, //0x000003b4 addl         $48, %eax
	0x88, 0x07, //0x000003b7 movb         %al, (%rdi)
	0x0f, 0xb7, 0xc2, //0x000003b9 movzwl       %dx, %eax
	0x89, 0xc1, //0x000003bc movl         %eax, %ecx
	0xc1, 0xe9, 0x02, //0x000003be shrl         $2, %ecx
	0x69, 0xc9, 0x7b, 0x14, 0x00, 0x00, //0x000003c1 imull        $5243, %ecx, %ecx
	0xc1, 0xe9, 0x11, //0x000003c7 shrl         $17, %ecx
	0x6b, 0xc9, 0x64, //0x000003ca imull        $100, %ecx, %ecx
	0x29, 0xc8, //0x000003cd subl         %ecx, %eax
	0x0f, 0xb7, 0xc0, //0x000003cf movzwl       %ax, %eax
	0x48, 0x8d, 0x0d, 0x27, 0x01, 0x00, 0x00, //0x000003d2 leaq         $295(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x000003d9 movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x47, 0x01, //0x000003dd movw         %ax, $1(%rdi)
	0xb9, 0x03, 0x00, 0x00, 0x00, //0x000003e1 movl         $3, %ecx
	0xe9, 0x31, 0x00, 0x00, 0x00, //0x000003e6 jmp          LBB0_25
	//0x000003eb LBB0_24
	0x6b, 0xc8, 0x64, //0x000003eb imull        $100, %eax, %ecx
	0x29, 0xca, //0x000003ee subl         %ecx, %edx
	0x0f, 0xb7, 0xc0, //0x000003f0 movzwl       %ax, %eax
	0x48, 0x8d, 0x0d, 0x06, 0x01, 0x00, 0x00, //0x000003f3 leaq         $262(%rip), %rcx  /* _Digits+0(%rip) */
	0x0f, 0xb7, 0x04, 0x41, //0x000003fa movzwl       (%rcx,%rax,2), %eax
	0x66, 0x89, 0x07, //0x000003fe movw         %ax, (%rdi)
	0x0f, 0xb7, 0xc2, //0x00000401 movzwl       %dx, %eax
	0x8a, 0x14, 0x41, //0x00000404 movb         (%rcx,%rax,2), %dl
	0x48, 0x01, 0xc0, //0x00000407 addq         %rax, %rax
	0x88, 0x57, 0x02, //0x0000040a movb         %dl, $2(%rdi)
	0x0f, 0xb7, 0xc0, //0x0000040d movzwl       %ax, %eax
	0x8a, 0x44, 0x08, 0x01, //0x00000410 movb         $1(%rax,%rcx), %al
	0x88, 0x47, 0x03, //0x00000414 movb         %al, $3(%rdi)
	0xb9, 0x04, 0x00, 0x00, 0x00, //0x00000417 movl         $4, %ecx
	//0x0000041c LBB0_25
	0x48, 0xba, 0xfd, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x0000041c movabsq      $-6067343680855748867, %rdx
	0x48, 0x89, 0xf0, //0x00000426 movq         %rsi, %rax
	0x48, 0xf7, 0xe2, //0x00000429 mulq         %rdx
	0x48, 0xc1, 0xea, 0x1a, //0x0000042c shrq         $26, %rdx
	0xc5, 0xf9, 0x6e, 0xc2, //0x00000430 vmovd        %edx, %xmm0
	0xc5, 0xfa, 0x6f, 0x0d, 0xc4, 0xfb, 0xff, 0xff, //0x00000434 vmovdqu      $-1084(%rip), %xmm1  /* LCPI0_0+0(%rip) */
	0xc5, 0xf9, 0xf4, 0xd1, //0x0000043c vpmuludq     %xmm1, %xmm0, %xmm2
	0xc5, 0xe9, 0x73, 0xd2, 0x2d, //0x00000440 vpsrlq       $45, %xmm2, %xmm2
	0xb8, 0x10, 0x27, 0x00, 0x00, //0x00000445 movl         $10000, %eax
	0xc4, 0xe1, 0xf9, 0x6e, 0xd8, //0x0000044a vmovq        %rax, %xmm3
	0xc5, 0xe9, 0xf4, 0xe3, //0x0000044f vpmuludq     %xmm3, %xmm2, %xmm4
	0xc5, 0xf9, 0xfa, 0xc4, //0x00000453 vpsubd       %xmm4, %xmm0, %xmm0
	0xc5, 0xe9, 0x61, 0xc0, //0x00000457 vpunpcklwd   %xmm0, %xmm2, %xmm0
	0xc5, 0xf9, 0x73, 0xf0, 0x02, //0x0000045b vpsllq       $2, %xmm0, %xmm0
	0xc5, 0xfb, 0x70, 0xc0, 0x50, //0x00000460 vpshuflw     $80, %xmm0, %xmm0
	0xc4, 0xe2, 0x79, 0x59, 0x15, 0xc2, 0xfb, 0xff, 0xff, //0x00000465 vpbroadcastq $-1086(%rip), %xmm2  /* LCPI0_1+0(%rip) */
	0xc5, 0xf9, 0x70, 0xc0, 0x50, //0x0000046e vpshufd      $80, %xmm0, %xmm0
	0xc5, 0xf9, 0xe4, 0xc2, //0x00000473 vpmulhuw     %xmm2, %xmm0, %xmm0
	0xc4, 0xe2, 0x79, 0x59, 0x25, 0xb8, 0xfb, 0xff, 0xff, //0x00000477 vpbroadcastq $-1096(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0xc5, 0xf9, 0xe4, 0xc4, //0x00000480 vpmulhuw     %xmm4, %xmm0, %xmm0
	0xc5, 0xfa, 0x6f, 0x2d, 0x84, 0xfb, 0xff, 0xff, //0x00000484 vmovdqu      $-1148(%rip), %xmm5  /* LCPI0_3+0(%rip) */
	0xc5, 0xf9, 0xd5, 0xf5, //0x0000048c vpmullw      %xmm5, %xmm0, %xmm6
	0xc5, 0xc9, 0x73, 0xf6, 0x10, //0x00000490 vpsllq       $16, %xmm6, %xmm6
	0xc5, 0xf9, 0xf9, 0xc6, //0x00000495 vpsubw       %xmm6, %xmm0, %xmm0
	0x69, 0xc2, 0x00, 0xe1, 0xf5, 0x05, //0x00000499 imull        $100000000, %edx, %eax
	0x29, 0xc6, //0x0000049f subl         %eax, %esi
	0xc5, 0xf9, 0x6e, 0xf6, //0x000004a1 vmovd        %esi, %xmm6
	0xc5, 0xc9, 0xf4, 0xc9, //0x000004a5 vpmuludq     %xmm1, %xmm6, %xmm1
	0xc5, 0xf1, 0x73, 0xd1, 0x2d, //0x000004a9 vpsrlq       $45, %xmm1, %xmm1
	0xc5, 0xf1, 0xf4, 0xdb, //0x000004ae vpmuludq     %xmm3, %xmm1, %xmm3
	0xc5, 0xc9, 0xfa, 0xdb, //0x000004b2 vpsubd       %xmm3, %xmm6, %xmm3
	0xc5, 0xf1, 0x61, 0xcb, //0x000004b6 vpunpcklwd   %xmm3, %xmm1, %xmm1
	0xc5, 0xf1, 0x73, 0xf1, 0x02, //0x000004ba vpsllq       $2, %xmm1, %xmm1
	0xc5, 0xfb, 0x70, 0xc9, 0x50, //0x000004bf vpshuflw     $80, %xmm1, %xmm1
	0xc5, 0xf9, 0x70, 0xc9, 0x50, //0x000004c4 vpshufd      $80, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xca, //0x000004c9 vpmulhuw     %xmm2, %xmm1, %xmm1
	0xc5, 0xf1, 0xe4, 0xcc, //0x000004cd vpmulhuw     %xmm4, %xmm1, %xmm1
	0xc5, 0xf1, 0xd5, 0xd5, //0x000004d1 vpmullw      %xmm5, %xmm1, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x10, //0x000004d5 vpsllq       $16, %xmm2, %xmm2
	0xc5, 0xf1, 0xf9, 0xca, //0x000004da vpsubw       %xmm2, %xmm1, %xmm1
	0xc5, 0xf9, 0x67, 0xc1, //0x000004de vpackuswb    %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xfc, 0x05, 0x36, 0xfb, 0xff, 0xff, //0x000004e2 vpaddb       $-1226(%rip), %xmm0, %xmm0  /* LCPI0_4+0(%rip) */
	0x89, 0xc8, //0x000004ea movl         %ecx, %eax
	0xc5, 0xfa, 0x7f, 0x04, 0x07, //0x000004ec vmovdqu      %xmm0, (%rdi,%rax)
	0x83, 0xc9, 0x10, //0x000004f1 orl          $16, %ecx
	0x89, 0xc8, //0x000004f4 movl         %ecx, %eax
	0x5d, //0x000004f6 popq         %rbp
	0xc3, //0x000004f7 retq         
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000004f8 .p2align 4, 0x00
	//0x00000500 _Digits
	0x30, 0x30, 0x30, 0x31, 0x30, 0x32, 0x30, 0x33, 0x30, 0x34, 0x30, 0x35, 0x30, 0x36, 0x30, 0x37, //0x00000500 QUAD $0x3330323031303030; QUAD $0x3730363035303430  // .ascii 16, '0001020304050607'
	0x30, 0x38, 0x30, 0x39, 0x31, 0x30, 0x31, 0x31, 0x31, 0x32, 0x31, 0x33, 0x31, 0x34, 0x31, 0x35, //0x00000510 QUAD $0x3131303139303830; QUAD $0x3531343133313231  // .ascii 16, '0809101112131415'
	0x31, 0x36, 0x31, 0x37, 0x31, 0x38, 0x31, 0x39, 0x32, 0x30, 0x32, 0x31, 0x32, 0x32, 0x32, 0x33, //0x00000520 QUAD $0x3931383137313631; QUAD $0x3332323231323032  // .ascii 16, '1617181920212223'
	0x32, 0x34, 0x32, 0x35, 0x32, 0x36, 0x32, 0x37, 0x32, 0x38, 0x32, 0x39, 0x33, 0x30, 0x33, 0x31, //0x00000530 QUAD $0x3732363235323432; QUAD $0x3133303339323832  // .ascii 16, '2425262728293031'
	0x33, 0x32, 0x33, 0x33, 0x33, 0x34, 0x33, 0x35, 0x33, 0x36, 0x33, 0x37, 0x33, 0x38, 0x33, 0x39, //0x00000540 QUAD $0x3533343333333233; QUAD $0x3933383337333633  // .ascii 16, '3233343536373839'
	0x34, 0x30, 0x34, 0x31, 0x34, 0x32, 0x34, 0x33, 0x34, 0x34, 0x34, 0x35, 0x34, 0x36, 0x34, 0x37, //0x00000550 QUAD $0x3334323431343034; QUAD $0x3734363435343434  // .ascii 16, '4041424344454647'
	0x34, 0x38, 0x34, 0x39, 0x35, 0x30, 0x35, 0x31, 0x35, 0x32, 0x35, 0x33, 0x35, 0x34, 0x35, 0x35, //0x00000560 QUAD $0x3135303539343834; QUAD $0x3535343533353235  // .ascii 16, '4849505152535455'
	0x35, 0x36, 0x35, 0x37, 0x35, 0x38, 0x35, 0x39, 0x36, 0x30, 0x36, 0x31, 0x36, 0x32, 0x36, 0x33, //0x00000570 QUAD $0x3935383537353635; QUAD $0x3336323631363036  // .ascii 16, '5657585960616263'
	0x36, 0x34, 0x36, 0x35, 0x36, 0x36, 0x36, 0x37, 0x36, 0x38, 0x36, 0x39, 0x37, 0x30, 0x37, 0x31, //0x00000580 QUAD $0x3736363635363436; QUAD $0x3137303739363836  // .ascii 16, '6465666768697071'
	0x37, 0x32, 0x37, 0x33, 0x37, 0x34, 0x37, 0x35, 0x37, 0x36, 0x37, 0x37, 0x37, 0x38, 0x37, 0x39, //0x00000590 QUAD $0x3537343733373237; QUAD $0x3937383737373637  // .ascii 16, '7273747576777879'
	0x38, 0x30, 0x38, 0x31, 0x38, 0x32, 0x38, 0x33, 0x38, 0x34, 0x38, 0x35, 0x38, 0x36, 0x38, 0x37, //0x000005a0 QUAD $0x3338323831383038; QUAD $0x3738363835383438  // .ascii 16, '8081828384858687'
	0x38, 0x38, 0x38, 0x39, 0x39, 0x30, 0x39, 0x31, 0x39, 0x32, 0x39, 0x33, 0x39, 0x34, 0x39, 0x35, //0x000005b0 QUAD $0x3139303939383838; QUAD $0x3539343933393239  // .ascii 16, '8889909192939495'
	0x39, 0x36, 0x39, 0x37, 0x39, 0x38, 0x39, 0x39, //0x000005c0 QUAD $0x3939383937393639  // .ascii 8, '96979899'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000005c8 .p2align 4, 0x00
	//0x000005d0 _VecShiftShuffles
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, //0x000005d0 QUAD $0x0706050403020100; QUAD $0x0f0e0d0c0b0a0908  // .ascii 16, '\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f'
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, //0x000005e0 QUAD $0x0807060504030201; QUAD $0xff0f0e0d0c0b0a09  // .ascii 16, '\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff'
	0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, //0x000005f0 QUAD $0x0908070605040302; QUAD $0xffff0f0e0d0c0b0a  // .ascii 16, '\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff'
	0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, //0x00000600 QUAD $0x0a09080706050403; QUAD $0xffffff0f0e0d0c0b  // .ascii 16, '\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff'
	0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, //0x00000610 QUAD $0x0b0a090807060504; QUAD $0xffffffff0f0e0d0c  // .ascii 16, '\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff'
	0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000620 QUAD $0x0c0b0a0908070605; QUAD $0xffffffffff0f0e0d  // .ascii 16, '\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff'
	0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000630 QUAD $0x0d0c0b0a09080706; QUAD $0xffffffffffff0f0e  // .ascii 16, '\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff'
	0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000640 QUAD $0x0e0d0c0b0a090807; QUAD $0xffffffffffffff0f  // .ascii 16, '\x07\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff\xff'
	0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, //0x00000650 QUAD $0x0f0e0d0c0b0a0908; QUAD $0xffffffffffffffff  // .ascii 16, '\x08\t\n\x0b\x0c\r\x0e\x0f\xff\xff\xff\xff\xff\xff\xff\xff'
}
 
