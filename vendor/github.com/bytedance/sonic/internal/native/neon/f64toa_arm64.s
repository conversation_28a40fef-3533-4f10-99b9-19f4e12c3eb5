// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

#include "go_asm.h"
#include "funcdata.h"
#include "textflag.h"

TEXT ·__f64toa_entry__(SB), NOSPLIT, $16
	NO_LOCAL_POINTERS
	WORD $0x100000a0 // adr x0, .+20
	MOVD R0, ret(FP)
	RET
	  // .p2align 2, 0x00
_f64toa:
	WORD $0xd10083ff  // sub	sp, sp, #32
	WORD $0xa900fbfd  // stp	fp, lr, [sp, #8]
	WORD $0xa93ffbfd  // stp	fp, lr, [sp, #-8]
	WORD $0xd10023fd  // sub	fp, sp, #8
	WORD $0x9e660009  // fmov	x9, d0
	WORD $0xd374f92b  // ubfx	x11, x9, #52, #11
	WORD $0x711ffd7f  // cmp	w11, #2047
	WORD $0x54007c20  // b.eq	LBB0_171 $3972(%rip)
	WORD $0x528005a8  // mov	w8, #45
	WORD $0x39000008  // strb	w8, [x0]
	WORD $0xd37ffd2a  // lsr	x10, x9, #63
	WORD $0x8b0a0008  // add	x8, x0, x10
	WORD $0x9e66000c  // fmov	x12, d0
	WORD $0xf240f99f  // tst	x12, #0x7fffffffffffffff
	WORD $0x54000ec0  // b.eq	LBB0_10 $472(%rip)
	WORD $0x9240cd2e  // and	x14, x9, #0xfffffffffffff
	WORD $0xd29c7fed  // mov	x13, #58367
	WORD $0xf2aa816d  // movk	x13, #21515, lsl #16
	WORD $0xf2c0004d  // movk	x13, #2, lsl #32
	WORD $0x34007c0b  // cbz	w11, LBB0_174 $3968(%rip)
	WORD $0xb24c01c9  // orr	x9, x14, #0x10000000000000
	WORD $0x5110cd6c  // sub	w12, w11, #1075
	WORD $0x510ffd6f  // sub	w15, w11, #1023
	WORD $0x7100d1ff  // cmp	w15, #52
	WORD $0x540000e8  // b.hi	LBB0_5 $28(%rip)
	WORD $0x5280866f  // mov	w15, #1075
	WORD $0x4b0b01ef  // sub	w15, w15, w11
	WORD $0x92800010  // mov	x16, #-1
	WORD $0x9acf2210  // lsl	x16, x16, x15
	WORD $0xea30013f  // bics	xzr, x9, x16
	WORD $0x54000fe0  // b.eq	LBB0_18 $508(%rip)
LBB0_5:
	WORD $0xf10001df  // cmp	x14, #0
	WORD $0x1a9f17ee  // cset	w14, eq
	WORD $0x7100057f  // cmp	w11, #1
	WORD $0x1a9f97eb  // cset	w11, hi
	WORD $0xd37ef52f  // lsl	x15, x9, #2
	WORD $0x6a0b01cb  // ands	w11, w14, w11
	WORD $0xaa0b01eb  // orr	x11, x15, x11
	WORD $0x52800050  // mov	w16, #2
	WORD $0xb37ed130  // bfi	x16, x9, #2, #53
	WORD $0x5288826e  // mov	w14, #17427
	WORD $0x72a0026e  // movk	w14, #19, lsl #16
	WORD $0x52802031  // mov	w17, #257
	WORD $0x72bfff11  // movk	w17, #65528, lsl #16
	WORD $0x1a9f1231  // csel	w17, w17, wzr, ne
	WORD $0xd1000961  // sub	x1, x11, #2
	WORD $0x1b0e458b  // madd	w11, w12, w14, w17
	WORD $0x13167d6b  // asr	w11, w11, #22
	WORD $0x528d962e  // mov	w14, #27825
	WORD $0x72bffcae  // movk	w14, #65509, lsl #16
	WORD $0x1b0e7d6e  // mul	w14, w11, w14
	WORD $0x0b8e4d8c  // add	w12, w12, w14, asr #19
	WORD $0x1100058c  // add	w12, w12, #1
	WORD $0x5280248e  // mov	w14, #292
	WORD $0x4b0b01ce  // sub	w14, w14, w11
Lloh0:
	WORD $0x10008031  // adr	x17, _pow10_ceil_sig.g $4100(%rip)
Lloh1:
	WORD $0x91000231  // add	x17, x17, _pow10_ceil_sig.g@PAGEOFF $0(%rip)
	WORD $0x8b2e522e  // add	x14, x17, w14, uxtw #4
	WORD $0xa94009d1  // ldp	x17, x2, [x14]
	WORD $0x9acc202e  // lsl	x14, x1, x12
	WORD $0x9bd17dc1  // umulh	x1, x14, x17
	WORD $0x9b117dc3  // mul	x3, x14, x17
	WORD $0x9bc27dce  // umulh	x14, x14, x2
	WORD $0xab0301ce  // adds	x14, x14, x3
	WORD $0x9a813421  // cinc	x1, x1, hs
	WORD $0xf27ff9df  // tst	x14, #0xfffffffffffffffe
	WORD $0x1a9f07ee  // cset	w14, ne
	WORD $0xaa0101c1  // orr	x1, x14, x1
	WORD $0x9acc21ee  // lsl	x14, x15, x12
	WORD $0x9bd17dcf  // umulh	x15, x14, x17
	WORD $0x9b117dc3  // mul	x3, x14, x17
	WORD $0x9bc27dce  // umulh	x14, x14, x2
	WORD $0xab0301c3  // adds	x3, x14, x3
	WORD $0x9a8f35ee  // cinc	x14, x15, hs
	WORD $0xf27ff87f  // tst	x3, #0xfffffffffffffffe
	WORD $0x1a9f07ef  // cset	w15, ne
	WORD $0xaa0e01ef  // orr	x15, x15, x14
	WORD $0x9acc220c  // lsl	x12, x16, x12
	WORD $0x9bd17d90  // umulh	x16, x12, x17
	WORD $0x9b117d91  // mul	x17, x12, x17
	WORD $0x9bc27d8c  // umulh	x12, x12, x2
	WORD $0xab11018c  // adds	x12, x12, x17
	WORD $0x9a903610  // cinc	x16, x16, hs
	WORD $0xf27ff99f  // tst	x12, #0xfffffffffffffffe
	WORD $0x1a9f07ec  // cset	w12, ne
	WORD $0xaa10018c  // orr	x12, x12, x16
	WORD $0x92400129  // and	x9, x9, #0x1
	WORD $0x8b090030  // add	x16, x1, x9
	WORD $0xcb090189  // sub	x9, x12, x9
	WORD $0xf100a1ff  // cmp	x15, #40
	WORD $0x540001e3  // b.lo	LBB0_7 $60(%rip)
	WORD $0xb202e7ec  // mov	x12, #-3689348814741910324
	WORD $0xf29999ac  // movk	x12, #52429
	WORD $0x9bcc7dcc  // umulh	x12, x14, x12
	WORD $0xd345fd8c  // lsr	x12, x12, #5
	WORD $0x8b0c0991  // add	x17, x12, x12, lsl #2
	WORD $0xd37df231  // lsl	x17, x17, #3
	WORD $0x9100a221  // add	x1, x17, #40
	WORD $0xeb11021f  // cmp	x16, x17
	WORD $0x1a9f97f1  // cset	w17, hi
	WORD $0xeb09003f  // cmp	x1, x9
	WORD $0x1a9f87e1  // cset	w1, ls
	WORD $0x9a8c858c  // cinc	x12, x12, ls
	WORD $0x6b01023f  // cmp	w17, w1
	WORD $0x54000460  // b.eq	LBB0_11 $140(%rip)
LBB0_7:
	WORD $0xd342fdcc  // lsr	x12, x14, #2
	WORD $0x927ef5d1  // and	x17, x14, #0xfffffffffffffffc
	WORD $0x91001221  // add	x1, x17, #4
	WORD $0xeb09003f  // cmp	x1, x9
	WORD $0x1a9f87e2  // cset	w2, ls
	WORD $0xeb11021f  // cmp	x16, x17
	WORD $0x1a9f97f0  // cset	w16, hi
	WORD $0x4a020210  // eor	w16, w16, w2
	WORD $0xb27f0231  // orr	x17, x17, #0x2
	WORD $0xeb1101ff  // cmp	x15, x17
	WORD $0x1a9f17ef  // cset	w15, eq
	WORD $0x0a4e09ee  // and	w14, w15, w14, lsr #2
	WORD $0x1a9f95ce  // csinc	w14, w14, wzr, ls
	WORD $0x8b0e018e  // add	x14, x12, x14
	WORD $0xeb09003f  // cmp	x1, x9
	WORD $0x9a8c8589  // cinc	x9, x12, ls
	WORD $0x7200021f  // tst	w16, #0x1
	WORD $0x9a8911cc  // csel	x12, x14, x9, ne
	WORD $0xeb0d019f  // cmp	x12, x13
	WORD $0x54000249  // b.ls	LBB0_12 $72(%rip)
LBB0_8:
	WORD $0xd29d0009  // mov	x9, #59392
	WORD $0xf2a90ec9  // movk	x9, #18550, lsl #16
	WORD $0xf2c002e9  // movk	x9, #23, lsl #32
	WORD $0xeb09019f  // cmp	x12, x9
	WORD $0x54000222  // b.hs	LBB0_14 $68(%rip)
	WORD $0x5280016d  // mov	w13, #11
	WORD $0x140000f3  // b	LBB0_67 $972(%rip)
LBB0_10:
	WORD $0x52800609  // mov	w9, #48
	WORD $0x39000109  // strb	w9, [x8]
	WORD $0x4b000108  // sub	w8, w8, w0
	WORD $0x11000500  // add	w0, w8, #1
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_11:
	WORD $0x1100056b  // add	w11, w11, #1
	WORD $0xeb0d019f  // cmp	x12, x13
	WORD $0x54fffe08  // b.hi	LBB0_8 $-64(%rip)
LBB0_12:
	WORD $0xf100299f  // cmp	x12, #10
	WORD $0x54000142  // b.hs	LBB0_16 $40(%rip)
	WORD $0x5280002d  // mov	w13, #1
	WORD $0x140000e5  // b	LBB0_67 $916(%rip)
LBB0_14:
	WORD $0xd2820009  // mov	x9, #4096
	WORD $0xf2ba94a9  // movk	x9, #54437, lsl #16
	WORD $0xf2c01d09  // movk	x9, #232, lsl #32
	WORD $0xeb09019f  // cmp	x12, x9
	WORD $0x54000262  // b.hs	LBB0_21 $76(%rip)
	WORD $0x5280018d  // mov	w13, #12
	WORD $0x140000de  // b	LBB0_67 $888(%rip)
LBB0_16:
	WORD $0xf101919f  // cmp	x12, #100
	WORD $0x540002c2  // b.hs	LBB0_23 $88(%rip)
	WORD $0x5280004d  // mov	w13, #2
	WORD $0x140000da  // b	LBB0_67 $872(%rip)
LBB0_18:
	WORD $0x9acf252a  // lsr	x10, x9, x15
Lloh2:
	WORD $0x10006d09  // adr	x9, _Digits $3488(%rip)
Lloh3:
	WORD $0x91000129  // add	x9, x9, _Digits@PAGEOFF $0(%rip)
	WORD $0xeb0d015f  // cmp	x10, x13
	WORD $0x54000269  // b.ls	LBB0_25 $76(%rip)
	WORD $0xd29d000b  // mov	x11, #59392
	WORD $0xf2a90ecb  // movk	x11, #18550, lsl #16
	WORD $0xf2c002eb  // movk	x11, #23, lsl #32
	WORD $0xeb0b015f  // cmp	x10, x11
	WORD $0x540003e2  // b.hs	LBB0_31 $124(%rip)
	WORD $0x5280016b  // mov	w11, #11
	WORD $0x14000067  // b	LBB0_54 $412(%rip)
LBB0_21:
	WORD $0xd2940009  // mov	x9, #40960
	WORD $0xf2a9ce49  // movk	x9, #20082, lsl #16
	WORD $0xf2c12309  // movk	x9, #2328, lsl #32
	WORD $0xeb09019f  // cmp	x12, x9
	WORD $0x54000182  // b.hs	LBB0_27 $48(%rip)
	WORD $0x528001ad  // mov	w13, #13
	WORD $0x140000c7  // b	LBB0_67 $796(%rip)
LBB0_23:
	WORD $0xf10fa19f  // cmp	x12, #1000
	WORD $0x540001e2  // b.hs	LBB0_29 $60(%rip)
	WORD $0x5280006d  // mov	w13, #3
	WORD $0x140000c3  // b	LBB0_67 $780(%rip)
LBB0_25:
	WORD $0xf100295f  // cmp	x10, #10
	WORD $0x540002e2  // b.hs	LBB0_33 $92(%rip)
	WORD $0x5280002b  // mov	w11, #1
	WORD $0x8b0b010b  // add	x11, x8, x11
	WORD $0x14000346  // b	LBB0_176 $3352(%rip)
LBB0_27:
	WORD $0xd2880009  // mov	x9, #16384
	WORD $0xf2a20f49  // movk	x9, #4218, lsl #16
	WORD $0xf2cb5e69  // movk	x9, #23283, lsl #32
	WORD $0xeb09019f  // cmp	x12, x9
	WORD $0x54000282  // b.hs	LBB0_35 $80(%rip)
	WORD $0x528001cd  // mov	w13, #14
	WORD $0x140000b7  // b	LBB0_67 $732(%rip)
LBB0_29:
	WORD $0xd344fd89  // lsr	x9, x12, #4
	WORD $0xf109c53f  // cmp	x9, #625
	WORD $0x540002c2  // b.hs	LBB0_37 $88(%rip)
	WORD $0x5280008d  // mov	w13, #4
	WORD $0x140000b2  // b	LBB0_67 $712(%rip)
LBB0_31:
	WORD $0xd282000b  // mov	x11, #4096
	WORD $0xf2ba94ab  // movk	x11, #54437, lsl #16
	WORD $0xf2c01d0b  // movk	x11, #232, lsl #32
	WORD $0xeb0b015f  // cmp	x10, x11
	WORD $0x54000282  // b.hs	LBB0_39 $80(%rip)
	WORD $0x5280018b  // mov	w11, #12
	WORD $0x14000044  // b	LBB0_54 $272(%rip)
LBB0_33:
	WORD $0xf101915f  // cmp	x10, #100
	WORD $0x540002e2  // b.hs	LBB0_41 $92(%rip)
	WORD $0x5280004b  // mov	w11, #2
	WORD $0x8b0b010b  // add	x11, x8, x11
	WORD $0x1400032e  // b	LBB0_176 $3256(%rip)
LBB0_35:
	WORD $0xb2718be9  // mov	x9, #1125899906809856
	WORD $0xf2b498c9  // movk	x9, #42182, lsl #16
	WORD $0xf2d1afc9  // movk	x9, #36222, lsl #32
	WORD $0xeb09019f  // cmp	x12, x9
	WORD $0x54000282  // b.hs	LBB0_43 $80(%rip)
	WORD $0x528001ed  // mov	w13, #15
	WORD $0x1400009f  // b	LBB0_67 $636(%rip)
LBB0_37:
	WORD $0xd345fd89  // lsr	x9, x12, #5
	WORD $0xf130d53f  // cmp	x9, #3125
	WORD $0x540002c2  // b.hs	LBB0_45 $88(%rip)
	WORD $0x528000ad  // mov	w13, #5
	WORD $0x1400009a  // b	LBB0_67 $616(%rip)
LBB0_39:
	WORD $0xd294000b  // mov	x11, #40960
	WORD $0xf2a9ce4b  // movk	x11, #20082, lsl #16
	WORD $0xf2c1230b  // movk	x11, #2328, lsl #32
	WORD $0xeb0b015f  // cmp	x10, x11
	WORD $0x540002a2  // b.hs	LBB0_47 $84(%rip)
	WORD $0x528001ab  // mov	w11, #13
	WORD $0x1400002c  // b	LBB0_54 $176(%rip)
LBB0_41:
	WORD $0xf10fa15f  // cmp	x10, #1000
	WORD $0x54000302  // b.hs	LBB0_49 $96(%rip)
	WORD $0x5280006b  // mov	w11, #3
	WORD $0x8b0b010b  // add	x11, x8, x11
	WORD $0x14000316  // b	LBB0_176 $3160(%rip)
LBB0_43:
	WORD $0xd2adf829  // mov	x9, #1874919424
	WORD $0xf2d0de49  // movk	x9, #34546, lsl #32
	WORD $0xf2e00469  // movk	x9, #35, lsl #48
	WORD $0xeb09019f  // cmp	x12, x9
	WORD $0x52800209  // mov	w9, #16
LBB0_44:
	WORD $0x1a89352d  // cinc	w13, w9, hs
	WORD $0x14000087  // b	LBB0_67 $540(%rip)
LBB0_45:
	WORD $0x52884809  // mov	w9, #16960
	WORD $0x72a001e9  // movk	w9, #15, lsl #16
	WORD $0xeb09019f  // cmp	x12, x9
	WORD $0x54000202  // b.hs	LBB0_51 $64(%rip)
	WORD $0x528000cd  // mov	w13, #6
	WORD $0x14000081  // b	LBB0_67 $516(%rip)
LBB0_47:
	WORD $0xd288000b  // mov	x11, #16384
	WORD $0xf2a20f4b  // movk	x11, #4218, lsl #16
	WORD $0xf2cb5e6b  // movk	x11, #23283, lsl #32
	WORD $0xeb0b015f  // cmp	x10, x11
	WORD $0x540001e2  // b.hs	LBB0_53 $60(%rip)
	WORD $0x528001cb  // mov	w11, #14
	WORD $0x14000013  // b	LBB0_54 $76(%rip)
LBB0_49:
	WORD $0xd344fd4b  // lsr	x11, x10, #4
	WORD $0xf109c57f  // cmp	x11, #625
	WORD $0x54000d82  // b.hs	LBB0_63 $432(%rip)
	WORD $0x5280008b  // mov	w11, #4
	WORD $0x8b0b010b  // add	x11, x8, x11
	WORD $0x140002fc  // b	LBB0_176 $3056(%rip)
LBB0_51:
	WORD $0x5292d009  // mov	w9, #38528
	WORD $0x72a01309  // movk	w9, #152, lsl #16
	WORD $0xeb09019f  // cmp	x12, x9
	WORD $0x54000d62  // b.hs	LBB0_65 $428(%rip)
	WORD $0x528000ed  // mov	w13, #7
	WORD $0x1400006e  // b	LBB0_67 $440(%rip)
LBB0_53:
	WORD $0xb2718beb  // mov	x11, #1125899906809856
	WORD $0xf2b498cb  // movk	x11, #42182, lsl #16
	WORD $0xf2d1afcb  // movk	x11, #36222, lsl #32
	WORD $0xeb0b015f  // cmp	x10, x11
	WORD $0x528001eb  // mov	w11, #15
	WORD $0x9a8b356b  // cinc	x11, x11, hs
LBB0_54:
	WORD $0x8b0b010b  // add	x11, x8, x11
LBB0_55:
	WORD $0xd288c3ac  // mov	x12, #17949
	WORD $0xf2ae230c  // movk	x12, #28952, lsl #16
	WORD $0xf2d798ec  // movk	x12, #48327, lsl #32
	WORD $0xf2e0014c  // movk	x12, #10, lsl #48
	WORD $0x9bcc7d4c  // umulh	x12, x10, x12
	WORD $0xd34efd8d  // lsr	x13, x12, #14
	WORD $0x5283e00c  // mov	w12, #7936
	WORD $0x72bf414c  // movk	w12, #64010, lsl #16
	WORD $0x1b0c29aa  // madd	w10, w13, w12, w10
	WORD $0x5282eb2c  // mov	w12, #5977
	WORD $0x72ba36ec  // movk	w12, #53687, lsl #16
	WORD $0x9bac7d4c  // umull	x12, w10, w12
	WORD $0xd36dfd8c  // lsr	x12, x12, #45
	WORD $0x5284e20e  // mov	w14, #10000
	WORD $0x1b0ea98a  // msub	w10, w12, w14, w10
	WORD $0x5291b72f  // mov	w15, #36281
	WORD $0x72a000cf  // movk	w15, #6, lsl #16
	WORD $0x9baf7d8f  // umull	x15, w12, w15
	WORD $0xd360fdef  // lsr	x15, x15, #32
	WORD $0x1b0eb1ec  // msub	w12, w15, w14, w12
	WORD $0x53023d4e  // ubfx	w14, w10, #2, #14
	WORD $0x52828f6f  // mov	w15, #5243
	WORD $0x1b0f7dce  // mul	w14, w14, w15
	WORD $0x53117dce  // lsr	w14, w14, #17
	WORD $0x52800c90  // mov	w16, #100
	WORD $0x1b10a9ca  // msub	w10, w14, w16, w10
	WORD $0x92403d4a  // and	x10, x10, #0xffff
	WORD $0x1b0f7d8f  // mul	w15, w12, w15
	WORD $0x53137def  // lsr	w15, w15, #19
	WORD $0x1b10b1f0  // msub	w16, w15, w16, w12
	WORD $0x786a792a  // ldrh	w10, [x9, x10, lsl #1]
	WORD $0x786f592f  // ldrh	w15, [x9, w15, uxtw #1]
	WORD $0xaa0b03ec  // mov	x12, x11
	WORD $0x781f8d8f  // strh	w15, [x12, #-8]!
	WORD $0x79000d8a  // strh	w10, [x12, #6]
	WORD $0x786e592a  // ldrh	w10, [x9, w14, uxtw #1]
	WORD $0x92403e0e  // and	x14, x16, #0xffff
	WORD $0x7900098a  // strh	w10, [x12, #4]
	WORD $0x786e792a  // ldrh	w10, [x9, x14, lsl #1]
	WORD $0x7900058a  // strh	w10, [x12, #2]
	WORD $0xaa0d03ea  // mov	x10, x13
	WORD $0xd3447dad  // ubfx	x13, x13, #4, #28
	WORD $0x7109c5bf  // cmp	w13, #625
	WORD $0x540058e3  // b.lo	LBB0_177 $2844(%rip)
LBB0_56:
	WORD $0x529c1fee  // mov	w14, #57599
	WORD $0x72a0beae  // movk	w14, #1525, lsl #16
	WORD $0x5282eb2f  // mov	w15, #5977
	WORD $0x72ba36ef  // movk	w15, #53687, lsl #16
	WORD $0x1284e1f0  // mov	w16, #-10000
	WORD $0x5290a3f1  // mov	w17, #34079
	WORD $0x72aa3d71  // movk	w17, #20971, lsl #16
	WORD $0x52800c81  // mov	w1, #100
LBB0_57:
	WORD $0x9baf7d4d  // umull	x13, w10, w15
	WORD $0xd36dfdad  // lsr	x13, x13, #45
	WORD $0x1b1029a2  // madd	w2, w13, w16, w10
	WORD $0x9bb17c43  // umull	x3, w2, w17
	WORD $0xd365fc63  // lsr	x3, x3, #37
	WORD $0x1b018862  // msub	w2, w3, w1, w2
	WORD $0x78625922  // ldrh	w2, [x9, w2, uxtw #1]
	WORD $0x78637923  // ldrh	w3, [x9, x3, lsl #1]
	WORD $0x781fcd83  // strh	w3, [x12, #-4]!
	WORD $0x79000582  // strh	w2, [x12, #2]
	WORD $0x6b0e015f  // cmp	w10, w14
	WORD $0xaa0d03ea  // mov	x10, x13
	WORD $0x54fffe88  // b.hi	LBB0_57 $-48(%rip)
	WORD $0x710191bf  // cmp	w13, #100
	WORD $0x54000163  // b.lo	LBB0_60 $44(%rip)
LBB0_59:
	WORD $0x53023daa  // ubfx	w10, w13, #2, #14
	WORD $0x52828f6e  // mov	w14, #5243
	WORD $0x1b0e7d4a  // mul	w10, w10, w14
	WORD $0x53117d4a  // lsr	w10, w10, #17
	WORD $0x52800c8e  // mov	w14, #100
	WORD $0x1b0eb54d  // msub	w13, w10, w14, w13
	WORD $0x92403dad  // and	x13, x13, #0xffff
	WORD $0x786d792d  // ldrh	w13, [x9, x13, lsl #1]
	WORD $0x781fed8d  // strh	w13, [x12, #-2]!
	WORD $0xaa0a03ed  // mov	x13, x10
LBB0_60:
	WORD $0x710029bf  // cmp	w13, #10
	WORD $0x540000e3  // b.lo	LBB0_62 $28(%rip)
	WORD $0x786d5928  // ldrh	w8, [x9, w13, uxtw #1]
	WORD $0x781fe188  // sturh	w8, [x12, #-2]
	WORD $0x4b000160  // sub	w0, w11, w0
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_62:
	WORD $0x321c05a9  // orr	w9, w13, #0x30
	WORD $0x39000109  // strb	w9, [x8]
	WORD $0x4b000160  // sub	w0, w11, w0
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_63:
	WORD $0xd345fd4b  // lsr	x11, x10, #5
	WORD $0xf130d57f  // cmp	x11, #3125
	WORD $0x54004d02  // b.hs	LBB0_166 $2464(%rip)
	WORD $0x528000ab  // mov	w11, #5
	WORD $0x8b0b010b  // add	x11, x8, x11
	WORD $0x1400028e  // b	LBB0_176 $2616(%rip)
LBB0_65:
	WORD $0x529c2009  // mov	w9, #57600
	WORD $0x72a0bea9  // movk	w9, #1525, lsl #16
	WORD $0xeb09019f  // cmp	x12, x9
	WORD $0x54004d02  // b.hs	LBB0_168 $2464(%rip)
	WORD $0x5280010d  // mov	w13, #8
LBB0_67:
	WORD $0x0b0b01a9  // add	w9, w13, w11
	WORD $0x5100592e  // sub	w14, w9, #22
	WORD $0xd360fd8f  // lsr	x15, x12, #32
	WORD $0x310071df  // cmn	w14, #28
	WORD $0x54000168  // b.hi	LBB0_71 $44(%rip)
	WORD $0x9100050b  // add	x11, x8, #1
	WORD $0x2a0d03ed  // mov	w13, w13
	WORD $0x8b0d016e  // add	x14, x11, x13
	WORD $0xb500022f  // cbnz	x15, LBB0_75 $68(%rip)
	WORD $0xaa0c03f0  // mov	x16, x12
	WORD $0x53047e0c  // lsr	w12, w16, #4
	WORD $0x7109c59f  // cmp	w12, #625
	WORD $0x540017c2  // b.hs	LBB0_94 $760(%rip)
LBB0_70:
	WORD $0xaa1003ec  // mov	x12, x16
	WORD $0x140000d3  // b	LBB0_96 $844(%rip)
LBB0_71:
	WORD $0x37f8074b  // tbnz	w11, #31, LBB0_77 $232(%rip)
	WORD $0x2a0d03ee  // mov	w14, w13
	WORD $0x8b0e010b  // add	x11, x8, x14
	WORD $0xb500084f  // cbnz	x15, LBB0_81 $264(%rip)
	WORD $0xaa0b03ef  // mov	x15, x11
	WORD $0x53047d90  // lsr	w16, w12, #4
	WORD $0x7109c61f  // cmp	w16, #625
	WORD $0x54000d82  // b.hs	LBB0_82 $432(%rip)
LBB0_74:
	WORD $0xaa0c03f0  // mov	x16, x12
	WORD $0x14000081  // b	LBB0_84 $516(%rip)
LBB0_75:
	WORD $0xd29cefef  // mov	x15, #59263
	WORD $0xf2b8460f  // movk	x15, #49712, lsl #16
	WORD $0xf2c7710f  // movk	x15, #15240, lsl #32
	WORD $0xf2eabccf  // movk	x15, #21990, lsl #48
	WORD $0x9bcf7d8f  // umulh	x15, x12, x15
	WORD $0xd359fdf0  // lsr	x16, x15, #25
	WORD $0x5283e00f  // mov	w15, #7936
	WORD $0x72bf414f  // movk	w15, #64010, lsl #16
	WORD $0x1b0f320c  // madd	w12, w16, w15, w12
	WORD $0x3400146c  // cbz	w12, LBB0_93 $652(%rip)
	WORD $0xd280000f  // mov	x15, #0
	WORD $0x5282eb31  // mov	w17, #5977
	WORD $0x72ba36f1  // movk	w17, #53687, lsl #16
	WORD $0x9bb17d91  // umull	x17, w12, w17
	WORD $0xd36dfe31  // lsr	x17, x17, #45
	WORD $0x5284e201  // mov	w1, #10000
	WORD $0x1b01b22c  // msub	w12, w17, w1, w12
	WORD $0x5291b722  // mov	w2, #36281
	WORD $0x72a000c2  // movk	w2, #6, lsl #16
	WORD $0x9ba27e22  // umull	x2, w17, w2
	WORD $0xd360fc42  // lsr	x2, x2, #32
	WORD $0x1b01c451  // msub	w17, w2, w1, w17
	WORD $0x53023d81  // ubfx	w1, w12, #2, #14
	WORD $0x52828f62  // mov	w2, #5243
	WORD $0x1b027c21  // mul	w1, w1, w2
	WORD $0x53117c21  // lsr	w1, w1, #17
	WORD $0x52800c83  // mov	w3, #100
	WORD $0x1b03b02c  // msub	w12, w1, w3, w12
	WORD $0x92403d8c  // and	x12, x12, #0xffff
	WORD $0x1b027e22  // mul	w2, w17, w2
	WORD $0x53137c42  // lsr	w2, w2, #19
Lloh4:
	WORD $0x10004b04  // adr	x4, _Digits $2400(%rip)
Lloh5:
	WORD $0x91000084  // add	x4, x4, _Digits@PAGEOFF $0(%rip)
	WORD $0x786c788c  // ldrh	w12, [x4, x12, lsl #1]
	WORD $0x1b03c451  // msub	w17, w2, w3, w17
	WORD $0x781fe1cc  // sturh	w12, [x14, #-2]
	WORD $0x92403e2c  // and	x12, x17, #0xffff
	WORD $0x78615891  // ldrh	w17, [x4, w1, uxtw #1]
	WORD $0x781fc1d1  // sturh	w17, [x14, #-4]
	WORD $0x786c788c  // ldrh	w12, [x4, x12, lsl #1]
	WORD $0x781fa1cc  // sturh	w12, [x14, #-6]
	WORD $0x7862588c  // ldrh	w12, [x4, w2, uxtw #1]
	WORD $0x781f81cc  // sturh	w12, [x14, #-8]
	WORD $0xd10021ce  // sub	x14, x14, #8
	WORD $0x53047e0c  // lsr	w12, w16, #4
	WORD $0x7109c59f  // cmp	w12, #625
	WORD $0x54fff8c3  // b.lo	LBB0_70 $-232(%rip)
	WORD $0x14000082  // b	LBB0_94 $520(%rip)
LBB0_77:
	WORD $0x7100013f  // cmp	w9, #0
	WORD $0x540023cc  // b.gt	LBB0_123 $1144(%rip)
	WORD $0x5285c60e  // mov	w14, #11824
	WORD $0x7800250e  // strh	w14, [x8], #2
	WORD $0x36f82369  // tbz	w9, #31, LBB0_123 $1132(%rip)
	WORD $0x2a2d03ee  // mvn	w14, w13
	WORD $0x4b0b01ce  // sub	w14, w14, w11
	WORD $0x7100fddf  // cmp	w14, #63
	WORD $0x54002082  // b.hs	LBB0_118 $1040(%rip)
	WORD $0x5280000e  // mov	w14, #0
	WORD $0x1400010f  // b	LBB0_121 $1084(%rip)
LBB0_81:
	WORD $0xd29cefef  // mov	x15, #59263
	WORD $0xf2b8460f  // movk	x15, #49712, lsl #16
	WORD $0xf2c7710f  // movk	x15, #15240, lsl #32
	WORD $0xf2eabccf  // movk	x15, #21990, lsl #48
	WORD $0x9bcf7d8f  // umulh	x15, x12, x15
	WORD $0xd359fdf0  // lsr	x16, x15, #25
	WORD $0x5283e00f  // mov	w15, #7936
	WORD $0x72bf414f  // movk	w15, #64010, lsl #16
	WORD $0x1b0f320c  // madd	w12, w16, w15, w12
	WORD $0x5282eb2f  // mov	w15, #5977
	WORD $0x72ba36ef  // movk	w15, #53687, lsl #16
	WORD $0x9baf7d8f  // umull	x15, w12, w15
	WORD $0xd36dfdef  // lsr	x15, x15, #45
	WORD $0x5284e211  // mov	w17, #10000
	WORD $0x1b11b1ec  // msub	w12, w15, w17, w12
	WORD $0x5291b721  // mov	w1, #36281
	WORD $0x72a000c1  // movk	w1, #6, lsl #16
	WORD $0x9ba17de1  // umull	x1, w15, w1
	WORD $0xd360fc21  // lsr	x1, x1, #32
	WORD $0x1b11bc2f  // msub	w15, w1, w17, w15
	WORD $0x53023d91  // ubfx	w17, w12, #2, #14
	WORD $0x52828f61  // mov	w1, #5243
	WORD $0x1b017e31  // mul	w17, w17, w1
	WORD $0x53117e31  // lsr	w17, w17, #17
	WORD $0x52800c82  // mov	w2, #100
	WORD $0x1b02b22c  // msub	w12, w17, w2, w12
	WORD $0x92403d8c  // and	x12, x12, #0xffff
	WORD $0x1b017de1  // mul	w1, w15, w1
	WORD $0x53137c21  // lsr	w1, w1, #19
	WORD $0x1b02bc22  // msub	w2, w1, w2, w15
Lloh6:
	WORD $0x100043c3  // adr	x3, _Digits $2168(%rip)
Lloh7:
	WORD $0x91000063  // add	x3, x3, _Digits@PAGEOFF $0(%rip)
	WORD $0x786c786c  // ldrh	w12, [x3, x12, lsl #1]
	WORD $0x78615861  // ldrh	w1, [x3, w1, uxtw #1]
	WORD $0xaa0b03ef  // mov	x15, x11
	WORD $0x781f8de1  // strh	w1, [x15, #-8]!
	WORD $0x79000dec  // strh	w12, [x15, #6]
	WORD $0x7871586c  // ldrh	w12, [x3, w17, uxtw #1]
	WORD $0x92403c51  // and	x17, x2, #0xffff
	WORD $0x790009ec  // strh	w12, [x15, #4]
	WORD $0x7871786c  // ldrh	w12, [x3, x17, lsl #1]
	WORD $0x790005ec  // strh	w12, [x15, #2]
	WORD $0xaa1003ec  // mov	x12, x16
	WORD $0x53047d90  // lsr	w16, w12, #4
	WORD $0x7109c61f  // cmp	w16, #625
	WORD $0x54fff2c3  // b.lo	LBB0_74 $-424(%rip)
LBB0_82:
	WORD $0x529c1ff1  // mov	w17, #57599
	WORD $0x72a0beb1  // movk	w17, #1525, lsl #16
	WORD $0x5282eb21  // mov	w1, #5977
	WORD $0x72ba36e1  // movk	w1, #53687, lsl #16
	WORD $0x1284e1e2  // mov	w2, #-10000
	WORD $0x5290a3e3  // mov	w3, #34079
	WORD $0x72aa3d63  // movk	w3, #20971, lsl #16
	WORD $0x52800c84  // mov	w4, #100
Lloh8:
	WORD $0x100040c5  // adr	x5, _Digits $2072(%rip)
Lloh9:
	WORD $0x910000a5  // add	x5, x5, _Digits@PAGEOFF $0(%rip)
LBB0_83:
	WORD $0x9ba17d90  // umull	x16, w12, w1
	WORD $0xd36dfe10  // lsr	x16, x16, #45
	WORD $0x1b023206  // madd	w6, w16, w2, w12
	WORD $0x9ba37cc7  // umull	x7, w6, w3
	WORD $0xd365fce7  // lsr	x7, x7, #37
	WORD $0x1b0498e6  // msub	w6, w7, w4, w6
	WORD $0x786658a6  // ldrh	w6, [x5, w6, uxtw #1]
	WORD $0x786778a7  // ldrh	w7, [x5, x7, lsl #1]
	WORD $0x781fcde7  // strh	w7, [x15, #-4]!
	WORD $0x790005e6  // strh	w6, [x15, #2]
	WORD $0x6b11019f  // cmp	w12, w17
	WORD $0xaa1003ec  // mov	x12, x16
	WORD $0x54fffe88  // b.hi	LBB0_83 $-48(%rip)
LBB0_84:
	WORD $0x7101921f  // cmp	w16, #100
	WORD $0x540001a3  // b.lo	LBB0_86 $52(%rip)
	WORD $0x53023e0c  // ubfx	w12, w16, #2, #14
	WORD $0x52828f71  // mov	w17, #5243
	WORD $0x1b117d8c  // mul	w12, w12, w17
	WORD $0x53117d8c  // lsr	w12, w12, #17
	WORD $0x52800c91  // mov	w17, #100
	WORD $0x1b11c190  // msub	w16, w12, w17, w16
Lloh10:
	WORD $0x10003df1  // adr	x17, _Digits $1980(%rip)
Lloh11:
	WORD $0x91000231  // add	x17, x17, _Digits@PAGEOFF $0(%rip)
	WORD $0x92403e10  // and	x16, x16, #0xffff
	WORD $0x78707a30  // ldrh	w16, [x17, x16, lsl #1]
	WORD $0x781fedf0  // strh	w16, [x15, #-2]!
	WORD $0xaa0c03f0  // mov	x16, x12
LBB0_86:
	WORD $0x8b09010c  // add	x12, x8, x9
	WORD $0x71002a1f  // cmp	w16, #10
	WORD $0x54000163  // b.lo	LBB0_89 $44(%rip)
Lloh12:
	WORD $0x10003cc8  // adr	x8, _Digits $1944(%rip)
Lloh13:
	WORD $0x91000108  // add	x8, x8, _Digits@PAGEOFF $0(%rip)
	WORD $0x78705908  // ldrh	w8, [x8, w16, uxtw #1]
	WORD $0x781fe1e8  // sturh	w8, [x15, #-2]
	WORD $0x6b0901bf  // cmp	w13, w9
	WORD $0x54000123  // b.lo	LBB0_90 $36(%rip)
LBB0_88:
	WORD $0x4b000180  // sub	w0, w12, w0
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_89:
	WORD $0x321c060f  // orr	w15, w16, #0x30
	WORD $0x3900010f  // strb	w15, [x8]
	WORD $0x6b0901bf  // cmp	w13, w9
	WORD $0x54ffff22  // b.hs	LBB0_88 $-28(%rip)
LBB0_90:
	WORD $0x8b000148  // add	x8, x10, x0
	WORD $0x8b0e010d  // add	x13, x8, x14
	WORD $0x910005af  // add	x15, x13, #1
	WORD $0x8b090108  // add	x8, x8, x9
	WORD $0xeb0801ff  // cmp	x15, x8
	WORD $0x9a8d9508  // csinc	x8, x8, x13, ls
	WORD $0xcb0d0108  // sub	x8, x8, x13
	WORD $0xf100211f  // cmp	x8, #8
	WORD $0x54003343  // b.lo	LBB0_164 $1640(%rip)
	WORD $0xf101011f  // cmp	x8, #64
	WORD $0x54000f02  // b.hs	LBB0_111 $480(%rip)
	WORD $0xd2800009  // mov	x9, #0
	WORD $0x14000084  // b	LBB0_115 $528(%rip)
LBB0_93:
	WORD $0x928000ef  // mov	x15, #-8
	WORD $0xd10021ce  // sub	x14, x14, #8
	WORD $0x53047e0c  // lsr	w12, w16, #4
	WORD $0x7109c59f  // cmp	w12, #625
	WORD $0x54ffe883  // b.lo	LBB0_70 $-752(%rip)
LBB0_94:
	WORD $0x529c1ff1  // mov	w17, #57599
	WORD $0x72a0beb1  // movk	w17, #1525, lsl #16
	WORD $0x5282eb21  // mov	w1, #5977
	WORD $0x72ba36e1  // movk	w1, #53687, lsl #16
	WORD $0x1284e1e2  // mov	w2, #-10000
	WORD $0x5290a3e3  // mov	w3, #34079
	WORD $0x72aa3d63  // movk	w3, #20971, lsl #16
	WORD $0x52800c84  // mov	w4, #100
Lloh14:
	WORD $0x100037c5  // adr	x5, _Digits $1784(%rip)
Lloh15:
	WORD $0x910000a5  // add	x5, x5, _Digits@PAGEOFF $0(%rip)
LBB0_95:
	WORD $0x9ba17e0c  // umull	x12, w16, w1
	WORD $0xd36dfd8c  // lsr	x12, x12, #45
	WORD $0x1b024186  // madd	w6, w12, w2, w16
	WORD $0x9ba37cc7  // umull	x7, w6, w3
	WORD $0xd365fce7  // lsr	x7, x7, #37
	WORD $0x1b0498e6  // msub	w6, w7, w4, w6
	WORD $0x786658a6  // ldrh	w6, [x5, w6, uxtw #1]
	WORD $0x786778a7  // ldrh	w7, [x5, x7, lsl #1]
	WORD $0x781fcdc7  // strh	w7, [x14, #-4]!
	WORD $0x790005c6  // strh	w6, [x14, #2]
	WORD $0x6b11021f  // cmp	w16, w17
	WORD $0xaa0c03f0  // mov	x16, x12
	WORD $0x54fffe88  // b.hi	LBB0_95 $-48(%rip)
LBB0_96:
	WORD $0x7101919f  // cmp	w12, #100
	WORD $0x540001a3  // b.lo	LBB0_98 $52(%rip)
	WORD $0x53023d90  // ubfx	w16, w12, #2, #14
	WORD $0x52828f71  // mov	w17, #5243
	WORD $0x1b117e10  // mul	w16, w16, w17
	WORD $0x53117e10  // lsr	w16, w16, #17
	WORD $0x52800c91  // mov	w17, #100
	WORD $0x1b11b20c  // msub	w12, w16, w17, w12
Lloh16:
	WORD $0x100034f1  // adr	x17, _Digits $1692(%rip)
Lloh17:
	WORD $0x91000231  // add	x17, x17, _Digits@PAGEOFF $0(%rip)
	WORD $0x92403d8c  // and	x12, x12, #0xffff
	WORD $0x786c7a2c  // ldrh	w12, [x17, x12, lsl #1]
	WORD $0x781fedcc  // strh	w12, [x14, #-2]!
	WORD $0xaa1003ec  // mov	x12, x16
LBB0_98:
	WORD $0x7100299f  // cmp	w12, #10
	WORD $0x540000c3  // b.lo	LBB0_100 $24(%rip)
Lloh18:
	WORD $0x100033f0  // adr	x16, _Digits $1660(%rip)
Lloh19:
	WORD $0x91000210  // add	x16, x16, _Digits@PAGEOFF $0(%rip)
	WORD $0x786c5a0c  // ldrh	w12, [x16, w12, uxtw #1]
	WORD $0x781fe1cc  // sturh	w12, [x14, #-2]
	WORD $0x14000003  // b	LBB0_101 $12(%rip)
LBB0_100:
	WORD $0x321c058c  // orr	w12, w12, #0x30
	WORD $0x3900016c  // strb	w12, [x11]
LBB0_101:
	WORD $0x8b0a01ea  // add	x10, x15, x10
	WORD $0x8b0a000a  // add	x10, x0, x10
	WORD $0x910005ec  // add	x12, x15, #1
LBB0_102:
	WORD $0x386d694e  // ldrb	w14, [x10, x13]
	WORD $0xd100054a  // sub	x10, x10, #1
	WORD $0xd100058c  // sub	x12, x12, #1
	WORD $0x7100c1df  // cmp	w14, #48
	WORD $0x54ffff80  // b.eq	LBB0_102 $-16(%rip)
	WORD $0x3940050e  // ldrb	w14, [x8, #1]
	WORD $0x3900010e  // strb	w14, [x8]
	WORD $0x8b0c01ac  // add	x12, x13, x12
	WORD $0x8b0d0148  // add	x8, x10, x13
	WORD $0xf100099f  // cmp	x12, #2
	WORD $0x540000ab  // b.lt	LBB0_105 $20(%rip)
	WORD $0x91000908  // add	x8, x8, #2
	WORD $0x528005ca  // mov	w10, #46
	WORD $0x3900016a  // strb	w10, [x11]
	WORD $0x14000002  // b	LBB0_106 $8(%rip)
LBB0_105:
	WORD $0x91000508  // add	x8, x8, #1
LBB0_106:
	WORD $0x52800caa  // mov	w10, #101
	WORD $0x3900010a  // strb	w10, [x8]
	WORD $0x5280002a  // mov	w10, #1
	WORD $0x4b09014a  // sub	w10, w10, w9
	WORD $0x71000529  // subs	w9, w9, #1
	WORD $0x5280056b  // mov	w11, #43
	WORD $0x528005ac  // mov	w12, #45
	WORD $0x1a8bb18b  // csel	w11, w12, w11, lt
	WORD $0x1a89b149  // csel	w9, w10, w9, lt
	WORD $0x3900050b  // strb	w11, [x8, #1]
	WORD $0x7101913f  // cmp	w9, #100
	WORD $0x54000243  // b.lo	LBB0_108 $72(%rip)
	WORD $0x529999aa  // mov	w10, #52429
	WORD $0x72b9998a  // movk	w10, #52428, lsl #16
	WORD $0x9baa7d2a  // umull	x10, w9, w10
	WORD $0xd363fd4a  // lsr	x10, x10, #35
	WORD $0x5280014b  // mov	w11, #10
	WORD $0x1b0ba549  // msub	w9, w10, w11, w9
Lloh20:
	WORD $0x10002e6b  // adr	x11, _Digits $1484(%rip)
Lloh21:
	WORD $0x9100016b  // add	x11, x11, _Digits@PAGEOFF $0(%rip)
	WORD $0x786a796a  // ldrh	w10, [x11, x10, lsl #1]
	WORD $0x7900050a  // strh	w10, [x8, #2]
	WORD $0x321c0529  // orr	w9, w9, #0x30
	WORD $0x39001109  // strb	w9, [x8, #4]
	WORD $0x9100150c  // add	x12, x8, #5
	WORD $0x4b000180  // sub	w0, w12, w0
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_108:
	WORD $0x7100293f  // cmp	w9, #10
	WORD $0x54000143  // b.lo	LBB0_110 $40(%rip)
Lloh22:
	WORD $0x10002cca  // adr	x10, _Digits $1432(%rip)
Lloh23:
	WORD $0x9100014a  // add	x10, x10, _Digits@PAGEOFF $0(%rip)
	WORD $0x78695949  // ldrh	w9, [x10, w9, uxtw #1]
	WORD $0x79000509  // strh	w9, [x8, #2]
	WORD $0x9100110c  // add	x12, x8, #4
	WORD $0x4b000180  // sub	w0, w12, w0
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_110:
	WORD $0x321c0529  // orr	w9, w9, #0x30
	WORD $0x91000d0c  // add	x12, x8, #3
	WORD $0x39000909  // strb	w9, [x8, #2]
	WORD $0x4b000180  // sub	w0, w12, w0
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_111:
	WORD $0x927ae509  // and	x9, x8, #0xffffffffffffffc0
	WORD $0x8b0e014d  // add	x13, x10, x14
	WORD $0x8b0001ad  // add	x13, x13, x0
	WORD $0x910081ad  // add	x13, x13, #32
	WORD $0x4f01e600  // movi.16b	v0, #48
	WORD $0xaa0903ef  // mov	x15, x9
LBB0_112:
	WORD $0xad3f01a0  // stp	q0, q0, [x13, #-32]
	WORD $0xac8201a0  // stp	q0, q0, [x13], #64
	WORD $0xf10101ef  // subs	x15, x15, #64
	WORD $0x54ffffa1  // b.ne	LBB0_112 $-12(%rip)
	WORD $0xeb09011f  // cmp	x8, x9
	WORD $0x54ffed60  // b.eq	LBB0_88 $-596(%rip)
	WORD $0xf27d091f  // tst	x8, #0x38
	WORD $0x54002240  // b.eq	LBB0_163 $1096(%rip)
LBB0_115:
	WORD $0x927df10d  // and	x13, x8, #0xfffffffffffffff8
	WORD $0x8b0d016b  // add	x11, x11, x13
	WORD $0x8b0a012a  // add	x10, x9, x10
	WORD $0x8b0e014a  // add	x10, x10, x14
	WORD $0x8b0a000a  // add	x10, x0, x10
	WORD $0xcb0d0129  // sub	x9, x9, x13
	WORD $0x0f01e600  // movi.8b	v0, #48
LBB0_116:
	WORD $0xfc008540  // str	d0, [x10], #8
	WORD $0xb1002129  // adds	x9, x9, #8
	WORD $0x54ffffc1  // b.ne	LBB0_116 $-8(%rip)
	WORD $0xeb0d011f  // cmp	x8, x13
	WORD $0x54ffeba0  // b.eq	LBB0_88 $-652(%rip)
	WORD $0x14000106  // b	LBB0_164 $1048(%rip)
LBB0_118:
	WORD $0x910005d0  // add	x16, x14, #1
	WORD $0x927a6a0e  // and	x14, x16, #0x1ffffffc0
	WORD $0x8b0e0108  // add	x8, x8, x14
	WORD $0x8b00014a  // add	x10, x10, x0
	WORD $0x9100894a  // add	x10, x10, #34
	WORD $0x4f01e600  // movi.16b	v0, #48
	WORD $0xaa0e03f1  // mov	x17, x14
LBB0_119:
	WORD $0xad3f0140  // stp	q0, q0, [x10, #-32]
	WORD $0xac820140  // stp	q0, q0, [x10], #64
	WORD $0xf1010231  // subs	x17, x17, #64
	WORD $0x54ffffa1  // b.ne	LBB0_119 $-12(%rip)
	WORD $0xeb0e021f  // cmp	x16, x14
	WORD $0x540000e0  // b.eq	LBB0_123 $28(%rip)
LBB0_121:
	WORD $0x0b0901ca  // add	w10, w14, w9
	WORD $0x4b0a03ea  // neg	w10, w10
	WORD $0x5280060e  // mov	w14, #48
LBB0_122:
	WORD $0x3800150e  // strb	w14, [x8], #1
	WORD $0x7100054a  // subs	w10, w10, #1
	WORD $0x54ffffc1  // b.ne	LBB0_122 $-8(%rip)
LBB0_123:
	WORD $0x2a0d03ea  // mov	w10, w13
	WORD $0x8b0a010e  // add	x14, x8, x10
	WORD $0xb500010f  // cbnz	x15, LBB0_126 $32(%rip)
	WORD $0xaa0c03ed  // mov	x13, x12
	WORD $0xaa0e03ec  // mov	x12, x14
	WORD $0x53047db0  // lsr	w16, w13, #4
	WORD $0x7109c61f  // cmp	w16, #625
	WORD $0x54000702  // b.hs	LBB0_129 $224(%rip)
LBB0_125:
	WORD $0xaa0d03f0  // mov	x16, x13
	WORD $0x1400004d  // b	LBB0_131 $308(%rip)
LBB0_126:
	WORD $0xd29cefed  // mov	x13, #59263
	WORD $0xf2b8460d  // movk	x13, #49712, lsl #16
	WORD $0xf2c7710d  // movk	x13, #15240, lsl #32
	WORD $0xf2eabccd  // movk	x13, #21990, lsl #48
	WORD $0x9bcd7d8d  // umulh	x13, x12, x13
	WORD $0xd359fdad  // lsr	x13, x13, #25
	WORD $0x5283e00f  // mov	w15, #7936
	WORD $0x72bf414f  // movk	w15, #64010, lsl #16
	WORD $0x1b0f31ac  // madd	w12, w13, w15, w12
	WORD $0x340004ec  // cbz	w12, LBB0_128 $156(%rip)
	WORD $0xd280000f  // mov	x15, #0
	WORD $0x5282eb30  // mov	w16, #5977
	WORD $0x72ba36f0  // movk	w16, #53687, lsl #16
	WORD $0x9bb07d90  // umull	x16, w12, w16
	WORD $0xd36dfe10  // lsr	x16, x16, #45
	WORD $0x5284e211  // mov	w17, #10000
	WORD $0x1b11b20c  // msub	w12, w16, w17, w12
	WORD $0x5291b721  // mov	w1, #36281
	WORD $0x72a000c1  // movk	w1, #6, lsl #16
	WORD $0x9ba17e01  // umull	x1, w16, w1
	WORD $0xd360fc21  // lsr	x1, x1, #32
	WORD $0x1b11c030  // msub	w16, w1, w17, w16
	WORD $0x53023d91  // ubfx	w17, w12, #2, #14
	WORD $0x52828f61  // mov	w1, #5243
	WORD $0x1b017e31  // mul	w17, w17, w1
	WORD $0x53117e31  // lsr	w17, w17, #17
	WORD $0x52800c82  // mov	w2, #100
	WORD $0x1b02b22c  // msub	w12, w17, w2, w12
	WORD $0x92403d8c  // and	x12, x12, #0xffff
	WORD $0x1b017e01  // mul	w1, w16, w1
	WORD $0x53137c21  // lsr	w1, w1, #19
Lloh24:
	WORD $0x10001fe3  // adr	x3, _Digits $1020(%rip)
Lloh25:
	WORD $0x91000063  // add	x3, x3, _Digits@PAGEOFF $0(%rip)
	WORD $0x786c786c  // ldrh	w12, [x3, x12, lsl #1]
	WORD $0x1b02c030  // msub	w16, w1, w2, w16
	WORD $0x781fe1cc  // sturh	w12, [x14, #-2]
	WORD $0x92403e0c  // and	x12, x16, #0xffff
	WORD $0x78715870  // ldrh	w16, [x3, w17, uxtw #1]
	WORD $0x781fc1d0  // sturh	w16, [x14, #-4]
	WORD $0x786c786c  // ldrh	w12, [x3, x12, lsl #1]
	WORD $0x781fa1cc  // sturh	w12, [x14, #-6]
	WORD $0x7861586c  // ldrh	w12, [x3, w1, uxtw #1]
	WORD $0x781f81cc  // sturh	w12, [x14, #-8]
	WORD $0xd10021cc  // sub	x12, x14, #8
	WORD $0x53047db0  // lsr	w16, w13, #4
	WORD $0x7109c61f  // cmp	w16, #625
	WORD $0x54fffa03  // b.lo	LBB0_125 $-192(%rip)
	WORD $0x14000006  // b	LBB0_129 $24(%rip)
LBB0_128:
	WORD $0x928000ef  // mov	x15, #-8
	WORD $0xd10021cc  // sub	x12, x14, #8
	WORD $0x53047db0  // lsr	w16, w13, #4
	WORD $0x7109c61f  // cmp	w16, #625
	WORD $0x54fff943  // b.lo	LBB0_125 $-216(%rip)
LBB0_129:
	WORD $0x529c1ff1  // mov	w17, #57599
	WORD $0x72a0beb1  // movk	w17, #1525, lsl #16
	WORD $0x5282eb21  // mov	w1, #5977
	WORD $0x72ba36e1  // movk	w1, #53687, lsl #16
	WORD $0x1284e1e2  // mov	w2, #-10000
	WORD $0x5290a3e3  // mov	w3, #34079
	WORD $0x72aa3d63  // movk	w3, #20971, lsl #16
	WORD $0x52800c84  // mov	w4, #100
Lloh26:
	WORD $0x10001c25  // adr	x5, _Digits $900(%rip)
Lloh27:
	WORD $0x910000a5  // add	x5, x5, _Digits@PAGEOFF $0(%rip)
LBB0_130:
	WORD $0x9ba17db0  // umull	x16, w13, w1
	WORD $0xd36dfe10  // lsr	x16, x16, #45
	WORD $0x1b023606  // madd	w6, w16, w2, w13
	WORD $0x9ba37cc7  // umull	x7, w6, w3
	WORD $0xd365fce7  // lsr	x7, x7, #37
	WORD $0x1b0498e6  // msub	w6, w7, w4, w6
	WORD $0x786658a6  // ldrh	w6, [x5, w6, uxtw #1]
	WORD $0x786778a7  // ldrh	w7, [x5, x7, lsl #1]
	WORD $0x781fcd87  // strh	w7, [x12, #-4]!
	WORD $0x79000586  // strh	w6, [x12, #2]
	WORD $0x6b1101bf  // cmp	w13, w17
	WORD $0xaa1003ed  // mov	x13, x16
	WORD $0x54fffe88  // b.hi	LBB0_130 $-48(%rip)
LBB0_131:
	WORD $0x7101921f  // cmp	w16, #100
	WORD $0x540001a3  // b.lo	LBB0_133 $52(%rip)
	WORD $0x53023e0d  // ubfx	w13, w16, #2, #14
	WORD $0x52828f71  // mov	w17, #5243
	WORD $0x1b117dad  // mul	w13, w13, w17
	WORD $0x53117dad  // lsr	w13, w13, #17
	WORD $0x52800c91  // mov	w17, #100
	WORD $0x1b11c1b0  // msub	w16, w13, w17, w16
Lloh28:
	WORD $0x10001951  // adr	x17, _Digits $808(%rip)
Lloh29:
	WORD $0x91000231  // add	x17, x17, _Digits@PAGEOFF $0(%rip)
	WORD $0x92403e10  // and	x16, x16, #0xffff
	WORD $0x78707a30  // ldrh	w16, [x17, x16, lsl #1]
	WORD $0x781fed90  // strh	w16, [x12, #-2]!
	WORD $0xaa0d03f0  // mov	x16, x13
LBB0_133:
	WORD $0x71002a1f  // cmp	w16, #10
	WORD $0x540000c3  // b.lo	LBB0_135 $24(%rip)
Lloh30:
	WORD $0x1000184d  // adr	x13, _Digits $776(%rip)
Lloh31:
	WORD $0x910001ad  // add	x13, x13, _Digits@PAGEOFF $0(%rip)
	WORD $0x787059ad  // ldrh	w13, [x13, w16, uxtw #1]
	WORD $0x781fe18d  // sturh	w13, [x12, #-2]
	WORD $0x14000003  // b	LBB0_136 $12(%rip)
LBB0_135:
	WORD $0x321c060c  // orr	w12, w16, #0x30
	WORD $0x3900010c  // strb	w12, [x8]
LBB0_136:
	WORD $0xd280000d  // mov	x13, #0
	WORD $0x8b0f01ce  // add	x14, x14, x15
	WORD $0x4b0f016c  // sub	w12, w11, w15
	WORD $0x51000581  // sub	w1, w12, #1
	WORD $0x51000991  // sub	w17, w12, #2
	WORD $0xaa1103f0  // mov	x16, x17
LBB0_137:
	WORD $0x8b0d01cc  // add	x12, x14, x13
	WORD $0x385ff18c  // ldurb	w12, [x12, #-1]
	WORD $0xd10005ad  // sub	x13, x13, #1
	WORD $0x11000610  // add	w16, w16, #1
	WORD $0x7100c19f  // cmp	w12, #48
	WORD $0x54ffff60  // b.eq	LBB0_137 $-20(%rip)
	WORD $0x8b0d01cc  // add	x12, x14, x13
	WORD $0x9100058c  // add	x12, x12, #1
	WORD $0x7100053f  // cmp	w9, #1
	WORD $0x54ffd98b  // b.lt	LBB0_88 $-1232(%rip)
	WORD $0x0b0a01e2  // add	w2, w15, w10
	WORD $0x0b0d0042  // add	w2, w2, w13
	WORD $0x11000442  // add	w2, w2, #1
	WORD $0x6b02013f  // cmp	w9, w2
	WORD $0x5400016a  // b.ge	LBB0_143 $44(%rip)
	WORD $0x4b0b01ec  // sub	w12, w15, w11
	WORD $0x1100058a  // add	w10, w12, #1
	WORD $0x8b0d014b  // add	x11, x10, x13
	WORD $0x7100057f  // cmp	w11, #1
	WORD $0x54000c6b  // b.lt	LBB0_162 $396(%rip)
	WORD $0x92407d6a  // and	x10, x11, #0xffffffff
	WORD $0x7100217f  // cmp	w11, #8
	WORD $0x540001a2  // b.hs	LBB0_146 $52(%rip)
	WORD $0xd280000b  // mov	x11, #0
	WORD $0x14000055  // b	LBB0_160 $340(%rip)
LBB0_143:
	WORD $0xcb0d0029  // sub	x9, x1, x13
	WORD $0x7100053f  // cmp	w9, #1
	WORD $0x54ffd74b  // b.lt	LBB0_88 $-1304(%rip)
	WORD $0x4b0f016b  // sub	w11, w11, w15
	WORD $0x4b0d016b  // sub	w11, w11, w13
	WORD $0x5100096b  // sub	w11, w11, #2
	WORD $0x7100fd7f  // cmp	w11, #63
	WORD $0x540000e2  // b.hs	LBB0_148 $28(%rip)
	WORD $0x5280000b  // mov	w11, #0
	WORD $0x1400001f  // b	LBB0_151 $124(%rip)
LBB0_146:
	WORD $0x7101017f  // cmp	w11, #64
	WORD $0x54000462  // b.hs	LBB0_153 $140(%rip)
	WORD $0xd280000b  // mov	x11, #0
	WORD $0x14000038  // b	LBB0_157 $224(%rip)
LBB0_148:
	WORD $0xd2800001  // mov	x1, #0
	WORD $0xcb0d0222  // sub	x2, x17, x13
	WORD $0x91000571  // add	x17, x11, #1
	WORD $0x927a6a2b  // and	x11, x17, #0x1ffffffc0
	WORD $0x9100060c  // add	x12, x16, #1
	WORD $0x927a698c  // and	x12, x12, #0x1ffffffc0
	WORD $0x8b0a01ea  // add	x10, x15, x10
	WORD $0x8b0a0108  // add	x8, x8, x10
	WORD $0x8b0d0108  // add	x8, x8, x13
	WORD $0x8b0c0108  // add	x8, x8, x12
	WORD $0x9100050c  // add	x12, x8, #1
	WORD $0x92407c48  // and	x8, x2, #0xffffffff
	WORD $0x91000508  // add	x8, x8, #1
	WORD $0x927a6908  // and	x8, x8, #0x1ffffffc0
	WORD $0x4f01e600  // movi.16b	v0, #48
LBB0_149:
	WORD $0x8b0101ca  // add	x10, x14, x1
	WORD $0x8b0d014a  // add	x10, x10, x13
	WORD $0x3c801140  // stur	q0, [x10, #1]
	WORD $0x3c811140  // stur	q0, [x10, #17]
	WORD $0x3c821140  // stur	q0, [x10, #33]
	WORD $0x3c831140  // stur	q0, [x10, #49]
	WORD $0x91010021  // add	x1, x1, #64
	WORD $0xeb01011f  // cmp	x8, x1
	WORD $0x54ffff01  // b.ne	LBB0_149 $-32(%rip)
	WORD $0xeb0b023f  // cmp	x17, x11
	WORD $0x54ffd2a0  // b.eq	LBB0_88 $-1452(%rip)
LBB0_151:
	WORD $0x52800608  // mov	w8, #48
LBB0_152:
	WORD $0x38001588  // strb	w8, [x12], #1
	WORD $0x1100056b  // add	w11, w11, #1
	WORD $0x6b09017f  // cmp	w11, w9
	WORD $0x54ffffab  // b.lt	LBB0_152 $-12(%rip)
	WORD $0x17fffe8f  // b	LBB0_88 $-1476(%rip)
LBB0_153:
	WORD $0xd280000f  // mov	x15, #0
	WORD $0x927a614b  // and	x11, x10, #0x7fffffc0
	WORD $0x0b0d0190  // add	w16, w12, w13
	WORD $0x11000610  // add	w16, w16, #1
	WORD $0x927a6210  // and	x16, x16, #0x7fffffc0
	WORD $0xcb1003f0  // neg	x16, x16
	WORD $0x8b0d01d1  // add	x17, x14, x13
LBB0_154:
	WORD $0x8b0f0221  // add	x1, x17, x15
	WORD $0x3cdf1020  // ldur	q0, [x1, #-15]
	WORD $0x3cde1021  // ldur	q1, [x1, #-31]
	WORD $0x3cdd1022  // ldur	q2, [x1, #-47]
	WORD $0x3cdc1023  // ldur	q3, [x1, #-63]
	WORD $0x3c9f2020  // stur	q0, [x1, #-14]
	WORD $0x3c9e2021  // stur	q1, [x1, #-30]
	WORD $0x3c9d2022  // stur	q2, [x1, #-46]
	WORD $0x3c9c2023  // stur	q3, [x1, #-62]
	WORD $0xd10101ef  // sub	x15, x15, #64
	WORD $0xeb0f021f  // cmp	x16, x15
	WORD $0x54fffea1  // b.ne	LBB0_154 $-44(%rip)
	WORD $0xeb0a017f  // cmp	x11, x10
	WORD $0x54000360  // b.eq	LBB0_162 $108(%rip)
	WORD $0xf27d095f  // tst	x10, #0x38
	WORD $0x54000200  // b.eq	LBB0_160 $64(%rip)
LBB0_157:
	WORD $0xcb0b03ef  // neg	x15, x11
	WORD $0x927d6d4b  // and	x11, x10, #0x7ffffff8
	WORD $0x0b0d018c  // add	w12, w12, w13
	WORD $0x1100058c  // add	w12, w12, #1
	WORD $0x927d6d8c  // and	x12, x12, #0x7ffffff8
	WORD $0xcb0c03ec  // neg	x12, x12
	WORD $0x8b0d01d0  // add	x16, x14, x13
LBB0_158:
	WORD $0x8b0f0211  // add	x17, x16, x15
	WORD $0xfc5f9220  // ldur	d0, [x17, #-7]
	WORD $0xfc1fa220  // stur	d0, [x17, #-6]
	WORD $0xd10021ef  // sub	x15, x15, #8
	WORD $0xeb0f019f  // cmp	x12, x15
	WORD $0x54ffff61  // b.ne	LBB0_158 $-20(%rip)
	WORD $0xeb0a017f  // cmp	x11, x10
	WORD $0x54000140  // b.eq	LBB0_162 $40(%rip)
LBB0_160:
	WORD $0xcb0b03ec  // neg	x12, x11
	WORD $0x8b0d01cf  // add	x15, x14, x13
LBB0_161:
	WORD $0x8b0c01f0  // add	x16, x15, x12
	WORD $0x386c69f1  // ldrb	w17, [x15, x12]
	WORD $0x39000611  // strb	w17, [x16, #1]
	WORD $0x9100056b  // add	x11, x11, #1
	WORD $0xd100058c  // sub	x12, x12, #1
	WORD $0xeb0a017f  // cmp	x11, x10
	WORD $0x54ffff43  // b.lo	LBB0_161 $-24(%rip)
LBB0_162:
	WORD $0x528005ca  // mov	w10, #46
	WORD $0x3829490a  // strb	w10, [x8, w9, uxtw]
	WORD $0x8b0d01c8  // add	x8, x14, x13
	WORD $0x9100090c  // add	x12, x8, #2
	WORD $0x4b000180  // sub	w0, w12, w0
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_163:
	WORD $0x8b09016b  // add	x11, x11, x9
LBB0_164:
	WORD $0x52800608  // mov	w8, #48
LBB0_165:
	WORD $0x38001568  // strb	w8, [x11], #1
	WORD $0xeb0c017f  // cmp	x11, x12
	WORD $0x54ffffc3  // b.lo	LBB0_165 $-8(%rip)
	WORD $0x17fffe52  // b	LBB0_88 $-1720(%rip)
LBB0_166:
	WORD $0x5288480b  // mov	w11, #16960
	WORD $0x72a001eb  // movk	w11, #15, lsl #16
	WORD $0xeb0b015f  // cmp	x10, x11
	WORD $0x54000122  // b.hs	LBB0_169 $36(%rip)
	WORD $0x528000cb  // mov	w11, #6
	WORD $0x8b0b010b  // add	x11, x8, x11
	WORD $0x14000023  // b	LBB0_176 $140(%rip)
LBB0_168:
	WORD $0x52994009  // mov	w9, #51712
	WORD $0x72a77349  // movk	w9, #15258, lsl #16
	WORD $0xeb09019f  // cmp	x12, x9
	WORD $0x52800129  // mov	w9, #9
	WORD $0x17fffd0e  // b	LBB0_44 $-3016(%rip)
LBB0_169:
	WORD $0x5292d00b  // mov	w11, #38528
	WORD $0x72a0130b  // movk	w11, #152, lsl #16
	WORD $0xeb0b015f  // cmp	x10, x11
	WORD $0x54000102  // b.hs	LBB0_172 $32(%rip)
	WORD $0x528000eb  // mov	w11, #7
	WORD $0x8b0b010b  // add	x11, x8, x11
	WORD $0x14000017  // b	LBB0_176 $92(%rip)
LBB0_171:
	WORD $0x52800000  // mov	w0, #0
	WORD $0xa940fbfd  // ldp	fp, lr, [sp, #8]
	WORD $0x910083ff  // add	sp, sp, #32
	WORD $0xd65f03c0  // ret
LBB0_172:
	WORD $0x529c200b  // mov	w11, #57600
	WORD $0x72a0beab  // movk	w11, #1525, lsl #16
	WORD $0xeb0b015f  // cmp	x10, x11
	WORD $0x540000e2  // b.hs	LBB0_175 $28(%rip)
	WORD $0x5280010b  // mov	w11, #8
	WORD $0x8b0b010b  // add	x11, x8, x11
	WORD $0x1400000c  // b	LBB0_176 $48(%rip)
LBB0_174:
	WORD $0x1280862c  // mov	w12, #-1074
	WORD $0xaa0e03e9  // mov	x9, x14
	WORD $0x17fffc2a  // b	LBB0_5 $-3928(%rip)
LBB0_175:
	WORD $0x5299400b  // mov	w11, #51712
	WORD $0x72a7734b  // movk	w11, #15258, lsl #16
	WORD $0xeb0b015f  // cmp	x10, x11
	WORD $0x5280012b  // mov	w11, #9
	WORD $0x9a8b356b  // cinc	x11, x11, hs
	WORD $0x8b0b010b  // add	x11, x8, x11
	WORD $0xd360fd4c  // lsr	x12, x10, #32
	WORD $0xb5ffa26c  // cbnz	x12, LBB0_55 $-2996(%rip)
LBB0_176:
	WORD $0xaa0b03ec  // mov	x12, x11
	WORD $0xd3447d4d  // ubfx	x13, x10, #4, #28
	WORD $0x7109c5bf  // cmp	w13, #625
	WORD $0x54ffa762  // b.hs	LBB0_56 $-2836(%rip)
LBB0_177:
	WORD $0xaa0a03ed  // mov	x13, x10
	WORD $0x710191bf  // cmp	w13, #100
	WORD $0x54ffa9e2  // b.hs	LBB0_59 $-2756(%rip)
	WORD $0x17fffd58  // b	LBB0_60 $-2720(%rip)
_Digits:
	WORD $0x31303030  // .ascii 4, '0001020304050607'
	WORD $0x33303230  // .ascii 4, '0203040506070809'
	WORD $0x35303430  // .ascii 4, '0405060708091011'
	WORD $0x37303630  // .ascii 4, '0607080910111213'
	WORD $0x39303830  // .ascii 4, '0809101112131415'
	WORD $0x31313031  // .ascii 4, '1011121314151617'
	WORD $0x33313231  // .ascii 4, '1213141516171819'
	WORD $0x35313431  // .ascii 4, '1415161718192021'
	WORD $0x37313631  // .ascii 4, '1617181920212223'
	WORD $0x39313831  // .ascii 4, '1819202122232425'
	WORD $0x31323032  // .ascii 4, '2021222324252627'
	WORD $0x33323232  // .ascii 4, '2223242526272829'
	WORD $0x35323432  // .ascii 4, '2425262728293031'
	WORD $0x37323632  // .ascii 4, '2627282930313233'
	WORD $0x39323832  // .ascii 4, '2829303132333435'
	WORD $0x31333033  // .ascii 4, '3031323334353637'
	WORD $0x33333233  // .ascii 4, '3233343536373839'
	WORD $0x35333433  // .ascii 4, '3435363738394041'
	WORD $0x37333633  // .ascii 4, '3637383940414243'
	WORD $0x39333833  // .ascii 4, '3839404142434445'
	WORD $0x31343034  // .ascii 4, '4041424344454647'
	WORD $0x33343234  // .ascii 4, '4243444546474849'
	WORD $0x35343434  // .ascii 4, '4445464748495051'
	WORD $0x37343634  // .ascii 4, '4647484950515253'
	WORD $0x39343834  // .ascii 4, '4849505152535455'
	WORD $0x31353035  // .ascii 4, '5051525354555657'
	WORD $0x33353235  // .ascii 4, '5253545556575859'
	WORD $0x35353435  // .ascii 4, '5455565758596061'
	WORD $0x37353635  // .ascii 4, '5657585960616263'
	WORD $0x39353835  // .ascii 4, '5859606162636465'
	WORD $0x31363036  // .ascii 4, '6061626364656667'
	WORD $0x33363236  // .ascii 4, '6263646566676869'
	WORD $0x35363436  // .ascii 4, '6465666768697071'
	WORD $0x37363636  // .ascii 4, '6667686970717273'
	WORD $0x39363836  // .ascii 4, '6869707172737475'
	WORD $0x31373037  // .ascii 4, '7071727374757677'
	WORD $0x33373237  // .ascii 4, '7273747576777879'
	WORD $0x35373437  // .ascii 4, '7475767778798081'
	WORD $0x37373637  // .ascii 4, '7677787980818283'
	WORD $0x39373837  // .ascii 4, '7879808182838485'
	WORD $0x31383038  // .ascii 4, '8081828384858687'
	WORD $0x33383238  // .ascii 4, '8283848586878889'
	WORD $0x35383438  // .ascii 4, '8485868788899091'
	WORD $0x37383638  // .ascii 4, '8687888990919293'
	WORD $0x39383838  // .ascii 4, '8889909192939495'
	WORD $0x31393039  // .ascii 4, '9091929394959697'
	WORD $0x33393239  // .ascii 4, '9293949596979899'
	WORD $0x35393439  // .ascii 4, '949596979899'
	WORD $0x37393639  // .ascii 4, '96979899'
	WORD $0x39393839  // .ascii 4, '9899'
	  // .p2align 3, 0x00
_LB_3b41de77: // _pow10_ceil_sig.g
	WORD $0xbebcdc4f; WORD $0xff77b1fc  // .quad -38366372719436721
	WORD $0x13bb0f7b; WORD $0x25e8e89c  // .quad 2731688931043774331
	WORD $0xf73609b1; WORD $0x9faacf3d  // .quad -6941508010590729807
	WORD $0x8c54e9ad; WORD $0x77b19161  // .quad 8624834609543440813
	WORD $0x75038c1d; WORD $0xc795830d  // .quad -4065198994811024355
	WORD $0xef6a2418; WORD $0xd59df5b9  // .quad -3054014793352862696
	WORD $0xd2446f25; WORD $0xf97ae3d0  // .quad -469812725086392539
	WORD $0x6b44ad1e; WORD $0x4b057328  // .quad 5405853545163697438
	WORD $0x836ac577; WORD $0x9becce62  // .quad -7211161980820077193
	WORD $0x430aec33; WORD $0x4ee367f9  // .quad 5684501474941004851
	WORD $0x244576d5; WORD $0xc2e801fb  // .quad -4402266457597708587
	WORD $0x93cda740; WORD $0x229c41f7  // .quad 2493940825248868160
	WORD $0xed56d48a; WORD $0xf3a20279  // .quad -891147053569747830
	WORD $0x78c11110; WORD $0x6b435275  // .quad 7729112049988473104
	WORD $0x345644d6; WORD $0x9845418c  // .quad -7474495936122174250
	WORD $0x6b78aaaa; WORD $0x830a1389  // .quad -9004363024039368022
	WORD $0x416bd60c; WORD $0xbe5691ef  // .quad -4731433901725329908
	WORD $0xc656d554; WORD $0x23cc986b  // .quad 2579604275232953684
	WORD $0x11c6cb8f; WORD $0xedec366b  // .quad -1302606358729274481
	WORD $0xb7ec8aa9; WORD $0x2cbfbe86  // .quad 3224505344041192105
	WORD $0xeb1c3f39; WORD $0x94b3a202  // .quad -7731658001846878407
	WORD $0x32f3d6aa; WORD $0x7bf7d714  // .quad 8932844867666826922
	WORD $0xa5e34f07; WORD $0xb9e08a83  // .quad -5052886483881210105
	WORD $0x3fb0cc54; WORD $0xdaf5ccd9  // .quad -2669001970698630060
	WORD $0x8f5c22c9; WORD $0xe858ad24  // .quad -1704422086424124727
	WORD $0x8f9cff69; WORD $0xd1b3400f  // .quad -3336252463373287575
	WORD $0xd99995be; WORD $0x91376c36  // .quad -7982792831656159810
	WORD $0xb9c21fa2; WORD $0x23100809  // .quad 2526528228819083170
	WORD $0x8ffffb2d; WORD $0xb5854744  // .quad -5366805021142811859
	WORD $0x2832a78b; WORD $0xabd40a0c  // .quad -6065211750830921845
	WORD $0xb3fff9f9; WORD $0xe2e69915  // .quad -2096820258001126919
	WORD $0x323f516d; WORD $0x16c90c8f  // .quad 1641857348316123501
	WORD $0x907ffc3b; WORD $0x8dd01fad  // .quad -8228041688891786181
	WORD $0x7f6792e4; WORD $0xae3da7d9  // .quad -5891368184943504668
	WORD $0xf49ffb4a; WORD $0xb1442798  // .quad -5673366092687344822
	WORD $0xdf41779d; WORD $0x99cd11cf  // .quad -7364210231179380835
	WORD $0x31c7fa1d; WORD $0xdd95317f  // .quad -2480021597431793123
	WORD $0xd711d584; WORD $0x40405643  // .quad 4629795266307937668
	WORD $0x7f1cfc52; WORD $0x8a7d3eef  // .quad -8467542526035952558
	WORD $0x666b2573; WORD $0x482835ea  // .quad 5199465050656154995
	WORD $0x5ee43b66; WORD $0xad1c8eab  // .quad -5972742139117552794
	WORD $0x0005eed0; WORD $0xda324365  // .quad -2724040723534582064
	WORD $0x369d4a40; WORD $0xd863b256  // .quad -2854241655469553088
	WORD $0x40076a83; WORD $0x90bed43e  // .quad -8016736922845615485
	WORD $0xe2224e68; WORD $0x873e4f75  // .quad -8701430062309552536
	WORD $0xe804a292; WORD $0x5a7744a6  // .quad 6518754469289960082
	WORD $0x5aaae202; WORD $0xa90de353  // .quad -6265101559459552766
	WORD $0xa205cb37; WORD $0x711515d0  // .quad 8148443086612450103
	WORD $0x31559a83; WORD $0xd3515c28  // .quad -3219690930897053053
	WORD $0xca873e04; WORD $0x0d5a5b44  // .quad 962181821410786820
	WORD $0x1ed58091; WORD $0x8412d999  // .quad -8929835859451740015
	WORD $0xfe9486c3; WORD $0xe858790a  // .quad -1704479370831952189
	WORD $0x668ae0b6; WORD $0xa5178fff  // .quad -6550608805887287114
	WORD $0xbe39a873; WORD $0x626e974d  // .quad 7092772823314835571
	WORD $0x402d98e3; WORD $0xce5d73ff  // .quad -3576574988931720989
	WORD $0x2dc81290; WORD $0xfb0a3d21  // .quad -357406007711231344
	WORD $0x881c7f8e; WORD $0x80fa687f  // .quad -9152888395723407474
	WORD $0xbc9d0b9a; WORD $0x7ce66634  // .quad 8999993282035256218
	WORD $0x6a239f72; WORD $0xa139029f  // .quad -6829424476226871438
	WORD $0xebc44e81; WORD $0x1c1fffc1  // .quad 2026619565689294465
	WORD $0x44ac874e; WORD $0xc9874347  // .quad -3925094576856201394
	WORD $0x66b56221; WORD $0xa327ffb2  // .quad -6690097579743157727
	WORD $0x15d7a922; WORD $0xfbe91419  // .quad -294682202642863838
	WORD $0x0062baa9; WORD $0x4bf1ff9f  // .quad 5472436080603216553
	WORD $0xada6c9b5; WORD $0x9d71ac8f  // .quad -7101705404292871755
	WORD $0x603db4aa; WORD $0x6f773fc3  // .quad 8031958568804398250
	WORD $0x99107c22; WORD $0xc4ce17b3  // .quad -4265445736938701790
	WORD $0x384d21d4; WORD $0xcb550fb4  // .quad -3795109844276665900
	WORD $0x7f549b2b; WORD $0xf6019da0  // .quad -720121152745989333
	WORD $0x46606a49; WORD $0x7e2a53a1  // .quad 9091170749936331337
	WORD $0x4f94e0fb; WORD $0x99c10284  // .quad -7367604748107325189
	WORD $0xcbfc426e; WORD $0x2eda7444  // .quad 3376138709496513134
	WORD $0x637a1939; WORD $0xc0314325  // .quad -4597819916706768583
	WORD $0xfefb5309; WORD $0xfa911155  // .quad -391512631556746487
	WORD $0xbc589f88; WORD $0xf03d93ee  // .quad -1135588877456072824
	WORD $0x7eba27cb; WORD $0x793555ab  // .quad 8733981247408842699
	WORD $0x35b763b5; WORD $0x96267c75  // .quad -7627272076051127371
	WORD $0x2f3458df; WORD $0x4bc1558b  // .quad 5458738279630526687
	WORD $0x83253ca2; WORD $0xbbb01b92  // .quad -4922404076636521310
	WORD $0xfb016f17; WORD $0x9eb1aaed  // .quad -7011635205744005353
	WORD $0x23ee8bcb; WORD $0xea9c2277  // .quad -1541319077368263733
	WORD $0x79c1cadd; WORD $0x465e15a9  // .quad 5070514048102157021
	WORD $0x7675175f; WORD $0x92a1958a  // .quad -7880853450996246689
	WORD $0xec191eca; WORD $0x0bfacd89  // .quad 863228270850154186
	WORD $0x14125d36; WORD $0xb749faed  // .quad -5239380795317920458
	WORD $0x671f667c; WORD $0xcef980ec  // .quad -3532650679864695172
	WORD $0x5916f484; WORD $0xe51c79a8  // .quad -1937539975720012668
	WORD $0x80e7401b; WORD $0x82b7e127  // .quad -9027499368258256869
	WORD $0x37ae58d2; WORD $0x8f31cc09  // .quad -8128491512466089774
	WORD $0xb0908811; WORD $0xd1b2ecb8  // .quad -3336344095947716591
	WORD $0x8599ef07; WORD $0xb2fe3f0b  // .quad -5548928372155224313
	WORD $0xdcb4aa16; WORD $0x861fa7e6  // .quad -8782116138362033642
	WORD $0x67006ac9; WORD $0xdfbdcece  // .quad -2324474446766642487
	WORD $0x93e1d49b; WORD $0x67a791e0  // .quad 7469098900757009563
	WORD $0x006042bd; WORD $0x8bd6a141  // .quad -8370325556870233411
	WORD $0x5c6d24e1; WORD $0xe0c8bb2c  // .quad -2249342214667950879
	WORD $0x4078536d; WORD $0xaecc4991  // .quad -5851220927660403859
	WORD $0x73886e19; WORD $0x58fae9f7  // .quad 6411694268519837209
	WORD $0x90966848; WORD $0xda7f5bf5  // .quad -2702340141148116920
	WORD $0x506a899f; WORD $0xaf39a475  // .quad -5820440219632367201
	WORD $0x7a5e012d; WORD $0x888f9979  // .quad -8606491615858654931
	WORD $0x52429604; WORD $0x6d8406c9  // .quad 7891439908798240260
	WORD $0xd8f58178; WORD $0xaab37fd7  // .quad -6146428501395930760
	WORD $0xa6d33b84; WORD $0xc8e5087b  // .quad -3970758169284363388
	WORD $0xcf32e1d6; WORD $0xd5605fcd  // .quad -3071349608317525546
	WORD $0x90880a65; WORD $0xfb1e4a9a  // .quad -351761693178066331
	WORD $0xa17fcd26; WORD $0x855c3be0  // .quad -8837122532839535322
	WORD $0x9a550680; WORD $0x5cf2eea0  // .quad 6697677969404790400
	WORD $0xc9dfc06f; WORD $0xa6b34ad8  // .quad -6434717147622031249
	WORD $0xc0ea481f; WORD $0xf42faa48  // .quad -851274575098787809
	WORD $0xfc57b08b; WORD $0xd0601d8e  // .quad -3431710416100151157
	WORD $0xf124da27; WORD $0xf13b94da  // .quad -1064093218873484761
	WORD $0x5db6ce57; WORD $0x823c1279  // .quad -9062348037703676329
	WORD $0xd6b70859; WORD $0x76c53d08  // .quad 8558313775058847833
	WORD $0xb52481ed; WORD $0xa2cb1717  // .quad -6716249028702207507
	WORD $0x0c64ca6f; WORD $0x54768c4b  // .quad 6086206200396171887
	WORD $0xa26da268; WORD $0xcb7ddcdd  // .quad -3783625267450371480
	WORD $0xcf7dfd0a; WORD $0xa9942f5d  // .quad -6227300304786948854
	WORD $0x0b090b02; WORD $0xfe5d5415  // .quad -117845565885576446
	WORD $0x435d7c4d; WORD $0xd3f93b35  // .quad -3172439362556298163
	WORD $0x26e5a6e1; WORD $0x9efa548d  // .quad -6991182506319567135
	WORD $0x4a1a6db0; WORD $0xc47bc501  // .quad -4288617610811380304
	WORD $0x709f109a; WORD $0xc6b8e9b0  // .quad -4127292114472071014
	WORD $0x9ca1091c; WORD $0x359ab641  // .quad 3862600023340550428
	WORD $0x8cc6d4c0; WORD $0xf867241c  // .quad -547429124662700864
	WORD $0x03c94b63; WORD $0xc30163d2  // .quad -4395122007679087773
	WORD $0xd7fc44f8; WORD $0x9b407691  // .quad -7259672230555269896
	WORD $0x425dcf1e; WORD $0x79e0de63  // .quad 8782263791269039902
	WORD $0x4dfb5636; WORD $0xc2109436  // .quad -4462904269766699466
	WORD $0x12f542e5; WORD $0x985915fc  // .quad -7468914334623251739
	WORD $0xe17a2bc4; WORD $0xf294b943  // .quad -966944318780986428
	WORD $0x17b2939e; WORD $0x3e6f5b7b  // .quad 4498915137003099038
	WORD $0x6cec5b5a; WORD $0x979cf3ca  // .quad -7521869226879198374
	WORD $0xeecf9c43; WORD $0xa705992c  // .quad -6411550076227838909
	WORD $0x08277231; WORD $0xbd8430bd  // .quad -4790650515171610063
	WORD $0x2a838354; WORD $0x50c6ff78  // .quad 5820620459997365076
	WORD $0x4a314ebd; WORD $0xece53cec  // .quad -1376627125537124675
	WORD $0x35246429; WORD $0xa4f8bf56  // .quad -6559282480285457367
	WORD $0xae5ed136; WORD $0x940f4613  // .quad -7777920981101784778
	WORD $0xe136be9a; WORD $0x871b7795  // .quad -8711237568605798758
	WORD $0x99f68584; WORD $0xb9131798  // .quad -5110715207949843068
	WORD $0x59846e40; WORD $0x28e2557b  // .quad 2946011094524915264
	WORD $0xc07426e5; WORD $0xe757dd7e  // .quad -1776707991509915931
	WORD $0x2fe589d0; WORD $0x331aeada  // .quad 3682513868156144080
	WORD $0x3848984f; WORD $0x9096ea6f  // .quad -8027971522334779313
	WORD $0x5def7622; WORD $0x3ff0d2c8  // .quad 4607414176811284002
	WORD $0x065abe63; WORD $0xb4bca50b  // .quad -5423278384491086237
	WORD $0x756b53aa; WORD $0x0fed077a  // .quad 1147581702586717098
	WORD $0xc7f16dfb; WORD $0xe1ebce4d  // .quad -2167411962186469893
	WORD $0x12c62895; WORD $0xd3e84959  // .quad -3177208890193991531
	WORD $0x9cf6e4bd; WORD $0x8d3360f0  // .quad -8272161504007625539
	WORD $0xabbbd95d; WORD $0x64712dd7  // .quad 7237616480483531101
	WORD $0xc4349dec; WORD $0xb080392c  // .quad -5728515861582144020
	WORD $0x96aacfb4; WORD $0xbd8d794d  // .quad -4788037454677749836
	WORD $0xf541c567; WORD $0xdca04777  // .quad -2548958808550292121
	WORD $0xfc5583a1; WORD $0xecf0d7a0  // .quad -1373360799919799391
	WORD $0xf9491b60; WORD $0x89e42caa  // .quad -8510628282985014432
	WORD $0x9db57245; WORD $0xf41686c4  // .quad -858350499949874619
	WORD $0xb79b6239; WORD $0xac5d37d5  // .quad -6026599335303880135
	WORD $0xc522ced6; WORD $0x311c2875  // .quad 3538747893490044630
	WORD $0x25823ac7; WORD $0xd77485cb  // .quad -2921563150702462265
	WORD $0x366b828c; WORD $0x7d633293  // .quad 9035120885289943692
	WORD $0xf77164bc; WORD $0x86a8d39e  // .quad -8743505996830120772
	WORD $0x02033198; WORD $0xae5dff9c  // .quad -5882264492762254952
	WORD $0xb54dbdeb; WORD $0xa8530886  // .quad -6317696477610263061
	WORD $0x0283fdfd; WORD $0xd9f57f83  // .quad -2741144597525430787
	WORD $0x62a12d66; WORD $0xd267caa8  // .quad -3285434578585440922
	WORD $0xc324fd7c; WORD $0xd072df63  // .quad -3426430746906788484
	WORD $0x3da4bc60; WORD $0x8380dea9  // .quad -8970925639256982432
	WORD $0x59f71e6e; WORD $0x4247cb9e  // .quad 4776009810824339054
	WORD $0x8d0deb78; WORD $0xa4611653  // .quad -6601971030643840136
	WORD $0xf074e609; WORD $0x52d9be85  // .quad 5970012263530423817
	WORD $0x70516656; WORD $0xcd795be8  // .quad -3640777769877412266
	WORD $0x6c921f8c; WORD $0x67902e27  // .quad 7462515329413029772
	WORD $0x4632dff6; WORD $0x806bd971  // .quad -9193015133814464522
	WORD $0xa3db53b7; WORD $0x00ba1cd8  // .quad 52386062455755703
	WORD $0x97bf97f3; WORD $0xa086cfcd  // .quad -6879582898840692749
	WORD $0xccd228a5; WORD $0x80e8a40e  // .quad -9157889458785081179
	WORD $0xfdaf7df0; WORD $0xc8a883c0  // .quad -3987792605123478032
	WORD $0x8006b2ce; WORD $0x6122cd12  // .quad 6999382250228200142
	WORD $0x3d1b5d6c; WORD $0xfad2a4b1  // .quad -373054737976959636
	WORD $0x20085f82; WORD $0x796b8057  // .quad 8749227812785250178
	WORD $0xc6311a63; WORD $0x9cc3a6ee  // .quad -7150688238876681629
	WORD $0x74053bb1; WORD $0xcbe33036  // .quad -3755104653863994447
	WORD $0x77bd60fc; WORD $0xc3f490aa  // .quad -4326674280168464132
	WORD $0x11068a9d; WORD $0xbedbfc44  // .quad -4693880817329993059
	WORD $0x15acb93b; WORD $0xf4f1b4d5  // .quad -796656831783192261
	WORD $0x15482d45; WORD $0xee92fb55  // .quad -1255665003235103419
	WORD $0x2d8bf3c5; WORD $0x99171105  // .quad -7415439547505577019
	WORD $0x2d4d1c4b; WORD $0x751bdd15  // .quad 8438581409832836171
	WORD $0x78eef0b6; WORD $0xbf5cd546  // .quad -4657613415954583370
	WORD $0x78a0635e; WORD $0xd262d45a  // .quad -3286831292991118498
	WORD $0x172aace4; WORD $0xef340a98  // .quad -1210330751515841308
	WORD $0x16c87c35; WORD $0x86fb8971  // .quad -8720225134666286027
	WORD $0x0e7aac0e; WORD $0x9580869f  // .quad -7673985747338482674
	WORD $0xae3d4da1; WORD $0xd45d35e6  // .quad -3144297699952734815
	WORD $0xd2195712; WORD $0xbae0a846  // .quad -4980796165745715438
	WORD $0x59cca10a; WORD $0x89748360  // .quad -8542058143368306422
	WORD $0x869facd7; WORD $0xe998d258  // .quad -1614309188754756393
	WORD $0x703fc94c; WORD $0x2bd1a438  // .quad 3157485376071780684
	WORD $0x5423cc06; WORD $0x91ff8377  // .quad -7926472270612804602
	WORD $0x4627ddd0; WORD $0x7b6306a3  // .quad 8890957387685944784
	WORD $0x292cbf08; WORD $0xb67f6455  // .quad -5296404319838617848
	WORD $0x17b1d543; WORD $0x1a3bc84c  // .quad 1890324697752655171
	WORD $0x7377eeca; WORD $0xe41f3d6a  // .quad -2008819381370884406
	WORD $0x1d9e4a94; WORD $0x20caba5f  // .quad 2362905872190818964
	WORD $0x882af53e; WORD $0x8e938662  // .quad -8173041140997884610
	WORD $0x7282ee9d; WORD $0x547eb47b  // .quad 6088502188546649757
	WORD $0x2a35b28d; WORD $0xb23867fb  // .quad -5604615407819967859
	WORD $0x4f23aa44; WORD $0xe99e619a  // .quad -1612744301171463612
	WORD $0xf4c31f31; WORD $0xdec681f9  // .quad -2394083241347571919
	WORD $0xe2ec94d5; WORD $0x6405fa00  // .quad 7207441660390446293
	WORD $0x38f9f37e; WORD $0x8b3c113c  // .quad -8413831053483314306
	WORD $0x8dd3dd05; WORD $0xde83bc40  // .quad -2412877989897052923
	WORD $0x4738705e; WORD $0xae0b158b  // .quad -5905602798426754978
	WORD $0xb148d446; WORD $0x9624ab50  // .quad -7627783505798704058
	WORD $0x19068c76; WORD $0xd98ddaee  // .quad -2770317479606055818
	WORD $0xdd9b0958; WORD $0x3badd624  // .quad 4300328673033783640
	WORD $0xcfa417c9; WORD $0x87f8a8d4  // .quad -8648977452394866743
	WORD $0x0a80e5d7; WORD $0xe54ca5d7  // .quad -1923980597781273129
	WORD $0x038d1dbc; WORD $0xa9f6d30a  // .quad -6199535797066195524
	WORD $0xcd211f4d; WORD $0x5e9fcf4c  // .quad 6818396289628184397
	WORD $0x8470652b; WORD $0xd47487cc  // .quad -3137733727905356501
	WORD $0x00696720; WORD $0x7647c320  // .quad 8522995362035230496
	WORD $0xd2c63f3b; WORD $0x84c8d4df  // .quad -8878612607581929669
	WORD $0x0041e074; WORD $0x29ecd9f4  // .quad 3021029092058325108
	WORD $0xc777cf09; WORD $0xa5fb0a17  // .quad -6486579741050024183
	WORD $0x00525891; WORD $0xf4681071  // .quad -835399653354481519
	WORD $0xb955c2cc; WORD $0xcf79cc9d  // .quad -3496538657885142324
	WORD $0x4066eeb5; WORD $0x7182148d  // .quad 8179122470161673909
	WORD $0x93d599bf; WORD $0x81ac1fe2  // .quad -9102865688819295809
	WORD $0x48405531; WORD $0xc6f14cd8  // .quad -4111420493003729615
	WORD $0x38cb002f; WORD $0xa21727db  // .quad -6766896092596731857
	WORD $0x5a506a7d; WORD $0xb8ada00e  // .quad -5139275616254662019
	WORD $0x06fdc03b; WORD $0xca9cf1d2  // .quad -3846934097318526917
	WORD $0xf0e4851d; WORD $0xa6d90811  // .quad -6424094520318327523
	WORD $0x88bd304a; WORD $0xfd442e46  // .quad -196981603220770742
	WORD $0x6d1da664; WORD $0x908f4a16  // .quad -8030118150397909404
	WORD $0x15763e2e; WORD $0x9e4a9cec  // .quad -7040642529654063570
	WORD $0x043287ff; WORD $0x9a598e4e  // .quad -7324666853212387329
	WORD $0x1ad3cdba; WORD $0xc5dd4427  // .quad -4189117143640191558
	WORD $0x853f29fe; WORD $0x40eff1e1  // .quad 4679224488766679550
	WORD $0xe188c128; WORD $0xf7549530  // .quad -624710411122851544
	WORD $0xe68ef47d; WORD $0xd12bee59  // .quad -3374341425896426371
	WORD $0x8cf578b9; WORD $0x9a94dd3e  // .quad -7307973034592864071
	WORD $0x301958cf; WORD $0x82bb74f8  // .quad -9026492418826348337
	WORD $0x3032d6e7; WORD $0xc13a148e  // .quad -4523280274813692185
	WORD $0x3c1faf02; WORD $0xe36a5236  // .quad -2059743486678159614
	WORD $0xbc3f8ca1; WORD $0xf18899b1  // .quad -1042414325089727327
	WORD $0xcb279ac2; WORD $0xdc44e6c3  // .quad -2574679358347699518
	WORD $0x15a7b7e5; WORD $0x96f5600f  // .quad -7569037980822161435
	WORD $0x5ef8c0ba; WORD $0x29ab103a  // .quad 3002511419460075706
	WORD $0xdb11a5de; WORD $0xbcb2b812  // .quad -4849611457600313890
	WORD $0xf6b6f0e8; WORD $0x7415d448  // .quad 8364825292752482536
	WORD $0x91d60f56; WORD $0xebdf6617  // .quad -1450328303573004458
	WORD $0x3464ad22; WORD $0x111b495b  // .quad 1232659579085827362
	WORD $0xbb25c995; WORD $0x936b9fce  // .quad -7823984217374209643
	WORD $0x00beec35; WORD $0xcab10dd9  // .quad -3841273781498745803
	WORD $0x69ef3bfb; WORD $0xb84687c2  // .quad -5168294253290374149
	WORD $0x40eea743; WORD $0x3d5d514f  // .quad 4421779809981343555
	WORD $0x046b0afa; WORD $0xe65829b3  // .quad -1848681798185579782
	WORD $0x112a5113; WORD $0x0cb4a5a3  // .quad 915538744049291539
	WORD $0xe2c2e6dc; WORD $0x8ff71a0f  // .quad -8072955151507069220
	WORD $0xeaba72ac; WORD $0x47f0e785  // .quad 5183897733458195116
	WORD $0xdb73a093; WORD $0xb3f4e093  // .quad -5479507920956448621
	WORD $0x65690f57; WORD $0x59ed2167  // .quad 6479872166822743895
	WORD $0xd25088b8; WORD $0xe0f218b8  // .quad -2237698882768172872
	WORD $0x3ec3532d; WORD $0x306869c1  // .quad 3488154190101041965
	WORD $0x83725573; WORD $0x8c974f73  // .quad -8316090829371189901
	WORD $0xc73a13fc; WORD $0x1e414218  // .quad 2180096368813151228
	WORD $0x644eeacf; WORD $0xafbd2350  // .quad -5783427518286599473
	WORD $0xf90898fb; WORD $0xe5d1929e  // .quad -1886565557410948869
	WORD $0x7d62a583; WORD $0xdbac6c24  // .quad -2617598379430861437
	WORD $0xb74abf3a; WORD $0xdf45f746  // .quad -2358206946763686086
	WORD $0xce5da772; WORD $0x894bc396  // .quad -8553528014785370254
	WORD $0x328eb784; WORD $0x6b8bba8c  // .quad 7749492695127472004
	WORD $0x81f5114f; WORD $0xab9eb47c  // .quad -6080224000054324913
	WORD $0x3f326565; WORD $0x066ea92f  // .quad 463493832054564197
	WORD $0xa27255a2; WORD $0xd686619b  // .quad -2988593981640518238
	WORD $0x0efefebe; WORD $0xc80a537b  // .quad -4032318728359182658
	WORD $0x45877585; WORD $0x8613fd01  // .quad -8785400266166405755
	WORD $0xe95f5f37; WORD $0xbd06742c  // .quad -4826042214438183113
	WORD $0x96e952e7; WORD $0xa798fc41  // .quad -6370064314280619289
	WORD $0x23b73705; WORD $0x2c481138  // .quad 3190819268807046917
	WORD $0xfca3a7a0; WORD $0xd17f3b51  // .quad -3350894374423386208
	WORD $0x2ca504c6; WORD $0xf75a1586  // .quad -623161932418579258
	WORD $0x3de648c4; WORD $0x82ef8513  // .quad -9011838011655698236
	WORD $0xdbe722fc; WORD $0x9a984d73  // .quad -7307005235402693892
	WORD $0x0d5fdaf5; WORD $0xa3ab6658  // .quad -6653111496142234891
	WORD $0xd2e0ebbb; WORD $0xc13e60d0  // .quad -4522070525825979461
	WORD $0x10b7d1b3; WORD $0xcc963fee  // .quad -3704703351750405709
	WORD $0x079926a9; WORD $0x318df905  // .quad 3570783879572301481
	WORD $0x94e5c61f; WORD $0xffbbcfe9  // .quad -19193171260619233
	WORD $0x497f7053; WORD $0xfdf17746  // .quad -148206168962011053
	WORD $0xfd0f9bd3; WORD $0x9fd561f1  // .quad -6929524759678968877
	WORD $0xedefa634; WORD $0xfeb6ea8b  // .quad -92628855601256908
	WORD $0x7c5382c8; WORD $0xc7caba6e  // .quad -4050219931171323192
	WORD $0xe96b8fc1; WORD $0xfe64a52e  // .quad -115786069501571135
	WORD $0x1b68637b; WORD $0xf9bd690a  // .quad -451088895536766085
	WORD $0xa3c673b1; WORD $0x3dfdce7a  // .quad 4466953431550423985
	WORD $0x51213e2d; WORD $0x9c1661a6  // .quad -7199459587351560659
	WORD $0xa65c084f; WORD $0x06bea10c  // .quad 486002885505321039
	WORD $0xe5698db8; WORD $0xc31bfa0f  // .quad -4387638465762062920
	WORD $0xcff30a63; WORD $0x486e494f  // .quad 5219189625309039203
	WORD $0xdec3f126; WORD $0xf3e2f893  // .quad -872862063775190746
	WORD $0xc3efccfb; WORD $0x5a89dba3  // .quad 6523987031636299003
	WORD $0x6b3a76b7; WORD $0x986ddb5c  // .quad -7463067817500576073
	WORD $0x5a75e01d; WORD $0xf8962946  // .quad -534194123654701027
	WORD $0x86091465; WORD $0xbe895233  // .quad -4717148753448332187
	WORD $0xf1135824; WORD $0xf6bbb397  // .quad -667742654568376284
	WORD $0x678b597f; WORD $0xee2ba6c0  // .quad -1284749923383027329
	WORD $0xed582e2d; WORD $0x746aa07d  // .quad 8388693718644305453
	WORD $0x40b717ef; WORD $0x94db4838  // .quad -7720497729755473937
	WORD $0xb4571cdd; WORD $0xa8c2a44e  // .quad -6286281471915778851
	WORD $0x50e4ddeb; WORD $0xba121a46  // .quad -5038936143766954517
	WORD $0x616ce414; WORD $0x92f34d62  // .quad -7857851839894723564
	WORD $0xe51e1566; WORD $0xe896a0d7  // .quad -1686984161281305242
	WORD $0xf9c81d18; WORD $0x77b020ba  // .quad 8624429273841147160
	WORD $0xef32cd60; WORD $0x915e2486  // .quad -7971894128441897632
	WORD $0xdc1d122f; WORD $0x0ace1474  // .quad 778582277723329071
	WORD $0xaaff80b8; WORD $0xb5b5ada8  // .quad -5353181642124984136
	WORD $0x132456bb; WORD $0x0d819992  // .quad 973227847154161339
	WORD $0xd5bf60e6; WORD $0xe3231912  // .quad -2079791034228842266
	WORD $0x97ed6c6a; WORD $0x10e1fff6  // .quad 1216534808942701674
	WORD $0xc5979c8f; WORD $0x8df5efab  // .quad -8217398424034108273
	WORD $0x1ef463c2; WORD $0xca8d3ffa  // .quad -3851351762838199358
	WORD $0xb6fd83b3; WORD $0xb1736b96  // .quad -5660062011615247437
	WORD $0xa6b17cb3; WORD $0xbd308ff8  // .quad -4814189703547749197
	WORD $0x64bce4a0; WORD $0xddd0467c  // .quad -2463391496091671392
	WORD $0xd05ddbdf; WORD $0xac7cb3f6  // .quad -6017737129434686497
	WORD $0xbef60ee4; WORD $0x8aa22c0d  // .quad -8457148712698376476
	WORD $0x423aa96c; WORD $0x6bcdf07a  // .quad 7768129340171790700
	WORD $0x2eb3929d; WORD $0xad4ab711  // .quad -5959749872445582691
	WORD $0xd2c953c7; WORD $0x86c16c98  // .quad -8736582398494813241
	WORD $0x7a607744; WORD $0xd89d64d5  // .quad -2838001322129590460
	WORD $0x077ba8b8; WORD $0xe871c7bf  // .quad -1697355961263740744
	WORD $0x6c7c4a8b; WORD $0x87625f05  // .quad -8691279853972075893
	WORD $0x64ad4973; WORD $0x11471cd7  // .quad 1244995533423855987
	WORD $0xc79b5d2d; WORD $0xa93af6c6  // .quad -6252413799037706963
	WORD $0x3dd89bd0; WORD $0xd598e40d  // .quad -3055441601647567920
	WORD $0x79823479; WORD $0xd389b478  // .quad -3203831230369745799
	WORD $0x8d4ec2c4; WORD $0x4aff1d10  // .quad 5404070034795315908
	WORD $0x4bf160cb; WORD $0x843610cb  // .quad -8919923546622172981
	WORD $0x585139bb; WORD $0xcedf722a  // .quad -3539985255894009413
	WORD $0x1eedb8fe; WORD $0xa54394fe  // .quad -6538218414850328322
	WORD $0xee658829; WORD $0xc2974eb4  // .quad -4424981569867511767
	WORD $0xa6a9273e; WORD $0xce947a3d  // .quad -3561087000135522498
	WORD $0x29feea33; WORD $0x733d2262  // .quad 8303831092947774003
	WORD $0x8829b887; WORD $0x811ccc66  // .quad -9143208402725783417
	WORD $0x5a3f5260; WORD $0x0806357d  // .quad 578208414664970848
	WORD $0x2a3426a8; WORD $0xa163ff80  // .quad -6817324484979841368
	WORD $0xb0cf26f8; WORD $0xca07c2dc  // .quad -3888925500096174344
	WORD $0x34c13052; WORD $0xc9bcff60  // .quad -3909969587797413806
	WORD $0xdd02f0b6; WORD $0xfc89b393  // .quad -249470856692830026
	WORD $0x41f17c67; WORD $0xfc2c3f38  // .quad -275775966319379353
	WORD $0xd443ace3; WORD $0xbbac2078  // .quad -4923524589293425437
	WORD $0x2936edc0; WORD $0x9d9ba783  // .quad -7089889006590693952
	WORD $0x84aa4c0e; WORD $0xd54b944b  // .quad -3077202868308390898
	WORD $0xf384a931; WORD $0xc5029163  // .quad -4250675239810979535
	WORD $0x65d4df12; WORD $0x0a9e795e  // .quad 765182433041899282
	WORD $0xf065d37d; WORD $0xf64335bc  // .quad -701658031336336515
	WORD $0xff4a16d6; WORD $0x4d4617b5  // .quad 5568164059729762006
	WORD $0x163fa42e; WORD $0x99ea0196  // .quad -7356065297226292178
	WORD $0xbf8e4e46; WORD $0x504bced1  // .quad 5785945546544795206
	WORD $0x9bcf8d39; WORD $0xc06481fb  // .quad -4583395603105477319
	WORD $0x2f71e1d7; WORD $0xe45ec286  // .quad -1990940103673781801
	WORD $0x82c37088; WORD $0xf07da27a  // .quad -1117558485454458744
	WORD $0xbb4e5a4d; WORD $0x5d767327  // .quad 6734696907262548557
	WORD $0x91ba2655; WORD $0x964e858c  // .quad -7616003081050118571
	WORD $0xd510f870; WORD $0x3a6a07f8  // .quad 4209185567039092848
	WORD $0xb628afea; WORD $0xbbe226ef  // .quad -4908317832885260310
	WORD $0x0a55368c; WORD $0x890489f7  // .quad -8573576096483297652
	WORD $0xa3b2dbe5; WORD $0xeadab0ab  // .quad -1523711272679187483
	WORD $0xccea842f; WORD $0x2b45ac74  // .quad 3118087934678041647
	WORD $0x464fc96f; WORD $0x92c8ae6b  // .quad -7869848573065574033
	WORD $0x0012929e; WORD $0x3b0b8bc9  // .quad 4254647968387469982
	WORD $0x17e3bbcb; WORD $0xb77ada06  // .quad -5225624697904579637
	WORD $0x40173745; WORD $0x09ce6ebb  // .quad 706623942056949573
	WORD $0x9ddcaabd; WORD $0xe5599087  // .quad -1920344853953336643
	WORD $0x101d0516; WORD $0xcc420a6a  // .quad -3728406090856200938
	WORD $0xc2a9eab6; WORD $0x8f57fa54  // .quad -8117744561361917258
	WORD $0x4a12232e; WORD $0x9fa94682  // .quad -6941939825212513490
	WORD $0xf3546564; WORD $0xb32df8e9  // .quad -5535494683275008668
	WORD $0xdc96abfa; WORD $0x47939822  // .quad 5157633273766521850
	WORD $0x70297ebd; WORD $0xdff97724  // .quad -2307682335666372931
	WORD $0x93bc56f8; WORD $0x59787e2b  // .quad 6447041592208152312
	WORD $0xc619ef36; WORD $0x8bfbea76  // .quad -8359830487432564938
	WORD $0x3c55b65b; WORD $0x57eb4edb  // .quad 6335244004343789147
	WORD $0x77a06b03; WORD $0xaefae514  // .quad -5838102090863318269
	WORD $0x0b6b23f2; WORD $0xede62292  // .quad -1304317031425039374
	WORD $0x958885c4; WORD $0xdab99e59  // .quad -2685941595151759932
	WORD $0x8e45ecee; WORD $0xe95fab36  // .quad -1630396289281299218
	WORD $0xfd75539b; WORD $0x88b402f7  // .quad -8596242524610931813
	WORD $0x18ebb415; WORD $0x11dbcb02  // .quad 1286845328412881941
	WORD $0xfcd2a881; WORD $0xaae103b5  // .quad -6133617137336276863
	WORD $0x9f26a11a; WORD $0xd652bdc2  // .quad -3003129357911285478
	WORD $0x7c0752a2; WORD $0xd59944a3  // .quad -3055335403242958174
	WORD $0x46f04960; WORD $0x4be76d33  // .quad 5469460339465668960
	WORD $0x2d8493a5; WORD $0x857fcae6  // .quad -8827113654667930715
	WORD $0x0c562ddc; WORD $0x6f70a440  // .quad 8030098730593431004
	WORD $0xb8e5b88e; WORD $0xa6dfbd9f  // .quad -6422206049907525490
	WORD $0x0f6bb953; WORD $0xcb4ccd50  // .quad -3797434642040374957
	WORD $0xa71f26b2; WORD $0xd097ad07  // .quad -3416071543957018958
	WORD $0x1346a7a8; WORD $0x7e2000a4  // .quad 9088264752731695016
	WORD $0xc873782f; WORD $0x825ecc24  // .quad -9052573742614218705
	WORD $0x8c0c28c9; WORD $0x8ed40066  // .quad -8154892584824854327
	WORD $0xfa90563b; WORD $0xa2f67f2d  // .quad -6704031159840385477
	WORD $0x2f0f32fb; WORD $0x72890080  // .quad 8253128342678483707
	WORD $0x79346bca; WORD $0xcbb41ef9  // .quad -3768352931373093942
	WORD $0x3ad2ffba; WORD $0x4f2b40a0  // .quad 5704724409920716730
	WORD $0xd78186bc; WORD $0xfea126b7  // .quad -98755145788979524
	WORD $0x4987bfa9; WORD $0xe2f610c8  // .quad -2092466524453879895
	WORD $0xe6b0f436; WORD $0x9f24b832  // .quad -6979250993759194058
	WORD $0x2df4d7ca; WORD $0x0dd9ca7d  // .quad 998051431430019018
	WORD $0xa05d3143; WORD $0xc6ede63f  // .quad -4112377723771604669
	WORD $0x79720dbc; WORD $0x91503d1c  // .quad -7975807747567252036
	WORD $0x88747d94; WORD $0xf8a95fcf  // .quad -528786136287117932
	WORD $0x97ce912b; WORD $0x75a44c63  // .quad 8476984389250486571
	WORD $0xb548ce7c; WORD $0x9b69dbe1  // .quad -7248020362820530564
	WORD $0x3ee11abb; WORD $0xc986afbe  // .quad -3925256793573221701
	WORD $0x229b021b; WORD $0xc24452da  // .quad -4448339435098275301
	WORD $0xce996169; WORD $0xfbe85bad  // .quad -294884973539139223
	WORD $0xab41c2a2; WORD $0xf2d56790  // .quad -948738275445456222
	WORD $0x423fb9c4; WORD $0xfae27299  // .quad -368606216923924028
	WORD $0x6b0919a5; WORD $0x97c560ba  // .quad -7510490449794491995
	WORD $0xc967d41b; WORD $0xdccd879f  // .quad -2536221894791146469
	WORD $0x05cb600f; WORD $0xbdb6b8e9  // .quad -4776427043815727089
	WORD $0xbbc1c921; WORD $0x5400e987  // .quad 6053094668365842721
	WORD $0x473e3813; WORD $0xed246723  // .quad -1358847786342270957
	WORD $0xaab23b69; WORD $0x290123e9  // .quad 2954682317029915497
	WORD $0x0c86e30b; WORD $0x9436c076  // .quad -7766808894105001205
	WORD $0x0aaf6522; WORD $0xf9a0b672  // .quad -459166561069996766
	WORD $0x8fa89bce; WORD $0xb9447093  // .quad -5096825099203863602
	WORD $0x8d5b3e6a; WORD $0xf808e40e  // .quad -573958201337495958
	WORD $0x7392c2c2; WORD $0xe7958cb8  // .quad -1759345355577441598
	WORD $0x30b20e05; WORD $0xb60b1d12  // .quad -5329133770099257851
	WORD $0x483bb9b9; WORD $0x90bd77f3  // .quad -8017119874876982855
	WORD $0x5e6f48c3; WORD $0xb1c6f22b  // .quad -5636551615525730109
	WORD $0x1a4aa828; WORD $0xb4ecd5f0  // .quad -5409713825168840664
	WORD $0x360b1af4; WORD $0x1e38aeb6  // .quad 2177682517447613172
	WORD $0x20dd5232; WORD $0xe2280b6c  // .quad -2150456263033662926
	WORD $0xc38de1b1; WORD $0x25c6da63  // .quad 2722103146809516465
	WORD $0x948a535f; WORD $0x8d590723  // .quad -8261564192037121185
	WORD $0x5a38ad0f; WORD $0x579c487e  // .quad 6313000485183335695
	WORD $0x79ace837; WORD $0xb0af48ec  // .quad -5715269221619013577
	WORD $0xf0c6d852; WORD $0x2d835a9d  // .quad 3279564588051781714
	WORD $0x98182244; WORD $0xdcdb1b27  // .quad -2532400508596379068
	WORD $0x6cf88e66; WORD $0xf8e43145  // .quad -512230283362660762
	WORD $0xbf0f156b; WORD $0x8a08f0f8  // .quad -8500279345513818773
	WORD $0x641b5900; WORD $0x1b8e9ecb  // .quad 1985699082112030976
	WORD $0xeed2dac5; WORD $0xac8b2d36  // .quad -6013663163464885563
	WORD $0x3d222f40; WORD $0xe272467e  // .quad -2129562165787349184
	WORD $0xaa879177; WORD $0xd7adf884  // .quad -2905392935903719049
	WORD $0xcc6abb10; WORD $0x5b0ed81d  // .quad 6561419329620589328
	WORD $0xea94baea; WORD $0x86ccbb52  // .quad -8733399612580906262
	WORD $0x9fc2b4ea; WORD $0x98e94712  // .quad -7428327965055601430
	WORD $0xa539e9a5; WORD $0xa87fea27  // .quad -6305063497298744923
	WORD $0x47b36225; WORD $0x3f2398d7  // .quad 4549648098962661925
	WORD $0x8e88640e; WORD $0xd29fe4b1  // .quad -3269643353196043250
	WORD $0x19a03aae; WORD $0x8eec7f0d  // .quad -8147997931578836306
	WORD $0xf9153e89; WORD $0x83a3eeee  // .quad -8961056123388608887
	WORD $0x300424ad; WORD $0x1953cf68  // .quad 1825030320404309165
	WORD $0xb75a8e2b; WORD $0xa48ceaaa  // .quad -6589634135808373205
	WORD $0x3c052dd8; WORD $0x5fa8c342  // .quad 6892973918932774360
	WORD $0x653131b6; WORD $0xcdb02555  // .quad -3625356651333078602
	WORD $0xcb06794e; WORD $0x3792f412  // .quad 4004531380238580046
	WORD $0x5f3ebf11; WORD $0x808e1755  // .quad -9183376934724255983
	WORD $0xbee40bd1; WORD $0xe2bbd88b  // .quad -2108853905778275375
	WORD $0xb70e6ed6; WORD $0xa0b19d2a  // .quad -6867535149977932074
	WORD $0xae9d0ec5; WORD $0x5b6aceae  // .quad 6587304654631931589
	WORD $0x64d20a8b; WORD $0xc8de0475  // .quad -3972732919045027189
	WORD $0x5a445276; WORD $0xf245825a  // .quad -989241218564861322
	WORD $0xbe068d2e; WORD $0xfb158592  // .quad -354230130378896082
	WORD $0xf0d56713; WORD $0xeed6e2f0  // .quad -1236551523206076653
	WORD $0xb6c4183d; WORD $0x9ced737b  // .quad -7138922859127891907
	WORD $0x9685606c; WORD $0x55464dd6  // .quad 6144684325637283948
	WORD $0xa4751e4c; WORD $0xc428d05a  // .quad -4311967555482476980
	WORD $0x3c26b887; WORD $0xaa97e14c  // .quad -6154202648235558777
	WORD $0x4d9265df; WORD $0xf5330471  // .quad -778273425925708321
	WORD $0x4b3066a9; WORD $0xd53dd99f  // .quad -3081067291867060567
	WORD $0xd07b7fab; WORD $0x993fe2c6  // .quad -7403949918844649557
	WORD $0x8efe402a; WORD $0xe546a803  // .quad -1925667057416912854
	WORD $0x849a5f96; WORD $0xbf8fdb78  // .quad -4643251380128424042
	WORD $0x72bdd034; WORD $0xde985204  // .quad -2407083821771141068
	WORD $0xa5c0f77c; WORD $0xef73d256  // .quad -1192378206733142148
	WORD $0x8f6d4441; WORD $0x963e6685  // .quad -7620540795641314239
	WORD $0x27989aad; WORD $0x95a86376  // .quad -7662765406849295699
	WORD $0x79a44aa9; WORD $0xdde70013  // .quad -2456994988062127447
	WORD $0xb17ec159; WORD $0xbb127c53  // .quad -4966770740134231719
	WORD $0x580d5d53; WORD $0x5560c018  // .quad 6152128301777116499
	WORD $0x9dde71af; WORD $0xe9d71b68  // .quad -1596777406740401745
	WORD $0x6e10b4a7; WORD $0xaab8f01e  // .quad -6144897678060768089
	WORD $0x62ab070d; WORD $0x92267121  // .quad -7915514906853832947
	WORD $0x04ca70e9; WORD $0xcab39613  // .quad -3840561048787980055
	WORD $0xbb55c8d1; WORD $0xb6b00d69  // .quad -5282707615139903279
	WORD $0xc5fd0d23; WORD $0x3d607b97  // .quad 4422670725869800739
	WORD $0x2a2b3b05; WORD $0xe45c10c4  // .quad -1991698500497491195
	WORD $0xb77c506b; WORD $0x8cb89a7d  // .quad -8306719647944912789
	WORD $0x9a5b04e3; WORD $0x8eb98a7a  // .quad -8162340590452013853
	WORD $0x92adb243; WORD $0x77f3608e  // .quad 8643358275316593219
	WORD $0x40f1c61c; WORD $0xb267ed19  // .quad -5591239719637629412
	WORD $0x37591ed4; WORD $0x55f038b2  // .quad 6192511825718353620
	WORD $0x912e37a3; WORD $0xdf01e85f  // .quad -2377363631119648861
	WORD $0xc52f6689; WORD $0x6b6c46de  // .quad 7740639782147942025
	WORD $0xbabce2c6; WORD $0x8b61313b  // .quad -8403381297090862394
	WORD $0x3b3da016; WORD $0x2323ac4b  // .quad 2532056854628769814
	WORD $0xa96c1b77; WORD $0xae397d8a  // .quad -5892540602936190089
	WORD $0x0a0d081b; WORD $0xabec975e  // .quad -6058300968568813541
	WORD $0x53c72255; WORD $0xd9c7dced  // .quad -2753989735242849707
	WORD $0x8c904a22; WORD $0x96e7bd35  // .quad -7572876210711016926
	WORD $0x545c7575; WORD $0x881cea14  // .quad -8638772612167862923
	WORD $0x77da2e55; WORD $0x7e50d641  // .quad 9102010423587778133
	WORD $0x697392d2; WORD $0xaa242499  // .quad -6186779746782440750
	WORD $0xd5d0b9ea; WORD $0xdde50bd1  // .quad -2457545025797441046
	WORD $0xc3d07787; WORD $0xd4ad2dbf  // .quad -3121788665050663033
	WORD $0x4b44e865; WORD $0x955e4ec6  // .quad -7683617300674189211
	WORD $0xda624ab4; WORD $0x84ec3c97  // .quad -8868646943297746252
	WORD $0xef0b113f; WORD $0xbd5af13b  // .quad -4802260812921368257
	WORD $0xd0fadd61; WORD $0xa6274bbd  // .quad -6474122660694794911
	WORD $0xeacdd58f; WORD $0xecb1ad8a  // .quad -1391139997724322417
	WORD $0x453994ba; WORD $0xcfb11ead  // .quad -3480967307441105734
	WORD $0xa5814af3; WORD $0x67de18ed  // .quad 7484447039699372787
	WORD $0x4b43fcf4; WORD $0x81ceb32c  // .quad -9093133594791772940
	WORD $0x8770ced8; WORD $0x80eacf94  // .quad -9157278655470055720
	WORD $0x5e14fc31; WORD $0xa2425ff7  // .quad -6754730975062328271
	WORD $0xa94d028e; WORD $0xa1258379  // .quad -6834912300910181746
	WORD $0x359a3b3e; WORD $0xcad2f7f5  // .quad -3831727700400522434
	WORD $0x13a04331; WORD $0x096ee458  // .quad 679731660717048625
	WORD $0x8300ca0d; WORD $0xfd87b5f2  // .quad -177973607073265139
	WORD $0x188853fd; WORD $0x8bca9d6e  // .quad -8373707460958465027
	WORD $0x91e07e48; WORD $0x9e74d1b7  // .quad -7028762532061872568
	WORD $0xcf55347e; WORD $0x775ea264  // .quad 8601490892183123070
	WORD $0x76589dda; WORD $0xc6120625  // .quad -4174267146649952806
	WORD $0x032a819e; WORD $0x95364afe  // .quad -7694880458480647778
	WORD $0xd3eec551; WORD $0xf79687ae  // .quad -606147914885053103
	WORD $0x83f52205; WORD $0x3a83ddbd  // .quad 4216457482181353989
	WORD $0x44753b52; WORD $0x9abe14cd  // .quad -7296371474444240046
	WORD $0x72793543; WORD $0xc4926a96  // .quad -4282243101277735613
	WORD $0x95928a27; WORD $0xc16d9a00  // .quad -4508778324627912153
	WORD $0x0f178294; WORD $0x75b7053c  // .quad 8482254178684994196
	WORD $0xbaf72cb1; WORD $0xf1c90080  // .quad -1024286887357502287
	WORD $0x12dd6339; WORD $0x5324c68b  // .quad 5991131704928854841
	WORD $0x74da7bee; WORD $0x971da050  // .quad -7557708332239520786
	WORD $0xebca5e04; WORD $0xd3f6fc16  // .quad -3173071712060547580
	WORD $0x92111aea; WORD $0xbce50864  // .quad -4835449396872013078
	WORD $0xa6bcf585; WORD $0x88f4bb1c  // .quad -8578025658503072379
	WORD $0xb69561a5; WORD $0xec1e4a7d  // .quad -1432625727662628443
	WORD $0xd06c32e6; WORD $0x2b31e9e3  // .quad 3112525982153323238
	WORD $0x921d5d07; WORD $0x9392ee8e  // .quad -7812920107430224633
	WORD $0x62439fd0; WORD $0x3aff322e  // .quad 4251171748059520976
	WORD $0x36a4b449; WORD $0xb877aa32  // .quad -5154464115860392887
	WORD $0xfad487c3; WORD $0x09befeb9  // .quad 702278666647013315
	WORD $0xc44de15b; WORD $0xe69594be  // .quad -1831394126398103205
	WORD $0x7989a9b4; WORD $0x4c2ebe68  // .quad 5489534351736154548
	WORD $0x3ab0acd9; WORD $0x901d7cf7  // .quad -8062150356639896359
	WORD $0x4bf60a11; WORD $0x0f9d3701  // .quad 1125115960621402641
	WORD $0x095cd80f; WORD $0xb424dc35  // .quad -5466001927372482545
	WORD $0x9ef38c95; WORD $0x538484c1  // .quad 6018080969204141205
	WORD $0x4bb40e13; WORD $0xe12e1342  // .quad -2220816390788215277
	WORD $0x06b06fba; WORD $0x2865a5f2  // .quad 2910915193077788602
	WORD $0x6f5088cb; WORD $0x8cbccc09  // .quad -8305539271883716405
	WORD $0x442e45d4; WORD $0xf93f87b7  // .quad -486521013540076076
	WORD $0xcb24aafe; WORD $0xafebff0b  // .quad -5770238071427257602
	WORD $0x1539d749; WORD $0xf78f69a5  // .quad -608151266925095095
	WORD $0xbdedd5be; WORD $0xdbe6fece  // .quad -2601111570856684098
	WORD $0x5a884d1c; WORD $0xb573440e  // .quad -5371875102083756772
	WORD $0x36b4a597; WORD $0x89705f41  // .quad -8543223759426509417
	WORD $0xf8953031; WORD $0x31680a88  // .quad 3560107088838733873
	WORD $0x8461cefc; WORD $0xabcc7711  // .quad -6067343680855748868
	WORD $0x36ba7c3e; WORD $0xfdc20d2b  // .quad -161552157378970562
	WORD $0xe57a42bc; WORD $0xd6bf94d5  // .quad -2972493582642298180
	WORD $0x04691b4d; WORD $0x3d329076  // .quad 4409745821703674701
	WORD $0xaf6c69b5; WORD $0x8637bd05  // .quad -8775337516792518219
	WORD $0xc2c1b110; WORD $0xa63f9a49  // .quad -6467280898289979120
	WORD $0x1b478423; WORD $0xa7c5ac47  // .quad -6357485877563259869
	WORD $0x33721d54; WORD $0x0fcf80dc  // .quad 1139270913992301908
	WORD $0xe219652b; WORD $0xd1b71758  // .quad -3335171328526686933
	WORD $0x404ea4a9; WORD $0xd3c36113  // .quad -3187597375937010519
	WORD $0x8d4fdf3b; WORD $0x83126e97  // .quad -9002011107970261189
	WORD $0x083126ea; WORD $0x645a1cac  // .quad 7231123676894144234
	WORD $0x70a3d70a; WORD $0xa3d70a3d  // .quad -6640827866535438582
	WORD $0x0a3d70a4; WORD $0x3d70a3d7  // .quad 4427218577690292388
	WORD $0xcccccccc; WORD $0xcccccccc  // .quad -3689348814741910324
	WORD $0xcccccccd; WORD $0xcccccccc  // .quad -3689348814741910323
	WORD $0x00000000; WORD $0x80000000  // .quad -9223372036854775808
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xa0000000  // .quad -6917529027641081856
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xc8000000  // .quad -4035225266123964416
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xfa000000  // .quad -432345564227567616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0x9c400000  // .quad -7187745005283311616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xc3500000  // .quad -4372995238176751616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xf4240000  // .quad -854558029293551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0x98968000  // .quad -7451627795949551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xbebc2000  // .quad -4702848726509551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xee6b2800  // .quad -1266874889709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0x9502f900  // .quad -7709325833709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xba43b740  // .quad -5024971273709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0xe8d4a510  // .quad -1669528073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x00000000; WORD $0x9184e72a  // .quad -7960984073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x80000000; WORD $0xb5e620f4  // .quad -5339544073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0xa0000000; WORD $0xe35fa931  // .quad -2062744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x04000000; WORD $0x8e1bc9bf  // .quad -8206744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0xc5000000; WORD $0xb1a2bc2e  // .quad -5646744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x76400000; WORD $0xde0b6b3a  // .quad -2446744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x89e80000; WORD $0x8ac72304  // .quad -8446744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0xac620000; WORD $0xad78ebc5  // .quad -5946744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x177a8000; WORD $0xd8d726b7  // .quad -2821744073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x6eac9000; WORD $0x87867832  // .quad -8681119073709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x0a57b400; WORD $0xa968163f  // .quad -6239712823709551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0xcceda100; WORD $0xd3c21bce  // .quad -3187955011209551616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x401484a0; WORD $0x84595161  // .quad -8910000909647051616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0x9019a5c8; WORD $0xa56fa5b9  // .quad -6525815118631426616
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0xf4200f3a; WORD $0xcecb8f27  // .quad -3545582879861895366
	WORD $0x00000000; WORD $0x00000000  // .quad 0
	WORD $0xf8940984; WORD $0x813f3978  // .quad -9133518327554766460
	WORD $0x00000000; WORD $0x40000000  // .quad 4611686018427387904
	WORD $0x36b90be5; WORD $0xa18f07d7  // .quad -6805211891016070171
	WORD $0x00000000; WORD $0x50000000  // .quad 5764607523034234880
	WORD $0x04674ede; WORD $0xc9f2c9cd  // .quad -3894828845342699810
	WORD $0x00000000; WORD $0xa4000000  // .quad -6629298651489370112
	WORD $0x45812296; WORD $0xfc6f7c40  // .quad -256850038250986858
	WORD $0x00000000; WORD $0x4d000000  // .quad 5548434740920451072
	WORD $0x2b70b59d; WORD $0x9dc5ada8  // .quad -7078060301547948643
	WORD $0x00000000; WORD $0xf0200000  // .quad -1143914305352105984
	WORD $0x364ce305; WORD $0xc5371912  // .quad -4235889358507547899
	WORD $0x00000000; WORD $0x6c280000  // .quad 7793479155164643328
	WORD $0xc3e01bc6; WORD $0xf684df56  // .quad -683175679707046970
	WORD $0x00000000; WORD $0xc7320000  // .quad -4093209111326359552
	WORD $0x3a6c115c; WORD $0x9a130b96  // .quad -7344513827457986212
	WORD $0x00000000; WORD $0x3c7f4000  // .quad 4359273333062107136
	WORD $0xc90715b3; WORD $0xc097ce7b  // .quad -4568956265895094861
	WORD $0x00000000; WORD $0x4b9f1000  // .quad 5449091666327633920
	WORD $0xbb48db20; WORD $0xf0bdc21a  // .quad -1099509313941480672
	WORD $0x00000000; WORD $0x1e86d400  // .quad 2199678564482154496
	WORD $0xb50d88f4; WORD $0x96769950  // .quad -7604722348854507276
	WORD $0x00000000; WORD $0x13144480  // .quad 1374799102801346560
	WORD $0xe250eb31; WORD $0xbc143fa4  // .quad -4894216917640746191
	WORD $0x00000000; WORD $0x17d955a0  // .quad 1718498878501683200
	WORD $0x1ae525fd; WORD $0xeb194f8e  // .quad -1506085128623544835
	WORD $0x00000000; WORD $0x5dcfab08  // .quad 6759809616554491904
	WORD $0xd0cf37be; WORD $0x92efd1b8  // .quad -7858832233030797378
	WORD $0x00000000; WORD $0x5aa1cae5  // .quad 6530724019560251392
	WORD $0x050305ad; WORD $0xb7abc627  // .quad -5211854272861108819
	WORD $0x40000000; WORD $0xf14a3d9e  // .quad -1059967012404461568
	WORD $0xc643c719; WORD $0xe596b7b0  // .quad -1903131822648998119
	WORD $0xd0000000; WORD $0x6d9ccd05  // .quad 7898413271349198848
	WORD $0x7bea5c6f; WORD $0x8f7e32ce  // .quad -8106986416796705681
	WORD $0xa2000000; WORD $0xe4820023  // .quad -1981020733047832576
	WORD $0x1ae4f38b; WORD $0xb35dbf82  // .quad -5522047002568494197
	WORD $0x8a800000; WORD $0xdda2802c  // .quad -2476275916309790720
	WORD $0xa19e306e; WORD $0xe0352f62  // .quad -2290872734783229842
	WORD $0xad200000; WORD $0xd50b2037  // .quad -3095344895387238400
	WORD $0xa502de45; WORD $0x8c213d9d  // .quad -8349324486880600507
	WORD $0xcc340000; WORD $0x4526f422  // .quad 4982938468024057856
	WORD $0x0e4395d6; WORD $0xaf298d05  // .quad -5824969590173362730
	WORD $0x7f410000; WORD $0x9670b12b  // .quad -7606384970252091392
	WORD $0x51d47b4c; WORD $0xdaf3f046  // .quad -2669525969289315508
	WORD $0x5f114000; WORD $0x3c0cdd76  // .quad 4327076842467049472
	WORD $0xf324cd0f; WORD $0x88d8762b  // .quad -8585982758446904049
	WORD $0xfb6ac800; WORD $0xa5880a69  // .quad -6518949010312869888
	WORD $0xefee0053; WORD $0xab0e93b6  // .quad -6120792429631242157
	WORD $0x7a457a00; WORD $0x8eea0d04  // .quad -8148686262891087360
	WORD $0xabe98068; WORD $0xd5d238a4  // .quad -3039304518611664792
	WORD $0x98d6d880; WORD $0x72a49045  // .quad 8260886245095692416
	WORD $0xeb71f041; WORD $0x85a36366  // .quad -8817094351773372351
	WORD $0x7f864750; WORD $0x47a6da2b  // .quad 5163053903184807760
	WORD $0xa64e6c51; WORD $0xa70c3c40  // .quad -6409681921289327535
	WORD $0x5f67d924; WORD $0x999090b6  // .quad -7381240676301154012
	WORD $0xcfe20765; WORD $0xd0cf4b50  // .quad -3400416383184271515
	WORD $0xf741cf6d; WORD $0xfff4b4e3  // .quad -3178808521666707
	WORD $0x81ed449f; WORD $0x82818f12  // .quad -9042789267131251553
	WORD $0x7a8921a5; WORD $0xbff8f10e  // .quad -4613672773753429595
	WORD $0x226895c7; WORD $0xa321f2d7  // .quad -6691800565486676537
	WORD $0x192b6a0e; WORD $0xaff72d52  // .quad -5767090967191786994
	WORD $0xeb02bb39; WORD $0xcbea6f8c  // .quad -3753064688430957767
	WORD $0x9f764491; WORD $0x9bf4f8a6  // .quad -7208863708989733743
	WORD $0x25c36a08; WORD $0xfee50b70  // .quad -79644842111309304
	WORD $0x4753d5b5; WORD $0x02f236d0  // .quad 212292400617608629
	WORD $0x179a2245; WORD $0x9f4f2726  // .quad -6967307053960650171
	WORD $0x2c946591; WORD $0x01d76242  // .quad 132682750386005393
	WORD $0x9d80aad6; WORD $0xc722f0ef  // .quad -4097447799023424810
	WORD $0xb7b97ef6; WORD $0x424d3ad2  // .quad 4777539456409894646
	WORD $0x84e0d58b; WORD $0xf8ebad2b  // .quad -510123730351893109
	WORD $0x65a7deb3; WORD $0xd2e08987  // .quad -3251447716342407501
	WORD $0x330c8577; WORD $0x9b934c3b  // .quad -7236356359111015049
	WORD $0x9f88eb30; WORD $0x63cc55f4  // .quad 7191217214140771120
	WORD $0xffcfa6d5; WORD $0xc2781f49  // .quad -4433759430461380907
	WORD $0xc76b25fc; WORD $0x3cbf6b71  // .quad 4377335499248575996
	WORD $0x7fc3908a; WORD $0xf316271c  // .quad -930513269649338230
	WORD $0x3945ef7b; WORD $0x8bef464e  // .quad -8363388681221443717
	WORD $0xcfda3a56; WORD $0x97edd871  // .quad -7499099821171918250
	WORD $0xe3cbb5ad; WORD $0x97758bf0  // .quad -7532960934977096275
	WORD $0x43d0c8ec; WORD $0xbde94e8e  // .quad -4762188758037509908
	WORD $0x1cbea318; WORD $0x3d52eeed  // .quad 4418856886560793368
	WORD $0xd4c4fb27; WORD $0xed63a231  // .quad -1341049929119499481
	WORD $0x63ee4bde; WORD $0x4ca7aaa8  // .quad 5523571108200991710
	WORD $0x24fb1cf8; WORD $0x945e455f  // .quad -7755685233340769032
	WORD $0x3e74ef6b; WORD $0x8fe8caa9  // .quad -8076983103442849941
	WORD $0xee39e436; WORD $0xb975d6b6  // .quad -5082920523248573386
	WORD $0x8e122b45; WORD $0xb3e2fd53  // .quad -5484542860876174523
	WORD $0xa9c85d44; WORD $0xe7d34c64  // .quad -1741964635633328828
	WORD $0x7196b617; WORD $0x60dbbca8  // .quad 6979379479186945559
	WORD $0xea1d3a4a; WORD $0x90e40fbe  // .quad -8006256924911912374
	WORD $0x46fe31ce; WORD $0xbc8955e9  // .quad -4861259862362934834
	WORD $0xa4a488dd; WORD $0xb51d13ae  // .quad -5396135137712502563
	WORD $0x98bdbe42; WORD $0x6babab63  // .quad 7758483227328495170
	WORD $0x4dcdab14; WORD $0xe264589a  // .quad -2133482903713240300
	WORD $0x7eed2dd2; WORD $0xc696963c  // .quad -4136954021121544750
	WORD $0x70a08aec; WORD $0x8d7eb760  // .quad -8250955842461857044
	WORD $0xcf543ca3; WORD $0xfc1e1de5  // .quad -279753253987271517
	WORD $0x8cc8ada8; WORD $0xb0de6538  // .quad -5702008784649933400
	WORD $0x43294bcc; WORD $0x3b25a55f  // .quad 4261994450943298508
	WORD $0xaffad912; WORD $0xdd15fe86  // .quad -2515824962385028846
	WORD $0x13f39ebf; WORD $0x49ef0eb7  // .quad 5327493063679123135
	WORD $0x2dfcc7ab; WORD $0x8a2dbf14  // .quad -8489919629131724885
	WORD $0x6c784338; WORD $0x6e356932  // .quad 7941369183226839864
	WORD $0x397bf996; WORD $0xacb92ed9  // .quad -6000713517987268202
	WORD $0x07965405; WORD $0x49c2c37f  // .quad 5315025460606161925
	WORD $0x87daf7fb; WORD $0xd7e77a8f  // .quad -2889205879056697349
	WORD $0xc97be907; WORD $0xdc33745e  // .quad -2579590211097073401
	WORD $0xb4e8dafd; WORD $0x86f0ac99  // .quad -8723282702051517699
	WORD $0x3ded71a4; WORD $0x69a028bb  // .quad 7611128154919104932
	WORD $0x222311bc; WORD $0xa8acd7c0  // .quad -6292417359137009220
	WORD $0x0d68ce0d; WORD $0xc40832ea  // .quad -4321147861633282547
	WORD $0x2aabd62b; WORD $0xd2d80db0  // .quad -3253835680493873621
	WORD $0x90c30191; WORD $0xf50a3fa4  // .quad -789748808614215279
	WORD $0x1aab65db; WORD $0x83c7088e  // .quad -8951176327949752869
	WORD $0xda79e0fb; WORD $0x792667c6  // .quad 8729779031470891259
	WORD $0xa1563f52; WORD $0xa4b8cab1  // .quad -6577284391509803182
	WORD $0x91185939; WORD $0x577001b8  // .quad 6300537770911226169
	WORD $0x09abcf26; WORD $0xcde6fd5e  // .quad -3609919470959866074
	WORD $0xb55e6f87; WORD $0xed4c0226  // .quad -1347699823215743097
	WORD $0xc60b6178; WORD $0x80b05e5a  // .quad -9173728696990998152
	WORD $0x315b05b5; WORD $0x544f8158  // .quad 6075216638131242421
	WORD $0x778e39d6; WORD $0xa0dc75f1  // .quad -6855474852811359786
	WORD $0x3db1c722; WORD $0x696361ae  // .quad 7594020797664053026
	WORD $0xd571c84c; WORD $0xc913936d  // .quad -3957657547586811828
	WORD $0xcd1e38ea; WORD $0x03bc3a19  // .quad 269153960225290474
	WORD $0x4ace3a5f; WORD $0xfb587849  // .quad -335385916056126881
	WORD $0x4065c724; WORD $0x04ab48a0  // .quad 336442450281613092
	WORD $0xcec0e47b; WORD $0x9d174b2d  // .quad -7127145225176161157
	WORD $0x283f9c77; WORD $0x62eb0d64  // .quad 7127805559067090039
	WORD $0x42711d9a; WORD $0xc45d1df9  // .quad -4297245513042813542
	WORD $0x324f8395; WORD $0x3ba5d0bd  // .quad 4298070930406474645
	WORD $0x930d6500; WORD $0xf5746577  // .quad -759870872876129024
	WORD $0x7ee3647a; WORD $0xca8f44ec  // .quad -3850783373846682502
	WORD $0xbbe85f20; WORD $0x9968bf6a  // .quad -7392448323188662496
	WORD $0xcf4e1ecc; WORD $0x7e998b13  // .quad 9122475437414293196
	WORD $0x6ae276e8; WORD $0xbfc2ef45  // .quad -4628874385558440216
	WORD $0xc321a67f; WORD $0x9e3fedd8  // .quad -7043649776941685121
	WORD $0xc59b14a2; WORD $0xefb3ab16  // .quad -1174406963520662366
	WORD $0xf3ea101f; WORD $0xc5cfe94e  // .quad -4192876202749718497
	WORD $0x3b80ece5; WORD $0x95d04aee  // .quad -7651533379841495835
	WORD $0x58724a13; WORD $0xbba1f1d1  // .quad -4926390635932268013
	WORD $0xca61281f; WORD $0xbb445da9  // .quad -4952730706374481889
	WORD $0xae8edc98; WORD $0x2a8a6e45  // .quad 3065383741939440792
	WORD $0x3cf97226; WORD $0xea157514  // .quad -1579227364540714458
	WORD $0x1a3293be; WORD $0xf52d09d7  // .quad -779956341003086914
	WORD $0xa61be758; WORD $0x924d692c  // .quad -7904546130479028392
	WORD $0x705f9c57; WORD $0x593c2626  // .quad 6430056314514152535
	WORD $0xcfa2e12e; WORD $0xb6e0c377  // .quad -5268996644671397586
	WORD $0x0c77836d; WORD $0x6f8b2fb0  // .quad 8037570393142690669
	WORD $0xc38b997a; WORD $0xe498f455  // .quad -1974559787411859078
	WORD $0x0f956448; WORD $0x0b6dfb9c  // .quad 823590954573587528
	WORD $0x9a373fec; WORD $0x8edf98b5  // .quad -8151628894773493780
	WORD $0x89bd5ead; WORD $0x4724bd41  // .quad 5126430365035880109
	WORD $0x00c50fe7; WORD $0xb2977ee3  // .quad -5577850100039479321
	WORD $0xec2cb658; WORD $0x58edec91  // .quad 6408037956294850136
	WORD $0xc0f653e1; WORD $0xdf3d5e9b  // .quad -2360626606621961247
	WORD $0x6737e3ee; WORD $0x2f2967b6  // .quad 3398361426941174766
	WORD $0x5899f46c; WORD $0x8b865b21  // .quad -8392920656779807636
	WORD $0x0082ee75; WORD $0xbd79e0d2  // .quad -4793553135802847627
	WORD $0xaec07187; WORD $0xae67f1e9  // .quad -5879464802547371641
	WORD $0x80a3aa12; WORD $0xecd85906  // .quad -1380255401326171630
	WORD $0x1a708de9; WORD $0xda01ee64  // .quad -2737644984756826647
	WORD $0x20cc9496; WORD $0xe80e6f48  // .quad -1725319251657714538
	WORD $0x908658b2; WORD $0x884134fe  // .quad -8628557143114098510
	WORD $0x147fdcde; WORD $0x3109058d  // .quad 3533361486141316318
	WORD $0x34a7eede; WORD $0xaa51823e  // .quad -6174010410465235234
	WORD $0x599fd416; WORD $0xbd4b46f0  // .quad -4806670179178130410
	WORD $0xc1d1ea96; WORD $0xd4e5e2cd  // .quad -3105826994654156138
	WORD $0x7007c91b; WORD $0x6c9e18ac  // .quad 7826720331309500699
	WORD $0x9923329e; WORD $0x850fadc0  // .quad -8858670899299929442
	WORD $0xc604ddb1; WORD $0x03e2cf6b  // .quad 280014188641050033
	WORD $0xbf6bff45; WORD $0xa6539930  // .quad -6461652605697523899
	WORD $0xb786151d; WORD $0x84db8346  // .quad -8873354301053463267
	WORD $0xef46ff16; WORD $0xcfe87f7c  // .quad -3465379738694516970
	WORD $0x65679a64; WORD $0xe6126418  // .quad -1868320839462053276
	WORD $0x158c5f6e; WORD $0x81f14fae  // .quad -9083391364325154962
	WORD $0x3f60c07f; WORD $0x4fcb7e8f  // .quad 5749828502977298559
	WORD $0x9aef7749; WORD $0xa26da399  // .quad -6742553186979055799
	WORD $0x0f38f09e; WORD $0xe3be5e33  // .quad -2036086408133152610
	WORD $0x01ab551c; WORD $0xcb090c80  // .quad -3816505465296431844
	WORD $0xd3072cc6; WORD $0x5cadf5bf  // .quad 6678264026688335046
	WORD $0x02162a63; WORD $0xfdcb4fa0  // .quad -158945813193151901
	WORD $0xc7c8f7f7; WORD $0x73d9732f  // .quad 8347830033360418807
	WORD $0x014dda7e; WORD $0x9e9f11c4  // .quad -7016870160886801794
	WORD $0xdcdd9afb; WORD $0x2867e7fd  // .quad 2911550761636567803
	WORD $0x01a1511d; WORD $0xc646d635  // .quad -4159401682681114339
	WORD $0x541501b9; WORD $0xb281e1fd  // .quad -5583933584809066055
	WORD $0x4209a565; WORD $0xf7d88bc2  // .quad -587566084924005019
	WORD $0xa91a4227; WORD $0x1f225a7c  // .quad 2243455055843443239
	WORD $0x6946075f; WORD $0x9ae75759  // .quad -7284757830718584993
	WORD $0xe9b06959; WORD $0x3375788d  // .quad 3708002419115845977
	WORD $0xc3978937; WORD $0xc1a12d2f  // .quad -4494261269970843337
	WORD $0x641c83af; WORD $0x0052d6b1  // .quad 23317005467419567
	WORD $0xb47d6b84; WORD $0xf209787b  // .quad -1006140569036166268
	WORD $0xbd23a49b; WORD $0xc0678c5d  // .quad -4582539761593113445
	WORD $0x50ce6332; WORD $0x9745eb4d  // .quad -7546366883288685774
	WORD $0x963646e1; WORD $0xf840b7ba  // .quad -558244341782001951
	WORD $0xa501fbff; WORD $0xbd176620  // .quad -4821272585683469313
	WORD $0x3bc3d899; WORD $0xb650e5a9  // .quad -5309491445654890343
	WORD $0xce427aff; WORD $0xec5d3fa8  // .quad -1414904713676948737
	WORD $0x8ab4cebf; WORD $0xa3e51f13  // .quad -6636864307068612929
	WORD $0x80e98cdf; WORD $0x93ba47c9  // .quad -7801844473689174817
	WORD $0x36b10138; WORD $0xc66f336c  // .quad -4148040191917883080
	WORD $0xe123f017; WORD $0xb8a8d9bb  // .quad -5140619573684080617
	WORD $0x445d4185; WORD $0xb80b0047  // .quad -5185050239897353851
	WORD $0xd96cec1d; WORD $0xe6d3102a  // .quad -1814088448677712867
	WORD $0x157491e6; WORD $0xa60dc059  // .quad -6481312799871692314
	WORD $0xc7e41392; WORD $0x9043ea1a  // .quad -8051334308064652398
	WORD $0xad68db30; WORD $0x87c89837  // .quad -8662506518347195600
	WORD $0x79dd1877; WORD $0xb454e4a1  // .quad -5452481866653427593
	WORD $0x98c311fc; WORD $0x29babe45  // .quad 3006924907348169212
	WORD $0xd8545e94; WORD $0xe16a1dc9  // .quad -2203916314889396588
	WORD $0xfef3d67b; WORD $0xf4296dd6  // .quad -853029884242176389
	WORD $0x2734bb1d; WORD $0x8ce2529e  // .quad -8294976724446954723
	WORD $0x5f58660d; WORD $0x1899e4a6  // .quad 1772699331562333709
	WORD $0xb101e9e4; WORD $0xb01ae745  // .quad -5757034887131305500
	WORD $0xf72e7f90; WORD $0x5ec05dcf  // .quad 6827560182880305040
	WORD $0x1d42645d; WORD $0xdc21a117  // .quad -2584607590486743971
	WORD $0xf4fa1f74; WORD $0x76707543  // .quad 8534450228600381300
	WORD $0x72497eba; WORD $0x899504ae  // .quad -8532908771695296838
	WORD $0x791c53a9; WORD $0x6a06494a  // .quad 7639874402088932265
	WORD $0x0edbde69; WORD $0xabfa45da  // .quad -6054449946191733143
	WORD $0x17636893; WORD $0x0487db9d  // .quad 326470965756389523
	WORD $0x9292d603; WORD $0xd6f8d750  // .quad -2956376414312278525
	WORD $0x5d3c42b7; WORD $0x45a9d284  // .quad 5019774725622874807
	WORD $0x5b9bc5c2; WORD $0x865b8692  // .quad -8765264286586255934
	WORD $0xba45a9b3; WORD $0x0b8a2392  // .quad 831516194300602803
	WORD $0xf282b732; WORD $0xa7f26836  // .quad -6344894339805432014
	WORD $0x68d7141f; WORD $0x8e6cac77  // .quad -8183976793979022305
	WORD $0xaf2364ff; WORD $0xd1ef0244  // .quad -3319431906329402113
	WORD $0x430cd927; WORD $0x3207d795  // .quad 3605087062808385831
	WORD $0xed761f1f; WORD $0x8335616a  // .quad -8992173969096958177
	WORD $0x49e807b9; WORD $0x7f44e6bd  // .quad 9170708441896323001
	WORD $0xa8d3a6e7; WORD $0xa402b9c5  // .quad -6628531442943809817
	WORD $0x9c6209a7; WORD $0x5f16206c  // .quad 6851699533943015847
	WORD $0x130890a1; WORD $0xcd036837  // .quad -3673978285252374367
	WORD $0xc37a8c10; WORD $0x36dba887  // .quad 3952938399001381904
	WORD $0x6be55a64; WORD $0x80222122  // .quad -9213765455923815836
	WORD $0xda2c978a; WORD $0xc2494954  // .quad -4446942528265218166
	WORD $0x06deb0fd; WORD $0xa02aa96b  // .quad -6905520801477381891
	WORD $0x10b7bd6d; WORD $0xf2db9baa  // .quad -946992141904134803
	WORD $0xc8965d3d; WORD $0xc83553c5  // .quad -4020214983419339459
	WORD $0x94e5acc8; WORD $0x6f928294  // .quad 8039631859474607304
	WORD $0x3abbf48c; WORD $0xfa42a8b7  // .quad -413582710846786420
	WORD $0xba1f17fa; WORD $0xcb772339  // .quad -3785518230938904582
	WORD $0x84b578d7; WORD $0x9c69a972  // .quad -7176018221920323369
	WORD $0x14536efc; WORD $0xff2a7604  // .quad -60105885123121412
	WORD $0x25e2d70d; WORD $0xc38413cf  // .quad -4358336758973016307
	WORD $0x19684abb; WORD $0xfef51385  // .quad -75132356403901765
	WORD $0xef5b8cd1; WORD $0xf46518c2  // .quad -836234930288882479
	WORD $0x5fc25d6a; WORD $0x7eb25866  // .quad 9129456591349898602
	WORD $0xd5993802; WORD $0x98bf2f79  // .quad -7440175859071633406
	WORD $0xfbd97a62; WORD $0xef2f773f  // .quad -1211618658047395230
	WORD $0x4aff8603; WORD $0xbeeefb58  // .quad -4688533805412153853
	WORD $0xfacfd8fb; WORD $0xaafb550f  // .quad -6126209340986631941
	WORD $0x5dbf6784; WORD $0xeeaaba2e  // .quad -1248981238337804412
	WORD $0xf983cf39; WORD $0x95ba2a53  // .quad -7657761676233289927
	WORD $0xfa97a0b2; WORD $0x952ab45c  // .quad -7698142301602209614
	WORD $0x7bf26184; WORD $0xdd945a74  // .quad -2480258038432112252
	WORD $0x393d88df; WORD $0xba756174  // .quad -5010991858575374113
	WORD $0x9aeef9e5; WORD $0x94f97111  // .quad -7712008566467528219
	WORD $0x478ceb17; WORD $0xe912b9d1  // .quad -1652053804791829737
	WORD $0x01aab85e; WORD $0x7a37cd56  // .quad 8806733365625141342
	WORD $0xccb812ee; WORD $0x91abb422  // .quad -7950062655635975442
	WORD $0xc10ab33b; WORD $0xac62e055  // .quad -6025006692552756421
	WORD $0x7fe617aa; WORD $0xb616a12b  // .quad -5325892301117581398
	WORD $0x314d600a; WORD $0x577b986b  // .quad 6303799689591218186
	WORD $0x5fdf9d94; WORD $0xe39c4976  // .quad -2045679357969588844
	WORD $0xfda0b80c; WORD $0xed5a7e85  // .quad -1343622424865753076
	WORD $0xfbebc27d; WORD $0x8e41ade9  // .quad -8196078626372074883
	WORD $0xbe847308; WORD $0x14588f13  // .quad 1466078993672598280
	WORD $0x7ae6b31c; WORD $0xb1d21964  // .quad -5633412264537705700
	WORD $0xae258fc9; WORD $0x596eb2d8  // .quad 6444284760518135753
	WORD $0x99a05fe3; WORD $0xde469fbd  // .quad -2430079312244744221
	WORD $0xd9aef3bc; WORD $0x6fca5f8e  // .quad 8055355950647669692
	WORD $0x80043bee; WORD $0x8aec23d6  // .quad -8436328597794046994
	WORD $0x480d5855; WORD $0x25de7bb9  // .quad 2728754459941099605
	WORD $0x20054ae9; WORD $0xada72ccc  // .quad -5933724728815170839
	WORD $0x9a10ae6b; WORD $0xaf561aa7  // .quad -5812428961928401301
	WORD $0x28069da4; WORD $0xd910f7ff  // .quad -2805469892591575644
	WORD $0x8094da05; WORD $0x1b2ba151  // .quad 1957835834444274181
	WORD $0x79042286; WORD $0x87aa9aff  // .quad -8670947710510816634
	WORD $0xf05d0843; WORD $0x90fb44d2  // .quad -7999724640327104445
	WORD $0x57452b28; WORD $0xa99541bf  // .quad -6226998619711132888
	WORD $0xac744a54; WORD $0x353a1607  // .quad 3835402254873283156
	WORD $0x2d1675f2; WORD $0xd3fa922f  // .quad -3172062256211528206
	WORD $0x97915ce9; WORD $0x42889b89  // .quad 4794252818591603945
	WORD $0x7c2e09b7; WORD $0x847c9b5d  // .quad -8900067937773286985
	WORD $0xfebada12; WORD $0x69956135  // .quad 7608094030047140370
	WORD $0xdb398c25; WORD $0xa59bc234  // .quad -6513398903789220827
	WORD $0x7e699096; WORD $0x43fab983  // .quad 4898431519131537558
	WORD $0x1207ef2e; WORD $0xcf02b2c2  // .quad -3530062611309138130
	WORD $0x5e03f4bc; WORD $0x94f967e4  // .quad -7712018656367741764
	WORD $0x4b44f57d; WORD $0x8161afb9  // .quad -9123818159709293187
	WORD $0xbac278f6; WORD $0x1d1be0ee  // .quad 2097517367411243254
	WORD $0x9e1632dc; WORD $0xa1ba1ba7  // .quad -6793086681209228580
	WORD $0x69731733; WORD $0x6462d92a  // .quad 7233582727691441971
	WORD $0x859bbf93; WORD $0xca28a291  // .quad -3879672333084147821
	WORD $0x03cfdcff; WORD $0x7d7b8f75  // .quad 9041978409614302463
	WORD $0xe702af78; WORD $0xfcb2cb35  // .quad -237904397927796872
	WORD $0x44c3d43f; WORD $0x5cda7352  // .quad 6690786993590490175
	WORD $0xb061adab; WORD $0x9defbf01  // .quad -7066219276345954901
	WORD $0x6afa64a8; WORD $0x3a088813  // .quad 4181741870994056360
	WORD $0x1c7a1916; WORD $0xc56baec2  // .quad -4221088077005055722
	WORD $0x45b8fdd1; WORD $0x088aaa18  // .quad 615491320315182545
	WORD $0xa3989f5b; WORD $0xf6c69a72  // .quad -664674077828931749
	WORD $0x57273d46; WORD $0x8aad549e  // .quad -8454007886460797626
	WORD $0xa63f6399; WORD $0x9a3c2087  // .quad -7332950326284164199
	WORD $0xf678864c; WORD $0x36ac54e2  // .quad 3939617107816777292
	WORD $0x8fcf3c7f; WORD $0xc0cb28a9  // .quad -4554501889427817345
	WORD $0xb416a7de; WORD $0x84576a1b  // .quad -8910536670511192098
	WORD $0xf3c30b9f; WORD $0xf0fdf2d3  // .quad -1081441343357383777
	WORD $0xa11c51d6; WORD $0x656d44a2  // .quad 7308573235570561494
	WORD $0x7859e743; WORD $0x969eb7c4  // .quad -7593429867239446717
	WORD $0xa4b1b326; WORD $0x9f644ae5  // .quad -6961356773836868826
	WORD $0x96706114; WORD $0xbc4665b5  // .quad -4880101315621920492
	WORD $0x0dde1fef; WORD $0x873d5d9f  // .quad -8701695967296086033
	WORD $0xfc0c7959; WORD $0xeb57ff22  // .quad -1488440626100012711
	WORD $0xd155a7eb; WORD $0xa90cb506  // .quad -6265433940692719637
	WORD $0xdd87cbd8; WORD $0x9316ff75  // .quad -7847804418953589800
	WORD $0x42d588f3; WORD $0x09a7f124  // .quad 695789805494438131
	WORD $0x54e9bece; WORD $0xb7dcbf53  // .quad -5198069505264599346
	WORD $0x538aeb30; WORD $0x0c11ed6d  // .quad 869737256868047664
	WORD $0x2a242e81; WORD $0xe5d3ef28  // .quad -1885900863153361279
	WORD $0xa86da5fb; WORD $0x8f1668c8  // .quad -8136200465769716229
	WORD $0x1a569d10; WORD $0x8fa47579  // .quad -8096217067111932656
	WORD $0x694487bd; WORD $0xf96e017d  // .quad -473439272678684739
	WORD $0x60ec4455; WORD $0xb38d92d7  // .quad -5508585315462527915
	WORD $0xc395a9ad; WORD $0x37c981dc  // .quad 4019886927579031981
	WORD $0x3927556a; WORD $0xe070f78d  // .quad -2274045625900771990
	WORD $0xf47b1418; WORD $0x85bbe253  // .quad -8810199395808373736
	WORD $0x43b89562; WORD $0x8c469ab8  // .quad -8338807543829064350
	WORD $0x78ccec8f; WORD $0x93956d74  // .quad -7812217631593927537
	WORD $0x54a6babb; WORD $0xaf584166  // .quad -5811823411358942533
	WORD $0x970027b3; WORD $0x387ac8d1  // .quad 4069786015789754291
	WORD $0xe9d0696a; WORD $0xdb2e51bf  // .quad -2653093245771290262
	WORD $0xfcc0319f; WORD $0x06997b05  // .quad 475546501309804959
	WORD $0xf22241e2; WORD $0x88fcf317  // .quad -8575712306248138270
	WORD $0xbdf81f04; WORD $0x441fece3  // .quad 4908902581746016004
	WORD $0xeeaad25a; WORD $0xab3c2fdd  // .quad -6107954364382784934
	WORD $0xad7626c4; WORD $0xd527e81c  // .quad -3087243809672255804
	WORD $0x6a5586f1; WORD $0xd60b3bd5  // .quad -3023256937051093263
	WORD $0xd8d3b075; WORD $0x8a71e223  // .quad -8470740780517707659
	WORD $0x62757456; WORD $0x85c70565  // .quad -8807064613298015146
	WORD $0x67844e4a; WORD $0xf6872d56  // .quad -682526969396179382
	WORD $0xbb12d16c; WORD $0xa738c6be  // .quad -6397144748195131028
	WORD $0x016561dc; WORD $0xb428f8ac  // .quad -5464844730172612132
	WORD $0x69d785c7; WORD $0xd106f86e  // .quad -3384744916816525881
	WORD $0x01beba53; WORD $0xe13336d7  // .quad -2219369894288377261
	WORD $0x0226b39c; WORD $0x82a45b45  // .quad -9032994600651410532
	WORD $0x61173474; WORD $0xecc00246  // .quad -1387106183930235788
	WORD $0x42b06084; WORD $0xa34d7216  // .quad -6679557232386875260
	WORD $0xf95d0191; WORD $0x27f002d7  // .quad 2877803288514593169
	WORD $0xd35c78a5; WORD $0xcc20ce9b  // .quad -3737760522056206171
	WORD $0xf7b441f5; WORD $0x31ec038d  // .quad 3597254110643241461
	WORD $0xc83396ce; WORD $0xff290242  // .quad -60514634142869810
	WORD $0x75a15272; WORD $0x7e670471  // .quad 9108253656731439730
	WORD $0xbd203e41; WORD $0x9f79a169  // .quad -6955350673980375487
	WORD $0xe984d387; WORD $0x0f0062c6  // .quad 1080972517029761927
	WORD $0x2c684dd1; WORD $0xc75809c4  // .quad -4082502324048081455
	WORD $0xa3e60869; WORD $0x52c07b78  // .quad 5962901664714590313
	WORD $0x37826145; WORD $0xf92e0c35  // .quad -491441886632713915
	WORD $0xccdf8a83; WORD $0xa7709a56  // .quad -6381430974388925821
	WORD $0x42b17ccb; WORD $0x9bbcc7a1  // .quad -7224680206786528053
	WORD $0x400bb692; WORD $0x88a66076  // .quad -8600080377420466542
	WORD $0x935ddbfe; WORD $0xc2abf989  // .quad -4419164240055772162
	WORD $0xd00ea436; WORD $0x6acff893  // .quad 7696643601933968438
	WORD $0xf83552fe; WORD $0xf356f7eb  // .quad -912269281642327298
	WORD $0xc4124d44; WORD $0x0583f6b8  // .quad 397432465562684740
	WORD $0x7b2153de; WORD $0x98165af3  // .quad -7487697328667536418
	WORD $0x7a8b704b; WORD $0xc3727a33  // .quad -4363290727450709941
	WORD $0x59e9a8d6; WORD $0xbe1bf1b0  // .quad -4747935642407032618
	WORD $0x592e4c5d; WORD $0x744f18c0  // .quad 8380944645968776285
	WORD $0x7064130c; WORD $0xeda2ee1c  // .quad -1323233534581402868
	WORD $0x6f79df74; WORD $0x1162def0  // .quad 1252808770606194548
	WORD $0xc63e8be7; WORD $0x9485d4d1  // .quad -7744549986754458649
	WORD $0x45ac2ba9; WORD $0x8addcb56  // .quad -8440366555225904215
	WORD $0x37ce2ee1; WORD $0xb9a74a06  // .quad -5069001465015685407
	WORD $0xd7173693; WORD $0x6d953e2b  // .quad 7896285879677171347
	WORD $0xc5c1ba99; WORD $0xe8111c87  // .quad -1724565812842218855
	WORD $0xccdd0438; WORD $0xc8fa8db6  // .quad -3964700705685699528
	WORD $0xdb9914a0; WORD $0x910ab1d4  // .quad -7995382660667468640
	WORD $0x400a22a3; WORD $0x1d9c9892  // .quad 2133748077373825699
	WORD $0x127f59c8; WORD $0xb54d5e4a  // .quad -5382542307406947896
	WORD $0xd00cab4c; WORD $0x2503beb6  // .quad 2667185096717282124
	WORD $0x971f303a; WORD $0xe2a0b5dc  // .quad -2116491865831296966
	WORD $0x840fd61e; WORD $0x2e44ae64  // .quad 3333981370896602654
	WORD $0xde737e24; WORD $0x8da471a9  // .quad -8240336443785642460
	WORD $0xd289e5d3; WORD $0x5ceaecfe  // .quad 6695424375237764563
	WORD $0x56105dad; WORD $0xb10d8e14  // .quad -5688734536304665171
	WORD $0x872c5f48; WORD $0x7425a83e  // .quad 8369280469047205704
	WORD $0x6b947518; WORD $0xdd50f199  // .quad -2499232151953443560
	WORD $0x28f7771a; WORD $0xd12f124e  // .quad -3373457468973156582
	WORD $0xe33cc92f; WORD $0x8a5296ff  // .quad -8479549122611984081
	WORD $0xd99aaa70; WORD $0x82bd6b70  // .quad -9025939945749304720
	WORD $0xdc0bfb7b; WORD $0xace73cbf  // .quad -5987750384837592197
	WORD $0x1001550c; WORD $0x636cc64d  // .quad 7164319141522920716
	WORD $0xd30efa5a; WORD $0xd8210bef  // .quad -2873001962619602342
	WORD $0x5401aa4f; WORD $0x3c47f7e0  // .quad 4343712908476262991
	WORD $0xe3e95c78; WORD $0x8714a775  // .quad -8713155254278333320
	WORD $0x34810a72; WORD $0x65acfaec  // .quad 7326506586225052274
	WORD $0x5ce3b396; WORD $0xa8d9d153  // .quad -6279758049420528746
	WORD $0x41a14d0e; WORD $0x7f1839a7  // .quad 9158133232781315342
	WORD $0x341ca07c; WORD $0xd31045a8  // .quad -3238011543348273028
	WORD $0x1209a051; WORD $0x1ede4811  // .quad 2224294504121868369
	WORD $0x2091e44d; WORD $0x83ea2b89  // .quad -8941286242233752499
	WORD $0xab460433; WORD $0x934aed0a  // .quad -7833187971778608077
	WORD $0x68b65d60; WORD $0xa4e4b66b  // .quad -6564921784364802720
	WORD $0x56178540; WORD $0xf81da84d  // .quad -568112927868484288
	WORD $0x42e3f4b9; WORD $0xce1de406  // .quad -3594466212028615495
	WORD $0xab9d668f; WORD $0x36251260  // .quad 3901544858591782543
	WORD $0xe9ce78f3; WORD $0x80d2ae83  // .quad -9164070410158966541
	WORD $0x6b42601a; WORD $0xc1d72b7c  // .quad -4479063491021217766
	WORD $0xe4421730; WORD $0xa1075a24  // .quad -6843401994271320272
	WORD $0x8612f820; WORD $0xb24cf65b  // .quad -5598829363776522208
	WORD $0x1d529cfc; WORD $0xc94930ae  // .quad -3942566474411762436
	WORD $0x6797b628; WORD $0xdee033f2  // .quad -2386850686293264856
	WORD $0xa4a7443c; WORD $0xfb9b7cd9  // .quad -316522074587315140
	WORD $0x017da3b2; WORD $0x169840ef  // .quad 1628122660560806834
	WORD $0x06e88aa5; WORD $0x9d412e08  // .quad -7115355324258153819
	WORD $0x60ee864f; WORD $0x8e1f2895  // .quad -8205795374004271537
	WORD $0x08a2ad4e; WORD $0xc491798a  // .quad -4282508136895304370
	WORD $0xb92a27e3; WORD $0xf1a6f2ba  // .quad -1033872180650563613
	WORD $0x8acb58a2; WORD $0xf5b5d7ec  // .quad -741449152691742558
	WORD $0x6774b1dc; WORD $0xae10af69  // .quad -5904026244240592420
	WORD $0xd6bf1765; WORD $0x9991a6f3  // .quad -7380934748073420955
	WORD $0xe0a8ef2a; WORD $0xacca6da1  // .quad -5995859411864064214
	WORD $0xcc6edd3f; WORD $0xbff610b0  // .quad -4614482416664388289
	WORD $0x58d32af4; WORD $0x17fd090a  // .quad 1728547772024695540
	WORD $0xff8a948e; WORD $0xeff394dc  // .quad -1156417002403097458
	WORD $0xef07f5b1; WORD $0xddfc4b4c  // .quad -2451001303396518479
	WORD $0x1fb69cd9; WORD $0x95f83d0a  // .quad -7640289654143017767
	WORD $0x1564f98f; WORD $0x4abdaf10  // .quad 5385653213018257807
	WORD $0xa7a4440f; WORD $0xbb764c4c  // .quad -4938676049251384305
	WORD $0x1abe37f2; WORD $0x9d6d1ad4  // .quad -7102991539009341454
	WORD $0xd18d5513; WORD $0xea53df5f  // .quad -1561659043136842477
	WORD $0x216dc5ee; WORD $0x84c86189  // .quad -8878739423761676818
	WORD $0xe2f8552c; WORD $0x92746b9b  // .quad -7893565929601608404
	WORD $0xb4e49bb5; WORD $0x32fd3cf5  // .quad 3674159897003727797
	WORD $0xdbb66a77; WORD $0xb7118682  // .quad -5255271393574622601
	WORD $0x221dc2a2; WORD $0x3fbc8c33  // .quad 4592699871254659746
	WORD $0x92a40515; WORD $0xe4d5e823  // .quad -1957403223540890347
	WORD $0xeaa5334b; WORD $0x0fabaf3f  // .quad 1129188820640936779
	WORD $0x3ba6832d; WORD $0x8f05b116  // .quad -8140906042354138323
	WORD $0xf2a7400f; WORD $0x29cb4d87  // .quad 3011586022114279439
	WORD $0xca9023f8; WORD $0xb2c71d5b  // .quad -5564446534515285000
	WORD $0xef511013; WORD $0x743e20e9  // .quad 8376168546070237203
	WORD $0xbd342cf6; WORD $0xdf78e4b2  // .quad -2343872149716718346
	WORD $0x6b255417; WORD $0x914da924  // .quad -7976533391121755113
	WORD $0xb6409c1a; WORD $0x8bab8eef  // .quad -8382449121214030822
	WORD $0xc2f7548f; WORD $0x1ad089b6  // .quad 1932195658189984911
	WORD $0xa3d0c320; WORD $0xae9672ab  // .quad -5866375383090150624
	WORD $0x73b529b2; WORD $0xa184ac24  // .quad -6808127464117294670
	WORD $0x8cc4f3e8; WORD $0xda3c0f56  // .quad -2721283210435300376
	WORD $0x90a2741f; WORD $0xc9e5d72d  // .quad -3898473311719230433
	WORD $0x17fb1871; WORD $0x88658996  // .quad -8618331034163144591
	WORD $0x7a658893; WORD $0x7e2fa67c  // .quad 9092669226243950739
	WORD $0x9df9de8d; WORD $0xaa7eebfb  // .quad -6161227774276542835
	WORD $0x98feeab8; WORD $0xddbb901b  // .quad -2469221522477225288
	WORD $0x85785631; WORD $0xd51ea6fa  // .quad -3089848699418290639
	WORD $0x7f3ea566; WORD $0x552a7422  // .quad 6136845133758244198
	WORD $0x936b35de; WORD $0x8533285c  // .quad -8848684464777513506
	WORD $0x8f872760; WORD $0xd53a8895  // .quad -3082000819042179232
	WORD $0xb8460356; WORD $0xa67ff273  // .quad -6449169562544503978
	WORD $0xf368f138; WORD $0x8a892aba  // .quad -8464187042230111944
	WORD $0xa657842c; WORD $0xd01fef10  // .quad -3449775934753242068
	WORD $0xb0432d86; WORD $0x2d2b7569  // .quad 3254824252494523782
	WORD $0x67f6b29b; WORD $0x8213f56a  // .quad -9073638986861858149
	WORD $0x0e29fc74; WORD $0x9c3b2962  // .quad -7189106879045698444
	WORD $0x01f45f42; WORD $0xa298f2c5  // .quad -6730362715149934782
	WORD $0x91b47b90; WORD $0x8349f3ba  // .quad -8986383598807123056
	WORD $0x42717713; WORD $0xcb3f2f76  // .quad -3801267375510030573
	WORD $0x36219a74; WORD $0x241c70a9  // .quad 2602078556773259892
	WORD $0xd30dd4d7; WORD $0xfe0efb53  // .quad -139898200960150313
	WORD $0x83aa0111; WORD $0xed238cd3  // .quad -1359087822460813039
	WORD $0x63e8a506; WORD $0x9ec95d14  // .quad -7004965403241175802
	WORD $0x324a40ab; WORD $0xf4363804  // .quad -849429889038008149
	WORD $0x7ce2ce48; WORD $0xc67bb459  // .quad -4144520735624081848
	WORD $0x3edcd0d6; WORD $0xb143c605  // .quad -5673473379724898090
	WORD $0xdc1b81da; WORD $0xf81aa16f  // .quad -568964901102714406
	WORD $0x8e94050b; WORD $0xdd94b786  // .quad -2480155706228734709
	WORD $0xe9913128; WORD $0x9b10a4e5  // .quad -7273132090830278360
	WORD $0x191c8327; WORD $0xca7cf2b4  // .quad -3855940325606653145
	WORD $0x63f57d72; WORD $0xc1d4ce1f  // .quad -4479729095110460046
	WORD $0x1f63a3f1; WORD $0xfd1c2f61  // .quad -208239388580928527
	WORD $0x3cf2dccf; WORD $0xf24a01a7  // .quad -987975350460687153
	WORD $0x673c8ced; WORD $0xbc633b39  // .quad -4871985254153548563
	WORD $0x8617ca01; WORD $0x976e4108  // .quad -7535013621679011327
	WORD $0xe085d814; WORD $0xd5be0503  // .quad -3044990783845967852
	WORD $0xa79dbc82; WORD $0xbd49d14a  // .quad -4807081008671376254
	WORD $0xd8a74e19; WORD $0x4b2d8644  // .quad 5417133557047315993
	WORD $0x51852ba2; WORD $0xec9c459d  // .quad -1397165242411832414
	WORD $0x0ed1219f; WORD $0xddf8e7d6  // .quad -2451955090545630817
	WORD $0x52f33b45; WORD $0x93e1ab82  // .quad -7790757304148477115
	WORD $0xc942b504; WORD $0xcabb90e5  // .quad -3838314940804713212
	WORD $0xe7b00a17; WORD $0xb8da1662  // .quad -5126760611758208489
	WORD $0x3b936244; WORD $0x3d6a751f  // .quad 4425478360848884292
	WORD $0xa19c0c9d; WORD $0xe7109bfb  // .quad -1796764746270372707
	WORD $0x0a783ad5; WORD $0x0cc51267  // .quad 920161932633717461
	WORD $0x450187e2; WORD $0x906a617d  // .quad -8040506994060064798
	WORD $0x668b24c6; WORD $0x27fb2b80  // .quad 2880944217109767366
	WORD $0x9641e9da; WORD $0xb484f9dc  // .quad -5438947724147693094
	WORD $0x802dedf7; WORD $0xb1f9f660  // .quad -5622191765467566601
	WORD $0xbbd26451; WORD $0xe1a63853  // .quad -2186998636757228463
	WORD $0xa0396974; WORD $0x5e7873f8  // .quad 6807318348447705460
	WORD $0x55637eb2; WORD $0x8d07e334  // .quad -8284403175614349646
	WORD $0x6423e1e9; WORD $0xdb0b487b  // .quad -2662955059861265943
	WORD $0x6abc5e5f; WORD $0xb049dc01  // .quad -5743817951090549153
	WORD $0x3d2cda63; WORD $0x91ce1a9a  // .quad -7940379843253970333
	WORD $0xc56b75f7; WORD $0xdc5c5301  // .quad -2568086420435798537
	WORD $0xcc7810fc; WORD $0x7641a140  // .quad 8521269269642088700
	WORD $0x1b6329ba; WORD $0x89b9b3e1  // .quad -8522583040413455942
	WORD $0x7fcb0a9e; WORD $0xa9e904c8  // .quad -6203421752542164322
	WORD $0x623bf429; WORD $0xac2820d9  // .quad -6041542782089432023
	WORD $0x9fbdcd45; WORD $0x546345fa  // .quad 6080780864604458309
	WORD $0xbacaf133; WORD $0xd732290f  // .quad -2940242459184402125
	WORD $0x47ad4096; WORD $0xa97c1779  // .quad -6234081974526590826
	WORD $0xd4bed6c0; WORD $0x867f59a9  // .quad -8755180564631333184
	WORD $0xcccc485e; WORD $0x49ed8eab  // .quad 5327070802775656542
	WORD $0x49ee8c70; WORD $0xa81f3014  // .quad -6332289687361778576
	WORD $0xbfff5a75; WORD $0x5c68f256  // .quad 6658838503469570677
	WORD $0x5c6a2f8c; WORD $0xd226fc19  // .quad -3303676090774835316
	WORD $0x6fff3112; WORD $0x73832eec  // .quad 8323548129336963346
	WORD $0xd9c25db7; WORD $0x83585d8f  // .quad -8982326584375353929
	WORD $0xc5ff7eac; WORD $0xc831fd53  // .quad -4021154456019173716
	WORD $0xd032f525; WORD $0xa42e74f3  // .quad -6616222212041804507
	WORD $0xb77f5e56; WORD $0xba3e7ca8  // .quad -5026443070023967146
	WORD $0xc43fb26f; WORD $0xcd3a1230  // .quad -3658591746624867729
	WORD $0xe55f35ec; WORD $0x28ce1bd2  // .quad 2940318199324816876
	WORD $0x7aa7cf85; WORD $0x80444b5e  // .quad -9204148869281624187
	WORD $0xcf5b81b4; WORD $0x7980d163  // .quad 8755227902219092404
	WORD $0x1951c366; WORD $0xa0555e36  // .quad -6893500068174642330
	WORD $0xc3326220; WORD $0xd7e105bc  // .quad -2891023177508298208
	WORD $0x9fa63440; WORD $0xc86ab5c3  // .quad -4005189066790915008
	WORD $0xf3fefaa8; WORD $0x8dd9472b  // .quad -8225464990312760664
	WORD $0x878fc150; WORD $0xfa856334  // .quad -394800315061255856
	WORD $0xf0feb952; WORD $0xb14f98f6  // .quad -5670145219463562926
	WORD $0xd4b9d8d2; WORD $0x9c935e00  // .quad -7164279224554366766
	WORD $0x569f33d4; WORD $0x6ed1bf9a  // .quad 7985374283903742932
	WORD $0x09e84f07; WORD $0xc3b83581  // .quad -4343663012265570553
	WORD $0xec4700c9; WORD $0x0a862f80  // .quad 758345818024902857
	WORD $0x4c6262c8; WORD $0xf4a642e1  // .quad -817892746904575288
	WORD $0x2758c0fb; WORD $0xcd27bb61  // .quad -3663753745896259333
	WORD $0xcfbd7dbd; WORD $0x98e7e9cc  // .quad -7428711994456441411
	WORD $0xb897789d; WORD $0x8038d51c  // .quad -9207375118826243939
	WORD $0x03acdd2c; WORD $0xbf21e440  // .quad -4674203974643163860
	WORD $0xe6bd56c4; WORD $0xe0470a63  // .quad -2285846861678029116
	WORD $0x04981478; WORD $0xeeea5d50  // .quad -1231068949876566920
	WORD $0xe06cac75; WORD $0x1858ccfc  // .quad 1754377441329851509
	WORD $0x02df0ccb; WORD $0x95527a52  // .quad -7686947121313936181
	WORD $0x0c43ebc9; WORD $0x0f37801e  // .quad 1096485900831157193
	WORD $0x8396cffd; WORD $0xbaa718e6  // .quad -4996997883215032323
	WORD $0x8f54e6bb; WORD $0xd3056025  // .quad -3241078642388441413
	WORD $0x247c83fd; WORD $0xe950df20  // .quad -1634561335591402499
	WORD $0xf32a206a; WORD $0x47c6b82e  // .quad 5172023733869224042
	WORD $0x16cdd27e; WORD $0x91d28b74  // .quad -7939129862385708418
	WORD $0x57fa5442; WORD $0x4cdc331d  // .quad 5538357842881958978
	WORD $0x1c81471d; WORD $0xb6472e51  // .quad -5312226309554747619
	WORD $0xadf8e953; WORD $0xe0133fe4  // .quad -2300424733252327085
	WORD $0x63a198e5; WORD $0xe3d8f9e5  // .quad -2028596868516046619
	WORD $0xd97723a7; WORD $0x58180fdd  // .quad 6347841120289366951
	WORD $0x5e44ff8f; WORD $0x8e679c2f  // .quad -8185402070463610993
	WORD $0xa7ea7649; WORD $0x570f09ea  // .quad 6273243709394548297
	WORD $0x35d63f73; WORD $0xb201833b  // .quad -5620066569652125837
	WORD $0x51e513db; WORD $0x2cd2cc65  // .quad 3229868618315797467
	WORD $0x034bcf4f; WORD $0xde81e40a  // .quad -2413397193637769393
	WORD $0xa65e58d2; WORD $0xf8077f7e  // .quad -574350245532641070
	WORD $0x420f6191; WORD $0x8b112e86  // .quad -8425902273664687727
	WORD $0x27faf783; WORD $0xfb04afaf  // .quad -358968903457900669
	WORD $0xd29339f6; WORD $0xadd57a27  // .quad -5920691823653471754
	WORD $0xf1f9b564; WORD $0x79c5db9a  // .quad 8774660907532399972
	WORD $0xc7380874; WORD $0xd94ad8b1  // .quad -2789178761139451788
	WORD $0xae7822bd; WORD $0x18375281  // .quad 1744954097560724157
	WORD $0x1c830548; WORD $0x87cec76f  // .quad -8660765753353239224
	WORD $0x0d0b15b6; WORD $0x8f229391  // .quad -8132775725879323210
	WORD $0xe3a3c69a; WORD $0xa9c2794a  // .quad -6214271173264161126
	WORD $0x504ddb23; WORD $0xb2eb3875  // .quad -5554283638921766109
	WORD $0x9c8cb841; WORD $0xd433179d  // .quad -3156152948152813503
	WORD $0xa46151ec; WORD $0x5fa60692  // .quad 6892203506629956076
	WORD $0x81d7f328; WORD $0x849feec2  // .quad -8890124620236590296
	WORD $0xa6bcd334; WORD $0xdbc7c41b  // .quad -2609901835997359308
	WORD $0x224deff3; WORD $0xa5c7ea73  // .quad -6500969756868349965
	WORD $0x906c0801; WORD $0x12b9b522  // .quad 1349308723430688769
	WORD $0xeae16bef; WORD $0xcf39e50f  // .quad -3514526177658049553
	WORD $0x34870a01; WORD $0xd768226b  // .quad -2925050114139026943
	WORD $0xf2cce375; WORD $0x81842f29  // .quad -9114107888677362827
	WORD $0x00d46641; WORD $0xe6a11583  // .quad -1828156321336891839
	WORD $0x6f801c53; WORD $0xa1e53af4  // .quad -6780948842419315629
	WORD $0xc1097fd1; WORD $0x60495ae3  // .quad 6938176635183661009
	WORD $0x8b602368; WORD $0xca5e89b1  // .quad -3864500034596756632
	WORD $0xb14bdfc5; WORD $0x385bb19c  // .quad 4061034775552188357
	WORD $0xee382c42; WORD $0xfcf62c1d  // .quad -218939024818557886
	WORD $0xdd9ed7b6; WORD $0x46729e03  // .quad 5076293469440235446
	WORD $0xb4e31ba9; WORD $0x9e19db92  // .quad -7054365918152680535
	WORD $0x6a8346d2; WORD $0x6c07a2c2  // .quad 7784369436827535058

TEXT ·__f64toa(SB), NOSPLIT, $0-24
	NO_LOCAL_POINTERS

_entry:
	MOVD 16(g), R16
	SUB $96, RSP, R17
	CMP  R16, R17
	BLS  _stack_grow

_f64toa:
	MOVD out+0(FP), R0
	FMOVD val+8(FP), F0
	MOVD ·_subr__f64toa(SB), R11
	WORD $0x1000005e // adr x30, .+8
	JMP (R11)
	MOVD R0, ret+16(FP)
	RET

_stack_grow:
	MOVD R30, R3
	CALL runtime·morestack_noctxt<>(SB)
	JMP  _entry
