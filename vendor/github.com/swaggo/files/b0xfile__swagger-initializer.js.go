// Code generaTed by fileb0x at "2022-11-15 13:53:16.093034824 +0100 CET m=+0.037451383" from config file "b0x.yaml" DO NOT EDIT.
// modified(2022-11-15 13:51:12.282597108 +0100 CET)
// original path: swagger-ui/dist/swagger-initializer.js

package swaggerFiles

import (
	"os"
)

// FileSwaggerInitializerJs is "/swagger-initializer.js"
var FileSwaggerInitializerJs = []byte("\x77\x69\x6e\x64\x6f\x77\x2e\x6f\x6e\x6c\x6f\x61\x64\x20\x3d\x20\x66\x75\x6e\x63\x74\x69\x6f\x6e\x28\x29\x20\x7b\x0a\x20\x20\x2f\x2f\x3c\x65\x64\x69\x74\x6f\x72\x2d\x66\x6f\x6c\x64\x20\x64\x65\x73\x63\x3d\x22\x43\x68\x61\x6e\x67\x65\x61\x62\x6c\x65\x20\x43\x6f\x6e\x66\x69\x67\x75\x72\x61\x74\x69\x6f\x6e\x20\x42\x6c\x6f\x63\x6b\x22\x3e\x0a\x0a\x20\x20\x2f\x2f\x20\x74\x68\x65\x20\x66\x6f\x6c\x6c\x6f\x77\x69\x6e\x67\x20\x6c\x69\x6e\x65\x73\x20\x77\x69\x6c\x6c\x20\x62\x65\x20\x72\x65\x70\x6c\x61\x63\x65\x64\x20\x62\x79\x20\x64\x6f\x63\x6b\x65\x72\x2f\x63\x6f\x6e\x66\x69\x67\x75\x72\x61\x74\x6f\x72\x2c\x20\x77\x68\x65\x6e\x20\x69\x74\x20\x72\x75\x6e\x73\x20\x69\x6e\x20\x61\x20\x64\x6f\x63\x6b\x65\x72\x2d\x63\x6f\x6e\x74\x61\x69\x6e\x65\x72\x0a\x20\x20\x77\x69\x6e\x64\x6f\x77\x2e\x75\x69\x20\x3d\x20\x53\x77\x61\x67\x67\x65\x72\x55\x49\x42\x75\x6e\x64\x6c\x65\x28\x7b\x0a\x20\x20\x20\x20\x75\x72\x6c\x3a\x20\x22\x68\x74\x74\x70\x73\x3a\x2f\x2f\x70\x65\x74\x73\x74\x6f\x72\x65\x2e\x73\x77\x61\x67\x67\x65\x72\x2e\x69\x6f\x2f\x76\x32\x2f\x73\x77\x61\x67\x67\x65\x72\x2e\x6a\x73\x6f\x6e\x22\x2c\x0a\x20\x20\x20\x20\x64\x6f\x6d\x5f\x69\x64\x3a\x20\x27\x23\x73\x77\x61\x67\x67\x65\x72\x2d\x75\x69\x27\x2c\x0a\x20\x20\x20\x20\x64\x65\x65\x70\x4c\x69\x6e\x6b\x69\x6e\x67\x3a\x20\x74\x72\x75\x65\x2c\x0a\x20\x20\x20\x20\x70\x72\x65\x73\x65\x74\x73\x3a\x20\x5b\x0a\x20\x20\x20\x20\x20\x20\x53\x77\x61\x67\x67\x65\x72\x55\x49\x42\x75\x6e\x64\x6c\x65\x2e\x70\x72\x65\x73\x65\x74\x73\x2e\x61\x70\x69\x73\x2c\x0a\x20\x20\x20\x20\x20\x20\x53\x77\x61\x67\x67\x65\x72\x55\x49\x53\x74\x61\x6e\x64\x61\x6c\x6f\x6e\x65\x50\x72\x65\x73\x65\x74\x0a\x20\x20\x20\x20\x5d\x2c\x0a\x20\x20\x20\x20\x70\x6c\x75\x67\x69\x6e\x73\x3a\x20\x5b\x0a\x20\x20\x20\x20\x20\x20\x53\x77\x61\x67\x67\x65\x72\x55\x49\x42\x75\x6e\x64\x6c\x65\x2e\x70\x6c\x75\x67\x69\x6e\x73\x2e\x44\x6f\x77\x6e\x6c\x6f\x61\x64\x55\x72\x6c\x0a\x20\x20\x20\x20\x5d\x2c\x0a\x20\x20\x20\x20\x6c\x61\x79\x6f\x75\x74\x3a\x20\x22\x53\x74\x61\x6e\x64\x61\x6c\x6f\x6e\x65\x4c\x61\x79\x6f\x75\x74\x22\x0a\x20\x20\x7d\x29\x3b\x0a\x0a\x20\x20\x2f\x2f\x3c\x2f\x65\x64\x69\x74\x6f\x72\x2d\x66\x6f\x6c\x64\x3e\x0a\x7d\x3b\x0a")

func init() {

	f, err := FS.OpenFile(CTX, "/swagger-initializer.js", os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0777)
	if err != nil {
		panic(err)
	}

	_, err = f.Write(FileSwaggerInitializerJs)
	if err != nil {
		panic(err)
	}

	err = f.Close()
	if err != nil {
		panic(err)
	}
}
