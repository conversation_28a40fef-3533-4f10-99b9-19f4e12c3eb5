// Code generaTed by fileb0x at "2022-11-15 13:53:16.093343763 +0100 CET m=+0.037760322" from config file "b0x.yaml" DO NOT EDIT.
// modified(2022-11-15 13:51:12.282597108 +0100 CET)
// original path: swagger-ui/dist/favicon-16x16.png

package swaggerFiles

import (
	"os"
)

// FileFavicon16x16Png is "/favicon-16x16.png"
var FileFavicon16x16Png = []byte("\x89\x50\x4e\x47\x0d\x0a\x1a\x0a\x00\x00\x00\x0d\x49\x48\x44\x52\x00\x00\x00\x10\x00\x00\x00\x10\x08\x03\x00\x00\x00\x28\x2d\x0f\x53\x00\x00\x01\x35\x50\x4c\x54\x45\x62\xb1\x34\x61\xb1\x34\x5e\xab\x35\x5b\xa5\x35\x57\xa0\x37\x55\x9d\x37\x52\x97\x38\x51\x96\x38\x2f\x5e\x40\x2e\x5d\x40\x2d\x5a\x41\x2b\x57\x41\x33\x66\x3e\x34\x66\x3f\x39\x6f\x3d\x25\x4e\x43\x24\x4d\x43\x24\x4f\x43\x26\x4d\x42\x24\x4b\x42\x23\x4c\x42\x21\x49\x43\x24\x4b\x42\x24\x4c\x42\x24\x4d\x42\x25\x4d\x42\x24\x4e\x43\x25\x4e\x43\x1c\x41\x44\x1c\x3f\x45\x1f\x43\x44\x1d\x43\x44\x1f\x44\x44\x20\x45\x43\x22\x49\x43\x22\x49\x43\x23\x4a\x42\x27\x53\x41\x24\x4c\x43\x26\x50\x41\x22\x47\x42\x22\x48\x43\x29\x56\x41\x2b\x59\x3f\x24\x4d\x41\x25\x4d\x42\x14\x36\x46\x15\x34\x44\x15\x32\x47\x11\x33\x44\x12\x35\x46\x10\x31\x42\x0c\x31\x49\x15\x2b\x40\x00\x24\x49\x00\x33\x4d\x00\x33\x33\x00\x00\x00\x00\x00\x00\x85\xea\x2d\x84\xe9\x2c\x83\xe8\x2c\x82\xe6\x2d\x81\xe5\x2c\x7f\xe2\x2e\x80\xe1\x2e\x7d\xdd\x2e\x7c\xdd\x2e\x76\xd2\x30\x74\xd0\x30\x72\xca\x31\x71\xc9\x31\x70\xc8\x31\x6f\xc6\x32\x6d\xc5\x31\x6d\xc4\x31\x6c\xc3\x32\x6b\xc0\x32\x6a\xbf\x32\x69\xbe\x33\x68\xbb\x33\x68\xba\x33\x67\xb8\x33\x4b\x8d\x39\x4a\x8a\x3a\x4a\x89\x3a\x44\x7f\x3b\x43\x7f\x3c\x40\x79\x3d\x3e\x77\x3d\x39\x6e\x3e\x38\x6d\x3e\x38\x6e\x3f\x36\x6a\x3f\x35\x68\x3f\x33\x65\x3f\x1b\x3d\x45\x1b\x3e\x45\x1c\x3f\x45\x1c\x3d\x45\x1e\x43\x45\x1f\x44\x44\x20\x46\x44\x60\x25\x11\x2f\x00\x00\x00\x3b\x74\x52\x4e\x53\xf4\xf4\xf5\xf5\xf6\xf5\xf7\xf6\xee\xee\xef\xf0\xea\xea\xe7\xe1\xe1\xe0\xe0\xe3\xe3\xdf\xdc\xdb\xdb\xda\xd9\xd8\xd8\xdb\xcf\xbf\xbc\xba\xac\xab\xa9\xa9\xa1\x99\x96\x94\x8e\x89\x85\x84\x4c\x31\x24\x1e\x1d\x1f\x15\x0c\x07\x0a\x05\x01\x00\x07\x07\xae\xc9\x00\x00\x00\xd8\x49\x44\x41\x54\x78\xda\x3d\xcf\xd9\x2e\x43\x51\x18\x86\xe1\xcf\x6e\x8a\x8d\x52\x69\xa9\x22\x86\xb6\x31\xcf\x73\xd6\xbb\x5b\xb3\x84\x12\x1b\x41\x8c\x35\x94\x3b\x75\xe0\x86\xa4\x12\xc1\x5a\xcd\x4e\x9f\xa3\xff\xff\xce\x5e\x19\x6b\x2e\x97\x49\x76\x0f\x4c\x2d\xb9\x5b\xc6\xac\x0f\x77\x94\x4b\x50\x3a\x4e\x8c\xae\xba\x61\x63\x30\x4e\xa4\x69\x68\xcd\x0e\x85\x96\xe8\xdd\xdb\x24\x96\x37\x9a\xf7\xe1\xf2\x01\xeb\xf1\x1e\xda\x16\x54\x08\xe1\x7d\x0b\x6b\xe7\x0d\xc2\x49\xf5\x04\xf0\x1a\xe0\xbc\x40\xd0\xa7\x14\x5c\xdd\xec\x9f\x1f\x9c\x1e\x9e\x54\x2e\x20\xed\xfd\x49\xbf\x71\xff\xcb\xaf\xf9\xb5\xef\x98\xf4\xa3\x6c\x00\x4f\x45\x9c\xe7\x22\x41\xaf\xc6\x43\xa8\xee\x62\x6d\x57\xe1\x6c\x42\xcb\xad\x70\x5b\xc1\xba\xbb\x86\xf6\x45\x99\x31\x8f\x86\xe6\x9c\xf1\x94\xca\x7f\x28\xf2\x99\x49\x4b\x36\x70\xba\xf3\xc8\xc5\x95\x13\x23\xf5\x38\x6b\x65\x36\x9b\xec\xea\x9f\xa9\xe7\xff\x03\xcd\x4a\x39\x84\xc0\xe4\xbb\xd1\x00\x00\x00\x00\x49\x45\x4e\x44\xae\x42\x60\x82")

func init() {

	f, err := FS.OpenFile(CTX, "/favicon-16x16.png", os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0777)
	if err != nil {
		panic(err)
	}

	_, err = f.Write(FileFavicon16x16Png)
	if err != nil {
		panic(err)
	}

	err = f.Close()
	if err != nil {
		panic(err)
	}
}
