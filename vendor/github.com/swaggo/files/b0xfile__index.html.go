// Code generaTed by fileb0x at "2022-11-15 13:53:16.140766442 +0100 CET m=+0.085183001" from config file "b0x.yaml" DO NOT EDIT.
// modified(2022-11-15 13:51:12.282597108 +0100 CET)
// original path: swagger-ui/dist/index.html

package swaggerFiles

import (
	"os"
)

// FileIndexHTML is "/index.html"
var FileIndexHTML = []byte("\x3c\x21\x2d\x2d\x20\x48\x54\x4d\x4c\x20\x66\x6f\x72\x20\x73\x74\x61\x74\x69\x63\x20\x64\x69\x73\x74\x72\x69\x62\x75\x74\x69\x6f\x6e\x20\x62\x75\x6e\x64\x6c\x65\x20\x62\x75\x69\x6c\x64\x20\x2d\x2d\x3e\x0a\x3c\x21\x44\x4f\x43\x54\x59\x50\x45\x20\x68\x74\x6d\x6c\x3e\x0a\x3c\x68\x74\x6d\x6c\x20\x6c\x61\x6e\x67\x3d\x22\x65\x6e\x22\x3e\x0a\x20\x20\x3c\x68\x65\x61\x64\x3e\x0a\x20\x20\x20\x20\x3c\x6d\x65\x74\x61\x20\x63\x68\x61\x72\x73\x65\x74\x3d\x22\x55\x54\x46\x2d\x38\x22\x3e\x0a\x20\x20\x20\x20\x3c\x74\x69\x74\x6c\x65\x3e\x53\x77\x61\x67\x67\x65\x72\x20\x55\x49\x3c\x2f\x74\x69\x74\x6c\x65\x3e\x0a\x20\x20\x20\x20\x3c\x6c\x69\x6e\x6b\x20\x72\x65\x6c\x3d\x22\x73\x74\x79\x6c\x65\x73\x68\x65\x65\x74\x22\x20\x74\x79\x70\x65\x3d\x22\x74\x65\x78\x74\x2f\x63\x73\x73\x22\x20\x68\x72\x65\x66\x3d\x22\x2e\x2f\x73\x77\x61\x67\x67\x65\x72\x2d\x75\x69\x2e\x63\x73\x73\x22\x20\x2f\x3e\x0a\x20\x20\x20\x20\x3c\x6c\x69\x6e\x6b\x20\x72\x65\x6c\x3d\x22\x73\x74\x79\x6c\x65\x73\x68\x65\x65\x74\x22\x20\x74\x79\x70\x65\x3d\x22\x74\x65\x78\x74\x2f\x63\x73\x73\x22\x20\x68\x72\x65\x66\x3d\x22\x69\x6e\x64\x65\x78\x2e\x63\x73\x73\x22\x20\x2f\x3e\x0a\x20\x20\x20\x20\x3c\x6c\x69\x6e\x6b\x20\x72\x65\x6c\x3d\x22\x69\x63\x6f\x6e\x22\x20\x74\x79\x70\x65\x3d\x22\x69\x6d\x61\x67\x65\x2f\x70\x6e\x67\x22\x20\x68\x72\x65\x66\x3d\x22\x2e\x2f\x66\x61\x76\x69\x63\x6f\x6e\x2d\x33\x32\x78\x33\x32\x2e\x70\x6e\x67\x22\x20\x73\x69\x7a\x65\x73\x3d\x22\x33\x32\x78\x33\x32\x22\x20\x2f\x3e\x0a\x20\x20\x20\x20\x3c\x6c\x69\x6e\x6b\x20\x72\x65\x6c\x3d\x22\x69\x63\x6f\x6e\x22\x20\x74\x79\x70\x65\x3d\x22\x69\x6d\x61\x67\x65\x2f\x70\x6e\x67\x22\x20\x68\x72\x65\x66\x3d\x22\x2e\x2f\x66\x61\x76\x69\x63\x6f\x6e\x2d\x31\x36\x78\x31\x36\x2e\x70\x6e\x67\x22\x20\x73\x69\x7a\x65\x73\x3d\x22\x31\x36\x78\x31\x36\x22\x20\x2f\x3e\x0a\x20\x20\x3c\x2f\x68\x65\x61\x64\x3e\x0a\x0a\x20\x20\x3c\x62\x6f\x64\x79\x3e\x0a\x20\x20\x20\x20\x3c\x64\x69\x76\x20\x69\x64\x3d\x22\x73\x77\x61\x67\x67\x65\x72\x2d\x75\x69\x22\x3e\x3c\x2f\x64\x69\x76\x3e\x0a\x20\x20\x20\x20\x3c\x73\x63\x72\x69\x70\x74\x20\x73\x72\x63\x3d\x22\x2e\x2f\x73\x77\x61\x67\x67\x65\x72\x2d\x75\x69\x2d\x62\x75\x6e\x64\x6c\x65\x2e\x6a\x73\x22\x20\x63\x68\x61\x72\x73\x65\x74\x3d\x22\x55\x54\x46\x2d\x38\x22\x3e\x20\x3c\x2f\x73\x63\x72\x69\x70\x74\x3e\x0a\x20\x20\x20\x20\x3c\x73\x63\x72\x69\x70\x74\x20\x73\x72\x63\x3d\x22\x2e\x2f\x73\x77\x61\x67\x67\x65\x72\x2d\x75\x69\x2d\x73\x74\x61\x6e\x64\x61\x6c\x6f\x6e\x65\x2d\x70\x72\x65\x73\x65\x74\x2e\x6a\x73\x22\x20\x63\x68\x61\x72\x73\x65\x74\x3d\x22\x55\x54\x46\x2d\x38\x22\x3e\x20\x3c\x2f\x73\x63\x72\x69\x70\x74\x3e\x0a\x20\x20\x20\x20\x3c\x73\x63\x72\x69\x70\x74\x20\x73\x72\x63\x3d\x22\x2e\x2f\x73\x77\x61\x67\x67\x65\x72\x2d\x69\x6e\x69\x74\x69\x61\x6c\x69\x7a\x65\x72\x2e\x6a\x73\x22\x20\x63\x68\x61\x72\x73\x65\x74\x3d\x22\x55\x54\x46\x2d\x38\x22\x3e\x20\x3c\x2f\x73\x63\x72\x69\x70\x74\x3e\x0a\x20\x20\x3c\x2f\x62\x6f\x64\x79\x3e\x0a\x3c\x2f\x68\x74\x6d\x6c\x3e\x0a")

func init() {

	f, err := FS.OpenFile(CTX, "/index.html", os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0777)
	if err != nil {
		panic(err)
	}

	_, err = f.Write(FileIndexHTML)
	if err != nil {
		panic(err)
	}

	err = f.Close()
	if err != nil {
		panic(err)
	}
}
