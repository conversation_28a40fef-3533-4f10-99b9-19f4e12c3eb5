// Code generaTed by fileb0x at "2022-11-15 13:53:16.221563184 +0100 CET m=+0.165979743" from config file "b0x.yaml" DO NOT EDIT.
// modified(2022-11-15 13:51:12.282597108 +0100 CET)
// original path: swagger-ui/dist/favicon-32x32.png

package swaggerFiles

import (
	"os"
)

// FileFavicon32x32Png is "/favicon-32x32.png"
var FileFavicon32x32Png = []byte("\x89\x50\x4e\x47\x0d\x0a\x1a\x0a\x00\x00\x00\x0d\x49\x48\x44\x52\x00\x00\x00\x20\x00\x00\x00\x20\x08\x03\x00\x00\x00\x44\xa4\x8a\xc6\x00\x00\x00\x90\x50\x4c\x54\x45\x00\x00\x00\x10\x33\x44\x16\x35\x46\x16\x36\x46\x17\x36\x46\x00\x2e\x3a\x16\x35\x46\x18\x38\x45\x17\x37\x46\x1a\x3c\x45\x0f\x31\x40\x14\x33\x44\x15\x35\x46\x16\x36\x46\x16\x35\x46\x16\x35\x45\x16\x35\x46\x15\x34\x46\x16\x36\x46\x16\x35\x46\x16\x33\x47\x85\xea\x2d\x17\x36\x47\x21\x47\x43\x81\xe5\x2c\x33\x66\x3f\x70\xc9\x31\x2f\x5e\x40\x37\x6b\x3e\x5a\xa5\x36\x7e\xe0\x2e\x43\x80\x3b\x77\xd4\x2f\x5f\xae\x35\x39\x6f\x3e\x6e\xc5\x32\x3f\x78\x3c\x73\xce\x30\x26\x4f\x42\x2c\x59\x41\x1e\x42\x45\x65\xb7\x34\x7a\xd9\x2e\x83\xe8\x2c\x48\x87\x3a\x4a\x8a\x3a\x49\x88\x3a\x4e\x90\x39\x78\x6f\x8d\xe5\x00\x00\x00\x15\x74\x52\x4e\x53\x00\x15\xcd\xf4\xe1\x07\x99\xfe\xf8\xfe\x10\x20\x77\xc4\xa9\x46\x8a\x53\xd7\xbd\x2d\x8a\x6b\xf8\x74\x00\x00\x01\x7e\x49\x44\x41\x54\x78\xda\x85\x53\xd9\x76\x82\x30\x10\x1d\x25\x10\x22\xee\x96\x09\x6b\x64\x07\xc5\xb6\xff\xff\x77\x2d\x49\x20\x14\x3d\xf6\xbe\x4c\x72\xe6\xce\x3e\x03\x06\xf6\x69\xbf\x26\xae\x4b\xd6\xfb\x93\x0d\xcf\x58\x39\x16\xb2\xb0\xfa\x7c\x54\x21\x43\xd7\x59\x2d\xf5\x5b\x0b\x93\x3c\xf0\x25\x82\x3c\x44\x6b\xfb\xc7\xcb\x66\x87\x49\xe4\xcf\x10\x25\xb8\xdb\x18\xbd\x47\xd8\xcd\x5f\x20\x67\xc4\x9b\xec\x09\x37\xe6\xc6\x09\x27\x3a\x11\x7b\x4d\x4b\xff\x05\x4a\xba\xb6\x55\x7e\x98\x0e\xff\xbe\x5c\xba\x49\xf1\x28\x03\x58\xc9\xf0\xab\x39\xc6\xa3\xa6\xa5\x71\x36\xc8\xc4\x1d\x82\x1c\xa9\xfc\x54\x58\xa4\x93\x69\x8c\x57\x69\x44\x9d\x5f\x82\x25\xdf\x7e\x8c\x99\x71\x5e\x63\x2b\xe5\xd5\xb5\xe1\x80\xaa\xc2\x06\xc5\xa4\xef\x05\x36\xf2\x71\xc3\x03\x38\x4c\xf5\x8f\xa3\x94\x1a\x94\x4b\x11\x30\x07\x2e\xb1\x7a\x62\xe7\xcf\xd0\x50\x45\x8f\x2f\x40\x0a\xd5\x38\x4c\xe6\x84\x02\x53\x25\xcf\xa0\xf2\x0d\x91\xd7\x7d\xdb\x65\x41\xc3\x85\xe0\x4d\x5f\x73\x0c\x65\x96\x16\xb8\x23\x21\x0b\x38\xbf\x0b\xce\x83\xac\x6b\xfb\xa8\x1b\x09\x3a\x84\xf8\x36\x21\x94\xc1\x97\xd0\x21\x76\x3a\x49\xca\xe6\x04\x36\x26\xb9\x03\x87\xf5\xba\x4c\xe1\x1b\x60\x37\x95\x79\xc2\x9b\x26\xdc\x8d\x5e\x20\x9f\x1a\x65\xbb\x57\xdd\xc9\xda\x10\xee\xb3\x56\xc3\x7e\x1c\x56\x6c\x86\x55\x60\x35\xc8\x4c\x0e\xcb\xa3\xa1\x34\x2a\xd8\x20\xf5\xe0\x78\x29\x6b\x91\xe3\x86\xa3\xee\x9a\x41\x54\xf6\xb3\x85\x01\xfb\xfc\xcf\xca\x81\x67\xbd\x5f\x5a\x80\x83\xc5\xf2\xa5\x3e\xa7\xc4\x83\x09\x1f\xe4\xfd\xe1\x00\xac\x2e\xf8\xf6\xf4\x86\x30\x67\x1c\x8e\xf7\xf1\x7c\xbc\x26\xce\xf6\xd5\xf9\xff\x00\xc6\x8c\x46\x7b\xbe\xb8\x05\x67\x00\x00\x00\x00\x49\x45\x4e\x44\xae\x42\x60\x82")

func init() {

	f, err := FS.OpenFile(CTX, "/favicon-32x32.png", os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0777)
	if err != nil {
		panic(err)
	}

	_, err = f.Write(FileFavicon32x32Png)
	if err != nil {
		panic(err)
	}

	err = f.Close()
	if err != nil {
		panic(err)
	}
}
