linters-settings:
  gocyclo:
    min-complexity: 45
  dupl:
    threshold: 200
  goconst:
    min-len: 2
    min-occurrences: 3

linters:
  enable-all: true
  disable:
    - recvcheck
    - unparam
    - lll
    - gochecknoinits
    - gochecknoglobals
    - funlen
    - godox
    - gocognit
    - whitespace
    - wsl
    - wrapcheck
    - testpackage
    - nlreturn
    - errorlint
    - nestif
    - godot
    - gofumpt
    - paralleltest
    - tparallel
    - thelper
    - exhaustruct
    - varnamelen
    - gci
    - depguard
    - errchkjson
    - inamedparam
    - nonamedreturns
    - musttag
    - ireturn
    - forcetypeassert
    - cyclop
    # deprecated linters
    #- deadcode
    #- interfacer
    #- scopelint
    #- varcheck
    #- structcheck
    #- golint
    #- nosnakecase
    #- maligned
    #- goerr113
    #- ifshort
    #- gomnd
    #- exhaustivestruct
