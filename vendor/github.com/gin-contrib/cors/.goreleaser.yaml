builds:
  - # If true, skip the build.
    # Useful for library projects.
    # Default is false
    skip: true

changelog:
  use: github
  groups:
    - title: Features
      regexp: "^.*feat[(\\w)]*:+.*$"
      order: 0
    - title: "Bug fixes"
      regexp: "^.*fix[(\\w)]*:+.*$"
      order: 1
    - title: "Enhancements"
      regexp: "^.*chore[(\\w)]*:+.*$"
      order: 2
    - title: "Refactor"
      regexp: "^.*refactor[(\\w)]*:+.*$"
      order: 3
    - title: "Build process updates"
      regexp: ^.*?(build|ci)(\(.+\))??!?:.+$
      order: 4
    - title: "Documentation updates"
      regexp: ^.*?docs?(\(.+\))??!?:.+$
      order: 4
    - title: Others
