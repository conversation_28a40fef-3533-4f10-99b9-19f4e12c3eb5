// Copyright 2025 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by generate.go. DO NOT EDIT.

package stdlib

type pkginfo struct {
	name string
	deps string // list of indices of dependencies, as varint-encoded deltas
}

var deps = [...]pkginfo{
	{"archive/tar", "\x03j\x03E5\x01\v\x01#\x01\x01\x02\x05\n\x02\x01\x02\x02\v"},
	{"archive/zip", "\x02\x04`\a\x16\x0205\x01+\x05\x01\x11\x03\x02\r\x04"},
	{"bufio", "\x03j}F\x13"},
	{"bytes", "m+R\x03\fH\x02\x02"},
	{"cmp", ""},
	{"compress/bzip2", "\x02\x02\xe6\x01C"},
	{"compress/flate", "\x02k\x03z\r\x025\x01\x03"},
	{"compress/gzip", "\x02\x04`\a\x03\x15eU"},
	{"compress/lzw", "\x02k\x03z"},
	{"compress/zlib", "\x02\x04`\a\x03\x13\x01f"},
	{"container/heap", "\xae\x02"},
	{"container/list", ""},
	{"container/ring", ""},
	{"context", "m\\i\x01\f"},
	{"crypto", "\x83\x01gE"},
	{"crypto/aes", "\x10\n\a\x8e\x02"},
	{"crypto/cipher", "\x03\x1e\x01\x01\x1d\x11\x1c,Q"},
	{"crypto/des", "\x10\x13\x1d-,\x96\x01\x03"},
	{"crypto/dsa", "@\x04)}\x0e"},
	{"crypto/ecdh", "\x03\v\f\x0e\x04\x14\x04\r\x1c}"},
	{"crypto/ecdsa", "\x0e\x05\x03\x04\x01\x0e\x16\x01\x04\f\x01\x1c}\x0e\x04L\x01"},
	{"crypto/ed25519", "\x0e\x1c\x16\n\a\x1c}E"},
	{"crypto/elliptic", "0=}\x0e:"},
	{"crypto/fips140", " \x05\x90\x01"},
	{"crypto/hkdf", "-\x12\x01-\x16"},
	{"crypto/hmac", "\x1a\x14\x11\x01\x112"},
	{"crypto/internal/boring", "\x0e\x02\rf"},
	{"crypto/internal/boring/bbig", "\x1a\xde\x01M"},
	{"crypto/internal/boring/bcache", "\xb3\x02\x12"},
	{"crypto/internal/boring/sig", ""},
	{"crypto/internal/cryptotest", "\x03\r\n)\x0e\x19\x06\x13\x12#\a\t\x11\x11\x11\x1b\x01\f\r\x05\n"},
	{"crypto/internal/entropy", "E"},
	{"crypto/internal/fips140", ">/}9\r\x15"},
	{"crypto/internal/fips140/aes", "\x03\x1d\x03\x02\x13\x04\x01\x01\x05*\x8c\x016"},
	{"crypto/internal/fips140/aes/gcm", " \x01\x02\x02\x02\x11\x04\x01\x06*\x8a\x01"},
	{"crypto/internal/fips140/alias", "\xc5\x02"},
	{"crypto/internal/fips140/bigmod", "%\x17\x01\x06*\x8c\x01"},
	{"crypto/internal/fips140/check", " \x0e\x06\b\x02\xac\x01["},
	{"crypto/internal/fips140/check/checktest", "%\xfe\x01\""},
	{"crypto/internal/fips140/drbg", "\x03\x1c\x01\x01\x04\x13\x04\b\x01(}\x0f9"},
	{"crypto/internal/fips140/ecdh", "\x03\x1d\x05\x02\t\f1}\x0f9"},
	{"crypto/internal/fips140/ecdsa", "\x03\x1d\x04\x01\x02\a\x02\x067}H"},
	{"crypto/internal/fips140/ed25519", "\x03\x1d\x05\x02\x04\v7\xc2\x01\x03"},
	{"crypto/internal/fips140/edwards25519", "%\a\f\x041\x8c\x019"},
	{"crypto/internal/fips140/edwards25519/field", "%\x13\x041\x8c\x01"},
	{"crypto/internal/fips140/hkdf", "\x03\x1d\x05\t\x069"},
	{"crypto/internal/fips140/hmac", "\x03\x1d\x14\x01\x017"},
	{"crypto/internal/fips140/mlkem", "\x03\x1d\x05\x02\x0e\x03\x041"},
	{"crypto/internal/fips140/nistec", "%\f\a\x041\x8c\x01*\x0f\x13"},
	{"crypto/internal/fips140/nistec/fiat", "%\x135\x8c\x01"},
	{"crypto/internal/fips140/pbkdf2", "\x03\x1d\x05\t\x069"},
	{"crypto/internal/fips140/rsa", "\x03\x1d\x04\x01\x02\r\x01\x01\x025}H"},
	{"crypto/internal/fips140/sha256", "\x03\x1d\x1c\x01\x06*\x8c\x01"},
	{"crypto/internal/fips140/sha3", "\x03\x1d\x18\x04\x010\x8c\x01L"},
	{"crypto/internal/fips140/sha512", "\x03\x1d\x1c\x01\x06*\x8c\x01"},
	{"crypto/internal/fips140/ssh", " \x05"},
	{"crypto/internal/fips140/subtle", "#"},
	{"crypto/internal/fips140/tls12", "\x03\x1d\x05\t\x06\x027"},
	{"crypto/internal/fips140/tls13", "\x03\x1d\x05\b\a\b1"},
	{"crypto/internal/fips140deps", ""},
	{"crypto/internal/fips140deps/byteorder", "\x99\x01"},
	{"crypto/internal/fips140deps/cpu", "\xad\x01\a"},
	{"crypto/internal/fips140deps/godebug", "\xb5\x01"},
	{"crypto/internal/fips140hash", "5\x1a4\xc2\x01"},
	{"crypto/internal/fips140only", "'\r\x01\x01M25"},
	{"crypto/internal/fips140test", ""},
	{"crypto/internal/hpke", "\x0e\x01\x01\x03\x1a\x1d#,`N"},
	{"crypto/internal/impl", "\xb0\x02"},
	{"crypto/internal/randutil", "\xea\x01\x12"},
	{"crypto/internal/sysrand", "mi!\x1f\r\x0f\x01\x01\v\x06"},
	{"crypto/internal/sysrand/internal/seccomp", "m"},
	{"crypto/md5", "\x0e2-\x16\x16`"},
	{"crypto/mlkem", "/"},
	{"crypto/pbkdf2", "2\r\x01-\x16"},
	{"crypto/rand", "\x1a\x06\a\x19\x04\x01(}\x0eM"},
	{"crypto/rc4", "#\x1d-\xc2\x01"},
	{"crypto/rsa", "\x0e\f\x01\t\x0f\f\x01\x04\x06\a\x1c\x03\x1325\r\x01"},
	{"crypto/sha1", "\x0e\f&-\x16\x16\x14L"},
	{"crypto/sha256", "\x0e\f\x1aO"},
	{"crypto/sha3", "\x0e'N\xc2\x01"},
	{"crypto/sha512", "\x0e\f\x1cM"},
	{"crypto/subtle", "8\x96\x01U"},
	{"crypto/tls", "\x03\b\x02\x01\x01\x01\x01\x02\x01\x01\x01\x03\x01\a\x01\v\x02\n\x01\b\x05\x03\x01\x01\x01\x01\x02\x01\x02\x01\x17\x02\x03\x13\x16\x14\b5\x16\x16\r\n\x01\x01\x01\x02\x01\f\x06\x02\x01"},
	{"crypto/tls/internal/fips140tls", " \x93\x02"},
	{"crypto/x509", "\x03\v\x01\x01\x01\x01\x01\x01\x01\x011\x03\x02\x01\x01\x02\x05\x0e\x06\x02\x02\x03E\x032\x01\x02\t\x01\x01\x01\a\x10\x05\x01\x06\x02\x05\f\x01\x02\r\x02\x01\x01\x02\x03\x01"},
	{"crypto/x509/pkix", "c\x06\a\x88\x01G"},
	{"database/sql", "\x03\nJ\x16\x03z\f\x06\"\x05\n\x02\x03\x01\f\x02\x02\x02"},
	{"database/sql/driver", "\r`\x03\xae\x01\x11\x10"},
	{"debug/buildinfo", "\x03W\x02\x01\x01\b\a\x03`\x18\x02\x01+\x0f "},
	{"debug/dwarf", "\x03c\a\x03z1\x13\x01\x01"},
	{"debug/elf", "\x03\x06P\r\a\x03`\x19\x01,\x19\x01\x15"},
	{"debug/gosym", "\x03c\n\xbe\x01\x01\x01\x02"},
	{"debug/macho", "\x03\x06P\r\n`\x1a,\x19\x01"},
	{"debug/pe", "\x03\x06P\r\a\x03`\x1a,\x19\x01\x15"},
	{"debug/plan9obj", "f\a\x03`\x1a,"},
	{"embed", "m+:\x18\x01T"},
	{"embed/internal/embedtest", ""},
	{"encoding", ""},
	{"encoding/ascii85", "\xea\x01E"},
	{"encoding/asn1", "\x03j\x03\x87\x01\x01&\x0f\x02\x01\x0f\x03\x01"},
	{"encoding/base32", "\xea\x01C\x02"},
	{"encoding/base64", "\x99\x01QC\x02"},
	{"encoding/binary", "m}\r'\x0f\x05"},
	{"encoding/csv", "\x02\x01j\x03zF\x11\x02"},
	{"encoding/gob", "\x02_\x05\a\x03`\x1a\f\x01\x02\x1d\b\x14\x01\x0e\x02"},
	{"encoding/hex", "m\x03zC\x03"},
	{"encoding/json", "\x03\x01]\x04\b\x03z\r'\x0f\x02\x01\x02\x0f\x01\x01\x02"},
	{"encoding/pem", "\x03b\b}C\x03"},
	{"encoding/xml", "\x02\x01^\f\x03z4\x05\f\x01\x02\x0f\x02"},
	{"errors", "\xc9\x01|"},
	{"expvar", "jK9\t\n\x15\r\n\x02\x03\x01\x10"},
	{"flag", "a\f\x03z,\b\x05\n\x02\x01\x0f"},
	{"fmt", "mE8\r\x1f\b\x0f\x02\x03\x11"},
	{"go/ast", "\x03\x01l\x0f\x01j\x03)\b\x0f\x02\x01"},
	{"go/ast/internal/tests", ""},
	{"go/build", "\x02\x01j\x03\x01\x03\x02\a\x02\x01\x17\x1e\x04\x02\t\x14\x12\x01+\x01\x04\x01\a\n\x02\x01\x11\x02\x02"},
	{"go/build/constraint", "m\xc2\x01\x01\x11\x02"},
	{"go/constant", "p\x10w\x01\x016\x01\x02\x11"},
	{"go/doc", "\x04l\x01\x06\t=-1\x12\x02\x01\x11\x02"},
	{"go/doc/comment", "\x03m\xbd\x01\x01\x01\x01\x11\x02"},
	{"go/format", "\x03m\x01\f\x01\x02jF"},
	{"go/importer", "s\a\x01\x01\x04\x01i9"},
	{"go/internal/gccgoimporter", "\x02\x01W\x13\x03\x05\v\x01g\x02,\x01\x05\x13\x01\v\b"},
	{"go/internal/gcimporter", "\x02n\x10\x01/\x05\x0e',\x17\x03\x02"},
	{"go/internal/srcimporter", "p\x01\x02\n\x03\x01i,\x01\x05\x14\x02\x13"},
	{"go/parser", "\x03j\x03\x01\x03\v\x01j\x01+\x06\x14"},
	{"go/printer", "p\x01\x03\x03\tj\r\x1f\x17\x02\x01\x02\n\x05\x02"},
	{"go/scanner", "\x03m\x10j2\x12\x01\x12\x02"},
	{"go/token", "\x04l\xbd\x01\x02\x03\x01\x0e\x02"},
	{"go/types", "\x03\x01\x06c\x03\x01\x04\b\x03\x02\x15\x1e\x06+\x04\x03\n%\a\n\x01\x01\x01\x02\x01\x0e\x02\x02"},
	{"go/version", "\xba\x01v"},
	{"hash", "\xea\x01"},
	{"hash/adler32", "m\x16\x16"},
	{"hash/crc32", "m\x16\x16\x14\x85\x01\x01\x12"},
	{"hash/crc64", "m\x16\x16\x99\x01"},
	{"hash/fnv", "m\x16\x16`"},
	{"hash/maphash", "\x94\x01\x05\x1b\x03@N"},
	{"html", "\xb0\x02\x02\x11"},
	{"html/template", "\x03g\x06\x19,5\x01\v \x05\x01\x02\x03\x0e\x01\x02\v\x01\x03\x02"},
	{"image", "\x02k\x1f^\x0f6\x03\x01"},
	{"image/color", ""},
	{"image/color/palette", "\x8c\x01"},
	{"image/draw", "\x8b\x01\x01\x04"},
	{"image/gif", "\x02\x01\x05e\x03\x1b\x01\x01\x01\vQ"},
	{"image/internal/imageutil", "\x8b\x01"},
	{"image/jpeg", "\x02k\x1e\x01\x04Z"},
	{"image/png", "\x02\a]\n\x13\x02\x06\x01^E"},
	{"index/suffixarray", "\x03c\a}\r*\f\x01"},
	{"internal/abi", "\xb4\x01\x91\x01"},
	{"internal/asan", "\xc5\x02"},
	{"internal/bisect", "\xa3\x02\x0f\x01"},
	{"internal/buildcfg", "pG_\x06\x02\x05\f\x01"},
	{"internal/bytealg", "\xad\x01\x98\x01"},
	{"internal/byteorder", ""},
	{"internal/cfg", ""},
	{"internal/chacha8rand", "\x99\x01\x1b\x91\x01"},
	{"internal/copyright", ""},
	{"internal/coverage", ""},
	{"internal/coverage/calloc", ""},
	{"internal/coverage/cfile", "j\x06\x17\x16\x01\x02\x01\x01\x01\x01\x01\x01\x01#\x01\x1f,\x06\a\f\x01\x03\f\x06"},
	{"internal/coverage/cformat", "\x04l-\x04I\f7\x01\x02\f"},
	{"internal/coverage/cmerge", "p-Z"},
	{"internal/coverage/decodecounter", "f\n-\v\x02@,\x19\x16"},
	{"internal/coverage/decodemeta", "\x02d\n\x17\x16\v\x02@,"},
	{"internal/coverage/encodecounter", "\x02d\n-\f\x01\x02>\f \x17"},
	{"internal/coverage/encodemeta", "\x02\x01c\n\x13\x04\x16\r\x02>,/"},
	{"internal/coverage/pods", "\x04l-y\x06\x05\f\x02\x01"},
	{"internal/coverage/rtcov", "\xc5\x02"},
	{"internal/coverage/slicereader", "f\nz["},
	{"internal/coverage/slicewriter", "pz"},
	{"internal/coverage/stringtab", "p8\x04>"},
	{"internal/coverage/test", ""},
	{"internal/coverage/uleb128", ""},
	{"internal/cpu", "\xc5\x02"},
	{"internal/dag", "\x04l\xbd\x01\x03"},
	{"internal/diff", "\x03m\xbe\x01\x02"},
	{"internal/exportdata", "\x02\x01j\x03\x03]\x1a,\x01\x05\x13\x01\x02"},
	{"internal/filepathlite", "m+:\x19B"},
	{"internal/fmtsort", "\x04\x9a\x02\x0f"},
	{"internal/fuzz", "\x03\nA\x18\x04\x03\x03\x01\f\x0355\r\x02\x1d\x01\x05\x02\x05\f\x01\x02\x01\x01\v\x04\x02"},
	{"internal/goarch", ""},
	{"internal/godebug", "\x96\x01 |\x01\x12"},
	{"internal/godebugs", ""},
	{"internal/goexperiment", ""},
	{"internal/goos", ""},
	{"internal/goroot", "\x96\x02\x01\x05\x14\x02"},
	{"internal/gover", "\x04"},
	{"internal/goversion", ""},
	{"internal/itoa", ""},
	{"internal/lazyregexp", "\x96\x02\v\x0f\x02"},
	{"internal/lazytemplate", "\xea\x01,\x1a\x02\v"},
	{"internal/msan", "\xc5\x02"},
	{"internal/nettrace", ""},
	{"internal/obscuretestdata", "e\x85\x01,"},
	{"internal/oserror", "m"},
	{"internal/pkgbits", "\x03K\x18\a\x03\x05\vj\x0e\x1e\r\f\x01"},
	{"internal/platform", ""},
	{"internal/poll", "mO\x1a\x149\x0f\x01\x01\v\x06"},
	{"internal/profile", "\x03\x04f\x03z7\r\x01\x01\x0f"},
	{"internal/profilerecord", ""},
	{"internal/race", "\x94\x01\xb1\x01"},
	{"internal/reflectlite", "\x94\x01 3<\""},
	{"internal/runtime/atomic", "\xc5\x02"},
	{"internal/runtime/exithook", "\xca\x01{"},
	{"internal/runtime/maps", "\x94\x01\x01\x1f\v\t\x05\x01w"},
	{"internal/runtime/math", "\xb4\x01"},
	{"internal/runtime/sys", "\xb4\x01\x04"},
	{"internal/runtime/syscall", "\xc5\x02"},
	{"internal/saferio", "\xea\x01["},
	{"internal/singleflight", "\xb2\x02"},
	{"internal/stringslite", "\x98\x01\xad\x01"},
	{"internal/sync", "\x94\x01 \x14k\x12"},
	{"internal/synctest", "\xc5\x02"},
	{"internal/syscall/execenv", "\xb4\x02"},
	{"internal/syscall/unix", "\xa3\x02\x10\x01\x11"},
	{"internal/sysinfo", "\x02\x01\xaa\x01=,\x1a\x02"},
	{"internal/syslist", ""},
	{"internal/testenv", "\x03\n`\x02\x01*\x1a\x10'+\x01\x05\a\f\x01\x02\x02\x01\n"},
	{"internal/testlog", "\xb2\x02\x01\x12"},
	{"internal/testpty", "m\x03\xa6\x01"},
	{"internal/trace", "\x02\x01\x01\x06\\\a\x03n\x03\x03\x06\x03\n6\x01\x02\x0f\x06"},
	{"internal/trace/internal/testgen", "\x03c\nl\x03\x02\x03\x011\v\x0f"},
	{"internal/trace/internal/tracev1", "\x03\x01b\a\x03t\x06\r6\x01"},
	{"internal/trace/raw", "\x02d\nq\x03\x06E\x01\x11"},
	{"internal/trace/testtrace", "\x02\x01j\x03l\x03\x06\x057\f\x02\x01"},
	{"internal/trace/tracev2", ""},
	{"internal/trace/traceviewer", "\x02]\v\x06\x1a<\x16\a\a\x04\t\n\x15\x01\x05\a\f\x01\x02\r"},
	{"internal/trace/traceviewer/format", ""},
	{"internal/trace/version", "pq\t"},
	{"internal/txtar", "\x03m\xa6\x01\x1a"},
	{"internal/types/errors", "\xaf\x02"},
	{"internal/unsafeheader", "\xc5\x02"},
	{"internal/xcoff", "Y\r\a\x03`\x1a,\x19\x01"},
	{"internal/zstd", "f\a\x03z\x0f"},
	{"io", "m\xc5\x01"},
	{"io/fs", "m+*(1\x12\x12\x04"},
	{"io/ioutil", "\xea\x01\x01+\x17\x03"},
	{"iter", "\xc8\x01[\""},
	{"log", "pz\x05'\r\x0f\x01\f"},
	{"log/internal", ""},
	{"log/slog", "\x03\nT\t\x03\x03z\x04\x01\x02\x02\x04'\x05\n\x02\x01\x02\x01\f\x02\x02\x02"},
	{"log/slog/internal", ""},
	{"log/slog/internal/benchmarks", "\r`\x03z\x06\x03<\x10"},
	{"log/slog/internal/buffer", "\xb2\x02"},
	{"log/slog/internal/slogtest", "\xf0\x01"},
	{"log/syslog", "m\x03~\x12\x16\x1a\x02\r"},
	{"maps", "\xed\x01X"},
	{"math", "\xad\x01LL"},
	{"math/big", "\x03j\x03)\x14=\r\x02\x024\x01\x02\x13"},
	{"math/bits", "\xc5\x02"},
	{"math/cmplx", "\xf7\x01\x02"},
	{"math/rand", "\xb5\x01B;\x01\x12"},
	{"math/rand/v2", "m,\x02\\\x02L"},
	{"mime", "\x02\x01b\b\x03z\f \x17\x03\x02\x0f\x02"},
	{"mime/multipart", "\x02\x01G#\x03E5\f\x01\x06\x02\x15\x02\x06\x11\x02\x01\x15"},
	{"mime/quotedprintable", "\x02\x01mz"},
	{"net", "\x04\t`+\x1d\a\x04\x05\f\x01\x04\x14\x01%\x06\r\n\x05\x01\x01\v\x06\a"},
	{"net/http", "\x02\x01\x04\x04\x02=\b\x13\x01\a\x03E5\x01\x03\b\x01\x02\x02\x02\x01\x02\x06\x02\x01\x01\n\x01\x01\x05\x01\x02\x05\n\x01\x01\x01\x02\x01\x01\v\x02\x02\x02\b\x01\x01\x01"},
	{"net/http/cgi", "\x02P\x1b\x03z\x04\b\n\x01\x13\x01\x01\x01\x04\x01\x05\x02\n\x02\x01\x0f\x0e"},
	{"net/http/cookiejar", "\x04i\x03\x90\x01\x01\b\f\x18\x03\x02\r\x04"},
	{"net/http/fcgi", "\x02\x01\nY\a\x03z\x16\x01\x01\x14\x1a\x02\r"},
	{"net/http/httptest", "\x02\x01\nE\x02\x1b\x01z\x04\x12\x01\n\t\x02\x19\x01\x02\r\x0e"},
	{"net/http/httptrace", "\rEn@\x14\n!"},
	{"net/http/httputil", "\x02\x01\n`\x03z\x04\x0f\x03\x01\x05\x02\x01\v\x01\x1b\x02\r\x0e"},
	{"net/http/internal", "\x02\x01j\x03z"},
	{"net/http/internal/ascii", "\xb0\x02\x11"},
	{"net/http/internal/httpcommon", "\r`\x03\x96\x01\x0e\x01\x19\x01\x01\x02\x1b\x02"},
	{"net/http/internal/testcert", "\xb0\x02"},
	{"net/http/pprof", "\x02\x01\nc\x19,\x11$\x04\x13\x14\x01\r\x06\x03\x01\x02\x01\x0f"},
	{"net/internal/cgotest", ""},
	{"net/internal/socktest", "p\xc2\x01\x02"},
	{"net/mail", "\x02k\x03z\x04\x0f\x03\x14\x1c\x02\r\x04"},
	{"net/netip", "\x04i+\x01#;\x026\x15"},
	{"net/rpc", "\x02f\x05\x03\x10\n`\x04\x12\x01\x1d\x0f\x03\x02"},
	{"net/rpc/jsonrpc", "j\x03\x03z\x16\x11!"},
	{"net/smtp", "\x19.\v\x13\b\x03z\x16\x14\x1c"},
	{"net/textproto", "\x02\x01j\x03z\r\t/\x01\x02\x13"},
	{"net/url", "m\x03\x86\x01%\x12\x02\x01\x15"},
	{"os", "m+\x01\x18\x03\b\t\r\x03\x01\x04\x10\x018\n\x05\x01\x01\v\x06"},
	{"os/exec", "\x03\n`H \x01\x14\x01+\x06\a\f\x01\x04\v"},
	{"os/exec/internal/fdtest", "\xb4\x02"},
	{"os/signal", "\r\x89\x02\x17\x05\x02"},
	{"os/user", "\x02\x01j\x03z,\r\f\x01\x02"},
	{"path", "m+\xab\x01"},
	{"path/filepath", "m+\x19:+\r\n\x03\x04\x0f"},
	{"plugin", "m"},
	{"reflect", "m'\x04\x1c\b\f\x04\x02\x19\x10,\f\x03\x0f\x02\x02"},
	{"reflect/internal/example1", ""},
	{"reflect/internal/example2", ""},
	{"regexp", "\x03\xe7\x018\v\x02\x01\x02\x0f\x02"},
	{"regexp/syntax", "\xad\x02\x01\x01\x01\x11\x02"},
	{"runtime", "\x94\x01\x04\x01\x02\f\x06\a\x02\x01\x01\x0f\x03\x01\x01\x01\x01\x01\x03\x0fd"},
	{"runtime/coverage", "\x9f\x01K"},
	{"runtime/debug", "pUQ\r\n\x02\x01\x0f\x06"},
	{"runtime/internal/startlinetest", ""},
	{"runtime/internal/wasitest", ""},
	{"runtime/metrics", "\xb6\x01A,\""},
	{"runtime/pprof", "\x02\x01\x01\x03\x06Y\a\x03$3#\r\x1f\r\n\x01\x01\x01\x02\x02\b\x03\x06"},
	{"runtime/race", "\xab\x02"},
	{"runtime/race/internal/amd64v1", ""},
	{"runtime/trace", "\rcz9\x0f\x01\x12"},
	{"slices", "\x04\xe9\x01\fL"},
	{"sort", "\xc9\x0104"},
	{"strconv", "m+:%\x02J"},
	{"strings", "m'\x04:\x18\x03\f9\x0f\x02\x02"},
	{"structs", ""},
	{"sync", "\xc8\x01\vP\x10\x12"},
	{"sync/atomic", "\xc5\x02"},
	{"syscall", "m(\x03\x01\x1b\b\x03\x03\x06\aT\n\x05\x01\x12"},
	{"testing", "\x03\n`\x02\x01X\x0f\x13\r\x04\x1b\x06\x02\x05\x02\a\x01\x02\x01\x02\x01\f\x02\x02\x02"},
	{"testing/fstest", "m\x03z\x01\v%\x12\x03\b\a"},
	{"testing/internal/testdeps", "\x02\v\xa6\x01'\x10,\x03\x05\x03\b\a\x02\r"},
	{"testing/iotest", "\x03j\x03z\x04"},
	{"testing/quick", "o\x01\x87\x01\x04#\x12\x0f"},
	{"testing/slogtest", "\r`\x03\x80\x01.\x05\x12\n"},
	{"text/scanner", "\x03mz,+\x02"},
	{"text/tabwriter", "pzY"},
	{"text/template", "m\x03B8\x01\v\x1f\x01\x05\x01\x02\x05\r\x02\f\x03\x02"},
	{"text/template/parse", "\x03m\xb3\x01\f\x01\x11\x02"},
	{"time", "m+\x1d\x1d'*\x0f\x02\x11"},
	{"time/tzdata", "m\xc7\x01\x11"},
	{"unicode", ""},
	{"unicode/utf16", ""},
	{"unicode/utf8", ""},
	{"unique", "\x94\x01>\x01P\x0f\x13\x12"},
	{"unsafe", ""},
	{"vendor/golang.org/x/crypto/chacha20", "\x10V\a\x8c\x01*'"},
	{"vendor/golang.org/x/crypto/chacha20poly1305", "\x10V\a\xd9\x01\x04\x01\a"},
	{"vendor/golang.org/x/crypto/cryptobyte", "c\n\x03\x88\x01&!\n"},
	{"vendor/golang.org/x/crypto/cryptobyte/asn1", ""},
	{"vendor/golang.org/x/crypto/internal/alias", "\xc5\x02"},
	{"vendor/golang.org/x/crypto/internal/poly1305", "Q\x15\x93\x01"},
	{"vendor/golang.org/x/net/dns/dnsmessage", "m"},
	{"vendor/golang.org/x/net/http/httpguts", "\x80\x02\x14\x1c\x13\r"},
	{"vendor/golang.org/x/net/http/httpproxy", "m\x03\x90\x01\x15\x01\x1a\x13\r"},
	{"vendor/golang.org/x/net/http2/hpack", "\x03j\x03zH"},
	{"vendor/golang.org/x/net/idna", "p\x87\x019\x13\x10\x02\x01"},
	{"vendor/golang.org/x/net/nettest", "\x03c\a\x03z\x11\x05\x16\x01\f\f\x01\x02\x02\x01\n"},
	{"vendor/golang.org/x/sys/cpu", "\x96\x02\r\f\x01\x15"},
	{"vendor/golang.org/x/text/secure/bidirule", "m\xd6\x01\x11\x01"},
	{"vendor/golang.org/x/text/transform", "\x03j}Y"},
	{"vendor/golang.org/x/text/unicode/bidi", "\x03\be~@\x15"},
	{"vendor/golang.org/x/text/unicode/norm", "f\nzH\x11\x11"},
	{"weak", "\x94\x01\x8f\x01\""},
}
