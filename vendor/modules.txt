# github.com/KyleBanks/depth v1.2.1
## explicit
github.com/KyleBanks/depth
# github.com/araujo88/gin-gonic-xss-middleware v0.0.0-**************-d89f16de6a7e
## explicit; go 1.19
github.com/araujo88/gin-gonic-xss-middleware
# github.com/aymerick/douceur v0.2.0
## explicit
github.com/aymerick/douceur/css
github.com/aymerick/douceur/parser
# github.com/bytedance/sonic v1.12.6
## explicit; go 1.17
github.com/bytedance/sonic
github.com/bytedance/sonic/ast
github.com/bytedance/sonic/decoder
github.com/bytedance/sonic/encoder
github.com/bytedance/sonic/internal/base64
github.com/bytedance/sonic/internal/caching
github.com/bytedance/sonic/internal/cpu
github.com/bytedance/sonic/internal/decoder/api
github.com/bytedance/sonic/internal/decoder/consts
github.com/bytedance/sonic/internal/decoder/errors
github.com/bytedance/sonic/internal/decoder/jitdec
github.com/bytedance/sonic/internal/decoder/optdec
github.com/bytedance/sonic/internal/encoder
github.com/bytedance/sonic/internal/encoder/alg
github.com/bytedance/sonic/internal/encoder/ir
github.com/bytedance/sonic/internal/encoder/vars
github.com/bytedance/sonic/internal/encoder/vm
github.com/bytedance/sonic/internal/encoder/x86
github.com/bytedance/sonic/internal/envs
github.com/bytedance/sonic/internal/jit
github.com/bytedance/sonic/internal/native
github.com/bytedance/sonic/internal/native/avx2
github.com/bytedance/sonic/internal/native/neon
github.com/bytedance/sonic/internal/native/sse
github.com/bytedance/sonic/internal/native/types
github.com/bytedance/sonic/internal/optcaching
github.com/bytedance/sonic/internal/resolver
github.com/bytedance/sonic/internal/rt
github.com/bytedance/sonic/internal/utils
github.com/bytedance/sonic/option
github.com/bytedance/sonic/unquote
github.com/bytedance/sonic/utf8
# github.com/bytedance/sonic/loader v0.2.1
## explicit; go 1.16
github.com/bytedance/sonic/loader
github.com/bytedance/sonic/loader/internal/abi
github.com/bytedance/sonic/loader/internal/rt
# github.com/cespare/xxhash/v2 v2.2.0
## explicit; go 1.11
github.com/cespare/xxhash/v2
# github.com/cloudwego/base64x v0.1.4
## explicit; go 1.16
github.com/cloudwego/base64x
# github.com/cloudwego/iasm v0.2.0
## explicit; go 1.16
github.com/cloudwego/iasm/expr
github.com/cloudwego/iasm/x86_64
# github.com/cpuguy83/go-md2man/v2 v2.0.7
## explicit; go 1.12
# github.com/davecgh/go-spew v1.1.1
## explicit
github.com/davecgh/go-spew/spew
# github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f
## explicit
github.com/dgryski/go-rendezvous
# github.com/gabriel-vasile/mimetype v1.4.7
## explicit; go 1.20
github.com/gabriel-vasile/mimetype
github.com/gabriel-vasile/mimetype/internal/charset
github.com/gabriel-vasile/mimetype/internal/json
github.com/gabriel-vasile/mimetype/internal/magic
# github.com/gin-contrib/cors v1.7.3
## explicit; go 1.21.0
github.com/gin-contrib/cors
# github.com/gin-contrib/secure v1.1.1
## explicit; go 1.21.0
github.com/gin-contrib/secure
# github.com/gin-contrib/sse v0.1.0
## explicit; go 1.12
github.com/gin-contrib/sse
# github.com/gin-gonic/gin v1.10.0
## explicit; go 1.20
github.com/gin-gonic/gin
github.com/gin-gonic/gin/binding
github.com/gin-gonic/gin/internal/bytesconv
github.com/gin-gonic/gin/internal/json
github.com/gin-gonic/gin/render
# github.com/go-openapi/jsonpointer v0.21.1
## explicit; go 1.20
github.com/go-openapi/jsonpointer
# github.com/go-openapi/jsonreference v0.21.0
## explicit; go 1.20
github.com/go-openapi/jsonreference
github.com/go-openapi/jsonreference/internal
# github.com/go-openapi/spec v0.21.0
## explicit; go 1.20
github.com/go-openapi/spec
# github.com/go-openapi/swag v0.23.1
## explicit; go 1.20
github.com/go-openapi/swag
# github.com/go-playground/locales v0.14.1
## explicit; go 1.17
github.com/go-playground/locales
github.com/go-playground/locales/currency
# github.com/go-playground/universal-translator v0.18.1
## explicit; go 1.18
github.com/go-playground/universal-translator
# github.com/go-playground/validator/v10 v10.23.0
## explicit; go 1.18
github.com/go-playground/validator/v10
# github.com/go-redis/redis/v8 v8.11.5
## explicit; go 1.17
github.com/go-redis/redis/v8
github.com/go-redis/redis/v8/internal
github.com/go-redis/redis/v8/internal/hashtag
github.com/go-redis/redis/v8/internal/hscan
github.com/go-redis/redis/v8/internal/pool
github.com/go-redis/redis/v8/internal/proto
github.com/go-redis/redis/v8/internal/rand
github.com/go-redis/redis/v8/internal/util
# github.com/goccy/go-json v0.10.4
## explicit; go 1.19
github.com/goccy/go-json
github.com/goccy/go-json/internal/decoder
github.com/goccy/go-json/internal/encoder
github.com/goccy/go-json/internal/encoder/vm
github.com/goccy/go-json/internal/encoder/vm_color
github.com/goccy/go-json/internal/encoder/vm_color_indent
github.com/goccy/go-json/internal/encoder/vm_indent
github.com/goccy/go-json/internal/errors
github.com/goccy/go-json/internal/runtime
# github.com/golang-jwt/jwt v3.2.2+incompatible
## explicit
github.com/golang-jwt/jwt
# github.com/golang/mock v1.6.0
## explicit; go 1.11
github.com/golang/mock/gomock
# github.com/golang/snappy v0.0.4
## explicit
github.com/golang/snappy
# github.com/gorilla/css v1.0.0
## explicit
github.com/gorilla/css/scanner
# github.com/jackc/pgpassfile v1.0.0
## explicit; go 1.12
github.com/jackc/pgpassfile
# github.com/jackc/pgservicefile v0.0.0-20221227161230-091c0ba34f0a
## explicit; go 1.14
github.com/jackc/pgservicefile
# github.com/jackc/pgx/v5 v5.5.5
## explicit; go 1.19
github.com/jackc/pgx/v5
github.com/jackc/pgx/v5/internal/anynil
github.com/jackc/pgx/v5/internal/iobufpool
github.com/jackc/pgx/v5/internal/pgio
github.com/jackc/pgx/v5/internal/sanitize
github.com/jackc/pgx/v5/internal/stmtcache
github.com/jackc/pgx/v5/pgconn
github.com/jackc/pgx/v5/pgconn/internal/bgreader
github.com/jackc/pgx/v5/pgconn/internal/ctxwatch
github.com/jackc/pgx/v5/pgproto3
github.com/jackc/pgx/v5/pgtype
github.com/jackc/pgx/v5/pgxpool
github.com/jackc/pgx/v5/stdlib
# github.com/jackc/puddle/v2 v2.2.1
## explicit; go 1.19
github.com/jackc/puddle/v2
github.com/jackc/puddle/v2/internal/genstack
# github.com/jinzhu/inflection v1.0.0
## explicit
github.com/jinzhu/inflection
# github.com/jinzhu/now v1.1.5
## explicit; go 1.12
github.com/jinzhu/now
# github.com/josharian/intern v1.0.0
## explicit; go 1.5
github.com/josharian/intern
# github.com/json-iterator/go v1.1.12
## explicit; go 1.12
github.com/json-iterator/go
# github.com/klauspost/compress v1.16.7
## explicit; go 1.18
github.com/klauspost/compress
github.com/klauspost/compress/fse
github.com/klauspost/compress/huff0
github.com/klauspost/compress/internal/cpuinfo
github.com/klauspost/compress/internal/snapref
github.com/klauspost/compress/zstd
github.com/klauspost/compress/zstd/internal/xxhash
# github.com/klauspost/cpuid/v2 v2.2.9
## explicit; go 1.20
github.com/klauspost/cpuid/v2
# github.com/leodido/go-urn v1.4.0
## explicit; go 1.18
github.com/leodido/go-urn
github.com/leodido/go-urn/scim/schema
# github.com/mailru/easyjson v0.9.0
## explicit; go 1.20
github.com/mailru/easyjson/buffer
github.com/mailru/easyjson/jlexer
github.com/mailru/easyjson/jwriter
# github.com/mattn/go-isatty v0.0.20
## explicit; go 1.15
github.com/mattn/go-isatty
# github.com/microcosm-cc/bluemonday v1.0.25
## explicit; go 1.19
github.com/microcosm-cc/bluemonday
github.com/microcosm-cc/bluemonday/css
# github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
## explicit
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v1.0.2
## explicit; go 1.12
github.com/modern-go/reflect2
# github.com/montanaflynn/stats v0.7.1
## explicit; go 1.13
github.com/montanaflynn/stats
# github.com/pelletier/go-toml/v2 v2.2.3
## explicit; go 1.21.0
github.com/pelletier/go-toml/v2
github.com/pelletier/go-toml/v2/internal/characters
github.com/pelletier/go-toml/v2/internal/danger
github.com/pelletier/go-toml/v2/internal/tracker
github.com/pelletier/go-toml/v2/unstable
# github.com/pmezard/go-difflib v1.0.0
## explicit
github.com/pmezard/go-difflib/difflib
# github.com/russross/blackfriday/v2 v2.1.0
## explicit
# github.com/shurcooL/sanitized_anchor_name v1.0.0
## explicit
# github.com/stretchr/testify v1.10.0
## explicit; go 1.17
github.com/stretchr/testify/assert
github.com/stretchr/testify/assert/yaml
# github.com/swaggo/files v1.0.1
## explicit; go 1.16
github.com/swaggo/files
# github.com/swaggo/gin-swagger v1.6.0
## explicit; go 1.17
github.com/swaggo/gin-swagger
# github.com/swaggo/swag v1.16.6
## explicit; go 1.18
github.com/swaggo/swag
# github.com/twitchyliquid64/golang-asm v0.15.1
## explicit; go 1.13
github.com/twitchyliquid64/golang-asm/asm/arch
github.com/twitchyliquid64/golang-asm/bio
github.com/twitchyliquid64/golang-asm/dwarf
github.com/twitchyliquid64/golang-asm/goobj
github.com/twitchyliquid64/golang-asm/obj
github.com/twitchyliquid64/golang-asm/obj/arm
github.com/twitchyliquid64/golang-asm/obj/arm64
github.com/twitchyliquid64/golang-asm/obj/mips
github.com/twitchyliquid64/golang-asm/obj/ppc64
github.com/twitchyliquid64/golang-asm/obj/riscv
github.com/twitchyliquid64/golang-asm/obj/s390x
github.com/twitchyliquid64/golang-asm/obj/wasm
github.com/twitchyliquid64/golang-asm/obj/x86
github.com/twitchyliquid64/golang-asm/objabi
github.com/twitchyliquid64/golang-asm/src
github.com/twitchyliquid64/golang-asm/sys
github.com/twitchyliquid64/golang-asm/unsafeheader
# github.com/ugorji/go/codec v1.2.12
## explicit; go 1.11
github.com/ugorji/go/codec
# github.com/urfave/cli/v2 v2.27.7
## explicit; go 1.18
# github.com/xdg-go/pbkdf2 v1.0.0
## explicit; go 1.9
github.com/xdg-go/pbkdf2
# github.com/xdg-go/scram v1.1.2
## explicit; go 1.11
github.com/xdg-go/scram
# github.com/xdg-go/stringprep v1.0.4
## explicit; go 1.11
github.com/xdg-go/stringprep
# github.com/xrash/smetrics v0.0.0-20250705151800-55b8f293f342
## explicit; go 1.15
# github.com/youmark/pkcs8 v0.0.0-20240726163527-a2c0da244d78
## explicit; go 1.17
github.com/youmark/pkcs8
# go.mongodb.org/mongo-driver v1.17.2
## explicit; go 1.18
go.mongodb.org/mongo-driver/bson
go.mongodb.org/mongo-driver/bson/bsoncodec
go.mongodb.org/mongo-driver/bson/bsonoptions
go.mongodb.org/mongo-driver/bson/bsonrw
go.mongodb.org/mongo-driver/bson/bsontype
go.mongodb.org/mongo-driver/bson/primitive
go.mongodb.org/mongo-driver/event
go.mongodb.org/mongo-driver/internal/aws
go.mongodb.org/mongo-driver/internal/aws/awserr
go.mongodb.org/mongo-driver/internal/aws/credentials
go.mongodb.org/mongo-driver/internal/aws/signer/v4
go.mongodb.org/mongo-driver/internal/bsonutil
go.mongodb.org/mongo-driver/internal/codecutil
go.mongodb.org/mongo-driver/internal/credproviders
go.mongodb.org/mongo-driver/internal/csfle
go.mongodb.org/mongo-driver/internal/csot
go.mongodb.org/mongo-driver/internal/driverutil
go.mongodb.org/mongo-driver/internal/handshake
go.mongodb.org/mongo-driver/internal/httputil
go.mongodb.org/mongo-driver/internal/logger
go.mongodb.org/mongo-driver/internal/ptrutil
go.mongodb.org/mongo-driver/internal/rand
go.mongodb.org/mongo-driver/internal/randutil
go.mongodb.org/mongo-driver/internal/uuid
go.mongodb.org/mongo-driver/mongo
go.mongodb.org/mongo-driver/mongo/address
go.mongodb.org/mongo-driver/mongo/description
go.mongodb.org/mongo-driver/mongo/options
go.mongodb.org/mongo-driver/mongo/readconcern
go.mongodb.org/mongo-driver/mongo/readpref
go.mongodb.org/mongo-driver/mongo/writeconcern
go.mongodb.org/mongo-driver/tag
go.mongodb.org/mongo-driver/version
go.mongodb.org/mongo-driver/x/bsonx/bsoncore
go.mongodb.org/mongo-driver/x/mongo/driver
go.mongodb.org/mongo-driver/x/mongo/driver/auth
go.mongodb.org/mongo-driver/x/mongo/driver/auth/creds
go.mongodb.org/mongo-driver/x/mongo/driver/auth/internal/gssapi
go.mongodb.org/mongo-driver/x/mongo/driver/connstring
go.mongodb.org/mongo-driver/x/mongo/driver/dns
go.mongodb.org/mongo-driver/x/mongo/driver/mongocrypt
go.mongodb.org/mongo-driver/x/mongo/driver/mongocrypt/options
go.mongodb.org/mongo-driver/x/mongo/driver/ocsp
go.mongodb.org/mongo-driver/x/mongo/driver/operation
go.mongodb.org/mongo-driver/x/mongo/driver/session
go.mongodb.org/mongo-driver/x/mongo/driver/topology
go.mongodb.org/mongo-driver/x/mongo/driver/wiremessage
# go.uber.org/multierr v1.10.0
## explicit; go 1.19
go.uber.org/multierr
# go.uber.org/zap v1.27.0
## explicit; go 1.19
go.uber.org/zap
go.uber.org/zap/buffer
go.uber.org/zap/internal
go.uber.org/zap/internal/bufferpool
go.uber.org/zap/internal/color
go.uber.org/zap/internal/exit
go.uber.org/zap/internal/pool
go.uber.org/zap/internal/stacktrace
go.uber.org/zap/zapcore
# go.yaml.in/yaml/v2 v2.4.2
## explicit; go 1.15
# golang.org/x/arch v0.12.0
## explicit; go 1.18
golang.org/x/arch/x86/x86asm
# golang.org/x/crypto v0.40.0
## explicit; go 1.23.0
golang.org/x/crypto/bcrypt
golang.org/x/crypto/blowfish
golang.org/x/crypto/ocsp
golang.org/x/crypto/pbkdf2
golang.org/x/crypto/scrypt
golang.org/x/crypto/sha3
# golang.org/x/mod v0.26.0
## explicit; go 1.23.0
golang.org/x/mod/internal/lazyregexp
golang.org/x/mod/module
golang.org/x/mod/semver
# golang.org/x/net v0.42.0
## explicit; go 1.23.0
golang.org/x/net/html
golang.org/x/net/html/atom
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/h2c
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/httpcommon
golang.org/x/net/webdav
golang.org/x/net/webdav/internal/xml
# golang.org/x/sync v0.16.0
## explicit; go 1.23.0
golang.org/x/sync/errgroup
golang.org/x/sync/semaphore
golang.org/x/sync/singleflight
# golang.org/x/sys v0.34.0
## explicit; go 1.23.0
golang.org/x/sys/cpu
golang.org/x/sys/unix
# golang.org/x/text v0.27.0
## explicit; go 1.23.0
golang.org/x/text/cases
golang.org/x/text/internal
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/tag
golang.org/x/text/language
golang.org/x/text/runes
golang.org/x/text/secure/bidirule
golang.org/x/text/secure/precis
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
golang.org/x/text/width
# golang.org/x/time v0.10.0
## explicit; go 1.18
golang.org/x/time/rate
# golang.org/x/tools v0.35.0
## explicit; go 1.23.0
golang.org/x/tools/go/ast/astutil
golang.org/x/tools/go/buildutil
golang.org/x/tools/go/internal/cgo
golang.org/x/tools/go/loader
golang.org/x/tools/imports
golang.org/x/tools/internal/event
golang.org/x/tools/internal/event/core
golang.org/x/tools/internal/event/keys
golang.org/x/tools/internal/event/label
golang.org/x/tools/internal/gocommand
golang.org/x/tools/internal/gopathwalk
golang.org/x/tools/internal/imports
golang.org/x/tools/internal/modindex
golang.org/x/tools/internal/stdlib
# google.golang.org/protobuf v1.36.1
## explicit; go 1.21
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/proto
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
# gopkg.in/yaml.v2 v2.4.0
## explicit; go 1.15
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# gorm.io/driver/postgres v1.5.11
## explicit; go 1.19
gorm.io/driver/postgres
# gorm.io/gorm v1.25.12
## explicit; go 1.18
gorm.io/gorm
gorm.io/gorm/callbacks
gorm.io/gorm/clause
gorm.io/gorm/logger
gorm.io/gorm/migrator
gorm.io/gorm/schema
gorm.io/gorm/utils
# sigs.k8s.io/yaml v1.6.0
## explicit; go 1.22
