services:
  backend:
    container_name: go-rest-api-template
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - 8001:8001
    depends_on:
      - db
      - mongo
    environment:
      POSTGRES_DB: go_app_dev
      POSTGRES_HOST: dockerPostgres
      POSTGRES_USER: docker
      POSTGRES_PASSWORD: password
      POSTGRES_PORT: 5435
      JWT_SECRET_KEY: ObL89O3nOSSEj6tbdHako0cXtPErzBUfq8l8o/3KD9g=INSECURE
      API_SECRET_KEY: cJGZ8L1sDcPezjOy1zacPJZxzZxrPObm2Ggs1U0V+fE=INSECURE
      REDIS_HOST: redis

  db:
    image: postgres:14.1-alpine
    restart: always
    container_name: dockerPostgres
    volumes:
      - .dbdata:/var/lib/postgres
    ports:
      - "5435:5435"
    environment:
      - POSTGRES_DB=go_app_dev
      - POSTGRES_USER=docker
      - POSTGRES_PASSWORD=password
    command: -p 5435

  redis:
    image: "redis:alpine"
    ports:
      - "6379:6379"
    container_name: dockerRedis

  mongo:
    image: mongo
    container_name: dockerMongo
    volumes:
      - mongo_data:/data/db
    ports:
      - "27017:27017"
    restart: always

volumes:
  mongo_data: # Declare the volume for MongoDB data
