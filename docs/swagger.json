{"swagger": "2.0", "info": {"description": "This is a sample server celler server.", "title": "Swagger Example API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "host": "localhost:8001", "basePath": "/api/v1", "paths": {"/": {"get": {"description": "do ping", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["example"], "summary": "ping example", "responses": {"200": {"description": "OK", "schema": {"type": "string"}}}}}, "/books": {"get": {"security": [{"ApiKeyAuth": []}], "description": "Get a list of all books with optional pagination", "produces": ["application/json"], "tags": ["books"], "summary": "Get all books with pagination", "parameters": [{"type": "integer", "default": 0, "description": "Offset for pagination", "name": "offset", "in": "query"}, {"type": "integer", "default": 10, "description": "Limit for pagination", "name": "limit", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved list of books", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Book"}}}}}, "post": {"security": [{"ApiKeyAuth": []}, {"JwtAuth": []}], "description": "Create a new book with the given input data", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["books"], "summary": "Create a new book", "parameters": [{"description": "Create book object", "name": "input", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateBook"}}], "responses": {"201": {"description": "Successfully created book", "schema": {"$ref": "#/definitions/models.Book"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/books/{id}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "Get details of a book by its ID", "produces": ["application/json"], "tags": ["books"], "summary": "Find a book by <PERSON>", "parameters": [{"type": "string", "description": "Book ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Successfully retrieved book", "schema": {"$ref": "#/definitions/models.Book"}}, "404": {"description": "Book not found", "schema": {"type": "string"}}}}, "put": {"security": [{"ApiKeyAuth": []}], "description": "Update the book details for the given ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["books"], "summary": "Update a book by ID", "parameters": [{"type": "string", "description": "Book ID", "name": "id", "in": "path", "required": true}, {"description": "Update book object", "name": "input", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateBook"}}], "responses": {"200": {"description": "Successfully updated book", "schema": {"$ref": "#/definitions/models.Book"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "404": {"description": "book not found", "schema": {"type": "string"}}}}, "delete": {"security": [{"ApiKeyAuth": []}], "description": "Delete the book with the given ID", "produces": ["application/json"], "tags": ["books"], "summary": "Delete a book by <PERSON>", "parameters": [{"type": "string", "description": "Book ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "Successfully deleted book", "schema": {"type": "string"}}, "404": {"description": "book not found", "schema": {"type": "string"}}}}}, "/login": {"post": {"security": [{"ApiKeyAuth": []}], "description": "Authenticates a user using username and password, returns a JWT token if successful", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "Authenticate a user", "parameters": [{"description": "User login object", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.LoginUser"}}], "responses": {"200": {"description": "JWT Token", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/register": {"post": {"security": [{"ApiKeyAuth": []}], "description": "Registers a new user with the given username and password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "Register a new user", "parameters": [{"description": "User registration object", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.LoginUser"}}], "responses": {"201": {"description": "Successfully registered", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/users": {"get": {"security": [{"ApiKeyAuth": []}], "description": "Get a list of all users with optional pagination", "produces": ["application/json"], "tags": ["user"], "summary": "Get all users with pagination", "parameters": [{"type": "integer", "default": 0, "description": "Offset for pagination", "name": "offset", "in": "query"}, {"type": "integer", "default": 10, "description": "Limit for pagination", "name": "limit", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved list of users", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.User"}}}}}}}, "definitions": {"models.Book": {"type": "object", "properties": {"author": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "title": {"type": "string"}, "updated_at": {"type": "string"}}}, "models.CreateBook": {"type": "object", "required": ["author", "title"], "properties": {"author": {"type": "string"}, "title": {"type": "string"}}}, "models.LoginUser": {"type": "object", "required": ["password", "username"], "properties": {"password": {"type": "string"}, "username": {"type": "string"}}}, "models.UpdateBook": {"type": "object", "properties": {"author": {"type": "string"}, "title": {"type": "string"}}}, "models.User": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "password": {"type": "string"}, "updated_at": {"type": "string"}, "username": {"type": "string"}}}}, "securityDefinitions": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-API-Key", "in": "header"}, "JwtAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "externalDocs": {"description": "OpenAPI", "url": "https://swagger.io/resources/open-api/"}}