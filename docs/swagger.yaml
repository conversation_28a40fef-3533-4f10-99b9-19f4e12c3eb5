basePath: /api/v1
definitions:
  models.Book:
    properties:
      author:
        type: string
      created_at:
        type: string
      id:
        type: integer
      title:
        type: string
      updated_at:
        type: string
    type: object
  models.CreateBook:
    properties:
      author:
        type: string
      title:
        type: string
    required:
    - author
    - title
    type: object
  models.LoginUser:
    properties:
      password:
        type: string
      username:
        type: string
    required:
    - password
    - username
    type: object
  models.UpdateBook:
    properties:
      author:
        type: string
      title:
        type: string
    type: object
  models.User:
    properties:
      created_at:
        type: string
      id:
        type: integer
      password:
        type: string
      updated_at:
        type: string
      username:
        type: string
    type: object
externalDocs:
  description: OpenAPI
  url: https://swagger.io/resources/open-api/
host: localhost:8001
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a sample server celler server.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Swagger Example API
  version: "1.0"
paths:
  /:
    get:
      consumes:
      - application/json
      description: do ping
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
      summary: ping example
      tags:
      - example
  /books:
    get:
      description: Get a list of all books with optional pagination
      parameters:
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      - default: 10
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved list of books
          schema:
            items:
              $ref: '#/definitions/models.Book'
            type: array
      security:
      - ApiKeyAuth: []
      summary: Get all books with pagination
      tags:
      - books
    post:
      consumes:
      - application/json
      description: Create a new book with the given input data
      parameters:
      - description: Create book object
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/models.CreateBook'
      produces:
      - application/json
      responses:
        "201":
          description: Successfully created book
          schema:
            $ref: '#/definitions/models.Book'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      - JwtAuth: []
      summary: Create a new book
      tags:
      - books
  /books/{id}:
    delete:
      description: Delete the book with the given ID
      parameters:
      - description: Book ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: Successfully deleted book
          schema:
            type: string
        "404":
          description: book not found
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: Delete a book by ID
      tags:
      - books
    get:
      description: Get details of a book by its ID
      parameters:
      - description: Book ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved book
          schema:
            $ref: '#/definitions/models.Book'
        "404":
          description: Book not found
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: Find a book by ID
      tags:
      - books
    put:
      consumes:
      - application/json
      description: Update the book details for the given ID
      parameters:
      - description: Book ID
        in: path
        name: id
        required: true
        type: string
      - description: Update book object
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/models.UpdateBook'
      produces:
      - application/json
      responses:
        "200":
          description: Successfully updated book
          schema:
            $ref: '#/definitions/models.Book'
        "400":
          description: Bad Request
          schema:
            type: string
        "404":
          description: book not found
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: Update a book by ID
      tags:
      - books
  /login:
    post:
      consumes:
      - application/json
      description: Authenticates a user using username and password, returns a JWT
        token if successful
      parameters:
      - description: User login object
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.LoginUser'
      produces:
      - application/json
      responses:
        "200":
          description: JWT Token
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: Authenticate a user
      tags:
      - user
  /register:
    post:
      consumes:
      - application/json
      description: Registers a new user with the given username and password
      parameters:
      - description: User registration object
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.LoginUser'
      produces:
      - application/json
      responses:
        "201":
          description: Successfully registered
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      summary: Register a new user
      tags:
      - user
  /users:
    get:
      description: Get a list of all users with optional pagination
      parameters:
      - default: 0
        description: Offset for pagination
        in: query
        name: offset
        type: integer
      - default: 10
        description: Limit for pagination
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved list of users
          schema:
            items:
              $ref: '#/definitions/models.User'
            type: array
      security:
      - ApiKeyAuth: []
      summary: Get all users with pagination
      tags:
      - user
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: X-API-Key
    type: apiKey
  JwtAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
